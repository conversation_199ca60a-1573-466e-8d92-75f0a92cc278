// 修复后的首页JavaScript代码

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化移动端功能
    initMobileFeatures();

    // 检查登录状态
    checkLoginStatus();

    // 加载页面数据
    loadPageData();

    // 隐藏加载动画
    setTimeout(() => {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }, 1000);
});

function initMobileFeatures() {
    // 初始化触摸交互
    if (typeof initTouchInteractions === 'function') {
        initTouchInteractions();
    }

    // 初始化导航
    if (typeof initNavigation === 'function') {
        initNavigation();
    }

    // 初始化搜索
    if (typeof initSearch === 'function') {
        initSearch();
    }

    // 初始化返回顶部
    if (typeof initBackToTop === 'function') {
        initBackToTop();
    }

    // 初始化学习标签切换
    initLearningTabs();
}

function checkLoginStatus() {
    const userinfo = sessionStorage.getItem("userinfo");
    const header = sessionStorage.getItem("header");
    const loginBtn = document.getElementById('loginBtn');
    const userAvatar = document.getElementById('userAvatar');
    const userName = document.getElementById('userName');

    const isLoggedIn = !!(userinfo && header);

    if (isLoggedIn) {
        try {
            const user = JSON.parse(userinfo);
            if (loginBtn) loginBtn.style.display = 'none';
            if (userAvatar) userAvatar.style.display = 'block';
            if (userName) userName.textContent = user.name || user.realName || '用户';
        } catch (e) {
            console.error('解析用户信息失败:', e);
            // 如果解析失败，清除无效数据
            sessionStorage.removeItem("userinfo");
            sessionStorage.removeItem("header");
            if (loginBtn) loginBtn.style.display = 'block';
            if (userAvatar) userAvatar.style.display = 'none';
        }
    } else {
        if (loginBtn) {
            loginBtn.style.display = 'block';
            // 绑定统一认证平台登录
            loginBtn.onclick = function() {
                const serviceUrl = window.location.origin + window.location.pathname.replace('index.html', '') + 'userinfo-simple.html';
                window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=' + encodeURIComponent(serviceUrl);
            };
        }
        if (userAvatar) userAvatar.style.display = 'none';
    }

    return {
        isLoggedIn: isLoggedIn,
        userInfo: isLoggedIn ? JSON.parse(userinfo) : null,
        token: header
    };
}

function loadPageData() {
    // 加载菜单
    if (typeof loadMenu === 'function') {
        loadMenu();
    }

    // 加载轮播图
    loadBanner();

    // 加载心声社区
    loadCommunity();

    // 加载学习内容
    loadLearningContent();
}

function loadBanner() {
    // 直接显示固定的loginbagg.png图片，不再请求API
    renderFixedBanner();
}

function renderFixedBanner() {
    const bannerWrapper = document.getElementById('bannerWrapper');
    if (!bannerWrapper) return;

    const html = `
        <div class="swiper-slide" style="background: white; display: flex; align-items: center; justify-content: center; min-height: 200px; padding: 20px;">
            <img src="img/loginbagg.png" alt="思政一体化平台" style="max-width: 100%; height: auto; object-fit: contain;" onerror="this.style.display='none'">
        </div>
    `;

    bannerWrapper.innerHTML = html;

    // 隐藏分页指示器，因为只有一张图片
    const pagination = document.querySelector('.swiper-pagination');
    if (pagination) {
        pagination.style.display = 'none';
    }
}

// 轮播图相关函数已简化，现在只显示固定的loginbagg.png图片

function loadCommunity() {
    const communityList = document.getElementById('communityList');
    if (!communityList) return;

    // 显示加载状态
    communityList.innerHTML = '<div class="loading-item"><div class="spinner"></div>加载中...</div>';

    // 首先获取心声社区分类ID
    $.ajax({
        url: baseurl + "/web/category/user",
        type: 'GET',
        contentType: "application/json",
        headers: {
            "Authorization": sessionStorage.getItem("header")
        },
        dataType: 'json',
        success: (res) => {
            if (res.code == '200' && res.data && res.data.length > 0) {
                const xsclassid = res.data[0].id;
                // 获取心声社区最新话题
                getCommunityPosts(xsclassid);
            } else {
                communityList.innerHTML = '<div class="empty-state">暂无社区内容</div>';
            }
        },
        error: (err) => {
            console.error('获取心声社区分类失败:', err);
            communityList.innerHTML = '<div class="empty-state">暂无社区内容</div>';
        }
    });
}

function getCommunityPosts(classid) {
    const communityList = document.getElementById('communityList');

    // 使用PC端相同的接口和参数
    const params = "?pageNum=1&pageSize=5&id=" + classid + "&sort=created_at&order=desc&title=";

    $.ajax({
        url: baseurl + "/web/xspostByCategoryId" + params,
        type: 'GET',
        contentType: "application/json",
        headers: {
            "Authorization": sessionStorage.getItem("header")
        },
        dataType: 'json',
        success: (res) => {
            if (res.code == '200') {
                const communityData = res.data.list || [];
                renderCommunity(communityData);
            } else {
                communityList.innerHTML = '<div class="empty-state">暂无社区内容</div>';
            }
        },
        error: (err) => {
            console.error('加载心声社区失败:', err);
            communityList.innerHTML = '<div class="empty-state">暂无社区内容</div>';
        }
    });
}

function renderCommunity(communityData) {
    const communityList = document.getElementById('communityList');
    if (!communityList) return;

    if (!communityData || communityData.length === 0) {
        communityList.innerHTML = '<div class="empty-state">暂无社区内容</div>';
        return;
    }

    let html = '';
    communityData.forEach(item => {
        // 格式化时间 - 使用PC端相同的字段
        const timeStr = item.createdAt || item.createTime || item.publishedTime;
        const formattedTime = timeStr ? formatTime(timeStr) : '刚刚';
        const isNew = formattedTime.includes('分钟前') || formattedTime.includes('小时前');

        html += `
            <div class="community-item" data-id="${item.id}">
                ${isNew ? '<span class="new-badge">NEW</span>' : ''}
                <div class="community-content">
                    <div class="community-title">${item.title || item.themename || '无标题'}</div>
                    <div class="community-meta">
                        <span class="community-author">作者：${item.author || '匿名'}</span>
                        <span class="community-time">${formattedTime}</span>
                    </div>
                    <div class="community-stats">
                        <span class="community-views">浏览 ${item.clickCount || 0}</span>
                        <span class="community-likes">点赞 ${item.giveLike || 0}</span>
                    </div>
                </div>
            </div>
        `;
    });

    communityList.innerHTML = html;

    // 绑定点击事件
    bindCommunityEvents();
}

function formatTime(timeStr) {
    if (!timeStr) return '刚刚';
    
    try {
        const time = new Date(timeStr);
        const now = new Date();
        const diff = now - time;
        
        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        
        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 30) return `${days}天前`;
        
        return time.toLocaleDateString();
    } catch (e) {
        return timeStr;
    }
}

function bindCommunityEvents() {
    const communityItems = document.querySelectorAll('.community-item');
    communityItems.forEach(item => {
        item.addEventListener('click', function(e) {
            const id = this.getAttribute('data-id');
            if (id) {
                // 记录点击统计
                if (typeof clicknum === 'function') {
                    clicknum(id);
                }
                
                // 跳转到心声社区详情页面
                setTimeout(() => {
                    window.location.href = `pages/community-detail.html?id=${id}`;
                }, 100);
            }
        });
    });
}

function initLearningTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // 移除所有active类
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));

            // 添加active类
            this.classList.add('active');
            const targetContent = document.getElementById(targetTab + 'Content');
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // 加载对应内容
            if (targetTab === 'redbooks') {
                loadRedBooks();
            } else if (targetTab === 'courses') {
                loadCourses();
            }
        });
    });
}

function loadLearningContent() {
    // 默认加载红色书籍
    loadRedBooks();
}

function loadRedBooks() {
    const redbooksContent = document.getElementById('redbooksContent');
    if (!redbooksContent) return;

    redbooksContent.innerHTML = '<div class="loading-item"><div class="spinner"></div>加载中...</div>';

    // 使用PC端相同的红色书籍接口
    $.ajax({
        url: baseurl + "/web/posts?categoryId=912354240784109568&pageSize=6&redBookId=1369261422076366848&pageNum=1&_t=" + new Date().getTime() + "&sort=publishedTime%2Cdesc",
        type: 'GET',
        contentType: "application/json",
        headers: {
            "Authorization": sessionStorage.getItem("header")
        },
        dataType: 'json',
        success: (res) => {
            if (res.code == '200') {
                renderLearningGrid(res.data.list || [], redbooksContent, 'redbook');
            } else {
                redbooksContent.innerHTML = '<div class="empty-state">暂无红色书籍</div>';
            }
        },
        error: (err) => {
            console.error('加载红色书籍失败:', err);
            redbooksContent.innerHTML = '<div class="empty-state">暂无红色书籍</div>';
        }
    });
}

function loadCourses() {
    const coursesContent = document.getElementById('coursesContent');
    if (!coursesContent) return;

    coursesContent.innerHTML = '<div class="loading-item"><div class="spinner"></div>加载中...</div>';

    // 使用PC端相同的课程接口
    $.ajax({
        url: baseurl + "/web/course",
        type: 'GET',
        contentType: "application/json",
        headers: {
            "Authorization": sessionStorage.getItem("header")
        },
        data: {
            pageSize: 6,
            pageNum: 1,
            sortField: "createTime",
            sortOrder: "desc"
        },
        dataType: 'json',
        success: (res) => {
            if (res.code == '200') {
                renderLearningGrid(res.data.list || [], coursesContent, 'course');
            } else {
                coursesContent.innerHTML = '<div class="empty-state">暂无课程内容</div>';
            }
        },
        error: (err) => {
            console.error('加载课程失败:', err);
            coursesContent.innerHTML = '<div class="empty-state">暂无课程内容</div>';
        }
    });
}

function renderLearningGrid(data, container, type) {
    if (!data || data.length === 0) {
        container.innerHTML = '<div class="empty-state">暂无内容</div>';
        return;
    }

    let html = '<div class="learning-grid">';
    data.forEach((item, index) => {
        let url, imageUrl, title, author, meta, coverClass = '';

        if (type === 'redbook') {
            // 红色书籍字段
            url = `pages/redbook-detail.html?id=${item.id}`;
            imageUrl = item.thumbPath && item.thumbPath[0] ? (baseurl + item.thumbPath[0]) : 'img/book_default.jpg';
            title = item.title || '无标题';
            author = item.author || '未知';
            meta = item.postName || '红色书籍';

            // 为红色书籍随机应用不同的美化效果
            const styles = ['', 'book-3d', 'textured', 'glowing', 'paper-texture'];
            const randomStyle = styles[index % styles.length];
            coverClass = `redbook-cover ${randomStyle}`;

            // 根据书籍类型或标题关键词应用特定样式
            if (title.includes('历史') || title.includes('革命')) {
                coverClass += ' style-classic';
            } else if (title.includes('理论') || title.includes('思想')) {
                coverClass += ' style-modern';
            } else if (title.includes('文化') || title.includes('传统')) {
                coverClass += ' style-warm';
            }
        } else {
            // 课程学习字段
            url = `pages/course-detail.html?id=${item.metaId || item.id}`;
            imageUrl = item.covertPath ? (baseurl + item.covertPath) : 'img/course_default.jpg';
            title = item.title || item.titleName || '无标题';
            author = item.author || item.principal || '未知';
            meta = item.attachType || '课程学习';
            coverClass = 'course-cover';
        }

        html += `
            <a href="${url}" class="learning-item" data-id="${item.id}" data-type="${item.attachType || 'redbook'}">
                <div class="learning-cover ${coverClass}">
                    <img src="${imageUrl}" alt="${title}" onerror="this.src='img/default.jpg'">
                    <div class="learning-type">${meta}</div>
                </div>
                <div class="learning-info">
                    <div class="learning-title">${title}</div>
                    <div class="learning-meta">
                        <span class="learning-author">作者：${author}</span>
                        <span class="learning-time">${formatTime(item.createTime || item.publishedTime || item.createdAt)}</span>
                    </div>
                    ${type === 'course' && item.score ? `<div class="learning-score">评分：${parseFloat(item.score).toFixed(1)}</div>` : ''}
                </div>
            </a>
        `;
    });
    html += '</div>';

    container.innerHTML = html;

    // 绑定点击事件
    bindLearningEvents(container);
}

function bindLearningEvents(container) {
    const learningItems = container.querySelectorAll('.learning-item');
    learningItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const id = this.getAttribute('data-id');
            if (id && typeof clicknum === 'function') {
                clicknum(id);
            }
            setTimeout(() => {
                window.location.href = this.getAttribute('href');
            }, 100);
        });
    });
}
