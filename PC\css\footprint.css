/* 重置body样式，避免style.css中的padding-top影响布局 */
body {
	padding-top: 0 !important;
	margin: 0;
	padding: 0;
}

/* 确保topview在footprint页面中正常定位 */
.topview {
	position: relative !important;
	top: auto !important;
	z-index: auto !important;
	margin-bottom: 0 !important;
}

/* 确保content区域正常显示 */
.content {
	margin-top: 0 !important;
	padding-top: 0 !important;
}

body{
	background: #fbfbfb !important;
}
.content{
	background: url(../img/footprintbag.png) no-repeat;
	background-size: 100%;
}
#map{
	height: auto;
	width: 100%;
	max-width: 66.666666rem;
	margin: 1.5625rem auto;
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
	padding: 0;
}
.maptitle{
	display: flex;
	justify-content: center;
}
.maptitle img{
	display: block;
	margin-top: 4.166666rem;
	width: 18.28125rem;
}
.jsmap-svg-container{
	width: 95%;
	margin: 0 auto;
	padding: 0;
	position: relative;
	display: flex;
	justify-content: center;
	align-items: center;
	transform-origin: center center;
}
#map svg *{
	font-size: 0.520833rem;
}
#map svg{
	filter: drop-shadow(0px 8px 12px rgba(0, 0, 0, 0.25)) !important;
	-webkit-filter: drop-shadow(0px 8px 12px rgba(0, 0, 0, 0.25)) !important;
	transition: all 0.5s ease-in-out;
}
#map svg path, 
.jsmap-svg path {
    fill: #DE2910 !important;          /* 中国红 */
    stroke: #FFFFFF !important;        /* 白色边框 */
    stroke-width: 1px !important;      /* 减小默认边框宽度 */
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow: 0 6px 12px rgba(0, 0, 0, 0.3);
}

#map svg text,
.jsmap-svg text {
    fill: #FFD700 !important;          /* 金黄色文字 */
    font-size: 12px !important;        /* 增大字体 */
    font-family: "Microsoft YaHei" !important;
    font-weight: 600 !important;       /* 加粗 */
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.4),
                -1px -1px 2px rgba(0, 0, 0, 0.3);  /* 多层文字阴影 */
    transition: all 0.3s ease;
    opacity: 0.9;                      /* 默认稍微透明 */
}

#map svg path:hover,
.jsmap-svg path:hover {
    fill: #FF4E4E !important;
    cursor: pointer;
    stroke-width: 1.5px !important;    /* 悬停时适度增加边框宽度 */
    filter: brightness(1.1) drop-shadow(4px 6px 6px rgba(0, 0, 0, 0.5));
    transform: translateY(-2px);
}

#map svg path:hover + text,
.jsmap-svg path:hover + text {
    fill: #FFF7CC !important;          /* 更亮的金色 */
    opacity: 1;                        /* 完全不透明 */
    transform: scale(1.1);             /* 文字略微放大 */
    text-shadow: 2px 2px 6px rgba(0, 0, 0, 0.5),
                -1px -1px 3px rgba(0, 0, 0, 0.4);  /* 增强阴影 */
}

#map svg path:active,
.jsmap-svg path:active {
    transform: translateY(0);          /* 恢复原位 */
    transition: all 0.1s ease;
    filter: brightness(0.95);          /* 略微变暗 */
}

#map:hover svg {
    filter: drop-shadow(0px 12px 16px rgba(0, 0, 0, 0.3)) !important;
    -webkit-filter: drop-shadow(0px 12px 16px rgba(0, 0, 0, 0.3)) !important;
}

.maptitlebox{
	display: none;
	width: 66.666666rem;
	height: 3.125rem;
	margin: 0px auto;
	align-items: center;
	justify-content: center;
	background: #FFFFFF;
}
.maptitlebox div{
	cursor: pointer;
	text-align: center;
	font-size: 0.833333rem;
	color: #999999;
	position: relative;
	height: 100%;
	line-height: 3.125rem;
	padding: 0px 0.78125rem;
	transition: all 0.3s ease;
	position: relative;
	overflow: hidden;
}
.maptitlebox div:hover{
	color: #DE2910;
	transform: translateY(-2px);
	box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}
.maptitlebox div::after{
	content: "";
	width: 0.572916rem;
	height: 0.3125rem;
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	margin: auto;
	background: url(../img/sj.png) no-repeat;
	display: none;
	transition: all 0.3s ease;
}
.maptitlebox div:hover::after{
	display: block;
}
.maptitlebox img{
	height: 3.125rem;
	width: auto;
	display: block;
}
.maptitlebox .actived::after{
	display: block;
}
.maptitlebox .actived{
	color: #DE2910;
	font-weight: bold;
	position: relative;
}
.maptitlebox .actived::before{
	content: "";
	position: absolute;
	bottom: 0.5rem;
	left: 0;
	width: 100%;
	height: 2px;
	background: #DE2910;
	transform: scaleX(0.8);
	transition: all 0.3s ease;
}
.maptitlebox .actived:hover::before{
	transform: scaleX(1);
}
.maptitlebox .selecting{
	color: #DE2910;
	transform: scale(1.05);
	box-shadow: 0 2px 8px rgba(222, 41, 16, 0.3);
	transition: all 0.3s ease;
}
.mapbtntitle{
	width: 66.666666rem;
	height: 4.166666rem;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #FFFFFF;
	font-size: 1.5625rem;
	font-weight: bold;
	background: #FFFFFF;
	margin: 0px auto;
	background: url(../img/foottop.png) no-repeat;
	background-size: 100%;
	border-top-left-radius: 0.833333rem;
	border-top-right-radius: 0.833333rem;
	position: relative;
}
.mapbtntitle img{
	width: 0.885416rem;
	display: block;
	height: auto;
	padding-right: 0.520833rem;
}
.borderdiv{
	width: 66.666666rem;
	height: 2px;
	margin: 0px auto;
	background: linear-gradient(to right, #c00714, #c00714, #ffc156);
}
.zjbox{
	width: 66.666666rem;
	max-width: 90%;
	height: auto;
	margin: 0px auto;
	display: flex;
	background: #FFFFFF;
	justify-content: space-between;
	position: relative;
	padding-top: 0.78125rem;
}
.zjbox::after{
	content: "";
	width: 0.052083rem;
	height: calc(100% -  0.78125rem);
	background: #c1c1c1;
	position: absolute;
	right: 0;
	left: 0;
	top: 0.78125rem;
	margin: auto;
}
.zjboxmain{
	width: 100%;
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
	/* padding-bottom: 3.125rem; */
}
.zjitem{
	height: 2rem;
	width: calc(50% - 1.5625rem);
	display: flex;
	align-items: center;
	margin: 0 0.78125rem 1rem 0.78125rem;
	border-bottom: 1px dashed #dedede;
	cursor: pointer;
	transition: all 0.3s ease;
	position: relative;
}
.zjitem:hover{
	background-color: rgba(222, 41, 16, 0.05);
	transform: translateX(5px);
}
.zjitem:hover .zjnr{
	color: #DE2910;
}
.zjitem:hover .zjyuan{
	background: #DE2910;
	transform: scale(1.5);
}
.zjyuan{
	border-radius: 100px;
	background: #cecece;
	width: 0.260416rem;
	height: 0.260416rem;
	transition: all 0.3s ease;
}
.zjnr{
	width: calc(100% - 0.260416rem - 0.520833rem - 1.5625rem - 7.8125rem);
	font-size: 0.833333rem;
	color: #333333;
	padding-left: 0.520833rem;
	padding-right: 1.5625rem;
	overflow: hidden;
	text-overflow:ellipsis;
	white-space: nowrap;
}
.zjtime{
	color: #cecece;
	font-size: 0.729166rem;
	display: flex;
	align-items: center;
	width: 7.8125rem;
	transition: all 0.3s ease;
}
.zjtime img{
	padding-right: 0.260416rem;
	width: 0.9375rem;
	display: block;
	transition: all 0.3s ease;
}
.zjitem:hover .zjtime{
	color: #DE2910;
}
.zjitem:hover .zjtime img{
	transform: scale(1.1) rotate(15deg);
}
.fybox{
	width: 66.666666rem;
	height: auto;
	margin: 0px auto;
	background: #FFFFFF;
	padding-top: 2.083333rem;
	padding-bottom: 1.5625rem;
}

.contentbox{
		width: 66.666666rem;
		margin: 0px auto;
		background: #FFFFFF;
		margin-top: 3.125rem;
		padding-bottom: 3.645833rem;
		margin-bottom: 3.125rem;
		min-height: 52.083333rem;
}
.boxtop{
	height: 2.604166rem;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1px dashed #dedede;
}
.boxtop div{
	display: flex;
	align-items: center;
	font-size: 0.729166rem;
	color: #e1e1e1;
	padding-left: 0.78125rem;
	padding-right: 0.78125rem;
}
.boxtop div img{
	padding-right: 0.260416rem;
}
.xqnrtitle{
	font-size: 1.354166rem;
	font-weight: bold;
	color: #333333;
	line-height: 5.208333rem;
	text-align: center;
	overflow: hidden;
	            text-overflow:ellipsis;
	            white-space: nowrap;
}
.xqnr{
	padding-left: 5.208333rem;
	padding-right: 5.208333rem;
	font-size: 0.833333rem;
	color: #666666;
	line-height: 1.5625rem;
	text-indent: 2em;
}
.xqnr img{
	max-width: 80%;
	display: block;
	margin: 0px auto;
	padding-top: 1.5625rem;
	padding-bottom: 1.5625rem;
}
.zwf{
	height: 3.125rem;
}
#citylist label{
	color: #999999;
	font-size: 0.833333rem;
}
#nodate{
	font-size: 0.833333rem;
	color: #333333;
	display: none;
	width: 66.666666rem;
	text-align: center;
	margin: 0px auto;
	background: #FFFFFF;
	padding-bottom: 1.041666rem;
	padding-top: 0.520833rem;
}

/* PC端大屏幕优化 - 确保双列布局 */
@media screen and (min-width: 1024px) {
    .zjbox {
        width: 66.666666rem;
        max-width: 1200px;
    }
    
    .zjboxmain {
        display: flex;
        flex-wrap: wrap;
        justify-content: space-between;
    }
    
    .zjitem {
        width: calc(50% - 1.5625rem);
        margin: 0 0.78125rem 1rem 0.78125rem;
        float: none;
    }
}

/* 基础响应式设置 */
@media screen and (max-width: 768px) {
    .content {
        background-size: cover;
        padding: 0;
        overflow: hidden;
    }
    
    /* 地图容器适配 */
    #map {
        width: 95%;
        margin: 0.5rem auto;
        transform-origin: center center;
    }
    
    .jsmap-svg-container {
        width: 95%;
        min-height: 280px;
        transform-origin: center center;
        margin: 0 auto;
    }
    
    /* 调整SVG容器 */
    #map svg,
    .jsmap-svg svg {
        width: 95% !important;
        height: auto !important;
        max-height: 70vh;
        margin: 0 auto;
        display: block;
        transform-origin: center center;
        position: relative;
        left: 50%;
        transform: translateX(-50%);
    }
    
    /* 优化地图文字 */
    #map svg text,
    .jsmap-svg text {
        font-size: 2.5vw !important;
        font-weight: 500 !important;
    }
    
    /* 标题图片适配 */
    .maptitle img {
        width: 60%;
        margin-top: 1rem;
    }
    
    /* 地图文字适配 */
    #map svg text,
    .jsmap-svg text {
        font-size: 3vw !important;
        font-weight: 500 !important;
    }
    
    /* 城市列适配 */
    .maptitlebox {
        width: 100%;
        height: auto;
        flex-wrap: wrap;
        padding: 0.5rem;
    }
    
    .maptitlebox div {
        padding: 0.5rem;
        font-size: 0.875rem;
        line-height: 1.5;
        height: auto;
    }
    
    /* 标题栏适配 */
    .mapbtntitle {
        width: 100%;
        height: auto;
        padding: 1rem;
        font-size: 1.125rem;
        background-size: cover;
        border-top-left-radius: 0.833333rem;
        border-top-right-radius: 0.833333rem;
    }
    
    /* 内容列表适配 */
    .zjbox {
        width: 100%;
    }
    
    .zjitem {
        width: calc(100% - 2rem);
        margin: 0 1rem;
        float: none;
    }
    
    /* 分页适配 */
    .fybox {
        width: 100%;
        padding: 1rem;
    }
}

/* 不同尺寸屏幕的优化 */
@media screen and (max-width: 480px) {
    #map {
        width: 95%;
    }
    
    .jsmap-svg-container {
        width: 95%;
        margin: 0 auto;
    }
    
    #map svg,
    .jsmap-svg svg {
        width: 95% !important;
    }
}

/* iPhone SE 等超小屏幕优化 */
@media screen and (max-width: 375px) {
    #map {
        width: 95%;
    }
    
    .jsmap-svg-container {
        width: 95%;
        margin: 0 auto;
    }
    
    #map svg,
    .jsmap-svg svg {
        width: 95% !important;
    }
}

/* 横屏模式优化 */
@media screen and (max-width: 768px) and (orientation: landscape) {
    .jsmap-svg-container {
        width: 85%;
        aspect-ratio: 16/9;
        transform: scale(0.85);
        min-height: 220px;
    }
    
    #map svg,
    .jsmap-svg svg {
        transform: translateX(-3%);
    }
}

/* 优化地图容器溢出处理 */
.content {
    overflow-x: hidden;
    max-width: 100vw;
    position: relative;
}

/* 确保地图SVG正确缩放和定位 */
#map svg,
.jsmap-svg svg {
    max-width: 95%;
    height: auto;
    transform-box: fill-box;
    transform-origin: center center;
    position: relative;
    margin: 0 auto;
}

/* 优化地图加载动画 */
.jsmap-svg-container {
    opacity: 0;
    animation: mapFadeIn 0.8s ease forwards;
    will-change: transform, opacity;
    backface-visibility: hidden;
    -webkit-backface-visibility: hidden;
}

@keyframes mapFadeIn {
    from {
        opacity: 0;
        transform: scale(0.9);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

/* 优化触摸交互 */
@media (hover: none) {
    .jsmap-svg-container {
        touch-action: manipulation;
    }
    
    #map svg path:active {
        transform: scale(0.98);
        transition: transform 0.2s ease;
    }
}

/* 移动端适配 */
@media screen and (max-width: 768px) {
    /* 基础布局优化 */
    .content {
        background: none;
        padding: 0;
        width: 100%;
        background-color: #f5f5f5;
    }
    
    /* 隐藏地图相关元素 */
    #map, 
    .jsmap-svg-container,
    .borderdiv {
        display: none;
    }
    
    /* 标题区域优化 */
    .maptitle {
        margin: 0;
        padding: 1rem;
        background: #fff;
    }
    
    .maptitle img {
        width: 70%;
        max-width: 280px;
        margin: 0 auto;
        display: block;
    }
    
    /* 标题栏优化 */
    .mapbtntitle {
        width: 100%;
        padding: 1rem;
        background: linear-gradient(to right, #DE2910, #FF4E4E);
        color: #fff;
        display: flex;
        align-items: center;
        box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        border-top-left-radius: 0.833333rem;
        border-top-right-radius: 0.833333rem;
        position: relative;
        justify-content: flex-start;
    }
    
    .mapbtntitle img {
        width: 1.2rem;
        margin-right: 0.5rem;
    }
    
    .mapbtntitle label {
        font-size: 1rem;
        font-weight: 500;
    }
    
    /* 城市列表优化 */
    .maptitlebox {
        width: 100%;
        display: flex !important; /* 强制显示 */
        padding: 1rem;
        background: #fff;
        flex-wrap: wrap;
        gap: 0.5rem;
        justify-content: flex-start;
    }
    
    .maptitlebox div {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        background: #f5f5f5;
        border-radius: 2rem;
        border: 1px solid #eee;
        line-height: 1.2;
        height: auto;
        transition: all 0.3s ease;
    }
    
    .maptitlebox div:hover {
        background: rgba(222, 41, 16, 0.1);
        color: #DE2910;
        transform: translateY(-2px);
    }
    
    .maptitlebox .actived {
        background: #DE2910;
        color: #fff;
        border-color: #DE2910;
        transform: none;
    }
    
    .maptitlebox .actived:hover {
        transform: translateY(-2px);
        box-shadow: 0 3px 6px rgba(0,0,0,0.1);
    }
    
    .maptitlebox .selecting {
        background: rgba(222, 41, 16, 0.7);
        color: #fff;
        transform: scale(1.05);
    }
    
    /* 内容列表优化 */
    .zjbox {
        width: 100%;
        background: #fff;
        margin-top: 0.5rem;
    }
    
    .zjboxmain {
        width: 100%;
        padding: 0.5rem 1rem;
    }
    
    .zjitem {
        width: 100%;
        padding: 1rem 0;
        margin: 0;
        border-bottom: 1px solid #eee;
        display: flex;
        align-items: flex-start;
    }
    
    .zjyuan {
        width: 6px;
        height: 6px;
        margin-top: 0.5rem;
        background: #DE2910;
        border-radius: 50%;
        flex-shrink: 0;
    }
    
    .zjnr {
        flex: 1;
        padding: 0 0.8rem;
        font-size: 0.95rem;
        line-height: 1.5;
        color: #333;
    }
    
    .zjtime {
        width: auto;
        min-width: 5.5rem;
        font-size: 0.8rem;
        color: #999;
        display: flex;
        align-items: center;
        flex-shrink: 0;
    }
    
    .zjtime img {
        width: 0.9rem;
        margin-right: 0.3rem;
    }
    
    /* 分页优化 */
    .fybox {
        width: 100%;
        padding: 1rem;
        background: #fff;
        display: flex;
        justify-content: center;
        align-items: center;
        flex-wrap: wrap;
        gap: 0.5rem;
    }
    
    .fybox span,
    .fybox label {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
        background: #f5f5f5;
        border-radius: 0.25rem;
        color: #666;
    }
    
    .fybox .actinum {
        background: #DE2910;
        color: #fff;
    }
    
    /* 无数据提示优化 */
    #nodate {
        width: 100%;
        padding: 2rem 1rem;
        font-size: 0.95rem;
        text-align: center;
        color: #999;
        background: #fff;
    }
    
    /* 移动端重置按钮样式 */
    .resetBtn {
        position: absolute;
        right: 0.75rem;
        font-size: 0.75rem;
        padding: 0.25rem 0.5rem;
        background: rgba(255, 255, 255, 0.25);
        border: 1px solid rgba(255, 255, 255, 0.5);
    }
    
    .reset-icon {
        font-size: 0.875rem;
        margin-right: 0.25rem;
    }
}

/* 超小屏幕优化 */
@media screen and (max-width: 375px) {
    .maptitle img {
        width: 80%;
    }
    
    .maptitlebox div {
        padding: 0.4rem 0.8rem;
        font-size: 0.85rem;
    }
    
    .zjnr {
        font-size: 0.9rem;
        padding: 0 0.6rem;
    }
    
    .zjtime {
        font-size: 0.75rem;
        min-width: 5rem;
    }
}

/* 触摸反馈优化 */
@media (hover: none) {
    .zjitem:active {
        background: #f9f9f9;
    }
    
    .maptitlebox div:active {
        opacity: 0.8;
    }
    
    .fybox span:active,
    .fybox label:active {
        opacity: 0.8;
    }
}

.fybox span, .fybox label{
	cursor: pointer;
	transition: all 0.3s ease;
}

.fybox span:hover, .fybox label:hover{
	color: #DE2910;
	transform: translateY(-2px);
}

.fybox .actinum{
	color: #DE2910;
	font-weight: bold;
	position: relative;
}

.fybox .actinum::after{
	content: "";
	position: absolute;
	bottom: -3px;
	left: 50%;
	transform: translateX(-50%);
	width: 50%;
	height: 2px;
	background-color: #DE2910;
	transition: all 0.3s ease;
}

.resetBtn {
	position: absolute;
	right: 1.5625rem;
	display: flex;
	align-items: center;
	background: rgba(255, 255, 255, 0.2);
	border: 1px solid rgba(255, 255, 255, 0.4);
	color: #FFFFFF;
	font-size: 0.833333rem;
	padding: 0.3125rem 0.625rem;
	border-radius: 1.041666rem;
	cursor: pointer;
	transition: all 0.3s ease;
	box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.resetBtn:hover {
	background: rgba(255, 255, 255, 0.3);
	transform: translateY(-2px);
	box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.resetBtn:active {
	transform: translateY(0);
}

.reset-icon {
	font-style: normal;
	font-size: 1.0625rem;
	margin-right: 0.3125rem;
	display: inline-block;
	transition: all 0.3s ease;
}

.resetBtn:hover .reset-icon {
	transform: rotate(180deg);
}