<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>思政一体化平台-学习任务管理</title>
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<style>
			.rwlistopview {
				display: flex;
				font-size: 0.833333rem;
				color: #999999;
				background: #fbfbfb;
				padding-top: 0.260417rem;
				padding-bottom: 0.260417rem;
				border-top: 0.052083rem solid #666666;
			}
			.selectrwbox{
				padding-bottom: 1.041666rem;
				display: flex;
				align-items: center;
			}
			
			.selectrwbox input,.selectrwbox select{
				background: #f3f3f3;
				height: 1.25rem;
				color: #999999;
				border: none;
				outline-style: none;
				padding: 0px 0.260417rem;
				margin-right: 0.5rem;
				font-size: 0.72916rem;
			}
			
			.selectrwbox button{
				border: none;
				background: #c00714;
				color: #FFFFFF;
				height: 1.25rem;
				width: 3rem;
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 0.72916rem;
				margin-right: 0.5rem;
				border-radius: 0.260416rem;
			}

			.rwcall1 {
				width: 15.625rem;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.rwcall2 {
				width: 7.8125rem;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.rwcall3 {
				width: 13.020833rem;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.rwcall4 {
				width: 7.8125rem;
				text-align: center;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}

			.rwcall5 {
				width: calc(100% - 15.625rem - 7.8125rem - 7.8125rem - 13.020833rem);
				text-align: center;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.srwitem{
				display: flex;
				padding: 1.041667rem 0;
				border-bottom: 0.052083rem dashed #c1c1c1;
			}
			.srwitem:last-child{
				border: none;
			}
			.srwitem .rwcall1{
				font-size: 0.833333rem;
				color: #666666;
			}
			.srwitem .rwcall2{
				font-size: 0.833333rem;
				color: #cccccc;
			}
			.srwitem .rwcall3{
				font-size: 0.833333rem;
				color: #cccccc;
			}
			.srwitem .rwcall4{
				font-size: 0.833333rem;
				color: #cccccc;
			}
			.srwitem .rwcall5{
				display: flex;
				align-items: center;
				justify-content: center;
			}
			.srwitem .rwcall5 div{
				font-size: 0.729167rem;
				color: #FFFFFF;
				background: #c00714;
				width: 5.208333rem;
				height: 1.458333rem;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 0.260417rem;
				cursor: pointer;
			}
			.rwtitletc{
				background: #a4080c;
			}
			.rwstr{
				background: #bf5355;
			}
			.rwstr .rwstritem{
				color: #FFFFFF;
			}
			.rwitemleft{
				color: #e56c74;
				background: #fff2f3;
			}
			.inxx{
				width: 5.208333rem;
				height: 1.458333rem;
				color: #FFFFFF;
				background: #c00714;
				display: flex;
				align-items: center;
				justify-content: center;
				border-radius: 0.260417rem;
				cursor: pointer;
				font-size: 0.729167rem;
			}
			.nono{
				background: #999999;
			}
		</style>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a class="leftitem activeleftitem">学习任务</a>
						<a href="learningrecords.html" class="leftitem">学习路径</a>
						<a href="achievement.html" class="leftitem">考试成绩</a>
						<a href="releaseyourvoice.html" class="leftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="userinfotopdiv">
						<a class="acccccg">任务列表</a>
					</div>
					<div class="paperscroll">
						<div class="selectrwbox">
							<input id="ssrwname" placeholder="请输入任务名称"/>
							<input id="ssjsname" placeholder="请输入发布教师名称"/>
							<select id="ssxkid">
								<option value="0">请选择学科</option>
							</select>
							<button onclick="ssrwlist()">查询</button>
							<button onclick="clearss()">清空</button>
						</div>
						<div class="rwlistopview">
							<div class="rwcall1">任务名称</div>
							<div class="rwcall2">学科</div>
							<div class="rwcall3">任务时间</div>
							<div class="rwcall4">发布教师</div>
							<div class="rwcall5">操作</div>
						</div>
						<div class="xxrwlist" id="list">
							
							
						</div>
						<div class="fybox" id="fyq">
							<span id="sy">首页</span>
							<span id="syy">上一页</span>
							<div class="num" id="num">
							</div>
							<span id="xyy">下一页</span>
							<span id="wy">尾页</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>

		<div class="xxrwtc">
			<div class="tcview">
				<div class="rwtcbox">
					<div class="rwtitletc"><label id="tctitlerw"></label>
						<img onclick="closeyulan()" src="img/rwclose1.png" />
					</div>
					<div class="rwstr">
						<div class="rwstritem">
							<img src="img/rwtime11.png" />
							<label id="yldate"></label>
						</div>
						<div class="rwstritem">
							<img src="img/rwxk11.png" />
							<label id="ylxk"></label>
						</div>
						
						
						
						<!-- <div class="rwstritem">
							<label onclick="showbaogao()">查看报告</label>
						</div> -->
						<!-- <div class="rwstritem">
							<img src="img/rwxy1.png"/>
							学院
						</div>
						<div class="rwstritem">
							<img src="img/rwzy1.png"/>
							专业
						</div>
						<div class="rwstritem">
							<img src="img/rwclass1.png"/>
							班级
						</div> -->
					</div>
					<div class="rwinfobox" id="rwlist">

					</div>
				</div>
			</div>
		</div>

		<div id="deletebox">
			<div class="deletesss">
				<div class="sjtitle2">
					<div>系统提示</div>
					<label class="paperyueclose" onclick="closedelte()"></label>
				</div>
				<div class="delstr">
					是否删除“<label id="delname"></label>”？
				</div>
				<div class="submitbox deldelpaper">
					<div class="bc" onclick="deletesubmit()">删除</div>
					<div class="gb" onclick="closedelte()">关闭</div>
				</div>
			</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
		<script>
			let showtaskid = null
			function showbaogao(){
				axios({
					method: 'GET',
					url: baseurl + '/student/stat/pdf?taskId=' + showtaskid,
					responseType: 'blob', //响应类型
					xsrfHeaderName: 'Authorization',
					headers: {
						'Content-Type': 'application/json',
						"Authorization": sessionStorage.getItem("header")
					},
				}).then(response => {
					let blob = new Blob([response.data], {
						type: 'application/pdf;charset=utf-8' //文件格式对应文件后缀xls（比如xlsx/dotx等）
					})
					let url = window.URL.createObjectURL(blob)
					window.open(url)
				}).catch(error => {
							
				})
			}
			
			
			
			//开始时间和结束时间分开  两个input    修改的时候  组卷界面重写  记录原有数据
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()

				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getfooterlink()
				getrwlist()
				getxuekelist()
			})
			let deleteid = null

			function deletess(str) {
				deleteid = $(str).attr("data-id")
				$("#delname").html($(str).attr("data-str"))
				$("#deletebox").attr("style", "display: flex;")
			}

			function deletesubmit() {
				$.ajax({
					url: baseurl + "/learning-tasks/delete/" + deleteid,
					type: 'delete',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							cocoMessage.success(1000, "删除成功！")
							getrwlist()
						} else {
							cocoMessage.error(1000, "删除失败！")
						}
						closedelte()
					}
				})
			}

			function closedelte() {
				$("#deletebox").attr("style", "display: none;")
			}

			function showyulan(item) {
				let typess = $(item).attr("data-type")
				showtaskid = $(item).attr("data-id")
				$("#yldate").html($(item).attr("data-date"))
				$("#ylxk").html($(item).attr("data-xkname"))
				$("#tctitlerw").html($(item).attr("data-name"))
				$('.xxrwtc').show()
				$.ajax({
					url: baseurl + "/learning-tasks-resource/getByTaskIdMap/" + $(item).attr("data-id"),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ''
							console.log(res.data)
							res.data.forEach((item) => {
								html +=
									`<div class="rwinfotitle">${item.name}(${item.List.length})</div><div class="rwinfolist">`
								item.List.forEach((item2, index) => {
									if(item.name == '教学资源'){
										html += `<div class="rwinfoitem"><div class="rwitemleft">资源${index+1}</div><div class="rwitemright"><div>资源标题: <label>${item2.resourceName}</label></div></div>`
										if(typess == '0'){
											html += `<div class="inxx" onclick="inkczy(this)" data-id="${item2.metaId}" data-code="${item2.resourceCode}" data-url="${item2.resourceAddress}" data-tasksid="${item2.tasksId}">进入学习</div>`
										}else{
											html += `<div class="inxx nono">进入学习</div>`
										}
										
										html += `</div>`
									}else if(item.name == '红色书籍'){
										html += `<div class="rwinfoitem"><div class="rwitemleft">资源${index+1}</div><div class="rwitemright"><div>资源标题: <label>${item2.resourceName}</label></div></div>`
										if(typess == '0'){
											html += `<div class="inxx" onclick="insjzy(this)" data-id="${item2.inforId}" data-code="${item2.resourceCode}" data-url="${item2.resourceAddress}" data-tasksid="${item2.tasksId}">进入学习</div>`
										}else{
											html += `<div class="inxx nono">进入学习</div>`
										}
										
										html += `</div>`
									}else if(item.name == 'VR红色游学'){
										html += `<div class="rwinfoitem"><div class="rwitemleft">资源${index+1}</div><div class="rwitemright"><div>资源标题: <label>${item2.resourceName}</label></div></div>`
										if(typess == 0){
											html += `<div class="inxx" onclick="invrzy(this)" data-id="${item2.inforId}" data-code="${item2.resourceCode}" data-url="${item2.resourceAddress}" data-tasksid="${item2.tasksId}">进入学习</div>`
										}else{
											html += `<div class="inxx nono">进入学习</div>`
										}
										`<div class="inxx" onclick="invrzy(this)" data-id="${item2.inforId}" data-code="${item2.resourceCode}" data-url="${item2.resourceAddress}" data-tasksid="${item2.tasksId}">进入学习</div>`
										html += `</div>`
									}else if(item.name == '考试资源'){
										html += `<div class="rwinfoitem"><div class="rwitemleft">资源${index+1}</div><div class="rwitemright"><div>资源标题: <label>${item2.resourceName}</label></div></div>`
										if(typess == 0){
											html += `<div class="inxx" onclick="inkszy(this)" data-id="${item2.inforId}" data-code="${item2.resourceCode}" data-url="${item2.resourceAddress}" data-tasksid="${item2.tasksId}">进入学习</div>`
										}else{
											html += `<div class="inxx nono">进入学习</div>`
										}
										
										html += `</div>`
									}
								})
								html += `</div>`
							})

							$("#rwlist").html(html)
						}
					}
				})
			}
			
			//进入课程学习
			function inkczy(item){
				let id = $(item).attr("data-id")
				let tasksid = $(item).attr("data-tasksid")
				window.location = 'onlinelearning4.html?id='+id + '&taskid='+tasksid
			}
			//进入红色书籍学习
			function insjzy(item){
				let id = $(item).attr("data-id")
				let tasksid = $(item).attr("data-tasksid")
				window.location = "onlinelearning5.html?id="+id+ '&taskid='+tasksid
			}
			//进入VR红色游学学习
			function invrzy(item){
				let id = $(item).attr("data-id")
				let tasksid = $(item).attr("data-tasksid")
				let json = {
					infoId: id, //信息id
					categoryId: null, //所属分类id
					totalInfo: "1", //总时长，总页数
					positioning: "", //学习了多久，多少页    
					progress: 0, //进度 百分比
					type: 'VR红色游学',
					learningTime: 0,
					sectionId: null,//学科ID
					status: "1",
					taskId: tasksid
				}
				$.ajax({
					url: baseurl + "/study/record/add",
					type: 'post',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: JSON.stringify(json),
					dataType: 'json',
					success: (res) => {
						window.open($(item).attr("data-url"))
					}
				})
			}
			//进入考试资源学习
			function inkszy(item){
				let id = $(item).attr("data-id")
				let tasksid = $(item).attr("data-tasksid")
				window.location = 'examination2.html?id='+id + '&taskid='+tasksid
			}
			
			
			
			function closeyulan() {
				$('.xxrwtc').hide()
			}
			let pageindex = 1
			let pageSize = 15

			function getnewlist(index) {
				pageindex = index
				getrwlist()
			}
			function getbaogao(item){
				showtaskid = $(item).attr("data-id")
				showbaogao()
			}
			
			function getxuekelist(){
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'get',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						let html = '<option value="0">请选择学科</option>'
						res.data.forEach((item)=>{
							html+=`<option value="${item.id}">${item.name}</option>`
						})
						$("#ssxkid").html(html)
					}
				})
			}
			
			let ssrwname = null
			let sslsname = null
			let sxkid = null
			function ssrwlist(){ //搜索按钮被点击
				pageindex = 1
				ssrwname = $("#ssrwname").val()
				sslsname = $("#ssjsname").val()
				if($("#ssxkid").val() == '0'){
					sxkid = null
				}else{
					sxkid = $("#ssxkid").val()
				}
				getrwlist()
			}
			
			function clearss(){
				ssrwname = null
				sslsname = null
				sxkid = null
				pageindex = 1
				$("#ssrwname").val("")
				$("#ssjsname").val("")
				$("#ssxkid").val("0")
				getrwlist()
			}
			
			function getrwlist() {
				$.ajax({
					url: baseurl + "/learning-tasks/weblist",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: pageindex,
						pageSize: pageSize,
						creatorName: sslsname,//老师名称
						tasksName: ssrwname,//任务名称
						sectionId: sxkid //学科ID
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							console.log(res.data)
							let html = ""
							res.data.list.forEach((item) => {
								html += `<div class="srwitem">
								<div class="rwcall1">${item.tasksName}</div>
								<div class="rwcall2">${item.sectionName}</div>
								<div class="rwcall3" title="${setDate(item.startTime)+'-'+setDate(item.endTime)}">${setDate(item.startTime)}-${setDate(item.endTime)}</div>
								<div class="rwcall4">${item.creatorName == null ? '无' : item.creatorName}</div>
								<div class="rwcall5">
									<div onclick="showyulan(this)" data-id="${item.id}" data-name="${item.tasksName}" data-date="${setDate(item.startTime)+'-'+setDate(item.endTime)}" data-xkname="${item.sectionName}" data-type="${item.isExpired}">查看</div>`
									if(item.isExpired == 1){
										html += `<div style="margin-left: 0.4rem;" onclick="getbaogao(this)" data-id="${item.id}">报告</div>`
									}else{
										html += `<div style="margin-left: 0.4rem;background: #999999;">报告</div>`
									}
									
								html+=`</div>
							</div>`
							})
							$("#list").html(html)

							pages = res.data.pages
							if (pages > 1) {
								let numhtml = ""
								//当页数大于5
								if (pages > 5) {
									if (pageindex <= 5) { //当前页码小于5的时候右侧不变
										for (let a = 1; a <= 10; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
										numhtml += '<label onclick="getnewlist(11)">...</label>'
									} else if (5 < pageindex & pageindex < pages - 5) {
										numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) - 5) +
											')">...</label>'
										for (let a = pageindex - 4; a <= pageindex + 4; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
										numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) + 5) +
											')">...</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + (pages - 11) +
											')">...</label>'
										for (let a = pages - 10; a <= pages; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
									}
								} else {
									for (let a = 1; a <= pages; a++) {
										if (pageindex == a) {
											numhtml += '<label class="actinum" onclick="getnewlist(' + a +
												')">' + a +
												'</label>'
										} else {
											numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
										}
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
						}
					}
				})
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 						.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '年' + MM + '月' + d + '日 '
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
