<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>用户信息 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="css/mobile-base.css">
    <link rel="stylesheet" href="css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../PC/js/coco-message.js"></script>
    <script src="mobile-config.js"></script>
    <script src="js/mobile-base.js"></script>
</head>
<body class="mobile-userinfo">
    <!-- 加载动画 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <p>正在处理登录信息...</p>
    </div>

    <script>
        // 获取URL参数
        function getUrlParam(name) {
            const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            const r = window.location.search.substr(1).match(reg);
            if (r != null) return unescape(r[2]);
            return null;
        }

        // 页面加载完成后处理登录
        document.addEventListener('DOMContentLoaded', function() {
            const ticket = getUrlParam('ticket');
            
            if (ticket) {
                // 有ticket参数，进行CAS登录
                const json = {
                    ticket: ticket,
                    service: window.location.href.split('?')[0]
                };
                
                $.ajax({
                    url: baseurl + "/student/caslogin",
                    type: 'POST',
                    data: JSON.stringify(json),
                    contentType: "application/json",
                    dataType: 'json',
                    success: (res) => {
                        if (res.code == "200") {
                            // 储存token和用户信息
                            sessionStorage.setItem('header', res.data.scheme + res.data.token);
                            sessionStorage.setItem('userinfo', JSON.stringify(res.data.student));
                            
                            // 登录成功，跳转到首页
                            setTimeout(() => {
                                window.location.href = 'index.html';
                            }, 1000);
                        } else {
                            alert('登录失败：' + res.message);
                            window.location.href = 'index.html';
                        }
                    },
                    error: (err) => {
                        console.error('CAS登录失败:', err);
                        alert('登录失败，请稍后重试');
                        window.location.href = 'index.html';
                    }
                });
            } else {
                // 没有ticket参数，检查是否已登录
                const userinfo = sessionStorage.getItem("userinfo");
                if (userinfo) {
                    // 已登录，跳转到个人中心
                    window.location.href = 'pages/profile.html';
                } else {
                    // 未登录，跳转到首页
                    window.location.href = 'index.html';
                }
            }
        });
    </script>
</body>
</html>
