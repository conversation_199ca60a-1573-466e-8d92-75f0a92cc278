<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-心声社区</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/community.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<style>
			/* 美化列表样式 */
			.table {
				width: 100%;
				max-width: 100%;
				table-layout: fixed;
				overflow: hidden;
				box-sizing: border-box;
			}
			.table .tr {
				display: flex;
				width: 100%;
				max-width: 100%;
				transition: all 0.3s ease;
				border-radius: 6px;
				margin-bottom: 3px;
				border: 1px solid transparent;
				background: #fafafa;
				box-sizing: border-box;
			}
			.table .tr:hover {
				background-color: #f8f9fa;
				transform: translateY(-1px);
				box-shadow: 0 3px 12px rgba(0,0,0,0.08);
				border-color: #e8f0fe;
			}
			.table .tr:first-child {
				background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
				color: white;
				font-weight: 600;
				letter-spacing: 0.5px;
			}
			.table .tr:first-child:hover {
				transform: none;
				box-shadow: none;
			}
			.table .tr:first-child .th {
				color: white !important;
			}
			/* 列宽调整 - 去掉类型列，重新分配宽度 */
			.th:nth-child(1) { flex: 0 0 55%; min-width: 0; padding: 8px 5px; } /* 标题 */
			.th:nth-child(2) { flex: 0 0 15%; min-width: 0; padding: 8px 3px; } /* 作者 */
			.th:nth-child(3) { flex: 0 0 18%; min-width: 0; padding: 8px 3px; } /* 发布时间 */
			.th:nth-child(4) { flex: 0 0 12%; min-width: 0; padding: 8px 3px; } /* 点赞/浏览 */
			
			.bt {
				color: #2c3e50;
				font-weight: 500;
				font-size: 15px;
				line-height: 1.4;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			.tr:hover .bt {
				color: #c00714;
				font-weight: 600;
			}
			.th {
				font-size: 15px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				word-break: break-word;
				line-height: 1.2;
				display: flex;
				align-items: center;
			}
			/* 发布时间列允许换行 */
			.th:nth-child(3) {
				white-space: normal;
				line-height: 1.3;
				word-break: break-word;
			}
			.fl, .zz, .sj {
				color: #6c757d;
				font-size: 12px;
			}
			.tr:hover .fl, .tr:hover .zz, .tr:hover .sj {
				color: #495057;
			}
			.ll {
				font-size: 12px;
				display: flex;
				align-items: center;
				justify-content: flex-start;
				gap: 6px;
				flex-wrap: wrap;
			}
			.ll .stat-item {
				display: inline-flex;
				align-items: center;
				gap: 3px;
				color: #6c757d;
				font-size: 11px;
			}
			.tr:hover .ll .stat-item {
				color: #495057;
			}
			.like-icon {
				width: 16px;
				height: 16px;
				display: inline-flex;
				align-items: center;
				justify-content: center;
				background: none;
				border-radius: 0;
				position: relative;
			}
			.like-icon::before {
				content: "";
				width: 16px;
				height: 16px;
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23ff6b6b'%3E%3Cpath d='M20.84 4.61a5.5 5.5 0 0 0-7.78 0L12 5.67l-1.06-1.06a5.5 5.5 0 0 0-7.78 7.78l1.06 1.06L12 21.23l7.78-7.78 1.06-1.06a5.5 5.5 0 0 0 0-7.78z'/%3E%3C/svg%3E");
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				line-height: 1;
			}
			.view-icon {
				width: 16px;
				height: 16px;
				display: inline-flex;
				align-items: center;
				justify-content: center;
				background: none;
				border-radius: 0;
				position: relative;
			}
			.view-icon::before {
				content: "";
				width: 16px;
				height: 16px;
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='none' stroke='%234ecdc4' stroke-width='2'%3E%3Cpath d='M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z'/%3E%3Ccircle cx='12' cy='12' r='3'/%3E%3C/svg%3E");
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				line-height: 1;
			}
			
			/* 搜索框区域美化 */
			.sqtop {
				background: white;
				border-radius: 8px;
				padding: 16px;
				margin-bottom: 16px;
				box-shadow: 0 2px 8px rgba(0,0,0,0.05);
				border: 1px solid #f0f0f0;
				display: flex;
				justify-content: space-between;
				align-items: center;
				flex-wrap: nowrap;
				gap: 15px;
				box-sizing: border-box;
				overflow: hidden;
				min-height: 70px;
			}
			.sqtopl {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				gap: 8px;
				flex: 1;
				min-width: 0;
			}
			.sqtopr {
				flex-shrink: 0;
				width: auto;
				display: flex;
				align-items: center;
			}
			.sqtopl label {
				color: #495057;
				font-weight: 500;
				font-size: 13px;
				white-space: nowrap;
				margin-right: 4px;
			}
			.sqtopl span {
				padding: 5px 10px;
				background: #f8f9fa;
				border: 1px solid #e9ecef;
				border-radius: 15px;
				cursor: pointer;
				transition: all 0.3s ease;
				color: #6c757d;
				font-size: 12px;
				white-space: nowrap;
			}
			.sqtopl span:hover, .activespan {
				background: #c00714 !important;
				color: white !important;
				border-color: #c00714 !important;
				box-shadow: 0 2px 4px rgba(192, 7, 20, 0.3);
			}
			.sqtopl select {
				padding: 5px 8px;
				border: 1px solid #e9ecef;
				border-radius: 4px;
				background: white;
				color: #495057;
				font-size: 12px;
				min-width: 80px;
				max-width: 120px;
			}
			.sqss {
				display: flex;
				align-items: center;
				background: #f8f9fa;
				border-radius: 25px;
				padding: 2px;
				border: 1px solid #e9ecef;
				transition: all 0.3s ease;
				width: 100%;
				max-width: 280px;
				box-sizing: border-box;
			}
			.sqss:focus-within {
				border-color: #c00714;
				box-shadow: 0 0 0 2px rgba(192, 7, 20, 0.1);
				background: white;
			}
			.sqss input {
				flex: 1;
				padding: 7px 14px;
				border: none;
				border-radius: 20px;
				background: transparent;
				color: #495057;
				font-size: 13px;
				outline: none;
				min-width: 0;
			}
			.sqss input::placeholder {
				color: #9ca3af;
			}
			.sqss label {
				background: #c00714;
				color: white;
				padding: 7px 14px;
				border-radius: 20px;
				cursor: pointer;
				transition: all 0.3s ease;
				font-size: 13px;
				border: none;
				white-space: nowrap;
				flex-shrink: 0;
			}
			.sqss label:hover {
				background: #a00610;
				transform: scale(1.02);
			}
			
			/* 分页样式美化 */
			.fybox {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 20px 0;
				gap: 5px;
				flex-wrap: wrap;
			}
			.fybox span, .fybox label {
				margin: 0 2px;
				padding: 8px 12px;
				border-radius: 6px;
				cursor: pointer;
				transition: all 0.3s ease;
				border: 1px solid #e1e1e1;
				background: white;
				color: #495057;
				font-size: 13px;
			}
			.fybox span:hover, .fybox label:hover {
				background-color: #f8f9fa;
				border-color: #c00714;
				color: #c00714;
			}
			.actinum {
				background: linear-gradient(135deg, #c00714 0%, #a00610 100%) !important;
				color: white !important;
				border-color: #c00714 !important;
				box-shadow: 0 2px 4px rgba(192, 7, 20, 0.3);
			}
			.actinum:hover {
				transform: translateY(-1px);
				box-shadow: 0 4px 8px rgba(192, 7, 20, 0.4);
			}
			
			/* 当前页信息样式 */
			.page-info {
				color: #6c757d;
				font-size: 12px;
				margin-top: 10px;
				text-align: center;
				padding: 8px 16px;
				background: #f8f9fa;
				border-radius: 15px;
				border: 1px solid #e9ecef;
			}
			
			/* 右侧热门话题样式美化 */
			.boxitem {
				transition: all 0.3s ease;
				padding: 8px 0;
				border-bottom: 1px solid #f0f0f0;
			}
			.boxitem:last-child {
				border-bottom: none;
			}
			.boxitem:hover {
				background-color: #f8f9fa;
				border-radius: 4px;
				padding-left: 8px;
				padding-right: 8px;
			}
			.boxitem:hover .itemnr {
				color: #c00714;
				font-weight: 500;
			}
			.itemnr {
				color: #2c3e50;
				font-size: 13px;
				line-height: 1.4;
				transition: all 0.3s ease;
			}
			.itemsj {
				color: #6c757d;
				font-size: 11px;
				margin-top: 2px;
			}
			.newtitles1 {
				color: #2c3e50;
				font-weight: 600;
				font-size: 15px;
			}
			
			/* 容器溢出控制 */
			.newboxleft {
				overflow: hidden;
				max-width: 100%;
				box-sizing: border-box;
			}
			
			/* 响应式调整 */
			@media (max-width: 768px) {
				.sqtop {
					flex-direction: column;
					align-items: center;
					gap: 15px;
					min-height: 80px;
					flex-wrap: wrap;
				}
				.sqtopl {
					min-width: auto;
					justify-content: center;
					width: 100%;
				}
				.sqtopr {
					min-width: auto;
					width: 100%;
					justify-content: center;
				}
				.sqss {
					max-width: none;
				}
				.th:nth-child(1) { flex: 0 0 50%; padding: 8px 3px; }
				.th:nth-child(2) { flex: 0 0 18%; padding: 8px 2px; }
				.th:nth-child(3) { flex: 0 0 20%; padding: 8px 2px; }
				.th:nth-child(4) { flex: 0 0 12%; padding: 8px 2px; }
			}
		</style>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="content">
			<div class="newsbox">
				<div class="newboxleft">
					<div class="sqtop">
						<div class="sqtopl">
							<label>分类选择</label>
							<select id="wzfl" onchange="classchange()">

							</select>
							<label>排序方式</label>
							<span id="zxzd" class="activespan" onclick="selectlist('dispaly_time')">最新发布</span>
							<span id="llzd" onclick="selectlist('click_count')">浏览最多</span>
							<span id="dzzd" onclick="selectlist('give_like')">点赞最多</span>
						</div>
						<div class="sqtopr">
							<div class="sqss">
								<input id="names" placeholder="请输入关键词" />
								<label onclick="sslist()">搜索</label>
							</div>
						</div>
					</div>
					<div class="table" id="listxs">


					</div>
					<div class="fybox" id="fyq">
						<span id="sy">首页</span>
						<span id="syy">上一页</span>
						<div class="num" id="num">
						</div>
						<span id="xyy">下一页</span>
						<span id="wy">尾页</span>
						<div class="page-info" id="pageInfo"></div>
					</div>
				</div>
				<div class="newsright">
					<div class="border"></div>
					<div class="newtitles1">
						热门话题
					</div>
					<div class="boxxx" id="rmht">
						
						
					</div>
					<div class="bbbb"></div>
					<div class="border"></div>
					<div class="newtitles1">
						最新话题
					</div>
					<div class="boxxx" id="zxht">
						
					</div>
				</div>
			</div>
		</div>

		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let mysort = 'dispaly_time' // 默认按发布时间排序
			let names = null
		let pageindex = 1
		let pagesize = 10
		let pages = 0
		let classid = null
		let communitypropertyType = null

			function incontent(id) {
				window.location.href = "newscontent.html?id=" + id
			}
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				let userinfo = sessionStorage.getItem("userinfo")
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
					if(window.localStorage.getItem("jilu")){
						$.ajax({
							url: baseurl + "/study/record/add",
							type: 'post',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: window.localStorage.getItem("jilu"),
							dataType: 'json',
							success: (res) => {
								window.localStorage.clear()
							}
						})
					}
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				getclass()
				getclassid()
				getfooterlink()
				getclasslist()
			})

			function getclasslist() {
				$.ajax({
					url: baseurl + "/web/communityproperty/list/tree",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = '<option value="">全部</option>'
							res.data.map((item) => {
								html += '<option value="' + item.name + '">' + item.name + '</option>'
							})
							$("#wzfl").html(html)
						}
					}
				})
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			function getclassid() {
				$.ajax({
					url: baseurl + "/web/category/user",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							classid = res.data[0].id
							getlist()
							getlist2()
							getlist3()
						}
					}
				})
			}

			function sslist() {
				names = $("#names").val()
				getlist()
			}

			function ininfo(item) {
				window.location.href = "communitydetail.html?id=" + $(item).attr("data-id")
			}

			function selectlist(type) {
				//activespan
				// console.log(type)
				//zxzd llzd dzzd
				if (type == 'dispaly_time') {
					$("#zxzd").attr("class", "activespan")
					$("#llzd").attr("class", "")
					$("#dzzd").attr("class", "")
				} else if (type == 'click_count') {
					$("#zxzd").attr("class", "")
					$("#llzd").attr("class", "activespan")
					$("#dzzd").attr("class", "")
				} else {
					$("#zxzd").attr("class", "")
					$("#llzd").attr("class", "")
					$("#dzzd").attr("class", "activespan")
				}
				mysort = type
				pageindex = 1
				getlist()
			}
			function classchange(){
				communitypropertyType = $("#wzfl").val()
				pageindex = 1
				getlist()
			}
			function getlist() {
				let params = "?pageNum="+pageindex+"&pageSize="+pagesize+"&id="+classid+"&sort="+mysort+"&title="+names+"&communitypropertyType="+communitypropertyType
				$.ajax({
					url: baseurl + "/web/xspostByCategoryId"+params,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = '<div class="tr"><div class="th">标题</div><div class="th">作者</div>' +
								'<div class="th">发布时间</div>' +
								'<div class="th">点赞/浏览</div></div>'
							res.data.list.map((item) => {
								html += '<div onclick="ininfo(this)" data-id="' + item.id +
									'" class="tr">' +
									'<div class="th bt">' + item.title + '</div>' +
									'<div class="th zz">' + item.author + '</div>' +
									'<div class="th sj">' + setDate(item.createdAt) + '</div>' +
									'<div class="th ll"><span class="stat-item"><i class="like-icon"></i>' + item.giveLike + '</span><span class="stat-item"><i class="view-icon"></i>' + item
									.clickCount + '</span></div></div>'
							})
							$("#listxs").html(html)
							pages = res.data.pages
							if (pages > 1) {
								let numhtml = ""
								// 限制显示的页码数量，最多显示5个页码
								let startPage = Math.max(1, pageindex - 2);
								let endPage = Math.min(pages, startPage + 4);
								
								if (endPage - startPage < 4) {
									startPage = Math.max(1, endPage - 4);
								}
								
								for (let a = startPage; a <= endPage; a++) {
									if (pageindex == a) {
										numhtml += '<label class="actinum" onclick="getnewlist(' + a + ')">' + a +
											'</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
									}
								}
								
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								
								// 添加当前页信息
								let total = res.data.total || 0;
								let startItem = (pageindex - 1) * pagesize + 1;
								let endItem = Math.min(pageindex * pagesize, total);
								$("#pageInfo").html("显示 " + startItem + "-" + endItem + " 条，共 " + total + " 条");
								
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
						}
					}
				})
			}
			
			
			function getlist2() {//最新话题
				let params = "?pageNum=1&pageSize=5&id="+classid+"&sort=dispaly_time&title="
				$.ajax({
					url: baseurl + "/web/xspostByCategoryId"+params,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.list.map((item) => {
								html+='<div onclick="ininfo(this)" data-id="'+item.id+'" class="boxitem"><div class="itemnr">'+item.title+'</div><div class="itemsj">'+setDate2(item.createdAt)+'</div></div>'
							})
							$("#zxht").html(html)
						}
					}
				})
			}
			function getlist3() {//最热话题
				let params = "?pageNum=1&pageSize=5&id="+classid+"&sort=click_count&title="
				$.ajax({
					url: baseurl + "/web/xspostByCategoryId"+params,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.list.map((item) => {
								html+='<div onclick="ininfo(this)" data-id="'+item.id+'" class="boxitem"><div class="itemnr">'+item.title+'</div><div class="itemsj">'+setDate2(item.createdAt)+'</div></div>'
							})
							$("#rmht").html(html)
						}
					}
				})
			}
			
			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是第最后一页了！")
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！")
					}
				} else {
					pageindex = index
					getlist()
				}
			}

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				// 格式化为双行显示：日期在上，时间在下
				return y + '-' + MM + '-' + d + '<br/>' + h + ':' + m
			}
			
			function setDate2(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					if (res.data[i].id == getUrlParam('id') || res.data[i].redirectUrl ==
			// 						'community.html') {
			// 						classdate = res.data[i]
			// 						html += '<a class="menuitem active" href="' + res.data[i].redirectUrl + '?id=' +
			// 							res
			// 							.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					} else {
			// 						html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 							.data[
			// 								i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					}
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
