/**
 * 连接测试脚本
 * 用于检测与目标服务器的连接是否正常
 */

const https = require('https');
const http = require('http');

// 全局计时器列表
const timers = [];

// 清理所有定时器
function clearAllTimers() {
    timers.forEach(timer => clearTimeout(timer));
}

// 测试直接连接
function testDirectConnection() {
    return new Promise((resolve) => {
        // console.log('测试直接连接到 https://szjx.sntcm.edu.cn/api/captcha...');
        
        const options = {
            hostname: 'szjx.sntcm.edu.cn',
            port: 443,
            path: '/api/captcha',
            method: 'GET',
            timeout: 10000,
            rejectUnauthorized: false // 不验证SSL证书
        };
        
        const req = https.request(options, (res) => {
            // console.log(`状态码: ${res.statusCode}`);
            // console.log(`响应头: ${JSON.stringify(res.headers)}`);
            
            // 收集响应数据
            const chunks = [];
            res.on('data', (chunk) => {
                chunks.push(chunk);
            });
            
            res.on('end', () => {
                if (res.statusCode === 200) {
                    // console.log('直接连接测试成功!');
                    // console.log(`响应数据大小: ${Buffer.concat(chunks).length} 字节`);
                } else {
                    // console.log(`直接连接响应异常: 状态码 ${res.statusCode}`);
                }
                resolve();
            });
        });
        
        req.on('error', (e) => {
            console.error(`直接连接失败: ${e.message}`);
            resolve();
        });
        
        req.on('timeout', () => {
            console.error('直接连接超时');
            req.destroy();
            resolve();
        });
        
        req.end();
    });
}

// 测试代理服务器
function testProxyServer() {
    return new Promise((resolve) => {
        // console.log('测试代理服务器 http://**************:5500/api/captcha...');
        
        const options = {
            hostname: '**************',
            port: 5500,
            path: '/api/captcha',
            method: 'GET',
            timeout: 10000
        };
        
        const req = http.request(options, (res) => {
            // console.log(`状态码: ${res.statusCode}`);
            // console.log(`响应头: ${JSON.stringify(res.headers)}`);
            
            // 收集响应数据
            const chunks = [];
            res.on('data', (chunk) => {
                chunks.push(chunk);
            });
            
            res.on('end', () => {
                if (res.statusCode === 200) {
                    // console.log('代理服务器测试成功!');
                    // console.log(`响应数据大小: ${Buffer.concat(chunks).length} 字节`);
                } else {
                    // console.log(`代理服务器响应异常: 状态码 ${res.statusCode}`);
                }
                resolve();
            });
        });
        
        req.on('error', (e) => {
            console.error(`代理服务器连接失败: ${e.message}`);
            resolve();
        });
        
        req.on('timeout', () => {
            console.error('代理服务器连接超时');
            req.destroy();
            resolve();
        });
        
        req.end();
    });
}

// 测试登录请求
function testLoginRequest() {
    return new Promise((resolve) => {
        // console.log('测试登录请求 http://**************:5500/api/auth/login...');
        
        const options = {
            hostname: '**************',
            port: 5500,
            path: '/api/auth/login',
            method: 'POST',
            timeout: 10000,
            headers: {
                'Content-Type': 'application/json'
            }
        };
        
        // 使用测试账号，实际环境请修改为有效凭据
        const data = JSON.stringify({
            identifier: 'test-user',
            credential: 'test-password',
            code: '1234'
        });
        
        const req = http.request(options, (res) => {
            // console.log(`登录请求状态码: ${res.statusCode}`);
            
            // 收集响应数据
            let responseData = '';
            res.on('data', (chunk) => {
                responseData += chunk;
            });
            
            res.on('end', () => {
                // console.log(`登录请求响应: ${responseData}`);
                resolve();
            });
        });
        
        req.on('error', (e) => {
            console.error(`登录请求失败: ${e.message}`);
            resolve();
        });
        
        req.on('timeout', () => {
            console.error('登录请求超时');
            req.destroy();
            resolve();
        });
        
        req.write(data);
        req.end();
    });
}

// 执行测试
async function runTests() {
    // console.log('开始连接测试...');
    // console.log(`当前时间: ${new Date().toISOString()}`);
    // console.log('------------------------');
    
    try {
        await testDirectConnection();
        // console.log('\n------------------------\n');
        
        await testProxyServer();
        // console.log('\n------------------------\n');
        
        await testLoginRequest();
        // console.log('\n------------------------\n');
        
        // console.log('所有测试完成!');
    } catch (error) {
        console.error('测试过程中发生错误:', error);
    } finally {
        clearAllTimers();
    }
}

// 启动测试
runTests(); 