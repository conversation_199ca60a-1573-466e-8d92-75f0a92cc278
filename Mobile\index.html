<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>思政一体化平台 - 移动端</title>

    <!-- CSS文件 -->
    <link rel="stylesheet" href="css/mobile-base.css">
    <link rel="stylesheet" href="css/mobile-index.css">
    <link rel="stylesheet" href="css/mobile-components.css">

    <style>
        /* 顶部导航栏样式优化 */
        .mobile-header {
            background: #c00714 !important;
            box-shadow: 0 2px 8px rgba(192, 7, 20, 0.3);
        }
        
        .mobile-header .logo img {
            filter: brightness(1.2);
        }
        
        .login-btn {
            background: #fff !important;
            color: #c00714 !important;
            border: 2px solid #fff !important;
            padding: 8px 16px !important;
            border-radius: 20px !important;
            font-weight: 600 !important;
            transition: all 0.3s ease !important;
        }
        
        .login-btn:hover, .login-btn:active {
            background: #f8f9fa !important;
            transform: scale(0.95) !important;
        }
        
        .search-btn, .menu-btn {
            color: white !important;
        }
        
        .user-avatar {
            background: rgba(255, 255, 255, 0.2) !important;
            color: white !important;
            padding: 8px 12px !important;
            border-radius: 20px !important;
        }
        
        /* 轮播图样式优化 */
        .banner-section {
            background: white;
            margin-bottom: 16px;
        }
        
        .swiper-slide {
            background: white !important;
            display: flex !important;
            align-items: center !important;
            justify-content: center !important;
            min-height: 200px !important;
            padding: 20px !important;
        }
        
        .swiper-slide img {
            max-width: 100% !important;
            height: auto !important;
            object-fit: contain !important;
        }
        
        /* 快捷功能区域样式优化 */
        .quick-actions {
            background: white;
            padding: 20px 16px;
            margin-bottom: 16px;
        }
        
        .actions-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 12px;
        }
        
        .action-item {
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 16px 8px;
            background: #f8f9fa;
            border-radius: 12px;
            text-decoration: none;
            color: #333;
            transition: all 0.3s ease;
            min-height: 80px;
        }
        
        .action-item:active {
            transform: scale(0.95);
            background: #e9ecef;
        }
        
        .action-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 8px;
            color: white;
        }
        
        .action-icon.courses {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .action-icon.redbooks {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
        }
        
        .action-icon.vr {
            background: linear-gradient(135deg, #43e97b 0%, #38d9a9 100%);
        }
        
        .action-icon.experiment {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .action-icon.museum {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .action-icon.footprint {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        
        .action-icon.party {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
        }
        
        .action-icon.medical {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .action-icon svg {
            width: 20px;
            height: 20px;
        }
        
        .action-item span {
            font-size: 11px;
            font-weight: 500;
            text-align: center;
            line-height: 1.2;
        }
        
        /* 心声社区样式优化 */
        .community-item {
            background: white;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .community-item:active {
            transform: scale(0.98);
            box-shadow: 0 1px 2px rgba(0,0,0,0.1);
        }

        .new-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: #ff4757;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 10px;
            font-weight: 600;
        }

        .community-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }

        .community-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 8px;
            font-size: 12px;
            color: #666;
        }

        .community-stats {
            display: flex;
            gap: 16px;
            font-size: 12px;
            color: #999;
        }

        .community-stats span {
            display: flex;
            align-items: center;
            gap: 4px;
        }

        @media (max-width: 375px) {
            .actions-grid {
                gap: 8px;
            }

            .action-item {
                padding: 12px 6px;
                min-height: 70px;
            }

            .action-icon {
                width: 36px;
                height: 36px;
            }

            .action-icon svg {
                width: 18px;
                height: 18px;
            }

            .action-item span {
                font-size: 10px;
            }

            .community-item {
                padding: 12px;
            }

            .community-title {
                font-size: 14px;
            }
        }
    </style>

    <!-- JavaScript文件 -->
    <script src="../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../PC/js/coco-message.js"></script>
    <script src="mobile-config.js"></script>
    <script src="js/mobile-base.js"></script>
    <script src="js/mobile-touch.js"></script>
    <script src="js/mobile-nav.js"></script>
    <script src="js/index-fix.js"></script>
</head>
<body class="mobile-index">
    <!-- 加载动画 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
    </div>

    <!-- 顶部导航栏 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="menu-btn" id="menuBtn">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="logo">
                <img src="../PC/img/logo.png" alt="思政一体化平台">
            </div>
            <div class="header-actions">
                <button class="search-btn" id="searchBtn">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                </button>
                <div class="user-info" id="userInfo">
                    <button class="login-btn" id="loginBtn">登录</button>
                    <div class="user-avatar" id="userAvatar" style="display: none;">
                        <span id="userName"></span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边菜单 -->
    <nav class="side-menu" id="sideMenu">
        <div class="menu-overlay" id="menuOverlay"></div>
        <div class="menu-content">
            <div class="menu-header">
                <div class="menu-logo">
                    <img src="../PC/img/logo.png" alt="思政一体化平台">
                    <h3>思政一体化平台</h3>
                </div>
                <button class="menu-close" id="menuClose">×</button>
            </div>
            <div class="menu-items" id="menuItems">
                <!-- 菜单项将通过JavaScript动态加载 -->
            </div>
        </div>
    </nav>

    <!-- 搜索面板 -->
    <div class="search-panel" id="searchPanel">
        <div class="search-content">
            <div class="search-input-wrapper">
                <input type="text" placeholder="搜索内容..." id="searchInput">
                <button class="search-submit" id="searchSubmit">搜索</button>
            </div>
            <button class="search-close" id="searchClose">取消</button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 轮播图区域 -->
        <section class="banner-section">
            <div class="swiper-container" id="bannerSwiper">
                <div class="swiper-wrapper" id="bannerWrapper">
                    <!-- 轮播图将通过JavaScript动态加载 -->
                </div>
                <div class="swiper-pagination"></div>
            </div>
        </section>

        <!-- 快捷功能区域 -->
        <section class="quick-actions">
            <div class="container">
                <div class="actions-grid">
                    <a href="pages/learning.html" class="action-item">
                        <div class="action-icon courses">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19ZM17 12H7V10H17V12ZM17 16H7V14H17V16ZM17 8H7V6H17V8Z"/>
                            </svg>
                        </div>
                        <span>课程学习</span>
                    </a>
                    <a href="pages/redbooks.html" class="action-item">
                        <div class="action-icon redbooks">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M18 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V4C20 2.9 19.1 2 18 2ZM18 20H6V4H7V13L9.5 11.5L12 13V4H18V20Z"/>
                            </svg>
                        </div>
                        <span>红色书籍</span>
                    </a>
                    <a href="pages/vrtour.html" class="action-item">
                        <div class="action-icon vr">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M7 9C7 8.4 7.4 8 8 8H16C16.6 8 17 8.4 17 9V15C17 15.6 16.6 16 16 16H8C7.4 16 7 15.6 7 15V9Z"/>
                                <circle cx="10" cy="12" r="1.5"/>
                                <circle cx="14" cy="12" r="1.5"/>
                            </svg>
                        </div>
                        <span>VR红色游学</span>
                    </a>
                    <a href="pages/footprint.html" class="action-item">
                        <div class="action-icon footprint">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                            </svg>
                        </div>
                        <span>总书记的足迹</span>
                    </a>
                    <a href="pages/experiment.html" class="action-item">
                        <div class="action-icon experiment">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M9 2V7H7.5C6.67 7 6 7.67 6 8.5V10H4.5C3.67 10 3 10.67 3 11.5V20.5C3 21.33 3.67 22 4.5 22H19.5C20.33 22 21 21.33 21 20.5V11.5C21 10.67 20.33 10 19.5 10H18V8.5C18 7.67 17.33 7 16.5 7H15V2H9ZM11 4H13V7H11V4ZM8 9H16V10H8V9ZM5 12H19V20H5V12ZM7 14V18H9V14H7ZM11 14V18H13V14H11ZM15 14V18H17V14H15Z"/>
                            </svg>
                        </div>
                        <span>虚仿实验空间</span>
                    </a>
                    <a href="pages/party-study.html" class="action-item">
                        <div class="action-icon party">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                            </svg>
                        </div>
                        <span>党建学习</span>
                    </a>
                    <a href="pages/museum.html" class="action-item">
                        <div class="action-icon museum">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 3L2 8V9H22V8L12 3ZM4 11V19H6V11H4ZM8 11V19H10V11H8ZM12 11V19H14V11H12ZM16 11V19H18V11H16ZM20 11V19H22V11H20ZM2 20V22H22V20H2Z"/>
                            </svg>
                        </div>
                        <span>医德博物馆</span>
                    </a>
                    <a href="pages/community.html" class="action-item">
                        <div class="action-icon medical">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
                            </svg>
                        </div>
                        <span>心声社区</span>
                    </a>
                </div>
            </div>
        </section>

        <!-- 心声社区区域 -->
        <section class="community-section">
            <div class="container">
                <div class="section-header">
                    <h2>
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
                        </svg>
                        心声社区
                    </h2>
                    <a href="pages/community.html" class="more-link">更多</a>
                </div>
                <div class="community-list" id="communityList">
                    <!-- 社区内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </section>

        <!-- 在线学习区域 -->
        <section class="learning-section">
            <div class="container">
                <div class="section-header">
                    <h2>
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3ZM5 13.18V17.18L12 21L19 17.18V13.18L12 17L5 13.18Z"/>
                        </svg>
                        在线学习
                    </h2>
                    <a href="pages/learning-modules.html" class="more-link">更多</a>
                </div>
                <div class="learning-tabs">
                    <button class="tab-btn active" data-tab="redbooks">红色书籍</button>
                    <button class="tab-btn" data-tab="courses">课程学习</button>
                </div>
                <div class="learning-content">
                    <div class="tab-content active" id="redbooksContent">
                        <!-- 红色书籍内容 -->
                    </div>
                    <div class="tab-content" id="coursesContent">
                        <!-- 课程学习内容 -->
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="index.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="pages/learning-modules.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="pages/community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="pages/profile.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>我的</span>
        </a>
    </nav>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83L16.59 15.41L18 14L12 8L6 14Z"/>
        </svg>
    </button>
</body>
</html>
