<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>登录指南 - 思政一体化平台</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .header p {
            margin: 10px 0 0;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px 20px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section h2 {
            color: #c00714;
            font-size: 18px;
            margin-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 8px;
        }
        
        .problem-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .problem-title {
            font-weight: 600;
            color: #856404;
            margin-bottom: 8px;
        }
        
        .solution-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
        }
        
        .solution-title {
            font-weight: 600;
            color: #0c5460;
            margin-bottom: 8px;
        }
        
        .step {
            background: #f8f9fa;
            border-left: 4px solid #c00714;
            padding: 15px;
            margin-bottom: 15px;
        }
        
        .step-number {
            display: inline-block;
            width: 24px;
            height: 24px;
            background: #c00714;
            color: white;
            border-radius: 50%;
            text-align: center;
            line-height: 24px;
            font-size: 12px;
            font-weight: bold;
            margin-right: 10px;
        }
        
        .btn-group {
            display: flex;
            gap: 10px;
            margin-top: 20px;
            flex-wrap: wrap;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 20px;
            background: #c00714;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 14px;
            text-align: center;
            transition: background 0.3s ease;
            flex: 1;
            min-width: 120px;
        }
        
        .btn:hover {
            background: #a00610;
        }
        
        .btn.secondary {
            background: #6c757d;
        }
        
        .btn.secondary:hover {
            background: #5a6268;
        }
        
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 8px 12px;
            font-family: monospace;
            font-size: 12px;
            color: #495057;
            word-break: break-all;
        }
        
        .highlight {
            background: #fff3cd;
            padding: 2px 4px;
            border-radius: 3px;
            font-weight: 600;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>移动端登录指南</h1>
            <p>解决统一认证登录问题的完整方案</p>
        </div>
        
        <div class="content">
            <!-- 问题说明 -->
            <div class="section">
                <h2>🚨 当前问题</h2>
                <div class="problem-box">
                    <div class="problem-title">移动端应用未注册</div>
                    <p>移动端的service URL在统一认证系统中未注册，导致登录时出现以下错误：</p>
                    <div class="code">Cannot read properties of null (reading 'children')</div>
                    <p>这个错误出现在CAS登录页面，表明移动端URL不被认可。</p>
                </div>
            </div>
            
            <!-- 解决方案 -->
            <div class="section">
                <h2>✅ 解决方案</h2>
                <div class="solution-box">
                    <div class="solution-title">使用PC端登录方式</div>
                    <p>通过模拟PC端的登录流程，使用已注册的PC端service URL进行认证，然后将登录信息同步到移动端。</p>
                </div>
            </div>
            
            <!-- 操作步骤 -->
            <div class="section">
                <h2>📋 操作步骤</h2>
                
                <div class="step">
                    <span class="step-number">1</span>
                    <strong>点击登录按钮</strong><br>
                    在移动端首页点击"登录"按钮，会跳转到登录选择页面。
                </div>
                
                <div class="step">
                    <span class="step-number">2</span>
                    <strong>选择登录方式</strong><br>
                    选择"<span class="highlight">使用PC端登录方式</span>"，系统会使用PC端的service URL进行认证。
                </div>
                
                <div class="step">
                    <span class="step-number">3</span>
                    <strong>统一认证登录</strong><br>
                    在统一认证页面输入用户名和密码完成登录。
                </div>
                
                <div class="step">
                    <span class="step-number">4</span>
                    <strong>自动跳转</strong><br>
                    登录成功后会自动跳转回移动端首页，登录状态已同步。
                </div>
            </div>
            
            <!-- 技术原理 -->
            <div class="section">
                <h2>🔧 技术原理</h2>
                <p><strong>Service URL替换：</strong></p>
                <div class="code">
                    移动端URL: http://127.0.0.1:5501/Mobile/userinfo-simple.html<br>
                    替换为PC端URL: https://szjx.sntcm.edu.cn/userinfo.html
                </div>
                
                <p><strong>登录流程：</strong></p>
                <ol>
                    <li>使用PC端已注册的service URL进行CAS认证</li>
                    <li>获取登录ticket和用户信息</li>
                    <li>将认证信息保存到移动端sessionStorage</li>
                    <li>跳转回移动端首页完成登录</li>
                </ol>
            </div>
            
            <!-- 备选方案 -->
            <div class="section">
                <h2>🔄 备选方案</h2>
                <p>如果PC端模拟登录仍有问题，可以尝试以下方案：</p>
                
                <div class="step">
                    <span class="step-number">A</span>
                    <strong>直接跳转PC端</strong><br>
                    点击"跳转到PC端登录"，在PC端完成登录后手动返回移动端。
                </div>
                
                <div class="step">
                    <span class="step-number">B</span>
                    <strong>测试登录功能</strong><br>
                    使用"测试登录功能"检查接口状态和调试信息。
                </div>
            </div>
            
            <!-- 操作按钮 -->
            <div class="section">
                <h2>🚀 快速操作</h2>
                <div class="btn-group">
                    <a href="login-pc-simulate.html" class="btn">开始登录</a>
                    <a href="login-test.html" class="btn secondary">测试功能</a>
                    <a href="index.html" class="btn secondary">返回首页</a>
                </div>
            </div>
            
            <!-- 常见问题 -->
            <div class="section">
                <h2>❓ 常见问题</h2>
                
                <p><strong>Q: 为什么不直接注册移动端URL？</strong></p>
                <p>A: 需要系统管理员在CAS服务器中注册新的service URL，这需要一定的时间和权限。</p>
                
                <p><strong>Q: PC端模拟登录安全吗？</strong></p>
                <p>A: 是的，我们只是使用PC端的service URL进行认证，认证过程完全相同，安全性不受影响。</p>
                
                <p><strong>Q: 登录后会跳转到PC端吗？</strong></p>
                <p>A: 不会，登录完成后会自动跳转回移动端首页，保持移动端的使用体验。</p>
            </div>
        </div>
    </div>
</body>
</html>
