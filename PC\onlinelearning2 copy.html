<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-在线学习-红色书籍</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/onlinelearning.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="contenttitlebox">
			<div>
				<a id="zxxx" href="onlinelearning.html">在线学习</a>
				<img src="img/jtright.png" />
				<label class="aa" id="hsname">教学资源</label>
			</div>
		</div>
		<div class="content">
			<div class="contenttopview">
				<div class="contentitem" id="classbox">
					<!-- <a class="titleactive">红色书籍</a>
					<a href="onlinelearning3.html">教学资源</a> -->
				</div>
				<!-- <div class="contentitem sss">
					<div class="ssdiv">
						<div class="select" onclick="showselect()">
							<label id="selecttxt">书籍</label>
							<img src="img/ssx.png" />
							<img src="img/ssb.png" />
						</div>
						<input />
						<div class="ss">搜索</div>
					</div>
					<div class="dztsg">电子图书馆</div>
					<div class="opbox" id="select">
						<div>书籍</div>
						<div>红色游学</div>
						<div>书籍</div>
						<div>红色游学</div>
					</div>
				</div> -->
			</div>
			<div class="tjbox" style="border: none;">
				<label>分类:</label>
				<span onclick="selects(this)" id="qbsj" class="tjactive" data-id="0">全部</span>
				<div id="flbox">
					
				</div>
			</div>
			<div id="htmlbox" class="sjbox">

			</div>
			<div class="fybox" id="fyq">
				<span id="sy">首页</span>
				<span id="syy">上一页</span>
				<div class="num" id="num">
				</div>
				<span id="xyy">下一页</span>
				<span id="wy">尾页</span>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/clipboard.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				let userinfo = sessionStorage.getItem("userinfo")
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				getclass('onlinelearning.html')
				getfooterlink()
				getclassid()
				getallfllist()
				getclassch()
			})
			let classid = null
			let selectid = 0
			
			let pageindex = 1
			let pageSize = 20
			let pages = 1
			function ininfo(item){
				window.location.href = "onlinelearning5.html?id="+$(item).attr("data-id")
			}
			function getallfllist(){
				$.ajax({
					url: baseurl + "/web/redbook/list ",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.list.map((item)=>{
								if(item.id!==selectid){
									html+='<span onclick="selects(this)" data-id="'+item.id+'">'+item.name+'</span>'
								}else{
									html+='<span class="tjactive">'+item.name+'</span>'
								}
							})
							$("#flbox").html(html)
						}
					}
				})
			}
			function selects(item){
				pageindex = 1
				let allfl = $("#flbox span")
				if($(item).attr("data-id")!=0){
					for(let i =0;i<allfl.length;i++){
						if($(allfl[i]).attr("data-id") == $(item).attr("data-id")){
							$(allfl[i]).attr("class","tjactive")
						}else{
							$(allfl[i]).attr("class","")
						}
					}
					$("#qbsj").attr("class","")
				}else{
					for(let i =0;i<allfl.length;i++){
						$(allfl[i]).attr("class","")
					}
					$("#qbsj").attr("class","tjactive")
				}
				
				selectid = $(item).attr("data-id")
				getsjlist()
			}
			function getclassid() {
				$.ajax({
					url: baseurl + "/web/category/book",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							classid = res.data[0].id
							getsjlist()
						}
					}
				})
			}
			let clipboard = new ClipboardJS('.copybtn');
			clipboard.on('success', function(e) {
				e.clearSelection();
				cocoMessage.success(1000, "复制成功！")
			});
			
			clipboard.on('error', function(e) {
				cocoMessage.error(1000, "复制失败！")
			});
			function getsjlist() {
				let ccid = null
				if(selectid!=0){
					ccid = selectid
				}
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						categoryId: classid,
						pageSize: pageSize,
						redBookId: ccid,
						pageNum: pageindex
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let newhtml = ""
							for (let k = 0; k < res.data.list.length; k++) {
								newhtml += '<div class="txtitem">' +
									'<div class="topitem" onclick="ininfo(this)" data-id="'+res.data.list[k].id+'">'
								if (res.data.list[k].thumbPath != null) {
									newhtml += '<img src="' + baseurl + res.data.list[k].thumbPath[0] + '"/>'
								}
								newhtml += '<div class="icobox">' +
									'<label><img src="img/gkl.png"/>' + res.data.list[k].clickCount +
									'</label></div></div>' +
									'<div class="bottomitem">' +
									'<div class="title">' + res.data.list[k].title + '</div>' +
									'<div class="zz">作者: ' + res.data.list[k].author + '</div><div class="zz">' + res
									.data.list[k].postName + '</div></div></div>'
							}
							$("#htmlbox").html(newhtml)
							pages = res.data.pages
							if (pages > 1) {
								let numhtml = ""
								//当页数大于5
								if (pages > 5) {
									if (pageindex <= 5) { //当前页码小于5的时候右侧不变
										for (let a = 1; a <= 10; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
										numhtml += '<label onclick="getnewlist(11)">...</label>'
									} else if (5 < pageindex & pageindex < pages - 5) {
										numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) - 5) +
											')">...</label>'
										for (let a = pageindex - 4; a <= pageindex + 4; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
										numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) + 5) +
											')">...</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + (pages - 11) +
											')">...</label>'
										for (let a = pages - 10; a <= pages; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
									}
								} else {
									for (let a = 1; a <= pages; a++) {
										if (pageindex == a) {
											numhtml += '<label class="actinum" onclick="getnewlist(' + a +
												')">' + a +
												'</label>'
										} else {
											numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
										}
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
						}
					}
				})
			}
			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是第最后一页了！")
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！")
					}
				} else {
					pageindex = index
					getsjlist()
				}
			}
			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}


			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					// console.log(res.data[i])
			// 					if (res.data[i].id == getUrlParam('id') || res.data[i].redirectUrl ==
			// 						'onlinelearning.html') {
			// 						classdate = res.data[i]
			// 						$("#zxxx").attr('href', "onlinelearning.html?id=" + res.data[i].id)
			// 						html += '<a class="menuitem active" href="' + res.data[i].redirectUrl + '?id=' +
			// 							res
			// 							.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					} else {
			// 						html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 							.data[
			// 								i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					}
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 			getlist()
			// 		}
			// 	})
			// }
			
			function getclassch(){
				$.ajax({
					url: baseurl + "/web/category/teacher",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							classdate = res.data[0]
							getlist(res.data[0])
						}
					}
				})
			}

			function getlist() {
				let classhtml = ""
				for (let i = 0; i < classdate.children.length; i++) {
					if (classdate.children[i].name == '红色书籍') {
						classhtml += '<a class="titleactive" href="' + classdate.children[i].redirectUrl + '?id=' + classdate
							.children[i].id + '">' + classdate.children[i].name + '</a>'
						$("#hsname").html(classdate.children[i].name)
					} else {
						classhtml += '<a href="' + classdate.children[i].redirectUrl + '?id=' + classdate.children[i].id + '">' +
							classdate.children[i].name + '</a>'
					}
				}
				$("#classbox").html(classhtml)
			}

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			$("#select div").on('click', function() {
				$("#selecttxt").html($(this).html())
				$("#select").hide()
			})

			function showselect() {
				$("#select").show()
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
