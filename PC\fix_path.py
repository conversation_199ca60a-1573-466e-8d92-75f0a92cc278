import os
import shutil
import glob

# 获取当前工作目录
current_dir = os.getcwd()
print(f"当前工作目录: {current_dir}")

# 确保目标目录存在
output_dir = os.path.join(current_dir, "output")
os.makedirs(output_dir, exist_ok=True)

# 寻找所有嵌套的Excel文件
nested_dir = os.path.join(current_dir, "python_data", "output")
if os.path.exists(nested_dir):
    # 获取所有Excel文件
    excel_files = glob.glob(os.path.join(nested_dir, "*.xlsx"))
    print(f"在嵌套目录中找到 {len(excel_files)} 个Excel文件")
    
    # 复制文件到正确的位置
    for file_path in excel_files:
        file_name = os.path.basename(file_path)
        dest_path = os.path.join(output_dir, file_name)
        shutil.copy2(file_path, dest_path)
        print(f"已复制: {file_name}")
    
    print("所有文件已复制到正确的位置")
else:
    print("未找到嵌套的输出目录")

# 列出当前输出目录中的所有文件
print("\n输出目录中的文件:")
if os.path.exists(output_dir):
    files = os.listdir(output_dir)
    for file in files:
        print(f"- {file}")
else:
    print("输出目录不存在") 