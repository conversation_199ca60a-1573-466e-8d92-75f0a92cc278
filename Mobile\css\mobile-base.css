/* 移动端基础样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html {
    font-size: 16px;
    -webkit-text-size-adjust: 100%;
    -webkit-tap-highlight-color: transparent;
}

body {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
    line-height: 1.6;
    color: #333;
    background-color: #f5f5f5;
    overflow-x: hidden;
    padding-bottom: 60px; /* 为底部导航留出空间 */
}

/* 容器 */
.container {
    max-width: 100%;
    margin: 0 auto;
    padding: 0 16px;
}

/* 加载动画 */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.95);
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    transition: opacity 0.3s ease;
}

.loading-spinner {
    width: 40px;
    height: 40px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #c00714;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 16px;
}

.loading-overlay p {
    color: #666;
    font-size: 14px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 顶部导航栏 */
.mobile-header {
    position: fixed;
    top: 0;
    left: 0;
    right: 0;
    height: 56px;
    background: #fff;
    border-bottom: 1px solid #e5e5e5;
    z-index: 1000;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.header-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    height: 100%;
    padding: 0 16px;
}

.menu-btn {
    background: none;
    border: none;
    width: 24px;
    height: 24px;
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    cursor: pointer;
    padding: 0;
}

.menu-btn span {
    width: 100%;
    height: 2px;
    background: #333;
    border-radius: 1px;
    transition: all 0.3s ease;
}

.menu-btn.active span:nth-child(1) {
    transform: rotate(45deg) translate(5px, 5px);
}

.menu-btn.active span:nth-child(2) {
    opacity: 0;
}

.menu-btn.active span:nth-child(3) {
    transform: rotate(-45deg) translate(7px, -6px);
}

.logo {
    flex: 1;
    text-align: center;
}

.logo img {
    height: 32px;
    width: auto;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

.search-btn {
    background: none;
    border: none;
    width: 24px;
    height: 24px;
    color: #666;
    cursor: pointer;
}

.search-btn svg {
    width: 100%;
    height: 100%;
}

.login-btn {
    background: #c00714;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 16px;
    font-size: 12px;
    cursor: pointer;
    transition: background 0.3s ease;
}

.login-btn:hover {
    background: #a00610;
}

.user-avatar {
    display: flex;
    align-items: center;
    gap: 8px;
    color: #333;
    font-size: 14px;
}

/* 主要内容区域 */
.main-content {
    margin-top: 56px;
    min-height: calc(100vh - 116px);
}

/* 侧边菜单 */
.side-menu {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: 2000;
    visibility: hidden;
    opacity: 0;
    transition: all 0.3s ease;
}

.side-menu.active {
    visibility: visible;
    opacity: 1;
}

.menu-overlay {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
}

.menu-content {
    position: absolute;
    top: 0;
    left: 0;
    width: 280px;
    height: 100%;
    background: #fff;
    transform: translateX(-100%);
    transition: transform 0.3s ease;
    overflow-y: auto;
}

.side-menu.active .menu-content {
    transform: translateX(0);
}

.menu-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 20px 16px;
    border-bottom: 1px solid #e5e5e5;
    background: #c00714;
    color: white;
}

.menu-logo {
    display: flex;
    align-items: center;
    gap: 12px;
}

.menu-logo img {
    width: 32px;
    height: 32px;
}

.menu-logo h3 {
    font-size: 16px;
    font-weight: 600;
}

.menu-close {
    background: none;
    border: none;
    color: white;
    font-size: 24px;
    cursor: pointer;
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.menu-items {
    padding: 16px 0;
}

.menu-item {
    display: block;
    padding: 12px 16px;
    color: #333;
    text-decoration: none;
    border-bottom: 1px solid #f0f0f0;
    transition: background 0.3s ease;
}

.menu-item:hover,
.menu-item.active {
    background: #f8f9fa;
    color: #c00714;
}

/* 搜索面板 */
.search-panel {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: #fff;
    z-index: 1500;
    transform: translateY(-100%);
    transition: transform 0.3s ease;
}

.search-panel.active {
    transform: translateY(0);
}

.search-content {
    padding: 20px 16px;
    display: flex;
    align-items: center;
    gap: 12px;
}

.search-input-wrapper {
    flex: 1;
    display: flex;
    align-items: center;
    background: #f5f5f5;
    border-radius: 8px;
    padding: 0 12px;
}

.search-input-wrapper input {
    flex: 1;
    border: none;
    background: none;
    padding: 12px 0;
    font-size: 16px;
    outline: none;
}

.search-submit {
    background: #c00714;
    color: white;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    cursor: pointer;
}

.search-close {
    background: none;
    border: none;
    color: #666;
    font-size: 16px;
    cursor: pointer;
    padding: 8px;
}

/* 底部导航 */
.bottom-nav {
    position: fixed;
    bottom: 0;
    left: 0;
    right: 0;
    height: 60px;
    background: #fff;
    border-top: 1px solid #e5e5e5;
    display: flex;
    z-index: 1000;
}

.nav-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    color: #999;
    transition: color 0.3s ease;
    padding: 8px 4px;
}

.nav-item.active {
    color: #c00714;
}

.nav-item svg {
    width: 20px;
    height: 20px;
    margin-bottom: 4px;
}

.nav-item span {
    font-size: 10px;
    line-height: 1;
}

/* 返回顶部按钮 */
.back-to-top {
    position: fixed;
    bottom: 80px;
    right: 16px;
    width: 44px;
    height: 44px;
    background: #c00714;
    color: white;
    border: none;
    border-radius: 50%;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
    cursor: pointer;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 999;
}

.back-to-top.show {
    opacity: 1;
    visibility: visible;
}

.back-to-top svg {
    width: 20px;
    height: 20px;
}

/* 响应式设计 */
@media (max-width: 375px) {
    .container {
        padding: 0 12px;
    }
    
    .header-content {
        padding: 0 12px;
    }
    
    .menu-content {
        width: 260px;
    }
}

/* 触摸优化 */
@media (hover: none) and (pointer: coarse) {
    .menu-item,
    .nav-item,
    .search-btn,
    .login-btn {
        min-height: 44px;
        display: flex;
        align-items: center;
        justify-content: center;
    }
}

/* 安全区域适配 */
@supports (padding: max(0px)) {
    .mobile-header {
        padding-left: max(16px, env(safe-area-inset-left));
        padding-right: max(16px, env(safe-area-inset-right));
    }
    
    .bottom-nav {
        padding-bottom: max(0px, env(safe-area-inset-bottom));
        padding-left: max(0px, env(safe-area-inset-left));
        padding-right: max(0px, env(safe-area-inset-right));
    }
}
