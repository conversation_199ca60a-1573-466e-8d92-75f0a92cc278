body {
	background: #fbfbfb !important;
}

.newsbox {
	width: 66.666666rem;
	display: flex;
	margin: 0px auto;
	min-height: 30rem;
}
.bbbb {
	height: 0.78125rem;
}
.newboxleft {
	width: 46.145833rem;
	background: #FFFFFF;
}

.newsright {
	width: calc(100% - 46.145833rem - 0.78125rem);
	margin-left: 0.78125rem;
}

.border {
	background: linear-gradient(to right, #c00714, #c00714, #ffc156);
	height: 2px;
}

.content {
	padding-top: 3.125rem;
	padding-bottom: 3.125rem;
	min-height: 28rem;
}
.content2{
	padding-bottom: 3.125rem;
}

.newtitles1 {
	height: 2.864583rem;
	display: flex;
	align-items: center;
	font-size: 0.9375rem;
	color: #c00714;
	font-weight: bold;
	border-bottom: 0.052083rem solid #e1e1e1;
	padding-left: 0.78125rem;
	background: #FFFFFF;
}

.boxxx {
	background: #FFFFFF;
}

.boxitem {
	display: flex;
	align-items: center;
	height: 2.239583rem;
	border-bottom: 0.052083rem dashed #e1e1e1;
	margin-left: 0.78125rem;
	margin-right: 0.260416rem;
	cursor: pointer;
}

.boxitem:last-child {
	border: none;
}

.itemnr {
	font-size: 0.833333rem;
	color: #333333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	width: calc(100% - 5.208333rem - 0.78125rem);
	box-sizing: border-box;
	padding-left: 0.78125rem;
	position: relative;
}

.itemnr::after {
	position: absolute;
	content: "";
	width: 0.15625rem;
	height: 0.15625rem;
	background: #cecece;
	left: 0.260416rem;
	top: 0;
	bottom: 0;
	margin: auto;
	border-radius: 100rem;
}

.itemsj {
	font-size: 0.729166rem;
	color: #cecece;
	width: 5.208333rem;
	padding-left: 0.78125rem;
}

.sqtop {
	background: linear-gradient(to right, #c00714, #c00714, #ffc156);
	height: 2.8125rem;
	border-bottom: 1px solid #c00714;
	padding: 0px 0.78125rem;
	display: flex;
}

.sqtopl {
	width: calc(100% - 8.333333rem);
	height: 2.8125rem;
	display: flex;
	align-items: center;
}

.sqtopr {
	width: 8.333333rem;
	height: 2.8125rem;
	display: flex;
	align-items: center;
}

.sqss {
	width: 8.333333rem;
	height: 1.354166rem;
	border: 1px solid #c00714;
	border-radius: 0.260416rem;
	display: flex;
	align-items: center;
	overflow: hidden;
}

.sqss input {
	width: calc(100% - 2.083333rem);
	border: none;
	outline-style: none;
	height: 1.354166rem;
	font-size: 0.729166rem;
	padding: 0px 0.260416rem;
}

.sqss label {
	width: 2.083333rem;
	color: #FFFFFF;
	background: #c00714;
	height: 1.354166rem;
	font-size: 0.729166rem;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.sqtopl label {
	font-size: 0.729166rem;
	color: #e56c74;
	padding-right: 0.78125rem;
}

.sqtopl select {
	background: #FFFFFF;
	border-radius: 0.260416rem;
	border: 1px solid #c1c1c1;
	min-width: 7.291666rem;
	outline-style: none;
	margin-right: 0.78125rem;
	font-size: 0.729166rem;
	color: #666666;
}

.sqtopl span {
	color: #f3f3f3;
	font-size: 0.729166rem;
	padding: 0px 0.520833rem;
	cursor: pointer;
}

.activespan {
	color: #c00714 !important;
	background: #FFFFFF;
	border-radius: 0.260416rem;
	padding: 0.104166rem 0.520833rem !important;
}

.table .tr {
	padding: 0px 0.520833rem;
	display: flex;
	align-items: center;
	height: 3.5rem;
	border-bottom: 0.052083rem dashed #e1e1e1;
	cursor: pointer;
}

.tr:last-child {
	border: none;
}

.table .tr:first-child {
	background: #f2f2f2;
	height: 1.822916rem;
	color: #c1c1c1;
	font-size: 0.729166rem;
}
.table .tr .th:nth-child(1) {
	width: calc(100% - 6.09375rem - (4.322916rem * 4));
	padding-right: 0.78125rem;
	    box-sizing: border-box;
}
.table .tr .th:nth-child(2) {
	width: 6.09375rem;
}

.table .tr .th:nth-child(3) {
	width: 6.09375rem;
}

.table .tr .th:nth-child(4) {
	width: 4.322916rem;
}

.table .tr .th:nth-child(5) {
	width: 4.322916rem;
}

.table .tr .th:nth-child(6) {
	width: 4.322916rem;
	text-align: center;
}

.fl {
	font-size: 0.729166rem;
	color: #c1c1c1;
}

.bt {
	font-size: 0.9375rem;
	color: #666666;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.zz {
	font-size: 0.729166rem;
	color: #c1c1c1;
}

.sj {
	font-size: 0.729166rem;
	color: #c1c1c1;
}

.ll {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}

.ll div {
	width: 100%;
}

.ll div:first-child {
	font-size: 0.729166rem;
	color: #333333;
}

.ll div:last-child {
	font-size: 0.729166rem;
	color: #c1c1c1;
}

.fybox {
	padding-bottom: 3.645833rem;
}

.toptop {
	width: 66.666666rem;
	margin: 0px auto;
	display: flex;
	height: 3.385416rem;
	align-items: center;
}

.toptop img {
	width: 0.78125rem;
	display: block;
	padding-left: 0.260416rem;
	padding-right: 0.260416rem;
}

.toptop label {
	font-size: 0.833333rem;
	color: #cecece;
}
.accc{
	color: #333333 !important;
}
.sqtopsss{
	height: 2.864583rem;
	display: flex;
	align-items: center;
	border-bottom: 0.052083rem dashed #e1e1e1;
	padding: 0px 0.78125rem;
}
.sqtopsss div{
	width: 50%;
	display: flex;
	align-items: center;
}
.sqtopsss div label{
	display: flex;
	align-items: center;
	color: #cecece;
	font-size: 0.729166rem;
	margin-right: 0.78125rem;
}
.sqtopsss div label img{
	display: block;
	width: 0.9375rem;
	padding-right: 0.260416rem;
}
.sqtopsss div:last-child{
	justify-content: flex-end;
}
.sqtopsss div:last-child label{
	margin-left: 0.78125rem;
	margin-left: 0px;
}
.sstitle{
	font-size: 1.25rem;
	color: #333333;
	font-weight: bold;
	text-align: center;
	padding-top: 1.875rem;
	padding-bottom: 1.875rem;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	padding-left: 1.041666rem;
	padding-right: 1.041666rem;
}
.nr{
	padding: 0.78125rem;
}
.nr p{
	text-indent: 2em;
	font-size: 0.9375rem;
	color: #666666;
	line-height: 1.5625rem;
	padding-bottom: 0.520833rem;
}
.nr img{
	width: 100%;
	display: block;
	padding-top: 1.041666rem;
	padding-bottom: 1.041666rem;
}
.dzbox{
	display: flex;
	align-items: center;
	justify-content: center;
	padding-top: 2.083333rem;
	padding-bottom: 2.083333rem;
}
.mz div:first-child{
	width: 7.291666rem;
	height: 2.916666rem;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}
.mz1 div:first-child{
	background: #f3f3f3;
	border-radius: 0.520833rem;
}
.mz2 div:first-child{
	background: #c00714;
	border-radius: 0.520833rem;
}
.mz div:last-child{
	font-size: 0.729166rem;
	color: #333333;
	text-align: center;
	padding-top: 0.260416rem;
}
#dz2{
	display: none;
}