.museumbox {
	width: 66.666666rem;
	margin: 0px auto;
	position: relative;
	z-index: 2;
}

.museumboxitemtop {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.museumboxitemtopl {
	position: relative;
}

.museumboxitemtopr {
	display: flex;
}

.museumboxitemtopl label {
	position: absolute;
	bottom: 0;
	left: 3.125rem;
	height: 2.083333rem;
	line-height: 2.34375rem;
	font-size: 1.041666rem;
	color: #FFFFFF;
	font-weight: bold;
}

.museumboxitemtopl img {
	display: block;
	width: 13.177083rem;
}

.inqj,
.ljxq {
	width: 6.25rem;
	height: 1.770833rem;
	border-radius: 5px;
	text-align: center;
	line-height: 1.770833rem;
	color: #FFFFFF;
	font-size: 0.833333rem;
	margin-bottom: -1.041666rem;
	cursor: pointer;
	display: block;
}

.inqj {
	background: #f39633;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-right: 0.78125rem;
}
.inqj2{
	background: #c00714;
	display: flex;
	align-items: center;
	justify-content: center;
	width: 6.25rem;
	height: 1.770833rem;
	border-radius: 5px;
	text-align: center;
	line-height: 1.770833rem;
	color: #FFFFFF;
	font-size: 0.833333rem;
	cursor: pointer;
}

.inqj img {
	padding-right: 0.260416rem;
	width: 1.302083rem;
	display: block;
}

.ljxq {
	background: #c00714;
}

.borderbag {
	background: linear-gradient(to right, #c00714, #c00714, #ffc156);
	height: 2px;
}

.museumbox {
	padding-top: 1.5625rem;
}

.museumboxitem {
	margin-bottom: 3.125rem;
}

.museumboxitem:last-child {
	margin: 0px;
}

.museumboxitembottom {
	width: 100%;
	display: flex;
	flex-wrap: nowrap;
	margin-top: 0.78125rem;
}

.museumboxitembottoml .swiper {
	overflow: hidden;
	width: 100%;
}

.museumboxitembottoml .swiper img {
	width: 100%;
	display: block;
}

.museumboxitembottoml {
	width: 24.947916rem;
	position: relative;
}

.museumboxitembottoml .swiper-pagination {
	position: absolute;
	right: 0.520833rem;
	bottom: 0.520833rem;
	background: #7b1b1e;
	width: auto;
	border-radius: 100px;
	height: 15px;
	display: flex;
	align-items: center;
	padding-left: 0.260416rem;
	padding-right: 0.260416rem;
}
.museumboxitembottoml .swiper-pagination-bullet-active{
	background: #FFFFFF !important;
}
.museumboxitembottoml .swiper-pagination-bullet{
	background: #FFFFFF;
}
.museumboxitembottoml .swiper-pagination span{
	margin-left: 0.208333rem;
	margin-right: 0.208333rem;
}
.museumboxitembottomr {
	width: calc(100% - 24.947916rem);
	padding-left: 0.78125rem;
	font-size: 0.833333rem;
	color: #333333;
	line-height: 1.302083rem;
}

.bodyview {
	width: 100%;
	position: relative;
	padding-bottom: 3.125rem;
	min-height: 32rem;
}

.topright {
	position: absolute;
	top: 0;
	right: 0;
	z-index: 1;
}

.leftbottom {
	position: absolute;
	bottom: 0;
	left: 0;
	z-index: 1;
}
.contenttitlebox{
	height: 3.125rem;
	background: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
}
.contenttitlebox div{
	width: 66.666666rem;
	display: flex;
	align-items: center;
}
.contenttitlebox div img{
	width: 0.78125rem;
	display: block;
	padding-left: 0.260416rem;
	padding-right: 0.260416rem;
}
.contenttitlebox div{
	font-size: 0.833333rem;
	color: #cecece;
}
.bodyview2{
	width: 100%;
	background: #fbfbfb;
	padding-bottom: 3.125rem;
}
.aa{
	color: #333333;
}
.contentbox{
		width: 66.666666rem;
		margin: 0px auto;
		background: #FFFFFF;
		margin-top: 0.78125rem;
		min-height: 28rem;
}
.boxtop{
	height: 2.604166rem;
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1px dashed #dedede;
}
.boxtop div{
	display: flex;
	align-items: center;
	font-size: 0.729166rem;
	color: #e1e1e1;
	padding-left: 0.78125rem;
	padding-right: 0.78125rem;
}
.boxtop div img{
	padding-right: 0.260416rem;
}
.xqnrtitle{
	font-size: 1.354166rem;
	font-weight: bold;
	color: #333333;
	line-height: 5.208333rem;
	text-align: center;
	overflow: hidden;
	            text-overflow:ellipsis;
	            white-space: nowrap;
}
.bwgnr{
	padding-left: 5.208333rem;
	padding-right: 5.208333rem;
	font-size: 0.833333rem;
	color: #666666;
	line-height: 1.5625rem;
	text-indent: 2em;
	min-height: 12.5rem;
}
.bwgnr img{
	width: 100%;
	display: block;
	margin: 0px auto;
	padding-top: 1.5625rem;
	padding-bottom: 1.5625rem;
}
.nrbtn{
	display: flex;
	align-items: center;
	border-top: 1px dashed #dedede;
	height: 2.604166rem;
	margin-top: 3.645833rem;;
}
.nrbtn div{
	display: flex;
	align-items: center;
	cursor: pointer;
	font-size: 0.833333rem;
	color: #cecece;
	font-weight: bold;
	padding-left: 1.041666rem;
	padding-right: 1.041666rem;
}
.nrbtn div label{
	width: 0.833333rem;
	height: 0.833333rem;
	display: block;
	background: url(../img/jngno.png) no-repeat;
	background-size: 100%;
	margin-right: 0.260416rem;
}
.nrbtn div:hover{
	color: #c00714;
}
.nrbtn div:hover label{
	background: url(../img/jng.png) no-repeat;
}
.aaaa{
	color: #c00714 !important;
}
.aaaa label{
	background: url(../img/jng.png) no-repeat !important;
}
.nrbtn div{
	border-right: 1px solid #c1c1c1;
}
.nrbtn div:last-child{
	border: none;
}