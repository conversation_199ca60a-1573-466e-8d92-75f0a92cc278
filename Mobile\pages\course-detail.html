<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>课程详情 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    <link rel="stylesheet" href="../css/learning-styles.css">
    
    <style>
        .detail-container {
            background: #f5f5f5;
            min-height: 100vh;
            padding-bottom: 80px;
        }

        /* 资源播放区域样式 */
        .resource-player {
            background: #000;
            position: relative;
            width: 100%;
            height: 280px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            overflow: hidden;
        }

        @media (max-width: 480px) {
            .resource-player {
                height: 240px;
            }
        }

        /* 视频播放器样式 */
        .video-container {
            position: relative;
            width: 100%;
            height: 100%;
            background: #000;
            overflow: hidden;
        }

        .video-container video {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .video-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            display: flex;
            align-items: center;
            justify-content: center;
            background: rgba(0, 0, 0, 0.3);
            opacity: 1;
            transition: opacity 0.3s ease;
            pointer-events: none;
        }

        .video-overlay.hidden {
            opacity: 0;
        }

        .center-play-btn {
            background: rgba(255, 255, 255, 0.9);
            border: none;
            color: #333;
            font-size: 20px;
            cursor: pointer;
            padding: 0;
            width: 50px;
            height: 50px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            pointer-events: all;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
            box-shadow: 0 2px 12px rgba(0, 0, 0, 0.3);
        }

        .center-play-btn:hover {
            background: rgba(255, 255, 255, 1);
            transform: scale(1.05);
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.4);
        }

        .center-play-btn:active {
            transform: scale(0.98);
        }

        .video-controls {
            position: absolute;
            bottom: 10px;
            left: 10px;
            right: 10px;
            display: flex;
            align-items: center;
            gap: 10px;
            background: rgba(0, 0, 0, 0.7);
            padding: 8px 12px;
            border-radius: 20px;
            color: white;
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .video-container:hover .video-controls {
            opacity: 1;
        }

        .play-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .progress-bar {
            flex: 1;
            height: 4px;
            background: rgba(255, 255, 255, 0.3);
            border-radius: 2px;
            overflow: hidden;
            cursor: pointer;
        }

        .progress-fill {
            height: 100%;
            background: #c00714;
            width: 0%;
            transition: width 0.1s ease;
        }

        .time-display {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.8);
        }

        .fullscreen-btn {
            background: none;
            border: none;
            color: white;
            font-size: 18px;
            cursor: pointer;
            padding: 0;
            width: 30px;
            height: 30px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .player-content {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .pdf-embed {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }

        .doc-viewer {
            width: 100%;
            height: 100%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: #666;
        }

        .ppt-viewer {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: white;
        }

        .video-player {
            width: 100%;
            height: 100%;
            background: #000;
        }

        .placeholder-content {
            text-align: center;
            color: #999;
        }

        .placeholder-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .placeholder-text {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .placeholder-subtitle {
            font-size: 14px;
            opacity: 0.7;
        }

        .play-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: opacity 0.3s ease;
        }

        .play-button {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .play-button:hover {
            background: white;
            transform: scale(1.1);
        }

        .resource-info {
            display: none; /* 隐藏资源信息 */
        }
        
        .detail-header {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            padding: 20px;
            position: relative;
        }
        
        .back-btn {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            cursor: pointer;
        }
        
        .detail-title {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            padding: 0 60px;
        }
        
        .course-info {
            background: white;
            margin: 20px;
            border-radius: 12px;
            padding: 0;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .course-header {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            color: #333;
            padding: 20px;
            text-align: center;
            position: relative;
            border-bottom: 1px solid #e9ecef;
        }

        .course-icon {
            font-size: 32px;
            margin-bottom: 12px;
            display: block;
            color: #c00714;
        }

        .course-content {
            padding: 20px;
        }
        
        .course-meta {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .course-title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
        }

        .course-teacher {
            color: #666;
            font-size: 14px;
            margin-bottom: 12px;
        }

        .course-category {
            display: inline-block;
            background: #f8f9fa;
            color: #c00714;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            margin-bottom: 8px;
            border: 1px solid #e9ecef;
            font-weight: 500;
        }

        .course-stats {
            display: flex;
            gap: 15px;
            font-size: 13px;
            color: #666;
            justify-content: center;
        }

        .course-rating {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #ffc107;
            font-size: 14px;
            justify-content: center;
            margin-bottom: 8px;
        }
        
        .course-description {
            background: white;
            margin: 0 20px 20px;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .description-content {
            color: #666;
            line-height: 1.6;
            font-size: 14px;
        }
        
        .course-chapters {
            background: white;
            margin: 0 20px 20px;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .chapter-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .chapter-item:last-child {
            border-bottom: none;
        }

        .chapter-actions {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: auto;
        }

        .chapter-actions button {
            transition: all 0.3s ease;
        }

        .chapter-actions button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .chapter-number {
            width: 30px;
            height: 30px;
            background: #f8f9fa;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            color: #666;
            margin-right: 12px;
        }
        
        .chapter-info {
            flex: 1;
        }
        
        .chapter-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 4px;
        }
        
        .chapter-duration {
            font-size: 12px;
            color: #999;
        }
        
        .action-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            padding: 15px 20px;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #c00714;
            color: white;
        }
        
        .btn-primary:hover {
            background: #a00610;
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #e9ecef;
        }
        
        .btn-secondary:hover {
            background: #e9ecef;
        }
        
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 50vh;
            color: #666;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-container {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }
        
        .error-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .error-message {
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .retry-btn {
            background: #c00714;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="detail-container">
        <!-- 头部 -->
        <div class="detail-header">
            <button class="back-btn" onclick="goBack()">
                ←
            </button>
            <h1 class="detail-title">课程详情</h1>
        </div>
        
        <!-- 内容区域 -->
        <div id="contentArea">
            <div class="loading-container">
                <div class="loading-spinner"></div>
                <p>正在加载课程详情...</p>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons" id="actionButtons" style="display: none;">
            <button class="action-btn btn-secondary" id="studyTimerBtn" onclick="toggleStudyTimer()">
                ⏱ 开始计时
            </button>
            <button class="action-btn btn-primary" onclick="showRatingDialog()">
                ⭐ 课程评分
            </button>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    
    <script>
        // 全局变量
        let studyStartTime = null;
        let studyTimer = null;
        let totalStudyTime = 0;
        let currentCourseId = null;

        // 获取URL参数
        function getUrlParam(name) {
            const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            const r = window.location.search.substr(1).match(reg);
            return r ? decodeURIComponent(r[2]) : null;
        }
        
        // 返回上一页
        function goBack() {
            // 如果正在学习，显示移动端友好的确认对话框
            if (studyStartTime !== null) {
                showMobileConfirm(
                    '确认返回？',
                    '您的学习记录将被保存',
                    () => {
                        // 确认返回
                        stopStudyTimer();
                        saveLearningRecord();
                        showToast('学习记录已保存', 'success');

                        setTimeout(() => {
                            if (window.history.length > 1) {
                                window.history.back();
                            } else {
                                window.location.href = '../index.html';
                            }
                        }, 300);
                    },
                    () => {
                        // 取消返回，继续学习
                        console.log('用户选择继续学习');
                    }
                );
            } else {
                // 没有在学习，直接返回
                if (window.history.length > 1) {
                    window.history.back();
                } else {
                    window.location.href = '../index.html';
                }
            }
        }
        
        // 格式化时间
        function formatTime(timeStr) {
            if (!timeStr) return '未知时间';
            try {
                const time = new Date(timeStr);
                return time.toLocaleDateString('zh-CN');
            } catch (e) {
                return timeStr;
            }
        }
        
        // 生成星级评分
        function generateStars(score) {
            const fullStars = Math.floor(score);
            const hasHalfStar = score % 1 >= 0.5;
            let stars = '';
            
            for (let i = 0; i < fullStars; i++) {
                stars += '⭐';
            }
            if (hasHalfStar) {
                stars += '⭐';
            }
            
            return stars || '⭐⭐⭐⭐⭐';
        }
        
        // 加载课程详情
        function loadCourseDetail(courseId) {
            if (!courseId) {
                showError('缺少课程ID参数');
                return;
            }

            console.log('加载课程详情，ID:', courseId);

            // 使用PC端相同的API接口
            $.ajax({
                url: baseurl + "/course/meta/" + courseId,
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: (res) => {
                    console.log('课程详情响应:', res);
                    if (res.code == '200' && res.data) {
                        renderCourseDetail(res.data);
                    } else {
                        showError('获取课程详情失败：' + (res.message || '未知错误'));
                    }
                },
                error: (err) => {
                    console.error('加载课程详情失败:', err);
                    showError('网络错误，请稍后重试');
                }
            });
        }
        
        // 渲染课程详情
        function renderCourseDetail(course) {
            console.log('渲染课程详情数据:', course);

            // 修复封面图片字段处理 - 参考PC端数据结构
            let imageUrl = '../img/course_default.jpg';
            if (course.coverPath && course.coverPath.length > 0) {
                imageUrl = baseurl + course.coverPath[0];
            } else if (course.covertPath) {
                // 兼容旧的拼写错误字段
                imageUrl = baseurl + course.covertPath;
            }

            // 处理评分数据 - PC端使用 listCourseEvaluate
            let score = 4.5;
            if (course.listCourseEvaluate && course.listCourseEvaluate.length > 3) {
                score = parseFloat(course.listCourseEvaluate[3].zh || 4.5);
            } else if (course.score) {
                score = parseFloat(course.score);
            }
            const stars = generateStars(score);
            
            // 处理课程基本信息
            const title = course.title || course.titleName || course.name || '无标题';
            const author = course.author || course.principal || course.teacher || '未知';
            const category = course.attachType || course.type || '课程学习';
            const createTime = course.createTime || course.createdAt || course.publishTime;
            const viewCount = course.view || course.studentCount || course.clickCount || 0;

            // 处理课程介绍
            const introduction = course.introduction || course.content || course.summary || course.description || course.remark || '暂无课程介绍';

            const html = `
                <!-- 资源播放区域 -->
                ${renderResourcePlayer(course)}

                <!-- 课程基本信息 -->
                <div class="course-info">
                    <div class="course-header">
                        <div class="course-icon">🎓</div>
                        <div class="course-title">${title}</div>
                        <div class="course-teacher">讲师：${author}</div>
                        <div class="course-rating">
                            <span>${stars}</span>
                            <span>${score.toFixed(1)}</span>
                        </div>
                    </div>
                    <div class="course-content">
                        <div class="course-category">${category}</div>
                        <div class="course-stats">
                            <span>📅 ${formatTime(createTime)}</span>
                            <span>👥 ${viewCount}人学习</span>
                        </div>
                    </div>
                </div>

                <!-- 课程介绍 -->
                <div class="course-description">
                    <div class="section-title">📋 课程介绍</div>
                    <div class="description-content">
                        ${introduction}
                    </div>
                </div>

                <!-- 课程资源列表已移除，资源在播放器中自动加载 -->
            `;
            
            document.getElementById('contentArea').innerHTML = html;
            document.getElementById('actionButtons').style.display = 'flex';

            // 更新页面标题
            document.title = `${title} - 课程详情`;

            // 自动启动学习计时
            setTimeout(() => {
                autoStartStudyTimer();
            }, 500);
        }

        // 渲染资源播放器
        function renderResourcePlayer(course) {
            // 检查是否有课程资源
            if (course.cmsResourcesCourseMetaList && course.cmsResourcesCourseMetaList.length > 0) {
                const firstResource = course.cmsResourcesCourseMetaList[0];
                const resourceType = getResourceType(firstResource.attachPath);
                const resourceName = firstResource.attachName || '课程资源';
                const resourcePath = baseurl + firstResource.attachPath;

                return renderPlayerByType(resourceType, resourcePath, resourceName);
            } else {
                // 没有资源时显示默认播放器
                return `
                    <div class="resource-player">
                        <div class="placeholder-content">
                            <div class="placeholder-icon">🎓</div>
                            <div class="placeholder-text">暂无课程资源</div>
                            <div class="placeholder-subtitle">请联系管理员添加课程内容</div>
                        </div>
                    </div>
                `;
            }
        }

        // 根据资源类型渲染播放器
        function renderPlayerByType(type, path, name) {
            switch (type.toLowerCase()) {
                case 'pdf':
                    // 自动在PDF查看器中打开
                    setTimeout(() => {
                        openFullPDF(path, name);
                    }, 1000);

                    return `
                        <div class="resource-player">
                            <div class="player-content">
                                <iframe src="https://docs.google.com/viewer?url=${encodeURIComponent(path)}&embedded=true"
                                        style="width: 100%; height: 100%; border: none; background: white;"
                                        onload="console.log('PDF加载完成')"
                                        onerror="console.log('PDF加载失败，将跳转到专用阅读器')">
                                </iframe>
                            </div>
                        </div>
                    `;

                case 'doc':
                case 'docx':
                    // 自动打开文档
                    setTimeout(() => {
                        openDocumentOnline(path, name);
                    }, 1000);

                    return `
                        <div class="resource-player">
                            <div class="doc-viewer">
                                <div class="placeholder-icon">📄</div>
                                <div style="margin-top: 15px; font-size: 16px; color: #666;">
                                    正在打开文档...
                                </div>
                                <div style="margin-top: 10px; font-size: 14px; color: #999;">
                                    如未自动打开，请点击下方按钮
                                </div>
                                <button onclick="openDocumentOnline('${path}', '${name}')"
                                        style="margin-top: 15px; padding: 10px 20px; background: #007bff; color: white; border: none; border-radius: 6px; cursor: pointer;">
                                    📖 手动打开
                                </button>
                            </div>
                        </div>
                    `;

                case 'ppt':
                case 'pptx':
                    // 自动打开演示文稿
                    setTimeout(() => {
                        openPresentationOnline(path, name);
                    }, 1000);

                    return `
                        <div class="resource-player">
                            <div class="ppt-viewer">
                                <div class="placeholder-icon">📊</div>
                                <div style="margin-top: 15px; font-size: 16px; color: white;">
                                    正在打开演示文稿...
                                </div>
                                <div style="margin-top: 10px; font-size: 14px; color: rgba(255,255,255,0.8);">
                                    如未自动打开，请点击下方按钮
                                </div>
                                <button onclick="openPresentationOnline('${path}', '${name}')"
                                        style="margin-top: 15px; padding: 10px 20px; background: #d04423; color: white; border: none; border-radius: 6px; cursor: pointer;">
                                    📊 手动打开
                                </button>
                            </div>
                        </div>
                    `;

                case 'mp4':
                case 'avi':
                case 'mov':
                case 'webm':
                    return `
                        <div class="resource-player">
                            <div class="video-container" id="videoContainer">
                                <video class="video-player" id="videoPlayer"
                                       preload="auto"
                                       style="width: 100%; height: 100%; object-fit: contain; background: #000;"
                                       onloadstart="console.log('视频开始加载')"
                                       oncanplay="console.log('视频可以播放')"
                                       onerror="handleVideoError(this, '${path}', '${name}')"
                                       ontimeupdate="updateVideoProgress(this)"
                                       onplay="hideVideoOverlay()"
                                       onpause="showVideoOverlay()"
                                       onended="showVideoOverlay()">
                                    <source src="${path}" type="video/${type}">
                                    <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #000; color: white; flex-direction: column;">
                                        <div style="font-size: 24px; margin-bottom: 10px;">🎥</div>
                                        <div>您的浏览器不支持视频播放</div>
                                        <button onclick="downloadResource('${path}', '${name}')"
                                                style="margin-top: 15px; padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                            📥 下载视频
                                        </button>
                                    </div>
                                </video>

                                <!-- 视频覆盖层和中央播放按钮 -->
                                <div class="video-overlay" id="videoOverlay">
                                    <button class="center-play-btn" onclick="toggleVideoPlay()">
                                        <span id="centerPlayIcon">▶</span>
                                    </button>
                                </div>

                                <!-- 视频控制条 -->
                                <div class="video-controls">
                                    <button class="play-btn" onclick="toggleVideoPlay()">
                                        <span id="playIcon">▶</span>
                                    </button>
                                    <div class="progress-bar" onclick="seekVideo(event)">
                                        <div class="progress-fill" id="progressFill"></div>
                                    </div>
                                    <div class="time-display">
                                        <span id="currentTime">0:00</span> / <span id="duration">0:00</span>
                                    </div>
                                    <button class="fullscreen-btn" onclick="toggleFullscreen()">
                                        <span id="fullscreenIcon">⛶</span>
                                    </button>
                                </div>
                            </div>
                        </div>
                    `;

                default:
                    return `
                        <div class="resource-player">
                            <div class="placeholder-content">
                                <div class="placeholder-icon">📄</div>
                                <div class="placeholder-text">${name}</div>
                                <div class="placeholder-subtitle">${type.toUpperCase()}文件</div>
                                <button onclick="downloadResource('${path}', '${name}')"
                                        style="margin-top: 15px; padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                    📥 下载文件
                                </button>
                            </div>
                            <div class="resource-info">
                                <div class="resource-title">${name}</div>
                                <div class="resource-meta">${type.toUpperCase()}文件 • 点击下载</div>
                            </div>
                        </div>
                    `;
            }
        }

        // 打开PDF全屏查看
        function openFullPDF(pdfUrl, title) {
            window.location.href = `pdf-viewer.html?url=${encodeURIComponent(pdfUrl)}&title=${encodeURIComponent(title)}`;
        }

        // 视频播放控制函数
        function toggleVideoPlay() {
            const video = document.getElementById('videoPlayer');
            const playIcon = document.getElementById('playIcon');
            const centerPlayIcon = document.getElementById('centerPlayIcon');

            if (video) {
                if (video.paused) {
                    video.play().then(() => {
                        if (playIcon) playIcon.textContent = '⏸';
                        if (centerPlayIcon) centerPlayIcon.textContent = '⏸';
                        hideVideoOverlay();
                    }).catch(e => {
                        console.log('播放失败:', e);
                    });
                } else {
                    video.pause();
                    if (playIcon) playIcon.textContent = '▶';
                    if (centerPlayIcon) centerPlayIcon.textContent = '▶';
                    showVideoOverlay();
                }
            }
        }

        function hideVideoOverlay() {
            const overlay = document.getElementById('videoOverlay');
            if (overlay) {
                overlay.classList.add('hidden');
            }
        }

        function showVideoOverlay() {
            const overlay = document.getElementById('videoOverlay');
            if (overlay) {
                overlay.classList.remove('hidden');
            }
        }

        function updateVideoProgress(video) {
            const progressFill = document.getElementById('progressFill');
            const currentTimeSpan = document.getElementById('currentTime');
            const durationSpan = document.getElementById('duration');

            if (progressFill && video.duration) {
                const progress = (video.currentTime / video.duration) * 100;
                progressFill.style.width = progress + '%';
            }

            if (currentTimeSpan) {
                currentTimeSpan.textContent = formatVideoTime(video.currentTime);
            }

            if (durationSpan && video.duration) {
                durationSpan.textContent = formatVideoTime(video.duration);
            }
        }

        function seekVideo(event) {
            const video = document.getElementById('videoPlayer');
            const progressBar = event.currentTarget;

            if (video && video.duration) {
                const rect = progressBar.getBoundingClientRect();
                const clickX = event.clientX - rect.left;
                const percentage = clickX / rect.width;
                video.currentTime = percentage * video.duration;
            }
        }

        function formatVideoTime(seconds) {
            if (isNaN(seconds)) return '0:00';

            const minutes = Math.floor(seconds / 60);
            const secs = Math.floor(seconds % 60);
            return `${minutes}:${secs.toString().padStart(2, '0')}`;
        }

        // 全屏功能
        function toggleFullscreen() {
            const videoContainer = document.getElementById('videoContainer');
            const fullscreenIcon = document.getElementById('fullscreenIcon');

            if (!videoContainer) return;

            if (!document.fullscreenElement) {
                // 进入全屏
                if (videoContainer.requestFullscreen) {
                    videoContainer.requestFullscreen();
                } else if (videoContainer.webkitRequestFullscreen) {
                    videoContainer.webkitRequestFullscreen();
                } else if (videoContainer.msRequestFullscreen) {
                    videoContainer.msRequestFullscreen();
                }
                if (fullscreenIcon) fullscreenIcon.textContent = '⛶';
            } else {
                // 退出全屏
                if (document.exitFullscreen) {
                    document.exitFullscreen();
                } else if (document.webkitExitFullscreen) {
                    document.webkitExitFullscreen();
                } else if (document.msExitFullscreen) {
                    document.msExitFullscreen();
                }
                if (fullscreenIcon) fullscreenIcon.textContent = '⛶';
            }
        }

        // 监听全屏状态变化
        document.addEventListener('fullscreenchange', handleFullscreenChange);
        document.addEventListener('webkitfullscreenchange', handleFullscreenChange);
        document.addEventListener('msfullscreenchange', handleFullscreenChange);

        function handleFullscreenChange() {
            const fullscreenIcon = document.getElementById('fullscreenIcon');
            if (fullscreenIcon) {
                if (document.fullscreenElement || document.webkitFullscreenElement || document.msFullscreenElement) {
                    fullscreenIcon.textContent = '⛶';
                } else {
                    fullscreenIcon.textContent = '⛶';
                }
            }
        }

        // 视频错误处理
        function handleVideoError(videoElement, videoUrl, videoName) {
            console.error('视频加载失败:', videoUrl);
            videoElement.style.display = 'none';

            const errorHtml = `
                <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #f8f9fa; color: #666; flex-direction: column; padding: 20px;">
                    <div style="font-size: 48px; margin-bottom: 15px;">🎥</div>
                    <div style="font-size: 18px; font-weight: 600; margin-bottom: 10px;">视频无法播放</div>
                    <div style="font-size: 14px; margin-bottom: 20px; text-align: center;">
                        视频格式不支持或文件损坏<br>
                        您可以尝试下载到本地播放
                    </div>
                    <button onclick="downloadResource('${videoUrl}', '${videoName}')"
                            style="background: #28a745; color: white; border: none; padding: 12px 24px; border-radius: 6px; font-size: 16px; cursor: pointer;">
                        📥 下载视频
                    </button>
                </div>
            `;

            videoElement.parentElement.innerHTML = errorHtml;
        }

        // 在线打开Word文档
        function openDocumentOnline(docUrl, title) {
            // 尝试多种在线查看方式
            const viewers = [
                `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(docUrl)}`,
                `https://docs.google.com/viewer?url=${encodeURIComponent(docUrl)}&embedded=true`
            ];

            // 首先尝试Office Online
            const newWindow = window.open(viewers[0], '_blank');

            // 如果打开失败，提供备选方案
            setTimeout(() => {
                if (!newWindow || newWindow.closed) {
                    if (confirm('Office Online无法打开，是否尝试Google Docs查看器？')) {
                        window.open(viewers[1], '_blank');
                    }
                }
            }, 3000);
        }

        // 在线打开PowerPoint演示文稿
        function openPresentationOnline(pptUrl, title) {
            // 尝试多种在线查看方式
            const viewers = [
                `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(pptUrl)}`,
                `https://docs.google.com/viewer?url=${encodeURIComponent(pptUrl)}&embedded=true`
            ];

            // 首先尝试Office Online
            const newWindow = window.open(viewers[0], '_blank');

            // 如果打开失败，提供备选方案
            setTimeout(() => {
                if (!newWindow || newWindow.closed) {
                    if (confirm('Office Online无法打开，是否尝试Google Docs查看器？')) {
                        window.open(viewers[1], '_blank');
                    }
                }
            }, 3000);
        }



        // 显示提示消息
        function showToast(message, type = 'info') {
            const toast = document.createElement('div');
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: ${type === 'success' ? '#28a745' : type === 'error' ? '#dc3545' : '#007bff'};
                color: white;
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                z-index: 10000;
                box-shadow: 0 2px 8px rgba(0,0,0,0.2);
                opacity: 0;
                transition: opacity 0.3s ease;
            `;
            toast.textContent = message;

            document.body.appendChild(toast);

            // 显示动画
            setTimeout(() => {
                toast.style.opacity = '1';
            }, 100);

            // 自动隐藏
            setTimeout(() => {
                toast.style.opacity = '0';
                setTimeout(() => {
                    if (toast.parentNode) {
                        toast.parentNode.removeChild(toast);
                    }
                }, 300);
            }, 3000);
        }

        // 移动端友好的确认对话框
        function showMobileConfirm(title, message, onConfirm, onCancel) {
            const modal = document.createElement('div');
            modal.style.cssText = `
                position: fixed;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: rgba(0, 0, 0, 0.5);
                z-index: 10001;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 20px;
                opacity: 0;
                transition: opacity 0.3s ease;
            `;

            const dialog = document.createElement('div');
            dialog.style.cssText = `
                background: white;
                border-radius: 12px;
                padding: 24px;
                max-width: 320px;
                width: 100%;
                box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
                transform: scale(0.9);
                transition: transform 0.3s ease;
            `;

            dialog.innerHTML = `
                <div style="text-align: center; margin-bottom: 20px;">
                    <div style="font-size: 18px; font-weight: 600; color: #333; margin-bottom: 8px;">${title}</div>
                    <div style="font-size: 14px; color: #666; line-height: 1.5;">${message}</div>
                </div>
                <div style="display: flex; gap: 12px;">
                    <button id="cancelBtn" style="flex: 1; padding: 12px; background: #f8f9fa; border: 1px solid #e9ecef; border-radius: 8px; color: #666; font-size: 16px; cursor: pointer;">
                        继续学习
                    </button>
                    <button id="confirmBtn" style="flex: 1; padding: 12px; background: #c00714; border: none; border-radius: 8px; color: white; font-size: 16px; cursor: pointer;">
                        确认返回
                    </button>
                </div>
            `;

            modal.appendChild(dialog);
            document.body.appendChild(modal);

            // 显示动画
            setTimeout(() => {
                modal.style.opacity = '1';
                dialog.style.transform = 'scale(1)';
            }, 50);

            // 绑定事件
            const confirmBtn = dialog.querySelector('#confirmBtn');
            const cancelBtn = dialog.querySelector('#cancelBtn');

            confirmBtn.onclick = () => {
                closeModal();
                if (onConfirm) onConfirm();
            };

            cancelBtn.onclick = () => {
                closeModal();
                if (onCancel) onCancel();
            };

            // 点击背景关闭
            modal.onclick = (e) => {
                if (e.target === modal) {
                    closeModal();
                    if (onCancel) onCancel();
                }
            };

            function closeModal() {
                modal.style.opacity = '0';
                dialog.style.transform = 'scale(0.9)';
                setTimeout(() => {
                    if (modal.parentNode) {
                        modal.parentNode.removeChild(modal);
                    }
                }, 300);
            }
        }

        // 下载资源
        function downloadResource(resourceUrl, fileName) {
            const link = document.createElement('a');
            link.href = resourceUrl;
            link.download = fileName;
            link.click();
        }

        // 自动启动学习计时
        function autoStartStudyTimer() {
            if (studyStartTime === null) {
                const btn = document.getElementById('studyTimerBtn');
                if (btn) {
                    // 自动开始计时
                    studyStartTime = new Date();
                    btn.innerHTML = '⏱ 已学习 0:00...';
                    btn.classList.add('btn-primary');
                    btn.classList.remove('btn-secondary');

                    // 更新计时显示
                    studyTimer = setInterval(updateStudyTime, 1000);

                    console.log('自动开始学习计时:', studyStartTime);

                    // 显示提示消息
                    showToast('📚 开始学习计时', 'success');

                    // 保持按钮可点击，但改变其功能为显示详细信息
                    btn.onclick = () => {
                        const currentTime = Math.floor((new Date() - studyStartTime) / 1000);
                        showToast(`本次学习时长：${formatStudyTime(currentTime)}`, 'info');
                    };
                }
            }
        }

        // 学习计时功能
        function toggleStudyTimer() {
            const btn = document.getElementById('studyTimerBtn');

            if (studyStartTime === null) {
                // 开始计时
                studyStartTime = new Date();
                btn.innerHTML = '⏱ 学习中...';
                btn.classList.add('btn-primary');
                btn.classList.remove('btn-secondary');

                // 更新计时显示
                studyTimer = setInterval(updateStudyTime, 1000);

                console.log('手动开始学习计时:', studyStartTime);
            } else {
                // 停止计时
                stopStudyTimer();
            }
        }

        function stopStudyTimer() {
            if (studyStartTime !== null) {
                const endTime = new Date();
                const sessionTime = Math.floor((endTime - studyStartTime) / 1000);
                totalStudyTime += sessionTime;

                const btn = document.getElementById('studyTimerBtn');
                btn.innerHTML = `⏱ 已学习 ${formatStudyTime(totalStudyTime)}`;
                btn.classList.remove('btn-primary');
                btn.classList.add('btn-secondary');

                if (studyTimer) {
                    clearInterval(studyTimer);
                    studyTimer = null;
                }

                studyStartTime = null;

                console.log('停止学习计时，本次学习:', sessionTime, '秒，总计:', totalStudyTime, '秒');

                // 保存学习记录
                saveStudyRecord(sessionTime);
            }
        }

        function updateStudyTime() {
            if (studyStartTime !== null) {
                const now = new Date();
                const currentSessionTime = Math.floor((now - studyStartTime) / 1000);
                const displayTime = totalStudyTime + currentSessionTime;

                const btn = document.getElementById('studyTimerBtn');
                // 添加动态效果，显示正在计时
                const dots = '.'.repeat((Math.floor(currentSessionTime) % 3) + 1);
                btn.innerHTML = `⏱ 已学习 ${formatStudyTime(displayTime)}${dots}`;

                // 添加轻微的颜色变化表示活跃状态
                btn.style.background = currentSessionTime % 2 === 0 ? '#c00714' : '#a00610';
            }
        }

        function formatStudyTime(seconds) {
            const hours = Math.floor(seconds / 3600);
            const minutes = Math.floor((seconds % 3600) / 60);
            const secs = seconds % 60;

            if (hours > 0) {
                return `${hours}:${minutes.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
            } else {
                return `${minutes}:${secs.toString().padStart(2, '0')}`;
            }
        }

        function saveStudyRecord(sessionTime) {
            if (!currentCourseId) return;

            // 参考PC端的学习记录保存
            const studyData = {
                courseId: currentCourseId,
                studyTime: sessionTime,
                totalTime: totalStudyTime,
                timestamp: new Date().toISOString()
            };

            // 保存到本地存储
            const studyRecords = JSON.parse(localStorage.getItem('studyRecords') || '[]');
            studyRecords.push(studyData);
            localStorage.setItem('studyRecords', JSON.stringify(studyRecords));

            // 如果有API接口，也可以发送到服务器
            // saveStudyRecordToServer(studyData);
        }

        // 评分功能
        function showRatingDialog() {
            const ratingHtml = `
                <div id="ratingModal" style="position: fixed; top: 0; left: 0; right: 0; bottom: 0; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; border-radius: 12px; padding: 30px; margin: 20px; max-width: 400px; width: 90%;">
                        <h3 style="text-align: center; margin-bottom: 20px; color: #333;">课程评分</h3>
                        <div style="text-align: center; margin-bottom: 20px;">
                            <div style="font-size: 14px; color: #666; margin-bottom: 15px;">请为这门课程打分</div>
                            <div id="starRating" style="font-size: 32px; margin-bottom: 15px;">
                                <span class="star" data-rating="1">⭐</span>
                                <span class="star" data-rating="2">⭐</span>
                                <span class="star" data-rating="3">⭐</span>
                                <span class="star" data-rating="4">⭐</span>
                                <span class="star" data-rating="5">⭐</span>
                            </div>
                            <div id="ratingText" style="font-size: 14px; color: #999; margin-bottom: 20px;">点击星星进行评分</div>
                        </div>
                        <textarea id="ratingComment" placeholder="请输入您的评价（可选）" style="width: 100%; height: 80px; border: 1px solid #ddd; border-radius: 6px; padding: 10px; resize: none; font-size: 14px; margin-bottom: 20px;"></textarea>
                        <div style="display: flex; gap: 10px;">
                            <button onclick="closeRatingDialog()" style="flex: 1; padding: 12px; background: #f8f9fa; border: 1px solid #ddd; border-radius: 6px; cursor: pointer;">取消</button>
                            <button onclick="submitRating()" style="flex: 1; padding: 12px; background: #c00714; color: white; border: none; border-radius: 6px; cursor: pointer;">提交评分</button>
                        </div>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', ratingHtml);

            // 添加星星点击事件
            const stars = document.querySelectorAll('.star');
            let selectedRating = 0;

            stars.forEach(star => {
                star.style.cursor = 'pointer';
                star.style.opacity = '0.3';

                star.addEventListener('click', function() {
                    selectedRating = parseInt(this.getAttribute('data-rating'));
                    updateStarDisplay(selectedRating);
                    updateRatingText(selectedRating);
                });

                star.addEventListener('mouseover', function() {
                    const rating = parseInt(this.getAttribute('data-rating'));
                    updateStarDisplay(rating);
                });
            });

            document.getElementById('starRating').addEventListener('mouseleave', function() {
                updateStarDisplay(selectedRating);
            });

            function updateStarDisplay(rating) {
                stars.forEach((star, index) => {
                    star.style.opacity = index < rating ? '1' : '0.3';
                });
            }

            function updateRatingText(rating) {
                const texts = ['', '很差', '较差', '一般', '良好', '优秀'];
                document.getElementById('ratingText').textContent = texts[rating] || '点击星星进行评分';
            }

            // 保存选中的评分到全局变量
            window.selectedCourseRating = selectedRating;
        }

        function closeRatingDialog() {
            const modal = document.getElementById('ratingModal');
            if (modal) {
                modal.remove();
            }
        }

        function submitRating() {
            const rating = window.selectedCourseRating || 0;
            const comment = document.getElementById('ratingComment').value.trim();

            if (rating === 0) {
                alert('请先选择评分');
                return;
            }

            // 保存评分
            const ratingData = {
                courseId: currentCourseId,
                rating: rating,
                comment: comment,
                timestamp: new Date().toISOString()
            };

            // 保存到本地存储
            const ratings = JSON.parse(localStorage.getItem('courseRatings') || '[]');
            ratings.push(ratingData);
            localStorage.setItem('courseRatings', JSON.stringify(ratings));

            console.log('课程评分已保存:', ratingData);

            // 这里可以添加发送到服务器的代码
            // submitRatingToServer(ratingData);

            closeRatingDialog();
            alert('评分提交成功！感谢您的反馈。');
        }

        // 访问路径记录功能（参考PC端）
        let learningStartTime = Date.now();
        let isLearningRecordSaved = false;

        // 创建学习记录数据
        function createLearningRecord() {
            if (!currentCourseId) return null;

            const userinfo = sessionStorage.getItem("userinfo");
            if (!userinfo) {
                console.log('用户未登录，无法创建学习记录');
                return null;
            }

            const user = JSON.parse(userinfo);
            const endTime = Date.now();
            const studyDuration = Math.floor((endTime - learningStartTime) / 1000); // 转换为秒

            // 构建学习记录数据
            return {
                courseId: currentCourseId,
                userId: user.id || user.userId,
                userName: user.name || user.username,
                studyTime: studyDuration,
                startTime: new Date(learningStartTime).toISOString(),
                endTime: new Date(endTime).toISOString(),
                pageType: 'course-detail-mobile',
                deviceType: 'mobile',
                userAgent: navigator.userAgent,
                timestamp: new Date().toISOString()
            };
        }

        function saveLearningRecord() {
            if (isLearningRecordSaved || !currentCourseId) return;

            const recordData = createLearningRecord();
            if (!recordData) return;

            console.log('准备保存学习记录:', recordData);

            // 保存到localStorage作为备份
            const existingRecords = JSON.parse(localStorage.getItem("jilu") || "[]");
            existingRecords.push(recordData);
            localStorage.setItem("jilu", JSON.stringify(existingRecords));

            // 尝试发送到服务器 - 使用异步请求避免阻塞
            const authHeader = sessionStorage.getItem("header");
            if (authHeader) {
                // 使用fetch API进行异步提交
                fetch(baseurl + "/api/study/record", {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': authHeader
                    },
                    body: JSON.stringify(recordData)
                })
                .then(response => {
                    if (response.ok) {
                        return response.json();
                    } else {
                        throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                    }
                })
                .then(data => {
                    console.log('学习记录保存成功:', data);
                    isLearningRecordSaved = true;
                    // 清除已成功保存的记录
                    localStorage.removeItem("jilu");
                })
                .catch(error => {
                    console.error('学习记录保存失败:', error);
                    // 尝试备用API
                    tryBackupAPI(recordData, authHeader);
                });
            } else {
                console.log('未找到认证头，仅保存到本地存储');
            }
        }

        // 尝试备用API
        function tryBackupAPI(recordData, authHeader) {
            console.log('尝试备用API提交学习记录');

            // 尝试使用PC端相同的API
            fetch(baseurl + "/study/record/add", {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': authHeader
                },
                body: JSON.stringify(recordData)
            })
            .then(response => {
                if (response.ok) {
                    return response.json();
                } else {
                    throw new Error(`备用API也失败: HTTP ${response.status}`);
                }
            })
            .then(data => {
                console.log('备用API保存成功:', data);
                isLearningRecordSaved = true;
                localStorage.removeItem("jilu");
            })
            .catch(error => {
                console.error('备用API也失败:', error);
                console.log('学习记录已保存到本地存储，等待下次重试');
            });
        }

        // 页面关闭前的处理（参考PC端）
        window.addEventListener('beforeunload', function(e) {
            // 停止学习计时
            if (studyStartTime !== null) {
                stopStudyTimer();
            }

            // 强制保存学习记录
            if (!isLearningRecordSaved) {
                saveLearningRecord();
            }

            // 不显示确认对话框，直接保存
            console.log('页面即将关闭，学习记录已保存');
        });

        // 页面隐藏时也保存记录
        window.addEventListener('pagehide', function(e) {
            if (!isLearningRecordSaved) {
                saveLearningRecord();
            }
        });

        // 页面可见性改变时的处理（参考PC端）
        let isPageInitialized = false;

        document.addEventListener('visibilitychange', function() {
            // 只有在页面初始化完成后才处理可见性变化
            if (!isPageInitialized) return;

            if (document.visibilityState === 'hidden') {
                // 页面隐藏时只保存记录，不停止计时
                console.log('页面隐藏，保存学习记录');
                saveLearningRecord();
            } else if (document.visibilityState === 'visible') {
                // 页面重新可见时重置学习记录状态
                console.log('页面重新可见，重置学习记录状态');
                learningStartTime = Date.now();
                isLearningRecordSaved = false;
            }
        });

        // 定期保存学习记录（每5分钟）- 但不重置计时
        setInterval(function() {
            if (!isLearningRecordSaved && studyStartTime !== null) {
                console.log('定期保存学习记录');
                // 创建一个临时的学习记录，但不标记为已保存
                const tempRecord = createLearningRecord();
                if (tempRecord) {
                    // 保存到本地存储作为备份
                    const existingRecords = JSON.parse(localStorage.getItem("jilu") || "[]");
                    existingRecords.push(tempRecord);
                    localStorage.setItem("jilu", JSON.stringify(existingRecords));
                    console.log('定期备份已保存到本地存储');
                }
            }
        }, 5 * 60 * 1000); // 5分钟

        // 渲染课程资源
        function renderCourseResources(course) {
            let resourcesHtml = '';

            // 检查是否有课程资源列表
            if (course.cmsResourcesCourseMetaList && course.cmsResourcesCourseMetaList.length > 0) {
                // 从第二个资源开始显示（第一个已在播放器中显示）
                const remainingResources = course.cmsResourcesCourseMetaList.slice(1);

                if (remainingResources.length > 0) {
                    remainingResources.forEach((resource, index) => {
                        const resourceType = getResourceType(resource.attachPath);
                        const resourceIcon = getResourceIcon(resourceType);

                        resourcesHtml += `
                            <div class="chapter-item">
                                <div class="chapter-number">${resourceIcon}</div>
                                <div class="chapter-info">
                                    <div class="chapter-title">${resource.attachName || `资源 ${index + 2}`}</div>
                                    <div class="chapter-duration">📄 ${resourceType.toUpperCase()}文件</div>
                                </div>
                                <div class="chapter-actions">
                                    <button onclick="openResource('${resource.attachPath}', '${resource.attachName}', '${resourceType}')"
                                            style="background: #c00714; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; margin-right: 8px; cursor: pointer;">
                                        📺 播放
                                    </button>
                                    <button onclick="openResourceInNewWindow('${resource.attachPath}', '${resource.attachName}', '${resourceType}')"
                                            style="background: #6c757d; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; cursor: pointer;">
                                        🔗 打开
                                    </button>
                                </div>
                            </div>
                        `;
                    });
                } else {
                    resourcesHtml = `
                        <div style="text-align: center; padding: 20px; color: #999;">
                            <div style="font-size: 24px; margin-bottom: 10px;">📚</div>
                            <div>主要资源已在上方播放器中显示</div>
                        </div>
                    `;
                }
            } else {
                // 默认显示一些示例章节
                resourcesHtml = `
                    <div class="chapter-item">
                        <div class="chapter-number">1</div>
                        <div class="chapter-info">
                            <div class="chapter-title">第一章：课程导论</div>
                            <div class="chapter-duration">⏱ 30分钟</div>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">2</div>
                        <div class="chapter-info">
                            <div class="chapter-title">第二章：核心内容</div>
                            <div class="chapter-duration">⏱ 45分钟</div>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">3</div>
                        <div class="chapter-info">
                            <div class="chapter-title">第三章：实践应用</div>
                            <div class="chapter-duration">⏱ 35分钟</div>
                        </div>
                    </div>
                `;
            }

            return resourcesHtml;
        }

        // 获取资源类型
        function getResourceType(filePath) {
            if (!filePath) return 'unknown';
            const extension = filePath.toLowerCase().split('.').pop();
            return extension || 'unknown';
        }

        // 获取资源图标
        function getResourceIcon(type) {
            const iconMap = {
                'pdf': '📕',
                'doc': '📄',
                'docx': '📄',
                'ppt': '📊',
                'pptx': '📊',
                'xls': '📈',
                'xlsx': '📈',
                'mp4': '🎥',
                'avi': '🎥',
                'mp3': '🎵',
                'wav': '🎵',
                'jpg': '🖼️',
                'jpeg': '🖼️',
                'png': '🖼️',
                'gif': '🖼️'
            };
            return iconMap[type] || '📄';
        }

        // 打开资源 - 修改为切换播放器内容
        function openResource(resourcePath, resourceName, resourceType) {
            if (!resourcePath) {
                alert('资源路径无效');
                return;
            }

            const fullUrl = baseurl + resourcePath;
            const playerContainer = document.querySelector('.resource-player');

            if (playerContainer) {
                // 显示加载状态
                playerContainer.innerHTML = `
                    <div style="display: flex; align-items: center; justify-content: center; height: 100%; background: #f8f9fa; color: #666; flex-direction: column;">
                        <div class="loading-spinner" style="width: 40px; height: 40px; border: 4px solid #f3f3f3; border-top: 4px solid #c00714; border-radius: 50%; animation: spin 1s linear infinite; margin-bottom: 20px;"></div>
                        <div style="font-size: 16px;">正在加载 ${resourceName}...</div>
                    </div>
                `;

                // 延迟更新内容，让用户看到加载状态
                setTimeout(() => {
                    const newPlayerHtml = renderPlayerByType(resourceType, fullUrl, resourceName);
                    playerContainer.outerHTML = newPlayerHtml;

                    // 滚动到播放器顶部
                    setTimeout(() => {
                        document.querySelector('.resource-player').scrollIntoView({
                            behavior: 'smooth',
                            block: 'start'
                        });
                    }, 100);
                }, 500);
            }
        }

        // 原始的打开资源功能（用于新窗口打开）
        function openResourceInNewWindow(resourcePath, resourceName, resourceType) {
            if (!resourcePath) {
                alert('资源路径无效');
                return;
            }

            const fullUrl = baseurl + resourcePath;

            if (resourceType === 'pdf') {
                // 跳转到PDF查看器
                window.location.href = `pdf-viewer.html?url=${encodeURIComponent(fullUrl)}&title=${encodeURIComponent(resourceName)}`;
            } else {
                // 其他类型文件直接下载或在新窗口打开
                window.open(fullUrl, '_blank');
            }
        }

        // 显示错误信息
        function showError(message) {
            const html = `
                <div class="error-container">
                    <div class="error-icon">🎓</div>
                    <div class="error-message">${message}</div>
                    <button class="retry-btn" onclick="location.reload()">重新加载</button>
                </div>
            `;
            document.getElementById('contentArea').innerHTML = html;
        }
        
        // 添加到收藏
        function addToFavorites() {
            // 这里可以添加收藏功能的实现
            alert('收藏功能开发中...');
        }
        
        // 开始学习
        function startLearning() {
            const courseId = getUrlParam('id');
            if (courseId) {
                // 跳转到学习页面
                window.location.href = `learning.html?id=${courseId}&type=course`;
            } else {
                alert('无法获取课程信息');
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            const courseId = getUrlParam('id');
            if (courseId) {
                currentCourseId = courseId; // 设置全局课程ID
                loadCourseDetail(courseId);

                // 自动开始学习计时
                setTimeout(() => {
                    autoStartStudyTimer();
                }, 2000); // 延迟2秒开始计时，让用户看到界面

                // 初始化学习记录
                learningStartTime = Date.now();
                isLearningRecordSaved = false;

                // 标记页面初始化完成
                setTimeout(() => {
                    isPageInitialized = true;
                    console.log('页面初始化完成，开始监听可见性变化');
                }, 3000); // 延迟3秒确保所有初始化完成

                console.log('课程详情页面初始化完成，课程ID:', courseId);
            } else {
                showError('缺少课程ID参数');
            }
        });
    </script>
</body>
</html>
