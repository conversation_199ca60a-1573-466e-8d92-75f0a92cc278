<!DOCTYPE html>
<html>
	<head>
	
		
		<meta charset="utf-8" />
		<title>思政一体化平台</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/index.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/yuanbao.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/check.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/login-mock.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body class="index">
		<div class="tcbox" id="tcbox">

			<div class="topviewsss">

				<a onclick="closetc()">关闭</a>
				<label id="pdfname"></label>
			</div>
			<div class="panel-body pdfbox">
				<a id="pdf" class="media" href=""></a>
			</div>
		</div>
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
					<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>
					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
					
				</div>
			</div>
		</div>
		<div id="indexswiper" class="swiper">
			<div class="swiper-wrapper" id="banner">

				<!-- <div class="swiper-slide">
					<img src="img/banner.png" />
					<div class="swipertxt">
						<div>
							这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字
						</div>
					</div>
				</div>
				<div class="swiper-slide">
					<img src="img/banner.png" />
					<div class="swipertxt">
						<div>
							这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字这是一段文字
						</div>
					</div>
				</div> -->
			</div>
			<div id="indexswiperpagination" class="swiper-pagination">

			</div>
		</div>
		<div class="contextview">
			<div class="flexview">
				<div class="item">
					<div class="itemtop" id="newsbarlist">

					</div>
					<div class="itemtopbox" id="new1list">


					</div>
				</div>
				<div class="item flex">
					<div class="itemleft">
						<div class="border"></div>
						<div class="paddleft">
							<div class="itemlefttitle">
								<label class="itemlefttitletxt titleactive">最新公告</label>
							</div>
							<div class="xwlist">
								<div class="swiper" id="news">
									<div class="swiper-wrapper" id="newsgglist">

									</div>
									<div class="newbottom">
										<div id="newspagination" class="swiper-pagination"></div>
									</div>
								</div>
							</div>
						</div>
					</div>
					<div class="itemright">
						<img src="img/map.jpg" />
						<a href="footprint.html" class="itemrightb">
							进入总书记的足迹>>
						</a>
					</div>
				</div>
			</div>
		</div>
		<div class="contextview hsyxbox">
			<div class="item hsyx">
				<div class="itemtop">
					<label class="title titleactive">VR红色游学</label>
					<a href="vrtour.html" class="more">更多>></a>
				</div>
				<div class="hsyxitembox" id="hsyxbox">

				</div>
			</div>
		</div>
		<div class="contextview zxxx">
			<div class="zxxxleft">
				<img class="topimghssj" src="img/hssjtopimg.png" />
				<div class="titlehssj">
					<img class="hssjico" src="img/hssjico.png" />
					<div>在</div>
					<div>线</div>
					<div>学</div>
					<div>习</div>
				</div>
				<img class="bottomimghssj" src="img/hssjbottomimg.png" />
			</div>
			<div class="zxxxright">
				<div class="zxxxrighttop">
					<div class="hssjboxs" id="hssjbox">

					</div>
					<a href="onlinelearning2.html" class="hssjmore">
						<div class="moress">
							<img src="img/morej.png" />
							<div>更</div>
							<div>多</div>
							<div>红</div>
							<div>色</div>
							<div>书</div>
							<div>籍</div>
						</div>
					</a>
				</div>
				<div class="jxzyboxs">
					<div class="jxzyboxsleft">
						<div class="scrollsss" id="zxxxbox">

						</div>
					</div>
					<a href="onlinelearning2.html" class="jxzyboxsmore">
						<div>
							<img src="img/moreb.png" />
							<div>更</div>
							<div>多</div>
							<div>教</div>
							<div>学</div>
							<div>资</div>
							<div>源</div>
						</div>
					</a>
				</div>
			</div>
		</div>
		<div class="contextview ysbwgview">
			<div class="item ysbwg">
				<div class="itemtop">
					<label class="title titleactive">教学成果</label>
					<a href="onlinelearning3.html?type=00" class="more">更多>></a>
				</div>
				<div class="ysbwgbox">
					<div class="sjbox" id="cgbox">


					</div>
				</div>
			</div>
		</div>
		<div class="contextview xssq">
			<div class="xssqleft">
				<div class="xssqlefttop">
					<div class="border"></div>
					<div class="xssqtitle"><img src="img/xf.png" />虚仿实验空间</div>
					<div class="bbbbbbbbbbbbbbbbbbbbb">
						<a href="experiment.html" class="bbbtn">
							点击进入
						</a>
					</div>
				</div>
				<a href="museum.html" class="xssqleftbottom">
					<div class="xfkj">
						<label><img src="img/jng.png" />医史博物馆</label>
						<img src="img/jtra.png" />
					</div>
				</a>
			</div>
			<div class="xssqright">
				<div class="item">
					<div class="itemtop">
						<label class="title titleactive">心声社区</label>
						<a href="community.html" class="more">更多>></a>
					</div>
				</div>
				<div class="xssqrightcenter">
					<div class="xssqrightleft">
						<div class="box">
							<div class="titles">
								<label class="activetitle">热门话题</label>
								<div class="jtdiv">
									<span class="leftimg" onclick="xsrmsyy()">
									</span>
									<span class="rightimg" onclick="xsrmxyy()">
									</span>
								</div>
							</div>
							<div class="itembox" id="rmht">


							</div>
						</div>
					</div>
					<div class="xssqrightright">
						<div class="box">
							<div class="titles">
								<label class="activetitle">最新话题</label>
								<div class="jtdiv">
									<span class="leftimg" onclick="xszxsyy()">
									</span>
									<span class="rightimg" onclick="xszxxyy()">
									</span>
								</div>
							</div>
							<div class="itembox" id="zxht">



							</div>
						</div>
					</div>
				</div>
			</div>
		</div>

		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">

					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<a href="https://yuanbao.tencent.com/" target="_blank" class="yuanbao-float">
			<img src="img/yuanbao512.png" alt="腾讯元宝" title="点击访问腾讯元宝" />
			腾讯元宝
		</a>
		<div class="iframe-container" id="iframe-container">
			<iframe src="" id="iframe"></iframe>
			<div id="closes">返回列表</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8">
		<script>
		</script>
		<script>
			// 添加统一的AJAX错误处理函数
			function handleAjaxError(jqXHR, textStatus, errorThrown, functionName) {
				console.error(`${functionName} 请求失败:`, textStatus, errorThrown);
				console.error(`状态码: ${jqXHR.status}`);
				console.error(`响应内容:`, jqXHR.responseText);
			}
			
			// 添加一个小延时确保login-mock.js有足够时间初始化
			setTimeout(function() {
				// 重新获取登录状态
				let userinfo = sessionStorage.getItem("userinfo")
				// console.log("当前登录状态:", userinfo ? "已登录" : "未登录")
				// console.log("当前baseurl:", baseurl);
				
				$(function() {
					$(".ssview").on('click', function() {
						window.location.href = 'searchall.html'
					})

					// 调用所有数据加载函数并添加错误处理
					try {
						getclass();
					} catch(e) {
						console.error("getclass 执行错误:", e);
					}
					
					try {
						getxuexilist();
					} catch(e) {
						console.error("getxuexilist 执行错误:", e);
					}
					
					try {
						getchengguolist();
					} catch(e) {
						console.error("getchengguolist 执行错误:", e);
					}
					
					try {
						getnews();
					} catch(e) {
						console.error("getnews 执行错误:", e);
					}
					
					try {
						getfooterlink();
					} catch(e) {
						console.error("getfooterlink 执行错误:", e);
					}
					
					try {
						getbanner();
					} catch(e) {
						console.error("getbanner 执行错误:", e);
					}
					
					try {
						getnewslist();
					} catch(e) {
						console.error("getnewslist 执行错误:", e);
					}
					
					try {
						getxssqclassid();
					} catch(e) {
						console.error("getxssqclassid 执行错误:", e);
					}
					
					if (userinfo) {
						//已登录 则显示用户信息
						$("#login").hide()
						$("#user").show()
						$("#user").html(JSON.parse(userinfo).name)
						$("#edit").show()
						if (window.localStorage.getItem("jilu")) {
							$.ajax({
								url: baseurl + "/study/record/add",
								type: 'post',
								contentType: "application/json",
								headers: {
									"Authorization": sessionStorage.getItem("header")
								},
								data: window.localStorage.getItem("jilu"),
								dataType: 'json',
								success: (res) => {
									window.localStorage.clear()
								}
							})
						}
					} else {
						//未登录 则显示登录按钮
						$("#login").show()
						$("#user").hide()
						$("#edit").hide()
					}

					// 绑定返回列表按钮点击事件
					$("#closes").on('click', function() {
						closeurl();
					});
				})
			})

			function closeurl() {
				$("#iframe-container, #iframe, #closes").fadeOut(300, function() {
					$("#iframe").attr("src", "");
					// 移除事件监听
					$("#iframe").off('load error');
				});
			}
			let newsclassid = null
			let newsggclassid = null
			let newsclassdata = null
			let xsrmindex = 1
			let xszxindex = 1
			let xsclassid = null
			let xsrmpages = 0
			let xszxpages = 0

			function clicknum(id) {
				$.ajax({
					url: baseurl + "/posts/click/" + id,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						// console.log("点击计数成功:", id);
					},
					error: function(jqXHR, textStatus, errorThrown) {
						handleAjaxError(jqXHR, textStatus, errorThrown, "clicknum");
					}
				})
			}

			function showurl(item) {
				clicknum($(item).attr("data-id"));
				if (userinfo) {
					let url = $(item).attr("data-url");
					$("#iframe").css('opacity', 0)
						.attr("src", url);
					$("#iframe-container, #iframe, #closes").fadeIn(300);
					$("#iframe").animate({opacity: 1}, 300);
					
					// 添加错误处理
					$("#iframe").on('load', function() {
						$(this).animate({opacity: 1}, 300);
					}).on('error', function() {
						closeurl();
						cocoMessage.error(2000, "页面加载失败，请稍后重试");
					});
				} else {
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html';
				}
			}

			function xsrmxyy() { //心声热门下一页
				if (xsrmindex < xsrmpages) {
					xsrmindex += 1
					getlist3()
				}
			}

			function xsrmsyy() { //心声热门上一页
				if (1 < xsrmindex) {
					xsrmindex -= 1
					getlist3()
				}
			}

			function xszxxyy() { //心声最新下一页
				if (xszxindex < xszxpages) {
					xszxindex += 1
					getlist2()
				}
			}

			function xszxsyy() { //心声最新上一页
				if (1 < xszxindex) {
					xszxindex -= 1
					getlist2()
				}
			}

			function getxssqclassid() {
				// console.log("开始获取心声社区分类ID");
				$.ajax({
					url: baseurl + "/web/category/user",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						// console.log("获取心声社区分类ID成功:", res);
						if (res.code == '200') {
							xsclassid = res.data[0].id
							getlist2()
							getlist3()
						} else {
							console.warn("获取心声社区分类ID接口返回非200状态:", res);
						}
					},
					error: function(jqXHR, textStatus, errorThrown) {
						handleAjaxError(jqXHR, textStatus, errorThrown, "getxssqclassid");
					}
				})
			}

			function getlist2() { //最新话题
				// console.log("开始获取最新话题");
				let params = "?pageNum=" + xszxindex + "&pageSize=5&id=" + xsclassid + "&sort=dispaly_time&title="
				$.ajax({
					url: baseurl + "/web/xspostByCategoryId" + params,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						// console.log("获取最新话题成功:", res);
						if (res.code == '200') {
							let html = ""
							res.data.list.map((item) => {
								html += '<div onclick="inxsinfo(this)" data-id="' + item.id +
									'" class="riitem"><div class="txt">' + item.title +
									'</div><div class="date">' + setDate2(item.createdAt) + '</div></div>'
							})
							$("#zxht").html(html)
							xszxpages = res.data.pages
						} else {
							console.warn("获取最新话题接口返回非200状态:", res);
						}
					},
					error: function(jqXHR, textStatus, errorThrown) {
						handleAjaxError(jqXHR, textStatus, errorThrown, "getlist2");
					}
				})
			}

			function getlist3() { //最热话题
				// console.log("开始获取最热话题");
				let params = "?pageNum=" + xsrmindex + "&pageSize=5&id=" + xsclassid + "&sort=click_count&title="
				$.ajax({
					url: baseurl + "/web/xspostByCategoryId" + params,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						// console.log("获取最热话题成功:", res);
						if (res.code == '200') {
							let html = ""
							res.data.list.map((item) => {
								html += '<div onclick="inxsinfo(this)" data-id="' + item.id +
									'" class="riitem"><div class="huo"><img src="img/huo.png" />' + item
									.clickCount + '</div><div class="txt">' + item.title +
									'</div><div class="date">' + setDate2(item.createdAt) + '</div></div>'
							})
							$("#rmht").html(html)
							xsrmpages = res.data.pages
						} else {
							console.warn("获取最热话题接口返回非200状态:", res);
						}
					},
					error: function(jqXHR, textStatus, errorThrown) {
						handleAjaxError(jqXHR, textStatus, errorThrown, "getlist3");
					}
				})
			}

			function inxsinfo(item) {
				window.location.href = "communitydetail.html?id=" + $(item).attr("data-id")
			}

			function setDate2(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d
			}

			function getnewgglist() {
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: 1,
						pageSize: 16,
						categoryId: newsggclassid
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data.list)
							let html = '<div class="swiper-slide">'
							for (let i = 1; i <= res.data.list.length; i++) {
								if (i % 4 == 0) {
									html += '<div class="xwitem" onclick="innewscontent(this)" data-id="' + res.data
										.list[i - 1].id + '"><div class="date"><label>' + getyear(res.data.list[i -
											1].eventTime) + '</label><label>' + getmon(res.data.list[i - 1]
											.eventTime) + '</label></div>' +
										'<div class="datatxt">' + res.data.list[i - 1].title + '</div></div>'
									html += '</div>'
									if (i != 16) {
										html += '<div class="swiper-slide">'
									}
								} else {
									html += '<div class="xwitem" onclick="innewscontent(this)" data-id="' + res.data
										.list[i - 1].id + '"><div class="date"><label>' + getyear(res.data.list[i -
											1].eventTime) + '</label><label>' + getmon(res.data.list[i - 1]
											.eventTime) + '</label></div>' +
										'<div class="datatxt">' + res.data.list[i - 1].title + '</div></div>'
								}
							}
							$("#newsgglist").html(html)
							var news = new Swiper('#news', {
								autoplay: true,
								loop: true,
								pagination: {
									el: '#newspagination',
									clickable: true
								}
							})
							news.el.onmouseover = function() {
								news.autoplay.stop();
							}
							news.el.onmouseout = function() {
								news.autoplay.start();
							}
						}
					}
				})
			}

			function getnewslist() {
				// console.log("开始获取新闻列表分类");
				$.ajax({
					url: baseurl + "/web/category/news",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header") || ''
					},
					dataType: 'json',
					success: (res) => {
						// console.log("获取新闻列表分类成功:", res);
						if (res.code == '200') {
							newsclassdata = res.data[0].children
							newsclassid = res.data[0].children[0].id
							newsggclassid = res.data[0].children[2].id
							let html = ''
							res.data[0].children.map((item, index) => {
								if (index < 2) {
									if (newsclassid == item.id) {
										html += '<a class="title titleactive">' + item.name + '</a>'
									} else {
										html +=
											'<a class="title" onclick="selectnewsclass(this)" data-id="' +
											item.id + '">' + item.name + '</a>'
									}
								}
							})
							$("#newsbarlist").html(html)
							getnewslistinfo()
							getnewgglist()
						} else {
							console.warn("获取新闻列表分类接口返回非200状态:", res);
						}
					},
					error: function(jqXHR, textStatus, errorThrown) {
						handleAjaxError(jqXHR, textStatus, errorThrown, "getnewslist");
					}
				})
			}

			function selectnewsclass(item) {
				newsclassid = $(item).attr("data-id")
				let html = ""
				newsclassdata.map((item, index) => {
					if (index < 2) {
						if (newsclassid == item.id) {
							html += '<a class="title titleactive">' + item.name + '</a>'
						} else {
							html += '<a class="title" onclick="selectnewsclass(this)" data-id="' + item.id + '">' + item
								.name + '</a>'
						}
					}
				})
				$("#newsbarlist").html(html)
				getnewslistinfo()
			}

			function getnewslistinfo() {
				// console.log("开始获取新闻列表内容");
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header") || ''
					},
					data: {
						pageNum: 1,
						pageSize: 5,
						categoryId: newsclassid
					},
					dataType: 'json',
					success: (res) => {
						// console.log("获取新闻列表内容成功:", res);
						if (res.code == '200') {
							// console.log(res.data.list)
							let html = ""
							res.data.list.map((item) => {
								html +=
									'<div class="itemtopboxitem" onclick="innewscontent(this)" data-id="' +
									item.id + '"><i class="new">NEW</i>' +
									'<div class="txt">' + item.title +
									'</div><div class="time">' + setDate(item.eventTime) + '</div></div>'
							})
							$("#new1list").html(html)
						} else {
							console.warn("获取新闻列表内容接口返回非200状态:", res);
						}
					},
					error: function(jqXHR, textStatus, errorThrown) {
						handleAjaxError(jqXHR, textStatus, errorThrown, "getnewslistinfo");
					}
				})
			}

			function innewscontent(item) {
				window.location.href = "newscontent.html?id=" + $(item).attr("data-id") + "&classid=" + newsggclassid
			}

			function getbanner() {
				$.ajax({
					url: baseurl + "/sys/banners/Newitems/list",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ''
							res.data.list.map((item) => {
								html += '<div class="swiper-slide"><img src="' + baseurl + item.url +
									'" /></div>'
							})
							$("#banner").html(html)
							var mySwiper = new Swiper('#indexswiper', {
								autoplay: true,
								loop: true,
								pagination: {
									el: '#indexswiperpagination',
									clickable: true
								}
							})
							mySwiper.el.onmouseover = function() {
								mySwiper.autoplay.stop();
							}
							mySwiper.el.onmouseout = function() {
								mySwiper.autoplay.start();
							}
						}
					}
				})
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			function getnews() { //获取新闻
				$.ajax({
					url: baseurl + "/web/home",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data.homeContentList)
							let hsyxhtml = ""
							res.data.homeContentList.map((item) => {
								if (item.name == "VR红色游学") {
									item.pageList.list.map((item2) => {
										hsyxhtml += '<div class="item" onclick="showurl(this)" data-id="' + item2.id + 
											'" data-url="' + item2.redirectUrl + '">' +
											'<img src="' + baseurl + item2.thumbPath[0] + '" />' +
											'<div class="itemname">' + item2.title + '</div>' +
											'<div class="itemcode">' + item2.keyWords + '</div></div>'
									})
								}
							})
							$("#hsyxbox").html(hsyxhtml)
						}
					}
				})
			}

			function inconss(url) { //进入红色游学详情
				let urlstr = $(url).attr("data-url")
				checkLogin(urlstr)
			}

			function getchengguolist() { //教学成果
				$.ajax({
					url: baseurl + "/web/teachingResults",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let shtml = ''
							res.data.list.map((item) => {
								shtml += '<a onclick="showsss(this)" data-type="' + item.attachType +
									'" data-id="' + item.metaId + '" class="zyitem">' +
									'<div class="zyitemimgbox">' +
									'<label class="itemtype ' + item.attachType + '">' + item.attachType +
									'</label>'
								if (item.coverPath) {
									shtml += '<img class="fms" src="' + baseurl + item.coverPath[0] + '" />'
								} else {
									shtml += '<img class="fms" src="' + baseurl + '" />'
								}

								shtml += '<div class="zyicobox">' +
									'<label><img src="img/kjzz.png"/>' + item.author + '</label>' +
									'<label><img src="img/kjyj.png"/>' + item.view + '</label>' +
									'</div>' +
									'</div>' +
									'<div class="zytitle">' + item.title + '</div>' +
									'<div class="zyms">' + item.introduction + '</div></a>'
							})

							// 如果列表项少于10个，复制一份用于无缝滚动
							if (res.data.list.length < 10) {
								shtml = '<div class="scroll-wrapper scrolling">' + shtml + shtml + '</div>';
							} else {
								shtml = '<div class="scroll-wrapper">' + shtml + '</div>';
							}

							// 将内容包装在滚动容器中
							shtml = '<div class="scroll-container">' + shtml + '</div>';
							
							$("#cgbox").html(shtml);

							// 如果列表项少于10个，确保滚动动画平滑
							if (res.data.list.length < 10) {
								const wrapper = document.querySelector('.scroll-wrapper');
								wrapper.addEventListener('mouseenter', () => {
									wrapper.style.animationPlayState = 'paused';
								});
								wrapper.addEventListener('mouseleave', () => {
									wrapper.style.animationPlayState = 'running';
								});
							}
						}
					}
				})
			}

			function showsss(cg) {
				if ($(cg).attr("data-type") == "pdf") {
					$.ajax({
						url: baseurl + "/course/meta/" + $(cg).attr("data-id"),
						type: 'GET',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						dataType: 'json',
						success: (res) => {
							if (res.code == '200') {
								$("#pdfname").html(res.data.cmsResourcesCourseMetaList[0].attachName)
								$("#tcbox").show()
								$("#pdf").attr("href", baseurl + res.data.cmsResourcesCourseMetaList[0].attachPath)
								$('a.media').media()
							}
						}
					})
				} else {
					window.location.href = "onlinelearning4.html?id=" + $(cg).attr("data-id")
				}
			}

			function closetc() {
				$("#tcbox").hide()
			}

			function sjininfo(item) {
				window.location.href = "onlinelearning5.html?id=" + $(item).attr("data-id")
			}

			function getxuexilist() { //在线学习列表
				$.ajax({
					url: baseurl + "/web/learn",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let hshtml = ""
							let xxhtml = ""
							let sjnum = 0
							res.data.homeContentList[0].children.map((item) => {
								if (item.name == "红色书籍") {
									item.pageList.list.map((item) => {
										if (sjnum < 5) {
											sjnum += 1
											hshtml += '<div onclick="sjininfo(this)" data-id="' +
												item.id + '" class="hssjitem">' +
												'<img src="' + baseurl + item.thumbPath[0] + '" />' +
												'<div class="hssjtitle">' + item.title + '</div>' +
												'<div class="zz">作者：<label>' + item.author +
												'</label></div></div>'
										}
									})
								} else {
									item.pageCourceList.list.map((item2) => {
										xxhtml += '<div onclick="showsss(this)" data-type="' + item2
											.attachType + '" data-id="' + item2.metaId +
											'" class="jxzyitem">' +
											'<label class="llllllll ' + item2.attachType + '">' +
											item2.attachType + '</label><div class="nrnrnr3">' +
											'<div>' + item2.title + '</div>' +
											'<div>' + item2.introduction + '</div>' +
											'</div><div class="ririri">' +
											'<label><img src="img/zz.png" />' + item2.author +
											'</label>' +
											'<label><img src="img/kjpf.png" />' + parseFloat(item2
												.score).toFixed(1) + '</label></div></div>'
									})
								}
							})
							$("#hssjbox").html(hshtml)
							$("#zxxxbox").html(xxhtml)
						}
					}
				})
			}
			
			
			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d
			}

			function getyear(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y
			}

			function getmon(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return MM + '-' + d
			}

			function edit() {
				// 清除sessionStorage
				sessionStorage.clear()
				
				// 清除localStorage
				localStorage.clear()
				
				// 清除所有相关cookies
				const cookies = document.cookie.split(";");
				for (let i = 0; i < cookies.length; i++) {
					const cookie = cookies[i];
					const eqPos = cookie.indexOf("=");
					const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
					document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
				}
				
				// 更新UI状态
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
				
				// 短暂延时后跳转到单点登录页面
				setTimeout(function() {
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html';
				}, 1200); // 延时1.2秒，确保消息显示完毕
			}

		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
		<style>
			.hsyxitembox .item {
				transition: all 0.3s ease;
				cursor: pointer;
			}

			.hsyxitembox .item:hover {
				transform: translateY(-5px);
				box-shadow: 0 5px 15px rgba(0,0,0,0.1);
			}

			.hsyxitembox .item img {
				transition: all 0.3s ease;
			}

			.hsyxitembox .item:hover img {
				transform: scale(1.05);
			}

			.hsyxitembox .item .itemname {
				transition: all 0.3s ease;
			}

			.hsyxitembox .item:hover .itemname {
				color: #A65D57;
			}

			/* 教学成果列表悬浮效果 */
			.zyitem {
				transition: all 0.3s ease;
				cursor: pointer;
			}

			.zyitem:hover {
				transform: translateY(-5px);
				box-shadow: 0 5px 15px rgba(0,0,0,0.1);
			}

			.zyitem .fms {
				transition: all 0.3s ease;
			}

			.zyitem:hover .fms {
				transform: scale(1.05);
			}

			.zyitem .zytitle {
				transition: all 0.3s ease;
			}

			.zyitem:hover .zytitle {
				color: #A65D57;
			}

			/* iframe弹窗样式 */
			#iframe {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				z-index: 9998;
				border: none;
				display: none;
				background: #fff;
			}

			.iframe-container {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				z-index: 9997;
				background: rgba(0, 0, 0, 0.5);
				display: none;
			}

			/* 返回按钮样式 */
			#closes {
				position: fixed;
				top: 20px;
				right: 20px;
				z-index: 9999;
				background: #A65D57;
				color: white;
				padding: 10px 20px;
				border-radius: 5px;
				cursor: pointer;
				display: none;
				transition: all 0.3s ease;
				font-size: 14px;
				display: flex;
				align-items: center;
				gap: 5px;
			}

			#closes:before {
				content: "←";
				font-size: 16px;
			}

			#closes:hover {
				background: #8B4513;
				transform: translateY(-2px);
				box-shadow: 0 2px 8px rgba(0,0,0,0.2);
			}

			/* 添加滚动动画容器样式 */
			.scroll-container {
				width: 100%;
				overflow: hidden;
				position: relative;
			}

			.scroll-wrapper {
				display: flex;
				animation: none; /* 默认不开启动画 */
			}

			/* 当列表项少于10个时的滚动动画 */
			.scroll-wrapper.scrolling {
				animation: scrollLeft 20s linear infinite;
			}

			@keyframes scrollLeft {
				0% {
					transform: translateX(0);
				}
				100% {
					transform: translateX(-100%);
				}
			}

			/* 保持原有样式 */
			.zyitem {
				flex-shrink: 0;
				margin-right: 1.25rem;
				width: calc((100% - 3.75rem) / 4);
			}

			/* 确保动画流畅 */
			.scroll-wrapper.scrolling .zyitem:last-child {
				margin-right: 0;
			}
		</style>
	</body>
</html>
