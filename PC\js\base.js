﻿//const baseurl = 'http://*************:8081'  
// 动态检测环境，同时支持相对路径和绝对路径
const baseurl = (function() {
	// 生产环境使用相对路径
	const isProduction = window.location.hostname !== 'localhost' && !window.location.hostname.includes('127.0.0.1');
	
	if (isProduction) {
		// 这里使用相对路径
		return '/api';
	} else {
		// 开发环境使用完整URL
		return 'http://localhost:5500/api';
	}
})();

function getclass(url) {
	// console.log("getclass被调用，传入的url参数:", url);
	
	$.ajax({
		url: baseurl + "/web/category/all",
		type: 'GET',
		contentType: "application/json",
		headers: {
			"Authorization": sessionStorage.getItem("header")
		},
		dataType: 'json',
		success: (res) => {
			if (res.code == '200') {
				// 调试输出所有菜单名称
				// console.log("所有菜单项:", res.data.map(item => `${item.name} -> ${item.redirectUrl}`).join(", "));
				
				let newurl = null
				if(url){
					newurl = url
				}else if(url != '0'){
					newurl = window.location.href
				}else{
					newurl = '0'
				}
				
				// 添加调试输出
				// console.log("处理后的newurl:", newurl);
				
				let html = ""
				if(newurl == '0'){
					res.data.forEach((item)=>{
						html+=`<div class="menuitemaaa">
											<a class="${item.name == '首页' ? 'menuaaaa active' : 'menuaaaa'}" href="${item.redirectUrl == null ? '#' : item.redirectUrl}">${item.name}</a>`
						if(item.name == '党建学习'){
							html+=`<div class="itemcboxaaa">`
							item.children.forEach((item2)=>{
								html+=`<a class="itema2aaa" href="${item2.redirectUrl}">${item2.name}</a>`
							})
						
						html+=`</div>`
						}		
						html+=`</div>`
					})
				}else{
					// 移除文件扩展名进行比较
					const currentPageWithoutExt = (typeof newurl === 'string') ? 
						newurl.split('.html')[0].split('/').pop() : '';
					
					// 特殊处理：如果是首页，只激活首页菜单
					// 增强根路径检测
					const isIndexPage = currentPageWithoutExt === 'index' || 
						newurl.endsWith('/index.html') || 
						newurl.endsWith('/') ||
						currentPageWithoutExt === '' ||
						newurl === window.location.origin ||
						newurl === window.location.origin + '/' ||
						(newurl.includes(window.location.hostname) && (newurl.endsWith('/') || newurl.split('/').pop() === ''));
					
					// 特殊处理：党建学习相关页面
					const isDjxxPage = currentPageWithoutExt === 'confident' || 
						currentPageWithoutExt === 'spirit' ||
						newurl.includes('confident.html') ||
						newurl.includes('spirit.html');
					
					// console.log("当前页面(去掉扩展名):", currentPageWithoutExt);
					
					// 将页面分类为红色书籍页面、课程学习页面和其他页面
					const isRedBookPage = 
						currentPageWithoutExt === 'onlinelearning2' || 
						newurl === 'onlinelearning2.html' || 
						(currentPageWithoutExt === 'onlinelearning5' && window.location.search.includes('redbook'));
						
					const isCoursePage = 
						currentPageWithoutExt === 'onlinelearning3' || 
						newurl === 'onlinelearning3.html' ||
						currentPageWithoutExt === 'onlinelearning4' || 
						newurl === 'onlinelearning4.html' ||
						(currentPageWithoutExt === 'onlinelearning5' && !window.location.search.includes('redbook'));
					
					const isOnlineLearningMain = 
						currentPageWithoutExt === 'onlinelearning' || 
						newurl === 'onlinelearning.html';
					
					// 调试输出页面类型
					// console.log("页面类型:", {
					//	"首页": isIndexPage,
					//	"党建学习页面": isDjxxPage,
					//	"红色书籍页面": isRedBookPage, 
					//	"课程学习页面": isCoursePage,
					//	"在线学习主页": isOnlineLearningMain
					// });
					
					res.data.forEach((item)=>{
						// 默认不激活
						let isActive = false;
						
						// 为调试输出记录判断依据
						let activationReason = "未激活";
						
						// 特殊处理首页：只激活首页菜单
						if (isIndexPage) {
							if (item.name === '首页') {
								isActive = true;
								activationReason = "首页激活首页菜单";
							}
						}
						// 特殊处理党建学习页面：激活党建学习菜单
						else if (isDjxxPage) {
							if (item.name === '党建学习') {
								isActive = true;
								activationReason = "党建学习子页面激活党建学习菜单";
							}
						}
						// 如果菜单项有redirectUrl
						else if (item.redirectUrl) {
							// 移除扩展名进行比较
							const menuPageWithoutExt = item.redirectUrl.split('.html')[0].split('/').pop();
							
							// 根据页面类型和菜单名称/URL判断是否激活
							if (isRedBookPage) {
								// 在红色书籍页面上，激活"红色书籍"菜单
								if (
									item.name === '红色书籍' || 
									(item.redirectUrl && item.redirectUrl.includes('onlinelearning2'))
								) {
									isActive = true;
									activationReason = "红色书籍页面激活红色书籍菜单";
								}
							} 
							else if (isCoursePage) {
								// 在课程学习页面上，激活"课程学习"菜单
								if (
									item.name === '课程学习' || 
									item.name === '在线学习' ||
									(item.redirectUrl && item.redirectUrl.includes('onlinelearning3'))
								) {
									isActive = true;
									activationReason = "课程学习页面激活课程学习菜单";
								}
							}
							else if (isOnlineLearningMain) {
								// 在在线学习主页上，激活"在线学习"菜单
								if (
									item.name === '在线学习' || 
									menuPageWithoutExt === 'onlinelearning'
								) {
									isActive = true;
									activationReason = "在线学习主页激活在线学习菜单";
								}
							}
							// 其他页面正常判断
							else {
								// 直接比较无扩展名的页面名
								if (currentPageWithoutExt === menuPageWithoutExt) {
									isActive = true;
									activationReason = "当前页面与菜单redirectUrl匹配";
								}
							}
						}
						
						// 输出调试信息
						// console.log(`菜单项: ${item.name}, redirectUrl: ${item.redirectUrl}, isActive: ${isActive}, 原因: ${activationReason}`);
						
						html+=`<div class="menuitemaaa">
											<a class="${isActive ? 'menuaaaa active' : 'menuaaaa'}" href="${item.redirectUrl == null ? '#' : item.redirectUrl}">${item.name}</a>`
						if(item.name == '党建学习'){
							html+=`<div class="itemcboxaaa">`
							item.children.forEach((item2)=>{
								html+=`<a class="itema2aaa" href="${item2.redirectUrl}">${item2.name}</a>`
							})
						
						html+=`</div>`
						}		
						html+=`</div>`
					})
				}
				
				
				$(".itembox").html(html)
			}
		}
	})
}

function getUrlParam(name) {
	var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
	var r = window.location.search.substr(1).match(reg); //匹配目标参数
	if (r != null) return unescape(r[2]);
	return null; //返回参数值
}

function edit() {
	sessionStorage.clear()
	$("#login").show()
	$("#user").hide()
	$("#edit").hide()
	cocoMessage.error(1000, "已退出登录！")
}