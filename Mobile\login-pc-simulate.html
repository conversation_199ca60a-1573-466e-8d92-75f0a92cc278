<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>登录 - 思政一体化平台</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .login-container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
            overflow: hidden;
        }
        
        .login-header {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
        }
        
        .login-title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .login-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .login-body {
            padding: 30px 20px;
        }
        
        .login-options {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }
        
        .login-btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px 20px;
            border: 2px solid #e0e0e0;
            border-radius: 8px;
            text-decoration: none;
            color: #333;
            font-size: 16px;
            font-weight: 500;
            transition: all 0.3s ease;
            background: white;
        }
        
        .login-btn:hover {
            border-color: #c00714;
            background: #f8f9fa;
        }
        
        .login-btn.primary {
            background: #c00714;
            color: white;
            border-color: #c00714;
        }
        
        .login-btn.primary:hover {
            background: #a00610;
            border-color: #a00610;
        }
        
        .login-btn-icon {
            margin-right: 10px;
            font-size: 18px;
        }
        
        .divider {
            text-align: center;
            margin: 20px 0;
            position: relative;
            color: #999;
            font-size: 14px;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e0e0e0;
            z-index: 1;
        }
        
        .divider span {
            background: white;
            padding: 0 15px;
            position: relative;
            z-index: 2;
        }
        
        .info-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            font-size: 14px;
            color: #666;
            line-height: 1.5;
        }
        
        .info-title {
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .status-indicator {
            display: inline-block;
            width: 8px;
            height: 8px;
            border-radius: 50%;
            margin-right: 8px;
        }
        
        .status-success {
            background: #28a745;
        }
        
        .status-warning {
            background: #ffc107;
        }
        
        .status-error {
            background: #dc3545;
        }
        
        .back-link {
            text-align: center;
            margin-top: 20px;
        }
        
        .back-link a {
            color: #c00714;
            text-decoration: none;
            font-size: 14px;
        }
        
        .back-link a:hover {
            text-decoration: underline;
        }
    </style>
</head>
<body>
    <div class="login-container">
        <div class="login-header">
            <div class="logo">思</div>
            <div class="login-title">思政一体化平台</div>
            <div class="login-subtitle">统一身份认证登录</div>
        </div>
        
        <div class="login-body">
            <div class="login-options">
                <!-- PC端模拟登录 -->
                <a href="#" class="login-btn primary" id="pcLoginBtn">
                    <span class="login-btn-icon">🖥️</span>
                    使用PC端登录方式
                </a>
                
                <!-- 直接跳转到PC端 -->
                <a href="#" class="login-btn" id="redirectPcBtn">
                    <span class="login-btn-icon">🔗</span>
                    跳转到PC端登录
                </a>
                
                <!-- 测试登录 -->
                <a href="#" class="login-btn" id="testLoginBtn">
                    <span class="login-btn-icon">🧪</span>
                    测试登录功能
                </a>
            </div>
            
            <div class="divider">
                <span>登录状态</span>
            </div>
            
            <div class="info-box">
                <div class="info-title">
                    <span class="status-indicator status-warning"></span>
                    当前状态
                </div>
                <div id="loginStatus">检查登录状态中...</div>
            </div>
            
            <div class="info-box">
                <div class="info-title">说明</div>
                <div>
                    • PC端模拟：使用PC端的service URL进行登录<br>
                    • 跳转PC端：直接访问PC端登录页面<br>
                    • 测试功能：检查登录接口和状态
                </div>
            </div>
            
            <div class="back-link">
                <a href="index.html">← 返回首页</a>
            </div>
        </div>
    </div>

    <script>
        // 配置
        const CONFIG = {
            pcBaseUrl: 'https://szjx.sntcm.edu.cn',
            mobileBaseUrl: window.location.origin + window.location.pathname.replace('login-pc-simulate.html', ''),
            casLoginUrl: 'https://cas.sntcm.edu.cn/lyuapServer/login'
        };

        // 工具函数
        const Utils = {
            // 更新登录状态显示
            updateLoginStatus() {
                const userinfo = sessionStorage.getItem('userinfo');
                const header = sessionStorage.getItem('header');
                const statusDiv = document.getElementById('loginStatus');
                const indicator = document.querySelector('.status-indicator');
                
                if (userinfo && header) {
                    try {
                        const user = JSON.parse(userinfo);
                        statusDiv.innerHTML = `
                            已登录<br>
                            用户：${user.name || user.realName || '未知'}<br>
                            <small>Token: ${header.substring(0, 20)}...</small>
                        `;
                        indicator.className = 'status-indicator status-success';
                    } catch (e) {
                        statusDiv.innerHTML = '登录数据格式错误';
                        indicator.className = 'status-indicator status-error';
                    }
                } else {
                    statusDiv.innerHTML = '未登录';
                    indicator.className = 'status-indicator status-warning';
                }
            },

            // 显示消息
            showMessage(message, type = 'info') {
                const statusDiv = document.getElementById('loginStatus');
                const indicator = document.querySelector('.status-indicator');
                
                statusDiv.innerHTML = message;
                indicator.className = `status-indicator status-${type}`;
            },

            // 获取PC端登录URL
            getPcLoginUrl() {
                // 使用PC端的userinfo.html作为回调，但实际会跳转到我们的移动端回调页面
                const serviceUrl = CONFIG.pcBaseUrl + '/userinfo.html';
                return `${CONFIG.casLoginUrl}?service=${encodeURIComponent(serviceUrl)}`;
            },

            // 获取移动端回调URL
            getMobileCallbackUrl() {
                return CONFIG.mobileBaseUrl + 'pc-login-callback.html';
            },

            // 获取PC端直接访问URL
            getPcDirectUrl() {
                return CONFIG.pcBaseUrl + '/index.html';
            }
        };

        // 登录处理类
        class LoginHandler {
            constructor() {
                this.init();
            }

            init() {
                // 更新登录状态
                Utils.updateLoginStatus();

                // 绑定事件
                this.bindEvents();

                // 检查是否从CAS返回
                this.checkCasReturn();
            }

            bindEvents() {
                // PC端模拟登录
                document.getElementById('pcLoginBtn').addEventListener('click', (e) => {
                    e.preventDefault();
                    this.handlePcSimulateLogin();
                });

                // 跳转到PC端
                document.getElementById('redirectPcBtn').addEventListener('click', (e) => {
                    e.preventDefault();
                    this.handleRedirectToPc();
                });

                // 测试登录
                document.getElementById('testLoginBtn').addEventListener('click', (e) => {
                    e.preventDefault();
                    this.handleTestLogin();
                });
            }

            // PC端模拟登录
            handlePcSimulateLogin() {
                Utils.showMessage('正在跳转到统一认证平台...', 'warning');
                
                // 使用PC端的service URL
                const pcLoginUrl = Utils.getPcLoginUrl();
                
                // 记录登录方式
                sessionStorage.setItem('loginMethod', 'pc-simulate');
                
                // 跳转到CAS登录
                window.location.href = pcLoginUrl;
            }

            // 跳转到PC端
            handleRedirectToPc() {
                Utils.showMessage('正在跳转到PC端...', 'warning');
                
                // 直接跳转到PC端
                window.location.href = Utils.getPcDirectUrl();
            }

            // 测试登录
            handleTestLogin() {
                Utils.showMessage('正在测试登录接口...', 'warning');
                
                // 跳转到测试页面
                window.location.href = 'login-test.html';
            }

            // 检查是否从CAS返回
            checkCasReturn() {
                const urlParams = new URLSearchParams(window.location.search);
                const ticket = urlParams.get('ticket');
                
                if (ticket) {
                    Utils.showMessage('检测到登录票据，正在处理...', 'warning');
                    this.handleCasTicket(ticket);
                }
            }

            // 处理CAS票据
            async handleCasTicket(ticket) {
                try {
                    const serviceUrl = window.location.href.split('?')[0];
                    
                    const response = await fetch(CONFIG.pcBaseUrl + '/student/caslogin', {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json'
                        },
                        body: JSON.stringify({
                            ticket: ticket,
                            service: serviceUrl
                        })
                    });

                    const result = await response.json();

                    if (result.code === '200' && result.data) {
                        // 保存登录信息
                        sessionStorage.setItem('header', result.data.scheme + result.data.token);
                        sessionStorage.setItem('userinfo', JSON.stringify(result.data.student));
                        
                        Utils.showMessage('登录成功！正在跳转...', 'success');
                        
                        setTimeout(() => {
                            window.location.href = 'index.html';
                        }, 2000);
                    } else {
                        Utils.showMessage('登录失败：' + (result.message || '未知错误'), 'error');
                    }
                } catch (error) {
                    console.error('处理登录票据失败:', error);
                    Utils.showMessage('登录处理失败，请稍后重试', 'error');
                }
            }
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            new LoginHandler();
        });

        // 监听存储变化
        window.addEventListener('storage', function(e) {
            if (e.key === 'userinfo' || e.key === 'header') {
                Utils.updateLoginStatus();
            }
        });
    </script>
</body>
</html>
