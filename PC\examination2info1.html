<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-思政考核系统</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/examination2info.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body class="index">
		<div class="topview2">
			试卷解析
		</div>
		<div class="content">
			<div class="contentleft">
				<div class="sjtitle">
					<img src="img/sjtitle.png" />
					<div class="sbag">
						<div class="stitle" id="papername"></div>
						<div class="sbottom">
							<label>学科: <span id="paperxk"></span></label>
							<label>考试时间: <span id="papertime"></span></label>
							<label>总分: <span id="papersc"></span></label>
							<label>及格线: <span id="papersc2"></span></label>
						</div>
					</div>
				</div>
				<div class="title2" id="sjname">
				</div>
				<div class="scrollview1">
					<div id="leftbox">
						<div class="item">
							<div class="itemleft" id="showth"></div>
							<div class="itemright">
								<div class="tm" id="showtm"></div>
								<div class="dabox" id="showxx">
									<!-- <div class="da"><span></span>A.法国里昂工人起义</div> -->
								</div>
							</div>
						</div>
						<div class="jiexi">
							<div class="jxtop" id="tops">
								<div class="trueda">正确答案 <label id="zqda"></label></div>
								<div class="ksda">考生答案 <label id="ksda"></label></div>
							</div>
							<div class="jxtext" id="jxstr">
								

							</div>
						</div>
					</div>

					<div class="xxtx">
						<div onclick="syt()">上一题</div>
						<div onclick="xyt()">下一题</div>
					</div>
				</div>
			</div>
			<div class="contentright">
				<div class="dtktitle">
					<img src="img/dtktitle.png" />
					<div>
						得分详情
					</div>
				</div>
				<div class="dtkbox">
					<div>客观题得分: <label id="zhuguan"></label></div>
					<div>主观题得分: <label id="keguan"></label></div>
					<div>总分: <label id="zfen"></label></div>
				</div>
				<div id="rightbox">
					<div class="titletype" id="t1" onclick="showright(1)">单选题<span></span></div>
					<div class="tmbox" id="box1">

					</div>
					<div class="titletype" id="t2" onclick="showright(2)">多选题<span></span></div>
					<div class="tmbox" id="box2">
					</div>
					<div class="titletype" id="t3" onclick="showright(3)">判断题<span></span></div>
					<div class="tmbox" id="box3">
					</div>
					<div class="titletype" id="t4" onclick="showright(4)">简答题<span></span></div>
					<div class="tmbox" id="box4">
					</div>
					<div class="titletype" id="t5" onclick="showright(5)">论述题<span></span></div>
					<div class="tmbox" id="box5">
					</div>
					<div class="titletype" id="t6" onclick="showright(6)">材料分析题<span></span></div>
					<div class="tmbox" id="box6">
					</div>
				</div>
				<div class="ksbtnbox">
					<div class="tjda" onclick="backsss()">返回试题列表</div>
				</div>
			</div>
		</div>
		<div class="footer">
			<a target="_blank" style="color:#FFFFFF; font-size: 0.833333rem;display: flex;align-items: center;" href="https://beian.miit.gov.cn/#/Integrated/index" class="ba">陕ICP备05001612号-1<img src="img/ba.png"/>陕公网安备 61040202000395号</a>
		</div>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					let userdata = JSON.parse(userinfo)
					$("#username").html(userdata.name)
					$("#userno").html(userdata.userAuth.identifier)
					getsjinfo()
				}
			})
			
			function backsss(){
				history.back()
			}
			let thisdata = null //当前显示的 题目
			let alldata = null //所有题目

			let indextm = 1 //题号
			let datanum = 0//当前题目集合的长度
			let thismydata = null //当前题目集合


			let data1 = [] //单选
			let data2 = [] //多选
			let data3 = [] //判断
			let data4 = [] //简答
			let data5 = [] //论述
			let data6 = [] //材料分析


			function getsjinfo() {
				let id = getUrlParam('id')
				$.ajax({
					url: baseurl + "/paper/analysis/" + id,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							alldata = res.data.cmsTestPaper.cmsSubjectList
							let data = res.data.cmsTestPaper
							data.cmsSubjectList.map((item) => {
								if (item.type == '0') {
									data1.push(item)
								} else if (item.type == '1') {
									data2.push(item)
								} else if (item.type == '2') {
									data3.push(item)
								} else if (item.type == '3') {
									data4.push(item)
								} else if (item.type == '4') {
									data5.push(item)
								} else if (item.type == '5') {
									data6.push(item)
								}
							})
							$("#zfen").html(parseInt(res.data.score)+'分')
							$("#keguan").html(parseInt(res.data.keguanTotalScore)+'分')
							$("#zhuguan").html(parseInt(res.data.zhuguanScore)+'分')
							
							$("#papername").html(data.name)
							$("#paperxk").html(res.data.subjectName)
							$("#papertime").html(data.limitedTime)
							$("#papersc").html(data.score)
							$("#papersc2").html(data.passedScore)
						
							showright(1)
						}
					}
				})
			}

			function showright(num) { //显示右侧内容
				indextm = 0
				$("#box1").attr("style", "display: none;")
				$("#box2").attr("style", "display: none;")
				$("#box3").attr("style", "display: none;")
				$("#box4").attr("style", "display: none;")
				$("#box5").attr("style", "display: none;")
				$("#box6").attr("style", "display: none;")
				$("#box" + num).attr("style", "display: flex;")
				$("#t1").attr("class", "titletype")
				$("#t2").attr("class", "titletype")
				$("#t3").attr("class", "titletype")
				$("#t4").attr("class", "titletype")
				$("#t5").attr("class", "titletype")
				$("#t6").attr("class", "titletype")
				$("#t" + num).attr("class", "titletype titletypeactive")
				if (num == '1') thisdata = data1[0]
				else if (num == '2') thisdata = data2[0]
				else if (num == '3') thisdata = data3[0]
				else if (num == '4') thisdata = data4[0]
				else if (num == '5') thisdata = data5[0]
				else if (num == '6') thisdata = data6[0]


				showsss()

				//根据num显示右侧数据
				let showdata = null
				if (num == "1") {
					showdata = data1
					$("#sjname").html('单选题(共' + data1.length + '题)')
				} else if (num == "2") {
					showdata = data2
					$("#sjname").html('多选题(共' + data2.length + '题)')
				} else if (num == "3") {
					showdata = data3
					$("#sjname").html('判断题(共' + data3.length + '题)')
				} else if (num == "4") {
					showdata = data4
					$("#sjname").html('简答题(共' + data4.length + '题)')
				} else if (num == "5") {
					showdata = data5
					$("#sjname").html('论述题(共' + data5.length + '题)')
				} else if (num == "6") {
					showdata = data6
					$("#sjname").html('材料分析题(共' + data6.length + '题)')
				}
				datanum = showdata.length
				thismydata = showdata
				let showhtml = ""
				showdata.map((item, index) => {
					if (item.listRecord[0].results == '正确') {
						showhtml +=
							'<a class="zhengque" onclick="showsssssssss(this)" data-num="'+(index)+'" data-id="'+item.id+'"><label>' +
							(index + 1) + '</label></a>'
					} else {
						showhtml += '<a class="cuowu" onclick="showsssssssss(this)" data-num="'+(index)+'" data-id="'+item.id+'"><label>' +
							(index +
								1) + '</label></a>'
					}
				})
				$("#box" + num).html(showhtml)
			}
			function showsssssssss(item){
				let id = $(item).attr("data-id")
				indextm = $(item).attr("data-num")
				alldata.map((item)=>{
					if(id === item.id){
						thisdata = item
					}
				})
				showsss()
			}
			function showsss() {
				// console.log(thisdata)
				if(!thisdata){
					return
				}
				$("#showth").html('【第' + (parseInt(indextm)+1) + '题】')
				$("#showtm").html(thisdata.name+'（'+thisdata.score+'分）')
				if(thisdata.type == '0'){
					let tmtmtmttm = ''
					thisdata.cmsSubjectOption.map((item)=>{
						if(item.keyword == thisdata.listRecord[0].answered){
							tmtmtmttm+=`<div class="da daactive"><span></span>${item.keyword+'. '+item.name}</div>`
						}else{
							tmtmtmttm+=`<div class="da"><span></span>${item.keyword+'. '+item.name}</div>`
						}
					})
					$("#jxstr").html('')
					$("#showxx").html(tmtmtmttm)
					$("#zqda").html(thisdata.answer)
					if(thisdata.listRecord[0].results=='错误')
					$("#ksda").html(thisdata.listRecord[0].answered+'<img class="tmsss" src="img/tmfalse.png" />')
					else
					$("#ksda").html(thisdata.listRecord[0].answered+'<img class="tmsss" src="img/tmtrue.png" />')
					$("#tops").attr("style","display: flex")
				}else if(thisdata.type == '1'){
					//多选题
					let tmtmtmttm = ''
					thisdata.cmsSubjectOption.map((item)=>{
						if(thisdata.listRecord[0].answered.indexOf(item.keyword)!=-1){
							tmtmtmttm+=`<div class="da daactive"><span></span>${item.keyword+'. '+item.name}</div>`
						}else{
							tmtmtmttm+=`<div class="da"><span></span>${item.keyword+'. '+item.name}</div>`
						}
					})
					$("#jxstr").html('')
					$("#showxx").html(tmtmtmttm)
					let sssttrr = ''
					thisdata.answer.split(',').forEach((item)=>{
						if(item!=''){
							sssttrr+=item
						}
					})
					$("#zqda").html(sssttrr)
					let sssttrr2 = ''
					thisdata.listRecord[0].answered.split(',').forEach((item)=>{
						if(item!=''){
							sssttrr2+=item
						}
					})
					
					$("#ksda").html(sssttrr2)
					$("#tops").attr("style","display: flex")
				}else if(thisdata.type == '2'){
					//判断题
					// console.log(thisdata.answer,thisdata.listRecord[0].answered)
					let tmtmtmttm = ''
					if(thisdata.listRecord[0].answered == 'true'){
						tmtmtmttm = '<div class="da daactive"><span></span>正确</div><div class="da"><span></span>错误</div>'
					}else{
						tmtmtmttm = '<div class="da"><span></span>正确</div><div class="da daactive"><span></span>错误</div>'
					}
					$("#jxstr").html('')
					$("#showxx").html(tmtmtmttm)
					$("#zqda").html(thisdata.answer=='true'?'正确':'错误')
					$("#ksda").html(thisdata.listRecord[0].answered=='true'?'正确':'错误')
					$("#tops").attr("style","display: flex")
				}else if(thisdata.type == '3'){
					$("#showxx").html('')
					$("#jxstr").html('<div><label>考生答案</label>'+thisdata.listRecord[0].answered+'</div>')
					let jxs = thisdata.answer==null?'无':thisdata.answer
					$("#jxstr").append('<div style="margin-top: 15px;"><label>题目解析</label>'+jxs+'</div>')
					$("#tops").attr("style","display: none")
				}else if(thisdata.type == '4'){
					let jxs = thisdata.answer==null?'无':thisdata.answer
					$("#showxx").html('')
					$("#jxstr").html('<div><label>考生答案</label>'+thisdata.listRecord[0].answered+'</div>')
					$("#jxstr").append('<div style="margin-top: 15px;"><label>题目解析</label>'+jxs+'</div>')
					$("#tops").attr("style","display: none")
				}else if(thisdata.type == '5'){
					let jxs = thisdata.answer==null?'无':thisdata.answer
					$("#showxx").html('')
					$("#jxstr").html('<div><label>考生答案</label>'+thisdata.listRecord[0].answered+'</div>')
					$("#jxstr").append('<div style="margin-top: 15px;"><label>题目解析</label>'+jxs+'</div>')
					$("#tops").attr("style","display: none")
				}
				
			}
			function syt(){
				if(0<indextm){
					
					indextm-=1
					// console.log(indextm,datanum)
					thisdata = thismydata[indextm]
					showsss()
				}
			}
			function xyt(){
				if(indextm<datanum-1){
					
					indextm+=1
					// console.log(indextm,datanum)
					thisdata = thismydata[indextm]
					showsss()
				}
			}
			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
