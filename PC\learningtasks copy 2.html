<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-学习任务管理</title>
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<style>
			#baogaotc {
				position: fixed;
				left: 0;
				right: 0;
				bottom: 0;
				top: 0;
				background: rgba(0, 0, 0, 0.7);
				z-index: 999;
				display: none;
				justify-content: center;
				align-items: center;
			}

			.rwselect {
				height: 1.25rem;
				font-size: 0.729166rem;
				color: #999999;
				border: none;
				outline-style: none;
				background: #f3f3f3;
				min-width: 6.25rem;
				margin-left: 0.364583rem;
				margin-right: 0.364583rem;
			}

			.selectrwbox {
				padding-bottom: 1.041666rem;
				display: flex;
				align-items: center;
				border-bottom: 0.052083rem solid #666666;
			}

			.selectrwbox input,
			.selectrwbox select {
				background: #f3f3f3;
				height: 1.25rem;
				color: #999999;
				border: none;
				outline-style: none;
				padding: 0px 0.260417rem;
				margin-right: 0.5rem;
				font-size: 0.72916rem;
			}

			.selectrwbox button {
				border: none;
				background: #c00714;
				color: #FFFFFF;
				height: 1.25rem;
				width: 3rem;
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 0.72916rem;
				margin-right: 0.5rem;
				border-radius: 0.260416rem;
			}
		</style>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a href="studentachievement.html" class="leftitem">学生成绩统计</a>
						<a href="studentlearning.html" class="leftitem">学习记录统计</a>
						<a href="userexamination2.html" class="leftitem">考试管理</a>
						<a class="leftitem activeleftitem">学习任务管理</a>
						<a href="achievements.html" class="leftitem">成果展示</a>
						<a href="releaseyourvoice2.html" class="leftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="userinfotopdiv">
						<a class="acccccg">任务列表</a>
						<a href="learningtasks2.html">发布任务</a>
					</div>
					<div class="paperscroll">
						<div class="selectrwbox">
							<input id="ssrwname" placeholder="请输入任务名称" />
							<select id="ssxkid">
								<option value="0">请选择学科</option>
							</select>
							<button onclick="ssrwlist()">查询</button>
							<button onclick="clearss()">清空</button>
						</div>
						<div class="xxrwlist" id="list">

						</div>
						<div class="fybox" id="fyq">
							<span id="sy">首页</span>
							<span id="syy">上一页</span>
							<div class="num" id="num">
							</div>
							<span id="xyy">下一页</span>
							<span id="wy">尾页</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>

		<div class="xxrwtc">
			<div class="tcview">
				<div class="rwtcbox">
					<div class="rwtitletc"><label id="tctitlerw"></label>
						<img onclick="closeyulan()" src="img/rwclose1.png" />
					</div>
					<div class="rwstr">
						<div class="rwstritem">
							<img src="img/rwtime1.png" />
							<label id="yldate"></label>
						</div>
						<div class="rwstritem">
							<img src="img/rwxk1.png" />
							<label id="ylxk"></label>
						</div>
						<!-- <div class="rwstritem">
							<img src="img/rwxy1.png"/>
							学院
						</div>
						<div class="rwstritem">
							<img src="img/rwzy1.png"/>
							专业
						</div>
						<div class="rwstritem">
							<img src="img/rwclass1.png"/>
							班级
						</div> -->
					</div>
					<div class="rwinfobox" id="rwlist">

					</div>
				</div>
			</div>
		</div>

		<div id="deletebox">
			<div class="deletesss">
				<div class="sjtitle2">
					<div>系统提示</div>
					<label class="paperyueclose" onclick="closedelte()"></label>
				</div>
				<div class="delstr">
					是否删除"<label id="delname"></label>"？
				</div>
				<div class="submitbox deldelpaper">
					<div class="bc" onclick="deletesubmit()">删除</div>
					<div class="gb" onclick="closedelte()">关闭</div>
				</div>
			</div>
		</div>

		<div id="baogaotc">
			<div class="deletesss">
				<div class="sjtitle2">
					<div>任务报告</div>
					<label class="paperyueclose" onclick="closerwbg()"></label>
				</div>
				<div class="delstr">
					<select id="bgxyselect" class="rwselect" onchange="bgxychange()">
					</select>
					<select id="bgzyselect" class="rwselect" onchange="bgzychange()">
						<option value="0">请选择专业</option>
					</select>
					<select id="bgbjselect" class="rwselect">
						<option value="0">请选择班级</option>
					</select>
				</div>
				<div style="font-size: 0.85rem;text-align: center;color: #999999;">获取报告需要一段时间,请您耐心等待~</div>
				<div class="submitbox deldelpaper">
					<div class="bc" onclick="getrwbg()" id="getbgbtn">获取报告</div>
					<div class="gb" onclick="closerwbg()">关闭</div>
				</div>
			</div>
		</div>

		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
		
			let taptaskid = null
			let bgxyid = null
			let bgzyid = null

			let bgxylist = []
			let bgzylist = []
			let bgbjlist = []

			function bgxychange() { //学院发生变化
				bgxyid = $("#bgxyselect").val()
				bgxylist.forEach((item) => {
					if (item.id == bgxyid) {
						bgzylist = item.children
						return
					}
				})
				let html = '<option value="0">请选择专业</option>'
				bgzylist.forEach((item) => {
					html += '<option value="' + item.id + '">' + item.name + '</option>'
				})
				$("#bgzyselect").html(html)

				$("#bgbjselect").html('<option value="0">请选择班级</option>')
			}

			function bgzychange() { //专业发生改变
				bgzyid = $("#bgzyselect").val()
				bgzylist.forEach((item) => {
					if (item.id == bgzyid) {
						bgbjlist = item.children
						return
					}
				})
				let html = '<option value="0">请选择班级</option>'
				bgbjlist.forEach((item) => {
					html += '<option value="' + item.id + '">' + item.name + '</option>'
				})
				$("#bgbjselect").html(html)
			}

			function getrwbg() { //获取任务报告
				let xyid = $("#bgxyselect").val()
				let zyid = $("#bgzyselect").val()
				let bjid = $("#bgbjselect").val()

				if (xyid == '0') {
					cocoMessage.error(1000, "请选择学院！")
				} else if (zyid == '0') {
					cocoMessage.error(1000, "请选择专业！")
				} else if (bjid == '0') {
					cocoMessage.error(1000, "请选择班级！")
				} else {
					$("#getbgbtn").html('正在获取...')
					axios({
						method: 'POST',
						url: baseurl + '/teacher/stat/pdf',
						data: {
							taskId: taptaskid,
							classId: bjid
						},
						responseType: 'blob', //响应类型
						xsrfHeaderName: 'Authorization',
						headers: {
							'Content-Type': 'application/json',
							"Authorization": sessionStorage.getItem("header")
						},
					}).then(response => {
						$("#getbgbtn").html('获取报告')
						let blob = new Blob([response.data], {
							type: 'application/pdf;charset=utf-8' //文件格式对应文件后缀xls（比如xlsx/dotx等）
						})
						let url = window.URL.createObjectURL(blob)
						window.open(url)
					}).catch(error => {
						$("#getbgbtn").html('获取报告')
					})
				}
			}

			function getbgclass() {
				// 此函数不再使用，已由getTaskClassData代替
				// $.ajax({
				// 	url: baseurl + "/binding/teacher-class",
				// 	type: 'GET',
				// 	contentType: "application/json",
				// 	headers: {
				// 		"Authorization": sessionStorage.getItem("header")
				// 	},
				// 	dataType: 'json',
				// 	success: (res) => {
				// 		if (res.code == '200') {
				// 			bgxylist = res.data
				// 			let html = '<option value="0">请选择学院</option>'
				// 			bgxylist.forEach((item) => {
				// 				html += '<option value="' + item.id + '">' + item.name + '</option>'
				// 			})
				// 			$("#bgxyselect").html(html)
				// 		}
				// 	}
				// })
			}

			function closerwbg() {
				$("#baogaotc").attr("style", "display: none;")
			}
			//开始时间和结束时间分开  两个input    修改的时候  组卷界面重写  记录原有数据
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()

				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getfooterlink()
				getrwlist()
				getxuekelist()
			})
			let deleteid = null

			function deletess(str) {
				deleteid = $(str).attr("data-id")
				$("#delname").html($(str).attr("data-str"))
				$("#deletebox").attr("style", "display: flex;")
			}

			function deletesubmit() {
				$.ajax({
					url: baseurl + "/learning-tasks/delete/" + deleteid,
					type: 'delete',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							cocoMessage.success(1000, "删除成功！")
							getrwlist()
						} else {
							cocoMessage.error(1000, "删除失败！")
						}
						closedelte()
					}
				})
			}

			function closedelte() {
				$("#deletebox").attr("style", "display: none;")
			}

			function baogaotc(item) {
				taptaskid = $(item).attr("data-id")
				$("#baogaotc").attr("style", "display: flex;")
				// 获取当前任务的班级权限数据
				getTaskClassData(taptaskid)
			}

			// 获取任务相关的班级数据
			function getTaskClassData(taskId) {
				// 先清空下拉框
				$("#bgxyselect").html('<option value="0">请选择学院</option>');
				$("#bgzyselect").html('<option value="0">请选择专业</option>');
				$("#bgbjselect").html('<option value="0">请选择班级</option>');
				
				// 使用weblist接口的数据，因为它可以正常工作
				$.ajax({
					url: baseurl + "/learning-tasks/weblist",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: 1,
						pageSize: 100,
						tasksName: "",
						sectionId: ""
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// 找到匹配的任务ID
							let taskData = null;
							for (let i = 0; i < res.data.list.length; i++) {
								if (res.data.list[i].id === taskId) {
									taskData = res.data.list[i];
									break;
								}
							}
							
							if (taskData && taskData.tasksAuthorityList && taskData.tasksAuthorityList.length > 0) {
								// 构建学院-专业-班级层级结构
								bgxylist = [];
								let collegeMap = new Map();
								
								// 遍历权限列表，构建基础结构
								taskData.tasksAuthorityList.forEach((item) => {
									if (!collegeMap.has(item.collegeId)) {
										collegeMap.set(item.collegeId, {
											id: item.collegeId,
											name: "", // 稍后填充
											children: new Map() // 使用Map存储专业
										});
									}
									
									let college = collegeMap.get(item.collegeId);
									if (!college.children.has(item.majorId)) {
										college.children.set(item.majorId, {
											id: item.majorId,
											name: "", // 稍后填充
											children: [] // 班级列表
										});
									}
									
									// 添加班级ID到专业下
									let major = college.children.get(item.majorId);
									if (!major.children.some(c => c.id === item.classId)) {
										major.children.push({
											id: item.classId,
											name: "" // 稍后填充
										});
									}
								});
								
								// 获取班级名称数据
								$.ajax({
									url: baseurl + "/binding/teacher-class",
									type: 'GET',
									contentType: "application/json",
									headers: {
										"Authorization": sessionStorage.getItem("header")
									},
									dataType: 'json',
									success: (classRes) => {
										if (classRes.code == '200') {
											let allClassData = classRes.data;
											
											// 填充学院、专业、班级名称
											for (let college of allClassData) {
												if (collegeMap.has(college.id)) {
													let taskCollege = collegeMap.get(college.id);
													taskCollege.name = college.name;
													
													// 填充专业名称
													for (let major of college.children) {
														if (taskCollege.children.has(major.id)) {
															let taskMajor = taskCollege.children.get(major.id);
															taskMajor.name = major.name;
															
															// 填充班级名称
															for (let cls of major.children) {
																let taskClassIndex = taskMajor.children.findIndex(c => c.id == cls.id);
																if (taskClassIndex !== -1) {
																	taskMajor.children[taskClassIndex].name = cls.name;
																}
															}
														}
													}
												}
											}
											
											// 转换数据结构为数组形式
											let collegeArray = [];
											collegeMap.forEach((college) => {
												let majorArray = [];
												college.children.forEach((major) => {
													majorArray.push({
														id: major.id,
														name: major.name,
														children: major.children
													});
												});
												collegeArray.push({
													id: college.id,
													name: college.name,
													children: majorArray
												});
											});
											
											bgxylist = collegeArray;
											
											// 更新学院下拉框
											let html = '<option value="0">请选择学院</option>';
											bgxylist.forEach((item) => {
												html += '<option value="' + item.id + '">' + item.name + '</option>';
											});
											$("#bgxyselect").html(html);
											
											// console.log("任务班级数据加载完成:", bgxylist);
										} else {
											console.error("获取班级数据失败:", classRes);
										}
									},
									error: (err) => {
										console.error("获取班级数据请求出错:", err);
									}
								});
							} else {
								console.warn("当前任务没有班级权限数据或未找到匹配的任务");
							}
						} else {
							console.error("获取任务列表失败:", res);
						}
					},
					error: (err) => {
						console.error("获取任务列表请求出错:", err);
					}
				});
			}

			function showyulan(item) {
				$("#yldate").html($(item).attr("data-date"))
				$("#ylxk").html($(item).attr("data-xkname"))
				$("#tctitlerw").html($(item).attr("data-name"))
				$('.xxrwtc').show()
				$.ajax({
					url: baseurl + "/learning-tasks-resource/getByTaskIdMap/" + $(item).attr("data-id"),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ''
							// console.log(res.data)
							res.data.forEach((item) => {
								html +=
									`<div class="rwinfotitle">${item.name}(${item.List.length})</div><div class="rwinfolist">`
								item.List.forEach((item2, index) => {
									html +=
										`<div class="rwinfoitem"><div class="rwitemleft">资源${index+1}</div><div class="rwitemright"><div>资源标题: <label>${item2.resourceName}</label></div></div></div>`
								})
								html += `</div>`
							})

							$("#rwlist").html(html)
						}
					}
				})
			}

			function closeyulan() {
				$('.xxrwtc').hide()
			}
			let pageindex = 1
			let pageSize = 15

			function getnewlist(index) {
				pageindex = index
				getrwlist()
			}

			//<input id="ssrwname" placeholder="请输入任务名称"/>
			//<input id="ssjsname" placeholder="请输入发布教师名称"/>
			//<select id="ssxkid">
			//	<option value="0">请选择学科</option>
			//</select>
			//<button onclick="ssrwlist()">查询</button>
			//<button onclick="clearss()">清空</button>

			function getxuekelist() {
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'get',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						let html = '<option value="0">请选择学科</option>'
						res.data.forEach((item) => {
							html += `<option value="${item.id}">${item.name}</option>`
						})
						$("#ssxkid").html(html)
					}
				})
			}

			let ssrwname = null
			let sxkid = null

			function ssrwlist() { //搜索按钮被点击
				pageindex = 1
				ssrwname = $("#ssrwname").val()
				if ($("#ssxkid").val() == '0') {
					sxkid = null
				} else {
					sxkid = $("#ssxkid").val()
				}
				getrwlist()
			}

			function clearss() {
				ssrwname = null
				sxkid = null
				pageindex = 1
				$("#ssrwname").val("")
				$("#ssxkid").val("0")
				getrwlist()
			}

			function getrwlist() {
				$.ajax({
					url: baseurl + "/learning-tasks/weblist",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: pageindex,
						pageSize: pageSize,
						tasksName: ssrwname,//任务名称
						sectionId: sxkid //学科ID
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data)
							let html = ""
							res.data.list.forEach((item) => {
								html += `<div class="xxrwitem">
								<div class="xxrwiteml">
									<div class="xxrwtitle"><div>${item.tasksName}</div></div>
									<div class="xxrwstr">
										<label>${setDate(item.startTime)}-${setDate(item.endTime)}</label>
										<label>${item.sectionName}</label>
									</div>
								</div>
								<div class="xxrmitemr">
									<div class="xxrwyl" onclick="showyulan(this)" data-id="${item.id}" data-name="${item.tasksName}" data-date="${setDate(item.startTime)+'-'+setDate(item.endTime)}" data-xkname="${item.sectionName}">预览</div>
									<div class="xxrwsc" style="margin-right: 0.625rem;" onclick="deletess(this)" data-str="${item.tasksName}" data-id="${item.id}">删除</div>`
									if(item.isExpired == 0){
										html += `<div class="xxrwsc" style="background: #999999;">报告</div>`
									}else{
										html += `<div class="xxrwsc" onclick="baogaotc(this)" data-id="${item.id}">报告</div>`
									}
									
								html+=`</div>
							</div>`
							})
							$("#list").html(html)

							pages = res.data.pages
							if (pages > 1) {
								let numhtml = ""
								//当页数大于5
								if (pages > 5) {
									if (pageindex <= 5) { //当前页码小于5的时候右侧不变
										for (let a = 1; a <= 10; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
										numhtml += '<label onclick="getnewlist(11)">...</label>'
									} else if (5 < pageindex & pageindex < pages - 5) {
										numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) - 5) +
											')">...</label>'
										for (let a = pageindex - 4; a <= pageindex + 4; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
										numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) + 5) +
											')">...</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + (pages - 11) +
											')">...</label>'
										for (let a = pages - 10; a <= pages; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
									}
								} else {
									for (let a = 1; a <= pages; a++) {
										if (pageindex == a) {
											numhtml += '<label class="actinum" onclick="getnewlist(' + a +
												')">' + a +
												'</label>'
										} else {
											numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
										}
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
						}
					}
				})
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 						.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '年' + MM + '月' + d + '日 '
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
