// 移动端基础JavaScript文件
// 复用PC端的API配置和核心功能

// 复用PC端的baseurl配置
const baseurl = (function() {
    const isProduction = window.location.hostname !== 'localhost' && !window.location.hostname.includes('127.0.0.1');
    
    if (isProduction) {
        return '/api';
    } else {
        return 'http://localhost:5500/api';
    }
})();

// 移动端专用的工具函数
const MobileUtils = {
    // 显示Toast消息
    showToast: function(message, type = 'info', duration = 3000) {
        const toast = document.createElement('div');
        toast.className = `toast toast-${type}`;
        toast.textContent = message;
        document.body.appendChild(toast);
        
        setTimeout(() => {
            toast.classList.add('show');
        }, 100);
        
        setTimeout(() => {
            toast.classList.remove('show');
            setTimeout(() => {
                document.body.removeChild(toast);
            }, 300);
        }, duration);
    },

    // 防抖函数
    debounce: function(func, wait) {
        let timeout;
        return function executedFunction(...args) {
            const later = () => {
                clearTimeout(timeout);
                func(...args);
            };
            clearTimeout(timeout);
            timeout = setTimeout(later, wait);
        };
    },

    // 节流函数
    throttle: function(func, limit) {
        let inThrottle;
        return function() {
            const args = arguments;
            const context = this;
            if (!inThrottle) {
                func.apply(context, args);
                inThrottle = true;
                setTimeout(() => inThrottle = false, limit);
            }
        };
    },

    // 格式化时间
    formatTime: function(dateString) {
        const date = new Date(dateString);
        const now = new Date();
        const diff = now - date;
        
        const minute = 60 * 1000;
        const hour = minute * 60;
        const day = hour * 24;
        
        if (diff < minute) {
            return '刚刚';
        } else if (diff < hour) {
            return Math.floor(diff / minute) + '分钟前';
        } else if (diff < day) {
            return Math.floor(diff / hour) + '小时前';
        } else if (diff < day * 7) {
            return Math.floor(diff / day) + '天前';
        } else {
            return date.toLocaleDateString();
        }
    },

    // 检查是否为移动设备
    isMobile: function() {
        return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    // 获取设备类型
    getDeviceType: function() {
        const width = window.innerWidth;
        if (width < 576) return 'mobile';
        if (width < 768) return 'tablet';
        return 'desktop';
    }
};

// 复用PC端的核心API函数，但适配移动端
function getclass(url) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: baseurl + "/web/category/all",
            type: 'GET',
            contentType: "application/json",
            headers: {
                "Authorization": sessionStorage.getItem("header")
            },
            dataType: 'json',
            success: (res) => {
                if (res.code == '200') {
                    resolve(res.data);
                } else {
                    reject(res.message);
                }
            },
            error: (err) => {
                reject('网络请求失败');
            }
        });
    });
}

// 获取心声社区数据
function getCommunityData(pageNum = 1, pageSize = 10) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: baseurl + "/web/posts",
            type: 'GET',
            contentType: "application/json",
            headers: {
                "Authorization": sessionStorage.getItem("header")
            },
            data: {
                pageNum: pageNum,
                pageSize: pageSize,
                sort: 'publishedTime,desc'
            },
            dataType: 'json',
            success: (res) => {
                if (res.code == '200') {
                    resolve(res.data);
                } else {
                    reject(res.message);
                }
            },
            error: (err) => {
                reject('获取社区数据失败');
            }
        });
    });
}

// 获取红色书籍数据
function getRedBooksData(pageNum = 1, pageSize = 10) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: baseurl + "/web/redbook/list",
            type: 'GET',
            contentType: "application/json",
            headers: {
                "Authorization": sessionStorage.getItem("header")
            },
            data: {
                pageNum: pageNum,
                pageSize: pageSize
            },
            dataType: 'json',
            success: (res) => {
                if (res.code == '200') {
                    resolve(res.data);
                } else {
                    reject(res.message);
                }
            },
            error: (err) => {
                reject('获取红色书籍数据失败');
            }
        });
    });
}

// 获取课程数据
function getCoursesData(pageNum = 1, pageSize = 10) {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: baseurl + "/web/course/list",
            type: 'GET',
            contentType: "application/json",
            headers: {
                "Authorization": sessionStorage.getItem("header")
            },
            data: {
                pageNum: pageNum,
                pageSize: pageSize
            },
            dataType: 'json',
            success: (res) => {
                if (res.code == '200') {
                    resolve(res.data);
                } else {
                    reject(res.message);
                }
            },
            error: (err) => {
                reject('获取课程数据失败');
            }
        });
    });
}

// 获取轮播图数据
function getBannerData() {
    return new Promise((resolve, reject) => {
        $.ajax({
            url: baseurl + "/web/banner/list",
            type: 'GET',
            contentType: "application/json",
            headers: {
                "Authorization": sessionStorage.getItem("header")
            },
            dataType: 'json',
            success: (res) => {
                if (res.code == '200') {
                    resolve(res.data);
                } else {
                    reject(res.message);
                }
            },
            error: (err) => {
                reject('获取轮播图数据失败');
            }
        });
    });
}

// 检查登录状态
function checkLoginStatus() {
    const userinfo = sessionStorage.getItem("userinfo");
    const header = sessionStorage.getItem("header");
    
    return {
        isLoggedIn: !!(userinfo && header),
        userInfo: userinfo ? JSON.parse(userinfo) : null,
        token: header
    };
}

// 退出登录
function logout() {
    sessionStorage.clear();
    MobileUtils.showToast('已退出登录', 'info');
    
    // 更新UI
    updateLoginUI();
    
    // 可选：跳转到登录页面
    // window.location.href = 'login.html';
}

// 更新登录UI状态
function updateLoginUI() {
    const loginStatus = checkLoginStatus();
    const loginBtn = document.getElementById('loginBtn');
    const userAvatar = document.getElementById('userAvatar');
    const userName = document.getElementById('userName');

    if (loginStatus && loginStatus.isLoggedIn) {
        if (loginBtn) loginBtn.style.display = 'none';
        if (userAvatar) userAvatar.style.display = 'block';
        if (userName && loginStatus.userInfo) {
            userName.textContent = loginStatus.userInfo.name || loginStatus.userInfo.realName || '用户';
        }
    } else {
        if (loginBtn) loginBtn.style.display = 'block';
        if (userAvatar) userAvatar.style.display = 'none';
    }
}

// 点击统计
function clicknum(id) {
    if (!id) return;
    
    $.ajax({
        url: baseurl + "/web/posts/click/" + id,
        type: 'POST',
        contentType: "application/json",
        headers: {
            "Authorization": sessionStorage.getItem("header")
        },
        dataType: 'json',
        success: (res) => {
            // 点击统计成功，无需特殊处理
        },
        error: (err) => {
            // 点击统计失败，无需特殊处理
        }
    });
}

// 获取URL参数
function getUrlParam(name) {
    const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
    const r = window.location.search.substr(1).match(reg);
    if (r != null) return unescape(r[2]);
    return null;
}

// 页面加载完成后的初始化
document.addEventListener('DOMContentLoaded', function() {
    // 延迟执行，避免与其他脚本冲突
    setTimeout(() => {
        // 更新登录UI
        updateLoginUI();

        // 绑定登录按钮事件（只在没有其他脚本处理时）
        const loginBtn = document.getElementById('loginBtn');
        if (loginBtn && !loginBtn.onclick) {
            loginBtn.addEventListener('click', function() {
                const serviceUrl = window.location.origin + window.location.pathname.replace('index.html', '') + 'userinfo-simple.html';
                window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=' + encodeURIComponent(serviceUrl);
            });
        }

        // 绑定用户头像点击事件（显示用户菜单）
        const userAvatar = document.getElementById('userAvatar');
        if (userAvatar && !userAvatar.onclick) {
            userAvatar.addEventListener('click', function() {
                // 可以显示用户菜单或跳转到用户页面
                window.location.href = 'pages/profile.html';
            });
        }
    }, 100);
});

// 导出给全局使用
window.MobileUtils = MobileUtils;
window.checkLoginStatus = checkLoginStatus;
window.updateLoginUI = updateLoginUI;
window.logout = logout;
