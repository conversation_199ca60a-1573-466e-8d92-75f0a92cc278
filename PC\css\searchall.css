.content {
	min-height: 34rem;
	padding-bottom: 2.916666rem;
}

body {
	background: #fbfbfb !important;
}

.ssdiv {
	display: flex;
	align-items: center;
	justify-content: center;
	padding-top: 2.916666rem;
}

.ssdiv div {
	width: 28.125rem;
	height: 2.8125rem;
	display: flex;
}

.ssdiv div input {
	outline-style: none;
	border: 0.052083rem solid #cecece;
	width: 20.833333rem;
	height: 2.8125rem;
	box-sizing: border-box;
	border-right: none;
	padding-left: 0.78125rem;
	padding-right: 0.78125rem;
	font-size: 0.9375rem;
	border-top-left-radius: 0.260416rem;
	border-bottom-left-radius: 0.260416rem;
}

.ssdiv div label {
	width: calc(100% - 20.833333rem);
	height: 2.8125rem;
	background: #c00714;
	color: #FFFFFF;
	font-size: 0.9375rem;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	border-top-right-radius: 0.260416rem;
	border-bottom-right-radius: 0.260416rem;
}

.gjcdiv {
	display: flex;
	align-items: center;
	justify-content: center;
	padding-top: 1.5625rem;
	padding-bottom: 2.604166rem;
}

.gjcdiv span {
	font-size: 0.833333rem;
	font-weight: bold;
	color: #333333;
	padding-right: 0.78125rem;
}

.gjcdiv label {
	font-size: 0.833333rem;
	color: #999999;
	padding: 0px 0.520833rem;
	cursor: pointer;
}

.classbox {
	display: flex;
	align-items: center;
	justify-content: center;
	height: 3.333333rem;
	background: #FFFFFF;
}

.classbox div {
	color: #333333;
	font-size: 0.9375rem;
	width: 6.770833rem;
	cursor: pointer;
}

.classbox .acccs {
	font-weight: bold;
	color: #c00714;
}

.classbox div:hover {
	font-weight: bold;
	color: #c00714;
}

.nrtoptile {
	display: flex;
	align-items: center;
	height: 1.979166rem;
	margin: 0px auto;
	width: 66.666666rem;
	font-size: 0.729166rem;
}

.bb {
	background: #f3f3f3;
}

.nrtoptile span {
	color: #999999;
	margin-right: 0.520833rem;
	padding: 0.104166rem 0.520833rem;
	cursor: pointer;
}

.nrtoptile label {
	color: #999999;
	margin-right: 0.78125rem;
}

.nrtoptile .acac {
	background: #FFFFFF;
	color: #e56c74;
	border-radius: 0.260416rem;
}

.nrtoptile span:hover {
	background: #FFFFFF;
	color: #e56c74;
	border-radius: 0.260416rem;
}

.strlist {
	width: 66.666666rem;
	margin: 0px auto;
}

.item1 {
	cursor: pointer;
	padding-top: 1.041666rem;
	padding-bottom: 1.041666rem;
	border-bottom: 0.052083rem dashed #e1e1e1;
	padding-left: 0.78125rem;
	padding-right: 0.78125rem;
	box-sizing: border-box;
}

.item1:nth-child(odd) {
	background: #fbfbfb;
}

.item1:nth-child(even) {
	background: #FFFFFF;
}

.item1:hover .item1nr {
	color: #c00714;
}

.item1:last-child {
	border: none;
}

.item1nr {
	font-size: 0.9375rem;
	color: #333333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	padding-bottom: 1.5625rem;
	width: 60%;
}

.item1btn {
	display: flex;
	align-items: center;
}

.item1btn label {
	font-size: 0.729166rem;
	color: #999999;
	padding-right: 1.302083rem;
	display: flex;
	align-items: center;
}

.item1btn label img {
	padding-right: 0.260416rem;
	display: block;
}

#uu {
	width: 0.78125rem;
}

#ss {
	width: 0.78125rem;
}

#yy {
	width: 0.9375rem;
}

#fyq {
	padding-top: 2.916666rem;
	padding-bottom: 2.916666rem;
}

.item2 {
	cursor: pointer;
	padding-top: 1.041666rem;
	padding-bottom: 1.041666rem;
	border-bottom: 0.052083rem dashed #e1e1e1;
	padding-left: 0.78125rem;
	padding-right: 0.78125rem;
	box-sizing: border-box;
	display: flex;
}

.item3 {
	cursor: pointer;
	padding-top: 1.041666rem;
	padding-bottom: 1.041666rem;
	border-bottom: 0.052083rem dashed #e1e1e1;
	padding-left: 0.78125rem;
	padding-right: 0.78125rem;
	box-sizing: border-box;
	display: flex;
}

.item2:nth-child(odd) {
	background: #fbfbfb;
}

.item2:nth-child(even) {
	background: #FFFFFF;
}

.item2:hover .item1nr {
	color: #c00714;
}

.item2:last-child {
	border: none;
}

.item2img {
	width: 13.333333rem;
	display: flex;
}

.item2right {
	width: calc(100% - 13.333333rem);
	box-sizing: border-box;
	padding-left: 1.041666rem;
	position: relative;
}

.item2rightbottom {
	position: absolute;
	bottom: 0;
	left: 1.041666rem;
	right: 0;
}

.item2righttop .item1nr {
	padding: 0;
}

.item2 .zjxk {
	color: #999999;
	font-size: 0.729166rem;
	padding-top: 0.260416rem;
}

.item3:last-child {
	border: none;
}

.item3img {
	width: 5.729166rem;
}

.item3right {
	width: calc(100% - 5.729166rem);
	box-sizing: border-box;
	padding-left: 1.041666rem;
	position: relative;
}

.item3 .zjxk {
	font-size: 0.833333rem;
	color: #999999;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	padding-bottom: 1.5625rem;
	width: 60%;
	padding-top: 0.260416rem;
}

.item3 .item1nr {
	padding: 0;
}

.item3rightbottom {
	position: absolute;
	bottom: 0.520833rem;
	left: 1.041666rem;
	right: 0;
}

.item3:nth-child(odd) {
	background: #fbfbfb;
}

.item3:nth-child(even) {
	background: #FFFFFF;
}

.item3:hover .item1nr {
	color: #c00714;
}
.nodata{
	text-align: center;
	margin-top: 1.5625rem;
	font-size: 0.9375rem;
	color: #999999;
}