<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>思政一体化平台-编辑资源</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<style>
			/* 重用uploadresources.html的样式 */
			.form-container {
				background: #fff;
				border-radius: 12px;
				padding: 30px;
				box-shadow: 0 2px 12px rgba(0,0,0,0.08);
				margin-bottom: 20px;
			}
			
			.form-title {
				font-size: 20px;
				font-weight: 600;
				color: #333;
				margin-bottom: 30px;
				padding-bottom: 15px;
				border-bottom: 2px solid #f0f0f0;
			}
			
			.form-grid {
				display: grid;
				grid-template-columns: 1fr 1fr;
				gap: 25px 30px;
				margin-bottom: 25px;
			}
			
			.form-item {
				display: flex;
				align-items: center;
				gap: 15px;
			}
			
			.form-item.full-width {
				grid-column: 1 / -1;
			}
			
			.form-label {
				font-weight: 500;
				color: #333;
				white-space: nowrap;
				min-width: 80px;
				font-size: 14px;
			}
			
			.form-input {
				flex: 1;
				padding: 10px 15px;
				border: 1px solid #d9d9d9;
				border-radius: 6px;
				font-size: 14px;
				transition: all 0.3s ease;
				background-color: #fff;
			}
			
			.form-input:focus {
				border-color: #1890ff;
				box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
				outline: none;
			}
			
			.form-select {
				flex: 1;
				padding: 10px 15px;
				border: 1px solid #d9d9d9;
				border-radius: 6px;
				font-size: 14px;
				background-color: #fff;
				cursor: pointer;
				transition: all 0.3s ease;
			}
			
			.form-select:focus {
				border-color: #1890ff;
				box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
				outline: none;
			}
			
			.form-textarea {
				flex: 1;
				padding: 12px 15px;
				border: 1px solid #d9d9d9;
				border-radius: 6px;
				font-size: 14px;
				min-height: 80px;
				resize: vertical;
				font-family: inherit;
				transition: all 0.3s ease;
			}
			
			.form-textarea:focus {
				border-color: #1890ff;
				box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
				outline: none;
			}
			
			.submit-section {
				text-align: center;
				padding-top: 20px;
				border-top: 1px solid #f0f0f0;
				display: flex;
				gap: 15px;
				justify-content: center;
			}
			
			.submit-btn {
				background: linear-gradient(135deg, #1890ff, #40a9ff);
				color: #fff;
				border: none;
				padding: 12px 40px;
				border-radius: 8px;
				font-size: 16px;
				font-weight: 600;
				cursor: pointer;
				transition: all 0.3s ease;
				box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
			}
			
			.submit-btn:hover {
				transform: translateY(-2px);
				box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
			}
			
			.cancel-btn {
				background: #f5f5f5;
				color: #666;
				border: none;
				padding: 12px 40px;
				border-radius: 8px;
				font-size: 16px;
				font-weight: 600;
				cursor: pointer;
				transition: all 0.3s ease;
			}
			
			.cancel-btn:hover {
				background: #e8e8e8;
				transform: translateY(-2px);
			}
			
			.loading-overlay {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(255,255,255,0.8);
				display: none;
				align-items: center;
				justify-content: center;
				z-index: 9999;
				backdrop-filter: blur(4px);
			}
			
			.loading-content {
				text-align: center;
				background: white;
				padding: 40px;
				border-radius: 12px;
				box-shadow: 0 8px 32px rgba(0,0,0,0.1);
			}
			
			.loading-spinner {
				width: 40px;
				height: 40px;
				border: 4px solid #f0f0f0;
				border-top: 4px solid #1890ff;
				border-radius: 50%;
				animation: spin 1s linear infinite;
				margin: 0 auto 20px;
			}
			
			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}

			@media (max-width: 768px) {
				.form-grid {
					grid-template-columns: 1fr;
					gap: 20px;
				}
				
				.submit-section {
					flex-direction: column;
					align-items: center;
				}
				
				.submit-btn, .cancel-btn {
					width: 200px;
				}
			}
		</style>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
					<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>
					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="logout()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
				</div>
			</div>
		</div>
		
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a href="studentachievement.html" class="leftitem">学生成绩统计</a>
						<a href="studentlearning.html" class="leftitem">学习记录统计</a>
						<a href="userexamination1.html" class="leftitem">考试管理</a>
						<a href="learningtasks.html" class="leftitem">学习任务管理</a>
						<a class="leftitem activeleftitem">成果展示</a>
						<a href="releaseyourvoice2.html" class="leftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="userinfotopdiv">
						<a href="achievements.html">成果列表</a>
						<a href="uploadresources.html">发布资源</a>
						<a class="acccccg">编辑资源</a>
					</div>
					
					<div id="loading" class="loading-overlay">
						<div class="loading-content">
							<div class="loading-spinner"></div>
							<div>正在保存修改...</div>
						</div>
					</div>
					
					<div class="form-container">
						<div class="form-title">✏️ 编辑资源</div>
						
						<div class="form-grid">
							<div class="form-item">
								<label class="form-label">标题:</label>
								<input id="bt" class="form-input" placeholder="请输入标题" />
							</div>
							
							<div class="form-item">
								<label class="form-label">作者:</label>
								<input id="zz" class="form-input" placeholder="请输入作者" />
							</div>
							
							<div class="form-item">
								<label class="form-label">组织:</label>
								<input id="zuzhi" class="form-input" placeholder="请输入学校或出版社" />
							</div>
							
							<div class="form-item">
								<label class="form-label">知识点:</label>
								<input id="zhishidian" class="form-input" placeholder="请输入知识点(多个知识点用逗号隔开)" />
							</div>
							
							<div class="form-item">
								<label class="form-label">资源类型:</label>
								<select id="types" class="form-select">
									<option value="0">教学成果</option>
									<option value="1">外来资源</option>
								</select>
							</div>
							
							<div class="form-item">
								<label class="form-label">属性:</label>
								<select id="shuxing" class="form-select">
									<option value="0">请选择属性</option>
								</select>
							</div>
							
							<div class="form-item">
								<label class="form-label">学科:</label>
								<select id="xueke" class="form-select" onchange="xuekechange()">
									<option value="0">请选择学科</option>
								</select>
							</div>
							
							<div class="form-item">
								<label class="form-label">章:</label>
								<select id="zhang" class="form-select" onchange="zhangchange()">
									<option value="0">请选择章</option>
								</select>
							</div>
							
							<div class="form-item full-width">
								<label class="form-label">课程简介:</label>
								<textarea id="jj" class="form-textarea" placeholder="请输入简介"></textarea>
							</div>
						</div>
						
						<div class="submit-section">
							<button class="cancel-btn" onclick="goBack()">
								❌ 取消
							</button>
							<button class="submit-btn" onclick="submitEdit()">
								✅ 保存修改
							</button>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
					</div>
				</div>
			</div>
			<div style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index" class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
			let shuxinglist = null;
			let xuekelist = null;
			let resourceId = null;
			let metaId = null;
			let currentResourceData = null;
			
			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html';
				});
				
				let userinfo = sessionStorage.getItem("userinfo");
				if (userinfo) {
					$("#login").hide();
					$("#user").show();
					$("#user").html(JSON.parse(userinfo).name);
					$("#edit").show();
				} else {
					$("#login").show();
					$("#user").hide();
					$("#edit").hide();
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html';
				}
				
				// 获取URL参数
				resourceId = getUrlParam('id');
				metaId = getUrlParam('metaId');
				
				if (!resourceId) {
					cocoMessage.error(2000, '缺少资源ID参数');
					setTimeout(() => {
						window.location.href = 'achievements.html';
					}, 2000);
					return;
				}
				
				// 初始化数据
				getclass();
				getshuxin();
				getxueke();
				getfooterlink();
				
				// 加载资源数据
				loadResourceData();
			});
			
			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
				var r = window.location.search.substr(1).match(reg);
				if (r != null) return unescape(r[2]);
				return null;
			}
			
			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = "";
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>';
							});
							$("#linkbox").html(html);
						}
					}
				});
			}
			
			function getclass() {
				$.ajax({
					url: baseurl + "/web/category/all",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = "";
							for (let i = 0; i < res.data.length; i++) {
								html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res.data[i].id + '"><label>' + res.data[i].name + '</label></a>';
							}
							$("#menubox").html(html);
						}
					}
				});
			}
			
			function getshuxin() {
				$.ajax({
					url: baseurl + "/attributes",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							shuxinglist = res.data;
							let html = "<option value=0>请选择属性</option>";
							shuxinglist.map((item) => {
								html += '<option value="' + item.id + '">' + item.name + '</option>';
							});
							$("#shuxing").html(html);
						}
					}
				});
			}
			
			function getxueke() {
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							xuekelist = res.data;
							let xuekehtml = "<option value=0>请选择学科</option>";
							xuekelist.map((item) => {
								xuekehtml += '<option value="' + item.id + '">' + item.name + '</option>';
							});
							$("#xueke").html(xuekehtml);
						}
					}
				});
			}
			
			function xuekechange() {
				if ($("#xueke").val() == '0') {
					$("#zhang").html('<option value=0>请选择章</option>');
				} else {
					xuekelist.map((item) => {
						if (item.id == $("#xueke").val()) {
							let zhtml = "<option value=0>请选择章</option>";
							if (item.children) {
								item.children.map((item2) => {
									zhtml += '<option value="' + item2.id + '">' + item2.name + '</option>';
								});
							}
							$("#zhang").html(zhtml);
						}
					});
				}
			}
			
			function zhangchange() {
				// 章节变化的处理逻辑保持不变
			}
			
			function loadResourceData() {
				$("#loading").show();
				
				$.ajax({
					url: baseurl + "/course/meta/" + metaId,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						$("#loading").hide();
						if (res.code == '200') {
							currentResourceData = res.data;
							populateForm(res.data);
						} else {
							cocoMessage.error(2000, '获取资源数据失败');
							setTimeout(() => {
								window.location.href = 'achievements.html';
							}, 2000);
						}
					},
					error: () => {
						$("#loading").hide();
						cocoMessage.error(2000, '获取资源数据失败');
						setTimeout(() => {
							window.location.href = 'achievements.html';
						}, 2000);
					}
				});
			}
			
			function populateForm(data) {
				$("#bt").val(data.title || '');
				$("#zz").val(data.author || '');
				$("#zuzhi").val(data.organization || '');
				$("#jj").val(data.introduction || '');
				$("#zhishidian").val(data.knowledge || '');
				$("#types").val(data.type || '0');
				
				if (data.attributesId) {
					$("#shuxing").val(data.attributesId);
				}
				
				if (data.projectId) {
					$("#xueke").val(data.projectId);
					xuekechange();
					
					setTimeout(() => {
						if (data.sectionId) {
							$("#zhang").val(data.sectionId);
						}
					}, 100);
				}
			}
			
			function submitEdit() {
				// 验证必填字段
				if (!$("#bt").val()) {
					cocoMessage.warning(1000, "请输入标题！");
					return;
				}
				if (!$("#zz").val()) {
					cocoMessage.warning(1000, "请输入作者！");
					return;
				}
				if (!$("#zuzhi").val()) {
					cocoMessage.warning(1000, "请输入组织！");
					return;
				}
				if (!$("#jj").val()) {
					cocoMessage.warning(1000, "请输入简介！");
					return;
				}
				if ($("#shuxing").val() == '0') {
					cocoMessage.warning(1000, "请选择属性！");
					return;
				}
				
				$("#loading").show();
				
				let editData = {
					id: resourceId,
					title: $("#bt").val(),
					author: $("#zz").val(),
					organization: $("#zuzhi").val(),
					introduction: $("#jj").val(),
					knowledge: $("#zhishidian").val(),
					attributesId: $("#shuxing").val(),
					projectId: $("#xueke").val() !== '0' ? $("#xueke").val() : null,
					sectionId: $("#zhang").val() !== '0' ? $("#zhang").val() : null,
					type: $("#types").val()
				};
				
				$.ajax({
					url: baseurl + "/course/edit",
					type: 'PUT',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: JSON.stringify(editData),
					dataType: 'json',
					success: (res) => {
						$("#loading").hide();
						if (res.code == '200') {
							cocoMessage.success(2000, "修改成功！");
							setTimeout(() => {
								window.location.href = 'achievements.html';
							}, 1500);
						} else {
							cocoMessage.error(1000, "修改失败！");
						}
					},
					error: () => {
						$("#loading").hide();
						cocoMessage.error(1000, "修改失败！");
					}
				});
			}
			
			function goBack() {
				if (confirm('确定要取消编辑吗？未保存的修改将丢失。')) {
					window.location.href = 'achievements.html';
				}
			}
			
			function logout() {
				sessionStorage.clear();
				$("#login").show();
				$("#user").hide();
				$("#edit").hide();
				cocoMessage.error(1000, "已退出登录！");
			}
		</script>
		
		<script>
			$("#backtop").hide();
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show();
					} else {
						$("#backtop").hide();
					}
				});
			});
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300);
			});
		</script>
	</body>
</html> 