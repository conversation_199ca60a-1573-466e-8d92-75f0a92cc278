<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-VR红色游学</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/news.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="content">
			<div class="newsbox">
				<div class="newboxleft">
					<div class="lefttop ssss" id="tablist">

					</div>
					<div class="padd">
						<div id="swpbox">
							<div id="swiperpagination" class="swiper-pagination"></div>
							<div id="swiper" class="swiper">
								<div class="swiper-wrapper" id="swiperdiv">

								</div>
							</div>
						</div>
						<div class="newitembox" id="newslist">

						</div>
						<div class="fybox" id="fyq">
							<span id="sy">首页</span>
							<span id="syy">上一页</span>
							<div class="num" id="num">
							</div>
							<span id="xyy">下一页</span>
							<span id="wy">尾页</span>
						</div>
					</div>

				</div>
				<div class="newsright">
					<div class="border"></div>
					<div class="newtitles1">
						<label>最新公告</label>
						<div class="jtdiv">
							<span class="leftimg" onclick="syy2()">
							</span>
							<span class="rightimg" onclick="xyy2()">
							</span>
						</div>
					</div>
					<div class="boxxx" id="gglist">
						
						
					</div>
					<div class="bbbb"></div>
					<!-- <div class="border"></div>
					<div class="newtitles1">
						思政活动公告
					</div>
					<div class="boxxx">
						<div class="boxitem">
							<div class="itemnr">这是一个新闻标题这是一个新闻标题这是一个新闻标题</div>
							<div class="itemsj">2022年1月12日</div>
						</div>
						<div class="boxitem">
							<div class="itemnr">这是一个新闻标题这是一个新闻标题这是一个新闻标题</div>
							<div class="itemsj">2022年1月12日</div>
						</div>
						<div class="boxitem">
							<div class="itemnr">这是一个新闻标题这是一个新闻标题这是一个新闻标题</div>
							<div class="itemsj">2022年1月12日</div>
						</div>
						<div class="boxitem">
							<div class="itemnr">这是一个新闻标题这是一个新闻标题这是一个新闻标题</div>
							<div class="itemsj">2022年1月12日</div>
						</div>
						<div class="boxitem">
							<div class="itemnr">这是一个新闻标题这是一个新闻标题这是一个新闻标题</div>
							<div class="itemsj">2022年1月12日</div>
						</div>
					</div> -->
				</div>
			</div>
		</div>

		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let pageindex = 1
			let pagesize = 10
			let pages = 0
			let pageindex2 = 1
			let pages2 = 1
			let classid = getUrlParam('classid')
			let classdata = null
			function incontent(item) {
				window.location.href = "newscontent.html?id=" + $(item).attr("data-id") + "&classid=" + classid
			}
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				let userinfo = sessionStorage.getItem("userinfo")
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
					if(window.localStorage.getItem("jilu")){
						$.ajax({
							url: baseurl + "/study/record/add",
							type: 'post',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: window.localStorage.getItem("jilu"),
							dataType: 'json',
							success: (res) => {
								window.localStorage.clear()
							}
						})
					}
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				getclass()
				getclassid()
				getfooterlink()
			})
			function getfooterlink(){
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item)=>{
								html+='<a href="'+item.url+'" target="_blank">'+item.name+'</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
			function getclassid() {
				$.ajax({
					url: baseurl + "/web/category/news",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							classdata = res.data[0]
							if (!classid) {
								classid = res.data[0].children[0].id
							}
							let html = ""
							res.data[0].children.map((item,index) => {
								if(index<2){
									if (item.id === classid) {
										html +=
											'<div class="titles2 activetitles2" onclick="selectid(this)" data-id="' +
											item.id + '">' + item.name + '</div>'
									} else {
										html += '<div class="titles2" onclick="selectid(this)" data-id="' + item
											.id + '">' + item.name + '</div>'
									}
								}
							})
							$("#tablist").html(html)
							getnewslist()
							getzxgg()
						}
					}
				})
			}
			
			function syy2(){
				if(pageindex2>1){
					pageindex2-=1
					getzxgg()
				}
			}
			function xyy2(){
				if(pageindex2<pages2){
					pageindex2+=1
					getzxgg()
				}
			}
			function getzxgg(){
				// console.log(classdata.children[2].id)
				$.ajax({
					url: baseurl + "/web/postByCategoryId",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: pageindex2,
						pageSize: 8,
						id: classdata.children[2].id,
						sort: 'dispaly_time'
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data)
							pages2 = res.data.pages
							let html = ""
							res.data.list.map((item)=>{
								html+='<div class="boxitem" onclick="incontent(this)" data-id="'+item.id+'"><div class="itemnr">'+item.title+'</div>'+
							'<div class="itemsj">'+setDate(item.eventTime)+'</div></div>'
							})
							$("#gglist").html(html)
						}
					}
				})
			}
			function selectid(items) {
				classid = $(items).attr("data-id")
				window.location.href='news.html?classid='+$(items).attr("data-id")
			}

			function getnewslist() {
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: pageindex,
						pageSize: pagesize,
						categoryId: classid
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							let swiperhtml = ''
							res.data.list.map((item,index) => {
								
								if(index<5){
									swiperhtml += '<div class="swiper-slide" onclick="incontent(this)" data-id="' + item
									.id + '">' +
										'<img src="' + baseurl + (item.thumbPath && item.thumbPath.length > 0 ? item.thumbPath[0] : '') + '" />' +
										'<div class="swiperdiv">' +
										'<div class="lllll">' +
										'<div class="swipertitle">'+item.title+'</div>' +
										//<div class="swipercontent">'+item+'</div>
										'</div>' +
										'<div class="sjsj"><img src="img/sj2.png" />'+setDate(item
										.eventTime)+'</div></div></div>'
								}

								html += '<div class="newitem" onclick="incontent(this)" data-id="' + item
									.id + '">' +
									'<div class="newitemtitle">' + item.title + '</div>' +
									'<div class="newitemsj"><img src="./img/sj2.png" />' + setDate(item
										.eventTime) +
									'</div></div>'
							})
							$("#swiperdiv").html(swiperhtml)
							$("#newslist").html(html)
							pages = res.data.pages
							if (pages > 1) {
								let numhtml = ""
								let maxButtons = 5; // 最多显示的页码按钮数
								let halfButtons = Math.floor(maxButtons / 2);
								let start = Math.max(1, pageindex - halfButtons);
								let end = Math.min(pages, start + maxButtons - 1);

								if (end - start + 1 < maxButtons) {
									start = Math.max(1, end - maxButtons + 1);
								}

								if (start > 1) {
									numhtml += '<label onclick="getnewlist(1)">1</label>';
									if (start > 2) numhtml += '<label>...</label>';
								}

								for (let a = start; a <= end; a++) {
									if (pageindex == a) {
										numhtml += '<label class="actinum" onclick="getnewlist(' + a + ')">' + a + '</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
									}
								}

								if (end < pages) {
									if (end < pages - 1) numhtml += '<label>...</label>';
									numhtml += '<label onclick="getnewlist(' + pages + ')">' + pages + '</label>';
								}

								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
						}
					},
					complete: (res) => {
						var swiper = new Swiper('#swiper', {
							autoplay: true,
							loop: true,
							pagination: {
								el: '.swiper-pagination',
								clickable: true
							}
						})
					}
				})
			}
function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是第最后一页了！")
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！")
					}
				} else {
					pageindex = index
					getnewslist()
				}
			}
			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					if (res.data[i].id == getUrlParam('id') || res.data[i].redirectUrl == 'news.html') {
			// 						classdate = res.data[i]
			// 						html += '<a class="menuitem active" href="' + res.data[i].redirectUrl + '?id=' +
			// 							res
			// 							.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					} else {
			// 						html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 							.data[
			// 								i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					}
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
