<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-四个自信</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/nnindex.css" />
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<style>
			#zxcontent p{
				text-indent: 2em;
			}
		</style>
	</head>
	<body class="confident">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
					
				</div>
			</div>
		</div>
		<div class="nzxtitleview">
			<img src="img/sgzx.png" />
		</div>
		<div class="nzxview">
			<div class="nzxtopbar" id="topzxlist">

			</div>
			<div class="zxstrbox">
				<div>
					<label><img src="img/zz.png" /><span id="bm"></span></label>
				</div>
				<div>
					<label><img src="img/sj2.png" /><span id="sj"></span></label>
					<label><img src="img/yj.png" /><span id="view"></span></label>
				</div>
			</div>
			<div class="zxcontent" id="zxcontent">

			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">

					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html"
					style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			$(document).ready(() => {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				getclass()
				getclassinfo()
				getfooterlink()
			})
			function getfooterlink(){
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item)=>{
								html+='<a href="'+item.url+'" target="_blank">'+item.name+'</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
			function getclassinfo() {
				$.ajax({
					url: baseurl + "/web/category/party/building",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data[0].children)
							let arr = res.data[0].children
							let classdata = arr.find(item => item.name == '四个自信')
							getlist(classdata.id)
						}
					}
				})
			}
			let posid = null
			let poslist = null

			function getlist(id) {
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: 1,
						pageSize: 4,
						categoryId: id
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data.list)
							poslist = res.data.list
							posid = res.data.list[0].id

							let html = ""
							poslist.forEach((item) => {
								html +=
									`<div class="${item.id == posid?'zxbaritem zxactive':'zxbaritem'}" onclick="showinfo(this)" data-id="${item.id}">${item.title}</div>`
							})
							$("#topzxlist").html(html)

							getinfo(posid)
						}
					}
				})
			}

			function showinfo(item) {
				let id = $(item).attr("data-id")
				let html = ""
				poslist.forEach((item) => {
					html +=
						`<div class="${item.id == id?'zxbaritem zxactive':'zxbaritem'}" onclick="showinfo(this)" data-id="${item.id}">${item.title}</div>`
				})
				$("#topzxlist").html(html)

				getinfo(id)
			}

			function getinfo(id) {
				$.ajax({
					url: baseurl + "/web/posts/" + id,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							$("#bm").html(res.data.author)
							if (res.data.eventTime) {
								$("#sj").html(setDate(res.data.eventTime))
							} else {
								$("#sj").html(setDate(res.data.createdAt))
							}

							$("#view").html(res.data.clickCount)
							$("#zxcontent").html(res.data.content)
							$("video").parent().attr("style","text-indent: 0")
							clicknum(res.data.id)
						}
					}
				})
			}

			function clicknum(id) {
				$.ajax({
					url: baseurl + "/web/posts/click/" + id,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {}
				})
			}

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
