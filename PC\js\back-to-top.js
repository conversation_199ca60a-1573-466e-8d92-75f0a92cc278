/**
 * 优化版返回顶部按钮
 * 提供更美观的返回顶部按钮，并自动在适当时机显示/隐藏
 */
(function() {
    // 创建返回顶部按钮
    function createBackTopButton() {
        // 移除旧的返回顶部按钮（如果存在）
        if(document.getElementById('backtop')) {
            document.getElementById('backtop').remove();
        }
        
        // 创建新的返回顶部按钮
        const backTopBtn = document.createElement('div');
        backTopBtn.id = 'backtop';
        backTopBtn.className = 'back-to-top-btn';
        backTopBtn.innerHTML = `
            <span class="back-to-top-icon">↑</span>
            <span class="back-to-top-text">返回顶部</span>
        `;
        
        // 添加到页面
        document.body.appendChild(backTopBtn);
        
        // 添加样式
        const style = document.createElement('style');
        style.textContent = `
            .back-to-top-btn {
                position: fixed;
                right: 1.2rem;
                bottom: 1.2rem;
                display: flex;
                align-items: center;
                justify-content: center;
                padding: 0 0.6rem;
                height: 1.6rem;
                background: #DE2910;
                color: #FFFFFF;
                border-radius: 0.8rem;
                cursor: pointer;
                z-index: 999;
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.15);
                transition: all 0.25s ease;
                opacity: 0;
                transform: translateY(10px);
                pointer-events: none;
                font-family: "Microsoft YaHei", "微软雅黑", sans-serif;
                text-align: center;
            }
            
            .back-to-top-btn.visible {
                opacity: 1;
                transform: translateY(0);
                pointer-events: auto;
            }
            
            .back-to-top-btn:hover {
                background: #FF4500;
                box-shadow: 0 3px 8px rgba(222, 41, 16, 0.3);
                transform: translateY(-2px);
            }
            
            .back-to-top-btn:active {
                transform: translateY(0);
                box-shadow: 0 1px 4px rgba(222, 41, 16, 0.3);
            }
            
            .back-to-top-icon {
                font-size: 0.7rem;
                margin-right: 0.2rem;
                font-weight: bold;
                line-height: 1;
            }
            
            .back-to-top-text {
                font-size: 0.65rem;
                font-weight: 500;
                line-height: 1;
            }
            
            @media (max-width: 768px) {
                .back-to-top-btn {
                    padding: 0 0.5rem;
                    height: 1.4rem;
                }
                
                .back-to-top-icon {
                    font-size: 0.65rem;
                }
                
                .back-to-top-text {
                    font-size: 0.6rem;
                }
            }
        `;
        document.head.appendChild(style);
        
        // 监听滚动事件
        let scrollTimer;
        window.addEventListener('scroll', function() {
            clearTimeout(scrollTimer);
            scrollTimer = setTimeout(toggleBackTop, 100);
        });
        
        // 初始检查
        toggleBackTop();
        
        // 点击事件
        backTopBtn.addEventListener('click', function() {
            smoothScrollToTop();
        });
    }
    
    // 控制返回顶部按钮的显示与隐藏
    function toggleBackTop() {
        const backTopBtn = document.getElementById('backtop');
        if(!backTopBtn) return;
        
        if(window.pageYOffset > 300) {
            backTopBtn.classList.add('visible');
        } else {
            backTopBtn.classList.remove('visible');
        }
    }
    
    // 平滑滚动到顶部
    function smoothScrollToTop() {
        const currentPosition = window.pageYOffset;
        if(currentPosition > 0) {
            // 使用平滑滚动
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        }
    }
    
    // 当DOM加载完成后初始化
    if(document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', createBackTopButton);
    } else {
        createBackTopButton();
    }
})(); 