<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
		<title>思政一体化平台-思政考核系统-试卷解析</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/examination2info.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<style>
			/* 优化页面布局和样式 */
			.subject-header {
				background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
				padding: 20px;
				border-radius: 12px;
				margin-bottom: 20px;
				color: white;
				box-shadow: 0 4px 15px rgba(0,0,0,0.1);
			}
			
			.subject-title {
				font-size: 1.5rem;
				font-weight: 600;
				margin-bottom: 10px;
				line-height: 1.4;
			}
			
			.subject-meta {
				display: flex;
				flex-wrap: wrap;
				gap: 20px;
				margin-top: 15px;
				font-size: 0.9rem;
				opacity: 0.9;
			}
			
			.subject-meta .meta-item {
				display: flex;
				align-items: center;
				gap: 5px;
			}
			
			.subject-meta .meta-item::before {
				content: "•";
				color: #ffd700;
				font-weight: bold;
			}
			
			.answer-section {
				background: #f8f9fc;
				border-radius: 12px;
				padding: 20px;
				margin: 20px 0;
				border-left: 4px solid #4f46e5;
			}
			
			.answer-title {
				font-size: 1.1rem;
				font-weight: 600;
				color: #4f46e5;
				margin-bottom: 15px;
				display: flex;
				align-items: center;
				gap: 8px;
			}
			
			.answer-title::before {
				content: "📝";
			}
			
			.answer-content {
				background: white;
				padding: 15px;
				border-radius: 8px;
				border: 1px solid #e5e7eb;
				line-height: 1.6;
				color: #374151;
				white-space: pre-wrap;
				word-wrap: break-word;
			}
			
			.score-indicator {
				display: inline-flex;
				align-items: center;
				padding: 4px 12px;
				border-radius: 20px;
				font-size: 0.85rem;
				font-weight: 500;
				margin-left: 10px;
			}
			
			.score-full {
				background: #d1fae5;
				color: #065f46;
			}
			
			.score-partial {
				background: #fef3c7;
				color: #92400e;
			}
			
			.score-zero {
				background: #fee2e2;
				color: #991b1b;
			}
			
			.comments-section {
				background: #fff7ed;
				border-radius: 12px;
				padding: 20px;
				margin: 20px 0;
				border-left: 4px solid #f59e0b;
			}
			
			.comments-title {
				font-size: 1.1rem;
				font-weight: 600;
				color: #f59e0b;
				margin-bottom: 15px;
				display: flex;
				align-items: center;
				gap: 8px;
			}
			
			.comments-title::before {
				content: "💬";
			}
			
			.comments-content {
				background: white;
				padding: 15px;
				border-radius: 8px;
				border: 1px solid #fed7aa;
				line-height: 1.6;
				color: #92400e;
			}
			
			.result-badge {
				display: inline-flex;
				align-items: center;
				padding: 6px 16px;
				border-radius: 25px;
				font-size: 0.9rem;
				font-weight: 600;
				margin-left: 15px;
			}
			
			.result-correct {
				background: #dcfce7;
				color: #166534;
				border: 1px solid #bbf7d0;
			}
			
			.result-correct::before {
				content: "✓";
				margin-right: 6px;
			}
			
			.result-wrong {
				background: #fef2f2;
				color: #dc2626;
				border: 1px solid #fecaca;
			}
			
			.result-wrong::before {
				content: "✗";
				margin-right: 6px;
			}
			
			/* 移动端适配 */
			@media (max-width: 768px) {
				.subject-meta {
					flex-direction: column;
					gap: 10px;
				}
				
				.answer-section, .comments-section {
					margin: 15px 0;
					padding: 15px;
				}
				
				.subject-title {
					font-size: 1.25rem;
				}
			}
		</style>
	</head>
	<body class="index">
		<!-- 顶部导航 -->
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
					<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>
					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
					<!-- 菜单项将通过JavaScript动态加载 -->
				</div>
			</div>
		</div>

		<div class="content">
			<!-- 题目信息展示区域 -->
			<div id="subjectInfo" style="display: none;">
				<div class="subject-header">
					<div class="subject-title" id="subjectTitle"></div>
					<div class="subject-meta">
						<div class="meta-item">
							<span>题型：</span>
							<span id="subjectType"></span>
						</div>
						<div class="meta-item">
							<span>分值：</span>
							<span id="subjectScore"></span>
						</div>
						<div class="meta-item">
							<span>难度：</span>
							<span id="subjectDifficulty"></span>
						</div>
						<div class="meta-item">
							<span>创建时间：</span>
							<span id="subjectCreatedAt"></span>
						</div>
					</div>
				</div>

				<!-- 学生答题记录 -->
				<div id="answerRecords"></div>
			</div>

			<div class="contentleft">
				<div class="sjtitle">
					<img src="img/sjtitle.png" />
					<div class="sbag">
						<div class="stitle" id="papername"></div>
						<div class="sbottom">
							<label>学科: <span id="paperxk"></span></label>
							<label>考试时间: <span id="papertime"></span></label>
							<label>总分: <span id="papersc"></span></label>
							<label>及格线: <span id="papersc2"></span></label>
						</div>
					</div>
				</div>
				<div class="title2" id="sjname">
				</div>
				<div class="scrollview1">
					<div id="leftbox">
						<div class="item">
							<div class="itemleft" id="showth"></div>
							<div class="itemright">
								<div class="tm" id="showtm"></div>
								<div class="dabox" id="showxx">
									<!-- <div class="da"><span></span>A.法国里昂工人起义</div> -->
								</div>
							</div>
						</div>
						<div class="jiexi">
							<div class="jxtop" id="tops">
								<div class="trueda">正确答案 <label id="zqda"></label></div>
								<div class="ksda">考生答案 <label id="ksda"></label></div>
							</div>
							<div class="jxtext" id="jxstr">
								

							</div>
						</div>
					</div>

					<div class="xxtx">
						<div onclick="syt()">上一题</div>
						<div onclick="xyt()">下一题</div>
					</div>
				</div>
			</div>
			<div class="contentright">
				<div class="dtktitle">
					<img src="img/dtktitle.png" />
					<div>
						得分详情
					</div>
				</div>
				<div class="dtkbox">
					<div>客观题得分: <label id="zhuguan"></label></div>
					<div>主观题得分: <label id="keguan"></label></div>
					<div>总分: <label id="zfen"></label></div>
				</div>
				<div id="rightbox">
					<div class="titletype" id="t1" onclick="showright(1)">单选题<span></span></div>
					<div class="tmbox" id="box1">

					</div>
					<div class="titletype" id="t2" onclick="showright(2)">多选题<span></span></div>
					<div class="tmbox" id="box2">
					</div>
					<div class="titletype" id="t3" onclick="showright(3)">判断题<span></span></div>
					<div class="tmbox" id="box3">
					</div>
					<div class="titletype" id="t4" onclick="showright(4)">简答题<span></span></div>
					<div class="tmbox" id="box4">
					</div>
					<div class="titletype" id="t5" onclick="showright(5)">论述题<span></span></div>
					<div class="tmbox" id="box5">
					</div>
					<div class="titletype" id="t6" onclick="showright(6)">材料分析题<span></span></div>
					<div class="tmbox" id="box6">
					</div>
				</div>
				<div class="ksbtnbox">
					<div class="tjda" onclick="backsss()">返回试题列表</div>
				</div>
			</div>
		</div>
		<div class="footer">
			<a target="_blank" style="color:#FFFFFF; font-size: 0.833333rem;display: flex;align-items: center;" href="https://beian.miit.gov.cn/#/Integrated/index" class="ba">陕ICP备05001612号-1<img src="img/ba.png"/>陕公网安备 61040202000395号</a>
		</div>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
			let userinfo = sessionStorage.getItem("userinfo")
			
			// 题型映射
			const typeMap = {
				'0': '单选题',
				'1': '多选题', 
				'2': '判断题',
				'3': '简答题',
				'4': '论述题',
				'5': '材料分析题'
			};
			
			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				
				// 处理用户登录状态
				if (userinfo) {
					let userdata = JSON.parse(userinfo)
					$("#login").hide()
					$("#user").show()
					$("#user").html(userdata.name)
					$("#edit").show()
				} else {
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				
				// 加载导航菜单
				getclass('examination2info.html')
				
				// 检查是否有单个题目的查看参数
				let subjectId = getUrlParam('subjectId')
				if (subjectId) {
					displaySingleSubject(subjectId)
				} else {
					getsjinfo()
				}
			})
			
			// 显示单个题目详情
			function displaySingleSubject(subjectId) {
				// 模拟题目数据（实际应该从接口获取）
				const subjectData = {
					"id": "1229442867219533824",
					"name": "请根据习近平治国理政陕西实践篇的课程撰写不少于800字的体会感受",
					"type": "4",
					"score": "100",
					"difficulty": "中等",
					"createdAt": "2025-04-15T09:29:48.000+00:00",
					"listRecord": [
						{
							"id": "1365025898646081536",
							"studentId": "1298966422248102351",
							"answered": "通过学习习近平治国理政陕西实践篇，我深深感受到了党的领导的重要性和陕西发展的巨大成就。习近平总书记在陕西的实践充分体现了以人民为中心的发展思想，无论是脱贫攻坚、生态环境保护，还是文化传承发展，都始终把人民群众的利益放在首位。\n\n陕西作为革命圣地，承载着深厚的红色文化底蕴。习近平总书记多次强调要传承红色基因，弘扬延安精神，这不仅是对历史的尊重，更是对未来发展的指引。通过学习，我认识到延安精神的精神内核——自力更生、艰苦奋斗、实事求是、为人民服务，这些精神品质在新时代依然具有重要的指导意义。\n\n在生态文明建设方面，陕西秦岭生态环境保护的实践让我印象深刻。'绿水青山就是金山银山'的理念在陕西得到了很好的践行，秦岭违建别墅整治、生态环境修复等举措，体现了生态优先、绿色发展的理念。这告诉我们，发展不能以牺牲环境为代价，要实现人与自然和谐共生。\n\n脱贫攻坚战中，陕西的实践更是可圈可点。精准扶贫、精准脱贫的方略在陕西落地生根，通过产业扶贫、教育扶贫、健康扶贫等多种方式，确保了贫困人口如期脱贫。这体现了我们党全心全意为人民服务的根本宗旨，也展现了社会主义制度的优越性。\n\n作为新时代的青年，我们要从习近平治国理政陕西实践中汲取智慧和力量，坚定理想信念，传承红色基因，在实现中华民族伟大复兴的征程中贡献自己的力量。",
							"comments": "答题内容较为全面，能够结合陕西实际情况进行分析，体现了对习近平治国理政思想的理解。但在理论深度和个人感悟方面还可以进一步加强。",
							"score": "85",
							"results": "良好",
							"createdAt": "2025-04-24T10:06:58.000+00:00"
						}
					]
				};
				
				// 显示题目信息
				$("#subjectInfo").show()
				$("#subjectTitle").text(subjectData.name)
				$("#subjectType").text(typeMap[subjectData.type] || '未知题型')
				$("#subjectScore").text(subjectData.score + '分')
				$("#subjectDifficulty").text(subjectData.difficulty || '未设置')
				$("#subjectCreatedAt").text(formatDate(subjectData.createdAt))
				
				// 显示答题记录
				let recordsHtml = '';
				if (subjectData.listRecord && subjectData.listRecord.length > 0) {
					subjectData.listRecord.forEach((record, index) => {
						const resultClass = record.results === '正确' ? 'result-correct' : 
										   record.results === '错误' ? 'result-wrong' : 'result-partial';
						
						let scoreClass = 'score-zero';
						const score = parseInt(record.score) || 0;
						const totalScore = parseInt(subjectData.score);
						if (score === totalScore) scoreClass = 'score-full';
						else if (score > 0) scoreClass = 'score-partial';
						
						recordsHtml += `
							<div class="answer-section">
								<div class="answer-title">
									学生答案 #${index + 1}
									<span class="result-badge ${resultClass}">${record.results || '未评分'}</span>
									<span class="score-indicator ${scoreClass}">
										${record.score || '0'}分 / ${subjectData.score}分
									</span>
								</div>
								<div class="answer-content">${record.answered || '未作答'}</div>
								<div style="margin-top: 10px; font-size: 0.85rem; color: #6b7280;">
									<strong>答题时间：</strong>${formatDate(record.createdAt)}
								</div>
							</div>
						`;
						
						// 如果有评语，显示评语
						if (record.comments) {
							recordsHtml += `
								<div class="comments-section">
									<div class="comments-title">教师评语</div>
									<div class="comments-content">${record.comments}</div>
								</div>
							`;
						}
					});
				} else {
					recordsHtml = `
						<div class="answer-section">
							<div class="answer-title">暂无答题记录</div>
							<div class="answer-content">该题目尚未有学生作答</div>
						</div>
					`;
				}
				
				$("#answerRecords").html(recordsHtml)
				
				// 隐藏原有的试卷解析界面
				$(".contentleft, .contentright").hide()
			}
			
			// 格式化日期
			function formatDate(dateString) {
				if (!dateString) return '未知';
				const date = new Date(dateString);
				return date.toLocaleString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit'
				});
			}
			
			function backsss(){
				history.back()
			}
			let thisdata = null //当前显示的 题目
			let alldata = null //所有题目

			let indextm = 1 //题号
			let datanum = 0//当前题目集合的长度
			let thismydata = null //当前题目集合


			let data1 = [] //单选
			let data2 = [] //多选
			let data3 = [] //判断
			let data4 = [] //简答
			let data5 = [] //论述
			let data6 = [] //材料分析


			function getsjinfo() {
				let id = getUrlParam('id')
				let taskid = getUrlParam('taskid')
				
				$.ajax({
					url: baseurl + "/paper/analysis",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data:{
						id: id,
						taskId: taskid
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							alldata = res.data.cmsTestPaper.cmsSubjectList
							let data = res.data.cmsTestPaper
							data.cmsSubjectList.map((item) => {
								if (item.type == '0') {
									data1.push(item)
								} else if (item.type == '1') {
									data2.push(item)
								} else if (item.type == '2') {
									data3.push(item)
								} else if (item.type == '3') {
									data4.push(item)
								} else if (item.type == '4') {
									data5.push(item)
								} else if (item.type == '5') {
									data6.push(item)
								}
							})
							$("#zfen").html(parseInt(res.data.score)+'分')
							$("#keguan").html(parseInt(res.data.keguanTotalScore)+'分')
							$("#zhuguan").html(parseInt(res.data.zhuguanScore)+'分')
							
							$("#papername").html(data.name)
							$("#paperxk").html(res.data.subjectName)
							$("#papertime").html(data.limitedTime)
							$("#papersc").html(data.score)
							$("#papersc2").html(data.passedScore)
						
							showright(1)
						}
					}
				})
			}

			function showright(num) { //显示右侧内容
				indextm = 0
				$("#box1").attr("style", "display: none;")
				$("#box2").attr("style", "display: none;")
				$("#box3").attr("style", "display: none;")
				$("#box4").attr("style", "display: none;")
				$("#box5").attr("style", "display: none;")
				$("#box6").attr("style", "display: none;")
				$("#box" + num).attr("style", "display: flex;")
				$("#t1").attr("class", "titletype")
				$("#t2").attr("class", "titletype")
				$("#t3").attr("class", "titletype")
				$("#t4").attr("class", "titletype")
				$("#t5").attr("class", "titletype")
				$("#t6").attr("class", "titletype")
				$("#t" + num).attr("class", "titletype titletypeactive")
				if (num == '1') thisdata = data1[0]
				else if (num == '2') thisdata = data2[0]
				else if (num == '3') thisdata = data3[0]
				else if (num == '4') thisdata = data4[0]
				else if (num == '5') thisdata = data5[0]
				else if (num == '6') thisdata = data6[0]


				showsss()

				//根据num显示右侧数据
				let showdata = null
				if (num == "1") {
					showdata = data1
					$("#sjname").html('单选题(共' + data1.length + '题)')
				} else if (num == "2") {
					showdata = data2
					$("#sjname").html('多选题(共' + data2.length + '题)')
				} else if (num == "3") {
					showdata = data3
					$("#sjname").html('判断题(共' + data3.length + '题)')
				} else if (num == "4") {
					showdata = data4
					$("#sjname").html('简答题(共' + data4.length + '题)')
				} else if (num == "5") {
					showdata = data5
					$("#sjname").html('论述题(共' + data5.length + '题)')
				} else if (num == "6") {
					showdata = data6
					$("#sjname").html('材料分析题(共' + data6.length + '题)')
				}
				datanum = showdata.length
				thismydata = showdata
				let showhtml = ""
				showdata.map((item, index) => {
					if (item.listRecord[0].results == '正确') {
						showhtml +=
							'<a class="zhengque" onclick="showsssssssss(this)" data-num="'+(index)+'" data-id="'+item.id+'"><label>' +
							(index + 1) + '</label></a>'
					} else {
						showhtml += '<a class="cuowu" onclick="showsssssssss(this)" data-num="'+(index)+'" data-id="'+item.id+'"><label>' +
							(index +
								1) + '</label></a>'
					}
				})
				$("#box" + num).html(showhtml)
			}
			function showsssssssss(item){
				let id = $(item).attr("data-id")
				indextm = $(item).attr("data-num")
				alldata.map((item)=>{
					if(id === item.id){
						thisdata = item
					}
				})
				showsss()
			}
			function showsss() {
				console.log(thisdata)
				if(!thisdata){
					return
				}
				$("#showth").html('【第' + (parseInt(indextm)+1) + '题】')
				$("#showtm").html(thisdata.name+'（'+thisdata.score+'分）')
				if(thisdata.type == '0'){
					let tmtmtmttm = ''
					thisdata.cmsSubjectOption.map((item)=>{
						if(item.keyword == thisdata.listRecord[0].answered){
							tmtmtmttm+=`<div class="da daactive"><span></span>${item.keyword+'. '+item.name}</div>`
						}else{
							tmtmtmttm+=`<div class="da"><span></span>${item.keyword+'. '+item.name}</div>`
						}
					})
					if(thisdata.knowledge!=null){
						$("#jxstr").html('<div style="margin-top: 15px;"><label>相关知识点</label>'+thisdata.knowledge+'</div>')
					}else{
						$("#jxstr").html('')
					}
					
					$("#showxx").html(tmtmtmttm)
					$("#zqda").html(thisdata.answer)
					if(thisdata.listRecord[0].results=='错误')
					$("#ksda").html(thisdata.listRecord[0].answered+'<img class="tmsss" src="img/tmfalse.png" />')
					else
					$("#ksda").html(thisdata.listRecord[0].answered+'<img class="tmsss" src="img/tmtrue.png" />')
					$("#tops").attr("style","display: flex")
				}else if(thisdata.type == '1'){
					//多选题
					let tmtmtmttm = ''
					thisdata.cmsSubjectOption.map((item)=>{
						if(thisdata.listRecord[0].answered.indexOf(item.keyword)!=-1){
							tmtmtmttm+=`<div class="da daactive"><span></span>${item.keyword+'. '+item.name}</div>`
						}else{
							tmtmtmttm+=`<div class="da"><span></span>${item.keyword+'. '+item.name}</div>`
						}
					})
					if(thisdata.knowledge!=null){
						$("#jxstr").html('<div style="margin-top: 15px;"><label>相关知识点</label>'+thisdata.knowledge+'</div>')
					}else{
						$("#jxstr").html('')
					}
					$("#showxx").html(tmtmtmttm)
					let sssttrr = ''
					thisdata.answer.split(',').forEach((item)=>{
						if(item!=''){
							sssttrr+=item
						}
					})
					$("#zqda").html(sssttrr)
					
					let sssttrr2 = ''
					thisdata.listRecord[0].answered.split(',').forEach((item)=>{
						if(item!=''){
							sssttrr2+=item
						}
					})
					
					if(thisdata.listRecord[0].results=='错误')
					$("#ksda").html(sssttrr2+'<img class="tmsss" src="img/tmfalse.png" />')
					else
					$("#ksda").html(sssttrr2+'<img class="tmsss" src="img/tmtrue.png" />')
					$("#tops").attr("style","display: flex")
				}else if(thisdata.type == '2'){
					//判断题
					let tmtmtmttm = ''
					if(thisdata.listRecord[0].answered == 'true'){
						tmtmtmttm = '<div class="da daactive"><span></span>正确</div><div class="da"><span></span>错误</div>'
					}else{
						tmtmtmttm = '<div class="da"><span></span>正确</div><div class="da daactive"><span></span>错误</div>'
					}
					if(thisdata.knowledge!=null){
						$("#jxstr").html('<div style="margin-top: 15px;"><label>相关知识点</label>'+thisdata.knowledge+'</div>')
					}else{
						$("#jxstr").html('')
					}
					$("#showxx").html(tmtmtmttm)
					$("#zqda").html(thisdata.answer=='true'?'正确':'错误')
					if(thisdata.listRecord[0].answered=='false')
					$("#ksda").html('错误<img class="tmsss" src="img/tmfalse.png" />')
					else
					$("#ksda").html('正确<img class="tmsss" src="img/tmtrue.png" />')
					$("#tops").attr("style","display: flex")
				}else if(thisdata.type == '3'){
					$("#showxx").html('')
					$("#jxstr").html('<div><label>考生答案</label>'+thisdata.listRecord[0].answered+'</div>')
					let jxs = thisdata.answer==null?'无':thisdata.answer
					$("#jxstr").append('<div style="margin-top: 15px;"><label>题目解析</label>'+jxs+'</div>')
					if(thisdata.knowledge!=null){
						$("#jxstr").append('<div style="margin-top: 15px;"><label>相关知识点</label>'+thisdata.knowledge+'</div>')
					}else{
						$("#jxstr").append('')
					}
					$("#tops").attr("style","display: none")
				}else if(thisdata.type == '4'){
					let jxs = thisdata.answer==null?'无':thisdata.answer
					$("#showxx").html('')
					$("#jxstr").html('<div><label>考生答案</label>'+thisdata.listRecord[0].answered+'</div>')
					$("#jxstr").append('<div style="margin-top: 15px;"><label>题目解析</label>'+jxs+'</div>')
					if(thisdata.knowledge!=null){
						$("#jxstr").append('<div style="margin-top: 15px;"><label>相关知识点</label>'+thisdata.knowledge+'</div>')
					}else{
						$("#jxstr").append('')
					}
					$("#tops").attr("style","display: none")
				}else if(thisdata.type == '5'){
					let jxs = thisdata.answer==null?'无':thisdata.answer
					$("#showxx").html('')
					$("#jxstr").html('<div><label>考生答案</label>'+thisdata.listRecord[0].answered+'</div>')
					$("#jxstr").append('<div style="margin-top: 15px;"><label>题目解析</label>'+jxs+'</div>')
					if(thisdata.knowledge!=null){
						$("#jxstr").append('<div style="margin-top: 15px;"><label>相关知识点</label>'+thisdata.knowledge+'</div>')
					}else{
						$("#jxstr").append('')
					}
					$("#tops").attr("style","display: none")
				}
				
			}
			function syt(){
				if(0<indextm){
					
					indextm-=1
					console.log(indextm,datanum)
					thisdata = thismydata[indextm]
					showsss()
				}
			}
			function xyt(){
				if(indextm<datanum-1){
					
					indextm+=1
					console.log(indextm,datanum)
					thisdata = thismydata[indextm]
					showsss()
				}
			}
			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
