<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>书籍数据调试 - 思政一体化平台</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        
        .content {
            padding: 20px;
        }
        
        .section {
            margin-bottom: 30px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            overflow: hidden;
        }
        
        .section-title {
            background: #f8f9fa;
            padding: 15px 20px;
            font-weight: 600;
            color: #333;
            border-bottom: 1px solid #e0e0e0;
        }
        
        .section-content {
            padding: 20px;
        }
        
        .json-display {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            word-break: break-all;
            max-height: 400px;
            overflow-y: auto;
        }
        
        .info-item {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 0;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .info-item:last-child {
            border-bottom: none;
        }
        
        .info-label {
            font-weight: 600;
            color: #333;
            min-width: 120px;
        }
        
        .info-value {
            flex: 1;
            color: #666;
            word-break: break-all;
        }
        
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
        }
        
        .status.warning {
            background: #fff3cd;
            color: #856404;
        }
        
        .btn {
            display: inline-block;
            padding: 10px 20px;
            background: #c00714;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 14px;
            margin: 5px;
            border: none;
            cursor: pointer;
        }
        
        .btn.secondary {
            background: #6c757d;
        }
        
        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .file-list {
            list-style: none;
            padding: 0;
        }
        
        .file-item {
            display: flex;
            align-items: center;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 4px;
            margin-bottom: 8px;
        }
        
        .file-icon {
            margin-right: 10px;
            font-size: 18px;
        }
        
        .file-info {
            flex: 1;
        }
        
        .file-name {
            font-weight: 500;
            color: #333;
        }
        
        .file-path {
            font-size: 12px;
            color: #666;
            font-family: monospace;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📚 书籍数据调试工具</h1>
            <p>查看书籍的详细数据结构和文件信息</p>
        </div>
        
        <div class="content">
            <div id="loadingArea" class="loading">
                <div class="loading-spinner"></div>
                <p>正在加载书籍数据...</p>
            </div>
            
            <div id="contentArea" style="display: none;">
                <!-- 基本信息 -->
                <div class="section">
                    <div class="section-title">📋 基本信息</div>
                    <div class="section-content" id="basicInfo">
                        <!-- 动态生成 -->
                    </div>
                </div>
                
                <!-- 文件信息 -->
                <div class="section">
                    <div class="section-title">📁 文件信息</div>
                    <div class="section-content" id="fileInfo">
                        <!-- 动态生成 -->
                    </div>
                </div>
                
                <!-- PDF检测结果 -->
                <div class="section">
                    <div class="section-title">🔍 PDF检测结果</div>
                    <div class="section-content" id="pdfDetection">
                        <!-- 动态生成 -->
                    </div>
                </div>
                
                <!-- 完整数据 -->
                <div class="section">
                    <div class="section-title">🗂️ 完整JSON数据</div>
                    <div class="section-content">
                        <div class="json-display" id="jsonData">
                            <!-- 动态生成 -->
                        </div>
                    </div>
                </div>
                
                <!-- 操作按钮 -->
                <div style="text-align: center; margin-top: 30px;">
                    <button class="btn" onclick="testPDFViewer()">测试PDF查看器</button>
                    <button class="btn secondary" onclick="goBack()">返回上一页</button>
                    <a href="../index.html" class="btn secondary">返回首页</a>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    
    <script>
        let bookData = null;
        let detectedPDFPath = null;
        
        // 获取URL参数
        function getUrlParam(name) {
            const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            const r = window.location.search.substr(1).match(reg);
            return r ? decodeURIComponent(r[2]) : null;
        }
        
        // 返回上一页
        function goBack() {
            window.history.back();
        }
        
        // 测试PDF查看器
        function testPDFViewer() {
            if (detectedPDFPath) {
                const pdfUrl = baseurl + detectedPDFPath;
                const title = bookData.title || '测试PDF';
                window.open(`pdf-viewer.html?url=${encodeURIComponent(pdfUrl)}&title=${encodeURIComponent(title)}`, '_blank');
            } else {
                alert('未检测到PDF文件');
            }
        }
        
        // 检测PDF文件
        function detectPDFFiles(data) {
            const results = [];
            let foundPDF = null;

            // 检查各种可能的字段 - 优先检查PC版使用的字段
            const fieldsToCheck = [
                'attachmentDtoList',  // PC版主要使用的字段
                'attachmentPath',
                'filePath',
                'attachments',
                'attachment',
                'file',
                'files',
                'documents'
            ];
            
            fieldsToCheck.forEach(field => {
                if (data[field]) {
                    const value = data[field];

                    if (Array.isArray(value)) {
                        value.forEach((item, index) => {
                            if (typeof item === 'string' && item.toLowerCase().endsWith('.pdf')) {
                                results.push({
                                    field: field,
                                    index: index,
                                    path: item,
                                    type: 'string'
                                });
                                if (!foundPDF) foundPDF = item;
                            } else if (typeof item === 'object' && item) {
                                // 特殊处理 attachmentDtoList
                                if (field === 'attachmentDtoList') {
                                    const path = item.attachmentPath;
                                    if (path && path.toLowerCase().endsWith('.pdf')) {
                                        results.push({
                                            field: field,
                                            index: index,
                                            path: path,
                                            fileName: item.fileName,
                                            type: 'attachmentDto',
                                            object: item
                                        });
                                        if (!foundPDF) foundPDF = path;
                                    }
                                } else {
                                    // 其他对象类型的处理
                                    const path = item.path || item.url || item.filePath || item.file || item.attachmentPath;
                                    if (path && path.toLowerCase().endsWith('.pdf')) {
                                        results.push({
                                            field: field,
                                            index: index,
                                            path: path,
                                            type: 'object',
                                            object: item
                                        });
                                        if (!foundPDF) foundPDF = path;
                                    }
                                }
                            }
                        });
                    } else if (typeof value === 'string' && value.toLowerCase().endsWith('.pdf')) {
                        results.push({
                            field: field,
                            path: value,
                            type: 'string'
                        });
                        if (!foundPDF) foundPDF = value;
                    }
                }
            });
            
            detectedPDFPath = foundPDF;
            return results;
        }
        
        // 渲染基本信息
        function renderBasicInfo(data) {
            const html = `
                <div class="info-item">
                    <div class="info-label">书籍ID:</div>
                    <div class="info-value">${data.id || '未知'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">标题:</div>
                    <div class="info-value">${data.title || data.titleName || '无标题'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">作者:</div>
                    <div class="info-value">${data.author || '未知'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">内容长度:</div>
                    <div class="info-value">${(data.content || '').length} 字符</div>
                </div>
                <div class="info-item">
                    <div class="info-label">创建时间:</div>
                    <div class="info-value">${data.createTime || data.publishedTime || '未知'}</div>
                </div>
                <div class="info-item">
                    <div class="info-label">点击次数:</div>
                    <div class="info-value">${data.clickCount || 0}</div>
                </div>
            `;
            document.getElementById('basicInfo').innerHTML = html;
        }
        
        // 渲染文件信息
        function renderFileInfo(data) {
            let html = '';
            
            // 检查各种文件字段 - 优先显示PC版使用的字段
            const fileFields = ['attachmentDtoList', 'attachmentPath', 'filePath', 'thumbPath', 'attachments', 'files'];
            
            fileFields.forEach(field => {
                if (data[field]) {
                    html += `<h4>${field}:</h4>`;
                    
                    if (Array.isArray(data[field])) {
                        if (data[field].length > 0) {
                            html += '<ul class="file-list">';
                            data[field].forEach((item, index) => {
                                const isString = typeof item === 'string';
                                let path, fileName, extraInfo = '';

                                if (field === 'attachmentDtoList' && !isString) {
                                    // 特殊处理 attachmentDtoList
                                    path = item.attachmentPath || '未知路径';
                                    fileName = item.fileName || `文件 ${index + 1}`;
                                    extraInfo = ` (文件名: ${item.fileName || '未知'})`;
                                } else {
                                    path = isString ? item : (item.path || item.url || item.filePath || item.attachmentPath || JSON.stringify(item));
                                    fileName = `文件 ${index + 1}`;
                                }

                                const isPDF = path.toLowerCase().endsWith('.pdf');
                                const isImage = /\.(jpg|jpeg|png|gif|webp)$/i.test(path);

                                let icon = '📄';
                                if (isPDF) icon = '📕';
                                else if (isImage) icon = '🖼️';

                                html += `
                                    <li class="file-item">
                                        <div class="file-icon">${icon}</div>
                                        <div class="file-info">
                                            <div class="file-name">${fileName} ${isPDF ? '(PDF)' : ''}${extraInfo}</div>
                                            <div class="file-path">${path}</div>
                                        </div>
                                    </li>
                                `;
                            });
                            html += '</ul>';
                        } else {
                            html += '<p style="color: #999;">数组为空</p>';
                        }
                    } else {
                        html += `<p>${data[field]}</p>`;
                    }
                }
            });
            
            if (!html) {
                html = '<p style="color: #999;">未找到文件信息</p>';
            }
            
            document.getElementById('fileInfo').innerHTML = html;
        }
        
        // 渲染PDF检测结果
        function renderPDFDetection(results) {
            let html = '';
            
            if (results.length > 0) {
                html += `<div class="status success">✓ 检测到 ${results.length} 个PDF文件</div><br>`;
                
                results.forEach((result, index) => {
                    html += `
                        <div class="info-item">
                            <div class="info-label">PDF ${index + 1}:</div>
                            <div class="info-value">
                                <strong>字段:</strong> ${result.field}<br>
                                <strong>路径:</strong> ${result.path}<br>
                                <strong>类型:</strong> ${result.type}
                                ${result.index !== undefined ? `<br><strong>索引:</strong> ${result.index}` : ''}
                            </div>
                        </div>
                    `;
                });
                
                if (detectedPDFPath) {
                    const fullUrl = baseurl + detectedPDFPath;
                    html += `
                        <div style="margin-top: 20px; padding: 15px; background: #e7f3ff; border-radius: 6px;">
                            <strong>推荐使用的PDF URL:</strong><br>
                            <code style="word-break: break-all;">${fullUrl}</code>
                        </div>
                    `;
                }
            } else {
                html = '<div class="status error">✗ 未检测到PDF文件</div>';
            }
            
            document.getElementById('pdfDetection').innerHTML = html;
        }
        
        // 加载书籍数据
        function loadBookData(bookId) {
            $.ajax({
                url: baseurl + "/web/posts/" + bookId,
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: (res) => {
                    document.getElementById('loadingArea').style.display = 'none';
                    document.getElementById('contentArea').style.display = 'block';
                    
                    if (res.code == '200' && res.data) {
                        bookData = res.data;
                        
                        // 渲染各个部分
                        renderBasicInfo(bookData);
                        renderFileInfo(bookData);
                        
                        const pdfResults = detectPDFFiles(bookData);
                        renderPDFDetection(pdfResults);
                        
                        // 显示完整JSON
                        document.getElementById('jsonData').textContent = JSON.stringify(bookData, null, 2);
                        
                        // 更新页面标题
                        document.title = `${bookData.title || '书籍调试'} - 数据调试`;
                    } else {
                        document.getElementById('contentArea').innerHTML = `
                            <div style="text-align: center; padding: 40px; color: #666;">
                                <h3>获取数据失败</h3>
                                <p>${res.message || '未知错误'}</p>
                                <button class="btn" onclick="location.reload()">重新加载</button>
                            </div>
                        `;
                    }
                },
                error: (err) => {
                    console.error('加载书籍数据失败:', err);
                    document.getElementById('loadingArea').style.display = 'none';
                    document.getElementById('contentArea').innerHTML = `
                        <div style="text-align: center; padding: 40px; color: #666;">
                            <h3>网络错误</h3>
                            <p>请检查网络连接后重试</p>
                            <button class="btn" onclick="location.reload()">重新加载</button>
                        </div>
                    `;
                }
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            const bookId = getUrlParam('id');
            if (bookId) {
                loadBookData(bookId);
            } else {
                document.getElementById('loadingArea').innerHTML = `
                    <div style="text-align: center; padding: 40px; color: #666;">
                        <h3>参数错误</h3>
                        <p>缺少书籍ID参数</p>
                        <a href="../index.html" class="btn">返回首页</a>
                    </div>
                `;
            }
        });
    </script>
</body>
</html>
