<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>个人中心 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        /* 个人中心页面专用样式 */
        .profile-header {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            padding: 20px 16px 40px;
            margin-top: 56px;
            position: relative;
        }
        
        .profile-info {
            display: flex;
            align-items: center;
            gap: 16px;
            margin-bottom: 20px;
        }
        
        .profile-avatar {
            width: 80px;
            height: 80px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 32px;
            font-weight: 600;
            border: 3px solid rgba(255, 255, 255, 0.3);
        }
        
        .profile-details {
            flex: 1;
        }
        
        .profile-name {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .profile-id {
            font-size: 14px;
            opacity: 0.8;
            margin-bottom: 8px;
        }
        
        .profile-level {
            background: rgba(255, 255, 255, 0.2);
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            display: inline-block;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 16px;
            margin-top: 20px;
        }
        
        .stat-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 16px;
            text-align: center;
            backdrop-filter: blur(10px);
        }
        
        .stat-number {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 4px;
        }
        
        .stat-label {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .profile-content {
            padding: 16px;
            margin-top: -20px;
            position: relative;
            z-index: 1;
        }
        
        .menu-section {
            background: white;
            border-radius: 12px;
            margin-bottom: 16px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .section-title {
            padding: 16px;
            font-size: 16px;
            font-weight: 600;
            color: #333;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .menu-item {
            display: flex;
            align-items: center;
            padding: 16px;
            text-decoration: none;
            color: #333;
            border-bottom: 1px solid #f8f9fa;
            transition: background 0.3s ease;
        }
        
        .menu-item:last-child {
            border-bottom: none;
        }
        
        .menu-item:active {
            background: #f8f9fa;
        }
        
        .menu-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 12px;
            color: white;
        }
        
        .menu-icon.learning {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        }
        
        .menu-icon.achievement {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
        }
        
        .menu-icon.tasks {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
        }
        
        .menu-icon.settings {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
        }
        
        .menu-icon.help {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
        }
        
        .menu-icon.logout {
            background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
        }
        
        .menu-content {
            flex: 1;
        }
        
        .menu-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 2px;
        }
        
        .menu-subtitle {
            font-size: 12px;
            color: #999;
        }
        
        .menu-arrow {
            color: #ccc;
            font-size: 16px;
        }
        
        .menu-badge {
            background: #c00714;
            color: white;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            margin-left: auto;
            margin-right: 8px;
        }
        
        .login-prompt {
            background: white;
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            margin-bottom: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
        }
        
        .login-prompt-icon {
            width: 80px;
            height: 80px;
            background: #f0f0f0;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            margin: 0 auto 16px;
            color: #999;
            font-size: 32px;
        }
        
        .login-prompt-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .login-prompt-text {
            font-size: 14px;
            color: #666;
            margin-bottom: 20px;
        }
        
        .login-prompt-btn {
            background: #c00714;
            color: white;
            border: none;
            padding: 12px 32px;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .login-prompt-btn:hover {
            background: #a00610;
        }
        
        @media (max-width: 375px) {
            .profile-header {
                padding: 16px 12px 32px;
            }
            
            .profile-content {
                padding: 12px;
            }
            
            .profile-avatar {
                width: 64px;
                height: 64px;
                font-size: 24px;
            }
            
            .profile-name {
                font-size: 20px;
            }
            
            .stats-grid {
                gap: 12px;
            }
            
            .stat-card {
                padding: 12px;
            }
        }
    </style>
</head>
<body class="mobile-profile">
    <!-- 顶部导航栏 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="menu-btn" id="menuBtn">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="logo">
                <h2 style="margin: 0; font-size: 16px; color: #333;">个人中心</h2>
            </div>
            <div class="header-actions">
                <button class="search-btn" id="searchBtn">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                </button>
            </div>
        </div>
    </header>

    <!-- 个人信息头部 -->
    <section class="profile-header" id="profileHeader">
        <!-- 登录状态下显示 -->
        <div class="profile-info" id="profileInfo" style="display: none;">
            <div class="profile-avatar" id="profileAvatar">用</div>
            <div class="profile-details">
                <div class="profile-name" id="profileName">用户名</div>
                <div class="profile-id" id="profileId">学号：202100001</div>
                <div class="profile-level">活跃学员</div>
            </div>
        </div>
        
        <div class="stats-grid" id="statsGrid" style="display: none;">
            <div class="stat-card">
                <div class="stat-number">24</div>
                <div class="stat-label">已学课程</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">156</div>
                <div class="stat-label">学习时长</div>
            </div>
            <div class="stat-card">
                <div class="stat-number">89</div>
                <div class="stat-label">学习积分</div>
            </div>
        </div>
    </section>

    <!-- 主要内容 -->
    <main class="profile-content">
        <!-- 未登录提示 -->
        <div class="login-prompt" id="loginPrompt">
            <div class="login-prompt-icon">👤</div>
            <div class="login-prompt-title">请先登录</div>
            <div class="login-prompt-text">登录后可查看个人学习记录和成就</div>
            <button class="login-prompt-btn" onclick="goToLogin()">立即登录</button>
        </div>

        <!-- 学习相关 -->
        <div class="menu-section" id="learningSection" style="display: none;">
            <div class="section-title">学习中心</div>
            <a href="learning-records.html" class="menu-item">
                <div class="menu-icon learning">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                        <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19Z"/>
                    </svg>
                </div>
                <div class="menu-content">
                    <div class="menu-title">学习记录</div>
                    <div class="menu-subtitle">查看学习历史和进度</div>
                </div>
                <span class="menu-arrow">›</span>
            </a>
            
            <a href="achievements.html" class="menu-item">
                <div class="menu-icon achievement">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                        <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                    </svg>
                </div>
                <div class="menu-content">
                    <div class="menu-title">我的成就</div>
                    <div class="menu-subtitle">查看获得的徽章和证书</div>
                </div>
                <span class="menu-badge">3</span>
                <span class="menu-arrow">›</span>
            </a>
            
            <a href="learning-tasks.html" class="menu-item">
                <div class="menu-icon tasks">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                        <path d="M14 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V8L14 2ZM18 20H6V4H13V9H18V20Z"/>
                    </svg>
                </div>
                <div class="menu-content">
                    <div class="menu-title">学习任务</div>
                    <div class="menu-subtitle">查看待完成的学习任务</div>
                </div>
                <span class="menu-badge">5</span>
                <span class="menu-arrow">›</span>
            </a>
        </div>

        <!-- 设置相关 -->
        <div class="menu-section">
            <div class="section-title">设置</div>
            <a href="settings.html" class="menu-item">
                <div class="menu-icon settings">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                        <path d="M19.14,12.94C19.18,12.64 19.2,12.33 19.2,12C19.2,11.68 19.18,11.36 19.13,11.06L21.16,9.48C21.34,9.34 21.39,9.07 21.28,8.87L19.36,5.55C19.24,5.33 18.99,5.26 18.77,5.33L16.38,6.29C15.93,5.93 15.45,5.64 14.92,5.42L14.5,2.81C14.46,2.57 14.25,2.4 14,2.4H10.08C9.83,2.4 9.62,2.57 9.58,2.81L9.16,5.42C8.63,5.64 8.15,5.93 7.7,6.29L5.31,5.33C5.09,5.26 4.84,5.33 4.72,5.55L2.8,8.87C2.68,9.07 2.73,9.34 2.91,9.48L4.94,11.06C4.89,11.36 4.87,11.68 4.87,12C4.87,12.33 4.89,12.64 4.94,12.94L2.91,14.52C2.73,14.66 2.68,14.93 2.8,15.13L4.72,18.45C4.84,18.67 5.09,18.74 5.31,18.67L7.7,17.71C8.15,18.07 8.63,18.36 9.16,18.58L9.58,21.19C9.62,21.43 9.83,21.6 10.08,21.6H14C14.25,21.6 14.46,21.43 14.5,21.19L14.92,18.58C15.45,18.36 15.93,18.07 16.38,17.71L18.77,18.67C18.99,18.74 19.24,18.67 19.36,18.45L21.28,15.13C21.39,14.93 21.34,14.66 21.16,14.52L19.14,12.94ZM12,15.6C10.02,15.6 8.4,13.98 8.4,12C8.4,10.02 10.02,8.4 12,8.4C13.98,8.4 15.6,10.02 15.6,12C15.6,13.98 13.98,15.6 12,15.6Z"/>
                    </svg>
                </div>
                <div class="menu-content">
                    <div class="menu-title">系统设置</div>
                    <div class="menu-subtitle">个人信息、通知设置等</div>
                </div>
                <span class="menu-arrow">›</span>
            </a>
            
            <a href="help.html" class="menu-item">
                <div class="menu-icon help">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                        <path d="M11,18H13V16H11V18ZM12,2C6.48,2 2,6.48 2,12C2,17.52 6.48,22 12,22C17.52,22 22,17.52 22,12C22,6.48 17.52,2 12,2ZM12,20C7.59,20 4,16.41 4,12C4,7.59 7.59,4 12,4C16.41,4 20,7.59 20,12C20,16.41 16.41,20 12,20ZM12,6C9.79,6 8,7.79 8,10H10C10,8.9 10.9,8 12,8C13.1,8 14,8.9 14,10C14,12 11,11.75 11,15H13C13,12.75 16,12.5 16,10C16,7.79 14.21,6 12,6Z"/>
                    </svg>
                </div>
                <div class="menu-content">
                    <div class="menu-title">帮助中心</div>
                    <div class="menu-subtitle">使用指南、常见问题</div>
                </div>
                <span class="menu-arrow">›</span>
            </a>
            
            <a href="#" class="menu-item" id="logoutBtn" style="display: none;" onclick="performLogout()">
                <div class="menu-icon logout">
                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px;">
                        <path d="M17,7L15.59,8.41L18.17,11H8V13H18.17L15.59,15.59L17,17L22,12L17,7ZM4,5H12V3H4C2.9,3 2,3.9 2,5V19C2,20.1 2.9,21 4,21H12V19H4V5Z"/>
                    </svg>
                </div>
                <div class="menu-content">
                    <div class="menu-title">退出登录</div>
                    <div class="menu-subtitle">安全退出当前账号</div>
                </div>
                <span class="menu-arrow">›</span>
            </a>
        </div>
    </main>

    <!-- 侧边菜单 -->
    <nav class="side-menu" id="sideMenu">
        <div class="menu-overlay" id="menuOverlay"></div>
        <div class="menu-content">
            <div class="menu-header">
                <div class="menu-logo">
                    <img src="../../PC/img/logo.png" alt="思政一体化平台">
                    <h3>思政一体化平台</h3>
                </div>
                <button class="menu-close" id="menuClose">×</button>
            </div>
            <div class="menu-items" id="menuItems">
                <!-- 菜单项将通过JavaScript动态加载 -->
            </div>
        </div>
    </nav>

    <!-- 搜索面板 -->
    <div class="search-panel" id="searchPanel">
        <div class="search-content">
            <div class="search-input-wrapper">
                <input type="text" placeholder="搜索..." id="searchInput">
                <button class="search-submit" id="searchSubmit">搜索</button>
            </div>
            <button class="search-close" id="searchClose">取消</button>
        </div>
    </div>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 4C16.55 4 17 4.45 17 5V9.5L20.5 13L19 14.5L15.5 11H15V5C15 4.45 15.45 4 16 4Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>我的</span>
        </a>
    </nav>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83L16.59 15.41L18 14L12 8L6 14Z"/>
        </svg>
    </button>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            initSearch();
            
            // 检查登录状态并更新UI
            updateProfileUI();
        });

        function updateProfileUI() {
            const loginStatus = checkLoginStatus();
            const loginPrompt = document.getElementById('loginPrompt');
            const profileInfo = document.getElementById('profileInfo');
            const statsGrid = document.getElementById('statsGrid');
            const learningSection = document.getElementById('learningSection');
            const logoutBtn = document.getElementById('logoutBtn');
            
            if (loginStatus.isLoggedIn) {
                // 已登录状态
                loginPrompt.style.display = 'none';
                profileInfo.style.display = 'flex';
                statsGrid.style.display = 'grid';
                learningSection.style.display = 'block';
                logoutBtn.style.display = 'flex';
                
                // 更新用户信息
                const userInfo = loginStatus.userInfo;
                const profileAvatar = document.getElementById('profileAvatar');
                const profileName = document.getElementById('profileName');
                const profileId = document.getElementById('profileId');
                
                if (profileAvatar && userInfo.name) {
                    profileAvatar.textContent = userInfo.name.charAt(0).toUpperCase();
                }
                
                if (profileName && userInfo.name) {
                    profileName.textContent = userInfo.name;
                }
                
                if (profileId && userInfo.identifier) {
                    profileId.textContent = `学号：${userInfo.identifier}`;
                }
            } else {
                // 未登录状态
                loginPrompt.style.display = 'block';
                profileInfo.style.display = 'none';
                statsGrid.style.display = 'none';
                learningSection.style.display = 'none';
                logoutBtn.style.display = 'none';
            }
        }

        function goToLogin() {
            window.location.href = '../login.html';
        }

        function performLogout() {
            if (confirm('确定要退出登录吗？')) {
                logout();
                updateProfileUI();
                MobileUtils.showToast('已退出登录', 'success');
            }
        }
    </script>
</body>
</html>
