<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>虚仿实验空间 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        /* 虚仿实验页面专用样式 */
        .experiment-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 16px;
            margin-top: 56px;
        }
        
        .experiment-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .experiment-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .search-section {
            background: white;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            position: sticky;
            top: 56px;
            z-index: 100;
        }
        
        .search-input-group {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .search-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #f0f0f0;
            border-radius: 8px;
            font-size: 16px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            border-color: #667eea;
            background: white;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .search-btn {
            background: #667eea;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .search-btn:hover {
            background: #5a67d8;
        }
        
        .filter-section {
            background: white;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .filter-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }
        
        .filter-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .filter-tag {
            padding: 8px 16px;
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e5e5e5;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .filter-tag.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .experiment-content {
            padding: 16px;
        }
        
        .experiment-grid {
            display: grid;
            grid-template-columns: 1fr;
            gap: 16px;
        }
        
        .experiment-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }
        
        .experiment-card:active {
            transform: scale(0.98);
        }
        
        .experiment-image {
            width: 100%;
            height: 200px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            position: relative;
            overflow: hidden;
        }
        
        .experiment-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .experiment-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
            color: white;
            padding: 16px;
        }
        
        .experiment-intro {
            font-size: 12px;
            opacity: 0.9;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .experiment-info {
            padding: 16px;
        }
        
        .experiment-name {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .experiment-meta {
            display: flex;
            align-items: center;
            gap: 16px;
            font-size: 12px;
            color: #999;
        }
        
        .meta-item {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .meta-icon {
            width: 14px;
            height: 14px;
            opacity: 0.7;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            gap: 8px;
        }
        
        .page-btn {
            min-width: 36px;
            height: 36px;
            border: 1px solid #e5e5e5;
            background: white;
            color: #666;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .page-btn:hover {
            border-color: #667eea;
            color: #667eea;
        }
        
        .page-btn.active {
            background: #667eea;
            color: white;
            border-color: #667eea;
        }
        
        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }
        
        .empty-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 16px;
            opacity: 0.5;
        }
        
        .empty-title {
            font-size: 16px;
            margin-bottom: 8px;
            color: #666;
        }
        
        .empty-description {
            font-size: 14px;
            line-height: 1.5;
        }
        
        .loading-state {
            text-align: center;
            padding: 40px 20px;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #667eea;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 375px) {
            .experiment-header {
                padding: 16px 12px;
            }
            
            .experiment-content {
                padding: 12px;
            }
            
            .experiment-grid {
                gap: 12px;
            }
            
            .experiment-image {
                height: 160px;
            }
        }
    </style>
</head>
<body class="mobile-experiment">
    <!-- 顶部导航栏 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="menu-btn" id="menuBtn">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="logo">
                <h2 style="margin: 0; font-size: 16px; color: #333;">虚仿实验空间</h2>
            </div>
            <div class="header-actions">
                <div class="user-info" id="userInfo">
                    <button class="login-btn" id="loginBtn">登录</button>
                    <div class="user-avatar" id="userAvatar" style="display: none;">
                        <span id="userName"></span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 实验头部 -->
    <section class="experiment-header">
        <div class="experiment-title">
            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 28px; height: 28px;">
                <path d="M9 2V7H7.5C6.67 7 6 7.67 6 8.5V10H4.5C3.67 10 3 10.67 3 11.5V20.5C3 21.33 3.67 22 4.5 22H19.5C20.33 22 21 21.33 21 20.5V11.5C21 10.67 20.33 10 19.5 10H18V8.5C18 7.67 17.33 7 16.5 7H15V2H9ZM11 4H13V7H11V4ZM8 9H16V10H8V9ZM5 12H19V20H5V12ZM7 14V18H9V14H7ZM11 14V18H13V14H11ZM15 14V18H17V14H15Z"/>
            </svg>
            虚仿实验空间
        </div>
        <div class="experiment-subtitle">沉浸式虚拟实验，探索科学奥秘</div>
    </section>

    <!-- 搜索区域 -->
    <section class="search-section">
        <div class="search-input-group">
            <input type="text" class="search-input" id="searchInput" placeholder="搜索实验项目...">
            <button class="search-btn" onclick="searchExperiments()">搜索</button>
        </div>
    </section>

    <!-- 筛选区域 -->
    <section class="filter-section">
        <div class="filter-title">学科分类</div>
        <div class="filter-tags" id="filterTags">
            <div class="filter-tag active" data-id="0">全部</div>
            <!-- 学科标签将通过JavaScript动态加载 -->
        </div>
    </section>

    <!-- 实验内容 -->
    <main class="experiment-content">
        <div class="experiment-grid" id="experimentGrid">
            <!-- 实验项目将通过JavaScript动态加载 -->
        </div>
        
        <!-- 分页 -->
        <div class="pagination" id="pagination" style="display: none;">
            <!-- 分页按钮将通过JavaScript动态生成 -->
        </div>
    </main>

    <!-- 侧边菜单 -->
    <nav class="side-menu" id="sideMenu">
        <div class="menu-overlay" id="menuOverlay"></div>
        <div class="menu-content">
            <div class="menu-header">
                <div class="menu-logo">
                    <img src="../../PC/img/logo.png" alt="思政一体化平台">
                    <h3>思政一体化平台</h3>
                </div>
                <button class="menu-close" id="menuClose">×</button>
            </div>
            <div class="menu-items" id="menuItems">
                <!-- 菜单项将通过JavaScript动态加载 -->
            </div>
        </div>
    </nav>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 4C16.55 4 17 4.45 17 5V9.5L20.5 13L19 14.5L15.5 11H15V5C15 4.45 15.45 4 16 4Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>我的</span>
        </a>
    </nav>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83L16.59 15.41L18 14L12 8L6 14Z"/>
        </svg>
    </button>

    <script>
        let currentPage = 1;
        let totalPages = 0;
        let currentSubjectId = null;
        let isLoading = false;
        let classdate = null;

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 检查登录状态
            updateLoginUI();
            
            // 加载数据
            loadSubjects();
            loadExperiments();
            getExperimentClass();
        });

        function loadSubjects() {
            $.ajax({
                url: baseurl + "/web/project/section/list/tree",
                type: 'GET',
                contentType: "application/json",
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        renderSubjects(res.data);
                    }
                },
                error: (err) => {
                    console.error('加载学科失败:', err);
                }
            });
        }

        function renderSubjects(subjects) {
            const filterTags = document.getElementById('filterTags');
            let html = '<div class="filter-tag active" data-id="0">全部</div>';
            
            subjects.forEach(subject => {
                html += `<div class="filter-tag" data-id="${subject.id}">${subject.name}</div>`;
            });
            
            filterTags.innerHTML = html;
            
            // 绑定点击事件
            filterTags.addEventListener('click', function(e) {
                if (e.target.classList.contains('filter-tag')) {
                    // 移除所有active类
                    filterTags.querySelectorAll('.filter-tag').forEach(tag => {
                        tag.classList.remove('active');
                    });
                    
                    // 添加active类到当前点击的标签
                    e.target.classList.add('active');
                    
                    // 获取学科ID
                    const subjectId = e.target.getAttribute('data-id');
                    currentSubjectId = subjectId === '0' ? null : subjectId;
                    currentPage = 1;
                    
                    // 重新加载实验
                    loadExperiments();
                }
            });
        }

        function loadExperiments() {
            if (isLoading) return;
            
            isLoading = true;
            const container = document.getElementById('experimentGrid');
            
            if (currentPage === 1) {
                container.innerHTML = `
                    <div class="loading-state">
                        <div class="loading-spinner"></div>
                        <p>加载中...</p>
                    </div>
                `;
            }
            
            const searchName = document.getElementById('searchInput').value.trim() || null;
            
            $.ajax({
                url: baseurl + "/web/virtual/list",
                type: 'GET',
                data: {
                    pageNum: currentPage,
                    pageSize: 10,
                    subjectId: currentSubjectId,
                    titleName: searchName
                },
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        totalPages = res.data.pages;
                        renderExperiments(res.data.list || []);
                        renderPagination();
                    } else {
                        renderEmptyState();
                    }
                    isLoading = false;
                },
                error: (err) => {
                    console.error('加载实验失败:', err);
                    renderEmptyState();
                    isLoading = false;
                }
            });
        }

        function renderExperiments(experiments) {
            const container = document.getElementById('experimentGrid');
            
            if (!experiments || experiments.length === 0) {
                renderEmptyState();
                return;
            }
            
            let html = '';
            experiments.forEach(experiment => {
                html += `
                    <a href="${experiment.linkAdress}" target="_blank" class="experiment-card" onclick="recordLearning(this)" data-id="${experiment.id}">
                        <div class="experiment-image">
                            <img src="${baseurl}${experiment.covertPath}" alt="${experiment.titleName}" onerror="this.style.display='none'">
                            <div class="experiment-overlay">
                                <div class="experiment-intro">${experiment.introduction || '探索虚拟实验的奥秘'}</div>
                            </div>
                        </div>
                        <div class="experiment-info">
                            <div class="experiment-name">${experiment.titleName}</div>
                            <div class="experiment-meta">
                                <div class="meta-item">
                                    <svg class="meta-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                                    </svg>
                                    <span>${experiment.schoolName}</span>
                                </div>
                                <div class="meta-item">
                                    <svg class="meta-icon" viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
                                    </svg>
                                    <span>${experiment.principal}</span>
                                </div>
                            </div>
                        </div>
                    </a>
                `;
            });
            
            container.innerHTML = html;
        }

        function renderEmptyState() {
            const container = document.getElementById('experimentGrid');
            container.innerHTML = `
                <div class="empty-state">
                    <svg class="empty-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zM13 17h-2v-6h2v6zm0-8h-2V7h2v2z"/>
                    </svg>
                    <div class="empty-title">暂无实验项目</div>
                    <div class="empty-description">请尝试调整筛选条件或搜索关键词</div>
                </div>
            `;
        }

        function renderPagination() {
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                pagination.style.display = 'none';
                return;
            }
            
            pagination.style.display = 'flex';
            
            let html = '';
            
            // 首页按钮
            html += `<button class="page-btn" onclick="goToPage(1)" ${currentPage === 1 ? 'disabled' : ''}>首页</button>`;
            
            // 上一页按钮
            html += `<button class="page-btn" onclick="goToPage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>上一页</button>`;
            
            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                html += `<button class="page-btn ${i === currentPage ? 'active' : ''}" onclick="goToPage(${i})">${i}</button>`;
            }
            
            // 下一页按钮
            html += `<button class="page-btn" onclick="goToPage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>下一页</button>`;
            
            // 尾页按钮
            html += `<button class="page-btn" onclick="goToPage(${totalPages})" ${currentPage === totalPages ? 'disabled' : ''}>尾页</button>`;
            
            pagination.innerHTML = html;
        }

        function goToPage(page) {
            if (page < 1 || page > totalPages || page === currentPage) return;
            
            currentPage = page;
            loadExperiments();
        }

        function searchExperiments() {
            currentPage = 1;
            loadExperiments();
        }

        function recordLearning(element) {
            // 检查classdate是否存在
            if (!classdate) {
                MobileUtils.showToast("数据加载错误，请刷新页面重试", "error");
                return;
            }
            
            // 检查用户是否登录
            const loginStatus = checkLoginStatus();
            if (!loginStatus.isLoggedIn) {
                MobileUtils.showToast("请先登录", "warning");
                return;
            }
            
            const infoId = element.getAttribute('data-id');
            if (!infoId) {
                MobileUtils.showToast("资源ID不存在", "error");
                return;
            }
            
            let json = {
                infoId: infoId,
                categoryId: classdate.id,
                totalInfo: "1",
                positioning: '1',
                progress: "100%",
                learningTime: 60,
                type: classdate.name || "虚仿实验"
            };
            
            $.ajax({
                url: baseurl + "/study/record/add",
                type: 'post',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: JSON.stringify(json),
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        MobileUtils.showToast("学习记录已保存", "success");
                    } else {
                        MobileUtils.showToast("保存学习记录失败: " + (res.msg || "未知错误"), "error");
                    }
                },
                error: (xhr, status, error) => {
                    console.error("学习记录保存失败:", xhr.responseText);
                    MobileUtils.showToast("保存学习记录失败，请稍后重试", "error");
                }
            });
        }

        function getExperimentClass() {
            $.ajax({
                url: baseurl + "/web/category/all",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        for (let i = 0; i < res.data.length; i++) {
                            if (res.data[i].redirectUrl == 'experiment.html') {
                                classdate = res.data[i];
                                break;
                            }
                        }
                    }
                }
            });
        }

        // 搜索框回车事件
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchExperiments();
            }
        });
    </script>
</body>
</html>
