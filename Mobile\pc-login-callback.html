<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>登录处理 - 思政一体化平台</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            margin: 0;
            padding: 20px;
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            padding: 40px 20px;
            text-align: center;
            max-width: 350px;
            width: 100%;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
        }
        
        .logo {
            width: 60px;
            height: 60px;
            background: #c00714;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        
        .title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
        }
        
        .message {
            font-size: 16px;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .success {
            color: #4CAF50;
        }
        
        .error {
            color: #f44336;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #c00714;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 14px;
            margin-top: 20px;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #a00610;
        }
        
        .debug-info {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-top: 20px;
            font-size: 12px;
            color: #666;
            text-align: left;
            max-height: 200px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">思</div>
        <div class="title">登录处理中</div>
        <div id="content">
            <div class="loading-spinner"></div>
            <div class="message">正在处理PC端登录回调...</div>
        </div>
        <div class="debug-info" id="debugInfo" style="display: none;"></div>
    </div>

    <script>
        // 配置
        const CONFIG = {
            baseurl: 'https://szjx.sntcm.edu.cn',
            timeout: 10000
        };

        // 调试日志
        function log(message) {
            const debugInfo = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugInfo.innerHTML += `[${timestamp}] ${message}<br>`;
            debugInfo.scrollTop = debugInfo.scrollHeight;
            debugInfo.style.display = 'block';
            console.log(message);
        }

        // 更新页面内容
        function updateContent(html) {
            document.getElementById('content').innerHTML = html;
        }

        // 显示成功信息
        function showSuccess(message) {
            updateContent(`
                <div class="message success">✓ ${message}</div>
                <div>正在跳转到移动端首页...</div>
            `);
            
            setTimeout(() => {
                window.location.href = 'index.html';
            }, 2000);
        }

        // 显示错误信息
        function showError(message) {
            updateContent(`
                <div class="message error">✗ ${message}</div>
                <a href="login-pc-simulate.html" class="btn">重新登录</a>
                <a href="index.html" class="btn" style="background: #6c757d; margin-left: 10px;">返回首页</a>
            `);
        }

        // 获取URL参数
        function getUrlParam(name) {
            try {
                const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                const r = window.location.search.substr(1).match(reg);
                return r ? decodeURIComponent(r[2]) : null;
            } catch (e) {
                log('获取URL参数失败: ' + e.message);
                return null;
            }
        }

        // 处理PC端登录回调
        async function handlePcLoginCallback() {
            try {
                log('开始处理PC端登录回调');
                
                const ticket = getUrlParam('ticket');
                log('Ticket: ' + (ticket || '无'));
                
                if (!ticket) {
                    // 检查是否已经登录
                    const userinfo = sessionStorage.getItem('userinfo');
                    if (userinfo) {
                        log('用户已登录，跳转到首页');
                        showSuccess('您已登录，正在跳转...');
                        return;
                    } else {
                        log('无ticket且未登录，跳转到登录页面');
                        showError('登录失败，未获取到有效的登录票据');
                        return;
                    }
                }

                // 使用PC端的service URL进行验证
                const serviceUrl = CONFIG.baseurl + '/userinfo.html';
                log('Service URL: ' + serviceUrl);

                updateContent(`
                    <div class="loading-spinner"></div>
                    <div class="message">正在验证登录信息...</div>
                `);

                const response = await fetch(CONFIG.baseurl + '/student/caslogin', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify({
                        ticket: ticket,
                        service: serviceUrl
                    })
                });

                log('登录请求已发送');

                if (!response.ok) {
                    throw new Error(`HTTP ${response.status}: ${response.statusText}`);
                }

                const result = await response.json();
                log('登录响应: ' + JSON.stringify(result));

                if (result.code === '200' && result.data) {
                    // 保存登录信息
                    const token = result.data.scheme + result.data.token;
                    const userInfo = JSON.stringify(result.data.student);
                    
                    sessionStorage.setItem('header', token);
                    sessionStorage.setItem('userinfo', userInfo);
                    
                    log('登录信息已保存');
                    log('用户: ' + (result.data.student.name || result.data.student.realName));
                    
                    showSuccess('登录成功！欢迎 ' + (result.data.student.name || result.data.student.realName));
                } else {
                    log('登录失败: ' + (result.message || '未知错误'));
                    showError('登录失败：' + (result.message || '服务器返回错误'));
                }
            } catch (error) {
                log('登录处理异常: ' + error.message);
                console.error('PC登录回调处理失败:', error);
                
                let errorMessage = '登录处理失败';
                if (error.message.includes('Failed to fetch')) {
                    errorMessage = '网络连接失败，请检查网络';
                } else if (error.message.includes('timeout')) {
                    errorMessage = '请求超时，请稍后重试';
                }
                
                showError(errorMessage);
            }
        }

        // 页面加载完成后处理
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            log('当前URL: ' + window.location.href);
            
            // 延迟一点时间再处理，确保页面完全加载
            setTimeout(() => {
                handlePcLoginCallback();
            }, 500);
        });

        // 全局错误处理
        window.addEventListener('error', function(e) {
            log('页面错误: ' + e.message);
            showError('页面运行出错，请刷新重试');
        });

        // 防止页面被嵌入iframe
        if (window.top !== window.self) {
            window.top.location = window.location;
        }
    </script>
</body>
</html>
