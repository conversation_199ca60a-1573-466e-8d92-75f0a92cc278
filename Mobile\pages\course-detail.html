<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>课程详情 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    <link rel="stylesheet" href="../css/learning-styles.css">
    
    <style>
        .detail-container {
            background: #f5f5f5;
            min-height: 100vh;
            padding-bottom: 80px;
        }

        /* 资源播放区域样式 */
        .resource-player {
            background: #000;
            position: relative;
            width: 100%;
            height: 280px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 20px;
            overflow: hidden;
        }

        @media (max-width: 480px) {
            .resource-player {
                height: 240px;
            }
        }

        .player-content {
            width: 100%;
            height: 100%;
            position: relative;
        }

        .pdf-embed {
            width: 100%;
            height: 100%;
            border: none;
            background: white;
        }

        .doc-viewer {
            width: 100%;
            height: 100%;
            background: white;
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: #666;
        }

        .ppt-viewer {
            width: 100%;
            height: 100%;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            flex-direction: column;
            color: white;
        }

        .video-player {
            width: 100%;
            height: 100%;
            background: #000;
        }

        .placeholder-content {
            text-align: center;
            color: #999;
        }

        .placeholder-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }

        .placeholder-text {
            font-size: 16px;
            margin-bottom: 10px;
        }

        .placeholder-subtitle {
            font-size: 14px;
            opacity: 0.7;
        }

        .play-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(0,0,0,0.5);
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: opacity 0.3s ease;
        }

        .play-button {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.9);
            border: none;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            color: #333;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .play-button:hover {
            background: white;
            transform: scale(1.1);
        }

        .resource-info {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0,0,0,0.7));
            color: white;
            padding: 20px;
            font-size: 14px;
        }

        .resource-title {
            font-weight: 600;
            margin-bottom: 5px;
        }

        .resource-meta {
            opacity: 0.8;
            font-size: 12px;
        }
        
        .detail-header {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            padding: 20px;
            position: relative;
        }
        
        .back-btn {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            cursor: pointer;
        }
        
        .detail-title {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            padding: 0 60px;
        }
        
        .course-info {
            background: white;
            margin: 20px;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .course-cover-section {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .course-cover {
            width: 120px;
            height: 90px;
            border-radius: 8px;
            overflow: hidden;
            flex-shrink: 0;
            background: #f5f5f5;
        }
        
        .course-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .course-meta {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .course-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
        }
        
        .course-teacher {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .course-category {
            display: inline-block;
            background: #007bff;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-bottom: 8px;
        }
        
        .course-stats {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: #999;
        }
        
        .course-rating {
            display: flex;
            align-items: center;
            gap: 5px;
            color: #ffc107;
            font-size: 14px;
        }
        
        .course-description {
            background: white;
            margin: 0 20px 20px;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .description-content {
            color: #666;
            line-height: 1.6;
            font-size: 14px;
        }
        
        .course-chapters {
            background: white;
            margin: 0 20px 20px;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .chapter-item {
            display: flex;
            align-items: center;
            padding: 12px 0;
            border-bottom: 1px solid #f0f0f0;
        }

        .chapter-item:last-child {
            border-bottom: none;
        }

        .chapter-actions {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-left: auto;
        }

        .chapter-actions button {
            transition: all 0.3s ease;
        }

        .chapter-actions button:hover {
            transform: translateY(-1px);
            box-shadow: 0 2px 4px rgba(0,0,0,0.2);
        }
        
        .chapter-number {
            width: 30px;
            height: 30px;
            background: #f8f9fa;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: 600;
            color: #666;
            margin-right: 12px;
        }
        
        .chapter-info {
            flex: 1;
        }
        
        .chapter-title {
            font-size: 14px;
            color: #333;
            margin-bottom: 4px;
        }
        
        .chapter-duration {
            font-size: 12px;
            color: #999;
        }
        
        .action-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            padding: 15px 20px;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #c00714;
            color: white;
        }
        
        .btn-primary:hover {
            background: #a00610;
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #e9ecef;
        }
        
        .btn-secondary:hover {
            background: #e9ecef;
        }
        
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 50vh;
            color: #666;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-container {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }
        
        .error-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .error-message {
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .retry-btn {
            background: #c00714;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="detail-container">
        <!-- 头部 -->
        <div class="detail-header">
            <button class="back-btn" onclick="goBack()">
                ←
            </button>
            <h1 class="detail-title">课程详情</h1>
        </div>
        
        <!-- 内容区域 -->
        <div id="contentArea">
            <div class="loading-container">
                <div class="loading-spinner"></div>
                <p>正在加载课程详情...</p>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons" id="actionButtons" style="display: none;">
            <button class="action-btn btn-secondary" onclick="addToFavorites()">
                ⭐ 收藏
            </button>
            <button class="action-btn btn-primary" onclick="startLearning()">
                🎓 开始学习
            </button>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    
    <script>
        // 获取URL参数
        function getUrlParam(name) {
            const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            const r = window.location.search.substr(1).match(reg);
            return r ? decodeURIComponent(r[2]) : null;
        }
        
        // 返回上一页
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '../index.html';
            }
        }
        
        // 格式化时间
        function formatTime(timeStr) {
            if (!timeStr) return '未知时间';
            try {
                const time = new Date(timeStr);
                return time.toLocaleDateString('zh-CN');
            } catch (e) {
                return timeStr;
            }
        }
        
        // 生成星级评分
        function generateStars(score) {
            const fullStars = Math.floor(score);
            const hasHalfStar = score % 1 >= 0.5;
            let stars = '';
            
            for (let i = 0; i < fullStars; i++) {
                stars += '⭐';
            }
            if (hasHalfStar) {
                stars += '⭐';
            }
            
            return stars || '⭐⭐⭐⭐⭐';
        }
        
        // 加载课程详情
        function loadCourseDetail(courseId) {
            if (!courseId) {
                showError('缺少课程ID参数');
                return;
            }

            console.log('加载课程详情，ID:', courseId);

            // 使用PC端相同的API接口
            $.ajax({
                url: baseurl + "/course/meta/" + courseId,
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: (res) => {
                    console.log('课程详情响应:', res);
                    if (res.code == '200' && res.data) {
                        renderCourseDetail(res.data);
                    } else {
                        showError('获取课程详情失败：' + (res.message || '未知错误'));
                    }
                },
                error: (err) => {
                    console.error('加载课程详情失败:', err);
                    showError('网络错误，请稍后重试');
                }
            });
        }
        
        // 渲染课程详情
        function renderCourseDetail(course) {
            console.log('渲染课程详情数据:', course);

            // 修复封面图片字段处理 - 参考PC端数据结构
            let imageUrl = '../img/course_default.jpg';
            if (course.coverPath && course.coverPath.length > 0) {
                imageUrl = baseurl + course.coverPath[0];
            } else if (course.covertPath) {
                // 兼容旧的拼写错误字段
                imageUrl = baseurl + course.covertPath;
            }

            // 处理评分数据 - PC端使用 listCourseEvaluate
            let score = 4.5;
            if (course.listCourseEvaluate && course.listCourseEvaluate.length > 3) {
                score = parseFloat(course.listCourseEvaluate[3].zh || 4.5);
            } else if (course.score) {
                score = parseFloat(course.score);
            }
            const stars = generateStars(score);
            
            // 处理课程基本信息
            const title = course.title || course.titleName || course.name || '无标题';
            const author = course.author || course.principal || course.teacher || '未知';
            const category = course.attachType || course.type || '课程学习';
            const createTime = course.createTime || course.createdAt || course.publishTime;
            const viewCount = course.view || course.studentCount || course.clickCount || 0;

            // 处理课程介绍
            const introduction = course.introduction || course.content || course.summary || course.description || course.remark || '暂无课程介绍';

            const html = `
                <!-- 资源播放区域 -->
                ${renderResourcePlayer(course)}

                <!-- 课程基本信息 -->
                <div class="course-info">
                    <div class="course-cover-section">
                        <div class="course-cover">
                            <img src="${imageUrl}" alt="${title}" onerror="this.src='../img/course_default.jpg'">
                        </div>
                        <div class="course-meta">
                            <div class="course-title">${title}</div>
                            <div class="course-teacher">讲师：${author}</div>
                            <div class="course-category">${category}</div>
                            <div class="course-rating">
                                <span>${stars}</span>
                                <span>${score.toFixed(1)}</span>
                            </div>
                            <div class="course-stats">
                                <span>📅 ${formatTime(createTime)}</span>
                                <span>👥 ${viewCount}人学习</span>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 课程介绍 -->
                <div class="course-description">
                    <div class="section-title">📋 课程介绍</div>
                    <div class="description-content">
                        ${introduction}
                    </div>
                </div>

                <!-- 课程资源列表 -->
                <div class="course-chapters">
                    <div class="section-title">📚 课程资源</div>
                    ${renderCourseResources(course)}
                </div>
            `;
            
            document.getElementById('contentArea').innerHTML = html;
            document.getElementById('actionButtons').style.display = 'flex';
            
            // 更新页面标题
            document.title = `${title} - 课程详情`;
        }

        // 渲染资源播放器
        function renderResourcePlayer(course) {
            // 检查是否有课程资源
            if (course.cmsResourcesCourseMetaList && course.cmsResourcesCourseMetaList.length > 0) {
                const firstResource = course.cmsResourcesCourseMetaList[0];
                const resourceType = getResourceType(firstResource.attachPath);
                const resourceName = firstResource.attachName || '课程资源';
                const resourcePath = baseurl + firstResource.attachPath;

                return renderPlayerByType(resourceType, resourcePath, resourceName);
            } else {
                // 没有资源时显示默认播放器
                return `
                    <div class="resource-player">
                        <div class="placeholder-content">
                            <div class="placeholder-icon">🎓</div>
                            <div class="placeholder-text">暂无课程资源</div>
                            <div class="placeholder-subtitle">请联系管理员添加课程内容</div>
                        </div>
                    </div>
                `;
            }
        }

        // 根据资源类型渲染播放器
        function renderPlayerByType(type, path, name) {
            switch (type.toLowerCase()) {
                case 'pdf':
                    return `
                        <div class="resource-player">
                            <div class="player-content">
                                <iframe src="https://docs.google.com/viewer?url=${encodeURIComponent(path)}&embedded=true"
                                        class="pdf-embed"
                                        frameborder="0">
                                </iframe>
                                <div class="play-overlay" onclick="openFullPDF('${path}', '${name}')">
                                    <div class="play-button">📕</div>
                                </div>
                                <div class="resource-info">
                                    <div class="resource-title">${name}</div>
                                    <div class="resource-meta">PDF文档 • 点击全屏查看</div>
                                </div>
                            </div>
                        </div>
                    `;

                case 'doc':
                case 'docx':
                    return `
                        <div class="resource-player">
                            <div class="doc-viewer">
                                <div class="placeholder-icon">📄</div>
                                <div class="placeholder-text">${name}</div>
                                <div class="placeholder-subtitle">Word文档</div>
                                <button onclick="openDocument('${path}', '${name}')"
                                        style="margin-top: 15px; padding: 8px 16px; background: #007bff; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                    📖 打开文档
                                </button>
                            </div>
                            <div class="resource-info">
                                <div class="resource-title">${name}</div>
                                <div class="resource-meta">Word文档 • 点击打开</div>
                            </div>
                        </div>
                    `;

                case 'ppt':
                case 'pptx':
                    return `
                        <div class="resource-player">
                            <div class="ppt-viewer">
                                <div class="placeholder-icon">📊</div>
                                <div class="placeholder-text">${name}</div>
                                <div class="placeholder-subtitle">PowerPoint演示文稿</div>
                                <button onclick="openPresentation('${path}', '${name}')"
                                        style="margin-top: 15px; padding: 8px 16px; background: #d04423; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                    🎯 打开演示文稿
                                </button>
                            </div>
                            <div class="resource-info">
                                <div class="resource-title">${name}</div>
                                <div class="resource-meta">PowerPoint文档 • 点击打开</div>
                            </div>
                        </div>
                    `;

                case 'mp4':
                case 'avi':
                case 'mov':
                    return `
                        <div class="resource-player">
                            <video class="video-player" controls poster="../img/video-poster.jpg">
                                <source src="${path}" type="video/${type}">
                                您的浏览器不支持视频播放。
                            </video>
                            <div class="resource-info">
                                <div class="resource-title">${name}</div>
                                <div class="resource-meta">视频文件 • ${type.toUpperCase()}格式</div>
                            </div>
                        </div>
                    `;

                default:
                    return `
                        <div class="resource-player">
                            <div class="placeholder-content">
                                <div class="placeholder-icon">📄</div>
                                <div class="placeholder-text">${name}</div>
                                <div class="placeholder-subtitle">${type.toUpperCase()}文件</div>
                                <button onclick="downloadResource('${path}', '${name}')"
                                        style="margin-top: 15px; padding: 8px 16px; background: #28a745; color: white; border: none; border-radius: 4px; cursor: pointer;">
                                    📥 下载文件
                                </button>
                            </div>
                            <div class="resource-info">
                                <div class="resource-title">${name}</div>
                                <div class="resource-meta">${type.toUpperCase()}文件 • 点击下载</div>
                            </div>
                        </div>
                    `;
            }
        }

        // 打开PDF全屏查看
        function openFullPDF(pdfUrl, title) {
            window.location.href = `pdf-viewer.html?url=${encodeURIComponent(pdfUrl)}&title=${encodeURIComponent(title)}`;
        }

        // 打开文档
        function openDocument(docUrl, title) {
            // 尝试使用Office Online查看器
            const officeViewerUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(docUrl)}`;
            window.open(officeViewerUrl, '_blank');
        }

        // 打开演示文稿
        function openPresentation(pptUrl, title) {
            // 尝试使用Office Online查看器
            const officeViewerUrl = `https://view.officeapps.live.com/op/embed.aspx?src=${encodeURIComponent(pptUrl)}`;
            window.open(officeViewerUrl, '_blank');
        }

        // 下载资源
        function downloadResource(resourceUrl, fileName) {
            const link = document.createElement('a');
            link.href = resourceUrl;
            link.download = fileName;
            link.click();
        }

        // 渲染课程资源
        function renderCourseResources(course) {
            let resourcesHtml = '';

            // 检查是否有课程资源列表
            if (course.cmsResourcesCourseMetaList && course.cmsResourcesCourseMetaList.length > 0) {
                // 从第二个资源开始显示（第一个已在播放器中显示）
                const remainingResources = course.cmsResourcesCourseMetaList.slice(1);

                if (remainingResources.length > 0) {
                    remainingResources.forEach((resource, index) => {
                        const resourceType = getResourceType(resource.attachPath);
                        const resourceIcon = getResourceIcon(resourceType);

                        resourcesHtml += `
                            <div class="chapter-item">
                                <div class="chapter-number">${resourceIcon}</div>
                                <div class="chapter-info">
                                    <div class="chapter-title">${resource.attachName || `资源 ${index + 2}`}</div>
                                    <div class="chapter-duration">📄 ${resourceType.toUpperCase()}文件</div>
                                </div>
                                <div class="chapter-actions">
                                    <button onclick="openResource('${resource.attachPath}', '${resource.attachName}', '${resourceType}')"
                                            style="background: #c00714; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; margin-right: 8px; cursor: pointer;">
                                        📺 播放
                                    </button>
                                    <button onclick="openResourceInNewWindow('${resource.attachPath}', '${resource.attachName}', '${resourceType}')"
                                            style="background: #6c757d; color: white; border: none; padding: 6px 12px; border-radius: 4px; font-size: 12px; cursor: pointer;">
                                        🔗 打开
                                    </button>
                                </div>
                            </div>
                        `;
                    });
                } else {
                    resourcesHtml = `
                        <div style="text-align: center; padding: 20px; color: #999;">
                            <div style="font-size: 24px; margin-bottom: 10px;">📚</div>
                            <div>主要资源已在上方播放器中显示</div>
                        </div>
                    `;
                }
            } else {
                // 默认显示一些示例章节
                resourcesHtml = `
                    <div class="chapter-item">
                        <div class="chapter-number">1</div>
                        <div class="chapter-info">
                            <div class="chapter-title">第一章：课程导论</div>
                            <div class="chapter-duration">⏱ 30分钟</div>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">2</div>
                        <div class="chapter-info">
                            <div class="chapter-title">第二章：核心内容</div>
                            <div class="chapter-duration">⏱ 45分钟</div>
                        </div>
                    </div>
                    <div class="chapter-item">
                        <div class="chapter-number">3</div>
                        <div class="chapter-info">
                            <div class="chapter-title">第三章：实践应用</div>
                            <div class="chapter-duration">⏱ 35分钟</div>
                        </div>
                    </div>
                `;
            }

            return resourcesHtml;
        }

        // 获取资源类型
        function getResourceType(filePath) {
            if (!filePath) return 'unknown';
            const extension = filePath.toLowerCase().split('.').pop();
            return extension || 'unknown';
        }

        // 获取资源图标
        function getResourceIcon(type) {
            const iconMap = {
                'pdf': '📕',
                'doc': '📄',
                'docx': '📄',
                'ppt': '📊',
                'pptx': '📊',
                'xls': '📈',
                'xlsx': '📈',
                'mp4': '🎥',
                'avi': '🎥',
                'mp3': '🎵',
                'wav': '🎵',
                'jpg': '🖼️',
                'jpeg': '🖼️',
                'png': '🖼️',
                'gif': '🖼️'
            };
            return iconMap[type] || '📄';
        }

        // 打开资源 - 修改为切换播放器内容
        function openResource(resourcePath, resourceName, resourceType) {
            if (!resourcePath) {
                alert('资源路径无效');
                return;
            }

            const fullUrl = baseurl + resourcePath;

            // 更新播放器内容
            const playerContainer = document.querySelector('.resource-player');
            if (playerContainer) {
                const newPlayerHtml = renderPlayerByType(resourceType, fullUrl, resourceName);
                playerContainer.outerHTML = newPlayerHtml;
            }

            // 滚动到播放器顶部
            document.querySelector('.resource-player').scrollIntoView({
                behavior: 'smooth',
                block: 'start'
            });
        }

        // 原始的打开资源功能（用于新窗口打开）
        function openResourceInNewWindow(resourcePath, resourceName, resourceType) {
            if (!resourcePath) {
                alert('资源路径无效');
                return;
            }

            const fullUrl = baseurl + resourcePath;

            if (resourceType === 'pdf') {
                // 跳转到PDF查看器
                window.location.href = `pdf-viewer.html?url=${encodeURIComponent(fullUrl)}&title=${encodeURIComponent(resourceName)}`;
            } else {
                // 其他类型文件直接下载或在新窗口打开
                window.open(fullUrl, '_blank');
            }
        }

        // 显示错误信息
        function showError(message) {
            const html = `
                <div class="error-container">
                    <div class="error-icon">🎓</div>
                    <div class="error-message">${message}</div>
                    <button class="retry-btn" onclick="location.reload()">重新加载</button>
                </div>
            `;
            document.getElementById('contentArea').innerHTML = html;
        }
        
        // 添加到收藏
        function addToFavorites() {
            // 这里可以添加收藏功能的实现
            alert('收藏功能开发中...');
        }
        
        // 开始学习
        function startLearning() {
            const courseId = getUrlParam('id');
            if (courseId) {
                // 跳转到学习页面
                window.location.href = `learning.html?id=${courseId}&type=course`;
            } else {
                alert('无法获取课程信息');
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            const courseId = getUrlParam('id');
            loadCourseDetail(courseId);
        });
    </script>
</body>
</html>
