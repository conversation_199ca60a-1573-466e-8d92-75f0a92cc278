<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>用户登录处理 - 思政一体化平台</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            color: #333;
        }
        
        .container {
            text-align: center;
            padding: 40px 20px;
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            max-width: 350px;
            width: 100%;
            margin: 20px;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            background: #c00714;
            border-radius: 50%;
            margin: 0 auto 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        
        .title {
            font-size: 20px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 20px auto;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .message {
            font-size: 16px;
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .success {
            color: #4CAF50;
        }
        
        .error {
            color: #f44336;
        }
        
        .countdown {
            font-size: 14px;
            color: #999;
            margin-top: 10px;
        }
        
        .btn {
            display: inline-block;
            padding: 12px 24px;
            background: #c00714;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            font-size: 14px;
            margin-top: 20px;
            transition: background 0.3s ease;
        }
        
        .btn:hover {
            background: #a00610;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="logo">思</div>
        <div class="title">思政一体化平台</div>
        <div id="content">
            <div class="loading-spinner"></div>
            <div class="message">正在处理登录信息...</div>
        </div>
    </div>

    <script>
        // 立即检查并重定向，避免CAS错误
        (function() {
            // 检查是否是从CAS直接跳转过来的
            const urlParams = new URLSearchParams(window.location.search);
            const ticket = urlParams.get('ticket');

            if (!ticket) {
                // 没有ticket，说明是直接访问，重定向到登录选择页面
                console.log('直接访问userinfo-simple.html，重定向到登录选择页面');
                window.location.replace('login-pc-simulate.html');
                return;
            }

            // 有ticket但是URL包含本地地址，说明是错误的CAS回调
            if (window.location.href.includes('127.0.0.1') || window.location.href.includes('localhost')) {
                console.log('检测到本地地址的CAS回调，重定向到PC端模拟登录');
                window.location.replace('login-pc-simulate.html?error=cas_callback_error');
                return;
            }
        })();

        // 配置
        const CONFIG = {
            baseurl: 'https://szjx.sntcm.edu.cn',
            timeout: 10000,
            redirectDelay: 2000
        };

        // 工具函数
        const Utils = {
            // 获取URL参数
            getUrlParam(name) {
                try {
                    const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                    const r = window.location.search.substr(1).match(reg);
                    return r ? decodeURIComponent(r[2]) : null;
                } catch (e) {
                    console.error('获取URL参数失败:', e);
                    return null;
                }
            },

            // 更新页面内容
            updateContent(html) {
                const content = document.getElementById('content');
                if (content) {
                    content.innerHTML = html;
                }
            },

            // 显示加载状态
            showLoading(message = '正在处理...') {
                this.updateContent(`
                    <div class="loading-spinner"></div>
                    <div class="message">${message}</div>
                `);
            },

            // 显示成功信息
            showSuccess(message, redirectUrl = 'index.html', delay = CONFIG.redirectDelay) {
                this.updateContent(`
                    <div class="message success">✓ ${message}</div>
                    <div class="countdown">正在跳转...</div>
                `);
                
                setTimeout(() => {
                    window.location.href = redirectUrl;
                }, delay);
            },

            // 显示错误信息
            showError(message, showRetry = true) {
                let html = `<div class="message error">✗ ${message}</div>`;
                
                if (showRetry) {
                    html += `
                        <div class="countdown">3秒后自动跳转到首页</div>
                        <a href="index.html" class="btn">立即返回首页</a>
                    `;
                    
                    setTimeout(() => {
                        window.location.href = 'index.html';
                    }, 3000);
                }
                
                this.updateContent(html);
            },

            // 简单的AJAX请求
            ajax(options) {
                return new Promise((resolve, reject) => {
                    const xhr = new XMLHttpRequest();
                    xhr.timeout = options.timeout || CONFIG.timeout;
                    
                    xhr.onreadystatechange = function() {
                        if (xhr.readyState === 4) {
                            if (xhr.status >= 200 && xhr.status < 300) {
                                try {
                                    const response = JSON.parse(xhr.responseText);
                                    resolve(response);
                                } catch (e) {
                                    reject(new Error('响应格式错误'));
                                }
                            } else {
                                reject(new Error(`请求失败: ${xhr.status}`));
                            }
                        }
                    };
                    
                    xhr.onerror = () => reject(new Error('网络错误'));
                    xhr.ontimeout = () => reject(new Error('请求超时'));
                    
                    xhr.open(options.method || 'GET', options.url);
                    xhr.setRequestHeader('Content-Type', 'application/json');
                    
                    xhr.send(options.data ? JSON.stringify(options.data) : null);
                });
            }
        };

        // 主要逻辑
        class LoginHandler {
            constructor() {
                this.init();
            }

            async init() {
                try {
                    const ticket = Utils.getUrlParam('ticket');
                    
                    if (ticket) {
                        await this.handleCASLogin(ticket);
                    } else {
                        this.handleNoTicket();
                    }
                } catch (e) {
                    console.error('初始化失败:', e);
                    Utils.showError('页面初始化失败，请刷新重试');
                }
            }

            async handleCASLogin(ticket) {
                try {
                    Utils.showLoading('正在验证登录信息...');
                    
                    const loginData = {
                        ticket: ticket,
                        service: window.location.href.split('?')[0]
                    };

                    const response = await Utils.ajax({
                        url: CONFIG.baseurl + '/student/caslogin',
                        method: 'POST',
                        data: loginData
                    });

                    if (response && response.code === '200' && response.data) {
                        // 保存登录信息
                        sessionStorage.setItem('header', response.data.scheme + response.data.token);
                        sessionStorage.setItem('userinfo', JSON.stringify(response.data.student));
                        
                        Utils.showSuccess('登录成功！欢迎回来');
                    } else {
                        Utils.showError('登录失败：' + (response.message || '未知错误'));
                    }
                } catch (error) {
                    console.error('CAS登录失败:', error);
                    
                    let errorMessage = '登录失败，请稍后重试';
                    if (error.message.includes('超时')) {
                        errorMessage = '网络超时，请检查网络连接';
                    } else if (error.message.includes('网络')) {
                        errorMessage = '网络连接失败，请检查网络';
                    }
                    
                    Utils.showError(errorMessage);
                }
            }

            handleNoTicket() {
                const userinfo = sessionStorage.getItem('userinfo');
                
                if (userinfo) {
                    Utils.showSuccess('您已登录，正在跳转...', 'pages/profile.html', 1000);
                } else {
                    Utils.showSuccess('正在跳转到首页...', 'index.html', 1000);
                }
            }
        }

        // 页面加载完成后启动
        document.addEventListener('DOMContentLoaded', function() {
            new LoginHandler();
        });

        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e);
            Utils.showError('页面运行出错，请刷新重试');
        });

        // 防止页面被嵌入iframe
        if (window.top !== window.self) {
            window.top.location = window.location;
        }
    </script>
</body>
</html>
