<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>总书记的足迹 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        /* 总书记足迹页面专用样式 */
        .footprint-header {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
            padding: 20px 16px;
            margin-top: 56px;
        }
        
        .footprint-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .footprint-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .map-section {
            background: white;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .map-title {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 16px;
        }
        
        .map-title h3 {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin: 0;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .reset-btn {
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e5e5e5;
            padding: 6px 12px;
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .reset-btn:hover {
            background: #e9ecef;
        }
        
        .map-container {
            width: 100%;
            height: 200px;
            background: #f5f5f5;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #999;
            font-size: 14px;
            margin-bottom: 16px;
        }
        
        .city-filter {
            background: white;
            padding: 16px;
            margin-bottom: 16px;
        }
        
        .filter-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }
        
        .city-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 8px;
        }
        
        .city-item {
            padding: 8px 12px;
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e5e5e5;
            border-radius: 6px;
            font-size: 13px;
            text-align: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .city-item.active {
            background: #fa709a;
            color: white;
            border-color: #fa709a;
        }
        
        .footprint-content {
            padding: 16px;
        }
        
        .footprint-list {
            display: flex;
            flex-direction: column;
            gap: 16px;
        }
        
        .footprint-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .footprint-item:active {
            transform: translateY(1px);
            box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
        }
        
        .footprint-date {
            display: flex;
            align-items: center;
            gap: 8px;
            margin-bottom: 12px;
        }
        
        .date-icon {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            font-weight: 600;
        }
        
        .date-text {
            font-size: 14px;
            color: #666;
        }
        
        .footprint-title-text {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
            line-height: 1.4;
        }
        
        .footprint-content-text {
            font-size: 14px;
            color: #666;
            line-height: 1.6;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .footprint-location {
            display: flex;
            align-items: center;
            gap: 4px;
            margin-top: 12px;
            font-size: 12px;
            color: #999;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            gap: 8px;
        }
        
        .page-btn {
            min-width: 36px;
            height: 36px;
            border: 1px solid #e5e5e5;
            background: white;
            color: #666;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .page-btn:hover {
            border-color: #fa709a;
            color: #fa709a;
        }
        
        .page-btn.active {
            background: #fa709a;
            color: white;
            border-color: #fa709a;
        }
        
        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .loading-state {
            text-align: center;
            padding: 40px 20px;
            color: #999;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #fa709a;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }
        
        .empty-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 16px;
            opacity: 0.5;
        }
        
        .empty-title {
            font-size: 16px;
            margin-bottom: 8px;
            color: #666;
        }
        
        .empty-description {
            font-size: 14px;
            line-height: 1.5;
        }
        
        @media (max-width: 375px) {
            .footprint-header {
                padding: 16px 12px;
            }
            
            .footprint-content {
                padding: 12px;
            }
            
            .city-grid {
                grid-template-columns: repeat(2, 1fr);
            }
            
            .footprint-list {
                gap: 12px;
            }
            
            .footprint-item {
                padding: 12px;
            }
        }
    </style>
</head>
<body class="mobile-footprint">
    <!-- 顶部导航栏 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="menu-btn" id="menuBtn">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="logo">
                <h2 style="margin: 0; font-size: 16px; color: #333;">总书记的足迹</h2>
            </div>
            <div class="header-actions">
                <div class="user-info" id="userInfo">
                    <button class="login-btn" id="loginBtn">登录</button>
                    <div class="user-avatar" id="userAvatar" style="display: none;">
                        <span id="userName"></span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 足迹头部 -->
    <section class="footprint-header">
        <div class="footprint-title">
            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 28px; height: 28px;">
                <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
            </svg>
            总书记的足迹
        </div>
        <div class="footprint-subtitle">追寻领袖足迹，感悟思想伟力</div>
    </section>

    <!-- 地图区域 -->
    <section class="map-section">
        <div class="map-title">
            <h3>
                <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px; color: #fa709a;">
                    <path d="M20.5 3l-.16.03L15 5.1 9 3 3.36 4.9c-.21.07-.36.25-.36.48V20.5c0 .28.22.5.5.5l.16-.03L9 18.9l6 2.1 5.64-1.9c.21-.07.36-.25.36-.48V3.5c0-.28-.22-.5-.5-.5zM15 19l-6-2.11V5l6 2.11V19z"/>
                </svg>
                足迹地图 - <span id="currentCity">全部</span>
            </h3>
            <button class="reset-btn" onclick="resetFilter()">
                <svg viewBox="0 0 24 24" fill="currentColor" style="width: 12px; height: 12px;">
                    <path d="M17.65 6.35C16.2 4.9 14.21 4 12 4c-4.42 0-7.99 3.58-7.99 8s3.57 8 7.99 8c3.73 0 6.84-2.55 7.73-6h-2.08c-.82 2.33-3.04 4-5.65 4-3.31 0-6-2.69-6-6s2.69-6 6-6c1.66 0 3.14.69 4.22 1.78L13 11h7V4l-2.35 2.35z"/>
                </svg>
                重置
            </button>
        </div>
        <div class="map-container">
            <span>中国地图（移动端简化版）</span>
        </div>
    </section>

    <!-- 城市筛选 -->
    <section class="city-filter">
        <div class="filter-title">按地区筛选</div>
        <div class="city-grid" id="cityGrid">
            <div class="city-item active" data-city="">全部</div>
            <!-- 城市列表将通过JavaScript动态加载 -->
        </div>
    </section>

    <!-- 足迹内容 -->
    <main class="footprint-content">
        <div class="footprint-list" id="footprintList">
            <!-- 足迹列表将通过JavaScript动态加载 -->
        </div>
        
        <!-- 分页 -->
        <div class="pagination" id="pagination" style="display: none;">
            <!-- 分页按钮将通过JavaScript动态生成 -->
        </div>
    </main>

    <!-- 侧边菜单 -->
    <nav class="side-menu" id="sideMenu">
        <div class="menu-overlay" id="menuOverlay"></div>
        <div class="menu-content">
            <div class="menu-header">
                <div class="menu-logo">
                    <img src="../../PC/img/logo.png" alt="思政一体化平台">
                    <h3>思政一体化平台</h3>
                </div>
                <button class="menu-close" id="menuClose">×</button>
            </div>
            <div class="menu-items" id="menuItems">
                <!-- 菜单项将通过JavaScript动态加载 -->
            </div>
        </div>
    </nav>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 4C16.55 4 17 4.45 17 5V9.5L20.5 13L19 14.5L15.5 11H15V5C15 4.45 15.45 4 16 4Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>我的</span>
        </a>
    </nav>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83L16.59 15.41L18 14L12 8L6 14Z"/>
        </svg>
    </button>

    <script>
        let currentPage = 1;
        let totalPages = 0;
        let pageSize = 10;
        let selectedProvince = null;
        let selectedCity = null;
        let isLoading = false;

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 检查登录状态
            updateLoginUI();
            
            // 加载数据
            loadCityList();
            loadFootprintData();
        });

        function loadCityList() {
            $.ajax({
                url: baseurl + "/web/footprint/city",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        renderCityList(res.data || []);
                    }
                },
                error: (err) => {
                    console.error('加载城市列表失败:', err);
                }
            });
        }

        function renderCityList(cities) {
            const cityGrid = document.getElementById('cityGrid');
            let html = '<div class="city-item active" data-city="" onclick="selectCity(this, null, null)">全部</div>';
            
            cities.forEach(city => {
                html += `<div class="city-item" data-city="${city.city}" onclick="selectCity(this, '${city.province}', '${city.city}')">${city.city}</div>`;
            });
            
            cityGrid.innerHTML = html;
        }

        function selectCity(element, province, city) {
            // 更新选中状态
            document.querySelectorAll('.city-item').forEach(item => {
                item.classList.remove('active');
            });
            element.classList.add('active');
            
            // 更新筛选条件
            selectedProvince = province;
            selectedCity = city;
            currentPage = 1;
            
            // 更新显示的城市名称
            const currentCitySpan = document.getElementById('currentCity');
            currentCitySpan.textContent = city || '全部';
            
            // 重新加载数据
            loadFootprintData();
        }

        function resetFilter() {
            // 重置筛选条件
            selectedProvince = null;
            selectedCity = null;
            currentPage = 1;
            
            // 重置UI
            document.querySelectorAll('.city-item').forEach(item => {
                item.classList.remove('active');
            });
            document.querySelector('.city-item[data-city=""]').classList.add('active');
            
            document.getElementById('currentCity').textContent = '全部';
            
            // 重新加载数据
            loadFootprintData();
        }

        function loadFootprintData() {
            if (isLoading) return;
            
            isLoading = true;
            const container = document.getElementById('footprintList');
            
            if (currentPage === 1) {
                container.innerHTML = `
                    <div class="loading-state">
                        <div class="loading-spinner"></div>
                        <p>加载中...</p>
                    </div>
                `;
            }
            
            const requestData = {
                pageNum: currentPage,
                pageSize: pageSize
            };
            
            if (selectedProvince) {
                requestData.province = selectedProvince;
            }
            if (selectedCity) {
                requestData.city = selectedCity;
            }
            
            $.ajax({
                url: baseurl + "/web/footprint",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: requestData,
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        totalPages = res.data.pages;
                        renderFootprintList(res.data.list || []);
                        renderPagination();
                    } else {
                        renderEmptyState();
                    }
                    isLoading = false;
                },
                error: (err) => {
                    console.error('加载足迹数据失败:', err);
                    renderEmptyState();
                    isLoading = false;
                }
            });
        }

        function renderFootprintList(footprints) {
            const container = document.getElementById('footprintList');
            
            if (!footprints || footprints.length === 0) {
                renderEmptyState();
                return;
            }
            
            let html = '';
            footprints.forEach(item => {
                const date = new Date(item.time);
                const month = String(date.getMonth() + 1).padStart(2, '0');
                const day = String(date.getDate()).padStart(2, '0');
                
                html += `
                    <div class="footprint-item" onclick="viewFootprintDetail('${item.id}')">
                        <div class="footprint-date">
                            <div class="date-icon">${month}/${day}</div>
                            <div class="date-text">${item.time}</div>
                        </div>
                        <div class="footprint-title-text">${item.title}</div>
                        <div class="footprint-content-text">${item.content || '暂无详细内容'}</div>
                        <div class="footprint-location">
                            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 12px; height: 12px;">
                                <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                            </svg>
                            <span>${item.province || ''} ${item.city || ''}</span>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        function renderPagination() {
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                pagination.style.display = 'none';
                return;
            }
            
            pagination.style.display = 'flex';
            
            let html = '';
            
            // 首页按钮
            html += `<button class="page-btn" onclick="goToPage(1)" ${currentPage === 1 ? 'disabled' : ''}>首页</button>`;
            
            // 上一页按钮
            html += `<button class="page-btn" onclick="goToPage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>上一页</button>`;
            
            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                html += `<button class="page-btn ${i === currentPage ? 'active' : ''}" onclick="goToPage(${i})">${i}</button>`;
            }
            
            // 下一页按钮
            html += `<button class="page-btn" onclick="goToPage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>下一页</button>`;
            
            // 尾页按钮
            html += `<button class="page-btn" onclick="goToPage(${totalPages})" ${currentPage === totalPages ? 'disabled' : ''}>尾页</button>`;
            
            pagination.innerHTML = html;
        }

        function goToPage(page) {
            if (page < 1 || page > totalPages || page === currentPage) return;
            
            currentPage = page;
            loadFootprintData();
        }

        function viewFootprintDetail(id) {
            if (id) {
                window.location.href = `footprint-detail.html?id=${id}`;
            }
        }

        function renderEmptyState() {
            const container = document.getElementById('footprintList');
            container.innerHTML = `
                <div class="empty-state">
                    <svg class="empty-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                    </svg>
                    <div class="empty-title">暂无足迹数据</div>
                    <div class="empty-description">请尝试调整筛选条件或稍后再试</div>
                </div>
            `;
            isLoading = false;
        }
    </script>
</body>
</html>
