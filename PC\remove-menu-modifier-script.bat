@echo off
echo 正在移除HTML文件中对menu-modifier.js的引用...
echo 此脚本将在所有HTML文件中查找并替换menu-modifier.js的引用行

REM 使用PowerShell来执行替换，因为它有更强大的文本处理能力
powershell -Command ^
"$files = Get-ChildItem -Path . -Filter *.html -Recurse; ^
foreach ($file in $files) { ^
    $content = Get-Content -Path $file.FullName -Raw; ^
    $pattern = '<script src=\"\./js/menu-modifier\.js\".*?></script>'; ^
    $replacement = '<!-- 已将menu-modifier.js的功能整合到swiper.min.js中 -->'; ^
    $newContent = $content -replace $pattern, $replacement; ^
    if ($content -ne $newContent) { ^
        Set-Content -Path $file.FullName -Value $newContent; ^
        Write-Host ('已更新文件: ' + $file.FullName); ^
    } ^
}"

echo 处理完成！
echo 所有HTML文件中的menu-modifier.js引用已被替换为注释
pause 