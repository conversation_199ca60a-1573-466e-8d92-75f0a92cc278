#!/usr/bin/env python
# -*- coding: utf-8 -*-

import requests
import pandas as pd
import os
import json
import time
from datetime import datetime

# 配置信息
TOKEN = "eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyMTIxMDAyIiwiY3JlYXRlZCI6MTczNTI5MTgwNTQxNywiZXhwIjoxNzUwODQzODA1fQ.NNJxLzbg_oOLwEJOoneW6LpxhQ__SOItVvfuKeDYaA7QdjJ2C0OC-scJrCQBtNejtwdBQmNT6o4fryPRj7EwXg"
BASE_URL = "http://szjx.sntcm.edu.cn/api"
HEADERS = {
    "Authorization": f"Bearer {TOKEN}",
    "Content-Type": "application/json"
}
REQUEST_TIMEOUT = 30  # 请求超时时间（秒）
MAX_RETRIES = 3  # 最大重试次数

# 获取当前工作目录和输出目录
CURRENT_DIR = os.getcwd()
OUTPUT_DIR = os.path.join(CURRENT_DIR, "output")
os.makedirs(OUTPUT_DIR, exist_ok=True)

print("开始抓取数据...")
print(f"数据将保存到: {OUTPUT_DIR}")

# 发送请求的通用函数，包含重试机制
def make_request(url, method="get", params=None, data=None):
    for attempt in range(MAX_RETRIES):
        try:
            if method.lower() == "get":
                response = requests.get(url, headers=HEADERS, params=params, timeout=REQUEST_TIMEOUT)
            else:
                response = requests.post(url, headers=HEADERS, json=data, timeout=REQUEST_TIMEOUT)
            
            response.raise_for_status()  # 如果响应状态码不是200，抛出异常
            return response.json()
        except requests.exceptions.RequestException as e:
            print(f"请求失败 (尝试 {attempt+1}/{MAX_RETRIES}): {str(e)}")
            if attempt < MAX_RETRIES - 1:
                wait_time = 2 ** attempt  # 指数退避策略
                print(f"等待 {wait_time} 秒后重试...")
                time.sleep(wait_time)
            else:
                print(f"达到最大重试次数，请求失败: {url}")
                return None
    return None

# 获取分类信息
def get_categories():
    url = f"{BASE_URL}/web/category/teacher"
    data = make_request(url)
    if data and data.get("code") == "200":
        return data["data"][0]
    return None

# 获取书籍数据
def get_books(category_id, page_size=100):
    url = f"{BASE_URL}/web/posts"
    params = {
        "categoryId": category_id,
        "pageSize": page_size
    }
    
    data = make_request(url, params=params)
    if data and data.get("code") == "200":
        return data["data"]["list"]
    return []

# 获取教学资源数据
def get_teaching_resources(category_id, page_size=100):
    url = f"{BASE_URL}/web/course"
    params = {
        "categoryId": category_id,
        "pageSize": page_size
    }
    
    data = make_request(url, params=params)
    if data and data.get("code") == "200":
        return data["data"]["list"]
    return []

# 处理书籍数据
def process_books(books):
    data = []
    for i, book in enumerate(books, 1):
        # 提取图片路径
        thumb_path = None
        if book.get("thumbPath") and len(book["thumbPath"]) > 0:
            thumb_path = f"{BASE_URL}{book['thumbPath'][0]}" if book["thumbPath"][0].startswith("/") else book["thumbPath"][0]
            
        # 获取更多详情信息（如果需要）
        book_detail = get_book_detail(book.get("id")) if book.get("id") else None
        content = book_detail.get("content", "") if book_detail else book.get("content", "")
            
        data.append({
            "序号": i,
            "书名": book.get("title", ""),
            "作者": book.get("author", ""),
            "出版社": book.get("postName", ""),
            "点击量": book.get("clickCount", 0),
            "简介": content,
            "发布时间": book.get("createTime", ""),
            "图片路径": thumb_path,
            "ID": book.get("id", "")
        })
        
        # 每处理10条数据休息一下，避免请求过于频繁
        if i % 10 == 0:
            print(f"已处理 {i} 条书籍数据")
            time.sleep(1)
    
    return pd.DataFrame(data)

# 获取书籍详情信息
def get_book_detail(book_id):
    url = f"{BASE_URL}/web/posts/{book_id}"
    data = make_request(url)
    if data and data.get("code") == "200":
        return data["data"]
    return {}

# 处理教学资源数据
def process_teaching_resources(resources):
    data = []
    for i, resource in enumerate(resources, 1):
        # 提取封面图片路径
        cover_path = None
        if resource.get("coverPath") and len(resource["coverPath"]) > 0:
            cover_path = f"{BASE_URL}{resource['coverPath'][0]}" if resource["coverPath"][0].startswith("/") else resource["coverPath"][0]
            
        # 获取更多详情信息（如果需要）
        resource_detail = get_resource_detail(resource.get("metaId")) if resource.get("metaId") else None
        introduction = resource_detail.get("introduction", "") if resource_detail else resource.get("introduction", "")
            
        data.append({
            "序号": i,
            "资源名称": resource.get("title", ""),
            "简介": introduction,
            "作者": resource.get("author", ""),
            "点击量": resource.get("view", 0),
            "类型": resource.get("attachType", ""),
            "文件格式": resource.get("attachType", ""),
            "课程ID": resource.get("courceId", ""),
            "资源ID": resource.get("metaId", ""),
            "封面图片": cover_path
        })
        
        # 每处理10条数据休息一下，避免请求过于频繁
        if i % 10 == 0:
            print(f"已处理 {i} 条教学资源数据")
            time.sleep(1)
    
    return pd.DataFrame(data)

# 获取资源详情信息
def get_resource_detail(resource_id):
    url = f"{BASE_URL}/course/meta/{resource_id}"
    data = make_request(url)
    if data and data.get("code") == "200":
        return data["data"]
    return {}

# 主函数
def main():
    try:
        # 获取分类信息
        print("正在获取分类信息...")
        categories = get_categories()
        if not categories:
            print("获取分类信息失败，请检查网络连接或Token是否有效")
            return
        
        print(f"成功获取分类信息: {categories['name']}")
        
        # 获取书籍分类ID (第一个子分类)
        book_category_id = categories["children"][0]["id"]
        book_category_name = categories["children"][0]["name"]
        print(f"书籍分类: {book_category_name}, ID: {book_category_id}")
        
        # 获取教学资源分类ID (第二个子分类)
        teaching_category_id = categories["children"][1]["id"]
        teaching_category_name = categories["children"][1]["name"]
        print(f"教学资源分类: {teaching_category_name}, ID: {teaching_category_id}")
        
        # 获取书籍数据
        print(f"正在获取{book_category_name}数据...")
        books = get_books(book_category_id)
        if not books:
            print(f"未获取到{book_category_name}数据或获取失败")
        else:
            print(f"成功获取{len(books)}条书籍数据")
            
            # 处理书籍数据并保存为Excel
            print("正在处理书籍数据...")
            books_df = process_books(books)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            books_excel_path = os.path.join(OUTPUT_DIR, f"{book_category_name}_{timestamp}.xlsx")
            books_df.to_excel(books_excel_path, index=False, engine="openpyxl")
            print(f"书籍数据已保存至: {books_excel_path}")
        
        # 获取教学资源数据
        print(f"正在获取{teaching_category_name}数据...")
        teaching_resources = get_teaching_resources(teaching_category_id)
        if not teaching_resources:
            print(f"未获取到{teaching_category_name}数据或获取失败")
        else:
            print(f"成功获取{len(teaching_resources)}条教学资源数据")
            
            # 处理教学资源数据并保存为Excel
            print("正在处理教学资源数据...")
            resources_df = process_teaching_resources(teaching_resources)
            timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
            resources_excel_path = os.path.join(OUTPUT_DIR, f"{teaching_category_name}_{timestamp}.xlsx")
            resources_df.to_excel(resources_excel_path, index=False, engine="openpyxl")
            print(f"教学资源数据已保存至: {resources_excel_path}")
        
        print("数据抓取和处理完成!")
        
    except Exception as e:
        print(f"程序运行出错: {str(e)}")

if __name__ == "__main__":
    main() 