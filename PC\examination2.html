<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-思政考核系统</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/examination2.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body class="index">
		<div class="topview2">
			陕西中医药大学思政金课一体化平台在线思政考核系统
		</div>
		<div class="content">
			<div class="contentleft">
				<div class="sjtitle">
					<img src="img/sjtitle.png" />
					<div class="sbag">
						<div class="stitle" id="papername"></div>
						<div class="sbottom">
							<label>学科: <span id="paperxk"></span></label>
							<label>考试时间: <span id="papertime"></span></label>
							<label>总分: <span id="papersc"></span></label>
							<label>及格线: <span id="papersc2"></span></label>
						</div>
					</div>
				</div>
				<div class="title2" id="sjname">

				</div>
				<div class="scrollview1">
					<div id="leftbox">

					</div>
					<!-- <div class="item">
						<div class="itemleft">【第1题】</div>
						<div class="itemright">
							<div class="tm">这是一段题目这是一段题目这是一段题目这是一段题目这是一段题目这是一段题目这是一段题目这是一段题目这是一段题目这是一段题目</div>
							<div class="dabox">
								<textarea placeholder="请输入您的答案"></textarea>
							</div>
						</div>
					</div> -->

					<div class="xxtx">
						<div onclick="xxtx()">继续下一题型</div>
					</div>
				</div>
				<div class="dttime">答题剩余时间<span id="times"></span></div>
			</div>
			<div class="contentright">
				<div class="dtktitle">
					<img src="img/dtktitle.png" />
					<div>
						答题卡
					</div>
				</div>
				<div class="dtkbox">
					<div>姓名: <label id="username"></label></div>
					<div>学号: <label id="userno"></label></div>
					<div>学院: <label id="xy"></label></div>
					<div>专业: <label id="zy"></label></div>
					<div>班级: <label id="bj"></label></div>
				</div>
				<div id="rightbox">
					<div class="titletype" id="t1" onclick="showright(1)">单选题<span></span></div>
					<div class="tmbox" id="box1">
					</div>
					<div class="titletype" id="t2" onclick="showright(2)">多选题<span></span></div>
					<div class="tmbox" id="box2">
					</div>
					<div class="titletype" id="t3" onclick="showright(3)">判断题<span></span></div>
					<div class="tmbox" id="box3">
					</div>
					<div class="titletype" id="t4" onclick="showright(4)">简答题<span></span></div>
					<div class="tmbox" id="box4">
					</div>
					<div class="titletype" id="t5" onclick="showright(5)">论述题<span></span></div>
					<div class="tmbox" id="box5">
					</div>
					<div class="titletype" id="t6" onclick="showright(6)">材料分析题<span></span></div>
					<div class="tmbox" id="box6">
					</div>
				</div>
				<div class="ksbtnbox">
					<div class="tjda" onclick="submits1()">提交答案</div>
					<div class="tckh" onclick="exitks()">退出考核</div>
				</div>
			</div>
		</div>
		<div class="footer">
			<a target="_blank" style="color:#FFFFFF; font-size: 0.833333rem;display: flex;align-items: center;" href="https://beian.miit.gov.cn/#/Integrated/index" class="ba">陕ICP备05001612号-1<img src="img/ba.png"/>陕公网安备 61040202000395号</a>
		</div>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<div id="mesbox1">
			<div class="messdiv">
				<div class="mestitle">
					<img src="img/dtktitle.png" />
					<label>警告</label>
				</div>
				<div class="ppp">
					<p>1.本次考试答卷时间为<span id="sjsj"></span>分钟;</p>					<p>2.每份试卷,用户只有一次答卷机会,中途主动退出,将无法再次答卷,请确认好当前拥有足够的时间用于答卷;</p>					<p>3.如果非人为因素导致答卷终止,请联系管理员或本门代课老师;</p>
					<p>4.开始测试后请不要刷新页面。</p>
				</div>
				<div class="btniwes">
					<label onclick="startint()">开始测试</label>
					<label onclick="history.back()">稍后再来</label>
				</div>
			</div>
		</div>
		
		<div id="mesbox2">
			<div class="messdiv">
				<div class="mestitle">
					<img src="img/dtktitle.png" />
					<label>警告</label>
				</div>
				<div class="ppp">
					<p>1.请确认是否已答题完毕;</p>
					<p>2.每份试卷,用户只有一次答卷机会,提交答案后无法更改;</p>
				</div>
				<div class="btniwes">
					<label onclick="submits()">确认提交</label>
					<label onclick="closediv2()">继续答题</label>
				</div>
			</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			function mdown(item){
				$(item).attr("class","upspan")
			}
			function mup(item){
				setTimeout(()=>{
					$(item).attr("class","")
				},100)
			}
		
		
			window.addEventListener('beforeunload',function(e){
				let conm = ""
				e.returnValue = conm
				return conm
			})
			function closediv2(){
				$("#mesbox2").hide()
			}
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					let userdata = JSON.parse(userinfo)
					$("#username").html(userdata.name)
					$("#userno").html(userdata.userAuth.identifier)
					$("#xy").html(userdata.collegeName)
					$("#zy").html(userdata.majorName)
					$("#bj").html(userdata.className2)
					getsjinfo()
				}
			})
			let data1 = [] //单选
			let data2 = [] //多选
			let data3 = [] //判断
			let data4 = [] //简答
			let data5 = [] //论述
			let data6 = [] //材料分析

			let mydata = []

			let timer = null
			let alltime = null
			let projectId = null
			function getsjinfo() { //获取试卷内试题内容
				$.ajax({
					url: baseurl + "/paper/" + getUrlParam('id'),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							projectId = res.data.projectId
							$("#papername").html(res.data.name)
							$("#paperxk").html(res.data.projectName)
							$("#papertime").html(res.data.limitedTime + "分钟")
							$("#papersc").html(res.data.score + "分")
							$("#papersc2").html(res.data.passedScore + "分")
							$("#sjsj").html(res.data.limitedTime)
							alltime = parseInt(res.data.limitedTime) * 60
							$("#times").html(alltime/60+":00")
							
							if (sessionStorage.getItem("sj" + getUrlParam('id'))) {
								let newdata = JSON.parse(sessionStorage.getItem("sj" + getUrlParam('id')))
								mydata = newdata
								newdata.map((item) => {
									if (item.type == '0') {
										data1.push(item)
									} else if (item.type == '1') {
										data2.push(item)
									} else if (item.type == '2') {
										data3.push(item)
									} else if (item.type == '3') {
										data4.push(item)
									} else if (item.type == '4') {
										data5.push(item)
									} else if (item.type == '5') {
										data6.push(item)
									}
								})
							} else {
								newdata = []
								res.data.cmsSubjectList.map((item) => {
									if (item.type == '0') {
										data1.push(item)
									} else if (item.type == '1') {
										data2.push(item)
									} else if (item.type == '2') {
										data3.push(item)
									} else if (item.type == '3') {
										data4.push(item)
									} else if (item.type == '4') {
										data5.push(item)
									} else if (item.type == '5') {
										data6.push(item)
									}
								})
							}
							showleft(1)
							showright(1)
						}
					}
				})
			}
			
			function startint(){
				$("#mesbox1").hide()
				timer = setInterval(() => {
					if(alltime>0){
						alltime-=1
						var minutes = parseInt(alltime / 60)
						var seconds = (alltime % 60)
						if(parseInt(minutes)<10){
							minutes='0'+minutes
						}
						if(parseInt(seconds)<10){
							seconds='0'+seconds
						}
						$("#times").html(minutes+":"+seconds)
					}else{
						//弹框  无法被关闭  提示是否提交答案  结束定时器
						timer.clear()
					}
				}, 1000)
			}
			
			let indexss = 1
			function xxtx(){
				if(indexss<6){
					indexss+=1
					showleft(indexss)
					showright(indexss)
				}
			}
			function submits1(){
				$("#mesbox2").show()
			}
			function submits() { //提交试卷
				$("#mesbox2").hide()
				let ttid = null
				if(getUrlParam('taskid')){
					ttid = getUrlParam('taskid')
				}
				let json = {
					id: getUrlParam('id'),
					userId: JSON.parse(userinfo).id,
					projectId: projectId,
					cmsSubjectList: [],
					taskId: ttid
				}
				mydata.map((item)=>{
					json.cmsSubjectList.push({
						id: item.id,
						value: item.value,
						type: item.type
					})
				})
				
				// console.log(json)
				$.ajax({
					url: baseurl + "/paper/answer",
					type: 'POST',
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: JSON.stringify(json),
					contentType: "application/json",
					dataType: 'json',
					success: (res) => {
						if(res.code == "200"){
							$.ajax({
								url: baseurl + "/paper/views/"+getUrlParam('id'),
								type: 'get',
								headers: {
									"Authorization": sessionStorage.getItem("header")
								},
								contentType: "application/json",
								dataType: 'json',
								success: (res) => {}
							})
							cocoMessage.success(1000, "答题成功！请等待老师阅卷完成后查看成绩！")
							sessionStorage.removeItem("sj" + getUrlParam('id'))
							setTimeout(function(){
								window.location.replace('examination.html')
							},1000)
						}
					}
				})
			}

			function exitks() { //退出考核
				sessionStorage.removeItem("sj" + getUrlParam('id'))
				history.back()
			}

			function showleft(num) {
				//将试题展示到页面
				//初始显示  type=0 单选题
				let showdata = null
				if (num == "1") {
					showdata = data1
					$("#sjname").html("单选题(共" + showdata.length + "题)")
				} else if (num == "2") {
					showdata = data2
					$("#sjname").html("多选题(共" + showdata.length + "题)")
				} else if (num == "3") {
					showdata = data3
					$("#sjname").html("判断题(共" + showdata.length + "题)")
				} else if (num == "4") {
					showdata = data4
					$("#sjname").html("简答题(共" + showdata.length + "题)")
				} else if (num == "5") {
					showdata = data5
					$("#sjname").html("论述题(共" + showdata.length + "题)")
				} else if (num == "6") {
					showdata = data6
					$("#sjname").html("材料分析题(共" + showdata.length + "题)")
				}
				let showhtml = ''
				showdata.map((item, index) => {
					showhtml += '<div class="item" id="'+item.id+'">' +
						'<div class="itemleft">【第' + (index + 1) + '题】</div>' +
						'<div class="itemright">' +
						'<div class="tm">' + item.name + '</div>' +
						'<div class="dabox">'
					if (num == '1' || num == '2') {
						item.cmsSubjectOption.map((item2) => {
							let userda = "" + item.value + ""
							if (userda.indexOf("" + item2.keyword + "") != -1) {
								showhtml += '<div class="da daactive" onclick="xzda(this)" data-id="' + item.id +
									'" data-key="' + item2.keyword + '" data-type="' + item.type +
									'"><span></span>' + item2.keyword + '.' + item2.name + '</div>'
							} else {
								showhtml += '<div class="da" onclick="xzda(this)" data-id="' + item.id +
									'" data-key="' + item2.keyword + '" data-type="' + item.type +
									'"><span></span>' + item2.keyword + '.' + item2.name + '</div>'
							}
						})
					} else if (num == '3') {
						let userda = item.value
						if (userda == "true") {
							showhtml += '<div class="da daactive" onclick="xzda(this)" data-id="' + item.id +
								'" data-key="true" data-type="' + item.type + '"><span></span>正确</div>' +
								'<div class="da" onclick="xzda(this)" data-id="' + item.id +
								'" data-key="false" data-type="' + item.type + '"><span></span>错误</div>'
						} else if (userda == "false") {
							showhtml += '<div class="da" onclick="xzda(this)" data-id="' + item.id +
								'" data-key="true" data-type="' + item.type + '"><span></span>正确</div>' +
								'<div class="da daactive" onclick="xzda(this)" data-id="' + item.id +
								'" data-key="false" data-type="' + item.type + '"><span></span>错误</div>'
						} else if (userda == null || userda == "") {
							showhtml += '<div class="da" onclick="xzda(this)" data-id="' + item.id +
								'" data-key="true" data-type="' + item.type + '"><span></span>正确</div>' +
								'<div class="da" onclick="xzda(this)" data-id="' + item.id +
								'" data-key="false" data-type="' + item.type + '"><span></span>错误</div>'
						}

					} else if (num == '4' || num == '5' || num == '6') {
						let userda = item.value
						if (userda) {
							showhtml += '<textarea onchange="xzda(this)" data-id="' + item.id + '" data-type="' + item
								.type + '" placeholder="请输入您的答案">' + userda + '</textarea>'
						} else {
							showhtml += '<textarea onchange="xzda(this)" data-id="' + item.id + '" data-type="' + item
								.type + '" placeholder="请输入您的答案"></textarea>'
						}
					}

					showhtml += '</div></div></div>'
				})
				$("#leftbox").html(showhtml)
			}

			function xzda(item) {
				let id = $(item).attr("data-id")
				let d = $(item).attr("data-key")
				let type = $(item).attr("data-type")
				let value = $(item).val()
				if (type == '0') {
					data1.map((itemd) => {
						if (itemd.id == id) {
							let dddd = "" + itemd.value === null ? '' : itemd.value + ""
							if (dddd.indexOf("" + d + "") != -1) {
								itemd.value = ""
							} else {
								itemd.value = d
							}
						}
					})
					showleft(1)
					showright(1)
				} else if (type == '1') {
					data2.map((itemd) => {
						if (itemd.id == id) {
							let dddd = itemd.value
							if(dddd){
								dddd = itemd.value
							}else{
								dddd = ""
							}
							if (dddd.indexOf("" + d + "") != -1) {
								let newda = ""
								dddd.split(',').map((da) => {
									if (da != ''&&da!=null) {
										if (da != d) {
											newda += da + ","
										}
									}
								})
								itemd.value = newda
							} else {
								if(itemd.value){
									itemd.value = itemd.value
								}else{
									itemd.value = ""
								}
								itemd.value += d + ","
							}
						}
					})
					showleft(2)
					showright(2)
				} else if (type == '2') {
					data3.map((itemd) => {
						if (itemd.id == id) {
							if (itemd.value == d) {
								itemd.value = ""
							} else {
								itemd.value = d
							}
						}
					})
					showleft(3)
					showright(3)
				} else if (type == '3') {
					data4.map((itemd) => {
						if (itemd.id == id) {
							itemd.value = value
						}
					})
					showleft(4)
					showright(4)
				} else if (type == '4') {
					data5.map((itemd) => {
						if (itemd.id == id) {
							itemd.value = value
						}
					})
					showleft(5)
					showright(5)
				} else if (type == '5') {
					data6.map((itemd) => {
						if (itemd.id == id) {
							itemd.value = value
						}
					})
					showleft(6)
					showright(6)
				}
				let alldata = []
				data1.map((dd) => {
					alldata.push(dd)
				})
				data2.map((dd) => {
					alldata.push(dd)
				})
				data3.map((dd) => {
					alldata.push(dd)
				})
				data4.map((dd) => {
					alldata.push(dd)
				})
				data5.map((dd) => {
					alldata.push(dd)
				})
				data6.map((dd) => {
					alldata.push(dd)
				})
				mydata = alldata
				sessionStorage.setItem("sj" + getUrlParam('id'), JSON.stringify(alldata))
			}

			function showright(num) { //显示右侧内容
				indexss = num
				$("#box1").attr("style", "display: none;")
				$("#box2").attr("style", "display: none;")
				$("#box3").attr("style", "display: none;")
				$("#box4").attr("style", "display: none;")
				$("#box5").attr("style", "display: none;")
				$("#box6").attr("style", "display: none;")
				$("#box" + num).attr("style", "display: flex;")
				$("#t1").attr("class", "titletype")
				$("#t2").attr("class", "titletype")
				$("#t3").attr("class", "titletype")
				$("#t4").attr("class", "titletype")
				$("#t5").attr("class", "titletype")
				$("#t6").attr("class", "titletype")
				$("#t" + num).attr("class", "titletype titletypeactive")

				//根据num显示右侧数据
				let showdata = null
				if (num == "1") {
					showdata = data1
				} else if (num == "2") {
					showdata = data2
				} else if (num == "3") {
					showdata = data3
				} else if (num == "4") {
					showdata = data4
				} else if (num == "5") {
					showdata = data5
				} else if (num == "6") {
					showdata = data6
				}
				let showhtml = ""
				showdata.map((item, index) => {
					if (item.value) {
						showhtml += '<a href="#'+item.id+'" class="tmactive"><label onmousedown="mdown(this)" onmouseup="mup(this)" onmouseout="mup(this)">' + (index + 1) + '</label></a>' //class="tmactive"
					} else {
						showhtml += '<a href="#'+item.id+'"><label onmousedown="mdown(this)" onmouseup="mup(this)" onmouseout="mup(this)">' + (index + 1) + '</label></a>' //class="tmactive"
					}
				})
				$("#box" + num).html(showhtml)
				showleft(num)
			}
			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
