.yuanbao-float {
    position: fixed;
    right: 30px;
    bottom: 100px;
    width: 60px;
    height: 80px;
    z-index: 999;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: float 3s ease-in-out infinite;
    filter: drop-shadow(0 4px 6px rgba(0, 0, 0, 0.1));
    text-align: center;
    text-decoration: none;
    color: #333;
    font-size: 14px;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.yuanbao-float img {
    width: 60px;
    height: 60px;
    display: block;
    margin-bottom: 4px;
}

.yuanbao-float:hover {
    transform: scale(1.15);
    filter: drop-shadow(0 6px 8px rgba(0, 0, 0, 0.2));
}

@keyframes float {
    0% {
        transform: translateY(0);
    }
    50% {
        transform: translateY(-10px);
    }
    100% {
        transform: translateY(0);
    }
}