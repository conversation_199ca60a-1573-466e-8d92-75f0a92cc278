<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-习主席足迹详情</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/footprint.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<!-- 外部链接检测与重定向脚本 -->
		<script type="text/javascript">
			// 检查内容是否为外部链接，如果是则重定向
			function checkAndRedirectExternalLink(content) {
				if (!content) return false;
				
				try {
					// 使用字符串方法而不是正则表达式来检测外部链接
					const startTag = "<!-- EXTERNAL_LINK_START -->";
					const endTag = "<!-- EXTERNAL_LINK_END -->";
					
					// 检查内容是否包含这两个标记
					if (typeof content === 'string' && content.indexOf(startTag) >= 0 && content.indexOf(endTag) >= 0) {
						const startPos = content.indexOf(startTag) + startTag.length;
						const endPos = content.indexOf(endTag);
						
						if (startPos < endPos) {
							const externalUrl = content.substring(startPos, endPos).trim();
							
							// 确保URL是有效的
							if (externalUrl && (externalUrl.startsWith('http://') || externalUrl.startsWith('https://'))) {
								// 添加加载提示
								document.getElementById('content').innerHTML = '<div style="text-align:center;padding:20px;">正在重定向到外部链接，请稍候...</div>';
								
								console.log("检测到外部链接，即将跳转到：", externalUrl);
								
								// 延迟一点重定向，让用户有时间看到提示
								setTimeout(function() {
									// 重定向到外部链接
									window.location.href = externalUrl;
								}, 800);
								return true;
							}
						}
					}
					return false;
				} catch (error) {
					console.error("检查外部链接时出错:", error);
					return false;
				}
			}
		</script>
	</head>
	<body class="index" onbeforeunload="return tiaozhuan()">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="content">
			<div class="contenttitlebox">
				<div>
					<a id="zxxx" href="footprint.html">总书记的足迹</a>
					<img src="img/jtright.png" />
					<label id="city" class="aa"></label>
				</div>
			</div>
			<div class="contentbox">
				<div class="boxtop">
					<div>
						<div><img src="img/zz.png" />
							<span id="zz"></span></div>
						<!-- <div><img src="img/ly.png" />
							社会科学部</div> -->
					</div>
					<div>
						<div><img src="img/sj2.png" /><span id="date"></span></div>
						<div><img src="img/yj.png" /><span id="view"></span></div>
					</div>
				</div>
				<div class="xqnr">
					<div class="xqnrtitle" id="title"></div>
					<div id="content">
						
					</div>
				</div>
			</div>
		</div>

		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
			// 从本地存储中恢复会话信息的函数
			function restoreSessionFromLocalStorage() {
				// 尝试从备份恢复用户信息
				let userinfo = sessionStorage.getItem("userinfo");
				if (!userinfo) {
					const backupUserInfo = localStorage.getItem("userinfo_backup");
					if (backupUserInfo) {
						console.log("从备份恢复用户信息");
						sessionStorage.setItem("userinfo", backupUserInfo);
						userinfo = backupUserInfo;
						
						// 检查备份的时间戳，如果超过12小时则提醒用户
						const timestamp = localStorage.getItem("userinfo_backup_timestamp");
						if (timestamp) {
							const backupTime = parseInt(timestamp);
							const currentTime = Date.now();
							const hoursDiff = (currentTime - backupTime) / (1000 * 60 * 60);
							
							if (hoursDiff > 12) {
								console.warn("会话备份已超过12小时，可能已过期");
							}
						}
					}
				}
				
				// 尝试从备份恢复授权头
				if (!sessionStorage.getItem("header")) {
					const backupHeader = localStorage.getItem("header_backup");
					if (backupHeader) {
						console.log("从备份恢复授权头");
						sessionStorage.setItem("header", backupHeader);
					}
				}
				
				return userinfo;
			}
			
			// 立即执行会话恢复
			let userinfo = restoreSessionFromLocalStorage();
			let pflid = null;
			let time1 = null;  // 重新定义为全局变量
			
			$(function() {
				// 添加调试信息
				console.log("页面加载状态检查:", {
					有用户信息: !!userinfo,
					Authorization头: !!sessionStorage.getItem("header"),
					当前URL: window.location.href,
					ID参数: getUrlParam('id')
				});
				
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				
				// 判断是否已登录
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				
				// 无论是否登录都尝试获取内容
				fetchFootprintContent();
				
				// 根据当前路径构建相对路径
				const relativePath = 'footprint.html';
				
				// 加载菜单和友情链接
				setTimeout(function() {
					// 延迟加载菜单，确保会话恢复后再加载
					getclass(relativePath);
					getfooterlink();
				}, 100);
			})
			
			// 提取获取足迹内容的函数
			function fetchFootprintContent() {
				const footprintId = getUrlParam('id');
				
				// 检查ID是否存在
				if (!footprintId) {
					$("#title").html("参数错误");
					$("#content").html('<div style="text-align:center;padding:20px;"><p style="font-size:16px;color:#666;">缺少必要的足迹ID参数，请返回列表重新选择</p></div>');
					return;
				}
				
				// 再次尝试恢复会话，确保不会因为新标签页而丢失登录状态
				if (!userinfo) {
					userinfo = restoreSessionFromLocalStorage();
					
					// 在页面上更新登录状态
					if (userinfo) {
						$("#login").hide();
						$("#user").show();
						$("#user").html(JSON.parse(userinfo).name);
						$("#edit").show();
					}
				}
				
				// 构建请求头，如果用户已登录则添加 Authorization 头
				let headers = {};
				const authHeader = sessionStorage.getItem("header");
				
				if (authHeader) {
					console.log("使用会话中的Authorization头");
					headers = {"Authorization": authHeader};
				} else {
					// 尝试从localStorage获取备份的Authorization头
					const backupHeader = localStorage.getItem("header_backup");
					if (backupHeader) {
						console.log("从备份恢复Authorization头");
						sessionStorage.setItem("header", backupHeader);
						headers = {"Authorization": backupHeader};
					} else {
						console.log("未找到任何Authorization头");
					}
				}
				
				// 确保使用localhost域名时设置正确的baseurl
				const apiBaseUrl = window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1')
					? 'http://localhost:5500/api'
					: baseurl;
					
				$.ajax({
					url: apiBaseUrl + "/footprint/" + footprintId,
					type: 'GET',
					contentType: "application/json",
					headers: headers,
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// 检查数据完整性 - 修改判断条件，放宽检查
							if (!res.data) {
								$("#title").html("数据错误");
								$("#content").html('<div style="text-align:center;padding:20px;"><p style="font-size:16px;color:#666;">获取数据不完整，请稍后再试</p><p style="margin-top:10px;"><a href="footprint.html" style="color:#DE2910;">返回列表</a></p></div>');
								console.error("数据结构不完整:", res.data);
								return;
							}
							
							// 添加更多调试信息
							console.log("获取到的数据结构:", {
								有cmsCategoryList: !!res.data.cmsCategoryList,
								cmsCategoryList长度: res.data.cmsCategoryList ? res.data.cmsCategoryList.length : 0,
								有ID: !!footprintId
							});
							
							// 更安全地设置pflid，如果没有分类列表则使用默认值
							pflid = (res.data.cmsCategoryList && res.data.cmsCategoryList.length > 0) 
								? res.data.cmsCategoryList[0].id 
								: footprintId; // 如果没有分类ID，使用足迹ID作为后备
								
							$("#city").html(res.data.city || '');
							$("#zz").html(res.data.author || '');
							$("#date").html(res.data.dateTime || '');
							$("#view").html(res.data.views || '0');
							$("#title").html(res.data.title || '无标题');
							
							// 检查是否为外部链接，如果是则重定向
							if (!checkAndRedirectExternalLink(res.data.content)) {
								// 不是外部链接，正常显示内容
								$("#content").html(res.data.content || '<div style="text-align:center;padding:20px;">暂无内容</div>');
							}
							
							// 记录浏览量 - 无论是否登录都记录
							try {
								recordViewCount(footprintId);
							} catch (error) {
								console.error("记录浏览量失败:", error);
							}
							
							// 如果用户已登录，记录学习记录
							if (userinfo && JSON.parse(userinfo).roleName == '学生') {
								time1 = Date.now();
								console.log("开始记录学习时间");
								
								// 定期自动保存学习记录（每60秒）
								const autoSaveInterval = setInterval(function() {
									if (time1) {
										const currentTime = Date.now();
										const studyTime = currentTime - time1;
										if (studyTime > 60000) { // 至少学习了1分钟才记录
											const tempRecord = {
												infoId: footprintId,
												categoryId: pflid,
												totalInfo: "1", 
												type: "主席足迹",
												learningTime: studyTime,
												positioning: formatStudyTime(studyTime),
												progress: "", 
											};
											// 存储临时学习记录
											localStorage.setItem("temp_jilu", JSON.stringify(tempRecord));
											console.log("自动保存学习时间", tempRecord.positioning);
										}
									}
								}, 60000); // 每60秒保存一次
								
								// 页面关闭时清除定时器
								window.addEventListener('unload', function() {
									clearInterval(autoSaveInterval);
								});
							}
						} else if (res.code == '401') {
							// 未授权，显示友好提示
							$("#title").html("需要登录才能查看详细内容");
							$("#content").html(`
								<div style="text-align:center;padding:30px;">
									<div style="font-size:18px;color:#DE2910;margin-bottom:15px;">
										<i style="font-size:24px;margin-right:8px;">!</i>权限不足，请登录后查看
									</div>
									<p style="font-size:14px;color:#666;margin-bottom:20px;">
										您需要登录账号才能查看此足迹的详细内容
									</p>
									<a href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html" 
									   style="display:inline-block;padding:8px 20px;background:#DE2910;color:#fff;border-radius:4px;text-decoration:none;font-size:14px;transition:all 0.3s ease;">
										立即登录
									</a>
									<p style="margin-top:20px;">
										<a href="footprint.html" style="color:#DE2910;font-size:14px;">返回足迹列表</a>
									</p>
								</div>
							`);
							
							// 尝试刷新会话信息
							refreshSession();
						} else {
							// 其他错误
							$("#title").html("获取内容失败");
							$("#content").html('<div style="text-align:center;padding:20px;"><p style="font-size:16px;color:#666;">获取内容失败，错误代码：' + res.code + '，请稍后再试</p><p style="margin-top:10px;"><a href="footprint.html" style="color:#DE2910;">返回列表</a></p></div>');
						}
					},
					error: function(xhr, status, error) {
						// 请求失败
						console.error("API请求错误:", status, error);
						
						// 检查是否是401未授权错误
						if (xhr.status === 401) {
							$("#title").html("需要登录才能查看详细内容");
							$("#content").html(`
								<div style="text-align:center;padding:30px;">
									<div style="font-size:18px;color:#DE2910;margin-bottom:15px;">
										<i style="font-size:24px;margin-right:8px;">!</i>权限不足，请登录后查看
									</div>
									<p style="font-size:14px;color:#666;margin-bottom:20px;">
										您需要登录账号才能查看此足迹的详细内容
									</p>
									<a href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html" 
									   style="display:inline-block;padding:8px 20px;background:#DE2910;color:#fff;border-radius:4px;text-decoration:none;font-size:14px;transition:all 0.3s ease;">
										立即登录
									</a>
									<p style="margin-top:20px;">
										<a href="footprint.html" style="color:#DE2910;font-size:14px;">返回足迹列表</a>
									</p>
								</div>
							`);
						} else {
							$("#title").html("网络错误");
							$("#content").html('<div style="text-align:center;padding:20px;"><p style="font-size:16px;color:#666;">网络连接错误，请检查网络连接后重试</p><p style="margin-top:10px;"><a href="footprint.html" style="color:#DE2910;">返回列表</a></p></div>');
						}
					}
				});
			}
			
			// 记录浏览量的单独函数
			function recordViewCount(id) {
				if (!id) {
					console.error("缺少ID，无法记录浏览量");
					return;
				}
				
				// 构建请求头，如果用户已登录则添加 Authorization 头
				let headers = {};
				const authHeader = sessionStorage.getItem("header");
				
				if (authHeader) {
					headers = {"Authorization": authHeader};
				} else {
					// 尝试从localStorage获取备份的Authorization头
					const backupHeader = localStorage.getItem("header_backup");
					if (backupHeader) {
						sessionStorage.setItem("header", backupHeader);
						headers = {"Authorization": backupHeader};
					}
				}
				
				// 确保使用localhost域名时设置正确的baseurl
				const apiBaseUrl = window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1')
					? 'http://localhost:5500/api'
					: baseurl;
				
				console.log("准备记录浏览量:", {id: id, 有授权头: !!headers.Authorization});
				
				// 使用同步请求确保浏览量记录完成
				try {
					$.ajax({
						url: apiBaseUrl + "/footprint/views/" + id,
						type: 'GET',
						contentType: "application/json",
						headers: headers,
						dataType: 'json',
						async: false, // 使用同步请求确保完成
						success: function(res) {
							console.log("浏览量记录成功:", id);
						},
						error: function(xhr, status, error) {
							console.error("浏览量记录失败:", status, error, xhr.status);
							
							// 如果是401错误但仍有备份凭据，则可能是会话过期
							if (xhr.status === 401 && localStorage.getItem("header_backup")) {
								console.warn("授权已过期，请刷新页面或重新登录");
							}
						}
					});
				} catch (e) {
					console.error("记录浏览量时出错:", e);
				}
			}
			
			function getfooterlink(){
				// 构建请求头，如果用户已登录则添加 Authorization 头
				let headers = {};
				const authHeader = sessionStorage.getItem("header");
				
				if (authHeader) {
					headers = {"Authorization": authHeader};
				} else {
					// 尝试从localStorage获取备份的Authorization头
					const backupHeader = localStorage.getItem("header_backup");
					if (backupHeader) {
						sessionStorage.setItem("header", backupHeader);
						headers = {"Authorization": backupHeader};
					}
				}
					
				// 确保使用localhost域名时设置正确的baseurl
				const apiBaseUrl = window.location.hostname === 'localhost' || window.location.hostname.includes('127.0.0.1')
					? 'http://localhost:5500/api'
					: baseurl;
					
				$.ajax({
					url: apiBaseUrl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: headers,
					dataType: 'json',
					success: (res) => {
						if (res.code == '200' && res.data) {
							let html = "";
							res.data.forEach((item) => {
								if (item && item.url && item.name) {
									html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>';
								}
							});
							$("#linkbox").html(html);
						}
					},
					error: function(xhr, status, error) {
						// 友情链接加载失败，不影响主要功能，只需记录错误
						console.error("友情链接加载失败:", status, error);
					}
				})
			}
			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}
			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
			
			// 添加刷新会话功能
			function refreshSession() {
				// 检查页面是否在iframe中
				if (window.parent && window.parent !== window) {
					console.log("检测到iframe环境，尝试从父窗口获取会话信息");
					
					try {
						// 尝试从父窗口获取会话信息
						const parentUserInfo = window.parent.sessionStorage.getItem("userinfo");
						const parentHeader = window.parent.sessionStorage.getItem("header");
						
						if (parentUserInfo && parentHeader) {
							console.log("从父窗口获取到会话信息，正在同步...");
							sessionStorage.setItem("userinfo", parentUserInfo);
							sessionStorage.setItem("header", parentHeader);
							
							// 刷新用户信息变量
							userinfo = parentUserInfo;
							
							// 更新界面
							if (userinfo) {
								$("#login").hide();
								$("#user").show();
								$("#user").html(JSON.parse(userinfo).name);
								$("#edit").show();
								
								// 重新获取内容
								setTimeout(fetchFootprintContent, 500);
							}
							
							return true;
						}
					} catch (error) {
						console.error("从父窗口获取会话信息失败:", error);
					}
				}
				
				// 尝试从localStorage获取备份的会话信息
				const backupUserInfo = localStorage.getItem("userinfo_backup");
				const backupHeader = localStorage.getItem("header_backup");
				
				if (backupUserInfo && backupHeader) {
					console.log("从备份恢复会话信息");
					sessionStorage.setItem("userinfo", backupUserInfo);
					sessionStorage.setItem("header", backupHeader);
					
					// 刷新用户信息变量
					userinfo = backupUserInfo;
					
					// 更新界面
					if (userinfo) {
						$("#login").hide();
						$("#user").show();
						$("#user").html(JSON.parse(userinfo).name);
						$("#edit").show();
						
						// 重新获取内容
						setTimeout(fetchFootprintContent, 500);
					}
					
					return true;
				}
				
				return false;
			}
			
			// 恢复tiaozhuan函数，用于记录学习时间
			function tiaozhuan() {
				// 只有登录用户才记录学习时间
				if (userinfo && JSON.parse(userinfo).roleName == '学生' && time1) {
					try {
						const footprintId = getUrlParam('id');
						
						// 确保有足迹ID和类别ID
						if (!footprintId || !pflid) {
							console.error("记录学习记录失败：缺少必要的ID", {footprintId, pflid});
							return;
						}
						
						let time2 = Date.now();
						let value = time2 - time1;
						
						// 格式化时间
						var days = parseInt(value / (1000 * 60 * 60 * 24));
						var hours = parseInt((value % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
						var minutes = parseInt((value % (1000 * 60 * 60)) / (1000 * 60));
						var seconds = Math.floor((value % (1000 * 60)) / 1000);
						
						// 构建学习记录JSON
						let json = {
							infoId: footprintId, //信息id
							categoryId: pflid, //所属分类id
							totalInfo: "1", //总时长，总页数
							positioning: days+"天"+hours+"时"+minutes+"分"+seconds+"秒", //学习了多久，多少页    
							type: "主席足迹",
							learningTime: value,
							progress: "", //进度 百分比
						};
						
						// 存储学习记录
						window.localStorage.setItem("jilu", JSON.stringify(json));
						console.log("学习记录已保存", {footprintId, pflid, learningTime: value});
					} catch (error) {
						console.error("保存学习记录时出错:", error);
					}
				}
			}
			
			// 格式化学习时间的辅助函数
			function formatStudyTime(milliseconds) {
				const days = parseInt(milliseconds / (1000 * 60 * 60 * 24));
				const hours = parseInt((milliseconds % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
				const minutes = parseInt((milliseconds % (1000 * 60 * 60)) / (1000 * 60));
				const seconds = Math.floor((milliseconds % (1000 * 60)) / 1000);
				return days + "天" + hours + "时" + minutes + "分" + seconds + "秒";
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
			
			// 添加页面卸载事件监听，确保学习记录被保存
			window.addEventListener('unload', function() {
				tiaozhuan();
			});
		</script>
	</body>
</html>
