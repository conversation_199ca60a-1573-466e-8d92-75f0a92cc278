// 移动端模拟登录脚本
// 基于PC端的login-mock.js，适配移动端界面

// 模拟登录信息存储
function mockLogin(userType) {
    // 可选用户类型（与PC端保持一致）
    const userTypes = {
        // 老师用户
        teacher: {
            token: 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyMTIxMDE4IiwiY3JlYXRlZCI6MTc0NDc2NzkyMzEwNiwiZXhwIjoxNzYwMzE5OTIzfQ.hJd3I-_acZAiSjcHLP7jbRlcDIZYHraB37bGVsFrWW72zvzkJ1fHwqoOG7hidvKs9w6qiFwoc_BjkE1tNy9Yvg',
            userInfo: {
                "id": "1299387870829744128",
                "name": "吴永刚",
                "identifier": null,
                "gender": null,
                "avatarPath": null,
                "loginTime": "2025-04-16T01:45:23.000+00:00",
                "lockTime": null,
                "userAuth": {
                    "id": "1299390514008821760",
                    "creator": "admin",
                    "createdAt": "2024-10-25T07:14:00.000+00:00",
                    "modifier": "admin",
                    "updatedAt": "2024-10-25T07:14:00.000+00:00",
                    "delFlag": 0,
                    "tenantCode": "1",
                    "ext": null,
                    "userId": "1299387870829744128",
                    "identityType": 2,
                    "identifier": "2121018",
                    "wechatOpenId": null,
                    "siteId": 0,
                    "newRecord": false
                },
                "college": null,
                "major": null,
                "className": null,
                "classNames": null,
                "approvalStatus": 1,
                "roleName": "老师",
                "passWord": null,
                "collegeName": null,
                "majorName": null,
                "className2": null,
                "questionId": null,
                "questionName": null,
                "answer": null,
                "researchSection": "马克思主义学院",
                "myCollegeName": "马克思主义学院",
                "userId": null,
                "createdAt": "2024-10-25 15:03:30",
                "status": null,
                "time1": null,
                "time2": null,
                "ids": null,
                "paperId": null,
                "paperName": null,
                "weChat": null,
                "wechatName": null,
                "wechatOpenId": null,
                "sessionKey": null,
                "email": null,
                "phone": "18993689001",
                "liveUserId": null,
                "collegeId": null,
                "majorId": null,
                "classId": null,
                "collegeIds": null,
                "studentCollegeList": null,
                "collegeList": null,
                "roleMojorList": [
                    {
                        "id": "1361742409058358358",
                        "creator": "super-admin",
                        "createdAt": "2025-04-15T08:38:10.000+00:00",
                        "modifier": "super-admin",
                        "updatedAt": "2025-04-15T08:38:10.000+00:00",
                        "delFlag": 0,
                        "tenantCode": null,
                        "ext": null,
                        "roleId": "2",
                        "roleName": "老师",
                        "usersId": "1299387870829744128",
                        "collegeId": "1288894906898714628",
                        "majorId": "1288894994127654913",
                        "classId": "1288895255575400448",
                        "classIds": null,
                        "subjectId": null,
                        "newRecord": false
                    }
                ],
                "collegeMojorList": null,
                "code": null,
                "roleList": [],
                "roleId": null
            }
        },
        // 学生用户
        student: {
            token: 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiI1MjIwMTA4MDE1MzIiLCJjcmVhdGVkIjoxNzQ2NTEzMDkwMDkwLCJleHAiOjE3NjIwNjUwOTB9.KKBVPXxZFbR5JlUaoqiG_zHQ5Cya89BT9rpQRKj3OxJydEhOJIlHDUpaDsuxz0bT_uGYIWoQai8pu9grduTE7w',
            userInfo: {
                "id": "1388727398726543212",
                "name": "测试学生",
                "identifier": null,
                "gender": null,
                "avatarPath": null,
                "loginTime": "2025-05-06T08:45:23.000+00:00",
                "lockTime": null,
                "userAuth": {
                    "id": "1388727398726543213",
                    "creator": "admin",
                    "createdAt": "2025-05-06T08:45:23.000+00:00",
                    "modifier": "admin",
                    "updatedAt": "2025-05-06T08:45:23.000+00:00",
                    "delFlag": 0,
                    "tenantCode": "1",
                    "ext": null,
                    "userId": "1388727398726543212",
                    "identityType": 1,
                    "identifier": "5220108015",
                    "wechatOpenId": null,
                    "siteId": 0,
                    "newRecord": false
                },
                "college": null,
                "major": null,
                "className": null,
                "classNames": null,
                "approvalStatus": 1,
                "roleName": "学生",
                "passWord": null,
                "collegeName": "中医学院",
                "majorName": "中医学",
                "className2": "中医2022-1班",
                "questionId": null,
                "questionName": null,
                "answer": null,
                "researchSection": null,
                "myCollegeName": "中医学院",
                "userId": null,
                "createdAt": "2025-05-06 08:45:23",
                "status": null,
                "time1": null,
                "time2": null,
                "ids": null,
                "paperId": null,
                "paperName": null,
                "weChat": null,
                "wechatName": null,
                "wechatOpenId": null,
                "sessionKey": null,
                "email": null,
                "phone": "15991234567",
                "liveUserId": null,
                "collegeId": "1288894906898714626",
                "majorId": "1288894994127654912",
                "classId": "1288895255575400446",
                "collegeIds": null,
                "studentCollegeList": null,
                "collegeList": null,
                "roleMojorList": [
                    {
                        "id": "1361742409058358359",
                        "creator": "super-admin",
                        "createdAt": "2025-05-06T08:45:23.000+00:00",
                        "modifier": "super-admin",
                        "updatedAt": "2025-05-06T08:45:23.000+00:00",
                        "delFlag": 0,
                        "tenantCode": null,
                        "ext": null,
                        "roleId": "1",
                        "roleName": "学生",
                        "usersId": "1388727398726543212",
                        "collegeId": "1288894906898714626",
                        "majorId": "1288894994127654912",
                        "classId": "1288895255575400446",
                        "classIds": null,
                        "subjectId": null,
                        "newRecord": false
                    }
                ],
                "collegeMojorList": null,
                "code": null,
                "roleList": [],
                "roleId": null
            }
        }
    };
    
    // 默认使用教师账号
    const selectedUser = userTypes[userType] || userTypes.teacher;
    
    // 存储认证token
    sessionStorage.setItem('header', selectedUser.token);
    
    // 存储用户信息
    sessionStorage.setItem('userinfo', JSON.stringify(selectedUser.userInfo));

    console.log(`模拟登录成功！用户角色：${selectedUser.userInfo.roleName}，用户名：${selectedUser.userInfo.name}`);
    console.log('Token和用户信息已存储到sessionStorage中');
    
    return selectedUser.userInfo;
}

// 强制切换到学生账号
function switchToStudent() {
    const studentInfo = mockLogin('student');
    showMobileToast(`已切换到学生账号：${studentInfo.name}`, 'success');
    // 更新UI
    setTimeout(() => {
        if (typeof updateLoginUI === 'function') {
            updateLoginUI();
        }
        window.location.reload();
    }, 1000);
}

// 强制切换到教师账号
function switchToTeacher() {
    const teacherInfo = mockLogin('teacher');
    showMobileToast(`已切换到教师账号：${teacherInfo.name}`, 'success');
    // 更新UI
    setTimeout(() => {
        if (typeof updateLoginUI === 'function') {
            updateLoginUI();
        }
        window.location.reload();
    }, 1000);
}

// 移动端Toast提示
function showMobileToast(message, type = 'info') {
    // 移除已存在的toast
    const existingToast = document.getElementById('mobile-toast');
    if (existingToast) {
        existingToast.remove();
    }
    
    // 创建toast元素
    const toast = document.createElement('div');
    toast.id = 'mobile-toast';
    toast.style.cssText = `
        position: fixed;
        top: 20px;
        left: 50%;
        transform: translateX(-50%);
        background: ${type === 'success' ? '#4CAF50' : type === 'error' ? '#f44336' : '#2196F3'};
        color: white;
        padding: 12px 20px;
        border-radius: 6px;
        font-size: 14px;
        z-index: 10000;
        box-shadow: 0 4px 12px rgba(0,0,0,0.3);
        max-width: 90%;
        text-align: center;
        animation: slideDown 0.3s ease-out;
    `;
    
    // 添加动画样式
    const style = document.createElement('style');
    style.textContent = `
        @keyframes slideDown {
            from { opacity: 0; transform: translateX(-50%) translateY(-20px); }
            to { opacity: 1; transform: translateX(-50%) translateY(0); }
        }
    `;
    document.head.appendChild(style);
    
    toast.textContent = message;
    document.body.appendChild(toast);
    
    // 3秒后自动移除
    setTimeout(() => {
        if (toast.parentNode) {
            toast.style.animation = 'slideDown 0.3s ease-out reverse';
            setTimeout(() => {
                toast.remove();
            }, 300);
        }
    }, 3000);
}

// 创建移动端登录选择器
function createMobileLoginSelector() {
    // 检查是否已存在选择器
    if (document.getElementById('mobile-login-selector')) {
        return;
    }
    
    // 创建选择器容器
    const selectorContainer = document.createElement('div');
    selectorContainer.id = 'mobile-login-selector';
    selectorContainer.style.cssText = `
        position: fixed;
        bottom: 20px;
        right: 20px;
        background-color: white;
        border: 2px solid #c00714;
        border-radius: 8px;
        padding: 15px;
        z-index: 9999;
        box-shadow: 0 4px 16px rgba(0,0,0,0.2);
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        min-width: 200px;
        max-width: 280px;
    `;
    
    // 标题
    const title = document.createElement('h3');
    title.textContent = '🔧 开发调试';
    title.style.cssText = `
        margin: 0 0 10px 0;
        fontSize: 16px;
        fontWeight: 600;
        color: #c00714;
        textAlign: center;
        borderBottom: 1px solid #eee;
        paddingBottom: 8px;
    `;
    selectorContainer.appendChild(title);
    
    // 当前状态显示
    const currentUser = JSON.parse(sessionStorage.getItem('userinfo') || '{}');
    const statusDiv = document.createElement('div');
    statusDiv.style.cssText = `
        margin-bottom: 12px;
        padding: 8px;
        background-color: #f8f9fa;
        border-radius: 4px;
        font-size: 12px;
        text-align: center;
        border-left: 3px solid #c00714;
    `;
    statusDiv.innerHTML = `当前：<strong>${currentUser.name || '未登录'}</strong><br><small>${currentUser.roleName || '无角色'}</small>`;
    selectorContainer.appendChild(statusDiv);
    
    // 创建按钮
    const createButton = (text, actionFn, primary = false, disabled = false) => {
        const button = document.createElement('button');
        button.textContent = text;
        button.disabled = disabled;
        button.style.cssText = `
            display: block;
            width: 100%;
            margin: 6px 0;
            padding: 10px 12px;
            border: none;
            border-radius: 4px;
            background-color: ${disabled ? '#ccc' : (primary ? '#c00714' : '#6c757d')};
            color: white;
            cursor: ${disabled ? 'not-allowed' : 'pointer'};
            font-size: 13px;
            font-weight: 500;
            text-align: center;
            transition: all 0.2s;
            opacity: ${disabled ? '0.6' : '1'};
        `;
        
        if (!disabled) {
            button.onmouseover = () => {
                button.style.backgroundColor = primary ? '#a00610' : '#5a6268';
                button.style.transform = 'translateY(-1px)';
            };
            button.onmouseout = () => {
                button.style.backgroundColor = primary ? '#c00714' : '#6c757d';
                button.style.transform = 'translateY(0)';
            };
            button.addEventListener('click', actionFn);
        }
        
        return button;
    };
    
    // 添加按钮
    const studentBtn = createButton('👨‍🎓 切换到学生', switchToStudent, true, currentUser.roleName === '学生');
    const teacherBtn = createButton('👨‍🏫 切换到教师', switchToTeacher, false, currentUser.roleName === '老师');
    
    selectorContainer.appendChild(studentBtn);
    selectorContainer.appendChild(teacherBtn);
    
    // 关闭按钮
    const closeBtn = createButton('✕ 关闭', () => {
        selectorContainer.remove();
    }, false);
    closeBtn.style.fontSize = '11px';
    closeBtn.style.padding = '6px 8px';
    closeBtn.style.marginTop = '10px';
    selectorContainer.appendChild(closeBtn);
    
    // 添加到页面
    document.body.appendChild(selectorContainer);
}

// 页面加载完成后执行
document.addEventListener('DOMContentLoaded', () => {
    // 检查是否已有登录信息
    const userInfo = sessionStorage.getItem('userinfo');
    if (!userInfo) {
        // 如果没有登录信息，默认使用教师账号登录
        mockLogin('teacher');
        showMobileToast('已自动登录为教师账号', 'success');
    }
    
    // 延迟创建选择器，确保页面其他元素已加载
    setTimeout(() => {
        // 只在开发环境显示选择器
        if (window.location.hostname === 'localhost' || window.location.hostname === '127.0.0.1') {
            createMobileLoginSelector();
        }
    }, 2000);
});

// 暴露全局函数，可以在控制台直接调用
window.switchToStudent = switchToStudent;
window.switchToTeacher = switchToTeacher;
window.mockLogin = mockLogin;
window.createMobileLoginSelector = createMobileLoginSelector;
