<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-学院荣誉</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/collegeprofile.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />学院简介</label>
					</div>
					<div class="leftitembox" id="classview">

					</div>
				</div>
				<div class="boxright">
					<div class="borderbag"></div>
					<div class="righttop2" id="classdate">

					</div>
					<div class="nrnr2">
						<div class="nr2left">
							<div class="swiper" id="swiper">
								<div id="fyq2" class="swiper-pagination"></div>
								<div class="swiper-wrapper" id="swiperlist">


								</div>
							</div>
						</div>
						<div class="nr2right" id="list1">


						</div>
						<div class="item4list" id="list2">

						</div>
						<div class="fybox" id="fyq">
							<span id="sy">首页</span>
							<span id="syy">上一页</span>
							<div class="num" id="num">
							</div>
							<span id="xyy">下一页</span>
							<span id="wy">尾页</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let pageindex = 1
			let pagesize = 20
			let pages = 0
			let classid = null
			let classdd = null
			let mySwiper = null
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				let userinfo = sessionStorage.getItem("userinfo")
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()

				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				getclass('collegeprofile.html')
				getfooterlink()
				getclassid()
			})

			function getclassid() {
				//获取学院简介分类
				$.ajax({
					url: baseurl + "/web/about",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data[0].children.map((item) => {
								if (item.redirectUrl == 'collegeprofile2.html') {
									html += '<div class="leftitem activeleftitem"><a>' + item.name +
										'</a></div>'
								} else {
									html += '<div class="leftitem"><a href="' + item.redirectUrl + '">' +
										item.name + '</a></div>'
								}
							})
							$("#classview").html(html)
							classid = res.data[0].children[2].children[0].id
							getlist(true)
							// console.log(classid)
							getinfo(res.data[0].children[2])
						}
					}
				})
			}

			function getinfo(dd) {
				classdd = dd
				let html = ""
				classdd.children.map((item) => {
					if (classid == item.id) {
						html += '<div onclick="select(this)" class="activetop2" data-id="' + item.id + '">' + item.name +
							'</div>'
					} else {
						html += '<div onclick="select(this)" data-id="' + item.id + '">' + item.name + '</div>'
					}
				})
				$("#classdate").html(html)
			}

			function select(item) {
				classid = $(item).attr("data-id")
				let html = ""
				classdd.children.map((item) => {
					if (classid == item.id) {
						html += '<div onclick="select(this)" class="activetop2" data-id="' + item.id + '">' + item.name +
							'</div>'
					} else {
						html += '<div onclick="select(this)" data-id="' + item.id + '">' + item.name + '</div>'
					}
				})
				$("#classdate").html(html)
				// console.log(classid)
				getlist(false)
			}

			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是第最后一页了！")
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！")
					}
				} else {
					pageindex = index
					getlist(false)
				}
			}

			function getlist(isf) { //获取学院荣誉列表
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: pageindex,
						pageSize: pagesize,
						categoryId: classid
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let items = res.data.list
							let html1 = ""
							let html2 = ""
							let html3 = ""
							items.map((item, index) => {
								if (index < 5) {
									html1 += '<div class="swiper-slide"><div class="imgiii" onclick="inxq(this)" data-id="'+item.id+'">' +
										'<img src="'
										 if(item.thumbPath){
											 html1+=baseurl+item.thumbPath[0]
										 }
										html1+='" /><div class="swpierbtn">' +
										'<div>' + item.title + '</div>' +
										'<label><img src="img/yj.png" /><span>' + item.clickCount +
										'</span></label></div></div></div>'
								} else if (5 <= index && index < 8) {
									html2 += '<div class="item3" onclick="inxq(this)" data-id="'+item.id+'"><div class="item3l">' +
										'<div class="item3ltitle">' + item.title +
										'</div><div class="item3btnview">' +
										'<div><label><img src="img/sj2.png"/>' + setDate(item.createdAt) +
										'</label>' +
										'<label><img src="img/zz.png"/>' + item.author + '</label></div>' +
										'<label><img src="img/yj.png"/>' + item.clickCount +
										'</label></div></div><div class="item3r"><img src="' + baseurl + item
										.thumbPath[0] + '" /></div></div>'
								} else if (index >= 8) {
									html3 += '<div class="item4" onclick="inxq(this)" data-id="'+item.id+'"><div>' + item.title +
										'</div><label><img src="img/sj2.png" />' + setDate(item.createdAt) +
										'</label></div>'
								}
							})
							$("#swiperlist").html(html1)
							$("#list1").html(html2)
							$("#list2").html(html3)
							pages = res.data.pages
							if (pages > 1) {
								let numhtml = ""
								for (let a = 1; a <= pages; a++) {
									if (pageindex == a) {
										numhtml += '<label class="actinum" onclick="getnewlist(' + a + ')">' + a +
											'</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
							if (isf) {
								mySwiper = new Swiper('.swiper', {
									autoplay: true,
									loop: true,
									pagination: {
										el: '.swiper-pagination',
										clickable: true
									}
								})
								mySwiper.el.onmouseover = function() {
									mySwiper.autoplay.stop();
								}
								mySwiper.el.onmouseout = function() {
									mySwiper.autoplay.start();
								}
							} else {
								mySwiper.destroy()
								mySwiper = new Swiper('.swiper', {
									autoplay: true,
									loop: true,
									pagination: {
										el: '.swiper-pagination',
										clickable: true
									}
								})
								mySwiper.el.onmouseover = function() {
									mySwiper.autoplay.stop();
								}
								mySwiper.el.onmouseout = function() {
									mySwiper.autoplay.start();
								}
							}
						}
					}
				})
			}
			function inxq(item){
				window.location.href = "collegeprofile3.html?id=" + $(item).attr("data-id")
			}
			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					if (res.data[i].id == getUrlParam('id') || res.data[i].redirectUrl ==
			// 						'collegeprofile.html') {
			// 						html += '<a class="menuitem active" href="' + res.data[i].redirectUrl + '?id=' +
			// 							res
			// 							.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					} else {
			// 						html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 							.data[
			// 								i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					}
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
