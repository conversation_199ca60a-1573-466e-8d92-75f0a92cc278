* {
	margin: 0;
	padding: 0;
	outline: none;
	box-sizing: content-box;
	background: transparent;
	font-family: -apple-system, BlinkMacSystemFont, "Helvetica Neue", Helvetica, "PingFang SC", "Microsoft YaHei", Arial, sans-serif;
}
body {
    background: #f5f5f5;
}
#container {
    position: relative;
    overflow: hidden;
    margin: 0 auto;
    width: 1010px;
    height: auto;
    background: #fff;
    box-shadow: #d6d6d6 0 0 15px;
}
main {
    display: block;
}
main>section {
    width: 940px;
    overflow: hidden;
    margin: 0 auto;
}
.same-btn {
	text-decoration: none;
    display: inline-block;
    border: #2285ee solid 1px;
    width: 90px;
    height: 30px;
    line-height: 30px;
    margin: 0 6px;
    text-align: center;
    border-radius: 4px;
    cursor: pointer;
    transition: .15s;
    color: #2285ee;
    font-weight: 400;
}
.same-btn:hover {
	background: #2285ee;
	color: #fff;
}
.map-wrapper {
    background: #fff;
    border: #e0e0e0 solid 1px;
    margin-top: 40px;
}
.map-wrapper>p:first-child {
    display: block;
    height: 50px;
    line-height: 50px;
    border-bottom: #e0e0e0 solid 1px;
    text-align: center;
    padding: 15px 0;
    margin-bottom: 40px;
    font-size: 24px;
    font-weight: 700;
    background: #fafafa;
}
.map-box {
    width: 900px;
    height: 500px;
    margin: 0 auto;
    overflow: hidden;
}
.multiple-button {
	text-align: center;
}
.multiple-button button {
	width: 230px;
	height: 36px
}
.multiple-button p {
	margin: 18px 0;
	font-size: 18px;
	font-weight: bold;
}
#hover-callback,
#click-callback {
	text-align: center;
	display: block;
	height: 30px;
	line-height: 30px;
	font-weight: bold;
}