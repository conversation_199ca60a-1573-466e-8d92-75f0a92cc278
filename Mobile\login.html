<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>登录 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="css/mobile-base.css">
    <link rel="stylesheet" href="css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../PC/js/coco-message.js"></script>
    <script src="js/mobile-base.js"></script>
    
    <style>
        /* 登录页面专用样式 */
        .login-page {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            padding: 20px;
        }
        
        .login-container {
            background: white;
            border-radius: 16px;
            padding: 32px 24px;
            width: 100%;
            max-width: 400px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.1);
        }
        
        .login-header {
            text-align: center;
            margin-bottom: 32px;
        }
        
        .login-logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 16px;
            background: #c00714;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 32px;
            font-weight: bold;
        }
        
        .login-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 8px;
        }
        
        .login-subtitle {
            font-size: 14px;
            color: #666;
        }
        
        .login-form {
            margin-bottom: 24px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-input {
            width: 100%;
            padding: 16px;
            border: 2px solid #f0f0f0;
            border-radius: 12px;
            font-size: 16px;
            transition: all 0.3s ease;
            background: #f8f9fa;
        }
        
        .form-input:focus {
            border-color: #c00714;
            background: white;
            box-shadow: 0 0 0 3px rgba(192, 7, 20, 0.1);
        }
        
        .captcha-group {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .captcha-input {
            flex: 1;
        }
        
        .captcha-image {
            width: 100px;
            height: 44px;
            border: 2px solid #f0f0f0;
            border-radius: 8px;
            cursor: pointer;
            background: #f8f9fa;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: #666;
        }
        
        .login-btn {
            width: 100%;
            padding: 16px;
            background: #c00714;
            color: white;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 16px;
        }
        
        .login-btn:hover {
            background: #a00610;
        }
        
        .login-btn:disabled {
            background: #ccc;
            cursor: not-allowed;
        }
        
        .login-options {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 24px;
        }
        
        .forgot-password {
            color: #c00714;
            text-decoration: none;
            font-size: 14px;
        }
        
        .remember-me {
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            color: #666;
        }
        
        .checkbox {
            width: 16px;
            height: 16px;
            accent-color: #c00714;
        }
        
        .divider {
            text-align: center;
            margin: 24px 0;
            position: relative;
            color: #999;
            font-size: 14px;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e5e5e5;
        }
        
        .divider span {
            background: white;
            padding: 0 16px;
            position: relative;
        }
        
        .cas-login-btn {
            width: 100%;
            padding: 16px;
            background: #f8f9fa;
            color: #333;
            border: 2px solid #e5e5e5;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .cas-login-btn:hover {
            background: #e9ecef;
            border-color: #c00714;
        }
        
        .back-link {
            position: absolute;
            top: 20px;
            left: 20px;
            color: white;
            text-decoration: none;
            display: flex;
            align-items: center;
            gap: 8px;
            font-size: 14px;
            padding: 8px 12px;
            border-radius: 20px;
            background: rgba(255, 255, 255, 0.2);
            backdrop-filter: blur(10px);
        }
        
        .back-link:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        @media (max-width: 375px) {
            .login-container {
                padding: 24px 20px;
            }
            
            .login-logo {
                width: 64px;
                height: 64px;
                font-size: 24px;
            }
            
            .login-title {
                font-size: 20px;
            }
        }
    </style>
</head>
<body class="login-page">
    <!-- 返回链接 -->
    <a href="index.html" class="back-link">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="currentColor">
            <path d="M20 11H7.83l5.59-5.59L12 4l-8 8 8 8 1.41-1.41L7.83 13H20v-2z"/>
        </svg>
        返回首页
    </a>

    <div class="login-container">
        <div class="login-header">
            <div class="login-logo">思</div>
            <h1 class="login-title">欢迎登录</h1>
            <p class="login-subtitle">思政一体化平台</p>
        </div>

        <!-- 统一认证登录 -->
        <button class="cas-login-btn" id="casLoginBtn">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2ZM21 9V7L15 1L9 7V9H21ZM12 8C8.69 8 6 10.69 6 14S8.69 20 12 20S18 17.31 18 14S15.31 8 12 8Z"/>
            </svg>
            统一认证登录
        </button>

        <div class="divider">
            <span>或使用账号密码登录</span>
        </div>

        <!-- 本地登录表单 -->
        <form class="login-form" id="loginForm">
            <div class="form-group">
                <input type="text" class="form-input" id="username" placeholder="请输入学号" required>
            </div>
            
            <div class="form-group">
                <input type="password" class="form-input" id="password" placeholder="请输入密码" required>
            </div>
            
            <div class="form-group">
                <div class="captcha-group">
                    <input type="text" class="form-input captcha-input" id="captcha" placeholder="验证码" required>
                    <div class="captcha-image" id="captchaImage" onclick="refreshCaptcha()">
                        点击刷新
                    </div>
                </div>
            </div>
            
            <div class="login-options">
                <label class="remember-me">
                    <input type="checkbox" class="checkbox" id="rememberMe">
                    记住我
                </label>
                <a href="#" class="forgot-password" onclick="showForgotPassword()">忘记密码？</a>
            </div>
            
            <button type="submit" class="login-btn" id="loginBtn">登录</button>
        </form>
    </div>

    <script>
        let isLogging = false;

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化验证码
            refreshCaptcha();
            
            // 绑定事件
            bindEvents();
            
            // 检查是否已登录
            checkExistingLogin();
        });

        function bindEvents() {
            // 统一认证登录
            document.getElementById('casLoginBtn').addEventListener('click', function() {
                window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html';
            });

            // 本地登录表单
            document.getElementById('loginForm').addEventListener('submit', function(e) {
                e.preventDefault();
                performLogin();
            });

            // 回车键登录
            document.addEventListener('keypress', function(e) {
                if (e.key === 'Enter') {
                    performLogin();
                }
            });
        }

        function refreshCaptcha() {
            // 这里应该调用获取验证码的API
            // 暂时显示占位符
            document.getElementById('captchaImage').innerHTML = 'ABCD';
        }

        function performLogin() {
            if (isLogging) return;

            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const captcha = document.getElementById('captcha').value.trim();

            if (!username) {
                MobileUtils.showToast('请输入学号', 'warning');
                return;
            }

            if (!password) {
                MobileUtils.showToast('请输入密码', 'warning');
                return;
            }

            if (!captcha) {
                MobileUtils.showToast('请输入验证码', 'warning');
                return;
            }

            isLogging = true;
            const loginBtn = document.getElementById('loginBtn');
            loginBtn.textContent = '登录中...';
            loginBtn.disabled = true;

            const loginData = {
                identifier: username,
                credential: password,
                code: captcha
            };

            $.ajax({
                url: baseurl + "/student/login",
                type: 'POST',
                data: JSON.stringify(loginData),
                contentType: "application/json",
                dataType: 'json',
                success: function(res) {
                    isLogging = false;
                    loginBtn.textContent = '登录';
                    loginBtn.disabled = false;

                    if (res.code === "200") {
                        MobileUtils.showToast('登录成功！', 'success');
                        
                        // 储存token和用户信息
                        sessionStorage.setItem('header', res.data.scheme + res.data.token);
                        sessionStorage.setItem('userinfo', JSON.stringify(res.data.student));

                        // 检查是否设置过安全问题
                        if (res.data.student.questionName) {
                            // 已有安全问题，跳转到主页
                            setTimeout(() => {
                                window.location.href = 'index.html';
                            }, 1000);
                        } else {
                            // 没有安全问题，提示设置
                            MobileUtils.showToast('请先设置安全问题', 'warning');
                            setTimeout(() => {
                                window.location.href = 'pages/security-question.html';
                            }, 1500);
                        }
                    } else {
                        MobileUtils.showToast(res.message || '登录失败', 'error');
                        refreshCaptcha();
                    }
                },
                error: function(err) {
                    isLogging = false;
                    loginBtn.textContent = '登录';
                    loginBtn.disabled = false;
                    MobileUtils.showToast('登录失败，请稍后重试', 'error');
                    refreshCaptcha();
                }
            });
        }

        function showForgotPassword() {
            MobileUtils.showToast('请联系管理员重置密码', 'info');
        }

        function checkExistingLogin() {
            const userinfo = sessionStorage.getItem("userinfo");
            if (userinfo) {
                // 已登录，跳转到主页
                window.location.href = 'index.html';
            }
        }
    </script>
</body>
</html>
