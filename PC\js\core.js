$(function() {

	let width = $(window).width()
	let height = $(window).height()

	$.getJSON("json/china.json").done(function(response) {
		jsMap.config("#map", response, {
			stroke: {
				width: 0.3,
				color: "#dedede"
			},
			name: 'china',
			width: width * 0.6,
			height: height * 0.7,
			tip: function(id, name) {
				// 	return '<div style="display: flex;width: 10.416666rem;height: 3.125rem;background: #910a0c;border-radius: 5px;box-shadow: 10px 10px 20px #000000;">'+
				// '<div style="width: 100%;padding-left: 0.78125rem;">'+
				// 	'<div style="font-size: 0.833333rem;color: #FFFFFF;line-height: 1.041666rem;margin-top: 0.520833rem">'+name+'</div>'+
				// 	'<div style="font-size: 0.625rem;color: #c79697;line-height: 0.78125rem;margin-top: 0.260416rem;">2021年12月24日</div></div>'+
				// '</div>';
			},
			areaName: {
				show: true
			},
			fill: {
				basicColor: "#faf9f5",
				hoverColor: "#d7a545",
				clickColor: "#d7a545"
			},
			clickCallback: function(id, name) {
				getnewss(name)
			}
		})




		// jsMap.config("#map-01", response);

		// jsMap.config("#map-02", response, {
		// 	areaName: {
		// 		show: true
		// 	}
		// });

		// jsMap.config("#map-03", response, {
		// 	multiple: true
		// });
		// $("#get-multiple-1").on("click", function() {
		// 	// console.log(jsMap.multipleValue("#map-03"));
		// })
		// $("#get-multiple-2").on("click", function() {
		// 	// console.log(jsMap.multipleValue("#map-03", {
		// 		type: "object"
		// 	}));
		// })

		// jsMap.config("#map-04", response, {
		// 	stroke: {
		// 		width: 2,
		// 		color: "#000"
		// 	}
		// });

		// jsMap.config("#map-05", response, {
		// 	fill: {
		// 		basicColor: "#259200",
		// 		hoverColor: "#57cb00",
		// 		clickColor: "#2e6f18"
		// 	}
		// });

		// jsMap.config("#map-06", response, {
		// 	fill: {
		// 		basicColor: {
		// 			heilongjiang: "#ff5900",
		// 			jilin: "#19bb00",
		// 			liaoning: "#6800ff"
		// 		},
		// 		hoverColor: {
		// 			heilongjiang: "#ff8c4e",
		// 			jilin: "#1fe000",
		// 			liaoning: "#954dff"
		// 		},
		// 		clickColor: {
		// 			heilongjiang: "#c94600",
		// 			jilin: "#159a00",
		// 			liaoning: "#5200c9"
		// 		}
		// 	}
		// });

		// jsMap.config("#map-07", response, {
		// 	disabled: {
		// 		name: ["heilongjiang", "jilin", "liaoning"]
		// 	}
		// });

		// jsMap.config("#map-08", response, {
		// 	disabled: {
		// 		name: ["heilongjiang", "jilin", "liaoning"],
		// 		except: true
		// 	}
		// });



		// var $hoverCallback = $("#hover-callback");
		// jsMap.config("#map-10", response, {
		// 	hoverCallback: function(id, name) {
		// 		$hoverCallback.text(id + " --- " + name);
		// 	}
		// });

		// var $clickCallback = $("#click-callback");
		// jsMap.config("#map-11", response, {
		// 	clickCallback: function(id, name) {
		// 		$clickCallback.text(id + " --- " + name);
		// 	}
		// });

	})

})
