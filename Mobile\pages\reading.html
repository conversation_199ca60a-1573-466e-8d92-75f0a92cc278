<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>阅读学习 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <style>
        .reading-container {
            background: #fff;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .reading-header {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            padding: 15px 20px;
            position: sticky;
            top: 0;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .back-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            cursor: pointer;
        }
        
        .reading-title {
            font-size: 16px;
            font-weight: 500;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .header-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .action-icon {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            cursor: pointer;
        }
        
        .reading-content {
            flex: 1;
            padding: 20px;
            line-height: 1.8;
            font-size: 16px;
            color: #333;
        }
        
        .content-title {
            font-size: 24px;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
            line-height: 1.4;
        }
        
        .content-meta {
            text-align: center;
            color: #666;
            font-size: 14px;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .content-body {
            color: #444;
            line-height: 1.8;
        }
        
        .content-body p {
            margin-bottom: 16px;
            text-indent: 2em;
        }
        
        .content-body h1,
        .content-body h2,
        .content-body h3 {
            color: #c00714;
            margin: 24px 0 16px;
            font-weight: 600;
        }
        
        .content-body h1 {
            font-size: 20px;
        }
        
        .content-body h2 {
            font-size: 18px;
        }
        
        .content-body h3 {
            font-size: 16px;
        }
        
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 50vh;
            color: #666;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-container {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }
        
        .error-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .error-message {
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .retry-btn {
            background: #c00714;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
        }
        
        .progress-bar {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            height: 4px;
            background: #f0f0f0;
            z-index: 100;
        }
        
        .progress-fill {
            height: 100%;
            background: #c00714;
            width: 0%;
            transition: width 0.3s ease;
        }
        
        .font-size-panel {
            position: fixed;
            top: 70px;
            right: 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            padding: 15px;
            display: none;
            z-index: 200;
        }
        
        .font-size-panel.show {
            display: block;
        }
        
        .font-size-btn {
            display: block;
            width: 100%;
            padding: 8px 12px;
            margin: 4px 0;
            border: 1px solid #e0e0e0;
            background: white;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        
        .font-size-btn.active {
            background: #c00714;
            color: white;
            border-color: #c00714;
        }
    </style>
</head>
<body>
    <div class="reading-container">
        <!-- 头部 -->
        <div class="reading-header">
            <div class="header-left">
                <button class="back-btn" onclick="goBack()">
                    ←
                </button>
                <div class="reading-title" id="readingTitle">加载中...</div>
            </div>
            <div class="header-actions">
                <button class="action-icon" onclick="toggleFontSize()" title="字体大小">
                    A
                </button>
                <button class="action-icon" onclick="toggleBookmark()" title="书签">
                    🔖
                </button>
            </div>
        </div>
        
        <!-- 字体大小面板 -->
        <div class="font-size-panel" id="fontSizePanel">
            <button class="font-size-btn" onclick="setFontSize(14)">小</button>
            <button class="font-size-btn active" onclick="setFontSize(16)">中</button>
            <button class="font-size-btn" onclick="setFontSize(18)">大</button>
            <button class="font-size-btn" onclick="setFontSize(20)">特大</button>
        </div>
        
        <!-- 内容区域 -->
        <div class="reading-content" id="readingContent">
            <div class="loading-container">
                <div class="loading-spinner"></div>
                <p>正在加载内容...</p>
            </div>
        </div>
        
        <!-- 进度条 -->
        <div class="progress-bar">
            <div class="progress-fill" id="progressFill"></div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    
    <script>
        let currentFontSize = 16;
        
        // 获取URL参数
        function getUrlParam(name) {
            const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            const r = window.location.search.substr(1).match(reg);
            return r ? decodeURIComponent(r[2]) : null;
        }
        
        // 返回上一页
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '../index.html';
            }
        }
        
        // 切换字体大小面板
        function toggleFontSize() {
            const panel = document.getElementById('fontSizePanel');
            panel.classList.toggle('show');
        }
        
        // 设置字体大小
        function setFontSize(size) {
            currentFontSize = size;
            const content = document.querySelector('.content-body');
            if (content) {
                content.style.fontSize = size + 'px';
            }
            
            // 更新按钮状态
            document.querySelectorAll('.font-size-btn').forEach(btn => {
                btn.classList.remove('active');
            });
            event.target.classList.add('active');
            
            // 隐藏面板
            document.getElementById('fontSizePanel').classList.remove('show');
        }
        
        // 切换书签
        function toggleBookmark() {
            alert('书签功能开发中...');
        }
        
        // 更新阅读进度
        function updateProgress() {
            const content = document.getElementById('readingContent');
            const scrollTop = content.scrollTop;
            const scrollHeight = content.scrollHeight - content.clientHeight;
            const progress = scrollHeight > 0 ? (scrollTop / scrollHeight) * 100 : 0;
            
            document.getElementById('progressFill').style.width = progress + '%';
        }
        
        // 加载内容
        function loadContent(id, type) {
            if (!id || !type) {
                showError('缺少必要参数');
                return;
            }
            
            let apiUrl = '';
            if (type === 'redbook') {
                apiUrl = baseurl + "/web/posts/" + id;
            } else if (type === 'course') {
                apiUrl = baseurl + "/web/course/" + id;
            } else {
                showError('不支持的内容类型');
                return;
            }
            
            $.ajax({
                url: apiUrl,
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200' && res.data) {
                        renderContent(res.data, type);
                    } else {
                        showError('获取内容失败：' + (res.message || '未知错误'));
                    }
                },
                error: (err) => {
                    console.error('加载内容失败:', err);
                    showError('网络错误，请稍后重试');
                }
            });
        }
        
        // 渲染内容
        function renderContent(data, type) {
            console.log('书籍数据:', data); // 调试信息

            const title = data.title || data.titleName || '无标题';
            const author = data.author || data.principal || '未知';
            const content = data.content || data.summary || '暂无内容';
            const createTime = data.createTime || data.publishedTime;

            // 检查是否有PDF文件 - 参考PC版的数据结构
            let pdfPath = null;
            let pdfFileName = null;

            // 首先检查 attachmentDtoList (PC版使用的主要字段)
            if (data.attachmentDtoList && data.attachmentDtoList.length > 0) {
                console.log('attachmentDtoList:', data.attachmentDtoList);
                const pdfAttachment = data.attachmentDtoList.find(item =>
                    item.attachmentPath && item.attachmentPath.toLowerCase().endsWith('.pdf')
                );
                if (pdfAttachment) {
                    pdfPath = pdfAttachment.attachmentPath;
                    pdfFileName = pdfAttachment.fileName || (title + '.pdf');
                    console.log('从attachmentDtoList找到PDF:', pdfPath);
                }
            }

            // 备用检查 attachmentPath 字段
            if (!pdfPath && data.attachmentPath && data.attachmentPath.length > 0) {
                console.log('attachmentPath:', data.attachmentPath);
                const foundPath = data.attachmentPath.find(path => path.toLowerCase().endsWith('.pdf'));
                if (foundPath) {
                    pdfPath = foundPath;
                    pdfFileName = title + '.pdf';
                    console.log('从attachmentPath找到PDF:', pdfPath);
                }
            }

            // 其他备用字段检查
            if (!pdfPath && data.filePath && data.filePath.length > 0) {
                console.log('filePath:', data.filePath);
                const foundPath = data.filePath.find(path => path.toLowerCase().endsWith('.pdf'));
                if (foundPath) {
                    pdfPath = foundPath;
                    pdfFileName = title + '.pdf';
                }
            }

            console.log('找到的PDF路径:', pdfPath);
            console.log('PDF文件名:', pdfFileName);

            // 如果有PDF文件，跳转到PDF查看器
            if (pdfPath) {
                const pdfUrl = baseurl + pdfPath;
                console.log('跳转到PDF查看器:', pdfUrl);
                window.location.href = `pdf-viewer.html?url=${encodeURIComponent(pdfUrl)}&title=${encodeURIComponent(pdfFileName || title)}`;
                return;
            }

            console.log('未找到PDF文件，显示文本内容');
            
            document.getElementById('readingTitle').textContent = title;
            document.title = title + ' - 思政一体化平台';
            
            // 如果内容为空或只是"暂无内容"，显示特殊提示
            let contentHtml;
            if (!content || content === '暂无内容' || content.trim() === '') {
                contentHtml = `
                    <div style="text-align: center; padding: 40px 20px; color: #666;">
                        <div style="font-size: 48px; margin-bottom: 20px;">📚</div>
                        <div style="font-size: 18px; margin-bottom: 15px; color: #333;">内容正在准备中</div>
                        <div style="font-size: 14px; line-height: 1.6; color: #666;">
                            该书籍可能是PDF格式或内容尚未录入系统。<br>
                            请尝试以下操作：
                        </div>
                        <div style="margin: 20px 0; text-align: left; max-width: 300px; margin-left: auto; margin-right: auto;">
                            <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 6px; font-size: 14px;">
                                📄 如果是PDF文件，请返回详情页点击"阅读PDF"
                            </div>
                            <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 6px; font-size: 14px;">
                                🔄 尝试刷新页面重新加载
                            </div>
                            <div style="margin: 10px 0; padding: 10px; background: #f8f9fa; border-radius: 6px; font-size: 14px;">
                                📞 联系管理员获取帮助
                            </div>
                        </div>
                        <div style="margin-top: 30px;">
                            <button onclick="window.history.back()" style="background: #c00714; color: white; border: none; padding: 12px 24px; border-radius: 6px; font-size: 14px; margin: 0 10px; cursor: pointer;">
                                ← 返回详情页
                            </button>
                            <button onclick="location.reload()" style="background: #6c757d; color: white; border: none; padding: 12px 24px; border-radius: 6px; font-size: 14px; margin: 0 10px; cursor: pointer;">
                                🔄 刷新页面
                            </button>
                        </div>
                    </div>
                `;
            } else {
                contentHtml = formatContent(content);
            }

            const html = `
                <div class="content-title">${title}</div>
                <div class="content-meta">
                    作者：${author} | 发布时间：${formatTime(createTime)}
                </div>
                <div class="content-body" style="font-size: ${currentFontSize}px;">
                    ${contentHtml}
                </div>
            `;
            
            document.getElementById('readingContent').innerHTML = html;
            
            // 绑定滚动事件
            document.getElementById('readingContent').addEventListener('scroll', updateProgress);
        }
        
        // 格式化内容
        function formatContent(content) {
            if (!content) return '<p>暂无内容</p>';
            
            // 简单的HTML格式化
            return content
                .replace(/\n\n/g, '</p><p>')
                .replace(/\n/g, '<br>')
                .replace(/^/, '<p>')
                .replace(/$/, '</p>');
        }
        
        // 格式化时间
        function formatTime(timeStr) {
            if (!timeStr) return '未知时间';
            try {
                const time = new Date(timeStr);
                return time.toLocaleDateString('zh-CN');
            } catch (e) {
                return timeStr;
            }
        }
        
        // 显示错误信息
        function showError(message) {
            const html = `
                <div class="error-container">
                    <div class="error-icon">📖</div>
                    <div class="error-message">${message}</div>
                    <button class="retry-btn" onclick="location.reload()">重新加载</button>
                </div>
            `;
            document.getElementById('readingContent').innerHTML = html;
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            const id = getUrlParam('id');
            const type = getUrlParam('type');
            loadContent(id, type);
            
            // 点击其他地方关闭字体面板
            document.addEventListener('click', function(e) {
                const panel = document.getElementById('fontSizePanel');
                const fontBtn = e.target.closest('.action-icon');
                if (!panel.contains(e.target) && !fontBtn) {
                    panel.classList.remove('show');
                }
            });
        });
    </script>
</body>
</html>
