<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>PDF阅读器 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <style>
        .pdf-container {
            background: #f5f5f5;
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .pdf-header {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            padding: 15px 20px;
            position: sticky;
            top: 0;
            z-index: 100;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .back-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            cursor: pointer;
        }
        
        .pdf-title {
            font-size: 16px;
            font-weight: 500;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .header-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .action-icon {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            cursor: pointer;
        }
        
        .pdf-viewer {
            flex: 1;
            background: white;
            position: relative;
            overflow: hidden;
        }
        
        .pdf-canvas-container {
            width: 100%;
            height: 100%;
            overflow: auto;
            display: flex;
            justify-content: center;
            align-items: flex-start;
            padding: 20px;
            box-sizing: border-box;
        }
        
        .pdf-canvas {
            max-width: 100%;
            height: auto;
            box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            border-radius: 4px;
        }
        
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 50vh;
            color: #666;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-container {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }
        
        .error-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .error-message {
            font-size: 16px;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .download-btn {
            display: inline-block;
            background: #c00714;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            margin: 10px;
        }
        
        .pdf-controls {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            padding: 15px 20px;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .page-nav {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .nav-btn {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            color: #333;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .nav-btn:hover {
            background: #e9ecef;
        }
        
        .nav-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .page-info {
            font-size: 14px;
            color: #666;
            min-width: 80px;
            text-align: center;
        }
        
        .zoom-controls {
            display: flex;
            align-items: center;
            gap: 10px;
        }
        
        .zoom-btn {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            color: #333;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            cursor: pointer;
        }
        
        .zoom-info {
            font-size: 12px;
            color: #999;
            min-width: 40px;
            text-align: center;
        }
        
        .fallback-viewer {
            width: 100%;
            height: 100vh;
            border: none;
        }
    </style>
</head>
<body>
    <div class="pdf-container">
        <!-- 头部 -->
        <div class="pdf-header">
            <div class="header-left">
                <button class="back-btn" onclick="goBack()">
                    ←
                </button>
                <div class="pdf-title" id="pdfTitle">PDF阅读器</div>
            </div>
            <div class="header-actions">
                <button class="action-icon" onclick="downloadPDF()" title="下载">
                    ⬇
                </button>
                <button class="action-icon" onclick="toggleFullscreen()" title="全屏">
                    ⛶
                </button>
            </div>
        </div>
        
        <!-- PDF查看器 -->
        <div class="pdf-viewer" id="pdfViewer">
            <div class="loading-container">
                <div class="loading-spinner"></div>
                <p>正在加载PDF文档...</p>
            </div>
        </div>
        
        <!-- 控制栏 -->
        <div class="pdf-controls" id="pdfControls" style="display: none;">
            <div class="page-nav">
                <button class="nav-btn" id="prevBtn" onclick="previousPage()">‹</button>
                <div class="page-info" id="pageInfo">1 / 1</div>
                <button class="nav-btn" id="nextBtn" onclick="nextPage()">›</button>
            </div>
            <div class="zoom-controls">
                <button class="zoom-btn" onclick="zoomOut()">-</button>
                <div class="zoom-info" id="zoomInfo">100%</div>
                <button class="zoom-btn" onclick="zoomIn()">+</button>
            </div>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    
    <!-- PDF.js库 -->
    <script src="https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.min.js"></script>
    
    <script>
        let pdfDoc = null;
        let pageNum = 1;
        let pageCount = 0;
        let scale = 1.0;
        let pdfUrl = '';
        let pdfTitle = '';
        
        // 获取URL参数
        function getUrlParam(name) {
            const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            const r = window.location.search.substr(1).match(reg);
            return r ? decodeURIComponent(r[2]) : null;
        }
        
        // 返回上一页
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '../index.html';
            }
        }
        
        // 下载PDF
        function downloadPDF() {
            if (pdfUrl) {
                const link = document.createElement('a');
                link.href = pdfUrl;
                link.download = pdfTitle || 'document.pdf';
                link.click();
            }
        }
        
        // 切换全屏
        function toggleFullscreen() {
            if (!document.fullscreenElement) {
                document.documentElement.requestFullscreen();
            } else {
                document.exitFullscreen();
            }
        }
        
        // 上一页
        function previousPage() {
            if (pageNum > 1) {
                pageNum--;
                renderPage();
            }
        }
        
        // 下一页
        function nextPage() {
            if (pageNum < pageCount) {
                pageNum++;
                renderPage();
            }
        }
        
        // 放大
        function zoomIn() {
            scale = Math.min(scale * 1.2, 3.0);
            renderPage();
            updateZoomInfo();
        }
        
        // 缩小
        function zoomOut() {
            scale = Math.max(scale / 1.2, 0.5);
            renderPage();
            updateZoomInfo();
        }
        
        // 更新缩放信息
        function updateZoomInfo() {
            document.getElementById('zoomInfo').textContent = Math.round(scale * 100) + '%';
        }
        
        // 更新页面信息
        function updatePageInfo() {
            document.getElementById('pageInfo').textContent = `${pageNum} / ${pageCount}`;
            document.getElementById('prevBtn').disabled = pageNum <= 1;
            document.getElementById('nextBtn').disabled = pageNum >= pageCount;
        }
        
        // 渲染页面
        function renderPage() {
            if (!pdfDoc) return;
            
            pdfDoc.getPage(pageNum).then(function(page) {
                const viewport = page.getViewport({ scale: scale });
                const canvas = document.getElementById('pdfCanvas');
                const context = canvas.getContext('2d');
                
                canvas.height = viewport.height;
                canvas.width = viewport.width;
                
                const renderContext = {
                    canvasContext: context,
                    viewport: viewport
                };
                
                page.render(renderContext);
                updatePageInfo();
            });
        }
        
        // 加载PDF
        function loadPDF(url, title) {
            pdfUrl = url;
            pdfTitle = title;
            
            // 更新标题
            document.getElementById('pdfTitle').textContent = title || 'PDF文档';
            document.title = (title || 'PDF文档') + ' - 思政一体化平台';
            
            // 设置PDF.js worker
            pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@3.11.174/build/pdf.worker.min.js';
            
            // 加载PDF文档
            pdfjsLib.getDocument(url).promise.then(function(pdf) {
                pdfDoc = pdf;
                pageCount = pdf.numPages;
                pageNum = 1;
                
                // 创建canvas
                const canvasContainer = document.createElement('div');
                canvasContainer.className = 'pdf-canvas-container';
                
                const canvas = document.createElement('canvas');
                canvas.id = 'pdfCanvas';
                canvas.className = 'pdf-canvas';
                
                canvasContainer.appendChild(canvas);
                
                // 替换加载内容
                document.getElementById('pdfViewer').innerHTML = '';
                document.getElementById('pdfViewer').appendChild(canvasContainer);
                
                // 显示控制栏
                document.getElementById('pdfControls').style.display = 'flex';
                
                // 渲染第一页
                renderPage();
                updateZoomInfo();
                
            }).catch(function(error) {
                console.error('PDF加载失败:', error);
                showError('PDF文档加载失败', '可能是网络问题或文件格式不支持');
            });
        }
        
        // 显示错误信息
        function showError(title, message) {
            const html = `
                <div class="error-container">
                    <div class="error-icon">📄</div>
                    <div class="error-message">
                        <strong>${title}</strong><br>
                        ${message}
                    </div>
                    <a href="${pdfUrl}" class="download-btn" download="${pdfTitle}">
                        📥 下载PDF文件
                    </a>
                    <div style="margin-top: 20px;">
                        <button onclick="tryFallbackViewer()" class="download-btn" style="background: #007bff;">
                            🔄 尝试其他方式打开
                        </button>
                    </div>
                </div>
            `;
            document.getElementById('pdfViewer').innerHTML = html;
        }
        
        // 尝试备用查看器
        function tryFallbackViewer() {
            const iframe = document.createElement('iframe');
            iframe.className = 'fallback-viewer';
            iframe.src = pdfUrl;
            
            document.getElementById('pdfViewer').innerHTML = '';
            document.getElementById('pdfViewer').appendChild(iframe);
        }
        
        // 从书籍详情获取PDF信息
        function loadBookPDF(bookId) {
            $.ajax({
                url: baseurl + "/web/posts/" + bookId,
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200' && res.data) {
                        const book = res.data;
                        const title = book.title || '无标题';
                        
                        // 查找PDF文件
                        let pdfPath = null;
                        if (book.attachmentPath && book.attachmentPath.length > 0) {
                            // 查找PDF附件
                            pdfPath = book.attachmentPath.find(path => path.toLowerCase().endsWith('.pdf'));
                        }
                        
                        if (pdfPath) {
                            const pdfUrl = baseurl + pdfPath;
                            loadPDF(pdfUrl, title);
                        } else {
                            showError('未找到PDF文件', '该书籍可能不是PDF格式，或文件路径有误');
                        }
                    } else {
                        showError('获取书籍信息失败', res.message || '未知错误');
                    }
                },
                error: (err) => {
                    console.error('加载书籍信息失败:', err);
                    showError('网络错误', '请检查网络连接后重试');
                }
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            const bookId = getUrlParam('id');
            const pdfUrl = getUrlParam('url');
            const title = getUrlParam('title');
            
            if (pdfUrl) {
                // 直接加载PDF URL
                loadPDF(decodeURIComponent(pdfUrl), decodeURIComponent(title || 'PDF文档'));
            } else if (bookId) {
                // 从书籍信息中获取PDF
                loadBookPDF(bookId);
            } else {
                showError('参数错误', '缺少必要的PDF文件信息');
            }
        });
    </script>
</body>
</html>
