<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0" />
		<title>思政一体化平台-学习与成绩统计</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/wangEditor.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<!-- 导航增强脚本 -->
		<script src="./js/nav-enhancement.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/echarts.js" type="text/javascript" charset="utf-8"></script>
		
		<!-- 添加outputLog函数以解决ReferenceError: outputLog is not defined错误 -->
		<script type="text/javascript">
			function outputLog(type, msg, isDoNotCheckOnce) {
				if (type === 'warn') {
					console.warn(msg);
				} else if (type === 'error') {
					console.error(msg);
				} else {
					console.log(msg);
				}
			}
		</script>
		
		<!-- 添加现代化的样式 -->
		<style>
			body {
				background: url(img/background.jpg) no-repeat;
				background-size: cover;
				background-attachment: fixed;
			}
			
			/* 现代化导航栏美化 - 与页面红色主题统一，红色偏橙色 */
			.boxleft {
				background: white;
				border-radius: 16px;
				box-shadow: 0 8px 32px rgba(220, 53, 69, 0.08);
				overflow: hidden;
				border: 1px solid rgba(231, 76, 60, 0.05);
				position: relative;
				backdrop-filter: blur(20px);
			}
			
			.boxleft::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8));
				pointer-events: none;
				border-radius: 16px;
			}
			
			/* 导航栏顶部标题区域 - 红色偏橙色渐变 */
			.lefttopview {
				width: 100%;
				height: 65px;
				background: linear-gradient(135deg, #dc3545, #e74c3c, #ff6b35);
				background-size: 300% 300%;
				animation: gradientShift 6s ease infinite;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 18px;
				color: #FFFFFF;
				font-weight: 700;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif;
				position: relative;
				box-shadow: 0 4px 20px rgba(220, 53, 69, 0.25);
				letter-spacing: 1.5px;
				border-radius: 16px 16px 0 0;
			}
			
			/* 渐变动画效果 */
			@keyframes gradientShift {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.lefttopview img {
				width: 24px;
				height: 24px;
				display: block;
				margin-right: 12px;
				filter: brightness(0) invert(1) drop-shadow(0 0 6px rgba(255, 255, 255, 0.4));
			}
			
			.lefttopview label {
				display: flex;
				align-items: center;
				text-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
			}
			
			/* 导航菜单容器 */
			.leftitembox {
				background: transparent;
				padding: 20px 0;
				position: relative;
				z-index: 2;
			}
			
			.leftitembox::before {
				content: '';
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 85%;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.15), transparent);
			}
			
			/* 导航菜单项 - 现代化设计与图标 */
			.leftitem {
				height: auto;
				padding: 16px 24px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				font-size: 15px;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif;
				font-weight: 500;
				color: #4a5568;
				border-bottom: none;
				text-decoration: none;
				transition: color 0.2s ease, background-color 0.2s ease;
				position: relative;
				margin: 4px 16px;
				border-radius: 12px;
				background: transparent;
				overflow: hidden;
				min-height: 48px;
			}
			
			/* 为不同菜单项添加对应的SVG图标 */
			.leftitem::before {
				content: '';
				width: 20px;
				height: 20px;
				margin-right: 12px;
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				opacity: 0.7;
				transition: opacity 0.2s ease;
				flex-shrink: 0;
			}
			
			/* 个人信息图标 */
			.leftitem[href*="userinfo"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
			}
			
			/* 学生成绩统计图标 */
			.leftitem[href*="studentachievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z'/%3E%3C/svg%3E");
			}
			
			/* 学习记录统计图标 */
			.leftitem[href*="studentlearning"]::before,
			.leftitem.activeleftitem::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
			}
			
			/* 考试管理图标 */
			.leftitem[href*="examination"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E");
			}
			
			/* 学习任务管理图标 */
			.leftitem[href*="learning"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
			}
			
			/* 成果展示图标 */
			.leftitem[href*="achievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E");
			}
			
			/* 发布心声图标 */
			.leftitem[href*="voice"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z'/%3E%3C/svg%3E");
			}
			
			/* 菜单项悬浮态 - 完全无抖动 */
			.leftitem:hover {
				background: linear-gradient(135deg, rgba(220, 53, 69, 0.06), rgba(231, 76, 60, 0.08));
				color: #dc3545;
			}
			
			.leftitem:hover::before {
				opacity: 1;
			}
			
			/* 激活状态的菜单项 - 红色偏橙色渐变与白色图标 */
			.activeleftitem {
				font-weight: 700 !important;
				color: white !important;
				background: linear-gradient(135deg, #dc3545, #e74c3c, #ff6b35) !important;
				background-size: 300% 300%;
				animation: activeGradient 4s ease infinite;
				box-shadow: 0 8px 32px rgba(220, 53, 69, 0.3);
				border-radius: 12px;
			}
			
			@keyframes activeGradient {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.activeleftitem::before {
				opacity: 1 !important;
				filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
			}
			
			.activeleftitem::after {
				content: '';
				position: absolute;
				right: 16px;
				top: 50%;
				transform: translateY(-50%);
				width: 8px;
				height: 8px;
				background: white;
				border-radius: 50%;
				box-shadow: 0 0 12px rgba(255, 255, 255, 0.8);
				animation: activePulse 2s ease infinite;
			}
			
			@keyframes activePulse {
				0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
				50% { opacity: 0.6; transform: translateY(-50%) scale(1.4); }
			}
			
			/* 添加精美的分隔线效果 */
			.leftitem:not(:last-child):not(.activeleftitem) {
				border-bottom: 1px solid rgba(231, 76, 60, 0.06);
				margin-bottom: 3px;
			}
			
			.leftitem:not(:last-child):not(.activeleftitem)::after {
				content: '';
				position: absolute;
				bottom: -1px;
				left: 40px;
				right: 24px;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.15), transparent);
				opacity: 0.5;
			}
			
			/* 整体容器增强效果 */
			.contentview .boxleft {
				background: rgba(255, 255, 255, 0.95);
				border-radius: 16px;
				box-shadow: 0 8px 40px rgba(220, 53, 69, 0.08);
				border: 1px solid rgba(231, 76, 60, 0.05);
				overflow: hidden;
				position: relative;
				backdrop-filter: blur(20px);
			}
			
			.contentview .boxleft::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				height: 3px;
				background: linear-gradient(90deg, #dc3545, #e74c3c, #ff6b35);
				opacity: 0;
				transition: opacity 0.4s ease;
			}
			
			.contentview .boxleft:hover::before {
				opacity: 1;
			}
			
			/* 响应式优化 */
			@media (max-width: 768px) {
				.boxleft {
					border-radius: 12px;
					margin-bottom: 20px;
				}
				
				.lefttopview {
					height: 55px;
					font-size: 16px;
					letter-spacing: 1px;
				}
				
				.lefttopview img {
					width: 20px;
					height: 20px;
					margin-right: 10px;
				}
				
				.leftitem {
					padding: 14px 20px;
					font-size: 14px;
					margin: 3px 12px;
					min-height: 44px;
				}
				
				.leftitem::before {
					width: 18px;
					height: 18px;
					margin-right: 10px;
				}
			}
			
			/* 字体优化 - 思源黑体 */
			.lefttopview,
			.leftitem {
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Source Han Sans', 'Noto Sans CJK SC', 'Microsoft YaHei UI', 'Microsoft YaHei', '微软雅黑', sans-serif;
			}
			
			/* 增强的交互反馈 */
			.leftitem {
				cursor: pointer;
			}
			
			.leftitem:active {
				transform: scale(0.98);
				transition: all 0.1s ease;
			}
			
			.activeleftitem:active {
				transform: scale(1.02);
			}
			
			/* 为菜单项添加微妙的进入动画 */
			.leftitem {
				animation: slideIn 0.5s ease-out forwards;
				opacity: 0;
				transform: translateX(-20px);
			}
			
			.leftitem:nth-child(1) { animation-delay: 0.1s; }
			.leftitem:nth-child(2) { animation-delay: 0.2s; }
			.leftitem:nth-child(3) { animation-delay: 0.3s; }
			.leftitem:nth-child(4) { animation-delay: 0.4s; }
			.leftitem:nth-child(5) { animation-delay: 0.5s; }
			.leftitem:nth-child(6) { animation-delay: 0.6s; }
			.leftitem:nth-child(7) { animation-delay: 0.7s; }
			
			@keyframes slideIn {
				to {
					opacity: 1;
					transform: translateX(0);
				}
			}
		</style>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a href="studentachievement.html" class="leftitem">学生成绩统计</a>
						<a class="leftitem activeleftitem">学习记录统计</a>
						<a href="userexamination2.html" class="leftitem">考试管理</a>
						<a href="learningtasks.html" class="leftitem">学习任务管理</a>
						<a href="achievements.html" class="leftitem">成果展示</a>
						<a href="releaseyourvoice2.html" class="leftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="righttopview2">
						<!-- <label>学习路径</label> -->
						<label class="accccc">学生学习与成绩统计分析</label>
					</div>
					
					<!-- 美化的筛选区域 -->
					<div class="filter-container" style="background: #f8f8f8; padding: 15px; border-radius: 4px; margin-bottom: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">
						<div class="filter-row" style="display: flex; flex-wrap: wrap; gap: 15px; margin-bottom: 15px;">
							<select id="years" onchange="yearschange(this)" style="flex: 1; min-width: 140px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: #fff;">
							</select>
							<select id="xuekeselect" onchange="xuekechange()" style="flex: 1; min-width: 140px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: #fff;">
								<option value="0">请选择学科</option>
							</select>
							<select id="xyselect" onchange="xychange(this)" style="flex: 1; min-width: 140px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: #fff;">
							</select>
						</div>
						<div class="filter-row" style="display: flex; flex-wrap: wrap; gap: 15px;">
							<select id="zyselect" onchange="zychange(this)" style="flex: 1; min-width: 140px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: #fff;">
							</select>
							<select id="bjselect" onchange="bjchange(this)" style="flex: 1; min-width: 140px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: #fff;">
							</select>
							<input id="userxh" oninput="filterData()" placeholder="请输入学号" style="flex: 1; min-width: 140px; padding: 8px; border: 1px solid #ddd; border-radius: 4px; background: #fff;">
						</div>
					</div>
					
					<div class="data-filter-note" style="text-align: center; color: #666; font-size: 14px; margin: 5px 0 15px; background: #f8f8f8; padding: 8px; border-radius: 4px;">
						注：统计数据已按每个学生的最高成绩进行筛选展示
					</div>

					<!-- 替换原有图表区域，使用更现代化的卡片设计 -->
					<div class="dashboard-container">
						<!-- 第一行：访问量统计和时长曲线变化图表（并列） -->
						<div class="chart-row" style="display: flex; gap: 20px; margin-bottom: 20px;">
							<div class="chart-card" style="flex: 1; background: #fff; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 20px;">
								<div class="chart-title" style="font-size: 18px; margin-bottom: 15px;">平台访问量统计</div>
								<div id="main1" style="height: 300px;"></div>
							</div>
							<div class="chart-card" style="flex: 1; background: #fff; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 20px;">
								<div class="chart-title" style="font-size: 18px; margin-bottom: 15px;">近期学习时长趋势</div>
								<div id="timelineTrend" style="height: 300px;"></div>
							</div>
						</div>
						
						<!-- 第二行：学习时间统计（占满） -->
						<div class="chart-row" style="display: flex; margin-bottom: 20px;">
							<div class="chart-card" style="flex: 1; background: #fff; border-radius: 8px; box-shadow: 0 2px 5px rgba(0,0,0,0.1); padding: 20px;">
								<div class="chart-title" style="font-size: 18px; margin-bottom: 15px; display: flex; justify-content: space-between; align-items: center;">
									<span>平台学习时间统计</span>
									<label style="font-size: 14px; color: #666; display: flex; align-items: center;">
										<input type="checkbox" id="onlyShowActive" checked onchange="toggleActiveStudents()" style="margin-right: 5px;">
										只显示有学习时长的学生
									</label>
								</div>
								<div class="chart-scroll-container" style="width: 100%; overflow-x: auto;">
									<div id="main2" style="height: 300px; min-width: 800px;"></div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let userinfo = sessionStorage.getItem("userinfo")


			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getfooterlink()
				getpaperallyears()
				getxueyuan()
				getallxueke()
				
				// 获取最近一次任务数据
				getLatestTaskData();
				
				// 初始化时长曲线变化图表
				initTimelineTrend()
			})

			// 全局变量存储原始数据
			let originalData1 = [];
			let originalData2 = [];
			
			// 是否只显示有学习时长的学生
			let onlyShowActive = true;
			
			// 最近任务ID
			let latestTaskId = null;
			
			// 获取最近的一次任务数据
			function getLatestTaskData() {
				$.ajax({
					url: baseurl + "/task/latest",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: function(res) {
						if (res.code == '200' && res.data) {
							latestTaskId = res.data.id;
							
							// 如果有学科ID，则优先使用
							if (res.data.subjectId) {
								subjectId = res.data.subjectId;
								$("#xuekeselect").val(subjectId);
							}
							
							// 更新年份选择框（如果任务有相关时间段）
							if (res.data.startTime && res.data.endTime) {
								let startDate = new Date(res.data.startTime);
								let endDate = new Date(res.data.endTime);
								
								// 设置时间范围
								time1 = formatDate(startDate);
								time2 = formatDate(endDate);
								
								// 找到最接近的学期并选中
								let year = startDate.getFullYear();
								let month = startDate.getMonth() + 1;
								let term = month >= 9 || month <= 2 ? year + '-下学期' : year + '-上学期';
								
								// 更新年份选择框
								$("#years").val(term).change();
							}
							
							// 获取统计数据
							getnumlist();
							fetchTimelineTrend();
							
							// 更新提示信息
							$(".data-filter-note").append('<div style="margin-top: 5px; font-style: italic;">当前显示: ' + 
								(res.data.title || '最近任务') + '</div>');
						} else {
							// 获取失败时显示所有数据
							getnumlist();
							showmain2();
							fetchTimelineTrend();
						}
					},
					error: function() {
						// 错误时加载默认数据
						getnumlist();
						showmain2();
						fetchTimelineTrend();
					}
				});
			}
			
			// 日期格式化辅助函数
			function formatDate(date) {
				let year = date.getFullYear();
				let month = (date.getMonth() + 1).toString().padStart(2, '0');
				let day = date.getDate().toString().padStart(2, '0');
				return year + '-' + month + '-' + day;
			}
			
			// 切换是否只显示有时长的学生
			function toggleActiveStudents() {
				onlyShowActive = document.getElementById('onlyShowActive').checked;
				renderTimeChart();
			}
			
			// 渲染时长图表
			function renderTimeChart() {
				// 销毁已存在的实例
				let existingChart = echarts.getInstanceByDom(document.getElementById('main2'));
				if (existingChart) {
					existingChart.dispose();
				}
				
				if (!originalData1.length || !originalData2.length) return;
				
				// 处理数据并根据设置过滤
				let filteredData1 = [...originalData1];
				let filteredData2 = JSON.parse(JSON.stringify(originalData2));
				
				if (onlyShowActive) {
					// 过滤学习时间为0的学生数据
					for (let series of filteredData2) {
						const parsedData = JSON.parse(series.datas);
						const hasValue = parsedData.some(val => parseInt(val) > 0);
						
						if (hasValue) {
							// 转换为秒并保留有值的数据点
							series.data = parsedData.map((val, index) => {
								return parseInt(val) / 1000;
							});
						} else {
							// 如果整个系列都是0，将设置为空数组
							series.data = [];
						}
					}
					
					// 过滤数据，只保留有学习时长的学生
					let nonZeroIndices = new Set();
					filteredData2.forEach(series => {
						const parsedData = JSON.parse(series.datas);
						parsedData.forEach((val, idx) => {
							if (parseInt(val) > 0) {
								nonZeroIndices.add(idx);
							}
						});
					});
					
					// 只保留有学习时长的学生对应的类别和数据
					if (nonZeroIndices.size > 0) {
						const indices = Array.from(nonZeroIndices).sort((a, b) => a - b);
						filteredData1 = indices.map(idx => filteredData1[idx]);
						
						filteredData2.forEach(series => {
							const parsedData = JSON.parse(series.datas);
							series.data = indices.map(idx => parseInt(parsedData[idx]) / 1000);
						});
					}
				} else {
					// 显示所有数据，包括0值
					filteredData2.forEach(series => {
						series.data = JSON.parse(series.datas).map(val => parseInt(val) / 1000);
					});
				}
				
				// 设置样式和交互
				filteredData2.forEach(item => {
					item.emphasis = {
						focus: 'series'
					};
					item.barWidth = '50%';
				});
				
				// 配置图表
				const labelOption = {
					show: true,
					formatter: '{name|{a}}',
					fontSize: 10,
					rich: {
						name: {}
					}
				};
				
				const myChart = echarts.init(document.getElementById('main2'));
				const option = {
					color: ["#A65D57"], // 统一配色
					title: {
						text: '平台学习时间统计（单位：秒）',
						left: 'center',
						textStyle: {
							fontSize: 16,
							fontWeight: 'normal'
						}
					},
					tooltip: {
						trigger: 'axis',
						axisPointer: {
							type: 'none'
						},
						formatter: function(params) {
							let tip = "";
							if (params != null && params.length > 0) {
								tip += params[0].name + "<br />";
								for (let i = 0; i < params.length; i++) {
									let ys = parseInt(params[i].value);
									
									let h = parseInt(ys / 60 / 60 % 24);
									let m = parseInt(ys / 60 % 60);
									let s = parseInt(ys % 60);
									
									tip += params[i].seriesName + ":  " + h + '时' + m + '分' + s + '秒' + "<br />";
								}
							}
							return tip;
						}
					},
					grid: {
						left: '3%',
						right: '4%',
						bottom: '15%', // 增加底部留白以便放置横向滚动条
						top: '15%',
						containLabel: true
					},
					dataZoom: [
						// 底部水平滚动条
						{
							type: 'slider',
							show: true,
							xAxisIndex: [0],
							start: 0,
							end: 100,
							height: 20
						}
					],
					xAxis: [{
						type: 'category',
						axisTick: {
							show: true
						},
						data: filteredData1,
						axisLabel: {
							formatter: function(value) {
								return value.split('').join('\n');
							},
							interval: 0,
							rotate: 0
						}
					}],
					yAxis: [{
						type: 'value',
						minInterval: 1,
						name: '时长(秒)',
						nameLocation: 'end'
					}],
					series: filteredData2
				};
				
				// 如果数据多于10条，默认只显示前10条
				if (filteredData1.length > 10) {
					option.dataZoom[0].end = Math.min(100, 10 * 100 / filteredData1.length);
				}
				
				// 使用配置
				myChart.setOption(option);
				
				// 响应窗口大小变化
				window.addEventListener('resize', function() {
					myChart.resize();
				});
			}

			let collegeId = null //学院
			let majorId = null //专业
			let classId = null //班级
			let time1 = null //开始时间
			let time2 = null //结束时间
			let subjectId = null // 学科ID

			let xueyuanlist = null //学院专业班级数据
			
			// 获取所有学科
			function getallxueke() {
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'get',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = '<option value="0">请选择学科</option>'
							res.data.forEach((item) => {
								html += `<option value="${item.id}">${item.name}</option>`
							})
							$("#xuekeselect").html(html)
						}
					}
				})
			}
			
			// 学科选择变更
			function xuekechange() {
				subjectId = $("#xuekeselect").val() != '0' ? $("#xuekeselect").val() : null;
				getnumlist();
				fetchTimelineTrend(); // 更新时长曲线变化图表
			}
			
			// 简化的成绩过滤函数，只保留系统需要的分析功能
			function filterData() {
				const searchTerm = $("#userxh").val().trim();
				// 重新获取数据
				getnumlist();
				// 更新时长曲线变化图表
				fetchTimelineTrend();
			}
			
			// 初始化时长曲线变化图表
			function initTimelineTrend() {
				const timelineChart = echarts.init(document.getElementById('timelineTrend'));
				const option = {
					color: ["#A65D57"],
					title: {
						text: '近期学习时长趋势',
						left: 'center',
						textStyle: {
							fontSize: 16,
							fontWeight: 'normal'
						}
					},
					tooltip: {
						trigger: 'axis',
						formatter: function(params) {
							let tip = params[0].axisValue + "<br/>";
							tip += params[0].seriesName + ": ";
							
							let seconds = params[0].value;
							let h = parseInt(seconds / 60 / 60 % 24);
							let m = parseInt(seconds / 60 % 60);
							let s = parseInt(seconds % 60);
							
							tip += h + '时' + m + '分' + s + '秒';
							return tip;
						}
					},
					grid: {
						left: '3%',
						right: '4%',
						bottom: '8%',
						top: '15%',
						containLabel: true
					},
					xAxis: {
						type: 'category',
						boundaryGap: false,
						data: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
						axisLabel: {
							interval: 0
						}
					},
					yAxis: {
						type: 'value',
						name: '时长(秒)',
						axisLabel: {
							formatter: function(value) {
								return value;
							}
						}
					},
					series: [{
						name: '学习时长',
						type: 'line',
						smooth: true,
						lineStyle: {
							width: 3
						},
						areaStyle: {
							opacity: 0.2
						},
						data: [0, 0, 0, 0, 0, 0, 0],
						markPoint: {
							data: [
								{type: 'max', name: '最大值'},
								{type: 'min', name: '最小值'}
							]
						}
					}]
				};
				
				timelineChart.setOption(option);
				
				// 响应窗口大小变化
				window.addEventListener('resize', function() {
					timelineChart.resize();
				});
				
				// 首次加载数据
				fetchTimelineTrend();
			}
			
			// 获取时长曲线变化数据
			function fetchTimelineTrend() {
				// 使用现有参数来获取统计数据，添加任务ID参数
				$.ajax({
					url: baseurl + "/stat/timeTrend",
					type: 'GET',
					contentType: "application/json",
					data: {
						collegeId, majorId, classId, time1, time2, subjectId, taskId: latestTaskId
					},
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: function(res) {
						if (res.code == '200') {
							updateTimelineTrend(res.data);
						} else {
							// 使用模拟数据
							const mockData = {
								dates: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
								values: [1800, 3600, 2400, 4800, 3000, 1200, 5400] // 模拟的秒数
							};
							updateTimelineTrend(mockData);
						}
					},
					error: function() {
						// 失败时使用模拟数据
						const mockData = {
							dates: ['周一', '周二', '周三', '周四', '周五', '周六', '周日'],
							values: [1800, 3600, 2400, 4800, 3000, 1200, 5400] // 模拟的秒数
						};
						updateTimelineTrend(mockData);
					}
				});
			}
			
			// 更新时长曲线变化图表
			function updateTimelineTrend(data) {
				const timelineChart = echarts.getInstanceByDom(document.getElementById('timelineTrend'));
				if (!timelineChart) return;
				
				// 如果服务器返回的数据格式与我们期望的不同，进行适当的转换
				let dates = data.dates || ['周一', '周二', '周三', '周四', '周五', '周六', '周日'];
				let values = data.values || [0, 0, 0, 0, 0, 0, 0];
				
				// 更新图表
				timelineChart.setOption({
					xAxis: {
						data: dates
					},
					series: [{
						data: values
					}]
				});
			}

			function getpaperallyears() { //获取所有年份
				let date = new Date()
				let nowyears = date.getFullYear()
				let smallYears = nowyears - 5
				let Years = nowyears - smallYears
				let array = []
				for (let i = 0; i <= Years; i++) {
					let yy = nowyears--
					array.push(yy + '-下学期')
					array.push(yy + '-上学期')
				}
				let yearstr = '<option value="0">全部年份</option>'
				array.map((item) => {
					yearstr += '<option value="' + item + '">' + item + '</option>'
				})
				$("#years").html(yearstr)
			}

			function yearschange(select) {
				let str = $(select).val()
				let strs = str.split('-')
				if (str == '0') {
					time1 = null
					time2 = null
				} else {
					if (strs[1] == '上学期') {
						//3-1  8-1
						time1 = strs[0] + '-3-2'
						time2 = strs[0] + '-8-1'
					} else {
						// 9- 1  2-29
						time1 = strs[0] + '-9-1'
						time2 = (parseInt(strs[0]) + 1) + '-3-1'
					}
				}
				
				getnumlist()
				// 更新时长曲线变化图表
				fetchTimelineTrend()
			}

			function getxueyuan() { //获取学院专业班级  学科树
				$.ajax({
					url: baseurl + "/binding/teacher-class",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							xueyuanlist = res.data
							// console.log(xueyuanlist)
							let xyhtml = '<option value="0">请选择学院</option>'
							xueyuanlist.forEach((item) => {
								xyhtml += '<option value="' + item.id + '">' + item.name + '</option>'
							})
							$("#xyselect").html(xyhtml)
							fleshxueyuan()
						}
					}
				})
			}

			function xychange(xy) { //当学院的list选择时
				if ($(xy).val() == '0') { //当学院选择了空 则 专业班级都为空
					collegeId = null
					majorId = null
					classId = null
				} else { //当选择了有值学院则 刷新list  专业和班级为空
					collegeId = $(xy).val()
					majorId = null
					classId = null
				}
				getnumlist()
				// 更新时长曲线变化图表
				fetchTimelineTrend()
				fleshxueyuan()
			}

			function zychange(zy) { //当专业的list选择时
				if ($(zy).val() == '0') { //当专业选择了空 则 班级为空
					majorId = null
					classId = null
				} else { //当选择了有值专业则 刷新list  班级为空
					majorId = $(zy).val()
					classId = null
				}
				getnumlist()
				// 更新时长曲线变化图表
				fetchTimelineTrend()
				fleshxueyuan()
			}

			function bjchange(bj) { //当班级选择时
				if ($(bj).val() == '0') {
					classId = null
				} else {
					classId = $(bj).val()
				}
				getnumlist()
				// 更新时长曲线变化图表
				fetchTimelineTrend()
			}

			function fleshxueyuan() { //刷新学院 专业班级数据
				if (collegeId) {
					//有学院id
					if (majorId) {
						//有学院ID  有专业ID 则 循环班级的list
						let bjhtml = '<option value="0">请选择班级</option>'
						xueyuanlist.forEach((item) => { //循环学院
							if (item.id == collegeId) { //判断学院ID
								item.children.forEach((item1) => { //循环专业
									if (item1.id == majorId) { //判断专业ID
										item1.children.forEach((item2) => {
											bjhtml += '<option value="' + item2.id + '">' + item2.name +
												'</option>'
										})
									}
								})
							}
						})
						$("#bjselect").html(bjhtml)
					} else {
						//有学院ID 没有专业ID  则班级全部是空  循环专业list
						classId = null //班级ID 都为空 select为空
						$("#bjselect").html('<option value="0">请选择班级</option>')
						//循环班级的list
						let zyhtml = '<option value="0">请选择专业</option>'
						xueyuanlist.forEach((item) => {
							if (item.id == collegeId) { //判断学院的ID和当前选择的学院ID是否相同
								//如果相同 则循环 当前item下的children
								item.children.forEach((item1) => {
									zyhtml += '<option value="' + item1.id + '">' + item1.name + '</option>'
								})
							}
						})
						$("#zyselect").html(zyhtml)
					}
				} else {
					//没有学院ID
					majorId = null //专业ID 都为空 select为空
					$("#zyselect").html('<option value="0">请选择专业</option>')
					classId = null //班级ID 都为空 select为空
					$("#bjselect").html('<option value="0">请选择班级</option>')
				}
			}

			function getnumlist() {
				// 添加学科ID和任务ID作为参数
				$.ajax({
					url: baseurl + "/stat/numCategory",
					type: 'GET',
					contentType: "application/json",
					data:{
						collegeId, majorId, classId, time1, time2, subjectId, taskId: latestTaskId
					},
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							getnumlist2()
							if(res.data.length === 0){
								$("#msgbox").html(`<div id="nodata" class="nodatabbbb">没有数据...</div>`)
								$("#main1").hide()
							}else{
								$("#main1").show()
								$("#msgbox").html(``)
								showmain1(res.data)
							}
						}
					}
				})
			}

			function getnumlist2() {
				// 添加学科ID和任务ID作为参数
				$.ajax({
					url: baseurl + "/stat/numCategorys",
					type: 'GET',
					contentType: "application/json",
					data:{
						collegeId, majorId, classId, time1, time2, subjectId, taskId: latestTaskId
					},
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							showmain2(res.data[0].data, res.data[1].series)
						}
					}
				})
			}
			
			function showmain1(mydata1) {
				// 销毁已存在的实例
				let existingChart = echarts.getInstanceByDom(document.getElementById('main1'));
				if (existingChart) {
					existingChart.dispose();
				}
				
				let myCharttop = echarts.init(document.getElementById('main1'))
				// 指定图表的配置项和数据
				let option = {
					color: ["#A65D57", "#7db2aa", "#f87f9f", "#cb6fc9", "#747bcd", "#74a5cd"],
					title: {
						text: '平台访问量统计',
						left: 'center',
						textStyle: {
							fontSize: 16,
							fontWeight: 'normal'
						}
					},
					tooltip: {
						trigger: 'item',
						formatter: '{a} <br/>{b}: {c} ({d}%)'
					},
					legend: {
						orient: 'vertical',
						left: 'left',
						top: 'center',
						formatter: function(name) {
							// 截断长文本名称
							return name.length > 8 ? name.substring(0, 8) + '...' : name;
						},
						tooltip: {
							show: true,
							formatter: function(params) {
								return params.name;
							}
						}
					},
					series: [{
						name: '访问量',
						type: 'pie',
						radius: ['40%', '70%'], // 环形图
						center: ['60%', '50%'], // 将图表中心点向右移，给左侧图例留出空间
						avoidLabelOverlap: true,
						itemStyle: {
							borderRadius: 4,
							borderWidth: 2,
							borderColor: '#fff'
						},
						label: {
							show: true,
							position: 'outside', // 将标签放在饼图外侧
							formatter: function(params) {
								// 只显示百分比和数据值，不显示过长的名称
								return params.percent + '%\n' + params.value;
							},
							fontSize: 12,
							lineHeight: 16
						},
						labelLine: {
							length: 10, // 第一段线长
							length2: 15, // 第二段线长
							smooth: true, // 平滑的标签引导线
							lineStyle: {
								color: '#999',
								width: 1
							}
						},
						emphasis: {
							label: {
								show: true,
								fontSize: 14,
								fontWeight: 'bold'
							}
						},
						data: mydata1
					}]
				}
				// 使用刚指定的配置项和数据显示图表。
				myCharttop.setOption(option);
				
				// 响应窗口大小变化
				window.addEventListener('resize', function() {
					myCharttop.resize();
				});
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '年' + MM + '月' + d + '日 ' + h + ':' + m
			}
			
			function setdata33(value){
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return h + '时' + m + '分' + s + '秒'
			}

			function showmain2(data1, data2) {
				// 保存原始数据以便过滤
				originalData1 = data1 ? [...data1] : [];
				originalData2 = data2 ? [...data2] : [];
				
				// 根据当前设置过滤并渲染图表
				renderTimeChart();
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
