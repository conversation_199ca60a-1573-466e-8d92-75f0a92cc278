<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
		<title>思政一体化平台-在线学习-红色书籍</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/onlinelearning.css" />
		<link href="css/vendor/bootstrap.min.css" rel="stylesheet">
		<link href="css/vendor/animate.min.css" rel="stylesheet">
		<link rel="stylesheet" href="css/vendor/fontawesome.min.css">
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/vendor/bootstrap.bundle.min.js"></script>
		<style>
			/* 页面加载动画 */
			.loading-overlay {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(255, 255, 255, 0.95);
				display: flex;
				justify-content: center;
				align-items: center;
				z-index: 9999;
				transition: opacity 0.3s;
			}
			
			.loading-spinner {
				width: 50px;
				height: 50px;
				border: 3px solid #f3f3f3;
				border-top: 3px solid #A65D57;
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}
			
			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}

			/* 紧凑型筛选条件区域 - 红色风格 */
			.filter-container {
				background-color: #fff;
				border: 1px solid #eee;
				border-radius: 4px;
				margin: 10px 0;
				padding: 15px;
				box-shadow: 0 2px 4px rgba(0,0,0,0.05);
				display: none; /* 默认隐藏 */
			}
			
			/* 筛选器标题样式 */
			.filter-toggle {
				width: 100%;
				padding: 10px 15px;
				background-color: #d82121;
				color: white;
				border: none;
				border-radius: 4px;
				margin-bottom: 10px;
				text-align: left;
				font-size: 14px;
				font-weight: 500;
				cursor: pointer;
				display: flex;
				justify-content: space-between;
				align-items: center;
				transition: background-color 0.3s;
			}
			
			.filter-toggle:hover {
				background-color: #c01c1c;
			}
			
			.filter-toggle i {
				transition: transform 0.3s;
			}
			
			.filter-toggle.collapsed i {
				transform: rotate(180deg);
			}

			/* 筛选行样式 */
			.filter-row {
				display: flex;
				flex-wrap: wrap;
				gap: 20px;
				margin-bottom: 15px;
			}
			
			.filter-section {
				flex: 1;
				min-width: calc(50% - 10px);
			}
			
			/* 标签样式统一 */
			.tjbox {
				display: flex;
				flex-wrap: wrap;
				gap: 6px;
				margin-top: 5px;
				align-items: center;
				padding: 10px 0;
			}
			
			.tjbox span {
				padding: 6px 14px;
				margin: 0 6px 8px 0;
				border-radius: 8px;
				cursor: pointer;
				background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 50%, #e9ecef 100%);
				color: #495057;
				border: 1px solid #dee2e6;
				box-shadow: 
					0 2px 4px rgba(0, 0, 0, 0.08),
					0 1px 2px rgba(0, 0, 0, 0.06),
					inset 0 1px 0 rgba(255, 255, 255, 0.9),
					inset 0 -1px 0 rgba(0, 0, 0, 0.05);
				transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
				font-size: 12px;
				font-weight: 500;
				position: relative;
				user-select: none;
				min-height: 28px;
				display: inline-flex;
				align-items: center;
				justify-content: center;
				text-shadow: 0 1px 0 rgba(255, 255, 255, 0.8);
			}
			
			/* 按钮内部光泽效果 */
			.tjbox span::before {
				content: '';
				position: absolute;
				top: 1px;
				left: 1px;
				right: 1px;
				height: 45%;
				background: linear-gradient(180deg, rgba(255, 255, 255, 0.4) 0%, rgba(255, 255, 255, 0.1) 100%);
				border-radius: 6px 6px 0 0;
				pointer-events: none;
			}
			
			.tjbox span:hover {
				transform: translateY(-1px);
				background: linear-gradient(145deg, #f8f9fa 0%, #e9ecef 50%, #dee2e6 100%);
				border-color: #adb5bd;
				box-shadow: 
					0 4px 8px rgba(0, 0, 0, 0.12),
					0 2px 4px rgba(0, 0, 0, 0.08),
					inset 0 1px 0 rgba(255, 255, 255, 0.9),
					inset 0 -1px 0 rgba(0, 0, 0, 0.08);
				color: #343a40;
			}
			
			.tjbox span:active {
				transform: translateY(0);
				background: linear-gradient(145deg, #e9ecef 0%, #dee2e6 50%, #ced4da 100%);
				box-shadow: 
					0 1px 2px rgba(0, 0, 0, 0.1),
					inset 0 1px 3px rgba(0, 0, 0, 0.1),
					inset 0 -1px 0 rgba(255, 255, 255, 0.3);
			}
			
			/* 选中状态的拟物化红色按钮 */
			.tjbox span.tjactive {
				background: linear-gradient(145deg, #dc3545 0%, #c82333 50%, #bd2130 100%) !important;
				color: white !important;
				border-color: #bd2130 !important;
				font-weight: 600 !important;
				box-shadow: 
					0 3px 6px rgba(220, 53, 69, 0.3),
					0 1px 3px rgba(220, 53, 69, 0.2),
					inset 0 1px 0 rgba(255, 255, 255, 0.3),
					inset 0 -1px 0 rgba(0, 0, 0, 0.2) !important;
				text-shadow: 0 1px 0 rgba(0, 0, 0, 0.3) !important;
			}
			
			.tjbox span.tjactive::before {
				background: linear-gradient(180deg, rgba(255, 255, 255, 0.3) 0%, rgba(255, 255, 255, 0.1) 100%) !important;
			}
			