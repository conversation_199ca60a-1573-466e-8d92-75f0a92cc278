<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=yes">
    <title>PDF阅读 - 思政一体化平台</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            overflow: hidden;
        }
        
        .pdf-header {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            padding: 15px 20px;
            position: fixed;
            top: 0;
            left: 0;
            right: 0;
            z-index: 1000;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }
        
        .back-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
        }
        
        .pdf-title {
            font-size: 16px;
            font-weight: 500;
            max-width: 200px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }
        
        .download-btn {
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            cursor: pointer;
            text-decoration: none;
        }
        
        .pdf-container {
            position: fixed;
            top: 66px;
            left: 0;
            right: 0;
            bottom: 0;
            background: white;
        }
        
        .pdf-iframe {
            width: 100%;
            height: 100%;
            border: none;
        }
        
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            height: 100%;
            color: #666;
            text-align: center;
            padding: 40px 20px;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-container {
            text-align: center;
            padding: 40px 20px;
            color: #666;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
        }
        
        .error-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .error-message {
            font-size: 16px;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        
        .action-btn {
            display: inline-block;
            background: #c00714;
            color: white;
            padding: 12px 24px;
            border-radius: 6px;
            text-decoration: none;
            font-size: 14px;
            margin: 10px;
            border: none;
            cursor: pointer;
        }
        
        .action-btn.secondary {
            background: #6c757d;
        }
        
        .tips {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin: 20px;
            font-size: 14px;
            color: #856404;
        }
        
        .tips-title {
            font-weight: 600;
            margin-bottom: 8px;
        }
    </style>
</head>
<body>
    <!-- 头部 -->
    <div class="pdf-header">
        <div class="header-left">
            <a href="javascript:history.back()" class="back-btn">
                ←
            </a>
            <div class="pdf-title" id="pdfTitle">PDF文档</div>
        </div>
        <a href="#" class="download-btn" id="downloadBtn">
            📥 下载
        </a>
    </div>
    
    <!-- PDF容器 -->
    <div class="pdf-container" id="pdfContainer">
        <div class="loading-container">
            <div class="loading-spinner"></div>
            <p>正在加载PDF文档...</p>
            <div class="tips">
                <div class="tips-title">💡 提示</div>
                <div>• 支持手势缩放和滑动浏览</div>
                <div>• 如果加载失败，请尝试下载文件</div>
                <div>• 建议在WiFi环境下阅读</div>
            </div>
        </div>
    </div>

    <script>
        // 获取URL参数
        function getUrlParam(name) {
            const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            const r = window.location.search.substr(1).match(reg);
            return r ? decodeURIComponent(r[2]) : null;
        }
        
        // 显示错误信息
        function showError(title, message, pdfUrl) {
            const html = `
                <div class="error-container">
                    <div class="error-icon">📄</div>
                    <div class="error-message">
                        <strong>${title}</strong><br>
                        ${message}
                    </div>
                    <div>
                        <a href="${pdfUrl}" class="action-btn" download>
                            📥 下载PDF文件
                        </a>
                        <button onclick="tryReload()" class="action-btn secondary">
                            🔄 重新加载
                        </button>
                    </div>
                    <div class="tips">
                        <div class="tips-title">📱 移动端阅读建议</div>
                        <div>• 下载文件后使用系统自带的PDF阅读器</div>
                        <div>• 或使用微信、QQ等应用打开PDF文件</div>
                        <div>• 确保网络连接稳定</div>
                    </div>
                </div>
            `;
            document.getElementById('pdfContainer').innerHTML = html;
        }
        
        // 重新加载
        function tryReload() {
            location.reload();
        }
        
        // 加载PDF
        function loadPDF() {
            const pdfUrl = getUrlParam('url');
            const title = getUrlParam('title') || 'PDF文档';
            const bookId = getUrlParam('id');
            
            // 更新标题
            document.getElementById('pdfTitle').textContent = title;
            document.title = title + ' - 思政一体化平台';
            
            if (pdfUrl) {
                // 直接使用PDF URL
                setupPDFViewer(pdfUrl, title);
            } else if (bookId) {
                // 从书籍信息中获取PDF URL
                loadBookPDF(bookId);
            } else {
                showError('参数错误', '缺少必要的PDF文件信息', '');
            }
        }
        
        // 设置PDF查看器
        function setupPDFViewer(pdfUrl, title) {
            // 更新下载按钮
            const downloadBtn = document.getElementById('downloadBtn');
            downloadBtn.href = pdfUrl;
            downloadBtn.download = title + '.pdf';
            
            // 尝试不同的PDF显示方式
            tryEmbedPDF(pdfUrl) || tryIframePDF(pdfUrl) || tryGoogleViewer(pdfUrl) || showDownloadOnly(pdfUrl, title);
        }
        
        // 方式1: 使用embed标签
        function tryEmbedPDF(pdfUrl) {
            try {
                const embed = document.createElement('embed');
                embed.src = pdfUrl;
                embed.type = 'application/pdf';
                embed.style.width = '100%';
                embed.style.height = '100%';
                
                embed.onload = function() {
                    document.getElementById('pdfContainer').innerHTML = '';
                    document.getElementById('pdfContainer').appendChild(embed);
                };
                
                embed.onerror = function() {
                    return false;
                };
                
                return true;
            } catch (e) {
                return false;
            }
        }
        
        // 方式2: 使用iframe
        function tryIframePDF(pdfUrl) {
            try {
                const iframe = document.createElement('iframe');
                iframe.src = pdfUrl;
                iframe.className = 'pdf-iframe';
                
                iframe.onload = function() {
                    document.getElementById('pdfContainer').innerHTML = '';
                    document.getElementById('pdfContainer').appendChild(iframe);
                };
                
                iframe.onerror = function() {
                    return false;
                };
                
                // 延迟检查是否加载成功
                setTimeout(() => {
                    try {
                        if (iframe.contentDocument || iframe.contentWindow) {
                            // 加载成功
                        }
                    } catch (e) {
                        // 跨域或加载失败，尝试下一种方式
                        tryGoogleViewer(pdfUrl);
                    }
                }, 3000);
                
                return true;
            } catch (e) {
                return false;
            }
        }
        
        // 方式3: 使用Google Docs Viewer
        function tryGoogleViewer(pdfUrl) {
            try {
                const iframe = document.createElement('iframe');
                iframe.src = `https://docs.google.com/viewer?url=${encodeURIComponent(pdfUrl)}&embedded=true`;
                iframe.className = 'pdf-iframe';
                
                iframe.onload = function() {
                    document.getElementById('pdfContainer').innerHTML = '';
                    document.getElementById('pdfContainer').appendChild(iframe);
                };
                
                iframe.onerror = function() {
                    showDownloadOnly(pdfUrl, document.getElementById('pdfTitle').textContent);
                };
                
                return true;
            } catch (e) {
                return false;
            }
        }
        
        // 方式4: 仅提供下载
        function showDownloadOnly(pdfUrl, title) {
            showError(
                'PDF在线预览不可用',
                '您的浏览器不支持PDF在线预览，请下载文件后使用其他应用打开。',
                pdfUrl
            );
        }
        
        // 从书籍信息加载PDF
        function loadBookPDF(bookId) {
            // 这里需要根据实际API调整
            fetch(`/api/web/posts/${bookId}`, {
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                }
            })
            .then(response => response.json())
            .then(data => {
                if (data.code == '200' && data.data) {
                    const book = data.data;
                    let pdfPath = null;
                    
                    if (book.attachmentPath && book.attachmentPath.length > 0) {
                        pdfPath = book.attachmentPath.find(path => path.toLowerCase().endsWith('.pdf'));
                    }
                    
                    if (pdfPath) {
                        const pdfUrl = window.baseurl + pdfPath;
                        setupPDFViewer(pdfUrl, book.title || 'PDF文档');
                    } else {
                        showError('未找到PDF文件', '该书籍可能不是PDF格式', '');
                    }
                } else {
                    showError('获取书籍信息失败', data.message || '未知错误', '');
                }
            })
            .catch(error => {
                console.error('加载书籍信息失败:', error);
                showError('网络错误', '请检查网络连接后重试', '');
            });
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadPDF();
        });
    </script>
</body>
</html>
