// 模拟登录信息存储
function mockLogin(userType) {
    // 可选用户类型
    const userTypes = {
        // 老师用户
        teacher: {
            token: 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyMTIxMDE4IiwiY3JlYXRlZCI6MTc0NDc2NzkyMzEwNiwiZXhwIjoxNzYwMzE5OTIzfQ.hJd3I-_acZAiSjcHLP7jbRlcDIZYHraB37bGVsFrWW72zvzkJ1fHwqoOG7hidvKs9w6qiFwoc_BjkE1tNy9Yvg',
            userInfo: {
        "id": "1299387870829744128",
        "name": "吴永刚",
        "identifier": null,
        "gender": null,
        "avatarPath": null,
        "loginTime": "2025-04-16T01:45:23.000+00:00",
        "lockTime": null,
        "userAuth": {
            "id": "1299390514008821760",
            "creator": "admin",
            "createdAt": "2024-10-25T07:14:00.000+00:00",
            "modifier": "admin",
            "updatedAt": "2024-10-25T07:14:00.000+00:00",
            "delFlag": 0,
            "tenantCode": "1",
            "ext": null,
            "userId": "1299387870829744128",
            "identityType": 2,
            "identifier": "2121018",
            "wechatOpenId": null,
            "siteId": 0,
            "newRecord": false
        },
        "college": null,
        "major": null,
        "className": null,
        "classNames": null,
        "approvalStatus": 1,
        "roleName": "老师",
        "passWord": null,
        "collegeName": null,
        "majorName": null,
        "className2": null,
        "questionId": null,
        "questionName": null,
        "answer": null,
        "researchSection": "马克思主义学院",
        "myCollegeName": "马克思主义学院",
        "userId": null,
        "createdAt": "2024-10-25 15:03:30",
        "status": null,
        "time1": null,
        "time2": null,
        "ids": null,
        "paperId": null,
        "paperName": null,
        "weChat": null,
        "wechatName": null,
        "wechatOpenId": null,
        "sessionKey": null,
        "email": null,
        "phone": "18993689001",
        "liveUserId": null,
        "collegeId": null,
        "majorId": null,
        "classId": null,
        "collegeIds": null,
        "studentCollegeList": null,
        "collegeList": null,
        "roleMojorList": [
            {
                "id": "1361742409058358358",
                "creator": "super-admin",
                "createdAt": "2025-04-15T08:38:10.000+00:00",
                "modifier": "super-admin",
                "updatedAt": "2025-04-15T08:38:10.000+00:00",
                "delFlag": 0,
                "tenantCode": null,
                "ext": null,
                "roleId": "2",
                "roleName": "老师",
                "usersId": "1299387870829744128",
                "collegeId": "1288894906898714628",
                "majorId": "1288894994127654913",
                "classId": "1288895255575400448",
                "classIds": null,
                "subjectId": null,
                "newRecord": false
            }
        ],
        "collegeMojorList": null,
        "code": null,
        "roleList": [],
        "roleId": null
            }
        },
        // 学生用户
        student: {
            token: 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiI1MjIwMTA4MDE1MzIiLCJjcmVhdGVkIjoxNzQ2NTEzMDkwMDkwLCJleHAiOjE3NjIwNjUwOTB9.KKBVPXxZFbR5JlUaoqiG_zHQ5Cya89BT9rpQRKj3OxJydEhOJIlHDUpaDsuxz0bT_uGYIWoQai8pu9grduTE7w',
            userInfo: {
                "id": "1388727398726543212",
                "name": "测试学生",
                "identifier": null,
                "gender": null,
                "avatarPath": null,
                "loginTime": "2025-05-06T08:45:23.000+00:00",
                "lockTime": null,
                "userAuth": {
                    "id": "1388727398726543213",
                    "creator": "admin",
                    "createdAt": "2025-05-06T08:45:23.000+00:00",
                    "modifier": "admin",
                    "updatedAt": "2025-05-06T08:45:23.000+00:00",
                    "delFlag": 0,
                    "tenantCode": "1",
                    "ext": null,
                    "userId": "1388727398726543212",
                    "identityType": 1,
                    "identifier": "5220108015",
                    "wechatOpenId": null,
                    "siteId": 0,
                    "newRecord": false
                },
                "college": null,
                "major": null,
                "className": null,
                "classNames": null,
                "approvalStatus": 1,
                "roleName": "学生",
                "passWord": null,
                "collegeName": "中医学院",
                "majorName": "中医学",
                "className2": "中医2022-1班",
                "questionId": null,
                "questionName": null,
                "answer": null,
                "researchSection": null,
                "myCollegeName": "中医学院",
                "userId": null,
                "createdAt": "2025-05-06 08:45:23",
                "status": null,
                "time1": null,
                "time2": null,
                "ids": null,
                "paperId": null,
                "paperName": null,
                "weChat": null,
                "wechatName": null,
                "wechatOpenId": null,
                "sessionKey": null,
                "email": null,
                "phone": "15991234567",
                "liveUserId": null,
                "collegeId": "1288894906898714626",
                "majorId": "1288894994127654912",
                "classId": "1288895255575400446",
                "collegeIds": null,
                "studentCollegeList": null,
                "collegeList": null,
                "roleMojorList": [
                    {
                        "id": "1361742409058358359",
                        "creator": "super-admin",
                        "createdAt": "2025-05-06T08:45:23.000+00:00",
                        "modifier": "super-admin",
                        "updatedAt": "2025-05-06T08:45:23.000+00:00",
                        "delFlag": 0,
                        "tenantCode": null,
                        "ext": null,
                        "roleId": "1",
                        "roleName": "学生",
                        "usersId": "1388727398726543212",
                        "collegeId": "1288894906898714626",
                        "majorId": "1288894994127654912",
                        "classId": "1288895255575400446",
                        "classIds": null,
                        "subjectId": null,
                        "newRecord": false
                    }
                ],
                "collegeMojorList": null,
                "code": null,
                "roleList": [],
                "roleId": null
            }
        }
    };
    
    // 默认使用教师账号
    const selectedUser = userTypes[userType] || userTypes.teacher;
    
    // 存储认证token
    sessionStorage.setItem('header', selectedUser.token);
    
    // 存储用户信息
    sessionStorage.setItem('userinfo', JSON.stringify(selectedUser.userInfo));

    console.log(`模拟登录成功！用户角色：${selectedUser.userInfo.roleName}，用户名：${selectedUser.userInfo.name}`);
    console.log('Token和用户信息已存储到sessionStorage中');
    
    return selectedUser.userInfo;
}

// 强制切换到学生账号
function switchToStudent() {
    const studentInfo = mockLogin('student');
    alert(`已切换到学生账号：${studentInfo.name}，请稍候...`);
    // 强制刷新页面
    setTimeout(() => {
        window.location.reload();
    }, 500);
}

// 强制切换到教师账号
function switchToTeacher() {
    const teacherInfo = mockLogin('teacher');
    alert(`已切换到教师账号：${teacherInfo.name}，请稍候...`);
    // 强制刷新页面
    setTimeout(() => {
        window.location.reload();
    }, 500);
}

// 创建选择界面并执行模拟登录
function createLoginSelector() {
    // 检查是否已存在选择器
    if (document.getElementById('login-selector')) {
        return;
    }
    
    // 创建选择器容器
    const selectorContainer = document.createElement('div');
    selectorContainer.id = 'login-selector';
    selectorContainer.style.cssText = `
        position: fixed;
        top: 70px;
        right: 10px;
        background-color: #f8f9fa;
        border: 2px solid #A65D57;
        border-radius: 5px;
        padding: 10px;
        z-index: 9999;
        box-shadow: 0 4px 10px rgba(0,0,0,0.3);
        font-family: Arial, sans-serif;
        min-width: 180px;
    `;
    
    // 标题
    const title = document.createElement('h3');
    title.textContent = '账号切换器';
    title.style.margin = '0 0 10px 0';
    title.style.fontSize = '16px';
    title.style.fontWeight = 'bold';
    title.style.color = '#A65D57';
    title.style.textAlign = 'center';
    title.style.borderBottom = '1px solid #ddd';
    title.style.paddingBottom = '5px';
    selectorContainer.appendChild(title);
    
    // 当前状态显示
    const currentUser = JSON.parse(sessionStorage.getItem('userinfo') || '{}');
    const statusDiv = document.createElement('div');
    statusDiv.style.cssText = `
        margin-bottom: 10px;
        padding: 5px;
        background-color: #e9ecef;
        border-radius: 3px;
        font-size: 12px;
        text-align: center;
    `;
    statusDiv.innerHTML = `当前：<strong>${currentUser.name || '未登录'}</strong> (${currentUser.roleName || '无角色'})`;
    selectorContainer.appendChild(statusDiv);
    
    // 创建按钮
    const createButton = (text, actionFn, primary = false) => {
        const button = document.createElement('button');
        button.textContent = text;
        button.style.cssText = `
            display: block;
            width: 100%;
            margin: 5px 0;
            padding: 8px 10px;
            border: none;
            border-radius: 3px;
            background-color: ${primary ? '#A65D57' : '#6c757d'};
            color: white;
            cursor: pointer;
            font-size: 14px;
            font-weight: bold;
            text-align: center;
            transition: background-color 0.2s;
        `;
        button.onmouseover = () => {
            button.style.backgroundColor = primary ? '#8A4D47' : '#5a6268';
        };
        button.onmouseout = () => {
            button.style.backgroundColor = primary ? '#A65D57' : '#6c757d';
        };
        button.addEventListener('click', actionFn);
        
        return button;
    };
    
    // 添加按钮
    const studentBtn = createButton('切换到学生账号', switchToStudent, true);
    const teacherBtn = createButton('切换到教师账号', switchToTeacher, false);
    
    // 当前角色是学生时，禁用学生按钮
    if (currentUser.roleName === '学生') {
        studentBtn.disabled = true;
        studentBtn.style.opacity = '0.6';
        studentBtn.style.cursor = 'not-allowed';
        studentBtn.title = '已经是学生账号';
    }
    
    // 当前角色是老师时，禁用老师按钮
    if (currentUser.roleName === '老师') {
        teacherBtn.disabled = true;
        teacherBtn.style.opacity = '0.6';
        teacherBtn.style.cursor = 'not-allowed';
        teacherBtn.title = '已经是教师账号';
    }
    
    selectorContainer.appendChild(studentBtn);
    selectorContainer.appendChild(teacherBtn);
    
    // 添加到页面
    document.body.appendChild(selectorContainer);
}

// 页面加载完成后执行
window.addEventListener('DOMContentLoaded', () => {
    // 检查是否已有登录信息
    const userInfo = sessionStorage.getItem('userinfo');
    if (!userInfo) {
        // 如果没有登录信息，默认使用教师账号登录
    mockLogin('teacher');
    }
    
    // 创建选择器
    setTimeout(createLoginSelector, 1000); // 延迟1秒添加选择器，确保页面其他元素已加载
}); 

// 暴露全局函数，可以在控制台直接调用
window.switchToStudent = switchToStudent;
window.switchToTeacher = switchToTeacher; 