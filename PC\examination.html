<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-思政考核</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/examination.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="context">
			<div class="contexttopview">
				<div class="ctitle">在线考核</div>
				<div class="inputview">
					<input id="ssinput" placeholder="请输入试卷名称" oninput="ssinputs()" type="text" />
					<span onclick="sssubmit()">搜索</span>
				</div>
			</div>
			<div class="titlesbox">
				<span class="titlesl">学科:</span>
				<div id="xklist">
					
					
				</div>
			</div>
			<div class="topbarview">
				<span>试卷列表</span>
			</div>
			<div class="zxkhbox" id="paperlist">
				
				
			</div>
			<div class="fybox" id="fyq">
				<span id="sy">首页</span>
				<span id="syy">上一页</span>
				<div class="num" id="num">
				</div>
				<span id="xyy">下一页</span>
				<span id="wy">尾页</span>
			</div>
		</div>
		
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/clipboard.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let pageindex = 1
			let pagesize = 10
			let pages = 1
			
			let zjid = null
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				let userinfo = sessionStorage.getItem("userinfo")
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
					if(window.localStorage.getItem("jilu")){
						$.ajax({
							url: baseurl + "/study/record/add",
							type: 'post',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: window.localStorage.getItem("jilu"),
							dataType: 'json',
							success: (res) => {
								window.localStorage.clear()
							}
						})
					}
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getlistpaper()//获取试卷列表
				getfooterlink()
				getxuekelist()//获取学科列表
			})
			function getxuekelist(){
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = '<span class="titlesr" onclick="selectxk(this)" data-id="0">全部</span>'
							res.data.map((item)=>{
								html += '<span class="titlesr"  onclick="selectxk(this)" data-id="'+item.id+'">'+item.name+'</span>'
							})
							$("#xklist").html(html)
						}
					}
				})
			}
			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是第最后一页了！")
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！")
					}
				} else {
					pageindex = index
					getlistpaper()
				}
			}
			function selectxk(item){
				zjid = $(item).attr("data-id")
				var alltab = $("#xklist span")
				// console.log(alltab)
				for(let i = 0;i<alltab.length;i++){
					// console.log($(alltab[i]).attr("data-id"),zjid)
					if($(alltab[i]).attr("data-id") == zjid){
						$(alltab[i]).attr("class","titlesr tiactive")
					}else{
						$(alltab[i]).attr("class","titlesr")
					}
				}
				if(zjid == '0'){
					zjid = null
				}
				getlistpaper()
			}
			let name = null
			function sssubmit(){
				name = $("#ssinput").val()
				pageindex = 1
				getlistpaper()
			}
			function ssinputs(){
				name = $("#ssinput").val()
				pageindex = 1
				getlistpaper()
			}
			function getlistpaper(){
				let datas = new Date()
				$.ajax({
					url: baseurl + "/paper/listall",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: pageindex,
						pageSize: pagesize,
						projectId: zjid,
						name: name,
						currTime: setDate2(datas)
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							pages = res.data.pages
							// console.log(res.data.list)
							let html = ""
							res.data.list.map((item)=>{
								html+='<div class="item"><div class="leftitem"><img src="img/sl.png" />'+
									'<span>'+setDate(item.startTime)+'-'+setDate(item.endTime)+'</span>'+
								'</div><div class="centeritem">'+item.name+'</div>'+
								'<div class="rightitem"><div class="ksnum"><img src="img/ks.png" /><span>'+item.views+'</span></div>'+
									'<div class="ksdtbtn"><a href="examination2.html?id='+item.id+'">开始答卷</a></div></div></div>'
							})
							$("#paperlist").html(html)
							pages = res.data.pages
							if (pages > 1) {
								let numhtml = ""
								//当页数大于5
								if (pages > 5) {
									if (pageindex <= 5) { //当前页码小于5的时候右侧不变
										for (let a = 1; a <= 10; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
										numhtml += '<label onclick="getnewlist(11)">...</label>'
									} else if (5 < pageindex & pageindex < pages - 5) {
										numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) - 5) +
											')">...</label>'
										for (let a = pageindex - 4; a <= pageindex + 4; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
										numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) + 5) +
											')">...</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + (pages - 11) +
											')">...</label>'
										for (let a = pages - 10; a <= pages; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
									}
								} else {
									for (let a = 1; a <= pages; a++) {
										if (pageindex == a) {
											numhtml += '<label class="actinum" onclick="getnewlist(' + a +
												')">' + a +
												'</label>'
										} else {
											numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
										}
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
						}
					}
				})
			}
			
			let clipboard = new ClipboardJS('.copybtn');
			clipboard.on('success', function(e) {
				e.clearSelection();
				cocoMessage.success(1000, "复制成功！")
			});
			
			clipboard.on('error', function(e) {
				cocoMessage.error(1000, "复制失败！")
			});
			
			function getfooterlink(){
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item)=>{
								html+='<a href="'+item.url+'" target="_blank">'+item.name+'</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
			
			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					if (res.data[i].id == getUrlParam('id')||res.data[i].redirectUrl=='examination.html') {
			// 						classdate = res.data[i]
			// 						html += '<a class="menuitem active" href="' + res.data[i].redirectUrl + '?id=' + res
			// 							.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					} else {
			// 						html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res.data[
			// 							i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					}
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }
			
			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}
			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d
			}
			function setDate2(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '/' + MM + '/' + d
			}
		</script>
		<script>
			
			$("#backtop").hide()
			$(function(){
				$(window).scroll(function(){
					if($(window).scrollTop()>600){
						$("#backtop").show()
					}else{
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click',function(){
				$("body,html").animate({
					scrollTop: 0
				},300)
			})
		</script>
	</body>
</html>
