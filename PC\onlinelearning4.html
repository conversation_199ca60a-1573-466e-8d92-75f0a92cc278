<!DOCTYPE html>
<html>
		<head>				<meta charset="utf-8" />		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">		<title>思政一体化平台-在线学习-详情</title>		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />		<link rel="stylesheet" type="text/css" href="css/style.css" />		<link rel="stylesheet" type="text/css" href="css/onlinelearning.css" />
		<!-- Tailwind CSS -->
		<link rel="stylesheet" type="text/css" href="css/vendor/tailwind.min.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>		<!-- 添加多文件格式支持 -->		<script src="./js/viewers/howler.min.js" type="text/javascript" charset="utf-8"></script>		<script src="js/vendor/jszip.min.js" type="text/javascript" charset="utf-8"></script>		<script src="./js/viewers/docx-preview.min.js" type="text/javascript" charset="utf-8"></script>		<script src="./js/viewers/file-viewer.js" type="text/javascript" charset="utf-8"></script>
        <!-- 添加账号切换功能 -->
        <script src="./js/login-mock.js" type="text/javascript" charset="utf-8"></script>
			<style>
		/* 调整后的主布局容器 */
		.container-wrapper {
			width: 100%;
			max-width: 1200px;
			margin: 0 auto;
			padding: 0 1rem;
		}
		
		/* 现代音频播放器样式 */
		.modern-player-container {
			display: flex;
			justify-content: center;
			align-items: center;
			padding: 30px 0;
			background: #fff;
		}
		
		.modern-audio-player {
			width: 90%;
			max-width: 500px;
			padding: 30px;
			border-radius: 16px;
			background: #fff;
			box-shadow: 0 10px 30px rgba(0,0,0,0.08);
			display: flex;
			flex-direction: column;
			align-items: center;
			position: relative;
			transition: all 0.3s ease;
		}
			
			.album-art {
				width: 200px;
				height: 200px;
				border-radius: 50%;
				background: linear-gradient(145deg, #f0f0f0, #e6e6e6);
				margin-bottom: 25px;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
				overflow: hidden;
				transition: all 0.3s ease;
				/* 添加边框和内部阴影效果 */
				border: 4px solid #fff;
				box-shadow: 0 8px 20px rgba(0,0,0,0.15);
				/* 确保背景图片正确显示 */
				background-position: center !important;
				background-size: cover !important;
				background-repeat: no-repeat !important;
			}
			
			.album-art.rotating {
				animation: rotate 20s linear infinite;
			}
			
			@keyframes rotate {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}
			
			/* 为封面图片添加悬停效果 */
			.album-art:hover {
				transform: scale(1.05);
				box-shadow: 0 10px 25px rgba(0,0,0,0.2), inset 0 0 0 4px rgba(255,255,255,0.7);
			}
			
			.audio-icon {
				width: 70px;
				height: 70px;
				background: #A65D57;
				mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z'%3E%3C/path%3E%3C/svg%3E") no-repeat center;
				-webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z'%3E%3C/path%3E%3C/svg%3E") no-repeat center;
				mask-size: contain;
				-webkit-mask-size: contain;
				opacity: 0.9;
			}
			
			.audio-info {
				text-align: center;
				margin-bottom: 20px;
				width: 100%;
			}
			
			.audio-title {
				font-size: 1.5rem;
				font-weight: 600;
				color: #333;
				margin-bottom: 8px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
			
			.audio-author {
				font-size: 1rem;
				color: #777;
				margin-bottom: 15px;
			}
			
			.audio-progress-container {
				width: 100%;
				display: flex;
				align-items: center;
				margin-bottom: 25px;
			}
			
			.audio-time {
				font-size: 0.85rem;
				color: #888;
				font-variant-numeric: tabular-nums;
			}
			
			.progress-bar-container {
				flex-grow: 1;
				height: 6px;
				background: #f0f0f0;
				border-radius: 3px;
				margin: 0 10px;
				position: relative;
				cursor: pointer;
				overflow: hidden;
			}
			
			.progress-bar {
				position: absolute;
				top: 0;
				left: 0;
				height: 100%;
				background: #A65D57;
				border-radius: 3px;
				width: 0%;
				transition: width 0.1s linear;
			}
			
			.audio-controls {
				display: flex;
				align-items: center;
				justify-content: center;
			}
			
			.audio-controls button {
				background: transparent;
				border: none;
				cursor: pointer;
				outline: none;
				margin: 0 15px;
				padding: 0;
				transition: all 0.2s ease;
			}
			
			.audio-controls button:hover {
				transform: scale(1.1);
			}
			
			.btn-prev svg, .btn-next svg {
				width: 30px;
				height: 30px;
				fill: #A65D57;
			}
			
			.btn-play {
				width: 60px;
				height: 60px;
				border-radius: 50%;
				background: #A65D57;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
				box-shadow: 0 4px 10px rgba(166, 93, 87, 0.3);
				border: 2px solid #fff;
			}
			
			.btn-play svg {
				width: 30px;
				height: 30px;
				fill: white;
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				transition: opacity 0.2s ease;
				/* 增加描边使其在白色背景上更容易辨识 */
				stroke: #333;
				stroke-width: 0.3px;
			}
			
			.btn-play .pause-icon {
				opacity: 0;
			}
			
			.btn-play.playing .play-icon {
				opacity: 0;
			}
			
			.btn-play.playing .pause-icon {
				opacity: 1;
			}
			
			/* 响应式设计 */
			@media (max-width: 768px) {
				.modern-audio-player {
					padding: 20px;
				}
				
				.album-art {
					width: 150px;
					height: 150px;
					margin-bottom: 20px;
				}
				
				.audio-title {
					font-size: 1.2rem;
				}
				
				.audio-author {
					font-size: 0.9rem;
				}
				
				.btn-play {
					width: 50px;
					height: 50px;
				}
				
				.btn-play svg, .btn-prev svg, .btn-next svg {
					width: 24px;
					height: 24px;
				}
			}
			
			@media (max-width: 480px) {
				.modern-audio-player {
					padding: 15px;
				}
				
				.album-art {
					width: 120px;
					height: 120px;
					margin-bottom: 15px;
				}
				
				.audio-title {
					font-size: 1.1rem;
				}
				
				.audio-author {
					font-size: 0.8rem;
				}
				
				.audio-controls button {
					margin: 0 10px;
				}
			}

			.zsdlabel {
				background: #f7f7f7;
				border-radius: 8px;
				padding: 6px 12px;
				font-size: 13px;
				color: #333333;
				display: inline-flex;
				align-items: center;
				margin-bottom: 8px;
				margin-right: 8px;
				transition: all 0.3s ease;
				border: 1px solid #eee;
				box-shadow: 0 1px 2px rgba(0,0,0,0.02);
			}
			
			.zsdlabel:hover {
				background: rgba(166, 93, 87, 0.06);
				transform: translateY(-1px);
				box-shadow: 0 2px 5px rgba(166, 93, 87, 0.08);
				border-color: rgba(166, 93, 87, 0.2);
			}

			.zsdlabel img {
				width: 14px;
				height: 14px;
				margin-right: 6px;
				opacity: 0.85;
				transition: all 0.2s ease;
			}
			
			.zsdlabel:hover img {
				opacity: 1;
				transform: scale(1.05);
			}
			
			/* 按钮交互效果 */
			#backButton:hover {
				background-color: #eaeaea !important;
				box-shadow: 0 2px 5px rgba(0,0,0,0.1);
			}
			
			.kjpfbtn:hover {
				background-color: #8a4d47 !important;
				box-shadow: 0 2px 5px rgba(0,0,0,0.2);
			}
			
			/* 统一文件查看器样式 */
			.doc-toolbar {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 10px 15px;
				background: #f5f5f5;
				border-radius: 8px 8px 0 0;
				box-shadow: 0 1px 3px rgba(0,0,0,0.1);
				font-weight: 500;
				color: #333;
				width: 90%;
				max-width: 1000px;
				margin: 0 auto;
			}
			
			.doc-container {
				width: 90%;
				max-width: 1000px;
				height: 600px;
				border: 1px solid #ddd;
				border-top: none;
				border-radius: 0 0 8px 8px;
				overflow: auto;
				background: white;
				position: relative;
				margin: 0 auto;
			}
			
			.docx-viewer {
				padding: 20px;
				font-family: 'Times New Roman', serif;
				line-height: 1.5;
			}
			
			.docx-viewer .page {
				box-shadow: 0 2px 5px rgba(0,0,0,0.1);
				margin-bottom: 20px;
				background: white;
				padding: 20px;
				border: 1px solid #eee;
				max-width: 90%;
				margin-left: auto;
				margin-right: auto;
				word-break: break-word;
				overflow-wrap: break-word;
			}
			
			.audio-player {
				width: 100%;
				height: 200px;
				display: flex;
				flex-direction: column;
				justify-content: center;
				background: #f9f9f9;
				border: 1px solid #ddd;
				border-top: none;
				border-radius: 0 0 8px 8px;
				padding: 20px;
			}
			
			.audio-player .controls {
				display: flex;
				align-items: center;
				margin-top: 20px;
			}
			
			.audio-player .play-btn {
				background: #A65D57;
				color: white;
				border: none;
				border-radius: 50%;
				width: 50px;
				height: 50px;
				font-size: 20px;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				transition: background-color 0.3s;
			}
			
			.audio-player .progress-container {
				flex-grow: 1;
				margin: 0 20px;
				background: #ddd;
				border-radius: 5px;
				height: 8px;
				position: relative;
				cursor: pointer;
			}
			
			.audio-player .progress-bar {
				background: #A65D57;
				border-radius: 5px;
				height: 100%;
				width: 0;
			}
			
			.loading-indicator {
				display: flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(255, 255, 255, 0.7);
				border-radius: 50%;
				flex-direction: column;
				color: #666;
				font-size: 14px;
				text-align: center;
				z-index: 5;
				backdrop-filter: blur(2px);
			}
			
			.loading-indicator:before {
				content: "";
				width: 40px;
				height: 40px;
				margin-bottom: 10px;
				border: 4px solid #f3f3f3;
				border-top: 4px solid #A65D57;
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}
			
			.loading-indicator.error {
				background-color: rgba(255, 235, 235, 0.8);
				color: #a65d57;
			}
			
			.loading-indicator.error:before {
				border: 4px solid #ffcccc;
				border-top: 4px solid #a65d57;
				animation: none;
				transform: rotate(45deg);
			}
			
			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}
			
			.error-message {
				padding: 20px;
				color: #721c24;
				background-color: #f8d7da;
				border: 1px solid #f5c6cb;
				border-radius: 8px;
				margin: 10px 0;
				text-align: center;
			}
			
			/* 移动端适配样式 */
			@media (max-width: 768px) {
				/* 顶部导航 */
				.topview_1 {
					padding: 10px;
					flex-direction: column;
					align-items: center;
				}
				
				/* 返回按钮移动端样式 */
				#backButton {
					width: 100%;
					margin-bottom: 10px;
				}
				
				/* 评分按钮移动端样式 */
				.flex.items-center {
					flex-direction: column;
					align-items: flex-start;
				}
				
				.flex.items-center button {
					margin-left: 0;
					margin-top: 0.5rem;
				}
				
				.logo {
					margin-bottom: 10px;
				}
				
				.loginview {
					width: 100%;
					justify-content: space-between;
				}
				
				.ssview {
					width: 60%;
				}
				
				/* 导航菜单 */
				.menu-toggle {
					display: block;
					position: absolute;
					top: 15px;
					right: 15px;
					background: none;
					border: none;
					font-size: 24px;
					color: #A65D57;
					cursor: pointer;
					z-index: 1000;
				}
				
				.topview_2 .itembox {
					flex-direction: column;
					max-height: 0;
					overflow: hidden;
					transition: max-height 0.3s ease;
				}
				
				.topview_2 .itembox.expanded {
					max-height: 300px;
				}
				
				.topview_2 .menuitem {
					width: 100%;
					text-align: left;
					padding: 10px 15px;
					border-bottom: 1px solid rgba(0,0,0,0.05);
				}
				
				/* 内容区域 */
				.content {
					padding: 10px;
				}
				
				.contenttitlebox {
					overflow-x: auto;
					white-space: nowrap;
					padding: 10px 0;
				}
				
				/* 课件页面布局 */
				.kjtop {
					flex-direction: column;
				}
				
				.jttopitem {
					width: 100%;
					margin-bottom: 10px;
				}
				
				.kjname {
					font-size: 16px;
				}
				
				.kjzz {
					flex-wrap: wrap;
				}
				
				.kjzz label {
					margin-bottom: 5px;
				}
				
				/* 课件窗口 */
				.kjbox {
					flex-direction: column;
				}
				
				.kjview {
					width: 100%;
					margin-bottom: 15px;
				}
				
				.kjdg {
					width: 100%;
					max-height: none;
				}
				
				/* 课程详情 */
				.kjjj {
					margin-top: 15px;
				}
				
				.topkjjjs {
					overflow-x: auto;
					white-space: nowrap;
					padding-bottom: 10px;
				}
				
				.topkjjjs div {
					padding: 8px 15px;
					font-size: 14px;
				}
				
				/* 评价区域 */
				.pinjiabox {
					padding: 0 10px;
				}
				
				.pjbbb {
					width: 90%;
					max-width: 90%;
				}
				
				.xxbox {
					padding: 10px;
				}
				
				/* 全屏预览 */
				#qp {
					padding: 10px;
				}
				
				#qp img {
					max-width: 100%;
					max-height: 80vh;
				}
				
				/* 知识点标签 */
				.zsdlabel {
					margin: 3px;
					font-size: 12px;
				}
				
				/* 底部 */
				footer .yqlj .box {
					flex-direction: column;
				}
				
				footer .yqlj .box a {
					margin: 5px 0;
				}
				
				/* 返回顶部按钮 */
				#backtop {
					position: fixed;
					bottom: 20px;
					right: 20px;
					z-index: 999;
					width: 40px;
					height: 40px;
					background: rgba(166, 93, 87, 0.8);
					border-radius: 50%;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					box-shadow: 0 2px 10px rgba(0,0,0,0.2);
				}
				
				#backtop div {
					font-size: 10px;
					color: white;
				}
				
				#backtop img {
					width: 15px;
					height: 15px;
				}
				
				/* 文档容器的移动端适配 */
				.doc-container {
					height: 500px;
					width: 95% !important;
					margin: 0 auto !important;
					border-radius: 0 0 4px 4px;
				}
				
				/* 文档工具栏移动端适配 */
				.doc-toolbar {
					width: 95% !important;
					padding: 8px 10px;
					font-size: 0.9rem;
					margin: 0 auto;
				}
				
				.audio-player {
					height: 180px;
					padding: 15px;
				}
				
				.audio-player .play-btn {
					width: 40px;
					height: 40px;
					font-size: 16px;
				}
				
				.doc-nav {
					bottom: 70px !important;
					right: 15px !important;
					font-size: 12px !important;
				}
				
				/* 评分按钮移动端样式 */
				.kjpfbtn {
					font-size: 0.8rem !important;
					padding: 0.4rem 0.6rem !important;
				}
				
				/* 移动端按钮容器样式 */
				.jttopitem:last-child {
					justify-content: space-between !important;
					margin-top: 10px;
				}
				
				/* PDF阅读器的移动端适配 */
				.pdfbox {
					width: 100% !important; 
					height: 70vh !important;
					padding: 0 !important;
					margin: 0 auto;
				}
				
				.panel-body.pdfbox object,
				.panel-body.pdfbox iframe,
				.panel-body.pdfbox embed {
					width: 100% !important;
					max-width: 100% !important;
					height: 100% !important;
					margin: 0 auto;
				}
				
				/* DOCX阅读器的移动端适配 */
				.docx-viewer {
					padding: 10px;
				}
				
				.docx-viewer .page {
					padding: 12px;
					margin: 0 auto 15px auto;
					max-width: 98% !important;
					box-sizing: border-box;
					transform-origin: top center;
					overflow-x: auto;
				}
				
				/* 文档内容自适应处理 */
				.docx-viewer table {
					max-width: 100%;
					display: block;
					overflow-x: auto;
					white-space: normal;
					font-size: 0.9em;
				}
				
				/* 确保文档内容自适应移动端屏幕 */
				.docx-viewer p, 
				.docx-viewer span,
				.docx-viewer div {
					max-width: 100%;
					word-break: break-word;
					overflow-wrap: break-word;
				}
				
				.docx-viewer img {
					max-width: 100%;
					height: auto !important;
				}
				
				/* 优化PDF和DOCX渲染 */
				.doc-container {
					transform-origin: top center;
				}
				
				/* 设置PDF文档适应移动屏幕 */
				.panel-body.pdfbox object,
				.panel-body.pdfbox iframe,
				.panel-body.pdfbox embed {
					max-width: 100% !important;
				}
				
				/* 确保预览区域居中 */
				.kjview {
					display: flex;
					flex-direction: column;
					align-items: center;
				}
			}
			
			/* 超小屏幕优化 */
			@media (max-width: 480px) {
				/* PDF浏览器 */
				.tcbox {
					width: 98% !important;
					max-width: 98% !important;
					margin: 0 auto !important;
				}
				
				.pdfbox {
					width: 100% !important;
					height: 85vh !important;
				}
				
				/* 课件详情 */
				.kjboxtitle {
					font-size: 14px;
				}
				
				.jj p {
					font-size: 13px;
				}
				
				/* 操作按钮 */
				.czbox div label {
					padding: 6px 12px;
					margin: 0 3px;
					font-size: 12px;
				}
				
				/* 课程标签 */
				.zsdlabel {
					padding: 4px 8px;
					font-size: 11px;
					margin: 2px;
				}
				
				.zsdlabel img {
					width: 14px;
					padding-right: 5px;
				}
				
				/* 评分区域 */
				.xx {
					width: 20px;
					height: 20px;
					background-size: contain;
				}
				
				.xxbox div span {
					font-size: 12px;
					width: 80px;
				}
				
				.xxbtnview div {
					padding: 6px 12px;
					font-size: 12px;
				}
				
				/* 文档容器更小屏幕适配 */
				.doc-container {
					height: 450px !important;
					border-radius: 0;
					border-left: none;
					border-right: none;
				}
				
				.docx-viewer .page {
					padding: 8px;
					border: none;
					box-shadow: none;
					border-bottom: 1px solid #eee;
				}
				
				/* 改善在超小屏幕上的文档阅读体验 */
				.docx-viewer p, 
				.docx-viewer span, 
				.docx-viewer div {
					word-wrap: break-word;
					max-width: 100%;
				}
				
				.docx-viewer img {
					max-width: 100%;
					height: auto !important;
				}
				
				/* 缩放控制按钮位置优化 */
				.doc-toolbar button {
					padding: 4px 8px;
					font-size: 12px;
				}
			}

			/* 视频容器样式 - 只保留宽度100%，移除max-width和max-height */
			.video-container {
				width: 100% !important;
				aspect-ratio: 16/9 !important;
				background: #000 !important;
				display: flex !important;
				align-items: center !important;
				justify-content: center !important;
				border-radius: 0.5rem !important;
				box-shadow: 0 4px 12px rgba(0,0,0,0.15) !important;
				margin: 0 !important;
				overflow: hidden !important;
			}
			.video-container video {
				width: 100% !important;
				height: 100% !important;
				object-fit: contain !important;
				display: block !important;
				background: #000 !important;
			}
			
			/* 确保PPT图片在黑色背景下可见 */
			.video-container img.ppt-img {
				background: transparent !important;
				box-shadow: 0 0 10px rgba(255,255,255,0.1) !important;
			}
			
			/* 优化视频列表 */
			.kjdg {
				max-height: 600px;
			}
			
			.kjlist {
				overflow-y: auto;
				max-height: calc(100% - 40px);
			}
			
			.kjitem {
				display: flex;
				align-items: center;
				padding: 10px;
				border-bottom: 1px solid #f0f0f0;
				cursor: pointer;
				transition: background-color 0.2s ease;
			}
			
			.kjitem:hover {
				background-color: #f9f9f9;
			}
			
			.kjitem .fm {
				width: 80px;
				height: 45px;
				object-fit: cover;
				border-radius: 4px;
				margin-right: 10px;
			}
			
			.kjitem .kjbt {
				flex-grow: 1;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				font-size: 14px;
			}
			
			.kjitem .zt {
				width: 16px;
				height: 16px;
			}
			
			/* 当前播放视频高亮 */
			.kjitem.active {
				background-color: rgba(166, 93, 87, 0.1);
			}
			
			.kjitem.active .kjbt {
				color: #A65D57;
				font-weight: 500;
			}
			
			/* 响应式适配 */
			@media (max-width: 768px) {
				.video-container {
					max-height: 50vh; /* 移动设备上减小视频最大高度 */
					border-radius: 4px;
				}
				
				.video-container video {
					max-height: 50vh;
				}
				
				.kjitem .fm {
					width: 60px;
					height: 34px;
				}
				
				/* 移动端布局调整 */
				.container-wrapper {
					padding: 0 0.5rem;
				}
				
				.new-layout {
					grid-template-columns: 1fr;
					gap: 15px;
				}
				
				.vertical-tabs {
					position: fixed;
					right: 10px;
					top: 40%;
					transform: translateY(-50%);
					width: 50px;
					z-index: 100;
				}
				
				.vertical-tabs > div {
					padding: 8px 4px;
				}
				
				.vertical-tabs > div span {
					font-size: 11px;
				}
				
				.vertical-tabs > div img {
					width: 22px;
					height: 22px;
					margin-bottom: 4px;
				}
				
				.vertical-content {
					padding: 8px 5px;
					width: 100%;
					font-size: 0.8em;
				}
				
				.vertical-tabs-container {
					max-width: 80px;
				}
				
				.zsdlabel {
					padding: 0.15rem 0.3rem;
					font-size: 0.7rem;
				}
			}

			/* 新布局样式 - 黄金分割比例布局 */
			.new-layout {
				display: grid !important;
				grid-template-columns: 61.8% 38.2% !important;
				gap: 16px !important;
				align-items: start !important;
				max-width: 1300px !important;
				margin: 0 auto !important;
				padding: 0 !important;
				position: relative !important; /* 标签定位 */
			}
			
			/* 视频区域样式扩展 - 使用Tailwind左对齐 */
			.full-width {
				width: 100% !important;
				padding-right: 0;
				padding-left: 0;
				margin-left: 0; /* 确保左对齐 */
			}
			
			/* 确保视频内容与标题左对齐 */
			.kjview {
				margin-left: 0 !important;
				padding-left: 0 !important;
				/* justify-content: flex-start !important; */
				align-items: flex-start !important;
			}
			
			/* 垂直选项卡区域 - 现代化设计 */
			.vertical-tabs-container {
				display: flex !important;
				flex-direction: column !important;
				height: 100% !important;
				min-height: 380px !important;
				border: none !important;
				border-radius: 12px !important;
				overflow: hidden !important;
				box-shadow: 0 4px 20px rgba(0, 0, 0, 0.06) !important;
				margin: 0 !important;
				width: 94% !important; /* 稍微缩小宽度，使视觉效果更舒适 */
				margin-left: auto !important;
				align-items: flex-start !important;
				max-width: none !important;
				background-color: #fff !important;
				backdrop-filter: blur(10px) !important;
				-webkit-backdrop-filter: blur(10px) !important;
			}
			
			/* 垂直选项卡按钮区 - 现代风格设计 */
			.vertical-tabs {
				position: relative !important; 
				right: auto !important;
				top: auto !important;
				width: 100% !important;
				background-color: #fafafa !important;
				display: flex !important;
				flex-direction: row !important;
				justify-content: space-between !important; /* 改为space-between确保均匀分布 */
				gap: 8px !important;
				z-index: 10 !important;
				padding: 12px 16px !important;
				border-bottom: 1px solid rgba(0,0,0,0.05) !important;
				flex-wrap: nowrap !important; /* 确保不换行 */
			}
			
			.vertical-tabs > div {
				padding: 8px 10px !important;
				display: flex !important;
				flex-direction: row !important;
				align-items: center !important;
				border: none !important;
				border-radius: 10px !important;
				cursor: pointer !important;
				transition: all 0.3s cubic-bezier(0.16, 1, 0.3, 1) !important;
				background-color: #f2f2f2 !important;
				box-shadow: 0 1px 2px rgba(0,0,0,0.03) !important;
				width: auto !important;
				flex: 1 !important;
				justify-content: center !important;
				gap: 8px !important;
				min-width: 0 !important; /* 允许弹性收缩 */
				overflow: hidden !important; /* 防止内容溢出 */
			}
			
			.vertical-tabs > div:hover {
				background-color: #f5f5f5 !important;
				transform: translateY(-2px) !important;
				box-shadow: 0 4px 8px rgba(0,0,0,0.04) !important;
			}
			
			.vertical-tabs > div.aaakj {
				background-color: rgba(166, 93, 87, 0.08) !important;
				border: none !important;
				box-shadow: none !important;
				position: relative !important;
			}
			
			.vertical-tabs > div.aaakj::after {
				content: "" !important;
				position: absolute !important;
				bottom: -4px !important;
				left: 20% !important;
				width: 60% !important;
				height: 3px !important;
				background-color: #A65D57 !important;
				border-radius: 2px !important;
			}
			
			/* 标签图标和文字布局优化 */
			.vertical-tabs > div img {
				width: 16px !important; /* 稍微缩小图标 */
				height: 16px !important;
				margin: 0 !important;
				opacity: 0.85 !important;
				transition: all 0.2s ease !important;
				flex-shrink: 0 !important; /* 防止图标被挤压 */
			}
			
			.vertical-tabs > div:hover img {
				opacity: 1 !important;
				transform: scale(1.05) !important;
			}
			
			/* 确保文本能在必要时优雅地截断 */
			.vertical-tabs > div span {
				font-size: 13px !important; /* 稍微缩小字体 */
				color: #444 !important;
				text-align: center !important;
				font-weight: 400 !important;
				transition: all 0.2s ease !important;
				white-space: nowrap !important; /* 防止文本换行 */
				overflow: hidden !important;
				text-overflow: ellipsis !important; /* 文本溢出显示省略号 */
			}
			
			.vertical-tabs > div:hover span {
				color: #333 !important;
			}
			
			.vertical-tabs > div.aaakj span {
				color: #A65D57 !important;
				font-weight: 500 !important;
			}
			
			.vertical-tabs > div.aaakj img {
				opacity: 1 !important;
			}
			
			/* 垂直选项卡内容区 - 现代排版 */
			.vertical-content {
				flex-grow: 1 !important;
				overflow-y: auto !important;
				padding: 20px !important;
				background-color: #fff !important;
				width: 100% !important;
				margin: 0 !important;
				font-size: 0.95em !important;
				height: calc(100% - 62px) !important; /* 减去标签栏的高度 */
				line-height: 1.6 !important;
				color: #333 !important;
			}
			
			/* 选项卡内容区样式优化 - 现代风格 */
			.kjxqbox, .kjpfbox {
				height: 100%;
				overflow-y: auto;
				color: #333;
				line-height: 1.6;
				font-family: "Microsoft YaHei", "PingFang SC", sans-serif;
			}
			
			/* 知识点样式优化 - 现代布局 */
			#zsdbox {
				display: flex;
				flex-wrap: wrap;
				gap: 8px;
				padding: 6px 0;
			}
			
			/* 课程详情样式优化 - 现代排版 */
			.kjboxtitle {
				font-size: 15px;
				font-weight: 600;
				margin-bottom: 12px;
				color: #222;
				position: relative;
				border-left: none;
				padding-left: 14px;
			}
			
			.kjboxtitle::before {
				content: "";
				position: absolute;
				left: 0;
				top: 0;
				height: 100%;
				width: 4px;
				background: linear-gradient(to bottom, #A65D57, #c27d77);
				border-radius: 2px;
			}
			
			.jj p {
				line-height: 1.7;
				color: #444;
				font-size: 14px;
				margin-bottom: 10px;
			}
			
			/* 优化整体内容区域 */
			.vertical-content {
				scrollbar-width: thin;
				scrollbar-color: #ddd transparent;
			}
			
			.vertical-content::-webkit-scrollbar {
				width: 5px;
			}
			
			.vertical-content::-webkit-scrollbar-track {
				background: transparent;
			}
			
			.vertical-content::-webkit-scrollbar-thumb {
				background-color: #ddd;
				border-radius: 6px;
			}
			
			/* 评价区域美化 */
			.pjtitle {
				font-size: 15px !important;
				font-weight: 600 !important;
				margin-bottom: 14px !important;
				color: #222 !important;
				position: relative !important;
				padding-left: 14px !important;
			}
			
			.pjtitle::before {
				content: "" !important;
				position: absolute !important;
				left: 0 !important;
				top: 0 !important;
				height: 100% !important;
				width: 4px !important;
				background: linear-gradient(to bottom, #A65D57, #c27d77) !important;
				border-radius: 2px !important;
			}
			
			.xxbox {
				background: #fbfbfb !important;
				padding: 12px !important;
				border-radius: 8px !important;
				font-size: 13px !important;
				line-height: 1.6 !important;
			}
			
			.xxbox > div {
				display: flex !important;
				align-items: center !important;
				margin-bottom: 10px !important;
				padding-bottom: 8px !important;
				border-bottom: 1px solid #f0f0f0 !important;
			}
			
			.xxbox > div:last-child {
				border-bottom: none !important;
				margin-bottom: 0 !important;
				padding-bottom: 0 !important;
			}
			
			.xxbox > div span {
				font-size: 13px !important;
				color: #444 !important;
				width: 120px !important;
				flex-shrink: 0 !important;
				margin-right: 10px !important;
			}
			
			.xxbox .fs {
				margin-left: 10px !important;
				color: #A65D57 !important;
				font-weight: 500 !important;
			}
			
			/* 紧凑版知识点标签 */
			.zsdlabel {
				padding: 0.2rem 0.4rem;
				font-size: 0.75rem;
				margin: 0.2rem;
			}
			
			/* 适应性调整 */
			@media (max-width: 768px) {
				.new-layout {
					grid-template-columns: 1fr;
					grid-template-rows: auto auto;
					padding: 0;
				}
				
				.vertical-tabs-container {
					min-height: 300px;
					margin-top: 15px;
				}
				
				.vertical-tabs {
					width: 80px;
				}
				
				.vertical-tabs > div {
					padding: 10px 5px;
				}
				
				.vertical-tabs > div img {
					width: 20px;
					height: 20px;
					margin-bottom: 4px;
				}
				
				.vertical-tabs > div span {
					font-size: 12px;
				}
			}

			/* 统一所有预览内容的容器样式，保证16:9最大化填充 */
			.video-container > * {
				width: 100% !important;
				height: 100% !important;
				max-width: 100% !important;
				max-height: 100% !important;
				object-fit: contain !important;
				display: block !important;
				margin: 0 auto !important;
				background: transparent !important;
			}
			.video-container .docx-viewer,
			.video-container .pptx-viewer,
			.video-container .ppt-img,
			.video-container audio {
				width: 100% !important;
				height: 100% !important;
				max-width: 100% !important;
				max-height: 100% !important;
				object-fit: contain !important;
				display: block !important;
				margin: 0 auto !important;
				background: transparent !important;
			}
			
			/* 确保PPT图片在黑色背景下可见 */
			.video-container img.ppt-img {
				background: transparent !important;
				box-shadow: 0 0 10px rgba(255,255,255,0.1) !important;
			}
			
			/* PPT全屏模式优化 */
			.ppt-viewer-wrapper {
				width: 100%;
				position: relative;
			}
			
			/* 全屏按钮交互效果 */
			#pptFullscreenBtn {
				opacity: 0;
				transition: all 0.3s ease;
			}
			
			.video-container:hover #pptFullscreenBtn {
				opacity: 0.8;
			}
			
			#pptFullscreenBtn:hover {
				opacity: 1 !important;
				transform: scale(1.1);
				/* background: rgba(166,93,87,0.9) !important; */ /* 移除背景变化 */
			}
			
			/* 全屏模式下的容器样式 */
			#pptMainContainer:fullscreen {
				background: #000 !important;
				width: 100vw !important;
				height: 100vh !important;
				display: flex !important;
				align-items: center !important;
				justify-content: center !important;
				position: relative;
			}
			
			#pptMainContainer:fullscreen img.ppt-img {
				max-width: 98% !important;
				max-height: 98% !important;
				object-fit: contain !important;
			}
			
			/* 全屏时的控制栏渐变背景 */
			#pptMainContainer:fullscreen::after {
				content: "";
				position: absolute;
				bottom: 0;
				left: 0;
				right: 0;
				height: 80px;
				background: linear-gradient(to top, rgba(0,0,0,0.5), transparent);
				pointer-events: none;
				z-index: 4;
			}
			
			/* WORD文档渲染优化 */
			.docx-viewer {
				padding: 0 !important; /* 移除默认内边距 */
				background: white !important; /* 确保背景为白色 */
			}
			
			/* 强制隐藏文档工具栏 */
			.doc-toolbar, 
			.docx-toolbar,
			.doc-toolbar.docx-toolbar {
				display: none !important;
				height: 0 !important;
				overflow: hidden !important;
				visibility: hidden !important;
				position: absolute !important;
				opacity: 0 !important;
				z-index: -9999 !important;
				margin: 0 !important;
				padding: 0 !important;
				border: none !important;
			}
			
			.doc-container.docx-container {
				width: 100% !important;
				max-width: 100% !important; /* 容器占满整个可用宽度 */
				border: none !important; /* 移除边框 */
				border-radius: 0 !important; /* 移除圆角 */
				box-shadow: none !important; /* 移除阴影 */
				top: 0 !important;
				margin-top: 0 !important;
				padding-top: 0 !important;
				height: 100% !important;
				position: relative !important;
				background: white !important; /* 确保背景为白色 */
			}
			
			.docx-viewer .page {
				width: 100% !important;
				max-width: 100% !important; /* 页面占满容器宽度 */
				padding: 20px 30px !important; /* 增加左右内边距 */
				margin: 0 0 15px 0 !important; /* 移除左右外边距 */
				box-shadow: none !important; /* 移除阴影 */
				border: none !important; /* 移除边框 */
				background: white !important; /* 白色背景 */
				color: #000 !important; /* 确保文字为黑色 */
				font-size: 18px !important; /* 增大字体 */
				line-height: 1.6 !important; /* 优化行高 */
			}
			
			/* 优化文档内容排版 */
			.docx-viewer p, 
			.docx-viewer div, 
			.docx-viewer span {
				font-size: 18px !important; /* 正文字体大小 */
				line-height: 1.6 !important; /* 增加行高改善可读性 */
				margin-bottom: 16px !important; /* 增加段落间距 */
				color: #000 !important; /* 确保文字颜色为黑色 */
				max-width: 100% !important;
				word-break: normal !important;
				font-family: "Microsoft YaHei", "SimSun", Arial, sans-serif !important; /* 优化字体 */
			}
			
			/* 调整标题样式 */
			.docx-viewer h1 {
				font-size: 26px !important;
				margin: 24px 0 16px 0 !important;
				font-weight: bold !important;
				color: #000 !important;
			}
			
			.docx-viewer h2 {
				font-size: 22px !important;
				margin: 22px 0 14px 0 !important;
				font-weight: bold !important;
				color: #000 !important;
			}
			
			.docx-viewer h3 {
				font-size: 20px !important;
				margin: 20px 0 12px 0 !important;
				font-weight: bold !important;
				color: #000 !important;
			}
			
			/* 优化表格样式 */
			.docx-viewer table {
				border-collapse: collapse !important;
				margin: 16px 0 !important;
				width: auto !important;
				max-width: 100% !important;
				font-size: 16px !important;
				border: 1px solid #ddd !important;
			}
			
			.docx-viewer td, 
			.docx-viewer th {
				padding: 8px 12px !important;
				border: 1px solid #ddd !important;
				word-break: normal !important;
				background: white !important; /* 确保单元格背景为白色 */
			}
			
			/* 移除所有可能的灰色遮罩 */
			.docx-viewer:before, 
			.docx-viewer:after,
			.doc-container:before,
			.doc-container:after,
			.kjview:before,
			.kjview:after,
			.file-viewer-container:before,
			.file-viewer-container:after,
			.page:before,
			.page:after,
			.docx-viewer-wrapper:before,
			.docx-viewer-wrapper:after,
			div[class*="docx-viewer-wrapper"]:before,
			div[class*="docx-viewer-wrapper"]:after {
				display: none !important;
				content: none !important;
				background: transparent !important;
				opacity: 0 !important;
				visibility: hidden !important;
			}
			
			/* 移除背景色调整 */
			.docx-viewer,
			.doc-container,
			.kjview,
			.file-viewer-container,
			.page,
			.docx-viewer-wrapper,
			div[class*="docx-viewer-wrapper"] {
				background: white !important;
				background-color: white !important;
				background-image: none !important;
				padding: 0px !important;
				filter: none !important;
				-webkit-filter: none !important;
				opacity: 1 !important;
				box-shadow: none !important;
				border: none !important;
			}
			
			/* 专门处理docx-viewer-wrapper的背景 */
			.docx-viewer-wrapper {
				background: white !important;
				background-color: white !important;
				background-image: none !important;
				position: static !important;
				z-index: auto !important;
				transform: none !important;
				pointer-events: auto !important;
				mix-blend-mode: normal !important;
				opacity: 1 !important;
			}
			
			/* 隐藏WORD文档标题 */
			.docx-viewer-title {
				display: none !important;
			}
			
			/* 修复文件查看器容器样式 */
			.file-viewer-container {
				background: white !important; /* 确保背景为白色 */
				box-shadow: none !important; /* 移除阴影 */
				border: none !important; /* 移除边框 */
				width: 100% !important; /* 占满整个可用宽度 */
				max-width: 100% !important;
				margin: 0 !important;
				padding: 0 !important;
			}
			
			/* 专门针对WORD文档容器的样式 */
			.kjview.file-viewer-container {
				background: white !important;
				padding: 0 !important;
				margin: 0 !important;
				width: 100% !important;
				max-width: 100% !important;
				display: block !important;
			}
			
			/* 移除视频容器对WORD的影响 */
			.kjview.file-viewer-container .video-container {
				background: none !important;
				box-shadow: none !important;
			}
			
			/* 防止黑色遮罩 */
			.kjview:before,
			.kjview:after,
			.doc-container:before,
			.doc-container:after,
			.file-viewer-container:before,
			.file-viewer-container:after {
				display: none !important;
				content: none !important;
				background: transparent !important;
			}
			
			/* 确保文档页面内容能正常显示 */
			.docx-viewer p, 
			.docx-viewer div, 
			.docx-viewer span, 
			.docx-viewer h1, 
			.docx-viewer h2, 
			.docx-viewer h3, 
			.docx-viewer table {
				max-width: 100% !important;
				word-wrap: break-word !important;
				overflow-wrap: break-word !important;
			}
			
			/* 优化表格显示 */
			.docx-viewer table {
				width: auto !important;
				max-width: 100% !important;
				margin: 10px 0 !important;
				table-layout: auto !important;
				border-collapse: collapse !important;
				display: table !important;
			}
			
			/* 确保表格内容不溢出 */
			.docx-viewer td, 
			.docx-viewer th {
				word-break: break-word !important;
				max-width: 100% !important;
			}
			
			/* 移动端适配 */
			@media (max-width: 768px) {
				#pptFullscreenBtn {
					width: 36px !important;
					height: 36px !important;
					top: 8px !important;
					right: 8px !important;
				}
				
				#pptMainContainer:fullscreen #pptFullscreenBtn {
					width: 44px !important;
					height: 44px !important;
					top: 12px !important;
					right: 12px !important;
				}
			}
			
			/* 外部导航条样式优化 */
			.pptx-nav-external {
				display: flex;
				justify-content: center;
				align-items: center;
				gap: 8px;
				margin-top: 12px;
				background: #f5f5f5;
				padding: 10px;
				border-radius: 4px;
				flex-wrap: wrap;
				box-shadow: 0 2px 5px rgba(0,0,0,0.1);
			}
			
			.ppt-nav-btn {
				transition: transform 0.2s ease, background-color 0.2s ease;
			}
			
			.ppt-nav-btn:hover:not([disabled]) {
				transform: translateY(-2px);
				background-color: #8a4d47 !important;
			}
			
			/* 全屏模式样式 */
			#pptMainContainer:fullscreen {
				background: #000 !important;
				width: 100vw !important;
				height: 100vh !important;
				display: flex !important;
				align-items: center !important;
				justify-content: center !important;
			}
			
			#pptMainContainer:fullscreen img.ppt-img {
				max-width: 95% !important;
				max-height: 95% !important;
				object-fit: contain !important;
			}
			
			/* 全屏时的导航控制-在底部显示 */
			#pptMainContainer:fullscreen::after {
				content: "";
				position: absolute;
				bottom: 0;
				left: 0;
				right: 0;
				height: 60px;
				background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
				pointer-events: none;
				z-index: 5;
			}
			
			/* 浏览器前缀兼容 */
			#pptMainContainer:-webkit-full-screen {
				background: #000 !important;
				width: 100vw !important;
				height: 100vh !important;
			}
			
			#pptMainContainer:-moz-full-screen {
				background: #000 !important;
				width: 100vw !important;
				height: 100vh !important;
			}
			
			#pptMainContainer:-ms-fullscreen {
				background: #000 !important;
				width: 100vw !important;
				height: 100vh !important;
			}
			
			/* 移动端适配 */
			@media (max-width: 768px) {
				.pptx-nav-external {
					padding: 8px 5px;
					flex-wrap: wrap;
				}
				
				.ppt-nav-btn {
					padding: 5px 8px !important;
					min-width: auto !important;
					font-size: 12px !important;
				}
				
				#pptx-page-info {
					font-size: 12px !important;
					min-width: 60px !important;
				}
				
				/* 移动端全屏时的按钮尺寸调整 */
				#pptMainContainer:fullscreen #pptFullscreenBtn,
				#pptMainContainer:-webkit-full-screen #pptFullscreenBtn,
				#pptMainContainer:-moz-full-screen #pptFullscreenBtn {
					width: 48px !important;
					height: 48px !important;
					top: 15px !important;
					right: 15px !important;
					opacity: 0.8 !important;
				}
				
				/* 移动端触摸辅助 - 添加左右触摸区 */
				.touch-nav-hint {
					position: absolute;
					top: 30%;
					height: 40%;
					width: 50px;
					background: rgba(255,255,255,0.03);
					z-index: 3;
					opacity: 0;
				}
				
				.touch-nav-hint.left {
					left: 0;
					border-radius: 0 4px 4px 0;
				}
				
				.touch-nav-hint.right {
					right: 0;
					border-radius: 4px 0 0 4px;
				}
				
				#pptMainContainer:fullscreen .touch-nav-hint,
				#pptMainContainer:-webkit-full-screen .touch-nav-hint,
				#pptMainContainer:-moz-full-screen .touch-nav-hint {
					opacity: 0.2;
				}
			}
			
			/* PPTX/PPT图片导航按钮和页码样式 */
			.pptx-nav {
				width: 100%;
				display: flex;
				justify-content: center;
				align-items: center;
				gap: 8px;
				margin-top: 8px;
			}
			.pptx-nav button {
				padding: 4px 12px;
				border: none;
				background: #A65D57;
				color: #fff;
				border-radius: 4px;
				cursor: pointer;
				font-size: 14px;
			}
			.pptx-nav button:disabled {
				background: #ccc;
				cursor: not-allowed;
			}
			.pptx-nav span {
				color: #fff;
				font-size: 14px;
			}

			/* 全屏按钮交互效果 - 移除背景变化 */
			#pptFullscreenBtn:hover {
				opacity: 1 !important;
				transform: scale(1.1);
				/* background: rgba(166,93,87,0.9) !important; */ /* 移除背景变化 */
			}

			/* 全屏模式下的容器样式 */
			#pptMainContainer:fullscreen {
				background: #000 !important;
				width: 100vw !important;
				height: 100vh !important;
				display: flex !important;
				align-items: center !important;
				justify-content: center !important;
				position: relative;
			}

			/* 确保 kjview 容器在悬浮时没有背景变化 */
			.kjview:hover {
				background: transparent !important; /* 确保悬浮时背景透明 */
			}

			/* 当PPT查看器全屏时，隐藏页面其他主要元素 */
			body:has(#pptMainContainer:fullscreen) .topview,
			body:has(#pptMainContainer:fullscreen) .contenttitlebox,
			body:has(#pptMainContainer:fullscreen) .kjtop button, /* 返回列表按钮 */
			body:has(#pptMainContainer:fullscreen) .vertical-tabs-container,
			body:has(#pptMainContainer:fullscreen) footer,
			body:has(#pptMainContainer:fullscreen) #backtop,
			body:has(#pptMainContainer:fullscreen) .pinjiabox {
				display: none !important;
				visibility: hidden !important;
				opacity: 0 !important;
			}

			/* 兼容不同浏览器的全屏伪类 */
			body:has(#pptMainContainer:-webkit-full-screen) .topview,
			body:has(#pptMainContainer:-webkit-full-screen) .contenttitlebox,
			body:has(#pptMainContainer:-webkit-full-screen) .kjtop button,
			body:has(#pptMainContainer:-webkit-full-screen) .vertical-tabs-container,
			body:has(#pptMainContainer:-webkit-full-screen) footer,
			body:has(#pptMainContainer:-webkit-full-screen) #backtop,
			body:has(#pptMainContainer:-webkit-full-screen) .pinjiabox {
				display: none !important;
				visibility: hidden !important;
				opacity: 0 !important;
			}

			body:has(#pptMainContainer:-moz-full-screen) .topview,
			body:has(#pptMainContainer:-moz-full-screen) .contenttitlebox,
			body:has(#pptMainContainer:-moz-full-screen) .kjtop button,
			body:has(#pptMainContainer:-moz-full-screen) .vertical-tabs-container,
			body:has(#pptMainContainer:-moz-full-screen) footer,
			body:has(#pptMainContainer:-moz-full-screen) #backtop,
			body:has(#pptMainContainer:-moz-full-screen) .pinjiabox {
				display: none !important;
				visibility: hidden !important;
				opacity: 0 !important;
			}

			body:has(#pptMainContainer:-ms-fullscreen) .topview,
			body:has(#pptMainContainer:-ms-fullscreen) .contenttitlebox,
			body:has(#pptMainContainer:-ms-fullscreen) .kjtop button,
			body:has(#pptMainContainer:-ms-fullscreen) .vertical-tabs-container,
			body:has(#pptMainContainer:-ms-fullscreen) footer,
			body:has(#pptMainContainer:-ms-fullscreen) #backtop,
			body:has(#pptMainContainer:-ms-fullscreen) .pinjiabox {
				display: none !important;
				visibility: hidden !important;
				opacity: 0 !important;
			}
			
			/* 如果 kjview 内部还有其他可能导致遮罩的元素，也需要处理 */
			.kjview:hover > div, 
			.kjview:hover > span { /* 示例，具体看 kjview 内部结构 */
				background: transparent !important;
			}
			
			/* 确保PPT容器在全屏时占据整个视口，并且其他内容不干扰 */
			#pptMainContainer:fullscreen,
			#pptMainContainer:-webkit-full-screen,
			#pptMainContainer:-moz-full-screen,
			#pptMainContainer:-ms-fullscreen {
				position: fixed !important;
				top: 0 !important;
				left: 0 !important;
				width: 100vw !important;
				height: 100vh !important;
				z-index: 2147483647 !important; /* 确保在最顶层 */
				background-color: #000 !important; /* 确保背景是黑色 */
			}

			/* 评分标签页样式 */
			#pf4 {
				padding: 20px 15px !important;
				height: 100% !important;
				overflow-y: auto !important;
				background-color: white !important;
				font-family: "Microsoft YaHei", "PingFang SC", sans-serif !important;
			}
			
			.rating-title {
				font-size: 15px !important;
				font-weight: 600 !important;
				margin-bottom: 18px !important;
				color: #222 !important;
				position: relative !important;
				padding-left: 14px !important;
				border-left: 4px solid #A65D57 !important;
			}
			
			.rating-title::before {
				content: "" !important;
				position: absolute !important;
				left: 0 !important;
				top: 0 !important;
				height: 100% !important;
				width: 4px !important;
				background: linear-gradient(to bottom, #A65D57, #c27d77) !important;
				border-radius: 2px !important;
			}
			
			.rating-box {
				background: #fbfbfb !important;
				padding: 18px !important;
				border-radius: 10px !important;
				box-shadow: 0 3px 12px rgba(0, 0, 0, 0.06) !important;
				margin-bottom: 20px !important;
				transition: all 0.3s ease !important;
			}
			
			.rating-box:hover {
				transform: translateY(-2px) !important;
				box-shadow: 0 5px 15px rgba(166, 93, 87, 0.1) !important;
			}
			
			.rating-box > div {
				display: flex !important;
				align-items: center !important;
				margin-bottom: 15px !important;
				padding-bottom: 15px !important;
				border-bottom: 1px solid #f0f0f0 !important;
			}
			
			.rating-box > div:last-child {
				border-bottom: none !important;
				margin-bottom: 0 !important;
				padding-bottom: 0 !important;
			}
			
			.rating-box > div span {
				font-size: 14px !important;
				color: #444 !important;
				width: 120px !important;
				flex-shrink: 0 !important;
				margin-right: 15px !important;
				font-weight: 500 !important;
			}
			
			/* 星星评分优化 */
			.xx {
				transition: all 0.2s ease !important;
				cursor: pointer !important;
				margin: 0 2px !important;
				position: relative !important;
			}
			
			.xx:hover {
				transform: scale(1.2) rotate(5deg) !important;
			}
			
			/* 评分按钮美化 */
			.rating-action {
				display: flex !important;
				justify-content: center !important;
				margin-top: 25px !important;
			}
			
			.rating-btn {
				background: linear-gradient(to right, #A65D57, #c27d77) !important;
				color: white !important;
				padding: 10px 30px !important;
				border-radius: 6px !important;
				border: none !important;
				font-size: 14px !important;
				font-weight: 500 !important;
				cursor: pointer !important;
				transition: all 0.3s ease !important;
				box-shadow: 0 3px 10px rgba(166, 93, 87, 0.25) !important;
			}
			
			.rating-btn:hover {
				background: linear-gradient(to right, #8a4d47, #A65D57) !important;
				box-shadow: 0 4px 12px rgba(166, 93, 87, 0.35) !important;
				transform: translateY(-2px) !important;
			}
			
			.rating-btn:disabled {
				background: #cccccc !important;
				cursor: not-allowed !important;
				box-shadow: none !important;
				transform: none !important;
			}
			
			.rating-message {
				text-align: center !important;
				padding: 12px !important;
				margin: 15px 0 !important;
				border-radius: 6px !important;
				font-size: 14px !important;
				font-weight: 500 !important;
				opacity: 0 !important;
				transform: translateY(10px) !important;
				transition: all 0.3s ease !important;
			}
			
			.rating-message.show {
				opacity: 1 !important;
				transform: translateY(0) !important;
			}
			
			.rating-message.success {
				color: #155724 !important;
				background-color: #d4edda !important;
				border: 1px solid #c3e6cb !important;
			}
			
			.rating-message.error {
				color: #721c24 !important;
				background-color: #f8d7da !important;
				border: 1px solid #f5c6cb !important;
			}
			
			/* 移动端优化 */
			@media (max-width: 768px) {
				.vertical-tabs {
					padding: 10px 12px !important;
					gap: 6px !important;
				}
				
				.vertical-tabs > div {
					padding: 6px 8px !important;
				}
				
				.vertical-tabs > div img {
					width: 14px !important;
					height: 14px !important;
				}
				
				.vertical-tabs > div span {
					font-size: 12px !important;
				}
				
				#pf4 {
					padding: 15px 12px !important;
				}
				
				.rating-box {
					padding: 15px !important;
				}
				
				.rating-box > div span {
					width: 100px !important;
					font-size: 13px !important;
				}
			}
			
			/* 确保活动状态清晰可见 */
			.vertical-tabs > div.aaakj {
				background-color: rgba(166, 93, 87, 0.1) !important;
				border: none !important;
				box-shadow: none !important;
				position: relative !important;
			}
			
			.vertical-tabs > div.aaakj::after {
				content: "" !important;
				position: absolute !important;
				bottom: -4px !important;
				left: 20% !important;
				width: 60% !important;
				height: 3px !important;
				background-color: #A65D57 !important;
				border-radius: 2px !important;
			}
			
			/* 添加下载按钮样式 */
			.docx-download-btn {
				position: absolute;
				top: 10px;
				right: 10px;
				width: 36px;
				height: 36px;
				background-color: rgba(166, 93, 87, 0.8);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				z-index: 10;
				cursor: pointer;
				transition: all 0.3s ease;
				box-shadow: 0 2px 5px rgba(0, 0, 0, 0.2);
				border: none;
				outline: none;
			}
			
			.docx-download-btn:hover {
				background-color: rgba(166, 93, 87, 1);
				transform: scale(1.1);
			}
			
			.docx-download-btn svg {
				width: 20px;
				height: 20px;
				fill: white;
			}
			
			@media (max-width: 768px) {
				.docx-download-btn {
					width: 32px;
					height: 32px;
					top: 8px;
					right: 8px;
				}
				
				.docx-download-btn svg {
					width: 18px;
					height: 18px;
				}
			}

			/* 添加下载按钮样式 - 修改为更明显的样式 */
			.docx-download-btn {
				position: absolute !important;
				top: 15px !important;
				right: 15px !important;
				width: 40px !important;
				height: 40px !important;
				background-color: #A65D57 !important;
				border-radius: 50% !important;
				display: flex !important;
				align-items: center !important;
				justify-content: center !important;
				z-index: 9999 !important;
				cursor: pointer !important;
				transition: all 0.3s ease !important;
				box-shadow: 0 3px 8px rgba(0, 0, 0, 0.3) !important;
				border: 2px solid #fff !important;
				outline: none !important;
			}
			
			.docx-download-btn:hover {
				background-color: #8a4d47 !important;
				transform: scale(1.1) !important;
			}
			
			.docx-download-btn svg {
				width: 22px !important;
				height: 22px !important;
				fill: white !important;
			}
			
			@media (max-width: 768px) {
				.docx-download-btn {
					width: 36px !important;
					height: 36px !important;
					top: 10px !important;
					right: 10px !important;
				}
				
				.docx-download-btn svg {
					width: 20px !important;
					height: 20px !important;
				}
			}
		</style>
	</head>
	<body class="index">
		<!-- PDF查看器已移除 -->
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<button class="menu-toggle" id="menuToggle" aria-label="菜单切换">
					<i class="fas fa-bars"></i>
				</button>
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<!-- <div class="contenttitlebox">
			<div>
				<a id="zxxx" href="onlinelearning.html">在线学习</a>
				<img src="img/jtright.png" />
				<a href="onlinelearning3.html" id="hsname"></a>
				<img src="img/jtright.png" />
				<label class="aa" id="titlesss"></label>
			</div>
		</div> -->
		<div class="content">
			<div class="container-wrapper max-w-[1300px] px-4 mx-auto">
				<div class="kjtop flex flex-wrap items-start justify-between mb-4">
					<div class="jttopitem flex-1 mr-4">
						<div class="flex items-center">
							<div class="kjname text-xl font-medium text-gray-800" id="kjname"></div>
							<button onclick="showRatingTab()" class="ml-3 inline-flex items-center text-sm text-gray-600 hover:text-gray-800 transition-colors duration-300">
								<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-amber-500" viewBox="0 0 20 20" fill="currentColor">
									<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
								</svg>
								<span class="ml-1">评分</span>
							</button>
						</div>
						<div class="kjzz flex flex-wrap items-center mt-2">
							<label class="flex items-center mr-4"><img src="img/kjzz.png" class="mr-1" /><span id="zz" class="text-sm text-gray-600"></span></label>
							<label class="flex items-center mr-4"><img src="img/kjyj.png" class="mr-1" /><span id="yj" class="text-sm text-gray-600"></span></label>
							<label class="flex items-center"><img src="img/kjpf.png" class="mr-1" /><span id="pf" class="text-sm text-gray-600"></span></label>
						</div>
					</div>
					<div class="jttopitem flex items-end self-end">
						<button id="backButton" onclick="goBackToResourceList()" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300">
							<svg class="-ml-0.5 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
								<path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
							</svg>
							返回列表
						</button>
					</div>
				</div>
			</div>

			<div id="bbbb2" class="kjbox new-layout pl-4 grid grid-cols-[61.8%_38.2%] gap-4 max-w-[1300px] mx-auto items-stretch">
				<div class="kjview w-full h-full p-0 m-0">
					<!-- 这里留空，由js根据类型动态插入视频、PPT、Word、音频等内容 -->
					</div>
				<div class="vertical-tabs-container h-full" style="width:100%!important;max-width:none!important;">
					<div class="vertical-tabs" id="vertical-select">
						<div onclick="selects(this)" data-id="1" class="aaakj shadow-sm transition-all hover:scale-105">
							<img id="xqimg" src="img/kcxq1.png" class="w-5 h-5 mb-1" />
							<span class="text-xs font-medium">课程详情</span>
						</div>
						<div onclick="selects(this)" data-id="2" class="shadow-sm transition-all hover:scale-105">
							<img id="zsdimg" src="img/zsd2.png" class="w-5 h-5 mb-1" />
							<span class="text-xs font-medium">知识点</span>
						</div>
						<div onclick="selects(this)" data-id="3" class="shadow-sm transition-all hover:scale-105">
							<img id="pjimg" src="img/kcpf2.png" class="w-5 h-5 mb-1" />
							<span class="text-xs font-medium">课程评价</span>
						</div>
						<div onclick="selects(this)" data-id="4" class="shadow-sm transition-all hover:scale-105">
							<img id="wdpfimg" src="img/kcpf2.png" class="w-5 h-5 mb-1" />
							<span class="text-xs font-medium">我要评分</span>
						</div>
					</div>
					<div class="vertical-content" style="width: 100% !important; flex-grow: 1 !important; overflow-y: auto !important; height: calc(100% - 60px) !important;">
						<div id="pf1" class="kjxqbox text-sm h-full overflow-y-auto">
							<div class="kjboxtitle text-gray-700 border-l-3 border-red-700 pl-2 mb-3 text-sm font-semibold">课程简介:</div>
							<div class="jj text-gray-600 text-xs leading-relaxed">
								<p id="jj"></p>
							</div>
							<div id="kcgl" class="mt-3">
								<div id="kj111" class="mb-2">
									<div class="kjboxtitle text-gray-700 border-l-3 border-red-700 pl-2 mb-2 text-sm font-semibold">课件:</div>
									<div class="abq">
										<label onclick="showsss(this)" id="kj" class="text-xs text-blue-600 hover:text-blue-800 cursor-pointer">查看详情</label>
									</div>
								</div>
								<div id="kj222">
									<div class="kjboxtitle text-gray-700 border-l-3 border-red-700 pl-2 mb-2 text-sm font-semibold">课后测试:</div>
									<div class="abq">
										<!-- PDF功能已移除 -->
									</div>
								</div>
							</div>
						</div>
						<div id="pf2" style="display: none;" class="kjpfbox">
							<div class="zsdbox flex flex-wrap gap-1 p-1" id="zsdbox">
							</div>
						</div>
						<div id="pf3" style="display: none;" class="kjpfbox">
							<div class="pjtitle text-sm font-medium text-gray-700 mb-2">课程评价详情</div>
							<div class="xxbox text-xs" id="xxbox2">
							</div>
						</div>
						<div id="pf4" style="display: none;" class="kjpfbox">
							<div class="rating-title">我的评分</div>
							<div class="rating-box" id="ratingBox">
								<!-- 评分内容将由JS动态插入 -->
							</div>
							<div id="ratingMessage" class="rating-message" style="display: none;"></div>
							<div class="rating-action">
								<button id="submitRatingBtn" class="rating-btn" onclick="pinfensubmit()">提交评分</button>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<!-- 我们保留但隐藏该弹窗，以备不时之需 -->
		<div class="pinjiabox" style="display: none;">
			<div class="pjbbb">
				<div class="topbags">
					<img src="img/kjpf2.png" />
					课件评分
				</div>
				<div class="xxbox" id="xxbox">

				</div>
				<div class="str" id="pfwz" style="display: none;">您已评无法重复提交评分！</div>
				<div class="xxbtnview" id="wpf">
					<div onclick="pinfensubmit()">确定</div>
					<div onclick="hidepfbox()">取消</div>
				</div>
				<div class="xxbtnview" style="display: none;" id="ypf">
					<div class="gbgbh" onclick="hidepfbox()">关闭</div>
				</div>
			</div>
		</div>
		<div id="qp" onclick="hideqp()">
			<img id="img2" src="" />
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		
		<!-- 全局样式定义：确保文档查看器在所有类型都保持居中和良好尺寸 -->
		<style>
			/* PDF和文档查看器通用样式 - 应用于FileViewer和PDF预览 */
			.file-viewer-container {
				width: 90%;
				max-width: 1000px;
				margin: 15px auto;
				border-radius: 8px;
				box-shadow: 0 2px 10px rgba(0,0,0,0.1);
				overflow: hidden;
			}
			
			/* 让kjview容器内的所有查看器组件居中 */
			.kjview {
				display: flex;
				flex-direction: row-reverse;
				/* align-items: center; */
				width: 100%;
			}
			
			.kjview > * {
				max-width: 100%;
				width: 90%;
				margin: 0 auto;
			}
			
			/* 确保文档播放器占满空间但保持居中 */
			.kjview .doc-container,
			.kjview .doc-toolbar,
			.kjview .audio-player,
			.kjview embed,
			.kjview iframe,
			.kjview object {
				width: 90% !important;
				max-width: 1000px !important;
				margin: 0 auto !important;
			}
			
			/* 增强文档内容自适应 */
			.docx-viewer p, 
			.docx-viewer span, 
			.docx-viewer div, 
			.docx-viewer td, 
			.docx-viewer th, 
			.docx-viewer li {
				word-break: break-word !important;
				overflow-wrap: break-word !important;
				max-width: 100% !important;
			}
			
			.docx-viewer table {
				table-layout: auto !important;
				width: auto !important;
				max-width: 100% !important;
}

/* PPT主图片样式优化 */
#pptMainImage {
	max-width: 100% !important;
	max-height: 100% !important;
	width: auto !important;
	height: auto !important;
	object-fit: contain !important;
	background-color: transparent !important;
			}
			
			/* 防止表格撑破容器 */
			.docx-viewer .page {
				overflow-x: auto !important;
			}
			
			/* 横屏模式适配 */
			@media screen and (orientation: landscape) and (max-width: 920px) {
				.docx-viewer .page {
					max-width: 99.5% !important;
					padding: 8px !important;
				}
				
				.file-viewer-container,
				.kjview > *,
				.kjview .doc-container,
				.kjview .doc-toolbar,
				.kjview .audio-player,
				.kjview embed,
				.kjview iframe,
				.kjview object,
				.panel-body.pdfbox,
				.panel-body.pdfbox object,
				.panel-body.pdfbox iframe,
				.panel-body.pdfbox embed {
					width: 99.5% !important;
					max-width: 99.5% !important;
				}
				
				/* 横屏模式微调字体 */
				.docx-viewer {
					font-size: 0.95em !important;
				}
				
				.docx-viewer table {
					font-size: 0.9em !important;
				}
				
				/* 横屏状态下减少边距 */
				.docx-viewer p, 
				.docx-viewer span, 
				.docx-viewer div, 
				.docx-viewer td, 
				.docx-viewer th, 
				.docx-viewer li {
					margin: 0.2em 0 !important;
				}
			}
			
			/* 移动端适配 */
			@media (max-width: 768px) {
				.file-viewer-container,
				.kjview > *,
				.kjview .doc-container,
				.kjview .doc-toolbar,
				.kjview .audio-player,
				.kjview embed,
				.kjview iframe,
				.kjview object {
					width: 98% !important;
					max-width: 98% !important;
					margin-left: auto !important;
					margin-right: auto !important;
				}
				
				/* 移动端字体缩小 */
				.docx-viewer {
					font-size: 0.9em;
				}
				
				/* 移动端表格处理 */
				.docx-viewer table {
					font-size: 0.85em;
					width: 98% !important;
					max-width: 98% !important;
				}
				
				/* 确保文档阅读区域更宽 */
				.docx-viewer .page {
					max-width: 98% !important;
					padding: 10px !important;
				}
				
				/* 调整PDF查看器宽度 */
				.panel-body.pdfbox {
					width: 98% !important;
					max-width: 98% !important;
				}
				
				/* 优化文档内部元素 */
				.docx-viewer p, 
				.docx-viewer span, 
				.docx-viewer div, 
				.docx-viewer td, 
				.docx-viewer th, 
				.docx-viewer li {
					max-width: 98% !important;
				}
				
				/* 在特小屏幕上进一步调整 */
				@media (max-width: 480px) {
					.docx-viewer {
						font-size: 0.85em;
					}
					
					.docx-viewer table {
						font-size: 0.8em;
					}
					
					/* 特小屏幕进一步减少留白 */
					.file-viewer-container,
					.kjview > *,
					.kjview .doc-container,
					.kjview .doc-toolbar,
					.kjview .audio-player,
					.kjview embed,
					.kjview iframe,
					.kjview object,
					.docx-viewer .page,
					.panel-body.pdfbox {
						width: 99% !important;
						max-width: 99% !important;
					}
				}
			}
		</style>
		<script>
			function closetc() {
				$("#tcbox").hide()
			}

			function showsss(item) {
				window.location.href = "onlinelearning4.html?id=" + $(item).attr("data-id")
			}

			function hidepfbox() {
				$(".pinjiabox").attr("style", "display: none")
			}

			// 修改评分按钮功能 - 不再显示弹窗，而是切换到评分标签页
			function showRatingTab() {
				// 切换到评分标签页
				let ratingTab = $("#vertical-select div[data-id='4']");
				if (ratingTab.length) {
					selects(ratingTab[0]);
				}
			}

			function showpfbox() {
				// 修改为切换到评分标签页
				showRatingTab();
			}

			function showpdf(item) {
				// PDF功能已移除
				cocoMessage.info("PDF功能已移除，请联系管理员", 3000);
				console.log("PDF功能已被后端移除");
			}
			let imgindex = 0
			let imglist = null
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				
				// 确保黄金分割比在桌面和移动端都生效
				function applyGoldenRatioLayout() {
					const mainContainer = document.getElementById('bbbb2');
					if (mainContainer) {
						if (window.innerWidth <= 768) {
							// 移动端：堆叠布局
							mainContainer.classList.remove('grid-cols-[61.8%_38.2%]');
							mainContainer.classList.add('grid-cols-1');
						} else {
							// 桌面端：黄金分割比
							mainContainer.classList.remove('grid-cols-1');
							mainContainer.classList.add('grid-cols-[61.8%_38.2%]');
						}
					}
				}
				
				// 页面加载时立即应用布局
				applyGoldenRatioLayout();
				
				// 监听窗口大小变化
				$(window).on('resize', applyGoldenRatioLayout);
				
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
					getclass('onlinelearning.html')
					getinfo()
					getfooterlink()
					getclassid()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
			})

			// 评星函数升级版
			function xzxx(item) {
				let itemid = $(item).attr("data-id")
				//1 0星  2 半星  3  满星
				//当点击一个星星时  这个星包括前面的星全部满星 同时 data-id=3  后面的星星全部取消 data-id = 1
				if (itemid == 1) {
					$(item).attr('class', 'xx xx3').attr("data-id", "3")
					$(item).prevAll('label').attr("class", "xx xx3").attr("data-id", "3")
				} else if (itemid == 3) {
					//当点击的星星是满星时   这个星星后面的所有星星取消 并且 data-id =1
					$(item).attr('class', 'xx xx1').attr("data-id", "1")
					$(item).nextAll('label').attr("class", "xx xx1").attr("data-id", "1")
				}

				//黑星 0分   红星 1分   半星 0.5分
				let num = 0
				let allxx = $(item).prevAll('label')
				for (let i = 0; i < allxx.length; i++) {
					if ($(allxx[i]).attr("data-id") == '1') {
						num += 0
					} else if ($(allxx[i]).attr("data-id") == '3') {
						num += 1
					}
				}
				if ($(item).attr("data-id") == '1') {
					num += 0
				} else if ($(item).attr("data-id") == '3') {
					num += 1
				}
				// console.log(num)
				$(item).nextAll("div").html(num + '分')
			}

			function selects(item) {
				// 更新垂直选项卡的active状态
				let allitem = $("#vertical-select div");
				for (let i = 0; i < allitem.length; i++) {
					$(allitem[i]).attr("class", "");
				}
				$(item).attr("class", "aaakj");
				
				let thisid = $(item).attr("data-id");
				if (thisid == 1) {
					$("#xqimg").attr("src", "img/kcxq1.png");
					$("#pjimg").attr("src", "img/kcpf2.png");
					$("#zsdimg").attr("src", "img/zsd2.png");
					$("#wdpfimg").attr("src", "img/myrating.png");
					$("#pf1").show();
					$("#pf2").hide();
					$("#pf3").hide();
					$("#pf4").hide();
				} else if (thisid == 2) {
					$("#xqimg").attr("src", "img/kcxq2.png");
					$("#pjimg").attr("src", "img/kcpf2.png");
					$("#zsdimg").attr("src", "img/zsd1.png");
					$("#wdpfimg").attr("src", "img/myrating.png");
					$("#pf1").hide();
					$("#pf2").show();
					$("#pf3").hide();
					$("#pf4").hide();
				} else if (thisid == 3) {
					$("#xqimg").attr("src", "img/kcxq2.png");
					$("#pjimg").attr("src", "img/kcpf1.png");
					$("#zsdimg").attr("src", "img/zsd2.png");
					$("#wdpfimg").attr("src", "img/myrating.png");
					$("#pf1").hide();
					$("#pf2").hide();
					$("#pf3").show();
					$("#pf4").hide();
				} else if (thisid == 4) {
					$("#xqimg").attr("src", "img/kcxq2.png");
					$("#pjimg").attr("src", "img/kcpf2.png");
					$("#zsdimg").attr("src", "img/zsd2.png");
					$("#wdpfimg").attr("src", "img/kcpf1.png");
					$("#pf1").hide();
					$("#pf2").hide();
					$("#pf3").hide();
					$("#pf4").show();
					
					// 加载评分内容
					loadUserRating();
				}
			}

			// 加载用户评分
			function loadUserRating() {
				// 获取课程评分信息
				$.ajax({
					url: baseurl + "/course/meta/" + getUrlParam('id'),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// 处理评分状态
							const isCourseEvaluate = res.data.isCourseEvaluate;
							
							if (isCourseEvaluate == 0) {
								// 用户未评分，显示评分界面
								let xxhtml = '';
								res.data.listCurrentCourseEvaluate.map((item) => {
									xxhtml += '<div data-id="' + item.id + '"><span>'
									xxhtml += item.name
									xxhtml += ':</span>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label>' + 
										'<div class="fs">0分</div></div>';
								});
								
								$("#ratingBox").html(xxhtml);
								$("#submitRatingBtn").prop("disabled", false).show();
								
								// 清除任何消息
								$("#ratingMessage").hide().removeClass("success error");
							} else {
								// 用户已评分，显示已提交的评分
								let xxhtml = '';
								res.data.listCurrentCourseEvaluate.map((item) => {
									xxhtml += '<div><span>'
									xxhtml += item.name
									xxhtml += ':</span>';
									for (let i = 0; i < 5; i++) {
										if (i < item.score) {
											xxhtml += '<label class="xx xx3"></label>';
										} else {
											xxhtml += '<label class="xx xx1"></label>';
										}
									}
									xxhtml += '<div class="fs">' + item.score + '分</div></div>';
								});
								
								$("#ratingBox").html(xxhtml);
								$("#submitRatingBtn").prop("disabled", true).hide();
								
								// 显示已提交消息
								$("#ratingMessage")
									.html("您已成功提交评分，感谢您的反馈！")
									.addClass("success")
									.removeClass("error")
									.addClass("show")
									.show();
							}
						}
					}
				});
			}

			function getclassid() {
				$.ajax({
					url: baseurl + "/category/teaching",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							classid = res.data[0].id
							pflid = res.data[0].parentId
							$("#hsname").html(res.data.name)
						}
					}
				})
			}
			let time1 = Date.now()
			let classid = null
			let pflid = null
			let xkid = null
			
			let videotime = null

			var vid = document.getElementById("videos");
			if(vid) {
				vid.onloadedmetadata = function() {
					// console.log('metadata loaded!');
					// console.log(vid.duration); //打印时长
					videotime = vid.duration
				};
			}
		
			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			function pinfensubmit() {
				let all = $("#ratingBox div")
				let json = []
				for (let i = 0; i < all.length; i++) {
					json.push({
						courseId: getUrlParam('id'),
						evaluateId: $(all[i]).attr("data-id"),
						score: $(all[i]).find(".xx3").length
					})
				}
				
				// 显示加载状态
				$("#submitRatingBtn").prop("disabled", true).text("提交中...");
				
				$.ajax({
					url: baseurl + "/course/evaluate/add",
					type: 'POST',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: JSON.stringify(json),
					dataType: 'json',
					success: (res) => {
						if(res.code == '200') {
							// 提交成功
							$("#ratingMessage")
								.html("评分提交成功，感谢您的反馈！")
								.addClass("success")
								.removeClass("error")
								.addClass("show")
								.show();
								
							// 重新加载评分数据
							getinfo(true);
							setTimeout(loadUserRating, 500);
						} else {
							// 提交失败
							$("#ratingMessage")
								.html("评分提交失败，请稍后重试")
								.addClass("error")
								.removeClass("success")
								.addClass("show")
								.show();
								
							$("#submitRatingBtn").prop("disabled", false).text("提交评分");
						}
					},
					error: () => {
						// 提交错误
						$("#ratingMessage")
							.html("网络错误，请稍后重试")
							.addClass("error")
							.removeClass("success")
							.addClass("show")
							.show();
							
						$("#submitRatingBtn").prop("disabled", false).text("提交评分");
					}
				})
			}
			
			let looklist = []//已看过的图片
			let allimglist = []//全部的图片
			let lookcount = 0 //已经看过的数量
			
			function getinfo(isimg) {
				$.ajax({
					url: baseurl + "/course/meta/" + getUrlParam('id'),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							$("#pf").html(parseFloat(res.data.listCourseEvaluate[3].zh).toFixed(1))
							addview(res.data.courceId)
							let hhhxx = ""
							res.data.listCourseEvaluate.map((item) => {
								if (!item.score) {
									item.score = 0
								}
								if (item.name) {
									hhhxx += '<div><span>' + item.name + ':</span>'
									for (let i = 0; i < 5; i++) {
										if (i < parseInt(item.score)) {
											hhhxx += '<label class="xx xx3"></label>'
										} else {
											if (parseFloat(item.score) > 0) {
												if (parseFloat(item.score) % parseInt(item.score) === 0) {
													hhhxx += '<label class="xx xx1"></label>'
												} else {
													hhhxx += '<label class="xx xx2"></label>'
												}
											} else {
												hhhxx += '<label class="xx xx1"></label>'
											}
										}
									}
									hhhxx += '<div class="fs">' + parseFloat(item.score).toFixed(1) +
										'分</div></div>'
								} else {
									hhhxx += '<div><span>资源综合评分:</span>'
									for (let i = 0; i < 5; i++) {
										if (i < parseInt(item.zh)) {
											hhhxx += '<label class="xx xx3"></label>'
										} else {
											if (i < parseInt(item.zh)) {
												hhhxx += '<label class="xx xx3"></label>'
											} else {
												if (parseFloat(item.zh) > 0) {
													if (parseFloat(item.zh) % parseInt(item.zh) === 0) {
														hhhxx += '<label class="xx xx1"></label>'
													} else {
														hhhxx += '<label class="xx xx2"></label>'
													}
												} else {
													hhhxx += '<label class="xx xx1"></label>'
												}
											}
										}
									}
									hhhxx += '<div class="fs">' + parseFloat(item.zh).toFixed(1) +
										'分</div></div>'
								}
							})
							$("#xxbox2").html(hhhxx)
							if (res.data.isCourseEvaluate == 0) {
								let xxhtml = ''
								res.data.listCurrentCourseEvaluate.map((item) => {
									xxhtml += '<div data-id="' + item.id + '"><span>'
									xxhtml += item.name
									xxhtml += ':</span>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label></div>'
								})
								$("#xxbox").html(xxhtml)
							} else {
								//已评分
								$("#ypf").show()
								$("#wpf").hide()
								$("#pfwz").show()
								let xxhtml = ''
								res.data.listCurrentCourseEvaluate.map((item) => {
									xxhtml += '<div><span>'
									xxhtml += item.name
									xxhtml += ':</span>'
									for (let i = 0; i < 5; i++) {
										if (i < item.score) {
											xxhtml += '<label class="xx xx3"></label>'
										} else {
											xxhtml += '<label class="xx xx1"></label>'
										}
									}
									xxhtml += '</div>'
								})
								$("#xxbox").html(xxhtml)
							}
							if (!isimg) {
								xkid = res.data.projectId
								$("#kjname").html(res.data.title)
								document.title = res.data.title
								$("#titlesss").html(res.data.title)
								$("#zz").html(res.data.author)
								$("#yj").html(res.data.view)
								$("#jj").html(res.data.introduction)
								
								let kjnum = 0
								let kj2num = 0
								
								// 初始化文件查看器
								window.FileViewer.init({
									containerSelector: '#bbbb2',
									previewContainerSelector: '.kjview',
									baseUrl: baseurl
								});
								
								const fileData = res.data.cmsResourcesCourseMetaList[0];
								const fileType = fileData.attachType.toLowerCase();
								const fileName = fileData.attachName;
								const fileUrl = baseurl + fileData.attachPath;
								const metadata = fileData;
								
								// 处理课件关联
								res.data.cmsResourcesCourseMetaList.map((item) => {
									if (item.attachType == 'ppt' || item.attachType == 'pptx') {
										$("#kj").attr("data-id", item.id);
										kjnum += 1;
									}
								});
								if (kjnum === 0) {
									$("#kj111").hide();
								}

						// PDF功能已移除
						$("#kj222").hide();
								
								// 显示基本布局
								$("#bbbb2").show();
								$("#bbbb2").css("display", "flex");
								
								// 统一调用FileViewer.display渲染左侧内容
								window.FileViewer.display(fileUrl, fileType, fileName, metadata);
								
								if (fileType === 'mp4') {
									// 视频文件处理
									$("#video").show();
									$("#videobox").show();
									$("#ppt").hide();
									$("#pptbox").hide();
									
									$("#videos").attr("src", fileUrl);
									
									let html = '<div class="kjitem"><img class="fm" src="' + 
										baseurl + fileData.coverPath + '" /><div class="kjbt">1</div>' + 
										'<img class="zt" src="img/yxx.png" /></div>';
									
									$("#list2").html(html);
								} 
								else if (fileType === 'mp3') {
									// 音频文件处理
									$("#video").hide();
									$("#videobox").hide();
									$("#ppt").hide();
									$("#pptbox").hide();
									$("#kcgl").hide();
									
									// 创建现代简洁的音频播放器UI
									const audioPlayerContainer = $('<div class="modern-audio-player"></div>');
									
									// 使用资源封面图片代替默认图标
									let albumArtHtml = '';
									// 尝试使用封面路径或文件的封面路径
									let coverUrl = '';
									if (res.data.coverPath) {
										coverUrl = baseurl + res.data.coverPath;
									} else if (fileData.coverPath) {
										coverUrl = baseurl + fileData.coverPath;
									}
									
									if (coverUrl) {
										// 如果有封面图片，使用封面图片作为背景
										console.log("使用封面图片:", coverUrl);
										albumArtHtml = '<div class="album-art" style="background-image: url(\'' + coverUrl + '\'); background-size: cover; background-position: center;"></div>';
									} else {
										// 否则使用默认图标
										console.log("未找到封面图片，使用默认图标");
										albumArtHtml = '<div class="album-art"><div class="audio-icon"></div></div>';
									}
									const albumArt = $(albumArtHtml);
									
									// 使用资源标题作为音频名称
									const audioInfo = $('<div class="audio-info"><div class="audio-title">' + res.data.title + '</div><div class="audio-author">' + $("#zz").text() + '</div></div>');
									const progressContainer = $('<div class="audio-progress-container"><div class="audio-time current">00:00</div><div class="progress-bar-container"><div class="progress-bar"></div></div><div class="audio-time duration">00:00</div></div>');
									const controls = $('<div class="audio-controls"><button class="btn-prev"><svg viewBox="0 0 24 24"><path d="M6,18V6h2v12H6z M9.5,12l8.5,6V6L9.5,12z"></path></svg></button><button class="btn-play"><svg class="play-icon" viewBox="0 0 24 24"><path d="M8,5.14v14l11-7L8,5.14z"></path></svg><svg class="pause-icon" viewBox="0 0 24 24"><path d="M6,19h4V5H6V19z M14,5v14h4V5H14z"></path></svg></button><button class="btn-next"><svg viewBox="0 0 24 24"><path d="M16,18h2V6h-2V18z M6,18l8.5-6L6,6V18z"></path></svg></button></div>');
									
									// 组装播放器
									audioPlayerContainer.append(albumArt).append(audioInfo).append(progressContainer).append(controls);
									$(".kjview").empty().addClass("modern-player-container").append(audioPlayerContainer);
									
									// 创建音频元素并设置自动播放
									const audioElement = $('<audio autoplay muted></audio>');
									audioElement.attr('src', fileUrl);
									audioPlayerContainer.append(audioElement);
									
									// 音频播放器逻辑
									const audio = audioElement[0];
									
									// 先静音自动播放（浏览器策略允许），然后尝试取消静音并播放
									setTimeout(function() {
										console.log("尝试自动播放音频...");
										audio.muted = false;
										audio.play()
											.then(() => {
												console.log("自动播放成功！");
												playBtn.addClass('playing');
												albumArt.addClass('rotating');
											})
											.catch(err => {
												console.warn("无法自动播放（可能需要用户交互）:", err);
												// 显示轻提示，不打断用户
												setTimeout(() => {
													cocoMessage.info("点击播放按钮开始播放", 3000);
												}, 1000);
											});
									}, 500);
									const playBtn = controls.find('.btn-play');
									const progressBar = progressContainer.find('.progress-bar');
									const currentTimeEl = progressContainer.find('.current');
									const durationEl = progressContainer.find('.duration');
									
									// 初始设置暂停状态，等待加载完成后自动播放
									playBtn.removeClass('playing');
									
									// 音频控制事件
									playBtn.on('click', function() {
										if (audio.paused) {
											audio.play().then(() => {
												playBtn.addClass('playing');
												albumArt.addClass('rotating');
											}).catch(err => {
												console.error('播放失败:', err);
												cocoMessage.error('音频播放失败，请稍后重试');
											});
										} else {
											audio.pause();
											playBtn.removeClass('playing');
											albumArt.removeClass('rotating');
										}
									});
									
									// 音频加载完成后自动播放
									audio.addEventListener('canplaythrough', function() {
										if (!audio.paused) return; // 避免重复播放
										audio.play().then(() => {
											playBtn.addClass('playing');
											albumArt.addClass('rotating');
										}).catch(err => {
											console.warn('自动播放失败:', err);
											// 许多浏览器会阻止自动播放，显示提示
											cocoMessage.info('点击播放按钮开始播放');
										});
									});
									
									// 进度条更新
									audio.addEventListener('timeupdate', function() {
										const percent = (audio.currentTime / audio.duration) * 100;
										progressBar.css('width', percent + '%');
										currentTimeEl.text(formatTime(audio.currentTime));
									});
									
									// 音频加载后设置总时长
									audio.addEventListener('loadedmetadata', function() {
										durationEl.text(formatTime(audio.duration));
									});
									
									// 进度条点击事件
									progressContainer.find('.progress-bar-container').on('click', function(e) {
										const percent = (e.offsetX / $(this).width());
										audio.currentTime = percent * audio.duration;
									});
									
									// 上一曲/下一曲按钮（此处为装饰，可根据需要实现功能）
									controls.find('.btn-prev, .btn-next').on('click', function() {
										// 这里可以实现切换歌曲功能，现在只是提示
										cocoMessage.info('功能开发中');
									});
									
									// 时间格式化函数
									function formatTime(seconds) {
										const min = Math.floor(seconds / 60);
										const sec = Math.floor(seconds % 60);
										return (min < 10 ? '0' + min : min) + ':' + (sec < 10 ? '0' + sec : sec);
									}
									
									// 播放器初始化完成后的额外操作
									// 为整个播放器添加点击事件，点击封面也可以播放/暂停
									albumArt.on('click', function() {
										playBtn.trigger('click');
									});
									
									// 显示加载中状态
									const loadingIndicator = $('<div class="loading-indicator">准备播放中...</div>');
									albumArt.append(loadingIndicator);
									
									// 音频加载成功时移除加载指示器
									audio.addEventListener('canplay', function() {
										loadingIndicator.fadeOut(300, function() {
											$(this).remove();
										});
									});
									
									// 播放错误时处理
									audio.addEventListener('error', function() {
										loadingIndicator.text('加载失败，请重试').addClass('error');
										cocoMessage.error('音频加载失败');
									});
								}
								else if (fileType === 'doc' || fileType === 'docx') {
									// 文档文件处理
									$("#video").hide();
									$("#videobox").hide();
									$("#ppt").hide();
									$("#pptbox").hide();
									$("#kcgl").hide();
									
									// 保存文档URL到全局变量，供下载使用
									window.currentDocUrl = fileUrl;
									window.currentFileName = fileName || 'document.docx';
									console.log('文档URL已保存：', window.currentDocUrl);
									
									// 使用FileViewer显示文档，添加特殊样式确保居中
									$(".kjview").addClass("file-viewer-container");
									window.FileViewer.display(fileUrl, fileType, fileName, {
										containerClass: 'file-viewer-container',
										docContainerHeight: $(window).width() <= 768 ? '500px' : '700px', // 增加高度
										fitToWidth: true,
										enableResponsiveRendering: true,
										customClass: {
											toolbar: 'docx-toolbar',
											container: 'docx-container'
										},
										hideTitle: true, // 隐藏文档标题
										enhancedWordRendering: true, // 启用增强Word渲染
										fullWidthMode: true, // 启用全宽模式
										cleanBackground: true, // 清理背景
										removeBoxShadow: true, // 移除阴影
										removeBorders: true, // 移除边框
										hideToolbar: true, // 完全隐藏工具栏
										// 文档加载完成后执行
										onDocumentReady: function() {
											// 移除可能的黑色背景
											$('.kjview').css('background', 'white');
											$('.doc-container').css('background', 'white');
											$('.docx-viewer').css('background', 'white');
											// 确保容器宽度100%
											$('.kjview, .doc-container, .docx-viewer').css({
												'width': '100%',
												'max-width': '100%',
												'margin': '0',
												'box-shadow': 'none',
												'border': 'none'
											});
											
											// 强制隐藏工具栏
											$('.doc-toolbar').hide();
											
											// 调整文档容器位置和高度（补偿工具栏隐藏后的空间）
											$('.doc-container').css({
												'top': '0',
												'height': '100%',
												'position': 'relative'
											});
											
											// 添加下载按钮 - 使用更明显的方式添加
											if ($('.docx-download-btn').length === 0) {
												// 先移除可能存在的旧按钮
												$('.docx-download-btn').remove();
												
												// 创建新按钮元素
												const downloadBtn = $('<button class="docx-download-btn" title="下载文档"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/></svg></button>');
												
												// 添加到文档容器
												$('.doc-container').append(downloadBtn);
												
												// 也添加到kjview确保显示
												$('.kjview').append(downloadBtn.clone());
												
												// 绑定下载事件
												$('.docx-download-btn').on('click', function(e) {
													e.preventDefault();
													e.stopPropagation();
													
													// 创建下载链接
													const a = document.createElement('a');
													a.href = fileUrl;
													a.download = fileName || 'document.docx';
													document.body.appendChild(a);
													a.click();
													document.body.removeChild(a);
													
													// 显示下载提示
													cocoMessage.success('文档下载中...', 2000);
												});
												
												console.log('下载按钮已添加', downloadBtn);
											}
											
											// 使用定时器确保按钮在DOM完全渲染后添加
											setTimeout(function() {
												if ($('.docx-download-btn').length === 0) {
													const downloadBtn = $('<button class="docx-download-btn" title="下载文档"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/></svg></button>');
													$('.doc-container').append(downloadBtn);
													$('.kjview').append(downloadBtn.clone());
													
													$('.docx-download-btn').on('click', function(e) {
														e.preventDefault();
														e.stopPropagation();
														
														const a = document.createElement('a');
														a.href = fileUrl;
														a.download = fileName || 'document.docx';
														document.body.appendChild(a);
														a.click();
														document.body.removeChild(a);
														
														cocoMessage.success('文档下载中...', 2000);
													});
													
													console.log('延迟添加下载按钮成功');
												}
											}, 1000);
											
											// 完全移除灰色遮罩
											setTimeout(function() {
												// 确保完全移除所有可能的灰色遮罩和背景
												$('.kjview, .doc-container, .docx-viewer, .page, .file-viewer-container, .docx-viewer-wrapper, div[class*="docx-viewer-wrapper"]').css({
													'background': 'white',
													'background-color': 'white',
													'background-image': 'none',
													'filter': 'none',
													'-webkit-filter': 'none',
													'opacity': '1',
													'box-shadow': 'none',
													'border': 'none'
												});
												
												// 移除所有伪元素
												$('<style>.kjview:before, .kjview:after, .doc-container:before, .doc-container:after, .docx-viewer:before, .docx-viewer:after, .page:before, .page:after, .file-viewer-container:before, .file-viewer-container:after, .docx-viewer-wrapper:before, .docx-viewer-wrapper:after, div[class*="docx-viewer-wrapper"]:before, div[class*="docx-viewer-wrapper"]:after { display: none !important; content: none !important; background: transparent !important; opacity: 0 !important; visibility: hidden !important; }</style>').appendTo('head');
												
												// 增大字体
												$('.docx-viewer p, .docx-viewer div, .docx-viewer span').css({
													'font-size': '18px',
													'line-height': '1.6',
													'color': '#000'
												});
												
												// 优化段落间距
												$('.docx-viewer p').css('margin-bottom', '16px');
												
												// 确保背景元素完全透明
												$('div[style*="background"]').css('background', 'white');
												
												// 再次检查下载按钮
												if ($('.docx-download-btn').length === 0) {
													const downloadBtn = $('<button class="docx-download-btn" title="下载文档"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/></svg></button>');
													$('.doc-container').append(downloadBtn);
													$('.kjview').append(downloadBtn.clone());
													
													$('.docx-download-btn').on('click', function(e) {
														e.preventDefault();
														e.stopPropagation();
														
														const a = document.createElement('a');
														a.href = fileUrl;
														a.download = fileName || 'document.docx';
														document.body.appendChild(a);
														a.click();
														document.body.removeChild(a);
														
														cocoMessage.success('文档下载中...', 2000);
													});
													
													console.log('最终检查添加下载按钮');
												}
											}, 1500);
										}
									});
								}
								else if (fileType === 'ppt' || fileType === 'pptx') {
									// PPT文件处理
									$("#video").hide();
									$("#videobox").hide();
									
									// 如果有PPT图片列表
									if (fileData.attachListPath && fileData.attachListPath.length > 0) {
										$("#ppt").show();
										$("#pptbox").show();
										
										$("#img").attr("src", baseurl + fileData.attachListPath[0]);
										$("#img2").attr("src", baseurl + fileData.attachListPath[0]);
										
										let html = "";
										imglist = fileData.attachListPath;
										
										fileData.attachListPath.map((item, index) => {
											html += '<div class="kjitem" onclick="showimg(this)" data-url2="'+item+'" data-url="' +
												baseurl + item + '" data-index="' + index +
												'"><img class="fm" src="' + baseurl + item +
												'" /><div class="kjbt">' +
												(index + 1) +
												'</div><img class="zt" src="img/yxx.png" /></div>';
										});
										
										$("#list").html(html);
									} else {
										// 没有PPT图片列表时，使用FileViewer处理
										$(".kjview").addClass("file-viewer-container");
										window.FileViewer.display(fileUrl, fileType, fileName, {
											containerClass: 'file-viewer-container',
											fitToWidth: true,
											enableResponsiveRendering: true
										});
									}
								} 
								else {
									// 其他未知类型文件的处理
									$("#video").hide();
									$("#videobox").hide();
									$("#ppt").hide();
									$("#pptbox").hide();
									$("#kcgl").hide();
									
									// 使用FileViewer提供下载链接
									$(".kjview").addClass("file-viewer-container");
									window.FileViewer.display(fileUrl, fileType, fileName, {
										containerClass: 'file-viewer-container',
										fitToWidth: true,
										enableResponsiveRendering: true
									});
								}
							}
						
							// 根据文件类型设置不同的学习记录处理方式
							const fileType = res.data.cmsResourcesCourseMetaList[0].attachType.toLowerCase();
							
							// 设置退出页面时的学习记录保存
							window.onbeforeunload = function() {
								if (JSON.parse(userinfo).roleName == '学生') {
									let time2 = Date.now();
									let value = time2 - time1;
									var days = parseInt(value / (1000 * 60 * 60 * 24));
									var hours = parseInt((value % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
									var minutes = parseInt((value % (1000 * 60 * 60)) / (1000 * 60));
									var seconds = Math.floor((value % (1000 * 60)) / 1000);
									
									let rwid = null;
									if (getUrlParam('taskid')) {
										rwid = getUrlParam('taskid');
									}
								
									// 根据不同的文件类型设置不同的记录参数
									let totalInfo = 0;
									let progress = "";
									
									if (fileType === 'mp4') {
										// 视频文件学习记录
										totalInfo = videotime || 0;
									}
									else if (fileType === 'mp3') {
										// 音频文件学习记录
										totalInfo = value / 1000; // 使用学习时间作为总时长
									}
									else if (fileType === 'ppt' || fileType === 'pptx') {
										// PPT文件学习记录
										if (res.data.cmsResourcesCourseMetaList[0].attachListPath) {
											allimglist = res.data.cmsResourcesCourseMetaList[0].attachListPath;
											
											// 如果是首次加载，初始化记录
											if (!looklist || looklist.length === 0) {
												looklist = [allimglist[0]];
												lookcount = 1;
											}
											
											totalInfo = allimglist.length;
											progress = lookcount;
										}
									}
									else if (fileType === 'doc' || fileType === 'docx') {
										// 文档文件学习记录
										totalInfo = value / 1000; // 使用学习时间作为总信息量
									}
									else {
										// 其他所有类型的文件，使用页面停留时间作为学习记录
										totalInfo = value / 1000;
									}
								
									let json = {
										infoId: getUrlParam('id'), //信息id
										categoryId: pflid, //所属分类id
										totalInfo: totalInfo, //总时长或总页数
										positioning: days + "天" + hours + "时" + minutes + "分" + seconds + "秒", //学习了多久
										progress: progress, //进度
										type: '在线学习',
										learningTime: value, //学习时长
										sectionId: xkid, //学科ID
										taskId: rwid
									};
								
									fetch(baseurl + '/study/record/add', {
										method: 'POST',
										headers: {
											"Authorization": sessionStorage.getItem("header"),
											"Content-Type": "application/json"
										},
										body: JSON.stringify(json),
										keepalive: true
									});
									
									// 移除确认提示，使用更弱的提示方式，让用户可以直接离开
									try {
										// 如果有提示组件可用，就显示提示而不是弹窗
										if (window.cocoMessage) {
											cocoMessage.success("学习记录已保存", 1000);
										}
									} catch (e) {
										// 忽略任何错误，不影响用户离开页面
									}
								}
								// 不返回任何内容，避免弹出确认对话框
							};
							
							// 如果是PPT并有图片列表，初始化浏览记录
							if (fileType === 'ppt' || fileType === 'pptx') {
								if (res.data.cmsResourcesCourseMetaList[0].attachListPath) {
									allimglist = res.data.cmsResourcesCourseMetaList[0].attachListPath;
									looklist = [allimglist[0]];
									lookcount = 1;
								}
							}
						
							
						}


						let zsdhtml = ''

						let knowledge = res.data.knowledge
						// 添加空值检查，确保knowledge存在且非空
						if (knowledge && typeof knowledge === 'string') {
							knowledge.split(',').forEach((item) => {
								if (item != null && item != '') {
									zsdhtml += `<div class="zsdlabel"><img src="img/zsd.png"/>${item}</div>`
								}
							})
						} else {
							// console.log('知识点信息不存在或格式不正确')
							// 如果knowledge为空，可以添加默认文本或者留空
							// zsdhtml = '<div class="zsdlabel"><img src="img/zsd.png"/>暂无知识点</div>'
						}
						$("#zsdbox").html(zsdhtml)
						//<div class="zsdlabel"><img src="img/zsd.png"/>知识点1</div>
					}
				})
			}

			function showimg(item) {
				$("#img").attr("src", $(item).attr("data-url"))
				$("#img2").attr("src", $(item).attr("data-url"))
				imgindex = $(item).attr("data-index")
				//判断已看列表中是否有当前点击的这个图片
				let ishave = 0
				looklist.forEach((myurl)=>{
					if(myurl == $(item).attr("data-url2")){
						ishave += 1
					}
				})
				
				if(ishave == 0){
					//没有看过 则将这个图片添加到已经看过的列表
					looklist.push($(item).attr("data-url2"))
					lookcount += 1 //更新已看过的数量
				}else{
					//已经看过了 则不做处理
				}
				
				// console.log('全部图片',allimglist)
				// console.log('已经看过的图片',looklist)
				// console.log('已看过的数量',lookcount)
			}

			function syy() {
				// console.log(imgindex)
				if (imgindex > 0) { //当前下标记 大于0则表示前面还有
					imgindex -= 1
					$("#img").attr("src", baseurl + imglist[imgindex])
					$("#img2").attr("src", baseurl + imglist[imgindex])
					
					let ishave = 0
					looklist.forEach((item)=>{
						if(item == allimglist[imgindex]){
							ishave += 1
						}
					})
					
					if(ishave == 0){
						//没有看过 则将这个图片添加到已经看过的列表
						looklist.push(allimglist[imgindex])
						lookcount += 1 //更新已看过的数量
					}else{
						//已经看过了 则不做处理
					}
				}
				// console.log('全部图片',allimglist)
				// console.log('已经看过的图片',looklist)
				// console.log('已看过的数量',lookcount)
			}

			function xyy() {
				// console.log(imgindex)
				if (imgindex < (imglist.length - 1)) { //当前下标记 小于 list长度-1  则表示后面还有
					imgindex += 1
					$("#img").attr("src", baseurl + imglist[imgindex])
					$("#img2").attr("src", baseurl + imglist[imgindex])
					
					let ishave = 0
					looklist.forEach((item)=>{
						if(item == allimglist[imgindex]){
							ishave += 1
						}
					})
					
					if(ishave == 0){
						//没有看过 则将这个图片添加到已经看过的列表
						looklist.push(allimglist[imgindex])
						lookcount += 1 //更新已看过的数量
					}else{
						//已经看过了 则不做处理
					}
				}
				// console.log('全部图片',allimglist)
				// console.log('已经看过的图片',looklist)
				// console.log('已看过的数量',lookcount)
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					if (res.data[i].id == getUrlParam('id') || res.data[i].redirectUrl ==
			// 						'onlinelearning.html') {
			// 						$("#zxxx").attr('href', "onlinelearning.html?id=" + res.data[i].id)
			// 						$("#zxxx").html(res.data[i].name)
			// 						pflid = res.data[i].id
			// 						html += '<a class="menuitem active" href="' + res.data[i].redirectUrl + '?id=' +
			// 							res
			// 							.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					} else {
			// 						html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 							.data[
			// 								i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					}
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function hideqp() {
				$("#qp").attr("style", "display: none;")
			}

			function qpshow() {
				$("#qp").attr("style", "display: flex;")
			}

			function addview(id) {
				$.ajax({
					url: baseurl + "/course/view/" + id,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {}
				})
			}

			// 返回上一页函数
			function goBack() {
				window.history.back();
			}

			// 返回到资源列表页面
			function goBackToResourceList() {
				// 检查是否有保存的筛选状态
				const savedFilterState = sessionStorage.getItem('resourceFilterState');
				
				if (savedFilterState) {
					try {
						// 使用保存的完整筛选状态
						const filterState = JSON.parse(savedFilterState);
						
						// 构建返回URL
						let url = "onlinelearning3.html";
						
						// 添加基本参数
						url += "?categoryId=" + (filterState.categoryId || "");
						url += "&currentPage=" + (filterState.pageIndex || 1);
						
						// 添加课程ID
						if (filterState.courseId) url += "&courseId=" + filterState.courseId;
						
						// 添加章节ID
						if (filterState.chapterId) url += "&chapterId=" + filterState.chapterId;
						
						// 添加节ID
						if (filterState.nodeId) url += "&nodeId=" + filterState.nodeId;
						
						// 添加小节ID
						if (filterState.barId) url += "&barId=" + filterState.barId;
						
						// 添加文件类型
						if (filterState.fileType) url += "&fileType=" + filterState.fileType;
						
						// 添加属性ID
						if (filterState.attributeId) url += "&attributeId=" + filterState.attributeId;
						
						// 添加类型ID
						if (filterState.typeId) url += "&typeId=" + filterState.typeId;
						
						// 添加类别ID (mtype)
						if (filterState.activeFilters && filterState.activeFilters.categoryOption) {
							// 防止"111"(全部)作为参数
							if (filterState.activeFilters.categoryOption !== "111") {
								url += "&mtype=" + filterState.activeFilters.categoryOption;
							}
						}
						
						// 添加排序方向
						url += "&sortDesc=" + filterState.sortDesc;
						
						// 添加搜索关键词
						if (filterState.searchKeyword) url += "&keyword=" + encodeURIComponent(filterState.searchKeyword);
						
						// 添加筛选状态标记 - 告知onlinelearning3.html应用保存的筛选状态
						url += "&applyFilter=true";
						
						// 跳转到资源列表页
						window.location.href = url;
					} catch (error) {
						console.error("解析存储的筛选状态时出错:", error);
						// 出错时回退到使用URL参数方式
						fallbackToUrlParams();
					}
				} else {
					// 如果没有保存的状态，则使用URL参数构建返回URL
					fallbackToUrlParams();
				}
				
				// 没有saved状态时使用URL参数的回退方法
				function fallbackToUrlParams() {
					// 获取分类ID参数
					let categoryId = pflid || getUrlParam('categoryId');
					
					// 获取页码参数
					let currentPage = getUrlParam('currentPage') || 1;
					
					// 构建返回URL，添加所有可能的筛选参数
					let url = "onlinelearning3.html";
					
					// 添加基本参数
					url += "?categoryId=" + (categoryId || "");
					url += "&currentPage=" + currentPage;
					
					// 添加可能的课程ID
					let courseId = getUrlParam('courseId');
					if (courseId) url += "&courseId=" + courseId;
					
					// 添加可能的章节ID
					let chapterId = getUrlParam('chapterId');
					if (chapterId) url += "&chapterId=" + chapterId;
					
					// 添加可能的节ID
					let nodeId = getUrlParam('nodeId');
					if (nodeId) url += "&nodeId=" + nodeId;
					
					// 添加可能的小节ID
					let barId = getUrlParam('barId');
					if (barId) url += "&barId=" + barId;
					
					// 添加可能的文件类型
					let fileType = getUrlParam('fileType');
					if (fileType) url += "&fileType=" + fileType;
					
					// 添加可能的属性ID
					let attributeId = getUrlParam('attributeId');
					if (attributeId) url += "&attributeId=" + attributeId;
					
					// 添加可能的类型ID
					let typeId = getUrlParam('typeId');
					if (typeId) url += "&typeId=" + typeId;
					
					// 添加可能的类别ID (mtype)
					let mtype = getUrlParam('mtype');
					if (mtype) url += "&mtype=" + mtype;
					
					// 添加可能的排序方向
					let sortDesc = getUrlParam('sortDesc');
					if (sortDesc !== null) url += "&sortDesc=" + sortDesc;
					
					// 添加可能的搜索关键词
					let keyword = getUrlParam('keyword');
					if (keyword) url += "&keyword=" + keyword;
					
					// 跳转到资源列表页
					window.location.href = url;
				}
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
		
		<!-- 移动端适配代码 -->
		<script>
			$(document).ready(function() {
				// 移动端菜单切换功能
				$("#menuToggle").on('click', function() {
					$("#menubox").toggleClass('expanded');
				});
				
				// 处理窗口大小变化，确保在切换到桌面视图时菜单正常显示
				$(window).resize(function() {
					if ($(window).width() > 768) {
						$("#menubox").removeClass('expanded');
					}
					
					// 重新适配文档查看器
					adjustDocumentViewer();
				});
				
				// 优化文档查看器显示
				function adjustDocumentViewer() {
					// 处理文档内容宽度自适应
					setTimeout(function() {
						// 计算合适的容器宽度
						let optimalWidth = '90%';
						let screenWidth = $(window).width();
						let screenHeight = $(window).height();
						let isLandscape = screenWidth > screenHeight;
						
						// 根据屏幕宽度和方向动态调整容器尺寸
						if (screenWidth <= 480) {
							optimalWidth = isLandscape ? '99.5%' : '99%'; // 特小屏幕几乎占满
						} else if (screenWidth <= 768) {
							optimalWidth = isLandscape ? '99%' : '98%'; // 普通移动设备
						} else if (screenWidth <= 1024) {
							optimalWidth = '95%'; // 平板设备
						}
						
						// 应用到所有文档容器
						$('.file-viewer-container, .kjview > *, .kjview .doc-container, .kjview .doc-toolbar, .kjview .audio-player').css({
							'width': optimalWidth,
							'max-width': screenWidth <= 768 ? optimalWidth : '1000px',
							'margin-left': 'auto',
							'margin-right': 'auto'
						});
						
						// 处理嵌入式文档
						$('.kjview embed, .kjview iframe, .kjview object, .panel-body.pdfbox object, .panel-body.pdfbox iframe, .panel-body.pdfbox embed').css({
							'width': optimalWidth,
							'max-width': '100%'
						});
						
						// 处理DOCX查看器
						$('.docx-viewer .page').css({
							'max-width': screenWidth <= 768 ? optimalWidth : '90%',
							'padding': screenWidth <= 480 ? '8px' : (screenWidth <= 768 ? '10px' : '20px'),
							'overflow-x': 'auto'
						});
						
						// 处理段落和文字
						$('.docx-viewer p, .docx-viewer span, .docx-viewer div, .docx-viewer li').css({
							'max-width': '100%',
							'word-break': 'break-word',
							'overflow-wrap': 'break-word'
						});
						
						// 特殊处理表格和内容
						if (isLandscape && screenWidth <= 920) {
							// 横屏手机模式
							$('.docx-viewer').css('font-size', '0.95em');
							$('.docx-viewer table').css({
								'max-width': '100%',
								'font-size': '0.9em',
								'width': 'auto'
							});
							$('.docx-viewer .page').css({
								'max-width': '99.5%',
								'padding': '8px'
							});
							$('.panel-body.pdfbox object, .panel-body.pdfbox iframe, .panel-body.pdfbox embed').css({
								'width': '99.5%',
								'max-width': '99.5%'
							});
							// 减少边距
							$('.docx-viewer p, .docx-viewer span, .docx-viewer div, .docx-viewer td, .docx-viewer th, .docx-viewer li').css({
								'margin': '0.2em 0'
							});
						} else {
							// 竖屏或桌面模式
							$('.docx-viewer table').css({
								'max-width': '100%',
								'font-size': screenWidth <= 480 ? '0.8em' : (screenWidth <= 768 ? '0.85em' : '1em'),
								'width': 'auto'
							});
						}
						
						// 处理图片
						$('.docx-viewer img').css({
							'max-width': '100%',
							'height': 'auto'
						});
					}, 300);
				}
				
				// 页面加载后立即调整文档查看器
				adjustDocumentViewer();
				
				// 设置MutationObserver监听DOM变化，确保动态加载的内容也能适配
				const documentObserver = new MutationObserver(function(mutations) {
					// 当DOM发生变化时重新调整文档查看器
					adjustDocumentViewer();
					
					// 查找新加载的docx内容并应用样式
					mutations.forEach(function(mutation) {
						if (mutation.addedNodes && mutation.addedNodes.length > 0) {
							// 检查是否有新的文档内容被添加
							for (let i = 0; i < mutation.addedNodes.length; i++) {
								const node = mutation.addedNodes[i];
								if (node.nodeType === 1) { // 元素节点
									// 如果是新添加的文档查看器或其内部元素
									if ($(node).hasClass('doc-container') || 
										$(node).hasClass('docx-viewer') || 
										$(node).find('.docx-viewer').length > 0 ||
										$(node).find('iframe').length > 0 ||
										$(node).find('object').length > 0) {
										// 稍等片刻让内容完全加载
										setTimeout(adjustDocumentViewer, 1000);
										break;
									}
								}
							}
						}
					});
				});
				
				// 开始观察文档容器的变化
				documentObserver.observe(document.getElementById('bbbb2'), {
					childList: true,
					subtree: true
				});
				
				// 观察弹出式PDF查看器的变化
				if (document.getElementById('tcbox')) {
					documentObserver.observe(document.getElementById('tcbox'), {
						childList: true,
						subtree: true
					});
				}
				
				// 优化移动端PDF和视频播放体验
				if ($(window).width() <= 768) {
					// 视频播放优化
					$("#videos").on('play', function() {
						// 设置视频在移动端全屏播放
						if (this.requestFullscreen) {
							this.requestFullscreen();
						} else if (this.webkitRequestFullscreen) {
							this.webkitRequestFullscreen();
						} else if (this.msRequestFullscreen) {
							this.msRequestFullscreen();
						}
					});
					
					// PDF查看器 - 功能已移除
					window.showpdf = function(item) {
						// 提示用户PDF功能已移除
						cocoMessage.warning(2000, "PDF功能已移除，请使用其他格式查看内容");
					};
					
					// 优化全屏图片查看
					window.qpshow = function() {
						$("#qp").css({
							"display": "flex",
							"align-items": "center",
							"justify-content": "center"
						});
					};
				} else {
					// 桌面端PDF功能已移除
				}
				
				
				// 优化返回顶部按钮
				$("#backtop").css({
					"position": "fixed",
					"bottom": "20px",
					"right": "20px",
					"z-index": "999"
				});
				
				// 监听设备方向变化，在横竖屏切换时调整文档布局
				$(window).on('orientationchange', function() {
					// 等待方向实际切换完成
					setTimeout(function() {
						// 强制调整文档
						forceAdjustDocument();
					}, 500);
				});
				
				// 监听窗口大小变化，适用于桌面浏览器和不支持orientation事件的设备
				$(window).on('resize', function() {
					// 延迟执行以避免频繁触发
					clearTimeout(window.resizeTimer);
					window.resizeTimer = setTimeout(function() {
						// 检测是否横屏
						checkOrientationAndAdjust();
					}, 250);
				});
				
				// 检测横屏状态并应用相应样式
				function checkOrientationAndAdjust() {
					let screenWidth = $(window).width();
					let screenHeight = $(window).height();
					let isLandscape = screenWidth > screenHeight;
					
					// 如果是横屏手机模式
					if (isLandscape && screenWidth <= 920) {
						forceAdjustDocument();
					} else {
						// 正常调整
						adjustDocumentViewer();
					}
				}
				
				// 强制调整文档布局 - 用于横屏模式
				function forceAdjustDocument() {
					// 强制应用横屏样式
					let screenWidth = $(window).width();
					let screenHeight = $(window).height();
					
					// 确认是横屏模式
					if (screenWidth > screenHeight && screenWidth <= 920) {
						// 最大化利用空间
						$('.docx-viewer .page').css({
							'max-width': '99.5%',
							'padding': '8px'
						});
						
						// 文档容器最大化
						$('.file-viewer-container, .kjview > *, .kjview .doc-container, .kjview .doc-toolbar').css({
							'width': '99.5%',
							'max-width': '99.5%'
						});
						
						// 减小字体
						$('.docx-viewer').css('font-size', '0.95em');
						$('.docx-viewer table').css('font-size', '0.9em');
						
						// 特殊处理PDF查看器
						$('.panel-body.pdfbox, .panel-body.pdfbox object, .panel-body.pdfbox iframe, .panel-body.pdfbox embed').css({
							'width': '99.5%',
							'max-width': '99.5%'
						});
						
						// 减少内部元素边距
						$('.docx-viewer p, .docx-viewer span, .docx-viewer div, .docx-viewer td, .docx-viewer th, .docx-viewer li').css({
							'margin': '0.2em 0'
						});
						
						// 强制设置表格自动换行
						$('.docx-viewer table, .docx-viewer td, .docx-viewer th').css({
							'word-break': 'break-word',
							'width': 'auto',
							'max-width': '99.5%'
						});
					} else {
						// 正常调整
						adjustDocumentViewer();
					}
				}
				
				// 初始检查设备方向
				checkOrientationAndAdjust();
			});
		</script>

		<!-- 视频自动适应JavaScript -->
		<script>
			// 优化视频列表项点击高亮
			function highlightVideoItem(index) {
				const items = document.querySelectorAll('#list2 .kjitem');
				items.forEach(item => item.classList.remove('active'));
				
				if (items[index]) {
					items[index].classList.add('active');
					// 确保选中项在视图中可见
					items[index].scrollIntoView({ behavior: 'smooth', block: 'nearest' });
				}
			}
			
			// 扩展原有的showimg函数，添加高亮效果
			const originalShowImg = window.showimg;
			window.showimg = function(item) {
				if (originalShowImg) originalShowImg(item);
				
				// 高亮当前项
				const index = $(item).attr("data-index");
				highlightVideoItem(index);
			}
			
			// 视频加载时自动调整容器
			document.addEventListener('DOMContentLoaded', function() {
				const videoElement = document.getElementById('videos');
				const videoContainer = document.querySelector('.video-container');
				
				// 确保黄金分割比在桌面和移动端都生效
				function applyGoldenRatioLayout() {
					const mainContainer = document.getElementById('bbbb2');
					if (window.innerWidth <= 768) {
						// 移动端：堆叠布局
						mainContainer.classList.remove('grid-cols-[61.8%_38.2%]');
						mainContainer.classList.add('grid-cols-1');
					} else {
						// 桌面端：黄金分割比
						mainContainer.classList.remove('grid-cols-1');
						mainContainer.classList.add('grid-cols-[61.8%_38.2%]');
					}
				}
				
				// 初始应用布局
				applyGoldenRatioLayout();
				
				// 监听窗口大小变化
				window.addEventListener('resize', applyGoldenRatioLayout);
				
				if (videoElement) {
					// 监听视频加载完成事件
					videoElement.addEventListener('loadedmetadata', function() {
						const videoWidth = this.videoWidth;
						const videoHeight = this.videoHeight;
						const aspectRatio = videoWidth / videoHeight;
						
						console.log('视频原始尺寸:', videoWidth, 'x', videoHeight, '比例:', aspectRatio);
						
						// 根据视频比例自动调整容器高度
						// 确保高度不超过视口限制
						let containerWidth = videoContainer.clientWidth;
						let containerHeight = containerWidth / aspectRatio;
						
						// 计算视口高度限制
						const viewportHeightLimit = window.innerHeight * (window.innerWidth <= 768 ? 0.5 : 0.7);
						
						// 如果计算的高度超过限制，则基于视口高度限制重新计算宽度
						if (containerHeight > viewportHeightLimit) {
							containerHeight = viewportHeightLimit;
							containerWidth = containerHeight * aspectRatio;
							// 如果宽度超过父容器，还需要再调整
							const parentWidth = videoContainer.parentElement.clientWidth;
							if (containerWidth > parentWidth) {
								containerWidth = parentWidth;
								containerHeight = containerWidth / aspectRatio;
							}
						}
						
						console.log('调整后的容器尺寸:', containerWidth, 'x', containerHeight);
						
						// 使用内联样式设置容器尺寸，确保优先级最高
						videoContainer.style.cssText += `width: ${containerWidth}px !important; height: ${containerHeight}px !important; max-width: 100% !important;`;
						videoElement.style.cssText += `width: 100% !important; height: 100% !important; object-fit: contain !important;`;
					});
					
					// 窗口大小改变时重新调整
					window.addEventListener('resize', function() {
						// 如果视频已加载，则重新调整
						if (videoElement.videoWidth) {
							const videoWidth = videoElement.videoWidth;
							const videoHeight = videoElement.videoHeight;
							const aspectRatio = videoWidth / videoHeight;
							
							// 根据视频比例自动调整容器高度
							let containerWidth = videoContainer.parentElement.clientWidth;
							let containerHeight = containerWidth / aspectRatio;
							
							// 计算视口高度限制
							const viewportHeightLimit = window.innerHeight * (window.innerWidth <= 768 ? 0.5 : 0.7);
							
							// 如果计算的高度超过限制，则基于视口高度限制重新计算宽度
							if (containerHeight > viewportHeightLimit) {
								containerHeight = viewportHeightLimit;
								containerWidth = containerHeight * aspectRatio;
								// 如果宽度超过父容器，还需要再调整
								const parentWidth = videoContainer.parentElement.clientWidth;
								if (containerWidth > parentWidth) {
									containerWidth = parentWidth;
									containerHeight = containerWidth / aspectRatio;
								}
							}
							
							// 使用内联样式设置容器尺寸，确保优先级最高
							videoContainer.style.cssText += `width: ${containerWidth}px !important; height: ${containerHeight}px !important; max-width: 100% !important;`;
							videoElement.style.cssText += `width: 100% !important; height: 100% !important; object-fit: contain !important;`;
						}
					});
				}
			});
		</script>
		
		<!-- PPT导航优化脚本 -->
		<script>
			// 在页面完全加载后执行
			document.addEventListener('DOMContentLoaded', function() {
				// 创建一个观察器来监控PPT查看器的加载
				const observer = new MutationObserver(function(mutations) {
					// 检查是否有PPT底部导航栏
					const pptNavBar = document.getElementById('pptPermanentNavBar');
					if (pptNavBar) {
						alignPPTNavigationWithRightPanel();
						// 调整PPT容器高度
						adjustPPTContainerHeight();
						// 已找到PPT导航栏，停止观察
						observer.disconnect();
					}
				});
				
				// 开始观察文档变化
				observer.observe(document.body, { childList: true, subtree: true });
				
				// 窗口大小改变时重新对齐和调整高度
				window.addEventListener('resize', function() {
					alignPPTNavigationWithRightPanel();
					adjustPPTContainerHeight();
				});
				
				// 添加资源浏览记录事件监听
				window.addEventListener('recordResourceView', function(e) {
					// 确保用户信息和必要变量存在
					if (!window.userinfo || JSON.parse(userinfo).roleName !== '学生') {
						return; // 不是学生用户，不记录
					}
					
					const fileType = e.detail.fileType;
					const currentTime = new Date().getTime();
					
					// 获取资源ID
					const infoId = getUrlParam('id');
					if (!infoId) return;
					
					// 更新全局变量
					if (typeof window.resourceViewStartTime === 'undefined') {
						window.resourceViewStartTime = currentTime;
					}
					
					if (fileType === 'ppt' || fileType === 'pptx') {
						// 确保PPT浏览进度记录正确
						if (window.pptImages && window.pptCurrentPage !== undefined) {
							const currentPage = window.pptCurrentPage + 1;
							const totalPages = window.pptImages.length;
							
							// 查找是否已记录过此页
							let pageUrl = window.pptImages[window.pptCurrentPage];
							if (!window.looklist) window.looklist = [];
							if (!window.lookcount) window.lookcount = 0;
							
							let alreadyViewed = false;
							for (let i = 0; i < window.looklist.length; i++) {
								if (window.looklist[i] === pageUrl) {
									alreadyViewed = true;
									break;
								}
							}
							
							// 如果未查看过此页，添加到记录
							if (!alreadyViewed) {
								window.looklist.push(pageUrl);
								window.lookcount++;
							}
							
							console.log(`PPT浏览记录：当前第${currentPage}/${totalPages}页，已浏览${window.lookcount}页`);
						}
					}
					
					console.log(`资源浏览记录已更新：${fileType}`);
				});
			});
			
			// 调整PPT容器高度以匹配右侧标签页的高度，同时保持16:9的比例
			function adjustPPTContainerHeight() {
				const pptContainer = document.getElementById('pptMainContainer');
				const rightPanel = document.querySelector('.vertical-tabs-container');
				const pptWrapper = document.querySelector('.ppt-viewer-wrapper');
				
				if (!pptContainer || !rightPanel) return;
				
				// 获取右侧面板高度
				const rightPanelHeight = rightPanel.offsetHeight;
				
				// 根据16:9比例计算适合的宽度和高度
				const containerWidth = pptContainer.offsetWidth;
				
				// 计算基于宽度的16:9高度
				const aspectRatioHeight = containerWidth * (9/16);
				
				// 如果按比例计算的高度小于右侧面板高度，则使用比例高度，否则使用右侧面板高度
				const finalHeight = Math.min(aspectRatioHeight, rightPanelHeight);
				
				// 设置容器高度
				pptContainer.style.height = `${finalHeight}px`;
				
				// 确保ppt-viewer-wrapper采用合适的样式定位
				if (pptWrapper) {
					pptWrapper.style.height = `${finalHeight}px`;
					pptWrapper.style.marginBottom = '20px';
				}
				
				console.log(`调整PPT容器高度: ${finalHeight}px，右侧面板高度: ${rightPanelHeight}px`);
                
                // 立即渲染当前页面，确保图片正确显示
                if (window.renderPPTPage && typeof window.pptCurrentPage !== 'undefined') {
                    setTimeout(() => {
                        window.renderPPTPage(window.pptCurrentPage);
                    }, 100);
                }
			}
			
			// 将PPT导航与右侧面板对齐的函数
			function alignPPTNavigationWithRightPanel() {
				const pptNavBar = document.getElementById('pptPermanentNavBar');
				const rightPanel = document.querySelector('.vertical-tabs-container');
				const pptContainer = document.getElementById('pptMainContainer');
				
				if (!pptNavBar || !rightPanel || !pptContainer) return;
				
				// 获取右侧面板的宽度和位置
				const rightPanelRect = rightPanel.getBoundingClientRect();
				const containerRect = pptContainer.getBoundingClientRect();
				
				// 修改PPT导航栏样式
				pptNavBar.style.width = '94%'; // 与右侧标签页宽度匹配
				pptNavBar.style.right = '0';
				pptNavBar.style.left = 'auto';
				pptNavBar.style.borderTopLeftRadius = '8px';
				pptNavBar.style.borderTopRightRadius = '8px';
				pptNavBar.style.padding = '8px 0';
				pptNavBar.style.marginBottom = '10px';
				
				// 修改PPT导航栏按钮样式
				const navButtons = pptNavBar.querySelectorAll('button');
				navButtons.forEach(button => {
					button.style.background = '#A65D57';
					button.style.border = 'none';
					button.style.boxShadow = '0 2px 4px rgba(0, 0, 0, 0.2)';
					button.style.padding = '6px 12px';
				});
			}
		</script>
		
		<!-- 添加页面关闭前的学习记录保存状态 -->
		<script>
			// 添加页面关闭前的学习记录保存状态
			let isLearningRecordSaved = false;
			let learningStartTime = Date.now();
			let isSubmittingRecord = false;
			
			// 定期保存学习记录（每5分钟）
			setInterval(() => {
				if (!isSubmittingRecord) {
					saveLearningRecord();
				}
			}, 300000); // 5分钟
			
			// 保存学习记录的函数
			function saveLearningRecord() {
				if (isSubmittingRecord) return;
				
				isSubmittingRecord = true;
				const currentTime = Date.now();
				const learningDuration = Math.floor((currentTime - learningStartTime) / 1000); // 转换为秒
				
				$.ajax({
					url: baseurl + "/course/record/add",
					type: 'POST',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: JSON.stringify({
						courseId: getUrlParam('id'),
						studyTime: learningDuration
					}),
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							isLearningRecordSaved = true;
							// 更新开始时间，为下一次记录做准备
							learningStartTime = currentTime;
						}
						isSubmittingRecord = false;
					},
					error: () => {
						isSubmittingRecord = false;
					}
				});
			}
			
			// 页面关闭前的处理
			window.addEventListener('beforeunload', function(e) {
				if (!isLearningRecordSaved) {
					// 尝试保存学习记录
					saveLearningRecord();
					
					// 显示提示消息
					e.preventDefault();
					e.returnValue = '系统正在保存您的学习记录，确定要离开吗？';
					return e.returnValue;
				}
			});
			
			// 页面可见性改变时的处理
			document.addEventListener('visibilitychange', function() {
				if (document.visibilityState === 'hidden') {
					// 页面隐藏时保存记录
					saveLearningRecord();
				} else if (document.visibilityState === 'visible') {
					// 页面重新可见时重置计时
					learningStartTime = Date.now();
					isLearningRecordSaved = false;
				}
			});
			
			// 定期检查页面是否处于活动状态
			let lastActivityTime = Date.now();
			
			function updateLastActivityTime() {
				lastActivityTime = Date.now();
			}
			
			// 监听用户活动
			['mousemove', 'keydown', 'scroll', 'click'].forEach(eventName => {
				document.addEventListener(eventName, updateLastActivityTime);
			});
			
			// 每分钟检查用户活动状态
			setInterval(() => {
				const inactiveTime = Date.now() - lastActivityTime;
				// 如果用户超过10分钟没有活动，保存记录
				if (inactiveTime > 600000) { // 10分钟
					saveLearningRecord();
				}
			}, 60000); // 1分钟
			
			// 原有代码保持不变
			function closetc() {
				$("#tcbox").hide()
			}
			// ... 其他原有代码 ...
		</script>
		
		<!-- 添加全局DOCX下载按钮检测器 -->
		<script>
		$(document).ready(function() {
			// 监听DOM变化，查找并应用DOCX下载按钮
			const observer = new MutationObserver(function(mutations) {
				mutations.forEach(function(mutation) {
					if (mutation.addedNodes && mutation.addedNodes.length > 0) {
						for (let i = 0; i < mutation.addedNodes.length; i++) {
							const node = mutation.addedNodes[i];
							if (node.nodeType === 1 && (
								$(node).hasClass('doc-container') || 
								$(node).hasClass('docx-viewer') || 
								$(node).find('.doc-container').length > 0 || 
								$(node).find('.docx-viewer').length > 0)
							) {
								// 找到DOCX查看器，添加下载按钮
								setTimeout(function() {
									if ($('.docx-download-btn').length === 0) {
										const docContainer = $('.doc-container').length ? $('.doc-container') : $('.docx-viewer');
										if (docContainer.length) {
											const downloadBtn = $('<button class="docx-download-btn" title="下载文档"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/></svg></button>');
											docContainer.append(downloadBtn);
											
											// 绑定下载事件
											$('.docx-download-btn').on('click', function(e) {
												e.preventDefault();
												e.stopPropagation();
												
												// 获取文档URL的所有可能方式
												let downloadUrl = '';
												
												// 优先使用我们存储的全局变量
												if (window.currentDocUrl) {
													downloadUrl = window.currentDocUrl;
													console.log('使用全局变量中的URL:', downloadUrl);
												} 
												// 尝试从URL参数中获取
												else if (getUrlParam && getUrlParam('id')) {
													const resourceId = getUrlParam('id');
													downloadUrl = baseurl + "/course/download/" + resourceId;
													console.log('使用资源ID构建下载URL:', downloadUrl);
												}
												// 尝试从iframe中获取
												else if ($('.doc-container iframe').length) {
													downloadUrl = $('.doc-container iframe').attr('src');
													console.log('从iframe获取URL:', downloadUrl);
												}
												// 尝试从其他全局变量获取
												else if (window.fileUrl) {
													downloadUrl = window.fileUrl;
													console.log('从window.fileUrl获取:', downloadUrl);
												}
												
												if (downloadUrl) {
													console.log('最终下载URL:', downloadUrl);
													
													// 获取文件名
													let fileName = window.currentFileName || window.fileName || 'document.docx';
													
													// 创建下载链接
													const a = document.createElement('a');
													a.href = downloadUrl;
													a.download = fileName;
													document.body.appendChild(a);
													a.click();
													document.body.removeChild(a);
													
													cocoMessage.success('文档下载中...', 2000);
												} else {
													console.error('无法获取下载链接');
													
													// 尝试使用API方式下载
													if (getUrlParam && getUrlParam('id')) {
														const resourceId = getUrlParam('id');
														const apiUrl = baseurl + "/course/download/" + resourceId;
														
														cocoMessage.info('正在使用API方式下载...', 2000);
														
														// 打开新窗口下载
														window.open(apiUrl, '_blank');
													} else {
														cocoMessage.error('无法获取文档下载链接，请联系管理员', 2000);
													}
												}
											});
											
											console.log('Observer成功添加下载按钮');
										}
									}
								}, 800);
								break;
							}
						}
					}
				});
			});
			
			// 开始观察文档变化
			observer.observe(document.body, { childList: true, subtree: true });
			
			// 立即检查一次
			setTimeout(function() {
				if ($('.doc-container').length > 0 && $('.docx-download-btn').length === 0) {
					const downloadBtn = $('<button class="docx-download-btn" title="下载文档"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/></svg></button>');
					$('.doc-container').append(downloadBtn);
					
					$('.docx-download-btn').on('click', function(e) {
						e.preventDefault();
						e.stopPropagation();
						
						// 获取文档URL的所有可能方式
						let downloadUrl = '';
						
						// 优先使用我们存储的全局变量
						if (window.currentDocUrl) {
							downloadUrl = window.currentDocUrl;
							console.log('使用全局变量中的URL:', downloadUrl);
						} 
						// 尝试从URL参数中获取
						else if (getUrlParam && getUrlParam('id')) {
							const resourceId = getUrlParam('id');
							downloadUrl = baseurl + "/course/download/" + resourceId;
							console.log('使用资源ID构建下载URL:', downloadUrl);
						}
						// 尝试从iframe中获取
						else if ($('.doc-container iframe').length) {
							downloadUrl = $('.doc-container iframe').attr('src');
							console.log('从iframe获取URL:', downloadUrl);
						}
						// 尝试从其他全局变量获取
						else if (window.fileUrl) {
							downloadUrl = window.fileUrl;
							console.log('从window.fileUrl获取:', downloadUrl);
						}
						
						if (downloadUrl) {
							console.log('最终下载URL:', downloadUrl);
							
							// 获取文件名
							let fileName = window.currentFileName || window.fileName || 'document.docx';
							
							// 创建下载链接
							const a = document.createElement('a');
							a.href = downloadUrl;
							a.download = fileName;
							document.body.appendChild(a);
							a.click();
							document.body.removeChild(a);
							
							cocoMessage.success('文档下载中...', 2000);
						} else {
							console.error('无法获取下载链接');
							
							// 尝试使用API方式下载
							if (getUrlParam && getUrlParam('id')) {
								const resourceId = getUrlParam('id');
								const apiUrl = baseurl + "/course/download/" + resourceId;
								
								cocoMessage.info('正在使用API方式下载...', 2000);
								
								// 打开新窗口下载
								window.open(apiUrl, '_blank');
							} else {
								cocoMessage.error('无法获取文档下载链接，请联系管理员', 2000);
							}
						}
					});
					
					console.log('初始检查添加下载按钮');
				}
			}, 1000);
			
			// 监听文档查看器可能的加载完成事件
			$(document).on('docxViewerReady', function(e) {
				console.log('文档查看器加载完成事件触发');
				
				// 再次检查添加下载按钮
				if ($('.docx-download-btn').length === 0) {
					const docContainer = $('.doc-container').length ? $('.doc-container') : $('.docx-viewer');
					if (docContainer.length) {
						const downloadBtn = $('<button class="docx-download-btn" title="下载文档"><svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24"><path d="M19 9h-4V3H9v6H5l7 7 7-7zM5 18v2h14v-2H5z"/></svg></button>');
						docContainer.append(downloadBtn);
						console.log('事件触发后添加下载按钮');
					}
				}
			});
		});
		</script>
		
		<!-- 额外添加API方式下载的辅助脚本 -->
		<script>
		// 创建全局下载辅助函数
		window.downloadDocument = function() {
			// 尝试从URL获取资源ID
			if (getUrlParam && getUrlParam('id')) {
				const resourceId = getUrlParam('id');
				const apiUrl = baseurl + "/course/download/" + resourceId;
				
				// 显示提示
				cocoMessage.info('正在通过API下载文档...', 2000);
				
				// 使用新窗口下载
				window.open(apiUrl, '_blank');
				return true;
			}
			return false;
		};
		
		// 添加键盘快捷键 (Ctrl+S) 用于下载
		$(document).on('keydown', function(e) {
			// 检测Ctrl+S组合键
			if (e.ctrlKey && e.keyCode === 83) {
				e.preventDefault(); // 阻止浏览器默认保存页面行为
				
				// 如果下载按钮存在，触发它的点击事件
				if ($('.docx-download-btn').length > 0) {
					$('.docx-download-btn').first().click();
				} else {
					// 尝试API下载方式
					window.downloadDocument();
				}
			}
		});
		</script>
	</body>
</html>
