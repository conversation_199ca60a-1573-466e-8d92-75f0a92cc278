// 移动端触摸交互JavaScript

// 触摸交互管理器
const TouchManager = {
    // 初始化触摸交互
    init: function() {
        this.initSwipeGestures();
        this.initTouchFeedback();
        this.initPullToRefresh();
        this.initScrollOptimization();
    },

    // 初始化滑动手势
    initSwipeGestures: function() {
        let startX, startY, startTime;
        
        document.addEventListener('touchstart', function(e) {
            const touch = e.touches[0];
            startX = touch.clientX;
            startY = touch.clientY;
            startTime = Date.now();
        }, { passive: true });
        
        document.addEventListener('touchend', function(e) {
            if (!startX || !startY) return;
            
            const touch = e.changedTouches[0];
            const endX = touch.clientX;
            const endY = touch.clientY;
            const endTime = Date.now();
            
            const deltaX = endX - startX;
            const deltaY = endY - startY;
            const deltaTime = endTime - startTime;
            
            // 检查是否为有效滑动
            if (Math.abs(deltaX) > 50 && deltaTime < 300) {
                if (deltaX > 0) {
                    // 向右滑动
                    TouchManager.handleSwipeRight(e);
                } else {
                    // 向左滑动
                    TouchManager.handleSwipeLeft(e);
                }
            }
            
            // 重置
            startX = startY = null;
        }, { passive: true });
    },

    // 处理向右滑动
    handleSwipeRight: function(e) {
        // 如果侧边菜单未打开，则打开
        const sideMenu = document.getElementById('sideMenu');
        if (sideMenu && !sideMenu.classList.contains('active')) {
            this.openSideMenu();
        }
    },

    // 处理向左滑动
    handleSwipeLeft: function(e) {
        // 如果侧边菜单已打开，则关闭
        const sideMenu = document.getElementById('sideMenu');
        if (sideMenu && sideMenu.classList.contains('active')) {
            this.closeSideMenu();
        }
    },

    // 打开侧边菜单
    openSideMenu: function() {
        const sideMenu = document.getElementById('sideMenu');
        const menuBtn = document.getElementById('menuBtn');
        
        if (sideMenu) {
            sideMenu.classList.add('active');
            document.body.style.overflow = 'hidden';
        }
        
        if (menuBtn) {
            menuBtn.classList.add('active');
        }
    },

    // 关闭侧边菜单
    closeSideMenu: function() {
        const sideMenu = document.getElementById('sideMenu');
        const menuBtn = document.getElementById('menuBtn');
        
        if (sideMenu) {
            sideMenu.classList.remove('active');
            document.body.style.overflow = '';
        }
        
        if (menuBtn) {
            menuBtn.classList.remove('active');
        }
    },

    // 初始化触摸反馈
    initTouchFeedback: function() {
        // 为可点击元素添加触摸反馈
        const clickableElements = document.querySelectorAll('button, .btn, .nav-item, .action-item, .list-item, .community-item, .learning-item');
        
        clickableElements.forEach(element => {
            element.addEventListener('touchstart', function() {
                this.style.transform = 'scale(0.98)';
                this.style.transition = 'transform 0.1s ease';
            }, { passive: true });
            
            element.addEventListener('touchend', function() {
                setTimeout(() => {
                    this.style.transform = '';
                    this.style.transition = '';
                }, 100);
            }, { passive: true });
            
            element.addEventListener('touchcancel', function() {
                this.style.transform = '';
                this.style.transition = '';
            }, { passive: true });
        });
    },

    // 初始化下拉刷新
    initPullToRefresh: function() {
        let startY = 0;
        let currentY = 0;
        let isPulling = false;
        let refreshThreshold = 80;
        
        const mainContent = document.querySelector('.main-content');
        if (!mainContent) return;
        
        // 创建下拉刷新指示器
        const refreshIndicator = document.createElement('div');
        refreshIndicator.className = 'pull-refresh-indicator';
        refreshIndicator.innerHTML = `
            <div class="refresh-spinner"></div>
            <span class="refresh-text">下拉刷新</span>
        `;
        refreshIndicator.style.cssText = `
            position: absolute;
            top: -60px;
            left: 0;
            right: 0;
            height: 60px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            color: #666;
            font-size: 14px;
            transition: transform 0.3s ease;
        `;
        
        mainContent.style.position = 'relative';
        mainContent.insertBefore(refreshIndicator, mainContent.firstChild);
        
        mainContent.addEventListener('touchstart', function(e) {
            if (window.scrollY === 0) {
                startY = e.touches[0].clientY;
                isPulling = true;
            }
        }, { passive: true });
        
        mainContent.addEventListener('touchmove', function(e) {
            if (!isPulling) return;
            
            currentY = e.touches[0].clientY;
            const deltaY = currentY - startY;
            
            if (deltaY > 0 && window.scrollY === 0) {
                e.preventDefault();
                const pullDistance = Math.min(deltaY * 0.5, refreshThreshold);
                refreshIndicator.style.transform = `translateY(${pullDistance}px)`;
                
                if (pullDistance >= refreshThreshold) {
                    refreshIndicator.querySelector('.refresh-text').textContent = '释放刷新';
                } else {
                    refreshIndicator.querySelector('.refresh-text').textContent = '下拉刷新';
                }
            }
        });
        
        mainContent.addEventListener('touchend', function(e) {
            if (!isPulling) return;
            
            const deltaY = currentY - startY;
            const pullDistance = Math.min(deltaY * 0.5, refreshThreshold);
            
            if (pullDistance >= refreshThreshold) {
                // 触发刷新
                refreshIndicator.querySelector('.refresh-text').textContent = '正在刷新...';
                refreshIndicator.style.transform = `translateY(${refreshThreshold}px)`;
                
                // 执行刷新逻辑
                this.performRefresh().finally(() => {
                    refreshIndicator.style.transform = '';
                    refreshIndicator.querySelector('.refresh-text').textContent = '下拉刷新';
                });
            } else {
                refreshIndicator.style.transform = '';
            }
            
            isPulling = false;
            startY = 0;
            currentY = 0;
        }.bind(this), { passive: true });
    },

    // 执行刷新
    performRefresh: function() {
        return new Promise((resolve) => {
            // 模拟刷新延迟
            setTimeout(() => {
                // 重新加载页面数据
                if (typeof loadPageData === 'function') {
                    loadPageData();
                }
                MobileUtils.showToast('刷新完成', 'success');
                resolve();
            }, 1000);
        });
    },

    // 初始化滚动优化
    initScrollOptimization: function() {
        let ticking = false;
        
        // 节流滚动事件
        const handleScroll = () => {
            if (!ticking) {
                requestAnimationFrame(() => {
                    this.updateScrollElements();
                    ticking = false;
                });
                ticking = true;
            }
        };
        
        window.addEventListener('scroll', handleScroll, { passive: true });
    },

    // 更新滚动相关元素
    updateScrollElements: function() {
        const scrollY = window.scrollY;
        const backToTop = document.getElementById('backToTop');
        
        // 显示/隐藏返回顶部按钮
        if (backToTop) {
            if (scrollY > 300) {
                backToTop.classList.add('show');
            } else {
                backToTop.classList.remove('show');
            }
        }
        
        // 头部阴影效果
        const header = document.querySelector('.mobile-header');
        if (header) {
            if (scrollY > 10) {
                header.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.15)';
            } else {
                header.style.boxShadow = '0 2px 8px rgba(0, 0, 0, 0.1)';
            }
        }
    }
};

// 初始化触摸交互功能
function initTouchInteractions() {
    TouchManager.init();
    
    // 绑定菜单按钮事件
    const menuBtn = document.getElementById('menuBtn');
    const menuOverlay = document.getElementById('menuOverlay');
    const menuClose = document.getElementById('menuClose');
    
    if (menuBtn) {
        menuBtn.addEventListener('click', function() {
            if (this.classList.contains('active')) {
                TouchManager.closeSideMenu();
            } else {
                TouchManager.openSideMenu();
            }
        });
    }
    
    if (menuOverlay) {
        menuOverlay.addEventListener('click', function() {
            TouchManager.closeSideMenu();
        });
    }
    
    if (menuClose) {
        menuClose.addEventListener('click', function() {
            TouchManager.closeSideMenu();
        });
    }
    
    // 绑定返回顶部按钮事件
    const backToTop = document.getElementById('backToTop');
    if (backToTop) {
        backToTop.addEventListener('click', function() {
            window.scrollTo({
                top: 0,
                behavior: 'smooth'
            });
        });
    }
    
    // 防止iOS Safari的橡皮筋效果
    document.addEventListener('touchmove', function(e) {
        if (e.scale !== 1) {
            e.preventDefault();
        }
    }, { passive: false });
    
    // 防止双击缩放
    let lastTouchEnd = 0;
    document.addEventListener('touchend', function(e) {
        const now = (new Date()).getTime();
        if (now - lastTouchEnd <= 300) {
            e.preventDefault();
        }
        lastTouchEnd = now;
    }, false);
}

// 导出给全局使用
window.TouchManager = TouchManager;
window.initTouchInteractions = initTouchInteractions;
