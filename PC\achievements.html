<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-个人中心</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/wangEditor.min.js" type="text/javascript" charset="utf-8"></script>
		<!-- 导航增强脚本 -->
		<script src="./js/nav-enhancement.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
		
		<style>
			body {
				background: url(img/background.jpg) no-repeat;
				background-size: cover;
				background-attachment: fixed;
			}
			
			/* 现代化导航栏美化 - 与页面红色主题统一，红色偏橙色 */
			.boxleft {
				background: white;
				border-radius: 16px;
				box-shadow: 0 8px 32px rgba(220, 53, 69, 0.08);
				overflow: hidden;
				border: 1px solid rgba(231, 76, 60, 0.05);
				position: relative;
				backdrop-filter: blur(20px);
			}
			
			.boxleft::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8));
				pointer-events: none;
				border-radius: 16px;
			}
			
			/* 导航栏顶部标题区域 - 红色偏橙色渐变 */
			.lefttopview {
				width: 100%;
				height: 65px;
				background: linear-gradient(135deg, #dc3545, #e74c3c, #ff6b35);
				background-size: 300% 300%;
				animation: gradientShift 6s ease infinite;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 18px;
				color: #FFFFFF;
				font-weight: 700;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif;
				position: relative;
				box-shadow: 0 4px 20px rgba(220, 53, 69, 0.25);
				letter-spacing: 1.5px;
				border-radius: 16px 16px 0 0;
			}
			
			/* 渐变动画效果 */
			@keyframes gradientShift {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.lefttopview img {
				width: 24px;
				height: 24px;
				display: block;
				margin-right: 12px;
				filter: brightness(0) invert(1) drop-shadow(0 0 6px rgba(255, 255, 255, 0.4));
			}
			
			.lefttopview label {
				display: flex;
				align-items: center;
				text-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
			}
			
			/* 导航菜单容器 */
			.leftitembox {
				background: transparent;
				padding: 20px 0;
				position: relative;
				z-index: 2;
			}
			
			.leftitembox::before {
				content: '';
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 85%;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.15), transparent);
			}
			
			/* 导航菜单项 - 现代化设计与图标 */
			.leftitem {
				height: auto;
				padding: 16px 24px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				font-size: 15px;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif;
				font-weight: 500;
				color: #4a5568;
				border-bottom: none;
				text-decoration: none;
				transition: color 0.2s ease, background-color 0.2s ease;
				position: relative;
				margin: 4px 16px;
				border-radius: 12px;
				background: transparent;
				overflow: hidden;
				min-height: 48px;
			}
			
			/* 为不同菜单项添加对应的SVG图标 */
			.leftitem::before {
				content: '';
				width: 20px;
				height: 20px;
				margin-right: 12px;
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				opacity: 0.7;
				transition: opacity 0.2s ease;
				flex-shrink: 0;
			}
			
			/* 个人信息图标 */
			.leftitem[href*="userinfo"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
			}
			
			/* 学生成绩统计图标 */
			.leftitem[href*="studentachievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z'/%3E%3C/svg%3E");
			}
			
			/* 学习记录统计图标 */
			.leftitem[href*="studentlearning"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
			}
			
			/* 考试管理图标 */
			.leftitem[href*="examination"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E");
			}
			
			/* 学习任务管理图标 */
			.leftitem[href*="learning"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
			}
			
			/* 成果展示图标 */
			.leftitem[href*="achievement"]::before,
			.leftitem.activeleftitem::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E");
			}
			
			/* 发布心声图标 */
			.leftitem[href*="voice"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z'/%3E%3C/svg%3E");
			}
			
			/* 菜单项悬浮态 - 完全无抖动 */
			.leftitem:hover {
				background: linear-gradient(135deg, rgba(220, 53, 69, 0.06), rgba(231, 76, 60, 0.08));
				color: #dc3545;
			}
			
			.leftitem:hover::before {
				opacity: 1;
			}
			
			/* 激活状态的菜单项 - 红色偏橙色渐变与白色图标 */
			.activeleftitem {
				font-weight: 700 !important;
				color: white !important;
				background: linear-gradient(135deg, #dc3545, #e74c3c, #ff6b35) !important;
				background-size: 300% 300%;
				animation: activeGradient 4s ease infinite;
				box-shadow: 0 8px 32px rgba(220, 53, 69, 0.3);
				border-radius: 12px;
			}
			
			@keyframes activeGradient {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.activeleftitem::before {
				opacity: 1 !important;
				filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
			}
			
			.activeleftitem::after {
				content: '';
				position: absolute;
				right: 16px;
				top: 50%;
				transform: translateY(-50%);
				width: 8px;
				height: 8px;
				background: white;
				border-radius: 50%;
				box-shadow: 0 0 12px rgba(255, 255, 255, 0.8);
				animation: activePulse 2s ease infinite;
			}
			
			@keyframes activePulse {
				0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
				50% { opacity: 0.6; transform: translateY(-50%) scale(1.4); }
			}
			
			/* 添加精美的分隔线效果 */
			.leftitem:not(:last-child):not(.activeleftitem) {
				border-bottom: 1px solid rgba(231, 76, 60, 0.06);
				margin-bottom: 3px;
			}
			
			.leftitem:not(:last-child):not(.activeleftitem)::after {
				content: '';
				position: absolute;
				bottom: -1px;
				left: 40px;
				right: 24px;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.15), transparent);
				opacity: 0.5;
			}
			
			/* 整体容器增强效果 */
			.contentview .boxleft {
				background: rgba(255, 255, 255, 0.95);
				border-radius: 16px;
				box-shadow: 0 8px 40px rgba(220, 53, 69, 0.08);
				border: 1px solid rgba(231, 76, 60, 0.05);
				overflow: hidden;
				position: relative;
				backdrop-filter: blur(20px);
			}
			
			.contentview .boxleft::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				height: 3px;
				background: linear-gradient(90deg, #dc3545, #e74c3c, #ff6b35);
				opacity: 0;
				transition: opacity 0.4s ease;
			}
			
			.contentview .boxleft:hover::before {
				opacity: 1;
			}
			
			/* 响应式优化 */
			@media (max-width: 768px) {
				.boxleft {
					border-radius: 12px;
					margin-bottom: 20px;
				}
				
				.lefttopview {
					height: 55px;
					font-size: 16px;
					letter-spacing: 1px;
				}
				
				.lefttopview img {
					width: 20px;
					height: 20px;
					margin-right: 10px;
				}
				
				.leftitem {
					padding: 14px 20px;
					font-size: 14px;
					margin: 3px 12px;
					min-height: 44px;
				}
				
				.leftitem::before {
					width: 18px;
					height: 18px;
					margin-right: 10px;
				}
			}
			
			/* 字体优化 - 思源黑体 */
			.lefttopview,
			.leftitem {
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Source Han Sans', 'Noto Sans CJK SC', 'Microsoft YaHei UI', 'Microsoft YaHei', '微软雅黑', sans-serif;
			}
			
			/* 增强的交互反馈 */
			.leftitem {
				cursor: pointer;
			}
			
			.leftitem:active {
				transform: scale(0.98);
				transition: all 0.1s ease;
			}
			
			.activeleftitem:active {
				transform: scale(1.02);
			}
			
			/* 为菜单项添加微妙的进入动画 */
			.leftitem {
				animation: slideIn 0.5s ease-out forwards;
				opacity: 0;
				transform: translateX(-20px);
			}
			
			.leftitem:nth-child(1) { animation-delay: 0.1s; }
			.leftitem:nth-child(2) { animation-delay: 0.2s; }
			.leftitem:nth-child(3) { animation-delay: 0.3s; }
			.leftitem:nth-child(4) { animation-delay: 0.4s; }
			.leftitem:nth-child(5) { animation-delay: 0.5s; }
			.leftitem:nth-child(6) { animation-delay: 0.6s; }
			.leftitem:nth-child(7) { animation-delay: 0.7s; }
			
			@keyframes slideIn {
				to {
					opacity: 1;
					transform: translateX(0);
				}
			}
			
			/* 现代化的成果列表样式 */
			.cgbox {
				background: #f8f9fa;
				border-radius: 12px;
				padding: 25px;
				margin-top: 20px;
			}
			
			/* 与页面整体风格一致的筛选区域 */
			.cgtop {
				background: linear-gradient(135deg, #d63031 0%, #e17055 100%);
				border-radius: 12px;
				padding: 25px;
				margin-bottom: 25px;
				box-shadow: 0 4px 15px rgba(214, 48, 49, 0.25);
				display: flex;
				/* flex-direction: column; */
				gap: 20px;
			}
			
			.filter-section {
				display: flex;
				align-items: center; /* 改回center对齐 */
				gap: 20px;
				/* 移除margin-bottom，由父容器的gap控制 */
			}
			
			.filter-label {
				color: #fff;
				font-weight: 600;
				font-size: 16px;
				text-shadow: 0 1px 2px rgba(0,0,0,0.2);
				min-width: 120px; /* 稍微增加宽度确保对齐 */
				flex-shrink: 0;
				display: flex;
				align-items: center;
			}
			
			.filter-select {
				padding: 8px 18px; /* 恢复上下padding */
				border: 2px solid rgba(255,255,255,0.3);
				border-radius: 8px;
				font-size: 14px;
				background: rgba(255,255,255,0.95);
				backdrop-filter: blur(10px);
				min-width: 220px;
				max-width: 220px;
				/* height: 1.8rem; 移除固定高度，让其自然计算 */
				cursor: pointer;
				transition: all 0.3s ease;
				box-shadow: 0 2px 8px rgba(0,0,0,0.1);
				color: #333;
				font-weight: 500;
				box-sizing: border-box;
				vertical-align: middle;
				/* 移除line-height: 1 */
				appearance: none;
				-webkit-appearance: none;
				-moz-appearance: none;
			}
			
			.filter-select option {
				padding: 8px;
				background: #fff;
				color: #333;
			}
			
			.filter-select:focus {
				border-color: rgba(255,255,255,0.8);
				box-shadow: 0 0 0 3px rgba(255,255,255,0.2);
				outline: none;
				background: #fff;
			}
			
			.search-section {
				display: flex;
				align-items: center; /* 改回center对齐 */
				gap: 15px;
			}
			
			.search-label {
				color: #fff;
				font-weight: 600;
				font-size: 16px;
				text-shadow: 0 1px 2px rgba(0,0,0,0.2);
				min-width: 120px; /* 与filter-label保持一致 */
				flex-shrink: 0;
				display: flex;
				align-items: center;
			}
			
			.search-input {
				padding: 8px 18px; /* 恢复上下padding */
				border: 2px solid rgba(255,255,255,0.3);
				border-radius: 8px;
				font-size: 14px;
				width: 280px;
				/* height: 1.8rem; 移除固定高度，让其自然计算 */
				background: rgba(255,255,255,0.95);
				backdrop-filter: blur(10px);
				transition: all 0.3s ease;
				box-shadow: 0 2px 8px rgba(0,0,0,0.1);
				color: #333;
				box-sizing: border-box;
				/* 移除line-height: 1 */
			}
			
			.search-input:focus {
				border-color: rgba(255,255,255,0.8);
				box-shadow: 0 0 0 3px rgba(255,255,255,0.2);
				outline: none;
				background: #fff;
			}
			
			.search-input::placeholder {
				color: #666;
				font-style: normal;
			}
			
			.search-btn {
				background: linear-gradient(135deg, #b2bec3 0%, #636e72 100%);
				color: #fff;
				border: none;
				padding: 8px 24px; /* 恢复上下padding */
				border-radius: 8px;
				font-size: 14px;
				font-weight: 600;
				cursor: pointer;
				transition: all 0.3s ease;
				box-shadow: 0 3px 10px rgba(99, 110, 114, 0.3);
				text-shadow: 0 1px 2px rgba(0,0,0,0.2);
				/* height: 1.8rem; 移除固定高度 */
				box-sizing: border-box;
				/* 移除line-height: 1 */
				/* 移除flex相关属性，让按钮自然对齐 */
			}
			
			.search-btn:hover {
				transform: translateY(-1px);
				box-shadow: 0 5px 15px rgba(99, 110, 114, 0.4);
				background: linear-gradient(135deg, #636e72, #2d3436);
			}
			
			.search-btn:active {
				transform: translateY(0);
			}
			
			/* 沉稳的表格样式 */
			.resources-table-container {
				background: #fff;
				border-radius: 12px;
				box-shadow: 0 4px 20px rgba(0,0,0,0.08);
				overflow: hidden;
				margin-bottom: 30px;
				border: 1px solid #e9ecef;
			}
			
			.resources-table {
				width: 100%;
				border-collapse: collapse;
				font-size: 14px;
			}
			
			.resources-table thead {
				background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
				color: #fff;
			}
			
			.resources-table th {
				padding: 16px 12px;
				text-align: center;
				font-weight: 600;
				text-shadow: 0 1px 2px rgba(0,0,0,0.2);
				border-bottom: 1px solid rgba(255,255,255,0.1);
				font-size: 13px;
			}
			
			.resources-table th:nth-child(1) { width: 100px; }
			.resources-table th:nth-child(2) { width: 300px; }
			.resources-table th:nth-child(3) { width: 200px; }
			.resources-table th:nth-child(4) { width: 150px; }
			.resources-table th:nth-child(5) { width: 250px; }
			
			.resources-table tbody tr {
				transition: all 0.3s ease;
				border-bottom: 1px solid #f0f0f0;
			}
			
			.resources-table tbody tr:hover {
				background: linear-gradient(135deg, #fff5f5 0%, #ffeaa7 20%, #fff 100%);
				transform: scale(1.001);
				box-shadow: 0 2px 8px rgba(214, 48, 49, 0.1);
			}
			
			.resources-table tbody tr:nth-child(even) {
				background: #fafbfc;
			}
			
			.resources-table tbody tr:nth-child(even):hover {
				background: linear-gradient(135deg, #fff5f5 0%, #ffeaa7 20%, #fafbfc 100%);
			}
			
			.resources-table td {
				padding: 16px 12px;
				vertical-align: middle;
				border-bottom: 1px solid #f0f0f0;
				text-align: center;
			}
			
			.table-cover {
				width: 60px;
				height: 45px;
				border-radius: 6px;
				object-fit: cover;
				background: linear-gradient(135deg, #636e72 0%, #2d3436 100%);
				display: flex;
				align-items: center;
				justify-content: center;
				color: #fff;
				font-size: 18px;
				font-weight: bold;
				margin: 0 auto;
				box-shadow: 0 2px 6px rgba(0,0,0,0.15);
			}
			
			.table-cover img {
				width: 100%;
				height: 100%;
				object-fit: cover;
				border-radius: 6px;
			}
			
			.table-title {
				font-weight: 600;
				color: #2d3436;
				line-height: 1.4;
				max-width: 290px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				font-size: 15px;
				margin: 0 auto;
				cursor: help;
				transition: color 0.3s ease;
			}
			
			.table-title:hover {
				color: #d63031; /* 悬浮时改变颜色 */
			}
			
			/* 完整显示的标签 */
			.table-stats {
				font-size: 13px; /* 增大字体 */
				color: #b2bec3;
				display: flex;
				flex-direction: column;
				gap: 3px;
				max-width: 140px; /* 调整到对应列宽 */
				margin: 0 auto; /* 居中显示 */
				align-items: center; /* 内容居中 */
			}
			
			.stat-item {
				display: flex;
				align-items: center;
				gap: 6px;
				white-space: nowrap;
				justify-content: center; /* 居中对齐 */
			}
			
			.table-actions {
				display: flex;
				gap: 8px; /* 增加按钮间距 */
				flex-wrap: nowrap; /* 强制不换行 */
				justify-content: center;
				align-items: center;
				min-width: 240px; /* 确保有足够空间 */
			}
			
			.action-btn {
				padding: 10px 16px; /* 增大按钮尺寸 */
				border-radius: 6px; /* 稍微增大圆角 */
				font-size: 13px; /* 增大字体 */
				cursor: pointer;
				transition: all 0.3s ease;
				border: none;
				font-weight: 600; /* 增强字重 */
				text-decoration: none;
				display: inline-flex;
				align-items: center;
				gap: 5px; /* 增加图标与文字间距 */
				white-space: nowrap;
				flex-shrink: 0; /* 防止按钮被压缩 */
				min-width: 70px; /* 设置最小宽度确保统一 */
				justify-content: center;
			}
			
			.action-btn.view {
				background: #74b9ff;
				color: #fff;
			}
			
			.action-btn.view:hover {
				transform: translateY(-1px);
				box-shadow: 0 3px 8px rgba(116, 185, 255, 0.4);
				background: #0984e3;
			}
			
			.action-btn.edit {
				background: #00b894;
				color: #fff;
			}
			
			.action-btn.edit:hover {
				transform: translateY(-1px);
				box-shadow: 0 3px 8px rgba(0, 184, 148, 0.4);
				background: #00a085;
			}
			
			.action-btn.delete {
				background: #e17055;
				color: #fff;
			}
			
			.action-btn.delete:hover {
				transform: translateY(-1px);
				box-shadow: 0 3px 8px rgba(225, 112, 85, 0.4);
				background: #d63031;
			}
			
			/* 空状态 */
			.empty-state {
				text-align: center;
				padding: 80px 20px;
				background: #fff;
				border-radius: 12px;
				box-shadow: 0 4px 20px rgba(0,0,0,0.08);
				margin-bottom: 30px;
				border: 1px solid #e9ecef;
			}
			
			.empty-icon {
				font-size: 64px;
				margin-bottom: 20px;
				opacity: 0.6;
				color: #b2bec3;
			}
			
			.empty-text {
				font-size: 18px;
				margin-bottom: 25px;
				color: #636e72;
				font-weight: 500;
			}
			
			.empty-btn {
				background: linear-gradient(135deg, #e17055, #d63031);
				color: #fff;
				border: none;
				padding: 15px 30px;
				border-radius: 8px;
				font-size: 16px;
				font-weight: 600;
				cursor: pointer;
				text-decoration: none;
				display: inline-block;
				transition: all 0.3s ease;
				box-shadow: 0 4px 15px rgba(214, 48, 49, 0.3);
			}
			
			.empty-btn:hover {
				transform: translateY(-2px);
				box-shadow: 0 6px 25px rgba(214, 48, 49, 0.4);
			}
			
			/* 分页样式优化 */
			.fybox {
				display: flex;
				justify-content: center;
				align-items: center;
				gap: 8px;
				padding: 25px 0;
				background: #fff;
				border-radius: 12px;
				box-shadow: 0 2px 10px rgba(0,0,0,0.05);
				border: 1px solid #e9ecef;
			}
			
			.fybox span, .fybox label {
				padding: 8px 12px;
				border-radius: 6px;
				cursor: pointer;
				transition: all 0.3s ease;
				border: 1px solid #ddd;
				background: #fff;
				color: #636e72;
				font-size: 14px;
				font-weight: 500;
				min-width: 40px;
				text-align: center;
			}
			
			.fybox label.actinum {
				background: linear-gradient(135deg, #636e72, #2d3436);
				color: #fff;
				border-color: transparent;
				box-shadow: 0 2px 8px rgba(99, 110, 114, 0.3);
			}
			
			.fybox span:hover, .fybox label:hover {
				background: #f8f9fa;
				border-color: #636e72;
				color: #2d3436;
				transform: translateY(-1px);
			}
			
			.fybox label.actinum:hover {
				background: linear-gradient(135deg, #636e72, #2d3436);
				color: #fff;
				transform: translateY(-1px);
				box-shadow: 0 4px 12px rgba(99, 110, 114, 0.4);
			}
			
			/* 响应式设计 */
			@media (max-width: 768px) {
				.cgtop {
					padding: 20px;
				}
				
				.filter-section, .search-section {
					flex-direction: column;
					align-items: stretch;
					gap: 15px;
				}
				
				.filter-label, .search-label {
					min-width: auto;
				}
				
				.filter-select, .search-input {
					width: 100%;
					max-width: none;
				}
				
				.resources-table-container {
					overflow-x: auto;
				}
				
				.resources-table {
					min-width: 800px;
				}
				
				.table-actions {
					flex-direction: column;
				}
			}
			
			/* 移除原有的表格样式 */
			.tabletop, .tablebox, .table {
				display: none;
			}
			
			/* 优化的分类标签显示 - 使用悬浮框而不是显示隐藏标签 */
			.table-meta {
				display: flex;
				gap: 4px;
				align-items: center;
				justify-content: center; /* 居中对齐 */
				flex-wrap: nowrap; /* 改为不换行 */
				max-width: 190px; /* 调整到对应列宽 */
				margin: 0 auto; /* 容器居中 */
				position: relative;
				cursor: pointer;
			}
			
			/* 只显示课程标签，其他标签通过悬浮框显示 */
			.table-meta .table-tag {
				background: #636e72;
				color: #fff;
				padding: 4px 10px; /* 稍微加大padding */
				border-radius: 12px; /* 更圆润的边角 */
				font-size: 13px; /* 稍微增大字体 */
				font-weight: 500;
				white-space: nowrap;
				flex-shrink: 0;
				transition: all 0.3s ease;
			}
			
			.table-meta .table-tag.type {
				background: #00b894;
				display: none; /* 完全隐藏类型标签 */
			}
			
			.table-meta .table-tag.attr {
				background: #e17055;
				display: none; /* 完全隐藏属性标签 */
			}
			
			.table-meta .table-tag.project {
				background: #0984e3; /* 课程标签使用蓝色 */
				position: relative;
			}
			
			/* 为课程标签添加提示图标，表示有更多信息 - 只有当有多个标签时 */
			.table-meta[data-tooltip] .table-tag.project::after {
				content: "⋯";
				margin-left: 4px;
				opacity: 0.7;
				font-size: 10px;
			}
			
			.table-meta[data-tooltip]:hover .table-tag.project::after {
				opacity: 0; /* 悬浮时隐藏提示 */
			}
			
			/* 悬浮提示框 - 只有当有data-tooltip属性时才显示 */
			.table-meta[data-tooltip]::before {
				content: attr(data-tooltip);
				position: absolute;
				top: -50px;
				left: 50%;
				transform: translateX(-50%);
				background: linear-gradient(135deg, #2d3436 0%, #636e72 100%);
				color: #fff;
				padding: 10px 14px;
				border-radius: 8px;
				font-size: 12px;
				white-space: nowrap;
				opacity: 0;
				visibility: hidden;
				transition: all 0.3s ease;
				z-index: 1000;
				box-shadow: 0 6px 20px rgba(0,0,0,0.25);
				font-weight: 500;
				max-width: 250px;
				text-align: center;
				line-height: 1.4;
				border: 1px solid rgba(255,255,255,0.1);
			}
			
			/* 悬浮提示框箭头 - 只有当有data-tooltip属性时才显示 */
			.table-meta[data-tooltip]::after {
				content: "";
				position: absolute;
				top: -12px;
				left: 50%;
				transform: translateX(-50%);
				width: 0;
				height: 0;
				border-left: 6px solid transparent;
				border-right: 6px solid transparent;
				border-top: 6px solid #2d3436;
				opacity: 0;
				visibility: hidden;
				transition: all 0.3s ease;
				z-index: 1000;
			}
			
			/* 悬浮时显示提示框 - 只有当有data-tooltip属性时 */
			.table-meta[data-tooltip]:hover::before,
			.table-meta[data-tooltip]:hover::after {
				opacity: 1;
				visibility: visible;
			}
			
			.table-meta:hover .table-tag.project {
				transform: scale(1.05);
			}
			
			.table-meta[data-tooltip]:hover .table-tag.project {
				transform: scale(1.05);
			}
		</style>
	</head>
	<body class="index">
		<div class="tcbox" id="tcbox">
			<div class="topviewsss">
				<a onclick="closetc()">关闭</a>
				<label id="pdfname"></label>
			</div>
			<div class="panel-body pdfbox">
				<a id="pdf" class="media" href=""></a>
			</div>
		</div>
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a href="studentachievement.html" class="leftitem">学生成绩统计</a>
						<a href="studentlearning.html" class="leftitem">学习记录统计</a>
						<a href="userexamination2.html" class="leftitem">考试管理</a>
						<a href="learningtasks.html" class="leftitem">学习任务管理</a>
						<a class="leftitem activeleftitem">成果展示</a>
						<a href="releaseyourvoice2.html" class="leftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="userinfotopdiv">
						<a class="acccccg">成果列表</a>
						<a href="uploadresources.html">发布资源</a>
					</div>
					<div class="cgbox">
						<div class="cgtop">
							<div class="filter-section">
								<label class="filter-label">📚 学科分类:</label>
								<select id="xueke" class="filter-select" onchange="showxkchange()">
								</select>
							</div>
							<div class="search-section">
								<label class="search-label">🔍 关键词:</label>
								<input id="ssssstr" class="search-input" placeholder="请输入标题、作者或关键词进行搜索..." />
								<button class="search-btn" onclick="ssssssubmit()">搜索</button>
							</div>
						</div>
						
						<!-- 资源表格布局 -->
						<div class="resources-table-container" id="resourcesTableContainer">
							<!-- 资源表格将动态生成在这里 -->
						</div>
						
						<!-- 空状态 -->
						<div class="empty-state" id="emptyState" style="display: none;">
							<div class="empty-icon">📁</div>
							<div class="empty-text">暂无资源内容</div>
							<a href="uploadresources.html" class="empty-btn">📤 发布第一个资源</a>
						</div>

						<div class="fybox" id="fyq">
							<span id="sy">首页</span>
							<span id="syy">上一页</span>
							<div class="num" id="num">
							</div>
							<span id="xyy">下一页</span>
							<span id="wy">尾页</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let datalist = null //列表数据
			let shuxinglist = null //属性列表
			let xuekelist = null //学科树

			let type = 'teacher' //teacher/student
			var mySwiper = new Swiper('.museumboxitembottoml .swiper', {
				autoplay: false,
				loop: true,
				pagination: {
					el: '.museumboxitembottoml .swiper-pagination',
					clickable: true
				}
			})
			let upid = null
			
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()

				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getclassid()
				getshuxin()
				getxueke()
				getfooterlink()
			})

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			function getshuxin() { //获取属性
				$.ajax({
					url: baseurl + "/attributes",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log('属性', res.data)
							shuxinglist = res.data
							let html = "<option value=0>请选择属性</option>"
							shuxinglist.map((item) => {
								html += '<option value="' + item.id + '">' + item.name + '</option>'
							})
							$("#editsx").html(html)
						}
					}
				})
			}

			function getxueke() { //获取学科树
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							xuekelist = res.data
							let xuekehtml = "<option value=0>请选择学科</option>"
							xuekelist.map((item) => {
								xuekehtml += '<option value="' + item.id + '">' + item.name + '</option>'
							})
							$("#editxk").html(xuekehtml)
							$("#xueke").html(xuekehtml)
						}
					}
				})
			}

			function editthis(ttt) { //点击修改事件
				let editId = $(ttt).attr("data-editid");
				let metaId = $(ttt).attr("data-id");
				
				// 跳转到编辑页面，传递资源ID
				window.location.href = `edit-resource.html?id=${editId}&metaId=${metaId}`;
			}

			function deletethis(ttt) { //点击删除事件
				let id = $(ttt).attr("data-id");
				let name = $(ttt).attr("data-name");
				
				if (confirm(`确定要删除资源"${name}"吗？删除后无法恢复。`)) {
					$.ajax({
						url: baseurl + "/course/" + id,
						type: 'DELETE',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						dataType: 'json',
						success: (res) => {
							if (res.code == '200') {
								cocoMessage.success(1000, "删除成功！");
								getlist(upid); // 重新加载列表
							} else {
								cocoMessage.error(1000, "删除失败！");
							}
						},
						error: () => {
							cocoMessage.error(1000, "删除失败！");
						}
					});
				}
			}

			function closetc() {
				$("#tcbox").hide()
			}

			function getclassid() {
				$.ajax({
					url: baseurl + "/category/teaching",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							upid = res.data[0].id
							getlist(res.data[0].id)
						}
					}
				})
			}

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}
			let xkididid = null
			let titlesssss = null

			function showxkchange() {
				if ($("#xueke").val() == '0') {
					xkididid = null
				} else {
					xkididid = $("#xueke").val()
				}
				getlist(upid)
			}

			function ssssssubmit() {
				if ($("#ssssstr").val()) {
					titlesssss = $("#ssssstr").val()
				} else {
					titlesssss = null
				}

				getlist(upid)
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
			let pagesize = 10
			let pageindex = 1
			let pages = 1

			function getlist(id) {
				$.ajax({
					url: baseurl + "/course",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						projectId: xkididid,
						title: titlesssss,
						categoryId: id,
						creator: JSON.parse(userinfo).userAuth.identifier,
						pageSize: pagesize,
						pageNum: pageindex
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							console.log(res.data)
							pages = res.data.pages
							datalist = res.data.list
							
							if (res.data.list.length === 0) {
								$("#resourcesTableContainer").hide();
								$("#emptyState").show();
								$("#fyq").hide();
								return;
							}
							
							$("#emptyState").hide();
							$("#resourcesTableContainer").show();
							
							// 构建完整的表格HTML
							let html = `
								<table class="resources-table">
									<thead>
										<tr>
											<th>封面</th>
											<th>标题</th>
											<th>分类/标签</th>
											<th>统计</th>
											<th>操作</th>
										</tr>
									</thead>
									<tbody>
							`;
							
							res.data.list.forEach((item) => {
								// 获取文件类型图标
								function getFileIcon(type) {
									const iconMap = {
										'pptx': '📊', 'ppt': '📊',
										'docx': '📄', 'doc': '📄',
										'pdf': '📋', 'mp4': '🎥',
										'mp3': '🎵'
									};
									return iconMap[type] || '📎';
								}
								
								// 构建表格行HTML
								html += `
									<tr>
										<td>
											<div class="table-cover">
												${item.cmsResourcesCourseMeta && item.cmsResourcesCourseMeta.coverPath ? 
													`<img src="${baseurl}${item.cmsResourcesCourseMeta.coverPath}" alt="封面" onerror="this.style.display='none'; this.parentNode.innerHTML='${getFileIcon(item.attachType)}'" />` :
													getFileIcon(item.attachType)
												}
											</div>
										</td>
										<td>
											<div class="table-title" title="${item.title}">${item.title}</div>
										</td>
										<td>
											<div class="table-meta" 
											     title="${[
												item.project ? item.project.name : '',
												item.cmsResourcesType ? item.cmsResourcesType.name : '',
												item.cmsResourcesAttributes ? item.cmsResourcesAttributes.name : ''
											].filter(Boolean).join(' • ')}"
											     ${(() => {
												const tags = [
													item.project ? `📚 ${item.project.name}` : '',
													item.cmsResourcesType ? `📋 ${item.cmsResourcesType.name}` : '',
													item.cmsResourcesAttributes ? `🏷️ ${item.cmsResourcesAttributes.name}` : ''
												].filter(Boolean);
												return tags.length > 1 ? `data-tooltip="${tags.join(' | ')}"` : '';
											})()}>
												${item.project ? `<span class="table-tag project">${item.project.name}</span>` : ''}
												${item.cmsResourcesType ? `<span class="table-tag type">${item.cmsResourcesType.name}</span>` : ''}
												${item.cmsResourcesAttributes ? `<span class="table-tag attr">${item.cmsResourcesAttributes.name}</span>` : ''}
											</div>
										</td>
										<td>
											<div class="table-stats">
												<div class="stat-item">
													<span>👁</span>
													<span>${item.view || 0} 浏览</span>
												</div>
												<div class="stat-item">
													<span>📅</span>
													<span>${setDate(item.createdAt)}</span>
												</div>
											</div>
										</td>
										<td>
											<div class="table-actions">
												<button class="action-btn view" onclick="showshow(this)" 
														data-type="${item.attachType}" data-id="${item.metaId}">
													👁 查看
												</button>
												<button class="action-btn edit" onclick="editthis(this)" 
														data-editid="${item.id}" data-id="${item.metaId}" data-name="${item.title}">
													✏️ 编辑
												</button>
												<button class="action-btn delete" onclick="deletethis(this)" 
														data-id="${item.id}" data-name="${item.title}">
													🗑 删除
												</button>
											</div>
										</td>
									</tr>
								`;
							});
							
							html += `
									</tbody>
								</table>
							`;
							
							$("#resourcesTableContainer").html(html);

							// 分页逻辑保持不变
							if (pages > 1) {
								let numhtml = ""
								//当页数大于5
								if (pages > 5) {
									if (pageindex <= 5) { //当前页码小于5的时候右侧不变
										for (let a = 1; a <= 10; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
										numhtml += '<label onclick="getnewlist(11)">...</label>'
									} else if (5 < pageindex & pageindex < pages - 5) {
										numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) - 5) +
											')">...</label>'
										for (let a = pageindex - 4; a <= pageindex + 4; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
										numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) + 5) +
											')">...</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + (pages - 11) +
											')">...</label>'
										for (let a = pages - 10; a <= pages; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
											}
										}
									}
								} else {
									for (let a = 1; a <= pages; a++) {
										if (pageindex == a) {
											numhtml += '<label class="actinum" onclick="getnewlist(' + a +
												')">' + a +
												'</label>'
										} else {
											numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
										}
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
						}
					}
				})
			}

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d
			}

			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是第最后一页了！")
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！")
					}
				} else {
					pageindex = index
					getlist(upid)
				}
			}

			function showshow(cg) {
				if ($(cg).attr("data-type") == "pdf") {
					$.ajax({
						url: baseurl + "/course/meta/" + $(cg).attr("data-id"),
						type: 'GET',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						dataType: 'json',
						success: (res) => {
							if (res.code == '200') {
								$("#pdfname").html(res.data.cmsResourcesCourseMetaList[0].attachName)
								$("#tcbox").show()
								$("#pdf").attr("href", baseurl + res.data.cmsResourcesCourseMetaList[0].attachPath)
								$('a.media').media()
							}
						}
					})
				} else {
					window.open("onlinelearning4.html?id=" + $(cg).attr("data-id"))
				}
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>

