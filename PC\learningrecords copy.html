<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-个人中心</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/wangEditor.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body class="index">
		<div class="tcbox" id="tcbox">
			
			<div class="topviewsss">
				<a onclick="closetc()">关闭</a>
				<label id="pdfname"></label>
			</div>
			<div class="panel-body pdfbox">
			    <a id="pdf" class="media" href=""></a>  
			</div>
		</div>
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a href="studentasks.html" class="leftitem">学习任务</a>
						<a class="leftitem activeleftitem">学习路径</a>
						<a href="achievement.html" class="leftitem">考试成绩</a>
						<a href="releaseyourvoice.html" class="leftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="righttopview2" id="flbox">
					</div>
					<div class="xxjlselectbox" id="xkselect">
						<select id="xueke" onchange="xuekechange()">
						</select>
					</div>
					<div class="listbox" id="listview">

					</div>
					<div class="fybox" id="fyq">
						<span id="sy">首页</span>
						<span id="syy">上一页</span>
						<div class="num" id="num">
						</div>
						<span id="xyy">下一页</span>
						<span id="wy">尾页</span>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let pageindex = 1
			let pagesize = 20
			let pages = 1

			let classid = null //分类ID
			let xkid = null //学科ID
			let userinfo = sessionStorage.getItem("userinfo")
			

			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getfooterlink()
				getfllist()
				getallxklist()
			})
			function xuekechange(){
				xkid = $("#xueke").val()
				getjllist()
			}
			function getallxklist(){
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let xuekehtml = "<option value=''>请选择学科</option>"
							res.data.map((item)=>{
								xuekehtml+='<option value="'+item.id+'">'+item.name+'</option>'
							})
							$("#xueke").html(xuekehtml)
						}
					}
				})
			}
			function getfllist() {
				$.ajax({
					url: baseurl + "/category/xinshengcategory",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data)
							let html = ''
							classid = res.data[0].id
							res.data.map((item,index) => {
								if(index == 0){
									html += '<label class="accccc" onclick="selectfl(this)" data-id="' + item.id + '">' + item
										.name + '</label>'
									if(item.name=='在线学习'){
										$("#xkselect").attr("style","display: flex")
									}else{
										$("#xkselect").hide()
									}
								}else{
									html += '<label onclick="selectfl(this)" data-id="' + item.id + '">' + item
										.name + '</label>'
								}
							})
							$("#flbox").html(html)
							getjllist()
						}
					}
				})
			}

			function selectfl(item) {
				let alll = $("#flbox label")
				for (let i = 0; i < alll.length; i++) {
					$(alll[i]).attr("class", "")
				}
				$(item).attr("class", 'accccc')
				classid = $(item).attr("data-id")
				pageindex = 1
				xkid = null
				getjllist()
				if($(item).html()=='在线学习'){
					$("#xkselect").attr("style","display: flex")
				}else{
					$("#xkselect").hide()
				}
			}

			function closetc(){
				$("#tcbox").hide()
				tiaozhuan()
			}

			function showpdf(pdf){
				$.ajax({
					url: baseurl + "/course/meta/"+$(pdf).attr("data-id"),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							$("#pdfname").html(res.data.cmsResourcesCourseMetaList[0].attachName)
							$("#tcbox").show()
							$("#pdf").attr("href",baseurl + res.data.cmsResourcesCourseMetaList[0].attachPath)
							$('a.media').media()
							time1 = Date.now()
							xkidsss = res.data.projectId
							infoid = $(pdf).attr("data-id")
							pflid = res.data.cmsCategory.parentId
						}
					}
				})
			}
			let time1 = null
			let pflid = null
			let xkidsss = null
			let infoid = null
			function tiaozhuan() {
				if(JSON.parse(userinfo).roleName == '学生'){
					let time2 = Date.now()
					let value = time2 - time1
					var days = parseInt(value / (1000 * 60 * 60 * 24))
					var hours = parseInt((value % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
					var minutes = parseInt((value % (1000 * 60 * 60)) / (1000 * 60))
					var seconds = Math.floor((value % (1000 * 60)) / 1000)
					let json = {
						infoId: infoid, //信息id
						categoryId: pflid, //所属分类id
						totalInfo: "1", //总时长，总页数
						positioning: days+"天"+hours+"时"+minutes+"分"+seconds+"秒", //学习了多久，多少页    
						progress: "", //进度 百分比
						type: '在线学习2',
						learningTime: value,
						sectionId: xkidsss//学科ID
					}
					window.localStorage.setItem("jilu",JSON.stringify(json))
					if(window.localStorage.getItem("jilu")){
						$.ajax({
							url: baseurl + "/study/record/add",
							type: 'post',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: window.localStorage.getItem("jilu"),
							dataType: 'json',
							success: (res) => {
								window.localStorage.clear()
								getjllist()
							}
						})
					}
				}
			}
			function getjllist() {
				$.ajax({
					url: baseurl + "/study/record/studenttecord",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: pageindex,
						pageSize: pagesize,
						categoryId: classid,
						sectionId: xkid
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data.list)
							let html = ''
							res.data.list.map((item) => {
								if (item.type == "心声社区") {
									html += '<div class="jlitem1 jlitem2">' +
										'<div class="cc cc2">' +
										'<div>' + item.postInfo.title + '</div>' +
										'<div>' +
										'<label>' + item.postInfo.cmsCategoryList[0].name
										if(item.postInfo.communitypropertyType){
											html+=" | "+item.postInfo.communitypropertyType
										}
										if(item.postInfo.themename){
											html+=" | "+item.postInfo.themename
										}
										html+='</label>' +
										'<label>' + setDate(item.createdAt) + '</label>' +
										'</div>' +
										'<div>' + item.positioning + '</div>' +
										'</div>' +
										'<div class="rr jx">' +
										'<label></label>' +
										'<a href="communitydetail.html?id='+item.postInfo.id+'">继续学习</a>' +
										'</div>' +
										'</div>'
								} else if (item.type == "主席足迹") {
									html += '<div class="jlitem1 jlitem2">' +
										'<div class="cc cc2">' +
										'<div>' + item.cmsFootprint.title + '</div>' +
										'<div>' +
										'<label>' + item.cmsFootprint.cmsCategoryList[0].name + '</label>' +
										'<label>' + setDate(item.createdAt) + '</label>' +
										'</div>' +
										'<div>' + item.positioning + '</div>' +
										'</div>' +
										'<div class="rr jx">' +
										'<label></label>' +
										'<a href="footprintcontent.html?id='+item.cmsFootprint.id+'">继续学习</a>' +
										'</div>' +
										'</div>'
								} else if (item.type == "虚仿实验空间") {
									html += '<div class="jlitem1">' +
										'<img src="'+baseurl + item.cmsVirtualSimulation.covertPath+'" />' +
										'<div class="cc">' +
										'<div>' + item.cmsVirtualSimulation.titleName + '</div>' +
										'<div>' +
										'<label></label>' +
										'<label>' + setDate(item.createdAt) + '</label>' +
										'</div>' +
										'<div>' + item.positioning + '</div>' +
										'</div>' +
										'<div class="rr jx">' +
										'<label></label>' +
										'<a target="_blank" href="'+item.cmsVirtualSimulation.linkAdress+'">继续学习</a>' +
										'</div>' +
										'</div>'
								} else if (item.type == "在线学习") {
									html += '<div class="jlitem1">' +
										'<img src="'+baseurl + item.resourcesCourse.attachType+'" />' +
										'<div class="cc">' +
										'<div>' + item.resourcesCourse.title + '</div>' +
										'<div>' +
										'<label>' + item.category.name
									if (item.resourcesCourse.cmsResourcesAttributes) {
										html += ' | ' + item.resourcesCourse.cmsResourcesAttributes.name
									}
									if (item.resourcesCourse.project) {
										html += ' | ' + item.resourcesCourse.project.name
									}
									
									html += '</label>' +
										'<label>' + setDate(item.createdAt) + '</label>' +
										'</div>' +
										'<div>' + item.positioning + '</div>' +
										'</div>' +
										'<div class="rr jx">' +
										'<label></label>' +
										'<a href="onlinelearning4.html?id='+item.infoId+'">继续学习</a>' +
										'</div>' +
										'</div>'
								} else if(item.type == "在线学习2"){
									html += '<div class="jlitem1">' +
										'<img src="img/zy.png" />' +
										'<div class="cc">' +
										'<div>' + item.resourcesCourse.title + '</div>' +
										'<div>' +
										'<label>' + item.category.name
									if (item.resourcesCourse.cmsResourcesAttributes) {
										html += ' | ' + item.resourcesCourse.cmsResourcesAttributes.name
									}
									if (item.resourcesCourse.project) {
										html += ' | ' + item.resourcesCourse.project.name
									}
									
									html += '</label>' +
										'<label>' + setDate(item.createdAt) + '</label>' +
										'</div>' +
										'<div>' + item.positioning + '</div>' +
										'</div>' +
										'<div class="rr jx">' +
										'<label></label>' +
										'<a onclick="showpdf(this)" data-id="'+item.infoId+'">继续学习</a>' +
										'</div>' +
										'</div>'
								} else if(item.type == "红色书籍"){
									html += '<div class="jlitem1">' +
										'<img src="img/hsbook.png" />' +
										'<div class="cc">' +
										'<div>' + (item.resourcesCourse ? item.resourcesCourse.title : '红色书籍') + '</div>' +
										'<div>' +
										'<label>' + (item.category ? item.category.name : '红色书籍')
									if (item.resourcesCourse && item.resourcesCourse.cmsResourcesAttributes) {
										html += ' | ' + item.resourcesCourse.cmsResourcesAttributes.name
									}
									if (item.resourcesCourse && item.resourcesCourse.project) {
										html += ' | ' + item.resourcesCourse.project.name
									}
									
									html += '</label>' +
										'<label>' + setDate(item.createdAt) + '</label>' +
										'</div>' +
										'<div>已阅读到第' + item.positioning + '页 / 共' + item.totalInfo + '页</div>' +
										'</div>' +
										'<div class="rr jx">' +
										'<label></label>' +
										'<a href="onlinelearning5.html?id='+item.infoId+'">继续阅读</a>' +
										'</div>' +
										'</div>'
								}
							})
							if(res.data.list.length == 0){
								$("#listview").html("<div class='mysj'>没有查询到记录...</div>")
							}else{
								$("#listview").html(html)
							}
							

							pages = res.data.pages
							if (pages > 1) {
								let numhtml = ""
								for (let a = 1; a <= pages; a++) {
									if (pageindex == a) {
										numhtml += '<label class="actinum" onclick="getnewlist(' + a + ')">' + a +
											'</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
						}
					}
				})
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是第最后一页了！")
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！")
					}
				} else {
					pageindex = index
					getjllist()
				}
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 						.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '年' + MM + '月' + d + '日 ' + h + ':' + m
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
