import os
import pandas as pd
from datetime import datetime

# 打印当前工作目录
cwd = os.getcwd()
print(f"当前工作目录: {cwd}")

# 指定输出的绝对路径
output_dir = os.path.join(cwd, "output")
os.makedirs(output_dir, exist_ok=True)

# 创建简单的数据
data = {'序号': [1, 2, 3], '名称': ['测试A', '测试B', '测试C']}
df = pd.DataFrame(data)

# 保存到Excel
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
file_path = os.path.join(output_dir, f"测试_{timestamp}.xlsx")

print(f"文件路径: {file_path}")

df.to_excel(file_path, index=False)
print(f"文件已保存")

# 列出目录内容
print("\n目录内容:")
print(os.listdir(output_dir)) 