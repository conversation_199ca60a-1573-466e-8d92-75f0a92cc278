/**
 * 文件查看器 - 支持多种文件格式的在线查看
 * 支持格式：视频(MP4)、音频(MP3)、PPT/PPTX、DOC/DOCX、PDF等
 */

/**
 * FileViewer - 通用文件预览组件
 * 支持文档、视频、音频、PDF等多种格式
 */

// 记录资源观看活动的通用函数
function recordResourceView(fileType) {
    // 检查是否在onlinelearning4.html页面中
    if (!window.location.pathname.includes('onlinelearning4.html')) {
        return; // 不在目标页面，不执行记录
    }

    // 检查全局变量，确保必要的变量存在
    if (typeof window.looklist === 'undefined') {
        window.looklist = [];
    }
    
    if (typeof window.lookcount === 'undefined') {
        window.lookcount = 0;
    }
    
    // 尝试记录当前浏览的资源
    try {
        // 发送自定义事件，通知页面记录学习活动
        const recordEvent = new CustomEvent('recordResourceView', {
            detail: {
                fileType: fileType,
                timestamp: new Date().getTime()
            }
        });
        window.dispatchEvent(recordEvent);
        
        console.log('资源浏览记录已触发:', fileType);
    } catch (e) {
        console.error('记录资源浏览失败:', e);
    }
}

// FileViewer全局配置与设置
window.fileViewerSettings = {
    previewContainerSelector: '.preview-container',
    baseUrl: '',
    debug: false,
    onload: null
};

// 文件格式与处理函数的映射
const FileViewerHandlers = {
    // 视频格式
    'mp4': showVideo,
    
    // 音频格式
    'mp3': showAudio,
    
    // 文档格式
    'pdf': showPDF,
    'doc': showDOCFile,  // 为DOC格式使用专门的处理函数
    'docx': showDOCX,
    
    // 演示文稿格式
    'ppt': showPPT,
    'pptx': showPPT
};

/**
 * 初始化文件查看器
 * @param {Object} options 配置选项
 */
function initFileViewer(options = {}) {
    const defaultOptions = {
        containerSelector: '#fileViewContainer', // 容器选择器
        previewContainerSelector: '.kjview',     // 预览容器选择器
        baseUrl: window.baseurl || ''            // 基础URL
    };
    
    const settings = Object.assign({}, defaultOptions, options);
    window.fileViewerSettings = settings;
    
    // 添加下载按钮的样式
    const style = document.createElement('style');
    style.textContent = `
        .file-download-btn {
            display: inline-block;
            padding: 8px 16px;
            background-color: #A65D57;
            color: white;
            border-radius: 4px;
            text-align: center;
            cursor: pointer;
            margin: 10px 0;
            text-decoration: none;
            font-weight: 500;
            transition: background-color 0.3s;
        }
        .file-download-btn:hover {
            background-color: #8A4D47;
        }
        .audio-player {
            width: 100%;
            margin: 20px 0;
            background: #f9f9f9;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }
        .audio-player .controls {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: 10px;
        }
        .audio-player .play-btn {
            background: #A65D57;
            color: white;
            border: none;
            border-radius: 50%;
            width: 50px;
            height: 50px;
            font-size: 20px;
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            transition: background-color 0.3s;
        }
        .audio-player .play-btn:hover {
            background: #8A4D47;
        }
        .audio-player .progress-container {
            flex-grow: 1;
            margin: 0 20px;
            background: #ddd;
            border-radius: 5px;
            height: 8px;
            position: relative;
            cursor: pointer;
        }
        .audio-player .progress-bar {
            background: #A65D57;
            border-radius: 5px;
            height: 100%;
            width: 0;
            transition: width 0.1s;
        }
        .audio-player .time {
            font-size: 14px;
            color: #666;
        }
        .audio-player .audio-title {
            font-size: 16px;
            color: #333;
            text-align: center;
            margin-bottom: 15px;
        }
        .doc-container {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: auto;
            background: white;
        }
        .doc-toolbar {
            padding: 10px;
            background: #f5f5f5;
            border-radius: 8px 8px 0 0;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        .loading-indicator {
            text-align: center;
            padding: 20px;
            font-size: 16px;
            color: #666;
        }
        .file-icon {
            font-size: 14px;
            margin-right: 5px;
        }
        .error-message {
            padding: 20px;
            color: #721c24;
            background-color: #f8d7da;
            border: 1px solid #f5c6cb;
            border-radius: 8px;
            margin: 10px 0;
            text-align: center;
        }
        @media (max-width: 768px) {
            .audio-player {
                padding: 15px;
            }
            .audio-player .play-btn {
                width: 40px;
                height: 40px;
                font-size: 16px;
            }
            .doc-container {
                height: 400px;
            }
        }
    `;
    document.head.appendChild(style);
    
    console.log('文件查看器初始化完成');
}

/**
 * 根据文件类型显示文件内容
 * @param {String} fileUrl 文件URL
 * @param {String} fileType 文件类型
 * @param {String} fileName 文件名
 * @param {Object} metadata 文件元数据
 */
function displayFileContent(fileUrl, fileType, fileName, metadata = {}) {
    const handler = FileViewerHandlers[fileType.toLowerCase()];
    
    if (handler) {
        handler(fileUrl, fileName, metadata);
    } else {
        // 对于不支持的文件类型，提供下载链接
        showDownloadLink(fileUrl, fileName, fileType);
    }
}

/**
 * 显示视频
 * @param {String} fileUrl 文件URL
 * @param {String} fileName 文件名
 */
function showVideo(fileUrl, fileName) {
    const settings = window.fileViewerSettings;
    const container = document.querySelector(settings.previewContainerSelector);
    
    // 记录视频资源浏览
    recordResourceView('mp4');
    
    // 清空容器，插入视频播放器（不再添加下载按钮）
    container.innerHTML = `
        <div class="video-container">
            <video autoplay controls controlsList="nodownload" style="width: 100%; height: 100%; object-fit: contain; background: #000; display: block;">
                <source src="${fileUrl}" type="video/mp4">
                您的浏览器不支持视频播放。
            </video>
        </div>
    `;
}

/**
 * 显示音频
 * @param {String} fileUrl 文件URL
 * @param {String} fileName 文件名
 */
function showAudio(fileUrl, fileName) {
    const settings = window.fileViewerSettings;
    const container = document.querySelector(settings.previewContainerSelector);
    
    // 记录音频资源浏览
    recordResourceView('mp3');
    
    // 隐藏其他元素
    hideElement('#ppt');
    hideElement('#pptbox');
    hideElement('#video');
    hideElement('#videobox');
    
    // 创建音频播放器容器，包裹在.video-container内
    const audioPlayerHTML = `
        <div class="video-container">
        <div class="doc-toolbar">
            <div>${fileName}</div>
        </div>
        <div class="audio-player">
            <div class="controls">
                <button class="play-btn" id="audioPlayBtn">▶</button>
                <div class="progress-container" id="audioProgress">
                    <div class="progress-bar" id="audioProgressBar"></div>
                </div>
                <div class="time" id="audioTime">00:00 / 00:00</div>
                </div>
            </div>
        </div>
    `;
    
    container.innerHTML = audioPlayerHTML;
    
    // 初始化Howler音频播放器
    const sound = new Howl({
        src: [fileUrl],
        html5: true,
        preload: true,
        format: ['mp3']
    });
    
    const playBtn = document.getElementById('audioPlayBtn');
    const progressBar = document.getElementById('audioProgressBar');
    const progressContainer = document.getElementById('audioProgress');
    const timeDisplay = document.getElementById('audioTime');
    
    // 播放暂停控制
    playBtn.addEventListener('click', function() {
        if (sound.playing()) {
            sound.pause();
            playBtn.innerHTML = '▶';
        } else {
            sound.play();
            playBtn.innerHTML = '⏸';
        }
    });
    
    // 点击进度条更新进度
    progressContainer.addEventListener('click', function(e) {
        const percent = e.offsetX / progressContainer.offsetWidth;
        sound.seek(percent * sound.duration());
    });
    
    // 更新进度条和时间显示
    let interval;
    sound.on('play', function() {
        interval = setInterval(updateProgress, 100);
    });
    
    sound.on('pause', function() {
        clearInterval(interval);
    });
    
    sound.on('end', function() {
        clearInterval(interval);
        playBtn.innerHTML = '▶';
        progressBar.style.width = '0%';
        timeDisplay.textContent = formatTime(0) + ' / ' + formatTime(sound.duration());
    });
    
    sound.on('load', function() {
        timeDisplay.textContent = formatTime(0) + ' / ' + formatTime(sound.duration());
    });
    
    function updateProgress() {
        const seek = sound.seek() || 0;
        const percent = (seek / sound.duration()) * 100;
        progressBar.style.width = percent + '%';
        timeDisplay.textContent = formatTime(seek) + ' / ' + formatTime(sound.duration());
    }
    
    function formatTime(seconds) {
        const minutes = Math.floor(seconds / 60);
        const remainingSeconds = Math.floor(seconds % 60);
        return (minutes < 10 ? '0' : '') + minutes + ':' + (remainingSeconds < 10 ? '0' : '') + remainingSeconds;
    }
}

/**
 * 显示PDF文件
 * @param {String} fileUrl 文件URL
 * @param {String} fileName 文件名
 */
function showPDF(fileUrl, fileName) {
    const settings = window.fileViewerSettings;
    const container = document.querySelector(settings.previewContainerSelector);
    
    // 记录PDF资源浏览
    recordResourceView('pdf');
    
    // 隐藏其他元素
    hideElement('#ppt');
    hideElement('#pptbox');
    hideElement('#video');
    hideElement('#videobox');
    
    // 使用与其他文件类型一致的样式结构
    container.innerHTML = `
        <div class="doc-toolbar">
            <div>${fileName}</div>
        </div>
        <div id="pdfContainer" class="doc-container">
            <div class="loading-indicator">PDF文档加载中，请稍候...</div>
        </div>
    `;
    
    // PDF查看使用jQuery.media
    const pdfEmbed = document.createElement('a');
    pdfEmbed.className = 'media';
    pdfEmbed.href = fileUrl;
    pdfEmbed.style.display = 'none';
    
    const pdfContainer = document.getElementById('pdfContainer');
    pdfContainer.appendChild(pdfEmbed);
    
    // 使用jQuery媒体初始化PDF查看器
    $(pdfEmbed).media({
        width: '100%',
        height: '100%',
        autoplay: true,
        src: fileUrl,
        bgColor: 'transparent',
        params: { 
            allowFullScreen: true,
            allowScriptAccess: 'always'
        },
        caption: false,
        wmode: 'transparent'
    });
    
    // 移除加载指示器
    setTimeout(() => {
        const loadingIndicator = pdfContainer.querySelector('.loading-indicator');
        if (loadingIndicator) {
            loadingIndicator.remove();
        }
    }, 1500);
}

/**
 * 显示DOC文件 - 仅提供下载链接，不尝试在线渲染
 * @param {String} fileUrl 文件URL
 * @param {String} fileName 文件名
 */
function showDOCFile(fileUrl, fileName) {
    const settings = window.fileViewerSettings;
    const container = document.querySelector(settings.previewContainerSelector);
    
    // 记录DOC资源浏览
    recordResourceView('doc');
    
    // 隐藏其他元素
    hideElement('#ppt');
    hideElement('#pptbox');
    hideElement('#video');
    hideElement('#videobox');
    
    // 创建DOC信息容器，包裹在.video-container内
    container.innerHTML = `
        <div class="video-container">
        <div class="doc-toolbar">
            <div>${fileName}</div>
        </div>
        <div style="padding: 30px; background: white; border: 1px solid #ddd; border-top: none; border-radius: 0 0 8px 8px; text-align: center;">
            <div style="margin-bottom: 20px;">
                <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#2b5797" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                    <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                    <polyline points="14 2 14 8 20 8"></polyline>
                    <path d="M16 13H8"></path>
                    <path d="M16 17H8"></path>
                    <path d="M10 9H8"></path>
                </svg>
            </div>
            <h3 style="color: #333; margin-bottom: 15px;">Word文档 (.doc 格式)</h3>
            <p style="color: #666; margin-bottom: 20px;">传统DOC格式不支持在线预览，请下载后使用Microsoft Word或WPS等软件查看</p>
            <a href="${fileUrl}" class="file-download-btn" download="${fileName}">
                <span class="file-icon">📥</span> 下载文档
            </a>
            </div>
        </div>
    `;
}

/**
 * 显示DOC/DOCX文件
 * @param {String} fileUrl 文件URL
 * @param {String} fileName 文件名
 */
function showDOCX(fileUrl, fileName) {
    const settings = window.fileViewerSettings;
    const container = document.querySelector(settings.previewContainerSelector);
    
    // 记录DOCX资源浏览
    recordResourceView('docx');
    
    // 隐藏其他元素
    hideElement('#ppt');
    hideElement('#pptbox');
    hideElement('#video');
    hideElement('#videobox');
    
    // 创建DOC查看器容器，包裹在.video-container内
    container.innerHTML = `
        <div class="video-container">
        <div class="doc-toolbar">
            <div>${fileName}</div>
            <div class="doc-controls"></div>
        </div>
        <div id="docContainer" class="doc-container">
            <div class="loading-indicator">文档加载中，请稍候...</div>
            </div>
        </div>
    `;
    
    // 添加下载链接（会在文档成功加载后移除）
    const downloadLinkDiv = document.createElement('div');
    downloadLinkDiv.id = 'docDownloadLink';
    downloadLinkDiv.style.textAlign = 'center';
    downloadLinkDiv.style.marginTop = '20px';
    
    const downloadLink = document.createElement('a');
    downloadLink.href = fileUrl;
    downloadLink.className = 'file-download-btn';
    downloadLink.download = fileName;
    downloadLink.innerHTML = `<span class="file-icon">📥</span> 下载文件`;
    
    downloadLinkDiv.appendChild(downloadLink);
    // 插入到.video-container内
    const videoContainer = container.querySelector('.video-container');
    if (videoContainer) {
        videoContainer.appendChild(downloadLinkDiv);
    } else {
    container.appendChild(downloadLinkDiv);
    }
    
    try {
        // 使用fetch获取文件
        fetch(fileUrl)
            .then(response => {
                if (!response.ok) {
                    throw new Error('文档加载失败，请尝试下载查看');
                }
                return response.arrayBuffer();
            })
            .then(buffer => {
                const docContainer = document.getElementById('docContainer');
                
                // 额外检查文档格式
                try {
                    if (buffer.byteLength === 0) {
                        throw new Error('文档内容为空');
                    }
                    
                    // 检查DOC/DOCX文件头部标识，简单验证文件完整性
                    const header = new Uint8Array(buffer.slice(0, 4));
                    const headerSignature = Array.from(header).map(b => b.toString(16).padStart(2, '0')).join('');
                    
                    // DOCX文件头通常以 "PK\x03\x04" (504b0304) 开头 - Office Open XML格式
                    if (headerSignature !== '504b0304') {
                        console.warn('文档头部标识不符合DOCX格式:', headerSignature);
                        
                        // DOC格式通常以 "D0CF11E0" 开头 - 旧版Office二进制格式
                        if (headerSignature.startsWith('d0cf') || fileName.toLowerCase().endsWith('.doc')) {
                            throw new Error('检测到旧版DOC格式文件，此格式不支持在线预览');
                        }
                    }
                } catch (validationError) {
                    console.warn('文档格式验证警告:', validationError);
                    
                    // 如果检测到DOC格式，直接提供下载链接而不尝试渲染
                    if (validationError.message.includes('DOC格式')) {
                        document.getElementById('docContainer').innerHTML = `
                            <div style="padding: 30px; text-align: center;">
                                <h3 style="color: #333; margin-bottom: 15px;">不支持的文档格式</h3>
                                <p style="color: #666; margin-bottom: 20px;">${validationError.message}</p>
                                <a href="${fileUrl}" class="file-download-btn" download="${fileName}">
                                    <span class="file-icon">📥</span> 下载文档
                                </a>
                            </div>
                        `;
                        return; // 终止进一步处理
                    }
                }
                
                // 增强docx-preview配置，优化样式和格式
                docx.renderAsync(buffer, docContainer, null, {
                    className: 'docx-viewer',
                    inWrapper: true,
                    ignoreHeight: false,
                    ignoreWidth: false,
                    ignoreFonts: false,
                    breakPages: true,
                    enablePagination: true,
                    useBase64URL: true,
                    renderHeaders: true,
                    renderFooters: true,
                    renderFootnotes: true,
                    renderEndnotes: true,
                    defaultStyleMap: {
                        paragraph: {
                            spacing: { line: 1.15 },
                            indent: { left: 0, right: 0 }
                        }
                    },
                    // 添加错误回调
                    errorHandler: (error) => {
                        console.error('文档渲染内部错误:', error);
                        // 允许继续渲染，尝试最大程度显示文档
                        return true;
                    }
                })
                .then((result) => {
                    console.log('文档渲染成功');
                    
                    // 文档加载成功后，移除下载按钮
                    const downloadLinkDiv = document.getElementById('docDownloadLink');
                    if (downloadLinkDiv) {
                        downloadLinkDiv.remove();
                    }
                    
                    // 获取页面数量
                    const pages = docContainer.querySelectorAll('.docx-viewer .page');
                    
                    // 添加页面导航
                    if (pages && pages.length > 0) {
                        addDocNavigation(docContainer, pages);
                    } else {
                        console.warn('未找到文档页面元素');
                    }
                })
                .catch(error => {
                    console.error('文档获取错误:', error);
                    document.getElementById('docContainer').innerHTML = `
                        <div class="error-message">
                            ${error.message}
                            <br><br>
                            <a href="${fileUrl}" class="file-download-btn" download="${fileName}" style="display: inline-block;">
                                <span class="file-icon">📥</span> 下载后查看
                            </a>
                        </div>
                    `;
                });
            })
            .catch(error => {
                console.error('文档获取错误:', error);
                document.getElementById('docContainer').innerHTML = `
                    <div class="error-message">
                        ${error.message}
                        <br><br>
                        <a href="${fileUrl}" class="file-download-btn" download="${fileName}" style="display: inline-block;">
                            <span class="file-icon">📥</span> 下载后查看
                        </a>
                    </div>
                `;
            });
    } catch (e) {
        console.error('文档处理异常:', e);
        document.getElementById('docContainer').innerHTML = `
            <div class="error-message">
                文档处理异常: ${e.message}
                <br><br>
                <a href="${fileUrl}" class="file-download-btn" download="${fileName}" style="display: inline-block;">
                    <span class="file-icon">📥</span> 下载后查看
                </a>
            </div>
        `;
    }
}

/**
 * 为文档添加页面导航
 * @param {Element} container 文档容器元素
 * @param {NodeList} pages 页面元素列表
 */
function addDocNavigation(container, pages) {
    // 进行更严格的检查
    if (!container || !pages || !pages.length || pages.length <= 1) {
        console.log('文档页面不足或无效，不添加导航');
        return;
    }
    
    try {
        const navDiv = document.createElement('div');
        navDiv.className = 'doc-nav';
        navDiv.style.position = 'fixed';
        navDiv.style.bottom = '20px';
        navDiv.style.right = '20px';
        navDiv.style.display = 'flex';
        navDiv.style.background = '#A65D57';
        navDiv.style.borderRadius = '4px';
        navDiv.style.boxShadow = '0 2px 5px rgba(0,0,0,0.2)';
        
        const prevBtn = document.createElement('button');
        prevBtn.innerHTML = '上一页';
        prevBtn.style.border = 'none';
        prevBtn.style.padding = '8px 12px';
        prevBtn.style.color = 'white';
        prevBtn.style.background = 'transparent';
        prevBtn.style.cursor = 'pointer';
        
        const pageIndicator = document.createElement('span');
        pageIndicator.id = 'docCurrentPage';
        pageIndicator.style.padding = '8px 12px';
        pageIndicator.style.color = 'white';
        pageIndicator.style.borderLeft = '1px solid rgba(255,255,255,0.3)';
        pageIndicator.style.borderRight = '1px solid rgba(255,255,255,0.3)';
        pageIndicator.textContent = '1/' + pages.length;
        
        const nextBtn = document.createElement('button');
        nextBtn.innerHTML = '下一页';
        nextBtn.style.border = 'none';
        nextBtn.style.padding = '8px 12px';
        nextBtn.style.color = 'white';
        nextBtn.style.background = 'transparent';
        nextBtn.style.cursor = 'pointer';
        
        navDiv.appendChild(prevBtn);
        navDiv.appendChild(pageIndicator);
        navDiv.appendChild(nextBtn);
        
        let currentPage = 0;
        
        // 更新页码显示和滚动到指定页面
        function goToPage(pageIndex) {
            if (pageIndex < 0) pageIndex = 0;
            if (pageIndex >= pages.length) pageIndex = pages.length - 1;
            
            currentPage = pageIndex;
            pageIndicator.textContent = (currentPage + 1) + '/' + pages.length;
            
            // 确保页面元素存在且有效
            if (pages[currentPage] && typeof pages[currentPage].scrollIntoView === 'function') {
                try {
                    pages[currentPage].scrollIntoView({ behavior: 'smooth' });
                } catch (e) {
                    console.warn('页面滚动失败:', e);
                    // 使用备用滚动方法
                    container.scrollTop = pages[currentPage].offsetTop;
                }
            } else {
                console.warn('无效的页面元素，无法滚动');
            }
        }
        
        prevBtn.addEventListener('click', () => goToPage(currentPage - 1));
        nextBtn.addEventListener('click', () => goToPage(currentPage + 1));
        
        // 添加文档滚动监听，更新当前页码
        let scrollTimeout;
        container.addEventListener('scroll', () => {
            clearTimeout(scrollTimeout);
            scrollTimeout = setTimeout(() => {
                try {
                    // 计算当前页码
                    for (let i = 0; i < pages.length; i++) {
                        if (!pages[i]) continue;
                        
                        const rect = pages[i].getBoundingClientRect();
                        if (rect.top <= container.getBoundingClientRect().top + 100 && 
                            rect.bottom >= container.getBoundingClientRect().top) {
                            if (currentPage !== i) {
                                currentPage = i;
                                pageIndicator.textContent = (currentPage + 1) + '/' + pages.length;
                            }
                            break;
                        }
                    }
                } catch (e) {
                    console.warn('滚动监听错误:', e);
                }
            }, 100);
        });
        
        document.body.appendChild(navDiv);
    } catch (e) {
        console.error('添加文档导航出错:', e);
    }
}

/**
 * 显示PPT/PPTX文件
 * @param {String} fileUrl 文件URL
 * @param {String} fileName 文件名
 * @param {Object} metadata 可能包含图片列表的元数据
 */
function showPPT(fileUrl, fileName, metadata) {
        const settings = window.fileViewerSettings;
        const container = document.querySelector(settings.previewContainerSelector);
    const baseUrl = settings.baseUrl || '';
        
    console.log('正在初始化PPT查看器...', metadata);
    
    // 记录PPT资源浏览
    recordResourceView('ppt');
    
    // 隐藏其他元素
        hideElement('#ppt');
        hideElement('#pptbox');
        hideElement('#video');
        hideElement('#videobox');
        
    // 首先清空容器内容，确保没有旧内容
    container.innerHTML = '';
    
    if (metadata && metadata.attachListPath && metadata.attachListPath.length > 0) {
        const images = metadata.attachListPath;
        // console.log('PPT图片列表: ', images);
        
        // 确保图片列表数据有效
        if (!Array.isArray(images) || images.length === 0) {
            console.error('无效的PPT图片列表数据');
            // 显示错误消息
            container.innerHTML = `
                <div style="padding: 20px; text-align: center; background: #f9f9f9; border-radius: 8px; margin: 20px auto;">
                    <h3 style="color: #A65D57;">PPT加载错误</h3>
                    <p>无法获取有效的PPT图片数据，请稍后再试</p>
                    <a href="${fileUrl}" class="file-download-btn" style="display: inline-block; margin-top: 15px;">
                        <span>📥</span> 下载文件
                    </a>
                </div>
            `;
            return;
        }
        
        // 创建容器结构 - 直接使用与视频容器完全相同的结构和样式
        container.innerHTML = `
            <!-- 使用与视频完全相同的结构和样式 -->
            <div class="video-container">
                <div id="pptImageContainer" style="width:100%; height:100%; display:flex; align-items:center; justify-content:center; position:relative;">
                    <img id="pptMainImage" style="max-width:100%; max-height:100%; object-fit:contain; background:transparent;" />
                    
                        <!-- 加载指示器 -->
                    <div id="pptLoadingIndicator" style="position:absolute; top:50%; left:50%; transform:translate(-50%,-50%); color:white; background:rgba(0,0,0,0.7); padding:10px 20px; border-radius:30px; font-size:14px; display:flex; align-items:center; z-index:15;">
                        <div style="width:20px; height:20px; border:2px solid #fff; border-top-color:transparent; border-radius:50%; margin-right:10px; animation:spin 1s linear infinite;"></div>
                            加载中...
                    </div>
                    
                    <!-- 底部导航栏 - 位于图片容器内部底部 -->
                    <div id="pptNavBar" style="position:absolute; bottom:10px; left:0; right:0; display:flex; justify-content:center; align-items:center; z-index:10; pointer-events:none;">
                        <div style="background:rgba(0,0,0,0.6); padding:6px 12px; border-radius:6px; display:flex; align-items:center; gap:8px; pointer-events:auto;">
                            <button id="pptPrevBtn" style="background:transparent; border:none; color:white; padding:5px; display:flex; align-items:center; cursor:pointer; font-size:13px;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" style="margin-right:4px;">
                                    <path d="M15 18l-6-6 6-6"/>
                                </svg>
                                上一页
                            </button>
                            
                            <span id="pptPageInfo" style="color:white; font-size:13px; min-width:40px; text-align:center;">1/?</span>
                            
                            <button id="pptNextBtn" style="background:transparent; border:none; color:white; padding:5px; display:flex; align-items:center; cursor:pointer; font-size:13px;">
                                下一页
                                <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5" style="margin-left:4px;">
                                    <path d="M9 18l6-6-6-6"/>
                                </svg>
                            </button>
                            
                            <div style="width:1px; height:16px; background-color:rgba(255,255,255,0.3); margin:0 4px;"></div> <!-- 分隔线 -->
                            
                            <button id="pptFullscreenBtn" style="background:transparent; border:none; color:white; padding:5px; display:flex; align-items:center; cursor:pointer; font-size:13px;">
                                <svg xmlns="http://www.w3.org/2000/svg" width="15" height="15" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2.5" stroke-linecap="round" stroke-linejoin="round" style="margin-right:4px;">
                                    <path d="M8 3H5a2 2 0 0 0-2 2v3"></path>
                                    <path d="M21 8V5a2 2 0 0 0-2-2h-3"></path>
                                    <path d="M3 16v3a2 2 0 0 0 2 2h3"></path>
                                    <path d="M16 21h3a2 2 0 0 0 2-2v-3"></path>
                                </svg>
                                全屏
                            </button>
                        </div>
                    </div>
                                </div>
                                </div>
        `;
        
        // 保存PPT图片数组和当前页码
        window.pptImages = images;
        window.pptCurrentPage = 0;
        
        // 获取DOM元素
        const mainImage = document.getElementById('pptMainImage');
        const loadingIndicator = document.getElementById('pptLoadingIndicator');
        const prevBtn = document.getElementById('pptPrevBtn');
        const nextBtn = document.getElementById('pptNextBtn');
        const pageInfo = document.getElementById('pptPageInfo');
        const fullscreenBtn = document.getElementById('pptFullscreenBtn');
        
        // 创建渲染函数
        window.renderPPTPage = function(idx) {
            if (idx < 0 || idx >= images.length) return;
                
                // 显示加载指示器
            if (loadingIndicator) loadingIndicator.style.display = 'flex';
            
            // 更新页码信息
                if (pageInfo) {
                pageInfo.textContent = `${idx + 1}/${images.length}`;
                }
                
            // 更新当前页码
            window.pptCurrentPage = idx;
                
            // 更新按钮状态
            if (prevBtn) {
                prevBtn.disabled = idx === 0;
                prevBtn.style.opacity = idx === 0 ? '0.5' : '1';
                prevBtn.style.cursor = idx === 0 ? 'not-allowed' : 'pointer';
            }
            
            if (nextBtn) {
                nextBtn.disabled = idx === images.length - 1;
                nextBtn.style.opacity = idx === images.length - 1 ? '0.5' : '1';
                nextBtn.style.cursor = idx === images.length - 1 ? 'not-allowed' : 'pointer';
            }
            
            // 加载图片
            mainImage.onload = function() {
                if (loadingIndicator) loadingIndicator.style.display = 'none';
            };
            
            mainImage.onerror = function() {
                console.error(`PPT图片加载失败: ${images[idx]}`);
                if (loadingIndicator) loadingIndicator.innerHTML = '加载失败，请重试...';
            };
                
            // 设置图片源
            const imageUrl = images[idx];
            mainImage.src = imageUrl.startsWith('http') ? imageUrl : (baseUrl + imageUrl);
            
            // 预加载下一页
            if (idx < images.length - 1) {
                const nextImg = new Image();
                const nextUrl = images[idx + 1];
                nextImg.src = nextUrl.startsWith('http') ? nextUrl : (baseUrl + nextUrl);
            }
        };
        
        // 设置按钮事件
        if (prevBtn) {
            prevBtn.addEventListener('click', function() {
                if (window.pptCurrentPage > 0) {
                    window.pptCurrentPage--; 
                    window.renderPPTPage(window.pptCurrentPage);
                }
            });
        }
        
        if (nextBtn) {
            nextBtn.addEventListener('click', function() {
                if (window.pptCurrentPage < images.length - 1) {
                    window.pptCurrentPage++; 
                    window.renderPPTPage(window.pptCurrentPage);
                }
            });
        }
        
        if (fullscreenBtn) {
            fullscreenBtn.addEventListener('click', function() {
                const videoContainer = fullscreenBtn.closest('.video-container');
                if (videoContainer) {
                    toggleFullScreen(videoContainer);
                }
            });
            
            // 鼠标悬停显示全屏按钮
            const videoContainer = fullscreenBtn.closest('.video-container');
            if (videoContainer) {
                videoContainer.addEventListener('mouseenter', function() {
                    fullscreenBtn.style.opacity = '1';
                });
                
                videoContainer.addEventListener('mouseleave', function() {
                    fullscreenBtn.style.opacity = '0.7';
                });
            }
        }
        
        // 添加键盘导航
        document.addEventListener('keydown', function(e) {
            if (e.key === 'ArrowLeft') {
                if (window.pptCurrentPage > 0) {
                    window.pptCurrentPage--;
                    window.renderPPTPage(window.pptCurrentPage);
                }
            } else if (e.key === 'ArrowRight') {
                if (window.pptCurrentPage < images.length - 1) {
                    window.pptCurrentPage++;
                    window.renderPPTPage(window.pptCurrentPage);
                }
            } else if (e.key === 'Home') {
                window.pptCurrentPage = 0;
                window.renderPPTPage(window.pptCurrentPage);
            } else if (e.key === 'End') {
                window.pptCurrentPage = images.length - 1;
                window.renderPPTPage(window.pptCurrentPage);
            } else if (e.key === 'f' || e.key === 'F') {
                const videoContainer = document.querySelector('.video-container');
                if (videoContainer) {
                    toggleFullScreen(videoContainer);
                }
            }
        });
        
        // 添加触摸滑动支持
        const videoContainer = document.querySelector('.video-container');
        if (videoContainer && typeof addTouchSwipeNavigation === 'function') {
            addTouchSwipeNavigation(videoContainer, function() {
            if (window.pptCurrentPage > 0) {
                window.pptCurrentPage--;
                window.renderPPTPage(window.pptCurrentPage);
            }
            }, function() {
            if (window.pptCurrentPage < window.pptImages.length - 1) {
                window.pptCurrentPage++;
                window.renderPPTPage(window.pptCurrentPage);
            }
            });
        }
        
        // 渲染第一页
                setTimeout(() => {
            window.renderPPTPage(0);
        }, 100);
        
    } else {
        // 没有图片列表时显示下载提示
        container.innerHTML = `
            <div class="video-container">
                <div style="text-align:center;padding:30px;background:white;border-radius:8px;box-shadow:0 2px 10px rgba(0,0,0,0.1);">
                    <div style="margin-bottom:20px;">
                        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="#A65D57" stroke-width="2">
                            <path d="M13 2H6a2 2 0 0 0-2 2v16c0 1.1.9 2 2 2h12a2 2 0 0 0 2-2V9l-7-7z"/>
                            <path d="M13 3v6h6"/>
                        </svg>
                    </div>
                    <h3 style="color:#333;margin-bottom:15px;">PPT/PPTX 演示文稿</h3>
                    <p style="color:#666;margin-bottom:20px;">该演示文稿没有可用的预览图片</p>
                    <a href="${fileUrl}" class="file-download-btn" download="${fileName}">
                        <span class="file-icon">📥</span> 下载演示文稿
                    </a>
                </div>
            </div>
        `;
    }
}

/**
 * 切换全屏显示
 * @param {Element} element - 要全屏显示的元素
 */
function toggleFullScreen(element) {
    if (!element) {
        console.error('全屏切换失败: 无效的元素');
        return;
    }
    
    try {
        console.log('触发全屏切换');
        
        // 检查是否已经是全屏模式
        const isFullScreen = document.fullscreenElement || 
                            document.mozFullScreenElement || 
                            document.webkitFullscreenElement || 
                            document.msFullscreenElement;
        
        if (!isFullScreen) {
            // 进入全屏前在页面上显示全屏提示
            const fullscreenMsg = document.createElement('div');
            fullscreenMsg.style = 'position:fixed;top:50%;left:50%;transform:translate(-50%,-50%);background:rgba(0,0,0,0.8);color:white;padding:15px 30px;border-radius:30px;z-index:9999;font-size:16px;';
            fullscreenMsg.textContent = '正在进入全屏模式...';
            document.body.appendChild(fullscreenMsg);
            
            // 0.5秒后移除提示
            setTimeout(() => {
                if (fullscreenMsg.parentNode) {
                    fullscreenMsg.parentNode.removeChild(fullscreenMsg);
                }
            }, 1000);
            
            // 进入全屏 - 尝试多种方法
            const enterFullScreen = () => {
                console.log('尝试进入全屏模式');
                
                if (element.requestFullscreen) {
                    element.requestFullscreen();
                } else if (element.mozRequestFullScreen) { // Firefox
                    element.mozRequestFullScreen();
                } else if (element.webkitRequestFullscreen) { // Chrome, Safari
                    element.webkitRequestFullscreen(Element.ALLOW_KEYBOARD_INPUT);
                } else if (element.msRequestFullscreen) { // IE/Edge
                    element.msRequestFullscreen();
                } else {
                    throw new Error('不支持的全屏API');
                }
            };
            
            // 尝试进入全屏
            enterFullScreen();
            
            // 0.5秒后检查是否真的进入了全屏，如果没有则尝试使用备用方法
            setTimeout(() => {
                const stillNotFullScreen = !document.fullscreenElement && 
                                         !document.mozFullScreenElement && 
                                         !document.webkitFullscreenElement && 
                                         !document.msFullscreenElement;
                
                if (stillNotFullScreen) {
                    console.warn('首次全屏尝试失败，使用备用方法');
                    // 尝试使用document.documentElement作为全屏元素
                    try {
                        const docElm = document.documentElement;
                        if (docElm.requestFullscreen) {
                            docElm.requestFullscreen();
                        } else if (docElm.mozRequestFullScreen) {
                            docElm.mozRequestFullScreen();
                        } else if (docElm.webkitRequestFullScreen) {
                            docElm.webkitRequestFullScreen(Element.ALLOW_KEYBOARD_INPUT);
                        } else if (docElm.msRequestFullscreen) {
                            docElm.msRequestFullscreen();
                        }
                    } catch (backupError) {
                        console.error('备用全屏方法也失败:', backupError);
                    }
                }
            }, 500);
            
            // 显示全屏导航
            const fullscreenNav = element.querySelector('.fullscreen-nav');
            if (fullscreenNav) {
                fullscreenNav.style.opacity = '1';
                fullscreenNav.style.pointerEvents = 'all';
                
                // 3秒后自动隐藏
                setTimeout(() => {
                    fullscreenNav.style.opacity = '0';
                    fullscreenNav.style.pointerEvents = 'none';
                }, 3000);
            }
        } else {
            // 退出全屏
            if (document.exitFullscreen) {
                document.exitFullscreen();
            } else if (document.mozCancelFullScreen) { // Firefox
                document.mozCancelFullScreen();
            } else if (document.webkitExitFullscreen) { // Chrome, Safari
                document.webkitExitFullscreen();
            } else if (document.msExitFullscreen) { // IE/Edge
                document.msExitFullscreen();
            }
            
            console.log('退出全屏模式');
        }
    } catch (error) {
        console.error('全屏切换出错:', error);
        
        // 显示错误提示
        alert('浏览器不支持全屏模式，或已被禁用。请尝试更新浏览器或检查浏览器设置。');
    }
}

/**
 * 为元素添加触摸滑动导航
 * @param {Element} element - 要添加触摸事件的元素
 * @param {Function} onSwipeLeft - 向左滑动的回调
 * @param {Function} onSwipeRight - 向右滑动的回调
 */
function addTouchSwipeNavigation(element, onSwipeLeft, onSwipeRight) {
    if (!element || typeof onSwipeLeft !== 'function' || typeof onSwipeRight !== 'function') return;
    
    let touchStartX = 0;
    let touchEndX = 0;
    let touchStartY = 0;
    let touchEndY = 0;
    let startTime = 0;
    let isSwiping = false;
    
    // 较小的滑动距离阈值，提高灵敏度
    const minSwipeDistance = 25; // 减小滑动距离阈值，使更容易触发
    // 最长滑动时间(毫秒)，防止拖动被误识别为滑动
    const maxSwipeTime = 400; // 增加有效时间窗口
    // 垂直滑动容差，允许一定角度的倾斜滑动
    const verticalTolerance = 50; // 增加垂直容差
    
    // 获取左右触摸区域
    const leftTouchArea = element.querySelector('.touch-nav-hint.left');
    const rightTouchArea = element.querySelector('.touch-nav-hint.right');
    const fullscreenNav = element.querySelector('.fullscreen-nav');
    
    // 为左右触摸区添加点击事件，移除视觉反馈
    if (leftTouchArea) {
        leftTouchArea.addEventListener('click', event => {
            onSwipeLeft(event);
        });
    }
    
    if (rightTouchArea) {
        rightTouchArea.addEventListener('click', event => {
            onSwipeRight(event);
        });
    }
    
    // 为全屏模式下的导航按钮添加事件
    const fsPrevBtn = element.querySelector('.fullscreen-nav .fs-nav-btn.prev');
    const fsNextBtn = element.querySelector('.fullscreen-nav .fs-nav-btn.next');
    
    if (fsPrevBtn) {
        fsPrevBtn.addEventListener('click', onSwipeLeft);
    }
    
    if (fsNextBtn) {
        fsNextBtn.addEventListener('click', onSwipeRight);
    }
    
    // Mobile devices show/hide fullscreen nav - this logic is refined in fullscreenChangeHandler
    // let navTimeout;
    // element.addEventListener('mousemove', showFullscreenNav); // This is now pptContainer.onmousemove
    // element.addEventListener('touchstart', showFullscreenNav);
    // element.addEventListener('click', showFullscreenNav);
    
    // function showFullscreenNav() { ... } // This function is now part of the mousemove logic
    
    // 创建视觉滑动提示元素
    const swipeHint = document.createElement('div');
    swipeHint.className = 'swipe-hint';
    swipeHint.style.cssText = 'position:absolute;top:0;left:0;right:0;bottom:0;pointer-events:none;z-index:25;opacity:0;transition:opacity 0.2s;';
    element.appendChild(swipeHint);
    
    // 设置滑动视觉提示
    function showSwipeHint(direction, strength) {
        // 方向: -1 左滑, 1 右滑
        // 强度: 0-1, 用于视觉反馈的强度
        
        // 清除之前的内容
        swipeHint.innerHTML = '';
        swipeHint.style.opacity = Math.min(strength, 0.7).toFixed(2);
        
        // 创建箭头容器
        const arrow = document.createElement('div');
        arrow.style.cssText = `position:absolute;top:50%;${direction < 0 ? 'right:20%' : 'left:20%'};transform:translateY(-50%);
                              width:50px;height:50px;background:rgba(166,93,87,${Math.min(strength, 0.8).toFixed(2)});
                              border-radius:50%;display:flex;align-items:center;justify-content:center;`;
        
        // 添加箭头SVG
        arrow.innerHTML = `
            <svg xmlns="http://www.w3.org/2000/svg" width="30" height="30" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
                <path d="${direction < 0 ? 'M15 18l-6-6 6-6' : 'M9 18l6-6-6-6'}"/>
            </svg>
        `;
        
        swipeHint.appendChild(arrow);
    }
    
    // 隐藏滑动提示
    function hideSwipeHint() {
        swipeHint.style.opacity = '0';
    }
    
    element.addEventListener('touchstart', function(e) {
        touchStartX = e.changedTouches[0].screenX;
        touchStartY = e.changedTouches[0].screenY;
        startTime = new Date().getTime();
        isSwiping = true;
    }, { passive: true });
    
    element.addEventListener('touchmove', function(e) {
        if (!isSwiping) return;
        
        const currentX = e.changedTouches[0].screenX;
        const currentY = e.changedTouches[0].screenY;
        const deltaX = currentX - touchStartX;
        const deltaY = currentY - touchStartY;
        
        // 如果垂直滑动距离大于水平滑动距离的1.5倍，认为是在滚动页面而非滑动翻页
        if (Math.abs(deltaY) > Math.abs(deltaX) * 1.5) {
            isSwiping = false;
            hideSwipeHint();
            return;
        }
        
        // 显示滑动方向提示
        if (Math.abs(deltaX) > minSwipeDistance / 2) {
            const direction = deltaX < 0 ? -1 : 1;
            const strength = Math.min(Math.abs(deltaX) / 150, 1); // 最多移动150px到达最大强度
            showSwipeHint(direction, strength);
        } else {
            hideSwipeHint();
        }
        
        // 在全屏模式下，阻止默认事件以防止页面滚动
        if (document.fullscreenElement || 
            document.webkitFullscreenElement || 
            document.mozFullScreenElement || 
            document.msFullscreenElement) {
            e.preventDefault();
        }
    }, { passive: false });
    
    element.addEventListener('touchend', function(e) {
        // 无论如何先隐藏滑动提示
        hideSwipeHint();
        
        if (!isSwiping) return;
        
        touchEndX = e.changedTouches[0].screenX;
        touchEndY = e.changedTouches[0].screenY;
        const endTime = new Date().getTime();
        const swipeTime = endTime - startTime;
        
        // 计算水平滑动距离
        const distance = touchEndX - touchStartX;
        // 计算垂直滑动距离
        const verticalDistance = Math.abs(touchEndY - touchStartY);
        
        // 只有当滑动时间短、垂直滑动距离小且水平滑动距离达到阈值时才触发
        if (swipeTime < maxSwipeTime && verticalDistance < verticalTolerance) {
            if (distance < -minSwipeDistance) {
                // 向左滑动 - 下一页
                console.log('触发左滑翻页');
                onSwipeRight();
            } else if (distance > minSwipeDistance) {
                // 向右滑动 - 上一页
                console.log('触发右滑翻页');
                onSwipeLeft();
            }
        }
        
        isSwiping = false;
    }, { passive: true });
}

/**
 * 显示下载链接
 * @param {String} fileUrl 文件URL
 * @param {String} fileName 文件名
 * @param {String} fileType 文件类型
 */
function showDownloadLink(fileUrl, fileName, fileType) {
    const settings = window.fileViewerSettings;
    const container = document.querySelector(settings.previewContainerSelector);
    
    // 隐藏其他元素
    hideElement('#ppt');
    hideElement('#pptbox');
    hideElement('#video');
    hideElement('#videobox');
    
    container.innerHTML = `
        <div style="text-align: center; padding: 30px; background: #f9f9f9; border-radius: 8px;">
            <h3 style="margin-bottom: 20px; color: #333;">${fileType.toUpperCase()} 文件</h3>
            <p style="margin-bottom: 20px; color: #666;">当前格式不支持在线预览，请下载后查看</p>
        </div>
    `;
    
    // 添加下载链接
    appendDownloadLink(container, fileUrl, fileName);
}

/**
 * 添加下载链接到容器
 * @param {Element} container 容器元素
 * @param {String} fileUrl 文件URL
 * @param {String} fileName 文件名
 */
function appendDownloadLink(container, fileUrl, fileName) {
    const downloadLinkDiv = document.createElement('div');
    downloadLinkDiv.style.textAlign = 'center';
    downloadLinkDiv.style.marginTop = '20px';
    
    const downloadLink = document.createElement('a');
    downloadLink.href = fileUrl;
    downloadLink.className = 'file-download-btn';
    downloadLink.download = fileName;
    downloadLink.innerHTML = `<span class="file-icon">📥</span> 下载文件`;
    
    downloadLinkDiv.appendChild(downloadLink);
    container.appendChild(downloadLinkDiv);
}

/**
 * 隐藏元素
 * @param {String} selector 选择器
 */
function hideElement(selector) {
    const element = document.querySelector(selector);
    if (element) {
        element.style.display = 'none';
    }
}

/**
 * 显示元素
 * @param {String} selector 选择器
 */
function showElement(selector) {
    const element = document.querySelector(selector);
    if (element) {
        element.style.display = '';
    }
}

// 导出函数
window.FileViewer = {
    init: initFileViewer,
    display: displayFileContent
}; 