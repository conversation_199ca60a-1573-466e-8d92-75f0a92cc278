<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件查看器测试</title>
    
    <!-- Tailwind CSS -->
    <script src="https://cdn.tailwindcss.com"></script>
    
    <script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>
    <script src="./js/viewers/howler.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="https://unpkg.com/jszip@3.10.1/dist/jszip.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="./js/viewers/docx-preview.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="./js/viewers/file-viewer.js" type="text/javascript" charset="utf-8"></script>
    
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f8f9fa;
        }
        
        /* 重用文件查看器组件样式 */
        .doc-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 10px 15px;
            background: #f5f5f5;
            border-radius: 8px 8px 0 0;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
            font-weight: 500;
            color: #333;
        }
        
        .doc-container {
            width: 100%;
            height: 600px;
            border: 1px solid #ddd;
            border-top: none;
            border-radius: 0 0 8px 8px;
            overflow: auto;
            background: white;
            position: relative;
        }
        
        .docx-viewer {
            padding: 20px;
            font-family: 'Times New Roman', serif;
            line-height: 1.5;
        }
        
        .docx-viewer .page {
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            background: white;
            padding: 20px;
            border: 1px solid #eee;
        }
    </style>
</head>
<body class="min-h-screen bg-gray-100">
    <div class="container mx-auto p-6">
        <header class="mb-8">
            <h1 class="text-3xl font-bold text-gray-800 mb-2">文件查看器测试</h1>
            <p class="text-gray-600">测试各种文件格式的在线预览效果</p>
        </header>
        
        <div class="bg-white rounded-lg shadow-lg p-6 mb-8">
            <div class="preview-container" id="previewContainer" style="min-height: 500px;">
                <!-- 文件预览将在这里显示 -->
                <div class="flex flex-col items-center justify-center h-80 text-gray-500">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-16 w-16 mb-4" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    <p class="text-xl">请选择下方的文件类型进行测试</p>
                </div>
            </div>
        </div>
        
        <div class="bg-white rounded-lg shadow-lg p-6">
            <h2 class="text-xl font-semibold mb-4">测试文件类型</h2>
            
            <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                <button onclick="loadDocx()" class="flex items-center justify-center px-4 py-3 bg-indigo-600 text-white rounded hover:bg-indigo-700 transition">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z" />
                    </svg>
                    测试DOCX文档
                </button>
                
                <button onclick="loadMp3()" class="flex items-center justify-center px-4 py-3 bg-pink-600 text-white rounded hover:bg-pink-700 transition">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19V6l12-3v13M9 19c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zm12-3c0 1.105-1.343 2-3 2s-3-.895-3-2 1.343-2 3-2 3 .895 3 2zM9 10l12-3" />
                    </svg>
                    测试MP3音频
                </button>
                
                <button onclick="loadPdf()" class="flex items-center justify-center px-4 py-3 bg-red-600 text-white rounded hover:bg-red-700 transition">
                    <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 10v6m0 0l-3-3m3 3l3-3M3 17V7a2 2 0 012-2h6l2 2h6a2 2 0 012 2v8a2 2 0 01-2 2H5a2 2 0 01-2-2z" />
                    </svg>
                    测试PDF文档
                </button>
            </div>
            
            <div class="mt-6 border-t pt-4">
                <h3 class="text-lg font-medium mb-3">使用本地文件</h3>
                <div class="flex flex-wrap gap-4">
                    <div>
                        <label for="localFile" class="block mb-2 text-sm font-medium text-gray-700">选择文件</label>
                        <input type="file" id="localFile" class="block w-full text-sm text-gray-500
                            file:mr-4 file:py-2 file:px-4
                            file:rounded-md file:border-0
                            file:text-sm file:font-medium
                            file:bg-gray-100 file:text-gray-700
                            hover:file:bg-gray-200
                        " />
                    </div>
                    <div class="flex items-end">
                        <button onclick="loadLocalFile()" class="px-4 py-2 bg-gray-800 text-white rounded hover:bg-gray-900 transition">加载选择的文件</button>
                    </div>
                </div>
            </div>
        </div>
        
        <footer class="mt-8 text-center text-gray-500 text-sm">
            <p>文件查看器 | 支持多种文件格式在线预览</p>
        </footer>
    </div>

    <script>
        // 初始化文件查看器
        window.FileViewer.init({
            containerSelector: '#previewContainer',
            previewContainerSelector: '#previewContainer',
            baseUrl: ''
        });
        
        // 测试加载DOCX文件
        function loadDocx() {
            const testUrl = 'https://file-examples.com/storage/fe8c7eef0c6364f6c9504cc/2017/02/file-sample_100kB.docx';
            window.FileViewer.display(testUrl, 'docx', '示例DOCX文件.docx');
        }
        
        // 测试加载MP3文件
        function loadMp3() {
            const testUrl = 'https://file-examples.com/storage/fe8c7eef0c6364f6c9504cc/2017/11/file_example_MP3_700KB.mp3';
            window.FileViewer.display(testUrl, 'mp3', '示例MP3文件.mp3');
        }
        
        // 测试加载PDF文件
        function loadPdf() {
            const testUrl = 'https://file-examples.com/storage/fe8c7eef0c6364f6c9504cc/2017/10/file-sample_150kB.pdf';
            window.FileViewer.display(testUrl, 'pdf', '示例PDF文件.pdf');
        }
        
        // 加载本地文件
        function loadLocalFile() {
            const fileInput = document.getElementById('localFile');
            if (fileInput.files.length === 0) {
                alert('请先选择文件');
                return;
            }
            
            const file = fileInput.files[0];
            const fileName = file.name;
            const fileType = fileName.split('.').pop().toLowerCase();
            
            // 创建文件URL
            const fileUrl = URL.createObjectURL(file);
            
            // 显示文件
            window.FileViewer.display(fileUrl, fileType, fileName);
        }
    </script>
</body>
</html> 