/**
 * 全局菜单修改器 - 注入到Swiper库中以确保所有页面都加载
 */
(function() {
    // 创建一个拦截器来修改API响应
    const originalFetch = window.fetch;
    const originalXHROpen = XMLHttpRequest.prototype.open;
    const originalXHRSend = XMLHttpRequest.prototype.send;
    
    // 拦截XMLHttpRequest
    XMLHttpRequest.prototype.open = function(method, url, async, user, password) {
        this._url = url;
        this._method = method;
        return originalXHROpen.apply(this, arguments);
    };
    
    XMLHttpRequest.prototype.send = function(data) {
        const xhr = this;
        const originalOnReadyStateChange = xhr.onreadystatechange;
        
        xhr.onreadystatechange = function() {
            if (xhr.readyState === 4 && xhr.status === 200) {
                // 尝试检测请求是否与菜单数据相关
                const url = xhr._url || '';
                if (url.includes('/web/category/all') || url.includes('/menus/list/tree') || url.includes('/menus/list/menu/tree')) {
                    try {
                        let response = JSON.parse(xhr.responseText);
                        if (response && response.code === '200' && response.data) {
                            response = modifyMenuData(response);
                            // 重写响应
                            Object.defineProperty(this, 'responseText', {
                                get: function() {
                                    return JSON.stringify(response);
                                }
                            });
                        }
                    } catch (e) {
                        console.error('菜单数据修改失败:', e);
                    }
                }
            }
            
            if (originalOnReadyStateChange) {
                originalOnReadyStateChange.apply(this, arguments);
            }
        };
        
        return originalXHRSend.apply(this, arguments);
    };
    
    // 拦截fetch API
    window.fetch = function(input, init) {
        return originalFetch(input, init).then(response => {
            const url = typeof input === 'string' ? input : input.url;
            
            // 检查是否是菜单数据请求
            if (url.includes('/web/category/all') || url.includes('/menus/list/tree') || url.includes('/menus/list/menu/tree')) {
                const clonedResponse = response.clone();
                
                return clonedResponse.json().then(data => {
                    if (data && data.code === '200' && data.data) {
                        const modifiedData = modifyMenuData(data);
                        
                        // 创建一个新的响应对象
                        const modifiedResponse = new Response(JSON.stringify(modifiedData), {
                            status: response.status,
                            statusText: response.statusText,
                            headers: response.headers
                        });
                        
                        return modifiedResponse;
                    }
                    return response;
                }).catch(error => {
                    console.error('处理菜单数据时出错:', error);
                    return response;
                });
            }
            
            return response;
        });
    };
    
    // 修改菜单数据的核心函数
    function modifyMenuData(response) {
        if (!response || !response.data || !Array.isArray(response.data)) {
            return response;
        }
        
        const data = response.data;
        const modifiedData = [];
        
        // 提取教学资源链接
        let teachingResourcesUrl = null;
        let redBooksUrl = null;
        let homepageItem = null;
        let courseItem = null;
        
        // 先找出首页菜单项
        for (let i = 0; i < data.length; i++) {
            if (data[i].name === '首页') {
                homepageItem = data[i];
                break;
            }
        }
        
        // 遍历原始菜单数据
        for (let i = 0; i < data.length; i++) {
            const item = data[i];
            
            // 跳过学院简介和新闻资讯
            if (item.name === '学院简介' || item.name === '新闻资讯') {
                continue;
            }
            
            // 跳过首页，因为我们会单独处理
            if (item.name === '首页') {
                continue;
            }
            
            // 处理在线学习菜单项
            if (item.name === '在线学习') {
                if (item.children && Array.isArray(item.children)) {
                    // 提取教学资源链接
                    for (let j = 0; j < item.children.length; j++) {
                        const child = item.children[j];
                        if (child.name === '教学资源') {
                            teachingResourcesUrl = child.redirectUrl;
                        } else if (child.name === '红色书籍') {
                            redBooksUrl = child.redirectUrl;
                        }
                    }
                }
                
                // 修改为只包含红色书籍
                const modifiedItem = {
                    ...item,
                    name: '红色书籍',
                    children: item.children ? item.children.filter(child => child.name === '红色书籍') : []
                };
                
                // 如果有红色书籍URL，则使用它
                if (redBooksUrl) {
                    modifiedItem.redirectUrl = redBooksUrl;
                }
                
                modifiedData.push(modifiedItem);
                continue;
            }
            
            // 正常添加其他菜单项
            modifiedData.push(item);
        }
        
        // 如果找到了教学资源链接，则添加新的"课程学习"菜单项
        if (teachingResourcesUrl) {
            courseItem = {
                id: 'course-learning-' + Date.now(),
                name: '课程学习',
                redirectUrl: teachingResourcesUrl,
                sort: 2 // 修改排序位置为2，确保在首页之后但在其他菜单之前
            };
            modifiedData.push(courseItem);
        }
        
        // 首页排到第一位
        if (homepageItem) {
            modifiedData.unshift(homepageItem);
        }
        
        // 对菜单项进行排序，确保首页排第一，课程学习排第二，红色书籍排第三
        modifiedData.sort((a, b) => {
            // 首页总是排在最前面
            if (a.name === '首页') return -1;
            if (b.name === '首页') return 1;
            
            // 课程学习排在首页之后第二位
            if (a.name === '课程学习') return -1;
            if (b.name === '课程学习') return 1;
            
            // 红色书籍排在第三位
            if (a.name === '红色书籍') return -1;
            if (b.name === '红色书籍') return 1;
            
            // 其他项目按照原有排序
            return (a.sort || 999) - (b.sort || 999);
        });
        
        // 更新排序号
        for (let i = 0; i < modifiedData.length; i++) {
            modifiedData[i].sort = i + 1;
        }
        
        response.data = modifiedData;
        return response;
    }
    
    // 直接修改已经在页面上的菜单
    function modifyExistingMenu() {
        // 等待DOM加载完成
        if (document.readyState === 'loading') {
            document.addEventListener('DOMContentLoaded', modifyMenuDOM);
        } else {
            modifyMenuDOM();
        }
    }
    
    function modifyMenuDOM() {
        // 尝试修改已经渲染的菜单
        setTimeout(() => {
            const menuBox = document.getElementById('menubox');
            if (!menuBox) return;
            
            // 隐藏学院简介和新闻资讯
            const menuItems = menuBox.querySelectorAll('.menuitemaaa, .menuitem, div[class^="menuitem"]');
            
            // 寻找首页、课程学习和红色书籍菜单项
            let homepageItem = null;
            let courseItem = null;
            let redBookItem = null;
            
            menuItems.forEach(item => {
                const text = item.textContent || '';
                if (text.includes('首页')) {
                    homepageItem = item;
                } else if (text.includes('课程学习')) {
                    courseItem = item;
                } else if (text.includes('红色书籍') || text.includes('在线学习')) {
                    redBookItem = item;
                    // 修改在线学习为红色书籍
                    if (text.includes('在线学习')) {
                        const elements = item.querySelectorAll('*');
                        elements.forEach(el => {
                            if (el.childNodes.length === 1 && el.childNodes[0].nodeType === 3) {
                                el.childNodes[0].textContent = el.childNodes[0].textContent.replace('在线学习', '红色书籍');
                            }
                        });
                        
                        const links = item.querySelectorAll('a');
                        links.forEach(link => {
                            const linkText = link.textContent || '';
                            if (linkText.includes('在线学习')) {
                                link.textContent = link.textContent.replace('在线学习', '红色书籍');
                            }
                        });
                    }
                }
            });
            
            // 重新排序菜单项：首页 -> 课程学习 -> 红色书籍
            if (homepageItem && menuBox.firstChild !== homepageItem) {
                menuBox.insertBefore(homepageItem, menuBox.firstChild);
            }
            
            if (courseItem && redBookItem && homepageItem) {
                // 确保课程学习在首页之后，红色书籍之前
                menuBox.insertBefore(courseItem, redBookItem);
            }
            
            // 隐藏学院简介和新闻资讯菜单项
            menuItems.forEach(item => {
                const text = item.textContent || '';
                const links = item.querySelectorAll('a');
                
                links.forEach(link => {
                    const linkText = link.textContent || '';
                    if (linkText.includes('学院简介') || linkText.includes('新闻资讯')) {
                        item.style.display = 'none';
                    }
                });
                
                if (text.includes('学院简介') || text.includes('新闻资讯')) {
                    item.style.display = 'none';
                }
            });
        }, 100);
    }
    
    // 在页面加载时修改已有菜单
    modifyExistingMenu();
    
    // 在页面上定期检查并修改DOM
    setInterval(modifyMenuDOM, 1000);
    
    // 添加横屏提示功能
    function createOrientationTip() {
        // 检查是否已创建过提示层
        if (document.getElementById('orientation-tip')) return;
        
        // 创建提示层
        const tipElement = document.createElement('div');
        tipElement.id = 'orientation-tip';
        tipElement.style.cssText = `
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.9);
            z-index: 9999;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            font-size: 16px;
            text-align: center;
            display: none;
        `;
        
        // 添加图标
        const iconElement = document.createElement('div');
        iconElement.style.cssText = `
            width: 64px;
            height: 64px;
            margin-bottom: 20px;
            background-color: white;
            -webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M16.48 2.52c3.27 1.55 5.61 4.72 5.97 8.48h1.5c-.51-6-5.3-10.69-11.22-10.96v1.5c1.28.09 2.51.37 3.67.82 0 .***********.06zm-7.25 1.13C8.4 2.77 9.93 2.09 11.61 2v-1.98C7.1 .55 3.18 3.52 1.55 7.66c.35.22.74.4 1.14.54 1.02-2.55 3.06-4.47 5.64-5.31 0-.01.05-.02.09-.04zm-.46 2.24c-.65.4-1.23.92-1.72 1.5 1.32.71 2.24 2.05 2.35 3.61h3.29c0-2.88-1.94-5.27-4.56-5.79-.02.01-.05 0-.07 0-.07.01-.13.05-.19.1.02.03 0 .07-.05.09-.03.03-.05.05-.06 0-.03-.07-.05-.07-.04-.04zm7.38 1.84c.96 1.09 1.55 2.51 1.55 4.07h1.5c0-1.96-.76-3.76-1.98-5.13-.07-.05-.12-.09-.17-.13-.03-.02-.07-.03-.11-.05-.04-.02-.09-.02-.13-.04-.05-.02-.05-.05 0-.07-.05-.02-.1-.04-.15-.06-.05-.02-.06-.07-.06-.09h-.03c-.12-.03-.24.09-.24.09 0 .04-.02.07-.05.11 0 0-.01 0-.01.01-.01.01 0 0 0 0-.03 0-.07.04-.1.06-.01 0-.13.09-.13.09-.05.04-.04.09-.05.14 0 .02-.01.04-.03.05.01.08.02.11.07.16.06.07.16.12.26.13z'/%3E%3Cpath d='M10.8 4.9c0-.99.19-1.94.54-2.81-1.2-.51-2.53-.8-3.93-.8-5.52 0-10 4.48-10 10s4.48 10 10 10c1.75 0 3.39-.45 4.81-1.24-.28-.9-.43-1.86-.43-2.85 0-5.52 4.48-10 10-10 .65 0 1.29.07 1.91.21.01-.7.02-.41.02-.62 0-2.49-.96-4.76-2.52-6.46-1.56-1.7-3.76-2.76-6.19-2.76-.34 0-.68.03-1.01.08.44.93.7 1.97.7 3.08 0 4.03-3.26 7.28-7.28 7.28-1.11 0-2.15-.25-3.08-.7-.05.32-.08.66-.08 1.01 0 2.43 1.06 4.63 2.76 6.19 1.7 1.56 3.97 2.52 6.46 2.52.21 0 .41-.01.62-.02-.14-.62-.21-1.26-.21-1.91 0-5.52-4.48-10-10-10zm7.2 14.98c.05-.32.08-.66.08-1.01 0-2.43-1.06-4.63-2.76-6.19-1.7-1.56-3.97-2.52-6.46-2.52-.21 0-.41.01-.62.02.14.62.21 1.26.21 1.91 0 5.52 4.48 10 10 10 .65 0 1.29-.07 1.91-.21.01.21.02.41.02.62 0 2.43-1.06 4.63-2.76 6.19-1.7 1.56-3.97 2.52-6.46 2.52-.34 0-.68-.03-1.01-.08.44-.93.7-1.97.7-3.08 0-4.02-3.26-7.28-7.28-7.28-1.11 0-2.15.25-3.08.7-.05-.33-.08-.67-.08-1.01 0-2.43 1.06-4.63 2.76-6.19 1.7-1.56 3.97-2.52 6.46-2.52.21 0 .41.01.62.02-.14.62-.21 1.26-.21 1.91 0 5.52 4.48 10 10 10 .65 0 1.29-.07 1.91-.21.01-.7.02-.41.02-.62 0-2.49-.96-4.76-2.52-6.46-1.56-1.7-3.76-2.76-6.19-2.76-.34 0-.68.03-1.01.08.44.93.7 1.97.7 3.08 0 4.02-3.26 7.28-7.28 7.28-1.11 0-2.15-.25-3.08-.7z'/%3E%3C/svg%3E") no-repeat center center;
            mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M16.48 2.52c3.27 1.55 5.61 4.72 5.97 8.48h1.5c-.51-6-5.3-10.69-11.22-10.96v1.5c1.28.09 2.51.37 3.67.82 0 .***********.06zm-7.25 1.13C8.4 2.77 9.93 2.09 11.61 2v-1.98C7.1 .55 3.18 3.52 1.55 7.66c.35.22.74.4 1.14.54 1.02-2.55 3.06-4.47 5.64-5.31 0-.01.05-.02.09-.04zm-.46 2.24c-.65.4-1.23.92-1.72 1.5 1.32.71 2.24 2.05 2.35 3.61h3.29c0-2.88-1.94-5.27-4.56-5.79-.02.01-.05 0-.07 0-.07.01-.13.05-.19.1.02.03 0 .07-.05.09-.03.03-.05.05-.06 0-.03-.07-.05-.07-.04-.04zm7.38 1.84c.96 1.09 1.55 2.51 1.55 4.07h1.5c0-1.96-.76-3.76-1.98-5.13-.07-.05-.12-.09-.17-.13-.03-.02-.07-.03-.11-.05-.04-.02-.09-.02-.13-.04-.05-.02-.05-.05 0-.07-.05-.02-.1-.04-.15-.06-.05-.02-.06-.07-.06-.09h-.03c-.12-.03-.24.09-.24.09 0 .04-.02.07-.05.11 0 0-.01 0-.01.01-.01.01 0 0 0 0-.03 0-.07.04-.1.06-.01 0-.13.09-.13.09-.05.04-.04.09-.05.14 0 .02-.01.04-.03.05.01.08.02.11.07.16.06.07.16.12.26.13z'/%3E%3Cpath d='M10.8 4.9c0-.99.19-1.94.54-2.81-1.2-.51-2.53-.8-3.93-.8-5.52 0-10 4.48-10 10s4.48 10 10 10c1.75 0 3.39-.45 4.81-1.24-.28-.9-.43-1.86-.43-2.85 0-5.52 4.48-10 10-10 .65 0 1.29.07 1.91.21.01-.7.02-.41.02-.62 0-2.49-.96-4.76-2.52-6.46-1.56-1.7-3.76-2.76-6.19-2.76-.34 0-.68.03-1.01.08.44.93.7 1.97.7 3.08 0 4.03-3.26 7.28-7.28 7.28-1.11 0-2.15-.25-3.08-.7-.05.32-.08.66-.08 1.01 0 2.43 1.06 4.63 2.76 6.19 1.7 1.56 3.97 2.52 6.46 2.52.21 0 .41-.01.62-.02-.14-.62-.21-1.26-.21-1.91 0-5.52-4.48-10-10-10zm7.2 14.98c.05-.32.08-.66.08-1.01 0-2.43-1.06-4.63-2.76-6.19-1.7-1.56-3.97-2.52-6.46-2.52-.21 0-.41.01-.62.02.14.62.21 1.26.21 1.91 0 5.52 4.48 10 10 10 .65 0 1.29-.07 1.91-.21.01.21.02.41.02.62 0 2.43-1.06 4.63-2.76 6.19-1.7 1.56-3.97 2.52-6.46 2.52-.34 0-.68-.03-1.01-.08.44-.93.7-1.97.7-3.08 0-4.02-3.26-7.28-7.28-7.28-1.11 0-2.15.25-3.08.7-.05-.33-.08-.67-.08-1.01 0-2.43 1.06-4.63 2.76-6.19 1.7-1.56 3.97-2.52 6.46-2.52.21 0 .41.01.62.02-.14.62-.21 1.26-.21 1.91 0 5.52 4.48 10 10 10 .65 0 1.29-.07 1.91-.21.01-.7.02-.41.02-.62 0-2.49-.96-4.76-2.52-6.46-1.56-1.7-3.76-2.76-6.19-2.76-.34 0-.68.03-1.01.08.44.93.7 1.97.7 3.08 0 4.02-3.26 7.28-7.28 7.28-1.11 0-2.15-.25-3.08-.7z'/%3E%3C/svg%3E") no-repeat center center;
            transform: rotate(90deg);
        `;
        
        // 添加文字提示
        const textElement = document.createElement('p');
        textElement.innerHTML = '请横屏浏览以获得最佳体验';
        textElement.style.cssText = 'margin: 15px 0; font-size: 18px;';
        
        // 添加按钮
        const buttonElement = document.createElement('button');
        buttonElement.innerHTML = '忽略并继续';
        buttonElement.style.cssText = `
            background-color: #e74c3c;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            font-size: 16px;
            cursor: pointer;
            margin-top: 20px;
        `;
        buttonElement.addEventListener('click', function() {
            localStorage.setItem('orientation-preference', 'ignore');
            document.getElementById('orientation-tip').style.display = 'none';
        });
        
        // 组装提示层
        tipElement.appendChild(iconElement);
        tipElement.appendChild(textElement);
        tipElement.appendChild(buttonElement);
        document.body.appendChild(tipElement);
    }
    
    // 检查屏幕方向并显示/隐藏提示
    function checkOrientation() {
        // 仅在移动设备上显示提示
        if (!isMobileDevice()) return;
        
        // 检查用户是否已选择忽略
        if (localStorage.getItem('orientation-preference') === 'ignore') return;
        
        createOrientationTip();
        
        const tipElement = document.getElementById('orientation-tip');
        if (!tipElement) return;
        
        // 检查是否是竖屏模式
        const isPortrait = window.innerHeight > window.innerWidth;
        
        // 显示或隐藏提示层
        if (isPortrait) {
            tipElement.style.display = 'flex';
        } else {
            tipElement.style.display = 'none';
        }
    }
    
    // 检测是否为移动设备
    function isMobileDevice() {
        return (
            navigator.userAgent.match(/Android/i) ||
            navigator.userAgent.match(/webOS/i) ||
            navigator.userAgent.match(/iPhone/i) ||
            navigator.userAgent.match(/iPad/i) ||
            navigator.userAgent.match(/iPod/i) ||
            navigator.userAgent.match(/BlackBerry/i) ||
            navigator.userAgent.match(/Windows Phone/i)
        );
    }
    
    // 在页面加载完成后执行
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            checkOrientation();
        });
    } else {
        checkOrientation();
    }
    
    // 在屏幕方向改变时重新检查
    window.addEventListener('resize', checkOrientation);
    window.addEventListener('orientationchange', checkOrientation);
    
    // console.log('菜单修改器已加载到Swiper库中');
})();

