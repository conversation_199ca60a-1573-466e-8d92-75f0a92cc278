<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-登录</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/login.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body>
		<div class="content">
			<div class="ctop1">
				<div>
					<img class="logo" src="img/logo.png" />
					<label class="title">陕西中医药大学思政金课一体化平台</label>
				</div>
				<div class="loginview">
					<a href="index.html" class="backhome">返回主页</a>
				</div>
			</div>
			<div class="boxview">
				<!-- 登录框 -->
				<div class="loginbox" id="box1">
					<div class="logintitle">登录</div>
					<div class="inputbox">
						<img src="img/userno.png" />
						<input id="userno" type="tel" placeholder="请输入学号" />
					</div>
					<div class="inputbox">
						<img src="img/pass.png" />
						<input id="password" type="password" placeholder="请输入密码" />
					</div>
					<div class="yzmbox">
						<input id="code" onkeyup="value=value.replace(/[^\w\.\/]/ig,'')" maxlength="4" placeholder="请输入验证码" />
						<img id="yzm" class="yzm" src="" onclick="getyzm()" />
						<img src="img/loading.png" id="loadimg" />
					</div>
					<div class="wjpass">
						<label onclick="wjpassword()">忘记密码</label>
					</div>
					<div class="btnview">
						<button onclick="login()">登录</button>
					</div>
					<img class="loginbag1" src="img/gz1.png" />
					<img class="loginbag2" src="img/gz2.png" />
				</div>
				<!-- 忘记密码1 -->
				<div class="loginbox" id="box2">
					<div class="logintitle">忘记密码</div>
					<div class="titlebox">
						<label class="aaaa">输入账号</label>
						<img src="img/jtright.png" />
						<label>验证安全问题</label>
						<img src="img/jtright.png" />
						<label>设置新密码</label>
					</div>
					<div class="inputbox2">
						<div class="iiititle">账号:</div>
						<input type="tel" id="wjmmuserno" />
					</div>
					<div class="inputbox2">
						<div class="iiititle">验证码:</div>
						<div class="yzmbox">
							<input type="tel" onkeyup="value=value.replace(/[^\w\.\/]/ig,'')" id="wjmmcode" maxlength="4" placeholder="请输入验证码" />
							<img id="yzm2" class="yzm" src="img/banner.png" onclick="getyzm()" />
							<img src="img/loading.png" id="loadimg2" />
						</div>
					</div>
					<div class="btnview2">
						<button class="xyb" onclick="submit1()">下一步</button>
						<button class="backlogin" onclick="backlogin()">返回登录</button>
					</div>
					<img class="loginbag1" src="img/gz1.png" />
					<img class="loginbag2" src="img/gz2.png" />
				</div>
				<!-- 忘记密码2 -->
				<div class="loginbox" id="box3">
					<div class="logintitle">忘记密码</div>
					<div class="titlebox">
						<label>输入账号</label>
						<img src="img/jtright.png" />
						<label class="aaaa">验证安全问题</label>
						<img src="img/jtright.png" />
						<label>设置新密码</label>
					</div>
					<div class="inputbox2">
						<div class="iiititle">安全问题:</div>
						<ul class="input" id="wjmm2q">这是一个安全问题</ul>
					</div>
					<div class="inputbox2">
						<div class="iiititle">答案:</div>
						<input type="tel" id="wjmm2a" />
					</div>
					<div class="btnview2">
						<button class="xyb" onclick="submit2()">下一步</button>
						<button class="backlogin" onclick="backlogin()">返回登录</button>
					</div>
					<img class="loginbag1" src="img/gz1.png" />
					<img class="loginbag2" src="img/gz2.png" />
				</div>
				<!-- 忘记密码3 -->
				<div class="loginbox" id="box4">
					<div class="logintitle">忘记密码</div>
					<div class="titlebox">
						<label>输入账号</label>
						<img src="img/jtright.png" />
						<label>验证安全问题</label>
						<img src="img/jtright.png" />
						<label class="aaaa">设置新密码</label>
					</div>
					<div class="inputbox2">
						<div class="iiititle">设置新密码:</div>
						<input type="password" id="newpassword1" />
					</div>
					<div class="inputbox2">
						<div class="iiititle">确认新密码:</div>
						<input type="password" id="newpassword2" />
					</div>
					<div class="btnview2">
						<button class="xyb" onclick="submit3()">完成</button>
						<button class="backlogin" onclick="backlogin()">返回登录</button>
					</div>
					<img class="loginbag1" src="img/gz1.png" />
					<img class="loginbag2" src="img/gz2.png" />
				</div>
				<!-- 设置安全问题 -->
				<div class="loginbox" id="box5">
					<div class="logintitle">设置安全问题</div>
					<div class="title2">初次登录需设置安全问题</div>
					<div class="inputbox2">
						<div class="iiititle">选择安全问题:</div>
						<select id="question">
							<option></option>
						</select>
					</div>
					<div class="inputbox2">
						<div class="iiititle">输入问题答案:</div>
						<input type="text" id="answer" />
					</div>
					<div class="inputbox2">
						<div class="iiititle">验证码:</div>
						<div class="yzmbox">
							<input type="tel" onkeyup="value=value.replace(/[^\w\.\/]/ig,'')" id="yzm4" maxlength="4" placeholder="请输入验证码" />
							<img class="yzm" id="yzm3" src="img/banner.png" onclick="getyzm()" />
							<img src="img/loading.png" id="loadimg3" />
						</div>
					</div>
					<div class="btnview" style="padding-bottom: 0">
						<button onclick="setquestion()">确定</button>
					</div>
					<div class="tiaoguo"><label onclick="inhome()">跳过</label></div>
					<img class="loginbag1" src="img/gz1.png" />
					<img class="loginbag2" src="img/gz2.png" />
				</div>
			</div>
		</div>
		<div class="bagview">
			<img class="bag1" src="img/loginbag1.png" />
			<img class="bag2" src="img/loginbagg.png" />
		</div>
		<div class="footer">
			<a target="_blank" style="color:#FFFFFF; font-size: 0.833333rem;display: flex;align-items: center;" href="https://beian.miit.gov.cn/#/Integrated/index" class="ba">陕ICP备05001612号-1<img src="img/ba.png"/>陕公网安备 61040202000395号</a>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let isloading = false
			$(function() {
				//window.location = ''
				//window.location = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://127.0.0.1:8848/%E9%99%95%E4%B8%AD%E5%8C%BB%E6%80%9D%E6%94%BF/index.html'
				 getyzm()
				 getquestion()
				 showbox("box1")
			})
			//获取验证码
			function getyzm() {
				if (!isloading) {
					showloading()
					let url = baseurl + "/captcha"
					let xhr = new XMLHttpRequest()
					xhr.open('GET', url, true)
					xhr.responseType = 'blob'
					xhr.onload = function() {
						if (this.status === 200) {
							let blob = new Blob([xhr.response], {
								type: 'image/png'
							});
							let src = window.URL.createObjectURL(blob)
							$("#yzm").attr("src", src)
							$("#yzm2").attr("src", src)
							$("#yzm3").attr("src", src)
							hideloading()
						} else {
							hideloading()
							getyzm()
						}
					}
					xhr.send()
				}
			}
			
			function showloading() { //显示loading图标
				$("#loadimg").css("display", "block")
				$("#loadimg2").css("display", "block")
				$("#loadimg3").css("display", "block")
				isloading = true
			}
			
			function hideloading() { //隐藏loading图标
				$("#loadimg").css("display", "none")
				$("#loadimg2").css("display", "none")
				$("#loadimg3").css("display", "none")
				isloading = false
			}
			//点击忘记密码
			function wjpassword() {
				// console.log("忘记密码")
				hidebox("box1")
				showbox("box2")
				getyzm()
			}
			//点击返回登录
			function backlogin() {
				showbox("box1")
				hidebox("box2")
				hidebox("box3")
				hidebox("box4")
				hidebox("box5")
				getyzm()
			}
			
			function showbox(name) {
				$("#" + name).css("transform", "translateY(0)")
			}
			
			function hidebox(name) {
				$("#" + name).css("transform", "translateY(800px)")
			}
			
			let islogin = true
			
			let questionName = null
			let answer = null
			let questionList = null
			let identifier = null
			//登录
			function login() {
				let json = {
					identifier: $("#userno").val(),
					credential: $("#password").val(),
					code: $("#code").val()
				}
				if(!json.identifier){
					cocoMessage.warning(1000, "请输入学号！")
				}else if(!json.credential){
					cocoMessage.warning(1000, "请输入密码！")
				}else if(!json.code){
					cocoMessage.warning(1000, "请输入验证码！")
				}else{
					if(islogin){
						islogin = false
						$.ajax({
							url: baseurl + "/student/login",
							type: 'POST',
							data: JSON.stringify(json),
							contentType: "application/json",
							dataType: 'json',
							success: (res) => {
								islogin = true
								if(res.code == "200"){
									cocoMessage.success(1000, "登录成功！")
									//储存token
									sessionStorage.setItem('header',res.data.scheme+""+res.data.token)
									//储存用户信息
									sessionStorage.setItem('userinfo',JSON.stringify(res.data.student))
									//判断用户是否设置过安全问题
									if(res.data.student.questionName){
										//已有安全问题 则跳转到主页
										inhome()
									}else{
										//没有安全问题 则显示设置安全问题弹窗
										showbox("box5")
										hidebox("box1")
										hidebox("box2")
										hidebox("box3")
										hidebox("box4")
									}
								}else{
									cocoMessage.error(1000, res.message)
								}
								getyzm()
							},
							error: (err) => {
								cocoMessage.error(1000, "登录失败！请稍后重试！")
								getyzm()
								islogin = true
							}
						})
					}
				}
			}
			function inhome(){//进入主页
				window.location.href = 'index.html'
			}
			//下一步  忘记密码1
			function submit1(){
				let json = {
					identifier: $("#wjmmuserno").val(),
					code: $("#wjmmcode").val()
				}
				if(!json.identifier){
					cocoMessage.warning(1000, "请输入学号！")
				}else if(!json.code){
					cocoMessage.warning(1000, "请输入验证码！")
				}else{
					if(islogin){
						islogin = false
						identifier = $("#wjmmuserno").val()
						$.ajax({
							url: baseurl + "/student/pwd/step/one",
							type: 'POST',
							data: JSON.stringify(json),
							contentType: "application/json",
							dataType: 'json',
							success: (res) => {
								islogin = true
								if(res.code == "200"){
									questionName = res.data.questionName
									answer = res.data.answer
									$("#wjmm2q").html(questionName)
									// console.log(questionName,answer)
									if(questionName){
										//已经设置过安全问题 则下一步
										showbox("box3")
										hidebox("box1")
										hidebox("box2")
										hidebox("box4")
										hidebox("box5")
									}else{
										//还没有设置过安全问题 提示初始密码
										cocoMessage.warning(5000, "未设置过安全问题！请使用初始密码或者联系老师！")
									}
								}else{
									cocoMessage.error(1000, res.message)
								}
								getyzm()
							},
							error: (err) => {
								islogin = true
								getyzm()
							}
						})
					}
				}
			}
			//获取安全问题列表
			function getquestion(){
				$.ajax({
					url: baseurl + "/question",
					type: 'GET',
					contentType: "application/json",
					dataType: 'json',
					success: (res) => {
						questionList = res.data
						if(questionList!==null){
							if(questionList.length>0){
								questionList.map((item)=>{
									$("#question").append('<option value="'+item.id+'">'+item.name+'</option>')
								})
							}
						}
					}
				})
			}
			//设置安全问题
			function setquestion(){
				let userinfo = JSON.parse(sessionStorage.getItem("userinfo"))
				let json = {
					id: userinfo.id,
					questionId: $("#question").val(),
					answer: $("#answer").val(),
					code: $("#yzm4").val()
				}
				if(!json.questionId){
					cocoMessage.warning(1000, "请选择安全问题！")
				}else if(!json.answer){
					cocoMessage.warning(1000, "请输入安全问题答案！")
				}else if(!json.code){
					cocoMessage.warning(1000, "请输入验证码！")
				}else{
					if(islogin){
						islogin = false
						$.ajax({
							url: baseurl + "/student/question",
							type: 'POST',
							data: JSON.stringify(json),
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							contentType: "application/json",
							dataType: 'json',
							success: (res) => {
								if(res.code == "200"){
									//设置成功 则直接进入主页
									cocoMessage.success(1000, "设置安全问题成功！")
									inhome()
								}else{
									//设置失败 则提示信息
									cocoMessage.error(1000, res.message)
								}
								islogin = true
								getyzm()
							},
							error: (err) => {
								cocoMessage.error(1000, "设置安全问题失败！请稍后重试！")
								getyzm()
								islogin = true
							}
						})
					}
				}
			}
			//下一步  忘记密码2
			function submit2(){
				let ma = $("#wjmm2a").val()
				if(!ma){
					cocoMessage.warning(1000, "请输入安全问题答案！")
				}else if(ma != answer){
					cocoMessage.warning(1000, "您输入的安全问题答案不正确！")
				}else{
					//安全问题匹配正确 跳到下一步
					showbox("box4")
					hidebox("box1")
					hidebox("box2")
					hidebox("box3")
					hidebox("box5")
				}
			}
			//设置新密码 
			function submit3(){
				let newpassword1 = $("#newpassword1").val()
				let newpassword2 = $("#newpassword2").val()
				if(!newpassword1){
					cocoMessage.warning(1000, "请输入新密码！")
				}else if(!newpassword2){
					cocoMessage.warning(1000, "请确认新密码！")
				}else if(newpassword1!=newpassword2){
					cocoMessage.warning(1000, "两次密码不一致！")
				}else{
					if(islogin){
						islogin = false
						let json = {
							identifier: identifier,
							passWord: newpassword1
						}
						$.ajax({
							url: baseurl + "/student/pwd/step/three",
							type: 'POST',
							data: JSON.stringify(json),
							contentType: "application/json",
							dataType: 'json',
							success: (res) => {
								if(res.code == "200"){
									//设置成功 则直接进入主页
									cocoMessage.success(1000, "修改密码成功！")
									showbox("box1")
									hidebox("box2")
									hidebox("box3")
									hidebox("box4")
									hidebox("box5")
								}else{
									//设置失败 则提示信息
									cocoMessage.error(1000, res.message)
								}
								islogin = true
							},
							error: (err) => {
								cocoMessage.error(1000, "修改密码失败！请稍后重试！")
								islogin = true
							}
						})
					}
				}
			}
		</script>
	</body>
</html>
