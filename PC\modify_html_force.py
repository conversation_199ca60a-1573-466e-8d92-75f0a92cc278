import os
import re
import glob

def add_script_to_html_files():
    """
    在所有HTML文件的head标签中添加menu-modifier.js脚本引用，无论是否已经包含
    """
    # 查找当前目录下所有的HTML文件
    html_files = glob.glob("*.html")
    
    # 统计修改的文件数量
    modified_count = 0
    
    for file_path in html_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 查找head标签
            head_pattern = re.compile(r'(<head[^>]*>)', re.IGNORECASE)
            match = head_pattern.search(content)
            
            if match:
                # 计算插入位置
                insert_pos = match.end()
                
                # 构建要插入的脚本标签
                script_tag = '\n\t'
                
                # 首先移除任何现有的menu-modifier.js引用
                menu_script_pattern = re.compile(r'<script[^>]*src="[^"]*menu-modifier\.js"[^>]*>[^<]*</script>', re.IGNORECASE)
                content = menu_script_pattern.sub('', content)
                
                # 在head标签后插入新的脚本标签
                modified_content = content[:insert_pos] + script_tag + content[insert_pos:]
                
                # 写入修改后的内容
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(modified_content)
                
                modified_count += 1
                print(f"修改成功: {file_path}")
            else:
                print(f"跳过 {file_path} - 未找到head标签")
        
        except Exception as e:
            print(f"处理 {file_path} 时出错: {str(e)}")
    
    print(f"\n总计修改了 {modified_count} 个HTML文件")

if __name__ == "__main__":
    add_script_to_html_files() 