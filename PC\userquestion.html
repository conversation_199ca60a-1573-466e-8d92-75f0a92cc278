<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-个人中心</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/nav-enhancement.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png"/>个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						
					</div>
				</div>
				<div class="boxright">
					<div class="righttopview">
						<label>个人信息</label>
					</div>
					<div class="passwordbox">
						<div class="pwtitle">
							<label>设置安全问题</label>
						</div>
						
						<div class="pwwtbox">
							<div class="aqwtstr">请选择安全问题:</div>
							<select id="question">
							</select>
						</div>
						
						<div class="editpasswordinputview">
							<div>请输入安全问题答案</div>
							<input id="questionda" type="text" />
						</div>
						
						
						<div class="editpasswordinputview">
							<div>请输入验证码</div>
							<div class="yzmbox">
								<input id="yzmcode" onkeyup="value=value.replace(/[^\w\.\/]/ig,'')" type="text" maxlength="4" />
								<img id="yzm" onclick="getyzm()" />
							</div>
						</div>
						
						
						<div class="editbtnview">
							<div id="bb1" class="querenbtnbtn" onclick="submitquestion()">确认设置</div>
							<div id="bb2" class="querenbtnbtn" style="display: none;">正在设置...</div>
							<div class="quxiaoquxiaobtn" onclick="backsssss()">取消</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				let userinfo = sessionStorage.getItem("userinfo")
				if (userinfo) {
					if(window.localStorage.getItem("jilu")){
						$.ajax({
							url: baseurl + "/study/record/add",
							type: 'post',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: window.localStorage.getItem("jilu"),
							dataType: 'json',
							success: (res) => {
								window.localStorage.clear()
							}
						})
					}
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
					getclass()
					getfooterlink()
					if(JSON.parse(userinfo).roleName == '老师'){
						$("#yz").attr('src','img/ls.png')
						//左侧菜单
						let html = '<a class="leftitem activeleftitem">个人信息</a>'+
							'<a href="studentachievement.html" class="leftitem">学生成绩统计</a>'+
							'<a href="studentlearning.html" class="leftitem">学习记录统计</a>'+
							'<a href="userexamination2.html" class="leftitem">考试管理</a>'+
							'<a href="achievements.html" class="leftitem">成果展示</a>'+
							'<a class="leftitem" href="releaseyourvoice2.html">发布心声</a>'
						$("#teambox").html(html)
						
						let html2 = '<div class="xx">'+
									'<div class="xxleft">教工号:</div>'+
									'<label class="xxnr">'+JSON.parse(userinfo).userAuth.identifier+'</label></div>'+
								'<div class="xx">'+
									'<div class="xxleft">姓名:</div>'+
									'<label class="xxnr">'+JSON.parse(userinfo).name+'</label><img src="img/jsico.png"/></div>'+
								'<div class="xx">'+
									'<div class="xxleft">院系:</div>'+
									'<label class="xxnr">xxxxx院</label></div>'+
								'<div class="xx">'+
									'<div class="xxleft">职称:</div>'+
									'<label class="xxnr">xxxxxx主任</label></div>'
						$("#info").html(html2)
					}else{
						$("#yz").attr('src','img/xs.png')
						//左侧菜单
						let html = '<a class="leftitem activeleftitem">个人信息</a>'+
							'<a href="learningrecords.html" class="leftitem">学习路径</a>'+
							'<a href="achievement.html" class="leftitem">考试成绩</a>'+
							'<a href="releaseyourvoice.html" class="leftitem">发布心声</a>'
						$("#teambox").html(html)
						// console.log(JSON.parse(userinfo))
						let html2 = '<div class="xx">'+
									'<div class="xxleft">学号:</div>'+
									'<label class="xxnr">'+JSON.parse(userinfo).userAuth.identifier+'</label></div>'+
								'<div class="xx">'+
									'<div class="xxleft">姓名:</div>'+
									'<label class="xxnr">'+JSON.parse(userinfo).name+'</label><img src="img/xsico.png"/></div>'+
								'<div class="xx">'+
									'<div class="xxleft">院系:</div>'+
									'<label class="xxnr">'+JSON.parse(userinfo).collegeList[0].name+'</label></div>'+
								'<div class="xx">'+
									'<div class="xxleft">专业:</div>'+
									'<label class="xxnr">'+JSON.parse(userinfo).collegeList[0].children[0].name+'</label></div>'+
								'<div class="xx">'+
									'<div class="xxleft">班级:</div>'+
									'<label class="xxnr">'+JSON.parse(userinfo).collegeList[0].children[0].children[0].name+'</label></div>'
						$("#info").html(html2)
					}
					getuserinfo(JSON.parse(userinfo).id)
					getquestion()
					getyzm()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
			})
			
			//返回上一页
			function backsssss(){
				history.back()
			}
			//设置安全问题
			function submitquestion(){
				let userinfo = JSON.parse(sessionStorage.getItem("userinfo"))
				let json = {
					id: userinfo.id,
					questionId: $("#question").val(),
					answer: $("#questionda").val(),
					code: $("#yzmcode").val()
				}
				if(!json.questionId){
					cocoMessage.warning(1000, "请选择安全问题！")
				}else if(!json.answer){
					cocoMessage.warning(1000, "请输入安全问题答案！")
				}else if(!json.code){
					cocoMessage.warning(1000, "请输入验证码！")
				}else if(json.code.length!=4){
					cocoMessage.warning(1000, "您输入的验证码格式不正确！")
				}else{
					$("#bb1").hide()
					$("#bb2").show()
					$.ajax({
						url: baseurl + "/student/question",
						type: 'POST',
						data: JSON.stringify(json),
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						contentType: "application/json",
						dataType: 'json',
						success: (res) => {
							if(res.code == "200"){
								//设置成功 则直接进入主页
								cocoMessage.success(1000, "设置安全问题成功！")
								setTimeout(()=>{
									$("#bb1").show()
									$("#bb2").hide()
									backsssss()
								},1000)
							}else{
								$("#bb1").show()
								$("#bb2").hide()
								//设置失败 则提示信息
								cocoMessage.error(1000, res.message)
							}
							getyzm()
						},
						error: (err) => {
							$("#bb1").show()
							$("#bb2").hide()
							cocoMessage.error(1000, "设置安全问题失败！请稍后重试！")
							getyzm()
						}
					})
				}
			}
			//获取验证码
			function getyzm() {
				let url = baseurl + "/captcha"
				let xhr = new XMLHttpRequest()
				xhr.open('GET', url, true)
				xhr.responseType = 'blob'
				xhr.onload = function() {
					if (this.status === 200) {
						let blob = new Blob([xhr.response], {
							type: 'image/png'
						});
						let src = window.URL.createObjectURL(blob)
						$("#yzm").attr("src", src)
					}
				}
				xhr.send()
			}
			//获取安全问题列表
			function getquestion(){
				$.ajax({
					url: baseurl + "/question",
					type: 'GET',
					contentType: "application/json",
					dataType: 'json',
					success: (res) => {
						questionList = res.data
						if(questionList!==null){
							if(questionList.length>0){
								questionList.map((item)=>{
									$("#question").append('<option value="'+item.id+'">'+item.name+'</option>')
								})
							}
						}
					}
				})
			}
			function getuserinfo(id){
				$.ajax({
					url: baseurl + "/student/"+id,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data)
						}
					}
				})
			}
			function getfooterlink(){
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item)=>{
								html+='<a href="'+item.url+'" target="_blank">'+item.name+'</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 						.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }
			
			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}
			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
