<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>安全登录 - 思政一体化平台</title>
    
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        
        .container {
            background: white;
            border-radius: 12px;
            box-shadow: 0 8px 32px rgba(0,0,0,0.1);
            max-width: 400px;
            width: 100%;
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .logo {
            width: 60px;
            height: 60px;
            background: rgba(255,255,255,0.2);
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
        }
        
        .title {
            font-size: 20px;
            font-weight: 600;
            margin-bottom: 5px;
        }
        
        .subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px 20px;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
            text-align: center;
        }
        
        .warning-icon {
            font-size: 48px;
            margin-bottom: 15px;
        }
        
        .warning-title {
            font-size: 18px;
            font-weight: 600;
            color: #856404;
            margin-bottom: 10px;
        }
        
        .warning-text {
            color: #856404;
            line-height: 1.5;
            margin-bottom: 15px;
        }
        
        .solution-box {
            background: #d1ecf1;
            border: 1px solid #bee5eb;
            border-radius: 8px;
            padding: 20px;
            margin-bottom: 25px;
        }
        
        .solution-title {
            font-size: 16px;
            font-weight: 600;
            color: #0c5460;
            margin-bottom: 10px;
        }
        
        .solution-text {
            color: #0c5460;
            line-height: 1.5;
            margin-bottom: 15px;
        }
        
        .btn {
            display: block;
            width: 100%;
            padding: 15px;
            background: #c00714;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-size: 16px;
            font-weight: 500;
            margin-bottom: 15px;
            transition: background 0.3s ease;
            border: none;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #a00610;
        }
        
        .btn.secondary {
            background: #6c757d;
        }
        
        .btn.secondary:hover {
            background: #5a6268;
        }
        
        .btn.outline {
            background: transparent;
            color: #c00714;
            border: 2px solid #c00714;
        }
        
        .btn.outline:hover {
            background: #c00714;
            color: white;
        }
        
        .steps {
            margin: 20px 0;
        }
        
        .step {
            display: flex;
            align-items: flex-start;
            margin-bottom: 15px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 6px;
        }
        
        .step-number {
            display: flex;
            align-items: center;
            justify-content: center;
            width: 24px;
            height: 24px;
            background: #c00714;
            color: white;
            border-radius: 50%;
            font-size: 12px;
            font-weight: bold;
            margin-right: 12px;
            flex-shrink: 0;
        }
        
        .step-text {
            flex: 1;
            font-size: 14px;
            line-height: 1.4;
        }
        
        .divider {
            text-align: center;
            margin: 25px 0;
            position: relative;
            color: #999;
            font-size: 14px;
        }
        
        .divider::before {
            content: '';
            position: absolute;
            top: 50%;
            left: 0;
            right: 0;
            height: 1px;
            background: #e0e0e0;
        }
        
        .divider span {
            background: white;
            padding: 0 15px;
            position: relative;
        }
        
        .status {
            padding: 10px;
            border-radius: 6px;
            margin-bottom: 15px;
            font-size: 14px;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="logo">思</div>
            <div class="title">思政一体化平台</div>
            <div class="subtitle">安全登录解决方案</div>
        </div>
        
        <div class="content">
            <!-- 问题警告 -->
            <div class="warning-box">
                <div class="warning-icon">⚠️</div>
                <div class="warning-title">检测到登录问题</div>
                <div class="warning-text">
                    移动端登录URL在统一认证系统中未注册，直接登录会导致页面错误。
                </div>
            </div>
            
            <!-- 解决方案 -->
            <div class="solution-box">
                <div class="solution-title">💡 推荐解决方案</div>
                <div class="solution-text">
                    使用PC端登录方式，避免认证系统错误，确保登录成功。
                </div>
            </div>
            
            <!-- 登录状态 -->
            <div id="loginStatus" class="status info">
                正在检查登录状态...
            </div>
            
            <!-- 操作按钮 -->
            <button class="btn" onclick="handleSafeLogin()">
                🔐 使用安全登录方式
            </button>
            
            <button class="btn outline" onclick="handleDirectPcLogin()">
                🖥️ 跳转到PC端登录
            </button>
            
            <div class="divider">
                <span>操作步骤</span>
            </div>
            
            <!-- 操作步骤 -->
            <div class="steps">
                <div class="step">
                    <div class="step-number">1</div>
                    <div class="step-text">点击"使用安全登录方式"按钮</div>
                </div>
                <div class="step">
                    <div class="step-number">2</div>
                    <div class="step-text">在统一认证页面输入用户名和密码</div>
                </div>
                <div class="step">
                    <div class="step-number">3</div>
                    <div class="step-text">登录成功后自动跳转回移动端</div>
                </div>
            </div>
            
            <div class="divider">
                <span>其他选项</span>
            </div>
            
            <button class="btn secondary" onclick="handleTestLogin()">
                🧪 测试登录功能
            </button>
            
            <button class="btn secondary" onclick="handleViewGuide()">
                📖 查看详细指南
            </button>
            
            <a href="index.html" class="btn secondary">
                🏠 返回首页
            </a>
        </div>
    </div>

    <script>
        // 防止页面被嵌入iframe
        if (window.top !== window.self) {
            window.top.location = window.location;
        }

        // 配置
        const CONFIG = {
            pcBaseUrl: 'https://szjx.sntcm.edu.cn',
            casLoginUrl: 'https://cas.sntcm.edu.cn/lyuapServer/login'
        };

        // 检查登录状态
        function checkLoginStatus() {
            const userinfo = sessionStorage.getItem('userinfo');
            const header = sessionStorage.getItem('header');
            const statusDiv = document.getElementById('loginStatus');
            
            if (userinfo && header) {
                try {
                    const user = JSON.parse(userinfo);
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `
                        ✅ 已登录<br>
                        用户：${user.name || user.realName || '未知'}
                    `;
                    
                    // 如果已登录，显示跳转选项
                    setTimeout(() => {
                        if (confirm('检测到您已登录，是否跳转到首页？')) {
                            window.location.href = 'index.html';
                        }
                    }, 1000);
                } catch (e) {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = '❌ 登录数据异常，建议重新登录';
                    // 清除异常数据
                    sessionStorage.removeItem('userinfo');
                    sessionStorage.removeItem('header');
                }
            } else {
                statusDiv.className = 'status info';
                statusDiv.innerHTML = '❓ 未登录状态';
            }
        }

        // 安全登录（使用PC端URL）
        function handleSafeLogin() {
            // 使用PC端的service URL
            const serviceUrl = CONFIG.pcBaseUrl + '/userinfo.html';
            const loginUrl = `${CONFIG.casLoginUrl}?service=${encodeURIComponent(serviceUrl)}`;
            
            // 记录登录方式
            sessionStorage.setItem('loginMethod', 'safe-pc-login');
            sessionStorage.setItem('loginTimestamp', Date.now().toString());
            
            // 显示提示
            alert('即将跳转到统一认证平台，请使用您的账号密码登录。\n\n注意：登录成功后会自动跳转回移动端。');
            
            // 跳转到CAS登录
            window.location.href = loginUrl;
        }

        // 直接PC端登录
        function handleDirectPcLogin() {
            if (confirm('将跳转到PC端网站，登录后需要手动返回移动端。\n\n是否继续？')) {
                window.location.href = CONFIG.pcBaseUrl + '/index.html';
            }
        }

        // 测试登录功能
        function handleTestLogin() {
            window.location.href = 'login-test.html';
        }

        // 查看详细指南
        function handleViewGuide() {
            window.location.href = 'login-guide.html';
        }

        // 页面加载完成后检查状态
        document.addEventListener('DOMContentLoaded', function() {
            checkLoginStatus();
            
            // 检查是否有错误参数
            const urlParams = new URLSearchParams(window.location.search);
            const error = urlParams.get('error');
            
            if (error === 'cas_callback_error') {
                const statusDiv = document.getElementById('loginStatus');
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '❌ 检测到CAS回调错误，请使用安全登录方式';
            }
        });

        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.error('页面错误:', e);
            const statusDiv = document.getElementById('loginStatus');
            statusDiv.className = 'status error';
            statusDiv.innerHTML = '❌ 页面运行出错，请刷新重试';
        });

        // 监听存储变化
        window.addEventListener('storage', function(e) {
            if (e.key === 'userinfo' || e.key === 'header') {
                checkLoginStatus();
            }
        });
    </script>
</body>
</html>
