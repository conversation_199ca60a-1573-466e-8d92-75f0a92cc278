@echo off
chcp 65001 > nul
title 思政一体化平台启动器

echo ===================================================
echo 思政一体化平台启动器
echo ===================================================
echo.

:: 检查Node.js版本
echo 检查Node.js版本...
node -v
if %errorlevel% neq 0 (
    echo 错误: 未检测到Node.js，请先安装Node.js
    pause
    exit /b 1
)

:: 检查npm版本
echo 检查npm版本...
npm -v
if %errorlevel% neq 0 (
    echo 错误: 未检测到npm，请先安装npm
    pause
    exit /b 1
)

:: 检查和安装依赖
echo 检查主目录依赖...
if not exist node_modules (
    echo 安装主目录依赖...
    call npm install express http-proxy-middleware cors
)

:: 检查代理服务器是否已运行（简化检查方式）
echo 检查代理服务器...
set PROXY_RUNNING=0
for /f "tokens=5" %%a in ('netstat -ano ^| find ":5500"') do set PROXY_RUNNING=1

if %PROXY_RUNNING%==0 (
    echo 代理服务器未运行，正在启动...
    
    :: 检查是否存在新的代理服务器文件
    if exist new-proxy.js (
        echo 使用新代理服务器配置
        start cmd /c "title 代理服务器 && node new-proxy.js"
    ) else if exist proxy-fixed.js (
        echo 使用修复版代理服务器配置
        start cmd /c "title 代理服务器 && node proxy-fixed.js"
    ) else if exist proxy.js (
        echo 使用原始代理服务器配置
        start cmd /c "title 代理服务器 && node proxy.js"
    ) else (
        echo 错误: 未找到任何代理服务器文件!
        pause
        exit /b 1
    )
    
    echo 等待代理服务器启动...
    timeout 5
) else (
    echo 代理服务器已在运行
)

:: 启动管理系统
echo.
echo 准备启动管理系统...
cd admin

:: 检查admin目录下的node_modules
if not exist node_modules (
    echo 管理系统依赖未安装，正在安装...
    call npm install --legacy-peer-deps --no-audit --no-fund
)

:: 启动管理系统
echo.
echo 启动管理系统...
start cmd /c "title 管理系统 && npm run serve"

:: 返回主目录
cd ..

echo.
echo ===================================================
echo 所有服务已启动
echo 代理服务器: http://**************:5500
echo 管理系统: http://localhost:8081
echo ===================================================
echo.
echo 批处理执行完成，请查看新打开的窗口

pause 