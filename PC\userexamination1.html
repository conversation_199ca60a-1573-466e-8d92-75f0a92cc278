<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>思政一体化平台-考试管理</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/wangEditor.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>
		<!-- 导航增强脚本 -->
		<script src="./js/nav-enhancement.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
		<style>
			/* 筛选区域样式 */
			.filter-container {
				background-color: #f8f9fa;
				border-radius: 8px;
				padding: 15px;
				margin-bottom: 20px;
				box-shadow: 0 2px 5px rgba(0,0,0,0.05);
			}
			
			.filter-row {
				display: flex;
				flex-wrap: wrap;
				gap: 10px;
				margin-bottom: 10px;
				align-items: center;
			}
			
			.filter-select {
				flex: 1;
				min-width: 150px;
				padding: 8px 12px;
				border: 1px solid #ced4da;
				border-radius: 4px;
				font-size: 0.9rem;
				background-color: white;
				transition: border-color 0.15s ease-in-out, box-shadow 0.15s ease-in-out;
			}
			
			.filter-select:focus {
				border-color: #80bdff;
				outline: 0;
				box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
			}
			
			.filter-label {
				font-weight: 600;
				color: #495057;
				margin-right: 8px;
			}
		</style>
		<style>
			#msmsms {
				text-align: center;
				font-size: 0.9375rem;
				color: #999999;
				margin-top: 10%;
			}
			.tabtitle{
				display: flex;
				align-items: center;
				width: 100%;
				background: #f0f4f8;
				font-weight: 600;
				border-radius: 6px 6px 0 0;
				box-shadow: 0 1px 3px rgba(0,0,0,0.05);
				height: 40px;
			}
			.tabtitle div{
				padding: 10px 4px;
				color: #495057;
				height: 100%;
				display: flex;
				align-items: center;
				justify-content: center;
				box-sizing: border-box;
			}
			.tabtitle div:first-child{
				width: 2.604167rem;
			}
			.tabtitle div:last-child{
				width: 8rem;
			}
			.tabtitle div.time-col, .tabtitle div.score-col {
				width: 5.208333rem;
			}
			.paperitem{
				display: flex;
				align-items: center;
				width: 100%;
				box-sizing: border-box;
				min-height: 40px;
				border-bottom: 1px solid #e9ecef;
				transition: background-color 0.2s;
				font-size: 0.85rem;
			}
			.paperitem:hover {
				background-color: #f8f9fa;
			}
			.paperitem span:first-child{
				width: 2.604167rem;
			}
			.paperitem span:last-child{
				width: 8rem;
				justify-content: space-around;
			}
			.paperitem span.time-col, .paperitem span.score-col {
				width: 5.208333rem;
			}
			.paperitem span.score-col {
				font-weight: bold;
				color: #ff6600;
			}
			.paperitem span{
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				padding: 8px 4px;
				display: flex;
				align-items: center;
				justify-content: center;
				box-sizing: border-box;
				height: 100%;
			}
			.paperitem span a.view-record {
				color: #1E90FF;
				margin: 0 0.26rem;
				cursor: pointer;
			}
			.paperitem span a.re-mark {
				color: #FF8C00;
				margin: 0 0.26rem;
				cursor: pointer;
			}
			.paperitem span a.mark-new {
				color: #28a745;
				margin: 0 0.26rem;
				cursor: pointer;
			}
			
			/* 新的按钮样式 */
			.action-btn {
				padding: 4px 8px;
				border-radius: 12px;
				font-size: 0.65rem;
				margin: 0 0.05rem;
				display: inline-flex;
				align-items: center;
				justify-content: center;
				min-width: 1.2rem;
				transition: all 0.3s ease;
				position: relative;
				overflow: hidden;
				box-shadow: 0 2px 5px rgba(0,0,0,0.1);
				border: none;
				font-weight: 500;
				cursor: pointer;
			}
			
			.btn-mark {
				background: linear-gradient(135deg, #28a745, #20c997);
				color: white !important;
			}
			
			.btn-remark {
				background: linear-gradient(135deg, #fd7e14, #ffc107);
				color: white !important;
			}
			
			.btn-view {
				background: linear-gradient(135deg, #007bff, #17a2b8);
				color: white !important;
			}
			
			/* 修改悬停颜色和效果 */
			.action-btn:hover {
				transform: translateY(-2px);
				box-shadow: 0 4px 8px rgba(0,0,0,0.15);
			}
			
			.action-btn:active {
				transform: translateY(1px);
				box-shadow: 0 1px 3px rgba(0,0,0,0.1);
			}
			
			/* 为按钮添加波纹效果 */
			.action-btn:after {
				content: "";
				display: block;
				position: absolute;
				width: 100%;
				height: 100%;
				top: 0;
				left: 0;
				pointer-events: none;
				background-image: radial-gradient(circle, #fff 10%, transparent 10.01%);
				background-repeat: no-repeat;
				background-position: 50%;
				transform: scale(10, 10);
				opacity: 0;
				transition: transform .3s, opacity .5s;
			}
			
			.action-btn:active:after {
				transform: scale(0, 0);
				opacity: .3;
				transition: 0s;
			}
			
			/* 自定义试卷下拉框样式 */
			.paper-option {
				padding: 8px 12px;
				cursor: pointer;
				font-size: 0.8rem;
				transition: background-color 0.2s;
				border-bottom: 1px solid #f1f1f1;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
			
			.paper-option:hover {
				background-color: #f8f9fa;
			}
			
			.paper-option-empty {
				padding: 10px;
				color: #6c757d;
				text-align: center;
				font-style: italic;
				font-size: 0.8rem;
			}
			
			.custom-select-wrapper {
				position: relative;
				width: 100%;
			}
			
			/* 美化滚动条 */
			#paperSelectDropdown::-webkit-scrollbar {
				width: 6px;
			}
			
			#paperSelectDropdown::-webkit-scrollbar-track {
				background: #f1f1f1;
				border-radius: 3px;
			}
			
			#paperSelectDropdown::-webkit-scrollbar-thumb {
				background: #c1c1c1;
				border-radius: 3px;
			}
			
			#paperSelectDropdown::-webkit-scrollbar-thumb:hover {
				background: #a8a8a8;
			}
			
			/* 确保试卷下拉框显示在最上层 */
			#paperSelectDropdown {
				z-index: 1000 !important;
			}
			
			/* 优化模糊搜索框的响应式布局 */
			#fuzzySearch {
				width: 100%;
				box-sizing: border-box;
				word-wrap: break-word;
				overflow-wrap: break-word;
			}
			
			/* 合并的学院专业班级列样式 */
			.combined-info {
				white-space: normal;
				overflow: hidden;
				text-overflow: ellipsis;
				position: relative;
				cursor: help;
			}
			
			/* 避免z-index上下文问题，确保悬浮元素显示在最上层 */
			.paperitem, .tabtitle {
				position: relative;
				z-index: 1;
			}
			
			.combined-info:hover::after, .combined-info:hover::before {
				display: none !important;
			}
			
			.combined-info:hover {
				z-index: 99999; /* 极高值确保在最上层 */
			}
			
			.combined-info:hover::after {
				content: attr(data-full);
				position: absolute;
				left: 0;
				top: 100%;
				margin-top: 5px;
				background: #fff;
				padding: 10px 15px;
				border: 1px solid #ddd;
				border-radius: 8px;
				box-shadow: 0 5px 15px rgba(0,0,0,0.2);
				z-index: 99999;
				white-space: normal;
				min-width: 250px;
				max-width: 300px;
				width: auto;
				color: #333;
				font-size: 0.75rem;
				line-height: 1.4;
				animation: fadeIn 0.2s ease-in-out;
				text-align: left;
				pointer-events: none;
			}
			
			.combined-info:hover::before {
				content: '';
				position: absolute;
				left: 20px;
				top: 100%;
				border-width: 5px;
				border-style: solid;
				border-color: transparent transparent #ddd transparent;
				z-index: 99999;
				pointer-events: none;
			}
			
			@keyframes fadeIn {
				from { opacity: 0; transform: translateY(-5px); }
				to { opacity: 1; transform: translateY(0); }
			}
			
			/* 减小列表字体 */
			.tabtitle, .paperitem {
				font-size: 0.75rem !important;
			}
			
			/* 确保表头和内容对齐 */
			.tabtitle div, .paperitem span {
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				padding: 4px;
				display: flex;
				align-items: center;
				justify-content: center;
			}
			
			/* 确保移动设备上有更好的显示效果 */
			@media (max-width: 768px) {
				.custom-select-wrapper, 
				#fuzzySearch {
					min-width: 100%;
					margin-bottom: 8px;
				}
				
				#paperxk {
					min-width: 100%;
					max-width: 100%;
					margin-bottom: 8px;
				}
			}
			
			.record-box {
				width: 100%;
				max-height: 70vh;
				overflow-y: auto;
				background: #fff;
				padding: 1rem;
				border-radius: 0.5rem;
			}
			.record-title {
				font-size: 1.2rem;
				font-weight: bold;
				margin-bottom: 1rem;
				text-align: center;
			}
			.record-item {
				margin-bottom: 1rem;
				border-bottom: 1px solid #eee;
				padding-bottom: 1rem;
			}
			.record-question {
				font-weight: bold;
				margin-bottom: 0.5rem;
			}
			.record-score {
				color: #ff6600;
				margin-right: 1rem;
			}
		</style>
		<style>
			/* 总分颜色样式 */
			.score-pass {
				color: #28a745 !important; /* 绿色表示及格 */
				font-weight: 600;
			}
			
			.score-fail {
				color: #dc3545 !important; /* 红色表示不及格 */
				font-weight: 600;
			}
			
			/* 小型操作按钮 */
		</style>
		<style>
			/* 统一列宽计算方式及对齐方式 */
			.col-sn {
				width: 3rem !important;
				flex: 0 0 3rem !important; /* 精确控制宽度 */
				text-align: center;
				justify-content: center;
			}
			
			.col-info {
				/* flex: 1 1 auto;
				width: 2rem !important; */
				vertical-align: middle;
			}
			
			/* 学院专业班级特殊处理 */
			.col-school {
				font-size: 0.7rem;
				line-height: 1.2;
				white-space: normal !important;
				height: auto !important;
				min-height: 2.5rem;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				overflow: hidden;
				text-overflow: ellipsis;
				padding: 4px 2px !important;
				width: 8rem !important;
				flex: 0 0 8rem !important; /* 精确控制宽度 */
				justify-content: center;
			}
			
			/* 姓名列特殊处理 */
			.col-name {
				text-align: center;
				width: 5rem !important;
				flex: 0 0 5rem !important; /* 精确控制宽度 */
				justify-content: center;
			}
			
			/* 学号列特殊处理 */
			.col-id {
				text-align: center;
				width: 10rem !important;
				flex: 0 0 10rem !important; /* 精确控制宽度 */
				justify-content: center;
			}
			
			.col-time {
				width: 10rem !important;
				flex: 0 0 10rem !important; /* 精确控制宽度 */
				font-size: 0.7rem;
				line-height: 1.2;
				white-space: normal !important;
				padding: 4px 2px !important;
				justify-content: center;
			}
			
			.col-score {
				width: 8rem !important;
				flex: 0 0 8rem !important; /* 精确控制宽度 */
				text-align: center;
				font-weight: bold;
				justify-content: center;
			}
			
			.col-action {
				width: 5rem !important;
				flex: 0 0 5rem !important; /* 精确控制宽度 */
				text-align: center;
				justify-content: center;
			}
			
			/* 表头样式加强 - 确保垂直居中 */
			.tabtitle {
				display: flex;
				align-items: center; /* 确保垂直居中 */
				width: 100%;
				background: #f0f4f8;
				font-weight: 600;
				border-radius: 6px 6px 0 0;
				box-shadow: 0 1px 3px rgba(0,0,0,0.05);
				min-height: 40px;
				height: auto;
			}
			
			/* 确保表头div和内容span完全一致的布局和对齐方式 */
			.tabtitle div, 
			.paperitem span {
				padding: 10px 4px;
				display: flex;
				align-items: center;
				justify-content: center;
				box-sizing: border-box;
				text-align: center;
				overflow: hidden;
				flex-shrink: 0; /* 防止收缩 */
				flex-grow: 0; /* 防止扩展 */
			}
			
			/* 表头文字颜色 */
			.tabtitle div {
				color: #495057;
			}
			
			/* 确保表格布局正确填充容器宽度 */
			.paperitem {
				display: flex;
				align-items: center; /* 确保垂直居中 */
				width: 100%;
				box-sizing: border-box;
				min-height: 40px;
				border-bottom: 1px solid #e9ecef;
				transition: background-color 0.2s;
			}
			
			/* 统一列宽计算方式及对齐方式 - 确保所有列完全一致的宽度 */
			.tabtitle .col-sn, .paperitem .col-sn {
				width: 3rem !important;
				flex: 0 0 3rem !important; /* 精确控制宽度 */
				text-align: center;
				justify-content: center;
			}
			
			.tabtitle .col-school, .paperitem .col-school {
				font-size: 0.7rem;
				line-height: 1.2;
				white-space: normal !important;
				height: auto !important;
				min-height: 2.5rem;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
				overflow: hidden;
				text-overflow: ellipsis;
				padding: 4px 2px !important;
				width: 8rem !important;
				flex: 0 0 8rem !important; /* 精确控制宽度 */
				justify-content: center;
			}
			
			.tabtitle .col-name, .paperitem .col-name {
				text-align: center;
				width: 5rem !important;
				flex: 0 0 5rem !important; /* 精确控制宽度 */
				justify-content: center;
			}
			
			.tabtitle .col-id, .paperitem .col-id {
				text-align: center;
				width: 10rem !important;
				flex: 0 0 10rem !important; /* 精确控制宽度 */
				justify-content: center;
			}
			
			.tabtitle .col-time, .paperitem .col-time {
				width: 10rem !important;
				flex: 0 0 10rem !important; /* 精确控制宽度 */
				font-size: 0.7rem;
				line-height: 1.2;
				white-space: normal !important;
				padding: 4px 2px !important;
				justify-content: center;
			}
			
			.tabtitle .col-score, .paperitem .col-score {
				width: 8rem !important;
				flex: 0 0 8rem !important; /* 精确控制宽度 */
				text-align: center;
				font-weight: bold;
				justify-content: center;
			}
			
			.tabtitle .col-action, .paperitem .col-action {
				width: 5rem !important;
				flex: 0 0 5rem !important; /* 精确控制宽度 */
				text-align: center;
				justify-content: center;
			}
		</style>
		<style>
		/* 全局提示框样式 */
		#global-tooltip {
			display: none;
			position: fixed;
			z-index: 999999;
			background: #fff;
			padding: 10px 15px;
			border: 1px solid #ddd;
			border-radius: 8px;
			box-shadow: 0 5px 15px rgba(0,0,0,0.2);
			min-width: 250px;
			max-width: 300px;
			font-size: 0.75rem;
			line-height: 1.4;
			color: #333;
			text-align: left;
			pointer-events: none;
		}
		</style>
		<!-- 添加新的表格样式 -->
		<style>
		.exam-table {
		  width: 100%; /* This makes the table responsive to its container */
		  border-collapse: collapse;
		  table-layout: fixed;
		  margin-bottom: 20px;
		  font-size: 0.8rem;
		}

		.exam-table thead th {
		  background: #e9ecef;
		  color: #495057;
		  font-weight: 600;
		  text-align: center;
		  padding: 10px 8px;
		  border: 1px solid #dee2e6;
		  border-bottom-width: 2px;
		  vertical-align: middle;
		  white-space: nowrap;
		}

		.exam-table tbody td {
		  padding: 8px 8px;
		  text-align: center;
		  border: 1px solid #e9ecef;
		  vertical-align: middle;
		  line-height: 1.3;
		  white-space: nowrap; /* Ensure single line */
		  /* overflow: hidden; REMOVED to allow content to be fully visible */
		  /* text-overflow: ellipsis; REMOVED */
		}

		.exam-table tbody tr:nth-child(even) {
		  background-color: #f8f9fa;
		}

		.exam-table tbody tr:hover {
		  background-color: #e2e6ea;
		}

		/* Updated Column Widths */
		.exam-table .w-seq { width: 2rem; } /* Reduced */
		.exam-table .w-class { width: 9rem; } /* Increased */
		.exam-table .w-name { width: 5rem; } /* Kept */
		.exam-table .w-id { width: 11rem; } /* Increased */
		.exam-table .w-time { width: 11rem; } /* Increased */
		.exam-table .w-score { width: 4rem; } /* Reduced */
		.exam-table .w-action { width: 7rem; } /* Increased for button spacing */

		.exam-table .score-pass { color: #28a745 !important; font-weight: 600; }
		.exam-table .score-fail { color: #dc3545 !important; font-weight: 600; }

		/* Class column specific style - ensure single line */
		.exam-table .class-col {
		  white-space: nowrap;
		  /* overflow: hidden; REMOVED */
		  /* text-overflow: ellipsis; REMOVED */
		}

		/* Action Button Styles (no change from previous step) */
		.action-btn {
		  padding: 6px 12px;
		  border-radius: 4px;
		  font-size: 0.75rem;
		  margin: 2px;
		  display: inline-block;
		  text-decoration: none;
		  border: none;
		  font-weight: 500;
		  cursor: pointer;
		  transition: all 0.2s ease;
		  color: white !important;
		  text-align: center;
		  vertical-align: middle;
		  min-width: auto;
		}

		.btn-mark { background-color: #28a745; }
		.btn-mark:hover { background-color: #218838; transform: translateY(-1px); box-shadow: 0 2px 4px rgba(0,0,0,0.1); }

		.btn-remark { background-color: #fd7e14; }
		.btn-remark:hover { background-color: #e66a00; transform: translateY(-1px); box-shadow: 0 2px 4px rgba(0,0,0,0.1); }

		.btn-view { background-color: #007bff; }
		.btn-view:hover { background-color: #0069d9; transform: translateY(-1px); box-shadow: 0 2px 4px rgba(0,0,0,0.1); }

		.action-btn:active { transform: translateY(0px); box-shadow: inset 0 1px 2px rgba(0,0,0,0.1); }

		.exam-table .w-action a + a { margin-left: 4px; }
		</style>
		
		<!-- 新增阅卷弹窗样式 -->
		<style>
			/* 阅卷弹窗基础样式 - 党政红风格 */
			#tbox {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(0,0,0,0.65);
				z-index: 9999;
				display: none;
				align-items: center;
				justify-content: center;
				overflow: hidden;
				backdrop-filter: blur(5px);
				-webkit-backdrop-filter: blur(5px);
				padding: 20px;
				box-sizing: border-box;
				margin-top: -6rem !important;
			}
			
			.yuepaperbox {
				width: 95%;
				max-width: 1400px;
				max-height: 700px;
				min-height: 300px;
				background: #f8f9fa;
				border-radius: 16px;
				box-shadow: 0 20px 60px rgba(0,0,0,0.3);
				overflow: hidden;
				display: flex;
				flex-direction: column;
				animation: fadeInScale 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
				margin: 0;
				margin-top: -18rem;
				position: relative;
			}
			
			@keyframes fadeInScale {
				from { 
					opacity: 0; 
					transform: scale(0.85) translateY(-30px); 
				}
				to { 
					opacity: 1; 
					transform: scale(1) translateY(0); 
				}
			}
			
			.yuepaperview {
				width: 100%;
				height: 100%;
				display: flex;
				flex-direction: column;
				overflow: hidden;
				min-height: 0;
			}
			
			/* 头部样式 - 党政红 */
			.yuepapaertoptitle {
				background: white;
				padding: 0;
				border-bottom: 1px solid #e9ecef;
				flex-shrink: 0;
			}
			
			.ypapertitle {
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 8px 20px;
				background: linear-gradient(135deg, #b80d0d, #9c0000);
				position: relative;
				overflow: hidden;
				min-height: 30px;
			}
			
			.ypapertitle:before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-image: url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M11 18c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm48 25c3.866 0 7-3.134 7-7s-3.134-7-7-7-7 3.134-7 7 3.134 7 7 7zm-43-7c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm63 31c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM34 90c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zm56-76c1.657 0 3-1.343 3-3s-1.343-3-3-3-3 1.343-3 3 1.343 3 3 3zM12 86c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm28-65c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm23-11c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-6 60c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm29 22c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zM32 63c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm57-13c2.76 0 5-2.24 5-5s-2.24-5-5-5-5 2.24-5 5 2.24 5 5 5zm-9-21c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM60 91c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM35 41c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2zM12 60c1.105 0 2-.895 2-2s-.895-2-2-2-2 .895-2 2 .895 2 2 2z' fill='%23ffffff' fill-opacity='0.1' fill-rule='evenodd'/%3E%3C/svg%3E");
				opacity: 0.3;
				pointer-events: none;
			}
			
			.ypapertitle label {
				font-size: 1.2rem;
				font-weight: 600;
				color: white;
				margin: 0;
				text-shadow: 0 2px 4px rgba(0,0,0,0.3);
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				max-width: calc(100% - 80px);
				line-height: 1.2;
				text-align: center;
			}
			
			.paperyueclose {
				width: 28px;
				height: 28px;
				cursor: pointer;
				position: absolute;
				right: 40px;
				top: 50%;
				transform: translateY(-50%);
				opacity: 0.9;
				transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
				background: rgba(255,255,255,0.2);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				z-index: 100;
				border: 1px solid rgba(255,255,255,0.3);
			}
			
			.paperyueclose:hover {
				opacity: 1;
				transform: translateY(-50%) rotate(90deg) scale(1.1);
				background: rgba(255,255,255,0.35);
				box-shadow: 0 4px 12px rgba(255,255,255,0.3);
				border-color: rgba(255,255,255,0.5);
			}
			
			.paperyueclose:before, .paperyueclose:after {
				content: '';
				position: absolute;
				width: 2px;
				height: 14px;
				background: white;
				border-radius: 1px;
			}
			
			.paperyueclose:before {
				transform: rotate(45deg);
			}
			
			.paperyueclose:after {
				transform: rotate(-45deg);
			}
			
			.ypaperbottom {
				display: flex;
				flex-wrap: wrap;
				padding: 16px 20px;
				background: white;
				gap: 15px;
				box-shadow: 0 2px 10px rgba(0,0,0,0.05);
				position: relative;
				z-index: 5;
				flex-shrink: 0;
				border-bottom: 1px solid #f0f0f0;
			}
			
			.ypaperbottom label {
				font-size: 0.9rem;
				color: #495057;
				margin: 0;
				display: flex;
				align-items: center;
				background: #f8f9fa;
				padding: 8px 12px;
				border-radius: 6px;
				box-shadow: 0 1px 3px rgba(0,0,0,0.05);
				transition: all 0.2s ease;
				flex: 0 0 auto;
				border: 1px solid #e9ecef;
			}
			
			.ypaperbottom label:hover {
				background: #e9ecef;
				box-shadow: 0 2px 6px rgba(0,0,0,0.08);
				transform: translateY(-1px);
				border-color: #dee2e6;
			}
			
			.ypaperbottom label span {
				font-weight: 600;
				color: #2c3e50;
				margin-left: 6px;
				max-width: 200px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				display: inline-block;
			}
			
			/* 内容区域样式 */
			.ypaperbox {
				display: flex;
				flex: 1;
				overflow: hidden;
				min-height: 0;
			}
			
			.ypaperboxleft {
				flex: 1;
				padding: 25px;
				overflow-y: auto;
				background: white;
				border-right: 1px solid #e9ecef;
				min-height: 0;
			}
			
			.ypaperboxright {
				width: 220px;
				background: #f8f9fa;
				padding: 20px;
				display: flex;
				flex-direction: column;
				border-left: 1px solid #e9ecef;
				box-shadow: -5px 0 15px rgba(0,0,0,0.03);
				flex-shrink: 0;
			}
			
			/* 题号列表样式 - 党政红 */
			.thlb {
				font-size: 0.9rem;
				font-weight: 600;
				color: #b80d0d;
				margin-top: 30px;
				padding-bottom: 10px;
				margin-bottom: 10px;
				border-bottom: 1px solid #e9ecef;
				text-align: center;
				position: relative;
			}
			
			.thlb:after {
				margin-top: 20px;
				content: '';
				position: absolute;
				bottom: -1px;
				left: 50%;
				transform: translateX(-50%);
				width: 40px;
				height: 1px;
				background: #b80d0d;
				border-radius: 1px;
			}
			
			.thbox {
				display: grid;
				grid-template-columns: repeat(auto-fill, minmax(28px, 1fr));
				gap: 6px;
				margin-bottom: 12px;
				overflow-y: auto;
				padding: 8px;
				flex: 1;
				max-height: calc(100% - 140px);
				background: #ffffff;
				border-radius: 6px;
				border: 1px solid #f0f0f0;
				box-shadow: inset 0 1px 3px rgba(0,0,0,0.05);
			}
			
			.th-item {
				display: flex;
				align-items: center;
				justify-content: center;
				width: 28px;
				height: 28px;
				border-radius: 4px;
				background: #f8f9fa;
				color: #6c757d;
				font-weight: 600;
				font-size: 0.8rem;
				text-decoration: none;
				transition: all 0.2s ease;
				border: 1px solid #e9ecef;
				position: relative;
				z-index: 1;
			}
			
			.th-item:hover {
				background: #e9ecef;
				color: #495057;
				border-color: #ced4da;
				transform: none;
				box-shadow: 0 1px 3px rgba(0,0,0,0.1);
			}
			
			.th-item.active {
				background: #b80d0d;
				color: white;
				border-color: #b80d0d;
				box-shadow: 0 1px 4px rgba(184, 13, 13, 0.3);
				z-index: 2;
			}
			
			.th-item.active:hover {
				background: #d32f2f;
				border-color: #d32f2f;
				box-shadow: 0 2px 6px rgba(184, 13, 13, 0.4);
			}
			
			/* 按钮样式 - 党政红 */
			.btnvvv {
				margin-top: 12px;
				padding-top: 0;
				position: relative;
				z-index: 10;
			}
			
			.btnvvv div {
				display: flex;
				
				align-items: center;
				justify-content: center;
				width: 100%;
				padding: 10px 16px;
				background: linear-gradient(135deg, #b80d0d, #9c0000);
				color: white !important;
				text-align: center;
				border-radius: 6px;
				font-weight: 600;
				font-size: 0.9rem;
				cursor: pointer;
				transition: all 0.3s ease;
				box-shadow: 0 2px 8px rgba(184, 13, 13, 0.25);
				position: relative;
				overflow: hidden;
				border: none;
				min-height: 36px;
				line-height: 1;
			}
			
			.btnvvv div:hover {
				background: linear-gradient(135deg, #c00000, #ae0000);
				color: white !important;
				transform: translateY(-1px);
				box-shadow: 0 4px 12px rgba(184, 13, 13, 0.35);
			}
			
			.btnvvv div:active {
				transform: translateY(0px);
				
				box-shadow: 0 1px 4px rgba(184, 13, 13, 0.3);
			}
			
			/* 分数显示颜色 */
			.btnvvv div.score-fail {
				background: linear-gradient(135deg, #dc3545, #c82333) !important;
				color: white !important;
				box-shadow: 0 2px 8px rgba(220, 53, 69, 0.25) !important;
			}
			
			.btnvvv div.score-fail:hover {
				background: linear-gradient(135deg, #e04555, #d32f2f) !important;
				color: white !important;
				box-shadow: 0 4px 12px rgba(220, 53, 69, 0.35) !important;
			}
			
			.btnvvv div.score-pass {
				background: linear-gradient(135deg, #28a745, #20c997) !important;
				color: white !important;
				box-shadow: 0 2px 8px rgba(40, 167, 69, 0.25) !important;
			}
			
			.btnvvv div.score-pass:hover {
				background: linear-gradient(135deg, #34ce57, #28a745) !important;
				color: white !important;
				box-shadow: 0 4px 12px rgba(40, 167, 69, 0.35) !important;
			}
			
			/* 题目项样式 */
			.tmitem {
				background: white;
				border-radius: 12px;
				box-shadow: 0 3px 12px rgba(0,0,0,0.06);
				margin-bottom: 30px;
				overflow: hidden;
				transition: all 0.3s ease;
				border: 1px solid #e9ecef;
			}
			
			.tmitem:hover {
				box-shadow: 0 8px 25px rgba(0,0,0,0.1);
				transform: translateY(-2px);
				border-color: #dee2e6;
			}
			
			.tmname {
				padding: 18px 20px;
				background: #f8f9fa;
				border-bottom: 1px solid #e9ecef;
				display: flex;
				align-items: flex-start;
				position: relative;
			}
			
			.tmname:after {
				content: '';
				position: absolute;
				bottom: -1px;
				left: 0;
				height: 2px;
				width: 100%;
				background: linear-gradient(90deg, #b80d0d, transparent);
			}
			
			.tmname label {
				font-weight: 700;
				color: #b80d0d;
				margin-right: 15px;
				white-space: nowrap;
				background: rgba(184, 13, 13, 0.1);
				padding: 6px 12px;
				border-radius: 6px;
				font-size: 0.9rem;
				border: 1px solid rgba(184, 13, 13, 0.2);
			}
			
			.tmname div {
				color: #212529;
				line-height: 1.6;
				font-weight: 500;
				font-size: 1rem;
			}
			
			/* 学生答案区域 */
			.studentbox, .studentbox2 {
				padding: 20px;
				border-bottom: 1px solid #f1f1f1;
			}
			
			.bbicott, .bbicott2 {
				margin-bottom: 15px;
			}
			
			.bbicott label, .bbicott2 label {
				display: inline-block;
				padding: 8px 15px;
				border-radius: 6px;
				font-size: 0.9rem;
				font-weight: 600;
				color: #495057;
				box-shadow: 0 2px 6px rgba(0,0,0,0.06);
			}
			
			.bbicott label {
				background: #fdf2f2;
				color: #b80d0d;
				border-left: 4px solid #b80d0d;
			}
			
			.bbicott2 label {
				background: #f1f8f1;
				color: #28a745;
				border-left: 4px solid #28a745;
			}
			
			.studentstr {
				font-size: 0.95rem;
				line-height: 1.8;
				color: #333;
				padding: 18px;
				background: #f9f9fa;
				border-radius: 8px;
				white-space: pre-wrap;
				overflow-wrap: break-word;
				word-wrap: break-word;
				max-height: 300px;
				overflow-y: auto;
				box-shadow: inset 0 2px 6px rgba(0,0,0,0.05);
				border: 1px solid #eee;
			}
			
			/* 评语区域 */
			.py {
				padding: 20px;
				display: flex;
				flex-direction: column;
				border-bottom: 1px solid #f1f1f1;
			}
			
			.py label {
				font-size: 0.95rem;
				font-weight: 600;
				color: #b80d0d;
				margin-bottom: 12px;
				position: relative;
				display: inline-block;
				padding-left: 8px;
			}
			
			.py label:before {
				content: '';
				position: absolute;
				left: 0;
				top: 0;
				height: 100%;
				width: 3px;
				background: #b80d0d;
				border-radius: 2px;
			}
			
			.pystr {
				padding: 15px;
				border: 1px solid #ced4da;
				border-radius: 8px;
				font-size: 0.95rem;
				line-height: 1.6;
				color: #495057;
				resize: vertical;
				min-height: 80px;
				transition: all 0.2s ease;
				box-shadow: 0 2px 6px rgba(0,0,0,0.05);
			}
			
			.pystr:focus {
				border-color: #b80d0d;
				outline: 0;
				box-shadow: 0 0 0 3px rgba(184, 13, 13, 0.15);
			}
			
			/* 得分区域 */
			.py2 {
				flex-direction: row;
				align-items: center;
				flex-wrap: wrap;
				background: #f9f9fa;
				border-radius: 8px;
				margin-top: 10px;
			}
			
			.py2 label {
				margin-right: 20px;
				margin-bottom: 0;
				white-space: nowrap;
				color: #495057;
			}
			
			.ff {
				width: 90px;
				padding: 10px 15px;
				border: 1px solid #ced4da;
				border-radius: 8px;
				font-size: 1.2rem;
				color: #b80d0d;
				text-align: center;
				font-weight: 700;
				margin-right: 20px;
				transition: all 0.2s ease;
				box-shadow: 0 2px 6px rgba(0,0,0,0.08);
			}
			
			.ff:focus {
				border-color: #b80d0d;
				outline: 0;
				box-shadow: 0 0 0 3px rgba(184, 13, 13, 0.15);
			}
			
			.py2 span {
				font-size: 0.85rem;
				color: #6c757d;
				background: white;
				padding: 6px 12px;
				border-radius: 6px;
				box-shadow: 0 1px 3px rgba(0,0,0,0.05);
				border: 1px solid #e9ecef;
			}
			
			.py2 span i {
				font-style: normal;
				color: #b80d0d;
				font-weight: 700;
			}
			
			/* 未作答和无参考答案样式 */
			.no-answer {
				color: #9aa0a6;
				font-style: italic;
				text-align: center;
				padding: 25px;
				background: #f8f8f8;
				border-radius: 8px;
				border: 1px dashed #dee2e6;
				box-shadow: inset 0 2px 6px rgba(0,0,0,0.03);
			}
			
			/* 添加题目高亮和闪烁效果 */
			.active-question {
				box-shadow: 0 0 0 3px #b80d0d, 0 8px 25px rgba(184, 13, 13, 0.2) !important;
				border-color: #b80d0d !important;
			}
			
			@keyframes highlight-flash {
				0% { box-shadow: 0 0 0 3px #b80d0d, 0 8px 25px rgba(184, 13, 13, 0.2); }
				50% { box-shadow: 0 0 0 5px #b80d0d, 0 12px 35px rgba(184, 13, 13, 0.3); }
				100% { box-shadow: 0 0 0 3px #b80d0d, 0 8px 25px rgba(184, 13, 13, 0.2); }
			}
			
			.highlight-question {
				animation: highlight-flash 1.2s ease-in-out;
			}
			
			/* 内容区域行间距优化 */
			.studentstr p {
				margin-bottom: 12px;
				text-indent: 2em;
			}
			
			/* 确保最后一个元素没有底部间距 */
			.studentstr p:last-child {
				margin-bottom: 0;
			}
			
			/* 使标题更美观 */
			#tcppname {
				display: inline-block;
				max-width: calc(100% - 60px);
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			
			/* 响应式优化 */
			@media (max-width: 1200px) {
				.yuepaperbox {
					width: 98%;
					max-width: none;
				}
				
				.ypaperboxright {
					width: 200px;
				}
				
				.ypapertitle {
					padding: 16px 20px;
				}
				
				.ypapertitle label {
					font-size: 1.2rem;
				}
			}
			
			@media (max-width: 992px) {
				#tbox {
					padding: 10px;
				}
				
				.yuepaperbox {
					width: 100%;
					max-height: calc(100vh - 20px);
				}
				
				.ypaperbox {
					flex-direction: column;
				}
				
				.ypaperboxright {
					width: 100%;
					border-left: none;
					border-top: 1px solid #e9ecef;
					padding: 15px;
					box-shadow: none;
					max-height: 200px;
				}
				
				.thbox {
					max-height: 100px;
					margin-bottom: 10px;
				}
				
				.thlb {
					padding-bottom: 10px;
					margin-bottom: 10px;
				}
				
				.btnvvv {
					padding-top: 5px;
					margin-top: 10px;
				}
				
				.py2 {
					flex-wrap: wrap;
				}
				
				.py2 label, .py2 .ff {
					margin-bottom: 10px;
				}
				
				.py2 span {
					margin-top: 5px;
					width: 100%;
				}
			}
			
			@media (max-width: 768px) {
				.ypapertitle {
					padding: 12px 16px;
				}
				
				.ypapertitle label {
					font-size: 1.1rem;
				}
				
				.paperyueclose {
					width: 32px;
					height: 32px;
				}
				
				.ypaperbottom {
					padding: 12px 16px;
					gap: 10px;
				}
				
				.ypaperboxleft {
					padding: 20px;
				}
				
				.tmitem {
					margin-bottom: 20px;
				}
				
				.tmname {
					padding: 15px;
				}
				
				.studentbox, .studentbox2, .py {
					padding: 15px;
				}
			}
		</style>
		<style>
			body {
				background: url(img/background.jpg) no-repeat;
				background-size: cover;
				background-attachment: fixed;
			}
			
			/* 现代化导航栏美化 - 与页面红色主题统一，红色偏橙色 */
			.boxleft {
				background: white;
				border-radius: 16px;
				box-shadow: 0 8px 32px rgba(220, 53, 69, 0.08);
				overflow: hidden;
				border: 1px solid rgba(231, 76, 60, 0.05);
				position: relative;
				backdrop-filter: blur(20px);
			}
			
			.boxleft::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8));
				pointer-events: none;
				border-radius: 16px;
			}
			
			/* 导航栏顶部标题区域 - 红色偏橙色渐变 */
			.lefttopview {
				width: 100%;
				height: 65px;
				background: linear-gradient(135deg, #dc3545, #e74c3c, #ff6b35);
				background-size: 300% 300%;
				animation: gradientShift 6s ease infinite;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 18px;
				color: #FFFFFF;
				font-weight: 700;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif;
				position: relative;
				box-shadow: 0 4px 20px rgba(220, 53, 69, 0.25);
				letter-spacing: 1.5px;
				border-radius: 16px 16px 0 0;
			}
			
			/* 渐变动画效果 */
			@keyframes gradientShift {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.lefttopview img {
				width: 24px;
				height: 24px;
				display: block;
				margin-right: 12px;
				filter: brightness(0) invert(1) drop-shadow(0 0 6px rgba(255, 255, 255, 0.4));
			}
			
			.lefttopview label {
				display: flex;
				align-items: center;
				text-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
			}
			
			/* 导航菜单容器 */
			.leftitembox {
				background: transparent;
				padding: 20px 0;
				position: relative;
				z-index: 2;
			}
			
			.leftitembox::before {
				content: '';
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 85%;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.15), transparent);
			}
			
			/* 导航菜单项 - 现代化设计与图标 */
			.leftitem {
				height: auto;
				padding: 16px 24px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				font-size: 15px;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif;
				font-weight: 500;
				color: #4a5568;
				border-bottom: none;
				text-decoration: none;
				transition: color 0.2s ease, background-color 0.2s ease;
				position: relative;
				margin: 4px 16px;
				border-radius: 12px;
				background: transparent;
				overflow: hidden;
				min-height: 48px;
			}
			
			/* 为不同菜单项添加对应的SVG图标 */
			.leftitem::before {
				content: '';
				width: 20px;
				height: 20px;
				margin-right: 12px;
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				opacity: 0.7;
				transition: opacity 0.2s ease;
				flex-shrink: 0;
			}
			
			/* 个人信息图标 */
			.leftitem[href*="userinfo"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
			}
			
			/* 学生成绩统计图标 */
			.leftitem[href*="studentachievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z'/%3E%3C/svg%3E");
			}
			
			/* 学习记录统计图标 */
			.leftitem[href*="studentlearning"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
			}
			
			/* 考试管理图标 */
			.leftitem[href*="examination"]::before,
			.leftitem.activeleftitem::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E");
			}
			
			/* 学习任务管理图标 */
			.leftitem[href*="learning"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
			}
			
			/* 成果展示图标 */
			.leftitem[href*="achievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E");
			}
			
			/* 发布心声图标 */
			.leftitem[href*="voice"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z'/%3E%3C/svg%3E");
			}
			
			/* 菜单项悬浮态 - 完全无抖动 */
			.leftitem:hover {
				background: linear-gradient(135deg, rgba(220, 53, 69, 0.06), rgba(231, 76, 60, 0.08));
				color: #dc3545;
			}
			
			.leftitem:hover::before {
				opacity: 1;
			}
			
			/* 激活状态的菜单项 - 红色偏橙色渐变与白色图标 */
			.activeleftitem {
				font-weight: 700 !important;
				color: white !important;
				background: linear-gradient(135deg, #dc3545, #e74c3c, #ff6b35) !important;
				background-size: 300% 300%;
				animation: activeGradient 4s ease infinite;
				box-shadow: 0 8px 32px rgba(220, 53, 69, 0.3);
				border-radius: 12px;
			}
			
			@keyframes activeGradient {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.activeleftitem::before {
				opacity: 1 !important;
				filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
			}
			
			.activeleftitem::after {
				content: '';
				position: absolute;
				right: 16px;
				top: 50%;
				transform: translateY(-50%);
				width: 8px;
				height: 8px;
				background: white;
				border-radius: 50%;
				box-shadow: 0 0 12px rgba(255, 255, 255, 0.8);
				animation: activePulse 2s ease infinite;
			}
			
			@keyframes activePulse {
				0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
				50% { opacity: 0.6; transform: translateY(-50%) scale(1.4); }
			}
			
			/* 添加精美的分隔线效果 */
			.leftitem:not(:last-child):not(.activeleftitem) {
				border-bottom: 1px solid rgba(231, 76, 60, 0.06);
				margin-bottom: 3px;
			}
			
			.leftitem:not(:last-child):not(.activeleftitem)::after {
				content: '';
				position: absolute;
				bottom: -1px;
				left: 40px;
				right: 24px;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.15), transparent);
				opacity: 0.5;
			}
			
			/* 整体容器增强效果 */
			.contentview .boxleft {
				background: rgba(255, 255, 255, 0.95);
				border-radius: 16px;
				box-shadow: 0 8px 40px rgba(220, 53, 69, 0.08);
				border: 1px solid rgba(231, 76, 60, 0.05);
				overflow: hidden;
				position: relative;
				backdrop-filter: blur(20px);
			}
			
			.contentview .boxleft::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				height: 3px;
				background: linear-gradient(90deg, #dc3545, #e74c3c, #ff6b35);
				opacity: 0;
				transition: opacity 0.4s ease;
			}
			
			.contentview .boxleft:hover::before {
				opacity: 1;
			}
			
			/* 响应式优化 */
			@media (max-width: 768px) {
				.boxleft {
					border-radius: 12px;
					margin-bottom: 20px;
				}
				
				.lefttopview {
					height: 55px;
					font-size: 16px;
					letter-spacing: 1px;
				}
				
				.lefttopview img {
					width: 20px;
					height: 20px;
					margin-right: 10px;
				}
				
				.leftitem {
					padding: 14px 20px;
					font-size: 14px;
					margin: 3px 12px;
					min-height: 44px;
				}
				
				.leftitem::before {
					width: 18px;
					height: 18px;
					margin-right: 10px;
				}
			}
			
			/* 字体优化 - 思源黑体 */
			.lefttopview,
			.leftitem {
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Source Han Sans', 'Noto Sans CJK SC', 'Microsoft YaHei UI', 'Microsoft YaHei', '微软雅黑', sans-serif;
			}
			
			/* 增强的交互反馈 */
			.leftitem {
				cursor: pointer;
			}
			
			.leftitem:active {
				transform: scale(0.98);
				transition: all 0.1s ease;
			}
			
			.activeleftitem:active {
				transform: scale(1.02);
			}
			
			/* 为菜单项添加微妙的进入动画 */
			.leftitem {
				animation: slideIn 0.5s ease-out forwards;
				opacity: 0;
				transform: translateX(-20px);
			}
			
			.leftitem:nth-child(1) { animation-delay: 0.1s; }
			.leftitem:nth-child(2) { animation-delay: 0.2s; }
			.leftitem:nth-child(3) { animation-delay: 0.3s; }
			.leftitem:nth-child(4) { animation-delay: 0.4s; }
			.leftitem:nth-child(5) { animation-delay: 0.5s; }
			.leftitem:nth-child(6) { animation-delay: 0.6s; }
			.leftitem:nth-child(7) { animation-delay: 0.7s; }
			
			@keyframes slideIn {
				to {
					opacity: 1;
					transform: translateX(0);
				}
			}
		</style>
		
		<!-- 页面标签样式统一 - 与userexamination2.html保持一致 -->
		<style>
			/* 页面标签容器样式统一 */
			.userinfotopdiv {
				margin-bottom: 8px; /* 减少下边距 */
			}
			
			.userinfotopdiv a {
				padding: 6px 12px; /* 减少内边距 */
				font-size: 18px; /* 减小字体 */
			}
		</style>
	</head>
	<body class="index">
		<div class="tcbox" id="tcbox">
			<div class="topviewsss">
				<a onclick="closetc()">关闭</a>
				<label id="pdfname"></label>
			</div>
			<div class="panel-body pdfbox">
				<a id="pdf" class="media" href=""></a>
			</div>
		</div>
		<!-- 重新设计的阅卷弹窗 -->
		<div id="tbox">
			<div class="yuepaperbox">
				<div class="yuepaperview">
					<div class="yuepapaertoptitle">
						<div class="ypapertitle">
							<label id="tcppname"></label>
							<label onclick="closeyuepaper()" class="paperyueclose"></label>
						</div>
						<div class="ypaperbottom" id="userinfos">
							<!-- 学生信息将通过JS动态填充 -->
						</div>
					</div>
					<div class="ypaperbox">
						<div class="ypaperboxleft" id="pmlist">
							<!-- 试题内容将通过JS动态填充 -->
						</div>
						<div class="ypaperboxright">
							<div class="thlb">题号导航</div>
							<div class="thbox" id="thlist">
								<!-- 题号列表将通过JS动态填充 -->
							</div>
							<div class="btnvvv">
								<div onclick="submityuejuan()">完成阅卷</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a href="studentachievement.html" class="leftitem">学生成绩统计</a>
						<a href="studentlearning.html" class="leftitem">学习记录统计</a>
						<a class="leftitem activeleftitem">考试管理</a>
						<a href="learningtasks.html" class="leftitem">学习任务管理</a>
						<a href="achievements.html" class="leftitem">成果展示</a>
						<a href="releaseyourvoice2.html" class="leftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="userinfotopdiv">
						<a href="userexamination2.html">试卷组合</a>
						<a class="acccccg">试卷审阅</a>
					</div>
					<div class="paperscroll">
						<div class="papertitle">学生答卷信息列表</div>
						
						<!-- 筛选条件区域 -->
						<div style="background-color: #f8f9fa; border-radius: 8px; padding: 12px; margin-bottom: 15px; box-shadow: 0 2px 5px rgba(0,0,0,0.05);">
							<div style="display: flex; flex-wrap: wrap; gap: 12px; margin-bottom: 8px; align-items: center;">
								<span style="font-weight: 600; color: #495057; margin-right: 8px; font-size: 0.85rem; flex: 0 0 auto;">筛选条件:</span>
								<select id="paperxk" onchange="xkchange(this)" style="flex: 1 1 150px; min-width: 120px; max-width: 180px; padding: 6px 10px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.8rem; background-color: white;">
									<!-- 学科选项 -->
								</select>
								<!-- This is the container for "试卷搜索" -->
								<div style="flex: 1 1 200px; min-width: 150px; position: relative; margin-right: 2rem;">
									<div class="custom-select-wrapper">
										<input id="paperSearchInput" type="text" placeholder="搜索试卷..." style="width: 100%; padding: 6px 10px; padding-right: 28px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.8rem; background-color: white;">
										<div class="custom-select" id="paperSelectDropdown" style="display: none; position: absolute; top: 100%; left: 0; width: 100%; max-height: 200px; overflow-y: auto; background: white; border: 1px solid #ced4da; border-radius: 0 0 4px 4px; z-index: 1000; box-shadow: 0 4px 8px rgba(0,0,0,0.1);">
											<!-- 试卷选项将通过JS动态填充 -->
										</div>
										<i style="position: absolute; right: 10px; top: 50%; transform: translateY(-50%); color: #6c757d; cursor: pointer;" onclick="togglePaperDropdown()">
											<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
												<path d="M7.247 11.14 2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z"/>
											</svg>
										</i>
									</div>
								</div>
								<div style="flex: 2 1 250px; position: relative; min-width: 200px;">
									<input id="fuzzySearch" onkeyup="fuzzySearch(this)" type="text" placeholder="模糊搜索: 学院/专业/班级/学号/姓名" style="width: 100%; padding: 6px 10px; padding-left: 30px; border: 1px solid #ced4da; border-radius: 4px; font-size: 0.8rem; background-color: white;">
									<i style="position: absolute; left: 10px; top: 50%; transform: translateY(-50%); color: #6c757d;">
										<svg xmlns="http://www.w3.org/2000/svg" width="14" height="14" fill="currentColor" viewBox="0 0 16 16">
											<path d="M11.742 10.344a6.5 6.5 0 1 0-1.397 1.398h-.001c.03.04.062.078.098.115l3.85 3.85a1 1 0 0 0 1.415-1.414l-3.85-3.85a1.007 1.007 0 0 0-.115-.1zM12 6.5a5.5 5.5 0 1 1-11 0 5.5 5.5 0 0 1 11 0z"/>
										</svg>
									</i>
								</div>
							</div>
							
							<!-- 添加筛选结果显示区域 -->
							<div id="filter-results" style="display: none; margin-top: 10px; padding: 8px 12px; background-color: #e9f7fe; border-radius: 6px; font-size: 0.8rem; color: #0c5460; border-left: 3px solid #17a2b8;">
								<div style="display: flex; justify-content: space-between; align-items: center;">
									<div>
										<span style="font-weight: 600;">当前筛选:</span> 
										<span id="filter-summary">全部数据</span>
									</div>
									<button onclick="clearFilters()" style="background: none; border: none; color: #dc3545; cursor: pointer; font-size: 0.75rem; padding: 2px 5px; border-radius: 3px;">
										<i style="margin-right: 3px;">×</i>清除筛选
									</button>
								</div>
							</div>
						</div>
						
						<!-- <div class="ppname" id="papaname" style="font-size: 0.9rem; margin-bottom: 10px; color: #333;">
							请选择学科
						</div> -->
						<!-- 使用标准HTML表格替代div布局 -->
						<table class="exam-table">
						  <thead>
						    <tr>
						      <th class="w-seq">序号</th>
						      <th class="w-class">班级</th>
						      <th class="w-name">姓名</th>
						      <th class="w-id">学号</th>
						      <th class="w-time">最后阅卷时间</th>
						      <th class="w-score">总分</th>
						      <th class="w-action">操作</th>
						    </tr>
						  </thead>
						  <tbody id="ppplist">
						    <tr>
						      <td colspan="7">请先选择学科</td>
						    </tr>
						  </tbody>
						</table>
						<div class="fybox" id="fyq">
							<span id="sy">首页</span>
							<span id="syy">上一页</span>
							<div class="num" id="num">
							</div>
							<span id="xyy">下一页</span>
							<span id="wy">尾页</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		
		<!-- 添加查看阅卷记录弹窗 -->
		<div id="record-tbox" style="display: none; position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 9999; align-items: center; justify-content: center;margin-top: -6rem !important;">
			<div class="record-box" style="width: 85%; max-width: 980px; border-radius: 12px; box-shadow: 0 10px 25px rgba(0,0,0,0.2); background: #f8f9fa; padding: 0;margin-top: -16rem !important;">
				<div style="display: flex; justify-content: space-between; align-items: center; border-bottom: 1px solid #eee; padding: 15px 20px; background: white; border-radius: 12px 12px 0 0;">
					<div class="record-title" style="margin: 0; color: #333; font-size: 1.4rem; font-weight: 600;">阅卷记录</div>
					<div onclick="closeRecordBox()" style="cursor: pointer; font-size: 2rem; color: #999; transition: color 0.3s ease; width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; border-radius: 50%;">&times;</div>
				</div>
				<div id="record-content" style="max-height: 70vh; overflow-y: auto; padding: 20px; background: #f8f9fa;"></div>
			</div>
		</div>
		
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let userinfo = sessionStorage.getItem("userinfo")

			let paperinfo = null
			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				
				// 添加空行样式
				addStyle();
				
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
					getpaperxueke() //学科列表
					getpaperlist() //获取所有试卷列表
					getcmlist() //获取所有学院专业班级
					
					// 获取最新考试的数据将在getpaperlist完成后触发
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getfooterlink()
			})

			// 添加空白行样式
			function addStyle() {
				// 检查是否已存在此样式
				if (!document.getElementById('empty-row-style')) {
					const style = document.createElement('style');
					style.id = 'empty-row-style';
					style.innerHTML = `
						.empty-row td {
							background-color: #ffffff !important;
							border-color: #e9ecef;
						}
						.empty-row:nth-child(even) td {
							background-color: #f8f9fa !important;
						}
						.empty-row:hover td {
							background-color: inherit !important;
						}
					`;
					document.head.appendChild(style);
				}
			}

			let paperid = null //试卷ID
			let xkid = null //学科ID
			let xyid = null //学院ID
			let zyid = null //专业ID
			let bjid = null //班级ID
			let studentno = null //学号
			let pagesize = 10
			let pageindex = 1
			let pages = 1
			let pplist = null

			let cmdata = null
			let zydata = null

			function xychange(items) { //当选择学院时
				xyid = $(items).val()

				let zyhtml = '<option value=null>请选择专业</option>'
				if (xyid != 'null') {
					cmdata.forEach((item) => {
						if (item.id == xyid) {
							zydata = item.children
							item.children.forEach((item2) => {
								zyhtml += '<option value="' + item2.id + '">' + item2.name + '</option>'
							})
						}
					})
					$("#zy").html(zyhtml)
				} else {
					$("#zy").html('<option value=null>请选择专业</option>')
				}
				$("#bj").html('<option value=null>请选择班级</option>')
				zyid = null
				bjid = null
				studentno = null
				mpaperlist()
			}

			function zychange(items) { //当专业选择时
				zyid = $(items).val()
				let bjhtml = '<option value=null>请选择班级</option>'
				if (zyid != 'null') {
					zydata.forEach((item) => {
						if (item.id == zyid) {
							item.children.forEach((item2) => {
								bjhtml += '<option value="' + item2.id + '">' + item2.name + '</option>'
							})
						}
					})
					$("#bj").html(bjhtml)
				} else {
					$("#bj").html('<option value=null>请选择班级</option>')
				}
				bjid = null
				studentno = null
				mpaperlist()
			}

			function bjchange(items) { //当班级选择时
				bjid = $(items).val()
				studentno = null
				getstudentlist()
				mpaperlist()
			}
			let studentdata = null

			function getstudentlist() {
				$.ajax({
					url: baseurl + "/student/ClaassStudentlist",
					type: 'GET',
					contentType: "application/json",
					data: {
						className: bjid
					},
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data)
							studentdata = res.data
							let usernamestr = '<option value=null>请选择姓名</option>'
							let usernostr = '<option value=null>请选择学号</option>'
							res.data.map((item) => {
								usernamestr += '<option value="' + item.identifier + '">' + item.name +
									'</option>'
								usernostr += '<option value="' + item.identifier + '">' + item.identifier +
									'</option>'
							})
							$('#xh').html(usernostr)
							$('#xm').html(usernamestr)
						}
					}
				})
			}

			function namec(item) { //选择姓名
				let str = $(item).val()
				$("#xh").val(str)
				studentno = str
				// let nn = null
				// studentdata.map((item) => {
				// 	if (str == item.name) {
				// 		nn = item.userAuth.identifier
				// 	}
				// })
				// if (nn) {
				// 	$("#xh").val(nn)
				// 	studentno = nn
				// } else {
				// 	$("#xh").val("null")
				// 	studentno = null
				// }
				mpaperlist()
			}

			function xhc(item) { //选择学号
				let str = $(item).val()
				$("#xm").val(str)
				studentno = str
				//let nn = null
				// studentdata.map((item) => {
				// 	if (str == item.userAuth.identifier) {
				// 		nn = item.name
				// 	}
				// })
				// if (nn) {
				// 	$("#xm").val(nn)
				// 	studentno = nn
				// } else {
				// 	$("#xm").val("null")
				// 	studentno = null
				// }
				mpaperlist()
			}

			function xkchange(items) { //学科选择
				if ($(items).val() != 'null') {
					xkid = $(items).val()
				} else {
					xkid = null
				}
				let sssname = ""
				xkmylist.forEach((aaa)=>{
					if(aaa.id == $(items).val()){
						sssname = aaa.name
					}
				})
				$("#papaname").html(sssname)

				mpaperlist()
				updateFilterSummary() // 更新筛选摘要
			}
			let xkmylist = null
			function getpaperxueke() { //获取学科
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'GET',
					
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let xuekehtml = "<option value=null>请选择学科</option>"
							res.data.map((item) => {
								xuekehtml += '<option value="' + item.id + '">' + item.name + '</option>'
							})
							xkmylist = res.data
							$("#paperxk").html(xuekehtml)
						}
					}
				})
			}

			function mpaperlist() {
				// 允许直接按试卷ID查询，不需要学科
				if (xkid == null && !paperid) {
					$('#ppplist').html(`<tr><td colspan="7"><div id="msmsms">请选择学科或试卷</div></td></tr>`)
					$("#fyq").hide()
					$("#filter-results").hide() // 隐藏筛选结果
				} else {
					$.ajax({
						url: baseurl + "/paper/answered",
						type: 'GET',
						data: {
							pageNum: 1, // 获取所有数据，前端分页
							pageSize: 1000, // 获取足够多的数据
							collegeId: xyid == 'null' ? '' : xyid,
							majorId: zyid == 'null' ? '' : zyid,
							classId: bjid == 'null' ? '' : bjid,
							paperId: paperid == 'null' ? '' : paperid,
							identifier: studentno,
							subjectId: xkid || '' // 如果xkid为null，则传空字符串
						},
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						dataType: 'json',
						success: (res) => {
							if (res.code == '200') {
								// 更新筛选结果计数
								updateFilterSummary(res.data.total)
								
								let html = ''
								if (res.data.list.length == 0) {
									$('#ppplist').html(`<tr><td colspan="7"><div id="msmsms">没有找到数据...</div></td></tr>`)
									// 添加空行补齐至10行
									for (let i = 0; i < pagesize - 1; i++) {
										$('#ppplist').append(`<tr class="empty-row" style="height: 52px; border-bottom: 1px solid #e9ecef;">
											<td class="w-seq"></td>
											<td class="w-class"></td>
											<td class="w-name"></td>
											<td class="w-id"></td>
											<td class="w-time"></td>
											<td class="w-score"></td>
											<td class="w-action"></td>
										</tr>`);
									}
									$("#fyq").hide();
								} else {
									// 处理重复学号问题，保留最高分记录
									const studentMap = new Map();
									
									// 第一遍循环，找出每个学号的最高分记录
									res.data.list.forEach(item => {
										if (!item.student) return;
										
										const studentId = item.student.userAuth.identifier;
										const score = parseFloat(item.zhuguanScore || item.score || 0);
										
										if (!studentMap.has(studentId)) {
											studentMap.set(studentId, { item, score });
										} else {
											const currentRecord = studentMap.get(studentId);
											if (score > currentRecord.score) {
												studentMap.set(studentId, { item, score });
											}
										}
									});
									
									// 将去重后的数据转换为数组
									const uniqueStudents = Array.from(studentMap.values());
									
									// 计算总页数
									pages = Math.ceil(uniqueStudents.length / pagesize);
									
									// 计算当前页应该显示的数据范围
									const startIndex = (pageindex - 1) * pagesize;
									const endIndex = startIndex + pagesize;
									const currentPageData = uniqueStudents.slice(startIndex, endIndex);
									
									// 渲染当前页数据
									currentPageData.forEach((studentRecord, index) => {
										const item = studentRecord.item;
										const globalIndex = startIndex + index + 1; // 全局序号
										
										// 确保最后阅卷时间正确显示
										const lastMarkTime = item.isMark == 1 && item.updatedAt ? setDate(item.updatedAt) : '未阅';
										// 获取总分
										const totalScore = item.zhuguanScore || item.score || '未评分';
										
										// 计算及格分数（总分*0.6）并设置颜色
										const passingScore = parseFloat(totalScore) * 0.6;
										let scoreClass = '';
										if (totalScore !== '未评分') {
											scoreClass = parseFloat(totalScore) >= passingScore ? 'score-pass' : 'score-fail';
										}
										
										// 合并学院专业班级信息
										const collegeName = item.student && item.student.collegeName ? item.student.collegeName : "";
										const majorName = item.student && item.student.majorName ? item.student.majorName : "";
										const className = item.student && item.student.className1 ? item.student.className1 : "";
										
										// 获取学生姓名
										const studentName = item.student && item.student.name ? item.student.name : "";
										
										if (item.isMark == '0' || item.isMark == 0) {
											html += `<tr>
												<td class="w-seq">${globalIndex}</td>
												<td class="w-class combined-info class-col" data-full="${collegeName} / ${majorName} / ${className}">${className}</td>
												<td class="w-name">${studentName}</td>
												<td class="w-id">${item.student!= null ? item.student.userAuth.identifier : ""}</td>
												<td class="w-time">未阅</td>
												<td class="w-score">-</td>
												<td class="w-action"><a class="action-btn btn-mark" onclick="yuepaper(this)" data-id="${item.id}">阅卷</a></td>
											</tr>`;
										} else {
											html += `<tr>
												<td class="w-seq">${globalIndex}</td>
												<td class="w-class combined-info class-col" data-full="${collegeName} / ${majorName} / ${className}">${className}</td>
												<td class="w-name">${studentName}</td>
												<td class="w-id">${item.student!= null ? item.student.userAuth.identifier : ""}</td>
												<td class="w-time">${lastMarkTime}</td>
												<td class="w-score ${scoreClass}">${totalScore}</td>
												<td class="w-action">
													<a class="action-btn btn-remark" onclick="yuepaper(this)" data-id="${item.id}">重阅</a>
													<a class="action-btn btn-view" onclick="viewRecord(this)" data-id="${item.id}">查看</a>
												</td>
											</tr>`;
										}
									});
									
									// 如果当前页数据不足pagesize条，添加空行补齐
									const currentPageCount = currentPageData.length;
									if (currentPageCount < pagesize) {
										const emptyRowsNeeded = pagesize - currentPageCount;
										for (let i = 0; i < emptyRowsNeeded; i++) {
											html += `<tr class="empty-row" style="height: 52px; border-bottom: 1px solid #e9ecef;">
												<td class="w-seq"></td>
												<td class="w-class"></td>
												<td class="w-name"></td>
												<td class="w-id"></td>
												<td class="w-time"></td>
												<td class="w-score"></td>
												<td class="w-action"></td>
											</tr>`;
										}
									}
									
									$('#ppplist').html(html);
									
									// 显示分页控件
									if (pages > 1) {
										let numhtml = "";
										//当页数大于5
										if (pages > 5) {
											// 前5页
											if (pageindex <= 5) {
												for (let a = 1; a <= Math.min(10, pages); a++) {
													if (pageindex == a) {
														numhtml += '<label class="actinum" onclick="getnewlist(' + a + ')">' + a + '</label>';
													} else {
														numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>';
													}
												}
												if (pages > 10) {
													numhtml += '<label onclick="getnewlist(' + Math.min(11, pages) + ')">...</label>';
												}
											} 
											// 中间页
											else if (pageindex > 5 && pageindex < pages - 5) {
												numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) - 5) + ')">...</label>';
												for (let a = pageindex - 4; a <= pageindex + 4; a++) {
													if (pageindex == a) {
														numhtml += '<label class="actinum" onclick="getnewlist(' + a + ')">' + a + '</label>';
													} else {
														numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>';
													}
												}
												numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) + 5) + ')">...</label>';
											} 
											// 最后5页
											else {
												if (pages > 10) {
													numhtml += '<label onclick="getnewlist(' + (pages - 10) + ')">...</label>';
												}
												for (let a = Math.max(1, pages - 9); a <= pages; a++) {
													if (pageindex == a) {
														numhtml += '<label class="actinum" onclick="getnewlist(' + a + ')">' + a + '</label>';
													} else {
														numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>';
													}
												}
											}
										} 
										// 页数不足5页时
										else {
											for (let a = 1; a <= pages; a++) {
												if (pageindex == a) {
													numhtml += '<label class="actinum" onclick="getnewlist(' + a + ')">' + a + '</label>';
												} else {
													numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>';
												}
											}
										}
										$("#sy").attr("onclick", "getnewlist(1)");
										$("#syy").attr("onclick", "getnewlist(1)");
										if (pageindex > 1) {
											$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")");
										}
										if (pageindex < pages) {
											$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")");
										} else {
											$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")");
										}
										$("#wy").attr("onclick", "getnewlist(" + pages + ")");
										$("#num").html(numhtml);
										$("#fyq").show();
									} else {
										$("#fyq").hide();
									}
								}
							}
						}
					})
				}
			}

			function getcmlist() { //学院 专业  班级列表树
				$.ajax({
					url: baseurl + "/college/major",
					type: 'GET',
					
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data)
							cmdata = res.data
							let html = '<option value=null>请选择学院</option>'
							res.data.forEach((item) => {
								html += `<option value=${item.id}>${item.name}</option>`
							})
							$("#xy").html(html)
						}
					}
				})
			}

			function getpaperlist() { //试卷列表
				$.ajax({
					url: baseurl + "/paper/listAll",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// 保存试卷列表数据
							pplist = res.data;
							
							// 按照创建时间从新到旧排序（如果有createdAt字段）
							pplist.sort((a, b) => {
								// 如果有createdAt字段，则按时间排序
								if (a.createdAt && b.createdAt) {
									return new Date(b.createdAt) - new Date(a.createdAt);
								}
								// 如果没有createdAt字段，则保持原来顺序
								return 0;
							});
							
							// 渲染下拉列表选项
							renderPaperOptions(pplist);
							
							// 获取最新考试数据，优先展示最新考试
							getRecentExam();
							
							// 如果没有最新考试数据且有试卷，默认选择第一个（最新的）
							// 这将在getRecentExam没有数据时作为备选方案
							setTimeout(function() {
								if (!paperid && pplist.length > 0) {
									selectPaper(pplist[0].id, pplist[0].name);
								}
							}, 500);
						}
					}
				})
			}

			// 渲染试卷选项
			function renderPaperOptions(papers, searchTerm = '') {
				let html = '';
				let filteredPapers = papers;
				
				// 如果有搜索词，进行过滤
				if (searchTerm) {
					searchTerm = searchTerm.toLowerCase();
					filteredPapers = papers.filter(paper => 
						paper.name && paper.name.toLowerCase().includes(searchTerm)
					);
				}
				
				// 生成选项HTML
				filteredPapers.forEach(paper => {
					html += `<div class="paper-option" data-id="${paper.id}" data-name="${paper.name}" onclick="selectPaper('${paper.id}', '${paper.name.replace(/'/g, "\\'")}')">${paper.name}</div>`;
				});
				
				// 如果没有匹配的试卷
				if (filteredPapers.length === 0) {
					html = '<div class="paper-option-empty">没有找到匹配的试卷</div>';
				}
				
				// 更新下拉列表内容
				$("#paperSelectDropdown").html(html);
			}
			
			// 选择试卷
			function selectPaper(id, name) {
				paperid = id;
				$("#paperSearchInput").val(name);
				$("#paperSelectDropdown").hide();
				$("#papaname").html(name);
				
				// 更新筛选摘要
				updateFilterSummary();
				
				// 立即加载对应试卷的数据，不需要依赖学科ID
				setTimeout(function() {
					mpaperlist();
				}, 100);
			}
			
			// 切换试卷下拉列表显示/隐藏
			function togglePaperDropdown() {
				const dropdown = $("#paperSelectDropdown");
				dropdown.toggle();
				if (dropdown.is(":visible")) {
					// 重新渲染选项
					renderPaperOptions(pplist);
				}
			}
			
			// 添加试卷搜索功能
			$(document).ready(function() {
				// 搜索框输入事件
				$("#paperSearchInput").on("input", function() {
					const searchTerm = $(this).val();
					renderPaperOptions(pplist, searchTerm);
					$("#paperSelectDropdown").show();
				});
				
				// 搜索框点击事件
				$("#paperSearchInput").on("click", function() {
					togglePaperDropdown();
				});
				
				// 点击页面其他区域时关闭下拉列表
				$(document).on("click", function(e) {
					if (!$(e.target).closest(".custom-select-wrapper").length) {
						$("#paperSelectDropdown").hide();
					}
				});
			});

			function closeyuepaper() {
				$("#tbox").hide()
			}
			
			// 关闭阅卷记录弹窗
			function closeRecordBox() {
				$("#record-tbox").hide()
			}
			
			// 查看阅卷记录
			function viewRecord(element) {
				const answerId = $(element).attr("data-id");
				$.ajax({
					url: baseurl + "/paper/answered/" + answerId,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let ddd = res.data;
							let html = '';
							
							// 使用zhuguanScore字段获取主观题总分
							const totalScore = ddd.zhuguanScore || ddd.score || 0;
							
							// 添加学生和试卷信息 - 更现代化的卡片式布局
							html += `<div style="background: white; border-radius: 10px; padding: 20px; margin-bottom: 25px; box-shadow: 0 2px 10px rgba(0,0,0,0.05);">
								<div style="display: flex; justify-content: space-between; flex-wrap: wrap; align-items: center;">
									<div style="flex: 1; min-width: 250px;">
										<div style="font-size: 1.3rem; font-weight: 600; margin-bottom: 15px; color: #2c3e50;">${ddd.cmsTestPaper.name}</div>
										<div style="margin-bottom: 5px; color: #555;"><strong>学生:</strong> ${ddd.student.name} (${ddd.student.userAuth.identifier})</div>
										<div style="margin-bottom: 5px; color: #555;"><strong>班级:</strong> ${ddd.student.className1}</div>
										<div style="color: #555;"><strong>提交时间:</strong> ${setDate(ddd.createdAt)}</div>
									</div>
									<div style="flex: 0 0 auto; text-align: center; background: #f8f9fa; padding: 15px 25px; border-radius: 10px; box-shadow: 0 2px 8px rgba(0,0,0,0.08);">
										<div style="font-size: 2.5rem; font-weight: bold; color: #ff6600; margin-bottom: 5px;">${totalScore}<span style="font-size: 1rem;">分</span></div>
										<div style="color: #666; font-size: 0.85rem;">阅卷时间: ${setDate(ddd.updatedAt)}</div>
									</div>
								</div>
							</div>`;
							
							// 添加题目和阅卷记录 - 更现代化的卡片式布局
							let tmdata = ddd.cmsTestPaper.cmsSubjectList;
							tmdata.forEach((item, index) => {
								const record = item.listRecord && item.listRecord.length > 0 ? item.listRecord[0] : null;
								// 如果是主观题且单题score为空，但整体有zhuguanScore，则显示总分
								const itemScore = (record && record.score) ? record.score : 
									(item.type === "4" && ddd.zhuguanScore) ? ddd.zhuguanScore : 0;
								
								// 格式化学生答案文本，添加段落和换行
								let formattedAnswer = '';
								if (record && record.answered) {
									// 将文本分段，每段之间添加段落标签
									const paragraphs = record.answered.split(/\n{2,}/);
									formattedAnswer = paragraphs.map(p => {
										// 处理单个换行符
										const lines = p.split(/\n/);
										return lines.join('<br>');
									}).join('</p><p>');
									formattedAnswer = `<p>${formattedAnswer}</p>`;
								} else {
									formattedAnswer = '<span style="color: #999; font-style: italic;">未作答</span>';
								}
								
								// 格式化参考答案文本
								let formattedReference = '';
								if (item.answer) {
									const paragraphs = item.answer.split(/\n{2,}/);
									formattedReference = paragraphs.map(p => {
										const lines = p.split(/\n/);
										return lines.join('<br>');
									}).join('</p><p>');
									formattedReference = `<p>${formattedReference}</p>`;
								}
								
								html += `<div style="background: white; border-radius: 10px; margin-bottom: 20px; box-shadow: 0 2px 10px rgba(0,0,0,0.05); overflow: hidden;">
									<div style="display: flex; justify-content: space-between; padding: 15px 20px; background: #f0f4f8; border-bottom: 1px solid #e3e8ec;">
										<div style="font-weight: 600; color: #2c3e50; font-size: 1.1rem;">第${index+1}题: ${item.name}</div>
										<div style="color: #ff6600; font-weight: 600; background: white; padding: 5px 15px; border-radius: 20px; box-shadow: 0 1px 3px rgba(0,0,0,0.1);">${itemScore}<span style="font-size: 0.8rem; color: #777;">/${item.score}分</span></div>
									</div>
									<div style="padding: 20px;">
										<div style="margin-bottom: 15px;">
											<div style="color: #2c3e50; font-weight: 600; margin-bottom: 8px; display: flex; align-items: center;">
												<span style="background: #e3f2fd; padding: 3px 10px; border-radius: 4px; margin-right: 10px;">学生回答</span>
											</div>
											<div style="background: #f9f9f9; padding: 12px 15px; border-radius: 6px; color: #333; line-height: 1.6; border-left: 3px solid #90caf9;">
												${formattedAnswer}
											</div>
										</div>
										${item.answer ? `<div style="margin-bottom: 15px;">
											<div style="color: #2c3e50; font-weight: 600; margin-bottom: 8px; display: flex; align-items: center;">
												<span style="background: #e8f5e9; padding: 3px 10px; border-radius: 4px; margin-right: 10px;">参考答案</span>
											</div>
											<div style="background: #f9f9f9; padding: 12px 15px; border-radius: 6px; color: #333; line-height: 1.6; border-left: 3px solid #81c784;">
												${formattedReference}
											</div>
										</div>` : ''}
										${record && (record.option || record.comments) ? `<div>
											<div style="color: #2c3e50; font-weight: 600; margin-bottom: 8px; display: flex; align-items: center;">
												<span style="background: #fff8e1; padding: 3px 10px; border-radius: 4px; margin-right: 10px;">教师评语</span>
											</div>
											<div style="background: #f9f9f9; padding: 12px 15px; border-radius: 6px; color: #0066cc; line-height: 1.6; border-left: 3px solid #ffc107; font-style: italic;">
												${record.option || record.comments}
											</div>
										</div>` : ''}
									</div>
								</div>`;
							});
							
							$("#record-content").html(html);
							$("#record-tbox").css("display", "flex");
						} else {
							cocoMessage.error(1000, "获取阅卷记录失败！");
						}
					},
					error: () => {
						cocoMessage.error(1000, "获取阅卷记录失败！");
					}
				});
			}
			
			let yuepaperid = null //当前点击阅卷的试卷ID
			let dtjgid = null //答题结果id
			let uppid = null //考试学生的ID
			function yuepaper(id) {
				$.ajax({
					url: baseurl + "/paper/answered/" + $(id).attr("data-id"),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let ddd = res.data
							yuepaperid = ddd.paperId
							dtjgid = ddd.id
							uppid = ddd.studentId
							// console.log(ddd)
							$("#tcppname").html(ddd.cmsTestPaper.name)
							$("#userinfos").html(`<label>专业：<span>${ddd.student.majorName}</span></label>
							<label>班级：<span>${ddd.student.className1}</span></label>
							<label>学号：<span>${ddd.student.userAuth.identifier}</span></label>
							<label>姓名：<span>${ddd.student.name}</span></label>
							<label>交卷时间：<span>${setDate(ddd.createdAt)}</span></label>`)


							let tmdata = ddd.cmsTestPaper.cmsSubjectList
							let html = ''
							let thhtml = ''
							tmdata.forEach((item, index) => {
								// 获取该题的评语和得分（如果已经阅过卷）
								let option = '';
								let score = '';
								
								if (item.listRecord && item.listRecord.length > 0) {
									const record = item.listRecord[0];
									// 优先使用option字段，如果为空则尝试使用comments字段
									option = record.option || record.comments || '';
									
									// 如果是主观题，且单题score为空但有总分
									if (item.type === "4" && (!record.score || record.score === '') && ddd.zhuguanScore) {
										score = ddd.zhuguanScore;
									} else {
										score = record.score || '';
									}
								}
								
								// 题号导航链接
								thhtml += `<a href="#id${item.id}" class="th-item">${index+1}</a>`;
								
								// 格式化学生答案和参考答案，使其更易于阅读
								let studentAnswer = '';
								let referenceAnswer = '';
								
								// 处理学生答案
								if (item.listRecord.length > 0 && item.listRecord[0].answered) {
									const rawAnswer = item.listRecord[0].answered;
									// 将文本分段，添加段落标签使其更易读
									studentAnswer = formatTextForDisplay(rawAnswer);
								} else {
									studentAnswer = '<div class="no-answer">未作答</div>';
								}
								
								// 处理参考答案
								if (item.answer) {
									referenceAnswer = formatTextForDisplay(item.answer);
								} else {
									referenceAnswer = '<div class="no-answer">无参考答案</div>';
								}
								
								html += `<div class="tmitem" data-id="${item.id}" id="id${item.id}">
								<div class="tmname">
									<label>【第${index+1}题】</label>
									<div>${item.name + '-（' + item.score + '分）'}</div>
								</div>
								<div class="studentbox">
									<div class="bbicott">
										<label>考生回答</label>
									</div>
									<div class="studentstr">
										${studentAnswer}
									</div>
								</div>
								<div class="studentbox2">
									<div class="bbicott2">
										<label>参考答案</label>
									</div>
									<div class="studentstr">
										${referenceAnswer}
									</div>
								</div>
								<div class="py">
									<label>评语:</label>
									<textarea class="pystr" rows="3">${option}</textarea>
								</div>
								<div class="py py2">
									<label>得分:</label>
									<input class="ff" onkeyup="inputcheck(this,${item.score})" type="number" value="${score}" />
									<span>本题满分<i>${item.score}</i>分（注：打分不得超过题目满分）</span>
								</div>
								</div>`
							})
							$("#pmlist").html(html)
							$("#thlist").html(thhtml)
							$("#tbox").attr("style", "display: flex;")
							
							// 添加点击题号高亮效果
							$(".th-item").on('click', function() {
								$(".th-item").removeClass("active");
								$(this).addClass("active");
							});
							
							// 监听滚动，更新当前题号高亮
							$(".ypaperboxleft").on('scroll', function() {
								highlightCurrentQuestion();
							});
							
							// 初始加载时高亮第一题
							$(".th-item:first").addClass("active");
							
							// 初始化按钮颜色
							updateScoreColor();
							
							// 为所有分数输入框添加实时监听
							$(".ff").on('input keyup', function() {
								updateScoreColor();
							});
						}
					}
				})
			}
			
			// 格式化文本以便更好地显示，处理段落和换行
			function formatTextForDisplay(text) {
				if (!text) return '<div class="no-answer">无内容</div>';
				
				// 替换连续的换行符为段落分隔符
				let formattedText = text.replace(/\n{2,}/g, '</p><p>');
				
				// 替换单个换行符为<br>
				formattedText = formattedText.replace(/\n/g, '<br>');
				
				// 如果文本不以</p>结尾，添加封闭标签
				if (!formattedText.endsWith('</p>')) {
					formattedText = '<p>' + formattedText + '</p>';
				} else {
					formattedText = '<p>' + formattedText;
				}
				
				return formattedText;
			}
			
			// 滚动时高亮当前可见的题目
			function highlightCurrentQuestion() {
				const scrollPosition = $(".ypaperboxleft").scrollTop();
				let currentQuestionId = null;
				
				// 遍历所有题目，找出当前可见的题目
				$(".tmitem").each(function() {
					const questionTop = $(this).position().top + $(".ypaperboxleft").scrollTop();
					const questionBottom = questionTop + $(this).outerHeight();
					
					// 如果题目在视口内
					if (questionTop <= scrollPosition + 100 && questionBottom >= scrollPosition) {
						currentQuestionId = $(this).attr("id");
						// 添加高亮样式到当前题目
						$(this).addClass("active-question");
						// 移除其他题目的高亮
						$(".tmitem").not(this).removeClass("active-question");
						return false; // 退出循环
					}
				});
				
				// 更新题号导航高亮
				if (currentQuestionId) {
					const activeLink = $(`.th-item[href="#${currentQuestionId}"]`);
					if (!activeLink.hasClass("active")) {
						$(".th-item").removeClass("active");
						activeLink.addClass("active");
						
						// 如果当前题号不在可视区域内，滚动题号列表
						const thboxContainer = $(".thbox");
						const linkTop = activeLink.position().top;
						const containerHeight = thboxContainer.height();
						
						if (linkTop < 0 || linkTop > containerHeight) {
							thboxContainer.animate({
								scrollTop: thboxContainer.scrollTop() + linkTop - containerHeight / 2
							}, 200);
						}
					}
				}
			}
			
			// 题号导航点击效果增强
			$(document).on('click', '.th-item', function(e) {
				e.preventDefault();
				const target = $(this).attr('href');
				const targetOffset = $(target).offset().top - $(".ypaperboxleft").offset().top + $(".ypaperboxleft").scrollTop();
				
				// 平滑滚动到目标题目
				$(".ypaperboxleft").animate({
					scrollTop: targetOffset - 20
				}, 300);
				
				// 添加闪烁效果
				$(target).addClass("highlight-question");
				setTimeout(function() {
					$(target).removeClass("highlight-question");
				}, 1000);
				
				// 更新题号导航高亮
				$(".th-item").removeClass("active");
				$(this).addClass("active");
			});

			function inputcheck(input, num) {
				let num1 = $(input).val()
				if (num1) {
					if (parseInt(num1) <= parseInt(num)) {
						$(input).val(parseInt(num1))
					} else {
						$(input).val(parseInt(num))
					}
				}
				
				// 实时计算总分并更新按钮颜色
				updateScoreColor();
			}
			
			// 更新按钮颜色根据总分
			function updateScoreColor() {
				let totalScore = 0;
				let totalPossibleScore = 0;
				
				// 计算当前总分和总可能分数
				$(".tmitem").each(function() {
					const currentScore = parseFloat($(this).find('.ff').val()) || 0;
					const maxScore = parseFloat($(this).find('.py2 span i').text()) || 0;
					
					totalScore += currentScore;
					totalPossibleScore += maxScore;
				});
				
				// 计算及格分数（60%）
				const passingScore = totalPossibleScore * 0.6;
				const submitBtn = $(".btnvvv div");
				
				// 移除之前的颜色类
				submitBtn.removeClass("score-fail score-pass");
				
				// 根据分数添加对应的颜色类
				if (totalScore >= passingScore) {
					submitBtn.addClass("score-pass");
				} else {
					submitBtn.addClass("score-fail");
				}
			}

			function submityuejuan() {
				let json = {
					id: yuepaperid,
					userId: uppid,
					answerId: dtjgid,
					cmsSubjectList: []
				}
				let alltm = $(".tmitem")
				for (let i = 0; i < alltm.length; i++) {
					json.cmsSubjectList.push({
						id: $(alltm[i]).attr("data-id"),
						option: $(alltm[i]).find('.pystr').val(),
						score: $(alltm[i]).find('.ff').val()
					})
				}
				// console.log(json)
				$.ajax({
					url: baseurl + "/paper/making",
					type: 'POST',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: JSON.stringify(json),
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							cocoMessage.success(1000, "阅卷完成！")
							closeyuepaper()
							mpaperlist()
						}
					}
				})
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 						.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}

			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是最后一页了！")
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！")
					}
				} else {
					pageindex = index
					mpaperlist()
				}
			}

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s
			}

			// 模糊搜索功能
			function fuzzySearch(input) {
				const searchTerm = $(input).val().toLowerCase();
				studentno = null;
				xyid = null;
				zyid = null;
				bjid = null;
				
				// 如果搜索词为空，重置筛选
				if (!searchTerm) {
					$("#filter-results").hide();
					mpaperlist();
					return;
				}
				
				// 更新筛选摘要 - 搜索条件
				$("#filter-summary").html(`搜索: "${searchTerm}"`)
				$("#filter-results").show()
				
				// 查询API获取符合条件的数据
				$.ajax({
					url: baseurl + "/paper/answered",
					type: 'GET',
					data: {
						pageNum: 1,
						pageSize: 100, // 一次获取较多数据以便本地筛选
						subjectId: xkid,
						paperId: paperid == 'null' ? '' : paperid
					},
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = '';
							let filtered = [];
							
							// 本地进行模糊搜索筛选
							if (res.data.list && res.data.list.length > 0) {
								filtered = res.data.list.filter(item => {
									if (!item.student) return false;
									
									// 在学院、专业、班级、学号、姓名中进行模糊匹配
									return (
										(item.student.collegeName && item.student.collegeName.toLowerCase().includes(searchTerm)) ||
										(item.student.majorName && item.student.majorName.toLowerCase().includes(searchTerm)) ||
										(item.student.className1 && item.student.className1.toLowerCase().includes(searchTerm)) ||
										(item.student.userAuth.identifier && item.student.userAuth.identifier.toLowerCase().includes(searchTerm)) ||
										(item.student.name && item.student.name.toLowerCase().includes(searchTerm))
									);
								});
							}
							
							// 处理重复学号问题，保留最高分记录
							const studentMap = new Map();
							
							// 找出每个学号的最高分记录
							filtered.forEach(item => {
								if (!item.student) return;
								
								const studentId = item.student.userAuth.identifier;
								const score = parseFloat(item.zhuguanScore || item.score || 0);
								
								if (!studentMap.has(studentId)) {
									studentMap.set(studentId, { item, score });
								} else {
									const currentRecord = studentMap.get(studentId);
									if (score > currentRecord.score) {
										studentMap.set(studentId, { item, score });
									}
								}
							});
							
							// 更新筛选摘要显示搜索结果数量
							$("#filter-summary").html(`搜索: "${searchTerm}" (共 ${studentMap.size} 条记录)`)
							
							// 渲染搜索结果
							if (studentMap.size === 0) {
								$('#ppplist').html(`<tr><td colspan="7"><div id="msmsms">没有找到匹配的数据...</div></td></tr>`);
								
								// 添加空行补齐至10行
								for (let i = 0; i < pagesize - 1; i++) {
									$('#ppplist').append(`<tr class="empty-row" style="height: 52px; border-bottom: 1px solid #e9ecef;">
										<td class="w-seq"></td>
										<td class="w-class"></td>
										<td class="w-name"></td>
										<td class="w-id"></td>
										<td class="w-time"></td>
										<td class="w-score"></td>
										<td class="w-action"></td>
									</tr>`);
								}
								
								$("#fyq").hide();
							} else {
								// 渲染搜索结果
								let index = 0;
								let renderedItems = [];
								
								studentMap.forEach(({ item }, studentId) => {
									index++;
									const lastMarkTime = item.isMark == 1 && item.updatedAt ? setDate(item.updatedAt) : '未阅';
									const totalScore = item.zhuguanScore || item.score || '未评分';
									
									// 计算及格分数（总分*0.6）并设置颜色
									const passingScore = parseFloat(totalScore) * 0.6;
									let scoreClass = '';
									if (totalScore !== '未评分') {
										scoreClass = parseFloat(totalScore) >= passingScore ? 'score-pass' : 'score-fail';
									}
									
									// 合并学院专业班级信息，添加鼠标悬停显示完整信息功能
									const collegeName = item.student && item.student.collegeName ? item.student.collegeName : "";
									const majorName = item.student && item.student.majorName ? item.student.majorName : "";
									const className = item.student && item.student.className1 ? item.student.className1 : "";
									const fullInfo = `${collegeName} / ${majorName} / ${className}`;
									
									// 获取学生姓名
									const studentName = item.student && item.student.name ? item.student.name : "";
									
									let rowHtml = '';
									if (item.isMark == '0' || item.isMark == 0) {
										rowHtml = `<tr>
											<td class="w-seq">${index}</td>
											<td class="w-class combined-info class-col" data-full="${collegeName} / ${majorName} / ${className}">${className}</td>
											<td class="w-name">${studentName}</td>
											<td class="w-id">${item.student!= null ? item.student.userAuth.identifier : ""}</td>
											<td class="w-time">未阅</td>
											<td class="w-score">-</td>
											<td class="w-action"><a class="action-btn btn-mark" onclick="yuepaper(this)" data-id="${item.id}">阅卷</a></td>
										</tr>`;
									} else {
										rowHtml = `<tr>
											<td class="w-seq">${index}</td>
											<td class="w-class combined-info class-col" data-full="${collegeName} / ${majorName} / ${className}">${className}</td>
											<td class="w-name">${studentName}</td>
											<td class="w-id">${item.student!= null ? item.student.userAuth.identifier : ""}</td>
											<td class="w-time">${lastMarkTime}</td>
											<td class="w-score ${scoreClass}">${totalScore}</td>
											<td class="w-action">
												<a class="action-btn btn-remark" onclick="yuepaper(this)" data-id="${item.id}">重阅</a>
												<a class="action-btn btn-view" onclick="viewRecord(this)" data-id="${item.id}">查看</a>
											</td>
										</tr>`;
									}
									
									renderedItems.push(rowHtml);
								});
								
								// 确保显示固定的10行
								const itemsToShow = Math.min(pagesize, renderedItems.length);
								html = renderedItems.slice(0, itemsToShow).join('');
								
								// 如果数据不足10条，添加空行补齐
								if (itemsToShow < pagesize) {
									const emptyRowsNeeded = pagesize - itemsToShow;
									for (let i = 0; i < emptyRowsNeeded; i++) {
										html += `<tr class="empty-row" style="height: 52px; border-bottom: 1px solid #e9ecef;">
											<td class="w-seq"></td>
											<td class="w-class"></td>
											<td class="w-name"></td>
											<td class="w-id"></td>
											<td class="w-time"></td>
											<td class="w-score"></td>
											<td class="w-action"></td>
										</tr>`;
									}
								}
								
								$('#ppplist').html(html);
								$("#fyq").hide(); // 搜索结果不显示分页
							}
						}
					}
				});
			}
			
			// 确保试卷筛选正常工作
			function getRecentExam() {
				$.ajax({
					url: baseurl + "/paper/recent",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200' && res.data) {
							// 如果有最近考试数据
							if (res.data.subjectId) {
								// 设置学科下拉框
								xkid = res.data.subjectId;
								$("#paperxk").val(xkid);
								// 更新学科名称显示
								let sssname = "";
								if (xkmylist) {
									xkmylist.forEach((aaa)=>{
										if(aaa.id == xkid){
											sssname = aaa.name;
										}
									});
									$("#papaname").html(sssname);
								}
								
								// 如果有试卷ID，设置为当前选中的试卷
								if (res.data.paperId && pplist) {
									paperid = res.data.paperId;
									const selectedPaper = pplist.find(paper => paper.id === res.data.paperId);
									if (selectedPaper) {
										$("#paperSearchInput").val(selectedPaper.name);
										$("#papaname").html(selectedPaper.name);
									}
								}
								
								// 获取答卷列表
								setTimeout(function() {
									mpaperlist(); // 延迟一下执行，确保数据已加载
								}, 300);
							}
						} else {
							// 如果没有最近考试数据，但有试卷列表，则默认选择第一个
							if (pplist && pplist.length > 0 && xkid) {
								paperid = pplist[0].id;
								$("#paperSearchInput").val(pplist[0].name);
								mpaperlist();
							}
						}
					}
				});
			}

			// 清除所有筛选条件
			function clearFilters() {
				// 重置筛选条件
				xkid = null
				paperid = null
				xyid = null
				zyid = null
				bjid = null
				studentno = null
				
				// 重置筛选UI
				$("#paperxk").val("null")
				$("#paperSearchInput").val("")
				$("#fuzzySearch").val("")
				
				// 更新筛选摘要
				$("#filter-results").hide()
				$("#papaname").html("请选择学科")
				
				// 重新加载数据
				$('#ppplist').html(`<tr><td colspan="7"><div id="msmsms">请选择学科或试卷</div></td></tr>`)
			}

			// 更新筛选摘要信息
			function updateFilterSummary(resultCount) {
				let hasFilter = false
				let summary = []
				
				// 检查是否有任何筛选条件
				if (xkid) {
					hasFilter = true
					let xkName = "未知学科"
					if (xkmylist) {
						xkmylist.forEach(item => {
							if (item.id == xkid) xkName = item.name
						})
					}
					summary.push(`学科: ${xkName}`)
				}
				
				if (paperid) {
					hasFilter = true
					let paperName = $("#paperSearchInput").val() || "未知试卷"
					summary.push(`试卷: ${paperName}`)
				}
				
				if (studentno) {
					hasFilter = true
					summary.push(`学号: ${studentno}`)
				}
				
				// 显示或隐藏筛选结果区域
				if (hasFilter) {
					let resultText = typeof resultCount !== 'undefined' ? `(共 ${resultCount} 条记录)` : ""
					$("#filter-summary").html(summary.join(" | ") + " " + resultText)
					$("#filter-results").show()
				} else {
					$("#filter-results").hide()
				}
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
		<script>
			// 为所有combined-info类的元素添加mousemove事件
			$(document).on('mousemove', '.combined-info', function(e) {
				// 获取鼠标位置
				var mouseX = e.clientX;
				var mouseY = e.clientY;
				
				// 添加自定义数据属性，记录鼠标位置
				$(this).attr('data-mousex', mouseX);
				$(this).attr('data-mousey', mouseY);
			});
		</script>
		<script>
		$(document).ready(function() {
			// 添加全局提示框到body
			$("body").append('<div id="global-tooltip"></div>');
			
			// 监听所有combined-info元素的鼠标事件
			$(document).on('mouseenter', '.combined-info', function(e) {
				// 获取完整信息
				var fullInfo = $(this).attr('data-full');
				
				// 更新全局提示框内容
				$('#global-tooltip').html(fullInfo);
				
				// 计算位置 - 显示在鼠标下方
				var posX = e.clientX - 125; // 居中显示
				var posY = e.clientY + 20; // 鼠标下方20px
				
				// 避免提示框超出屏幕边界
				if (posX < 10) posX = 10;
				if (posX + 250 > window.innerWidth) posX = window.innerWidth - 260;
				
				// 设置位置并显示
				$('#global-tooltip').css({
					left: posX + 'px',
					top: posY + 'px'
				}).fadeIn(200);
			});
			
			// 鼠标离开时隐藏提示框
			$(document).on('mouseleave', '.combined-info', function() {
				$('#global-tooltip').fadeOut(100);
			});
			
			// 鼠标移动时更新位置
			$(document).on('mousemove', '.combined-info', function(e) {
				if ($('#global-tooltip').is(':visible')) {
					var posX = e.clientX - 125;
					var posY = e.clientY + 20;
					
					if (posX < 10) posX = 10;
					if (posX + 250 > window.innerWidth) posX = window.innerWidth - 260;
					
					$('#global-tooltip').css({
						left: posX + 'px',
						top: posY + 'px'
					});
				}
			});
		});
		</script>
	</body>
</html>


