// 修复后的首页JavaScript代码

// 页面初始化
document.addEventListener('DOMContentLoaded', function() {
    // 初始化移动端功能
    initMobileFeatures();

    // 检查登录状态
    checkLoginStatus();

    // 加载页面数据
    loadPageData();

    // 隐藏加载动画
    setTimeout(() => {
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'none';
        }
    }, 1000);
});

function initMobileFeatures() {
    // 初始化触摸交互
    if (typeof initTouchInteractions === 'function') {
        initTouchInteractions();
    }

    // 初始化导航
    if (typeof initNavigation === 'function') {
        initNavigation();
    }

    // 初始化搜索
    if (typeof initSearch === 'function') {
        initSearch();
    }

    // 初始化返回顶部
    if (typeof initBackToTop === 'function') {
        initBackToTop();
    }

    // 初始化学习标签切换
    initLearningTabs();
}

function checkLoginStatus() {
    const userinfo = sessionStorage.getItem("userinfo");
    const loginBtn = document.getElementById('loginBtn');
    const userAvatar = document.getElementById('userAvatar');
    const userName = document.getElementById('userName');

    if (userinfo) {
        try {
            const user = JSON.parse(userinfo);
            if (loginBtn) loginBtn.style.display = 'none';
            if (userAvatar) userAvatar.style.display = 'block';
            if (userName) userName.textContent = user.name || user.realName || '用户';
        } catch (e) {
            console.error('解析用户信息失败:', e);
        }
    } else {
        if (loginBtn) loginBtn.style.display = 'block';
        if (userAvatar) userAvatar.style.display = 'none';
    }
}

function loadPageData() {
    // 加载菜单
    if (typeof loadMenu === 'function') {
        loadMenu();
    }

    // 加载轮播图
    loadBanner();

    // 加载心声社区
    loadCommunity();

    // 加载学习内容
    loadLearningContent();
}

function loadBanner() {
    // 获取轮播图数据
    $.ajax({
        url: baseurl + "/web/banner",
        type: 'GET',
        contentType: "application/json",
        headers: {
            "Authorization": sessionStorage.getItem("header")
        },
        dataType: 'json',
        success: (res) => {
            if (res.code == '200') {
                renderBanner(res.data || []);
            } else {
                renderDefaultBanner();
            }
        },
        error: (err) => {
            console.error('加载轮播图失败:', err);
            renderDefaultBanner();
        }
    });
}

function renderBanner(bannerData) {
    const bannerWrapper = document.getElementById('bannerWrapper');
    if (!bannerWrapper) return;

    let html = '';
    if (bannerData && bannerData.length > 0) {
        bannerData.forEach(item => {
            const imageUrl = item.imageUrl ? (baseurl + item.imageUrl) : '../PC/img/banner.png';
            html += `
                <div class="swiper-slide">
                    <img src="${imageUrl}" alt="${item.title || '轮播图'}" onerror="this.src='../PC/img/banner.png'">
                    <div class="slide-content">
                        <div class="slide-title">${item.title || '思政一体化平台'}</div>
                        <div class="slide-description">${item.description || item.summary || '传承红色基因，弘扬时代精神'}</div>
                    </div>
                </div>
            `;
        });
    } else {
        html = renderDefaultBanner();
    }

    bannerWrapper.innerHTML = html;
    initSwiper();
}

function renderDefaultBanner() {
    return `
        <div class="swiper-slide">
            <img src="../PC/img/banner.png" alt="思政一体化平台">
            <div class="slide-content">
                <div class="slide-title">思政一体化平台</div>
                <div class="slide-description">传承红色基因，弘扬时代精神</div>
            </div>
        </div>
    `;
}

function initSwiper() {
    // 简单的轮播图实现
    const slides = document.querySelectorAll('.swiper-slide');
    if (slides.length <= 1) {
        // 如果只有一张图片，显示默认轮播图
        if (slides.length === 0) {
            const bannerWrapper = document.getElementById('bannerWrapper');
            if (bannerWrapper) {
                bannerWrapper.innerHTML = renderDefaultBanner();
            }
        }
        return;
    }

    let currentSlide = 0;
    const totalSlides = slides.length;

    // 创建分页指示器
    const pagination = document.querySelector('.swiper-pagination');
    if (pagination) {
        let paginationHtml = '';
        for (let i = 0; i < totalSlides; i++) {
            paginationHtml += `<span class="swiper-pagination-bullet ${i === 0 ? 'swiper-pagination-bullet-active' : ''}"></span>`;
        }
        pagination.innerHTML = paginationHtml;
    }

    // 自动轮播
    const autoPlay = setInterval(() => {
        currentSlide = (currentSlide + 1) % totalSlides;
        updateSlide();
    }, 5000);

    function updateSlide() {
        slides.forEach((slide, index) => {
            slide.style.display = index === currentSlide ? 'block' : 'none';
        });

        const bullets = document.querySelectorAll('.swiper-pagination-bullet');
        bullets.forEach((bullet, index) => {
            bullet.classList.toggle('swiper-pagination-bullet-active', index === currentSlide);
        });
    }

    // 初始化显示
    updateSlide();

    // 点击分页器切换
    const bullets = document.querySelectorAll('.swiper-pagination-bullet');
    bullets.forEach((bullet, index) => {
        bullet.addEventListener('click', () => {
            currentSlide = index;
            updateSlide();
            // 重置自动播放
            clearInterval(autoPlay);
            setTimeout(() => {
                setInterval(() => {
                    currentSlide = (currentSlide + 1) % totalSlides;
                    updateSlide();
                }, 5000);
            }, 5000);
        });
    });
}

function loadCommunity() {
    const communityList = document.getElementById('communityList');
    if (!communityList) return;

    // 显示加载状态
    communityList.innerHTML = '<div class="loading-item"><div class="spinner"></div>加载中...</div>';

    // 获取心声社区数据
    $.ajax({
        url: baseurl + "/web/posts",
        type: 'GET',
        contentType: "application/json",
        headers: {
            "Authorization": sessionStorage.getItem("header")
        },
        data: {
            pageNum: 1,
            pageSize: 5
        },
        dataType: 'json',
        success: (res) => {
            if (res.code == '200') {
                // 过滤出心声社区相关的数据
                const communityData = res.data.list || [];
                renderCommunity(communityData);
            } else {
                communityList.innerHTML = '<div class="empty-state">暂无社区内容</div>';
            }
        },
        error: (err) => {
            console.error('加载心声社区失败:', err);
            communityList.innerHTML = '<div class="empty-state">暂无社区内容</div>';
        }
    });
}

function renderCommunity(communityData) {
    const communityList = document.getElementById('communityList');
    if (!communityList) return;

    if (!communityData || communityData.length === 0) {
        communityList.innerHTML = '<div class="empty-state">暂无社区内容</div>';
        return;
    }

    let html = '';
    communityData.forEach(item => {
        // 格式化时间
        const timeStr = item.createTime || item.publishedTime || item.updateTime;
        const formattedTime = timeStr ? formatTime(timeStr) : '刚刚';
        const isNew = formattedTime.includes('分钟前') || formattedTime.includes('小时前');

        html += `
            <div class="community-item" data-id="${item.id}">
                ${isNew ? '<span class="new-badge">NEW</span>' : ''}
                <div class="community-content">
                    <div class="community-title">${item.title || item.name || '无标题'}</div>
                    <div class="community-time">${formattedTime}</div>
                </div>
            </div>
        `;
    });

    communityList.innerHTML = html;

    // 绑定点击事件
    bindCommunityEvents();
}

function formatTime(timeStr) {
    if (!timeStr) return '刚刚';
    
    try {
        const time = new Date(timeStr);
        const now = new Date();
        const diff = now - time;
        
        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        
        if (minutes < 1) return '刚刚';
        if (minutes < 60) return `${minutes}分钟前`;
        if (hours < 24) return `${hours}小时前`;
        if (days < 30) return `${days}天前`;
        
        return time.toLocaleDateString();
    } catch (e) {
        return timeStr;
    }
}

function bindCommunityEvents() {
    const communityItems = document.querySelectorAll('.community-item');
    communityItems.forEach(item => {
        item.addEventListener('click', function(e) {
            const id = this.getAttribute('data-id');
            if (id) {
                // 记录点击统计
                if (typeof clicknum === 'function') {
                    clicknum(id);
                }
                
                // 跳转到社区详情页面
                setTimeout(() => {
                    window.location.href = `pages/community-detail.html?id=${id}`;
                }, 100);
            }
        });
    });
}

function initLearningTabs() {
    const tabBtns = document.querySelectorAll('.tab-btn');
    const tabContents = document.querySelectorAll('.tab-content');

    tabBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            const targetTab = this.getAttribute('data-tab');

            // 移除所有active类
            tabBtns.forEach(b => b.classList.remove('active'));
            tabContents.forEach(c => c.classList.remove('active'));

            // 添加active类
            this.classList.add('active');
            const targetContent = document.getElementById(targetTab + 'Content');
            if (targetContent) {
                targetContent.classList.add('active');
            }

            // 加载对应内容
            if (targetTab === 'redbooks') {
                loadRedBooks();
            } else if (targetTab === 'courses') {
                loadCourses();
            }
        });
    });
}

function loadLearningContent() {
    // 默认加载红色书籍
    loadRedBooks();
}

function loadRedBooks() {
    const redbooksContent = document.getElementById('redbooksContent');
    if (!redbooksContent) return;

    redbooksContent.innerHTML = '<div class="loading-item"><div class="spinner"></div>加载中...</div>';

    // 获取红色书籍数据
    $.ajax({
        url: baseurl + "/web/posts",
        type: 'GET',
        contentType: "application/json",
        headers: {
            "Authorization": sessionStorage.getItem("header")
        },
        data: {
            pageNum: 1,
            pageSize: 6
        },
        dataType: 'json',
        success: (res) => {
            if (res.code == '200') {
                renderLearningGrid(res.data.list || [], redbooksContent, 'redbook');
            } else {
                redbooksContent.innerHTML = '<div class="empty-state">暂无红色书籍</div>';
            }
        },
        error: (err) => {
            console.error('加载红色书籍失败:', err);
            redbooksContent.innerHTML = '<div class="empty-state">暂无红色书籍</div>';
        }
    });
}

function loadCourses() {
    const coursesContent = document.getElementById('coursesContent');
    if (!coursesContent) return;

    coursesContent.innerHTML = '<div class="loading-item"><div class="spinner"></div>加载中...</div>';

    // 获取课程数据
    $.ajax({
        url: baseurl + "/web/posts",
        type: 'GET',
        contentType: "application/json",
        headers: {
            "Authorization": sessionStorage.getItem("header")
        },
        data: {
            pageNum: 1,
            pageSize: 6
        },
        dataType: 'json',
        success: (res) => {
            if (res.code == '200') {
                renderLearningGrid(res.data.list || [], coursesContent, 'course');
            } else {
                coursesContent.innerHTML = '<div class="empty-state">暂无课程内容</div>';
            }
        },
        error: (err) => {
            console.error('加载课程失败:', err);
            coursesContent.innerHTML = '<div class="empty-state">暂无课程内容</div>';
        }
    });
}

function renderLearningGrid(data, container, type) {
    if (!data || data.length === 0) {
        container.innerHTML = '<div class="empty-state">暂无内容</div>';
        return;
    }

    let html = '<div class="learning-grid">';
    data.forEach(item => {
        const url = type === 'redbook' ?
            `pages/redbook-detail.html?id=${item.id}` :
            `pages/course-detail.html?id=${item.id}`;

        html += `
            <a href="${url}" class="learning-item" data-id="${item.id}">
                <div class="learning-cover">${item.title || item.name || '无标题'}</div>
                <div class="learning-info">
                    <div class="learning-title">${item.title || item.name || '无标题'}</div>
                    <div class="learning-meta">
                        <span>${type === 'redbook' ? '红色书籍' : '课程学习'}</span>
                        <span>${formatTime(item.createTime || item.publishedTime)}</span>
                    </div>
                </div>
            </a>
        `;
    });
    html += '</div>';

    container.innerHTML = html;

    // 绑定点击事件
    bindLearningEvents(container);
}

function bindLearningEvents(container) {
    const learningItems = container.querySelectorAll('.learning-item');
    learningItems.forEach(item => {
        item.addEventListener('click', function(e) {
            e.preventDefault();
            const id = this.getAttribute('data-id');
            if (id && typeof clicknum === 'function') {
                clicknum(id);
            }
            setTimeout(() => {
                window.location.href = this.getAttribute('href');
            }, 100);
        });
    });
}
