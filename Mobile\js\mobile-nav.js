// 移动端导航JavaScript

// 导航管理器
const NavigationManager = {
    currentPage: 'index',
    
    // 初始化导航
    init: function() {
        this.initBottomNav();
        this.initSideMenu();
        this.initSearch();
        this.updateActiveNav();
    },

    // 初始化底部导航
    initBottomNav: function() {
        const navItems = document.querySelectorAll('.bottom-nav .nav-item');
        
        navItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                
                // 移除所有active类
                navItems.forEach(nav => nav.classList.remove('active'));
                
                // 添加active类到当前项
                item.classList.add('active');
                
                // 获取目标页面
                const href = item.getAttribute('href');
                if (href && href !== '#') {
                    this.navigateTo(href);
                }
            });
        });
    },

    // 初始化侧边菜单
    initSideMenu: function() {
        this.loadMenuItems();
    },

    // 加载菜单项
    loadMenuItems: function() {
        getclass().then(menuData => {
            this.renderMenuItems(menuData);
        }).catch(error => {
            console.error('加载菜单失败:', error);
            this.renderDefaultMenu();
        });
    },

    // 渲染菜单项
    renderMenuItems: function(menuData) {
        const menuItems = document.getElementById('menuItems');
        if (!menuItems) return;
        
        let html = '';
        
        menuData.forEach(item => {
            const isActive = this.isMenuItemActive(item);
            html += `
                <a href="${item.redirectUrl || '#'}" class="menu-item ${isActive ? 'active' : ''}" data-name="${item.name}">
                    ${item.name}
                </a>
            `;
            
            // 如果有子菜单（如党建学习）
            if (item.children && item.children.length > 0) {
                item.children.forEach(child => {
                    html += `
                        <a href="${child.redirectUrl || '#'}" class="menu-item sub-item" data-name="${child.name}">
                            　${child.name}
                        </a>
                    `;
                });
            }
        });
        
        menuItems.innerHTML = html;
        
        // 绑定菜单项点击事件
        this.bindMenuItemEvents();
    },

    // 渲染默认菜单
    renderDefaultMenu: function() {
        const menuItems = document.getElementById('menuItems');
        if (!menuItems) return;
        
        const defaultMenu = [
            { name: '首页', url: 'index.html' },
            { name: '在线学习', url: 'pages/learning.html' },
            { name: '红色书籍', url: 'pages/redbooks.html' },
            { name: '课程学习', url: 'pages/courses.html' },
            { name: '心声社区', url: 'pages/community.html' },
            { name: 'VR红色游学', url: 'pages/vrtour.html' },
            { name: '虚仿实验空间', url: 'pages/experiment.html' },
            { name: '医德博物馆', url: 'pages/museum.html' },
            { name: '总书记的足迹', url: 'pages/footprint.html' },
            { name: '个人中心', url: 'pages/profile.html' }
        ];
        
        let html = '';
        defaultMenu.forEach(item => {
            const isActive = window.location.pathname.includes(item.url);
            html += `
                <a href="${item.url}" class="menu-item ${isActive ? 'active' : ''}" data-name="${item.name}">
                    ${item.name}
                </a>
            `;
        });
        
        menuItems.innerHTML = html;
        this.bindMenuItemEvents();
    },

    // 绑定菜单项事件
    bindMenuItemEvents: function() {
        const menuItems = document.querySelectorAll('.menu-item');
        
        menuItems.forEach(item => {
            item.addEventListener('click', (e) => {
                e.preventDefault();
                
                const href = item.getAttribute('href');
                if (href && href !== '#') {
                    // 关闭侧边菜单
                    TouchManager.closeSideMenu();
                    
                    // 延迟导航，等待菜单关闭动画
                    setTimeout(() => {
                        this.navigateTo(href);
                    }, 300);
                }
            });
        });
    },

    // 检查菜单项是否激活
    isMenuItemActive: function(item) {
        const currentPath = window.location.pathname;
        const currentPage = currentPath.split('/').pop() || 'index.html';
        
        if (item.redirectUrl) {
            const menuPage = item.redirectUrl.split('/').pop();
            return currentPage === menuPage;
        }
        
        return false;
    },

    // 初始化搜索功能
    initSearch: function() {
        const searchBtn = document.getElementById('searchBtn');
        const searchPanel = document.getElementById('searchPanel');
        const searchClose = document.getElementById('searchClose');
        const searchSubmit = document.getElementById('searchSubmit');
        const searchInput = document.getElementById('searchInput');
        
        if (searchBtn) {
            searchBtn.addEventListener('click', () => {
                this.openSearch();
            });
        }
        
        if (searchClose) {
            searchClose.addEventListener('click', () => {
                this.closeSearch();
            });
        }
        
        if (searchSubmit) {
            searchSubmit.addEventListener('click', () => {
                this.performSearch();
            });
        }
        
        if (searchInput) {
            searchInput.addEventListener('keypress', (e) => {
                if (e.key === 'Enter') {
                    this.performSearch();
                }
            });
        }
    },

    // 打开搜索
    openSearch: function() {
        const searchPanel = document.getElementById('searchPanel');
        const searchInput = document.getElementById('searchInput');
        
        if (searchPanel) {
            searchPanel.classList.add('active');
            document.body.style.overflow = 'hidden';
            
            // 聚焦输入框
            setTimeout(() => {
                if (searchInput) {
                    searchInput.focus();
                }
            }, 300);
        }
    },

    // 关闭搜索
    closeSearch: function() {
        const searchPanel = document.getElementById('searchPanel');
        const searchInput = document.getElementById('searchInput');
        
        if (searchPanel) {
            searchPanel.classList.remove('active');
            document.body.style.overflow = '';
            
            // 清空输入框
            if (searchInput) {
                searchInput.value = '';
                searchInput.blur();
            }
        }
    },

    // 执行搜索
    performSearch: function() {
        const searchInput = document.getElementById('searchInput');
        if (!searchInput) return;
        
        const query = searchInput.value.trim();
        if (!query) {
            MobileUtils.showToast('请输入搜索关键词', 'warning');
            return;
        }
        
        // 关闭搜索面板
        this.closeSearch();
        
        // 跳转到搜索结果页面
        this.navigateTo(`pages/search.html?q=${encodeURIComponent(query)}`);
    },

    // 导航到指定页面
    navigateTo: function(url) {
        // 检查是否需要登录
        if (this.requiresLogin(url)) {
            const loginStatus = checkLoginStatus();
            if (!loginStatus.isLoggedIn) {
                this.redirectToLogin();
                return;
            }
        }
        
        // 显示加载状态
        this.showNavigationLoading();
        
        // 导航到目标页面
        window.location.href = url;
    },

    // 检查页面是否需要登录
    requiresLogin: function(url) {
        const loginRequiredPages = [
            'profile.html',
            'learning.html',
            'achievement.html',
            'tasks.html'
        ];
        
        return loginRequiredPages.some(page => url.includes(page));
    },

    // 重定向到登录页面
    redirectToLogin: function() {
        const currentUrl = encodeURIComponent(window.location.href);
        window.location.href = `https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html&redirect=${currentUrl}`;
    },

    // 显示导航加载状态
    showNavigationLoading: function() {
        // 可以在这里添加页面切换的加载动画
        const loadingOverlay = document.getElementById('loadingOverlay');
        if (loadingOverlay) {
            loadingOverlay.style.display = 'flex';
        }
    },

    // 更新激活的导航项
    updateActiveNav: function() {
        const currentPath = window.location.pathname;
        const navItems = document.querySelectorAll('.bottom-nav .nav-item');
        
        navItems.forEach(item => {
            item.classList.remove('active');
            
            const href = item.getAttribute('href');
            if (href && currentPath.includes(href.replace('.html', ''))) {
                item.classList.add('active');
            }
        });
        
        // 如果是首页，激活首页导航
        if (currentPath.endsWith('index.html') || currentPath.endsWith('/')) {
            const homeNav = document.querySelector('.bottom-nav .nav-item[href="index.html"]');
            if (homeNav) {
                homeNav.classList.add('active');
            }
        }
    },

    // 设置页面标题
    setPageTitle: function(title) {
        document.title = title + ' - 思政一体化平台';
        
        // 更新头部标题（如果有的话）
        const headerTitle = document.querySelector('.mobile-header .page-title');
        if (headerTitle) {
            headerTitle.textContent = title;
        }
    }
};

// 初始化导航功能
function initNavigation() {
    NavigationManager.init();
}

// 加载菜单数据
function loadMenu() {
    NavigationManager.loadMenuItems();
}

// 初始化搜索功能
function initSearch() {
    NavigationManager.initSearch();
}

// 导出给全局使用
window.NavigationManager = NavigationManager;
window.initNavigation = initNavigation;
window.loadMenu = loadMenu;
window.initSearch = initSearch;
