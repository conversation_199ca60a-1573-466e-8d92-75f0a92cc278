/* 移动端组件样式 */

/* 卡片组件 */
.card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    margin-bottom: 16px;
    transition: all 0.3s ease;
}

.card:active {
    transform: translateY(1px);
    box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}

.card-header {
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
}

.card-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.card-body {
    padding: 16px;
}

.card-footer {
    padding: 12px 16px;
    background: #f8f9fa;
    border-top: 1px solid #f0f0f0;
}

/* 按钮组件 */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    cursor: pointer;
    transition: all 0.3s ease;
    min-height: 44px;
    gap: 8px;
}

.btn:active {
    transform: scale(0.98);
}

.btn-primary {
    background: #c00714;
    color: white;
}

.btn-primary:hover {
    background: #a00610;
}

.btn-secondary {
    background: #f8f9fa;
    color: #666;
    border: 1px solid #e5e5e5;
}

.btn-secondary:hover {
    background: #e9ecef;
}

.btn-outline {
    background: transparent;
    color: #c00714;
    border: 1px solid #c00714;
}

.btn-outline:hover {
    background: #c00714;
    color: white;
}

.btn-small {
    padding: 8px 16px;
    font-size: 12px;
    min-height: 36px;
}

.btn-large {
    padding: 16px 24px;
    font-size: 16px;
    min-height: 52px;
}

.btn-block {
    width: 100%;
}

.btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    transform: none !important;
}

/* 输入框组件 */
.form-group {
    margin-bottom: 16px;
}

.form-label {
    display: block;
    margin-bottom: 8px;
    font-size: 14px;
    font-weight: 500;
    color: #333;
}

.form-input {
    width: 100%;
    padding: 12px 16px;
    border: 1px solid #e5e5e5;
    border-radius: 8px;
    font-size: 16px;
    background: white;
    transition: all 0.3s ease;
    min-height: 44px;
}

.form-input:focus {
    outline: none;
    border-color: #c00714;
    box-shadow: 0 0 0 3px rgba(192, 7, 20, 0.1);
}

.form-input::placeholder {
    color: #999;
}

.form-textarea {
    resize: vertical;
    min-height: 80px;
}

/* 列表组件 */
.list {
    background: white;
    border-radius: 12px;
    overflow: hidden;
    margin-bottom: 16px;
}

.list-item {
    display: flex;
    align-items: center;
    padding: 16px;
    border-bottom: 1px solid #f0f0f0;
    text-decoration: none;
    color: #333;
    transition: background 0.3s ease;
    min-height: 60px;
}

.list-item:last-child {
    border-bottom: none;
}

.list-item:active {
    background: #f8f9fa;
}

.list-item-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 12px;
    background: #f0f0f0;
    color: #666;
}

.list-item-content {
    flex: 1;
}

.list-item-title {
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 4px;
    line-height: 1.3;
}

.list-item-subtitle {
    font-size: 12px;
    color: #999;
    line-height: 1.3;
}

.list-item-action {
    color: #999;
    font-size: 12px;
}

/* 标签组件 */
.tag {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 11px;
    font-weight: 500;
    line-height: 1;
}

.tag-primary {
    background: rgba(192, 7, 20, 0.1);
    color: #c00714;
}

.tag-success {
    background: rgba(40, 167, 69, 0.1);
    color: #28a745;
}

.tag-warning {
    background: rgba(255, 193, 7, 0.1);
    color: #ffc107;
}

.tag-info {
    background: rgba(23, 162, 184, 0.1);
    color: #17a2b8;
}

.tag-secondary {
    background: rgba(108, 117, 125, 0.1);
    color: #6c757d;
}

/* 徽章组件 */
.badge {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    min-width: 18px;
    height: 18px;
    padding: 0 6px;
    border-radius: 9px;
    font-size: 10px;
    font-weight: 600;
    line-height: 1;
    color: white;
    background: #c00714;
}

.badge-dot {
    width: 8px;
    height: 8px;
    min-width: 8px;
    padding: 0;
    border-radius: 50%;
}

/* 分割线组件 */
.divider {
    height: 1px;
    background: #f0f0f0;
    margin: 16px 0;
}

.divider-text {
    position: relative;
    text-align: center;
    margin: 20px 0;
    color: #999;
    font-size: 12px;
}

.divider-text::before {
    content: '';
    position: absolute;
    top: 50%;
    left: 0;
    right: 0;
    height: 1px;
    background: #f0f0f0;
    z-index: 1;
}

.divider-text span {
    background: white;
    padding: 0 12px;
    position: relative;
    z-index: 2;
}

/* 空状态组件 */
.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;
}

.empty-state-icon {
    width: 64px;
    height: 64px;
    margin: 0 auto 16px;
    opacity: 0.5;
}

.empty-state-title {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 8px;
    color: #666;
}

.empty-state-description {
    font-size: 14px;
    line-height: 1.5;
    margin-bottom: 20px;
}

/* 加载组件 */
.loading {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    color: #999;
    font-size: 14px;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #c00714;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

/* 消息提示组件 */
.toast {
    position: fixed;
    top: 70px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.8);
    color: white;
    padding: 12px 20px;
    border-radius: 20px;
    font-size: 14px;
    z-index: 3000;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.toast.show {
    opacity: 1;
    visibility: visible;
}

.toast-success {
    background: rgba(40, 167, 69, 0.9);
}

.toast-error {
    background: rgba(220, 53, 69, 0.9);
}

.toast-warning {
    background: rgba(255, 193, 7, 0.9);
    color: #333;
}

/* 模态框组件 */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    z-index: 2500;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 20px;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-content {
    background: white;
    border-radius: 12px;
    width: 100%;
    max-width: 400px;
    max-height: 80vh;
    overflow-y: auto;
    transform: scale(0.9);
    transition: transform 0.3s ease;
}

.modal.show .modal-content {
    transform: scale(1);
}

.modal-header {
    padding: 20px 20px 0;
    text-align: center;
}

.modal-title {
    font-size: 18px;
    font-weight: 600;
    margin-bottom: 8px;
}

.modal-body {
    padding: 20px;
}

.modal-footer {
    padding: 0 20px 20px;
    display: flex;
    gap: 12px;
}

.modal-footer .btn {
    flex: 1;
}

/* 响应式调整 */
@media (max-width: 375px) {
    .card {
        border-radius: 8px;
        margin-bottom: 12px;
    }
    
    .card-header,
    .card-body {
        padding: 12px;
    }
    
    .btn {
        padding: 10px 16px;
        font-size: 13px;
    }
    
    .form-input {
        padding: 10px 12px;
        font-size: 15px;
    }
    
    .list-item {
        padding: 12px;
        min-height: 56px;
    }
    
    .list-item-icon {
        width: 36px;
        height: 36px;
    }
}
