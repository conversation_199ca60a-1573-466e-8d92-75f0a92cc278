body {
	background: #FBFBFB;
}

.topbox {
	width: 100%;
	height: 3.385416rem;
	background: #FFFFFF;
	line-height: 3.385416rem;
}

.topbox div {
	width: 66.666666rem;
	margin: 0px auto;
	display: flex;
}

.topbox img {
	width: 0.78125rem;
	display: block;
	padding-left: 0.260416rem;
	padding-right: 0.260416rem;
}

.topbox div label {
	font-size: 0.833333rem;
	color: #333333;
	padding: 0px 0.78125rem;
}

.contenttopview {
	display: flex;
	justify-content: space-between;
	align-items: center;
	/* margin-top: 2.083333rem; */
	padding-bottom: 1.5625rem;
	border-bottom: 0.052083rem solid #dedede;
}

.content {
	width: 66.666666rem;
	margin: 0px auto;
	min-height: 35rem;
}

.contentitem a {
	font-size: 0.9375rem;
	color: #333333;
	margin: 0px 0.78125rem;
	cursor: pointer;
}

.contentitem {
	display: flex;
	position: relative;
}

.ssdiv {
	width: 14.0625rem;
	height: 1.666666rem;
	border: 1px solid #c00714;
	border-radius: 0.260416rem;
	box-sizing: border-box;
}

.dztsg {
	width: 6.25rem;
	height: 1.666666rem;
	background: #c00714;
	border-radius: 0.260416rem;
	color: #FFFFFF;
	font-size: 0.833333rem;
	margin-left: 0.78125rem;
	cursor: pointer;
	display: flex;
	justify-content: center;
	align-items: center;
}

.sss {
	align-items: center;
	justify-content: flex-end;
}

.ssdiv {
	display: flex;
	overflow: hidden;
}

.ssdiv .select {
	width: calc(100% - 3.125rem - 6.25rem);
	background: #FFFFFF !important;
	cursor: pointer;
	padding: 0px 0.260416rem;
	display: flex;
	align-items: center;
	justify-content: space-around;
}

.ssdiv .select label {
	font-size: 0.729166rem;
	color: #999999;
	margin: 0px !important;
}

.opbox {
	position: absolute;
	top: 1.666666rem;
	left: 0.260416rem;
	background: #FFFFFF;
	width: auto;
	text-align: center;
	z-index: 99;
	font-size: 0.729166rem;
	line-height: 1.302083rem;
	color: #999999;
	box-shadow: 0px 0px 10px #e4e4e4;
	padding: 0.260416rem;
	display: none;
}

.opbox div {
	cursor: pointer;
}

.opbox div:hover {
	color: #c00714;
}

.ssdiv .ss {
	background: #c00714;
	color: #FFFFFF;
	font-size: 0.833333rem;
	display: flex;
	justify-content: center;
	align-items: center;
	width: 3.125rem;
	cursor: pointer;
}

.ssdiv input {
	width: 6.25rem;
	border: none;
	outline-style: none;
	font-size: 0.833333rem;
	line-height: 1.666666rem;
}

.cctitle {
	margin-top: 2.604166rem;
	font-size: 1.25rem;
	font-weight: bold;
	color: #333333;
}

.sjbox {
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start;
	margin-bottom: 2.083333rem;
}

.txtitem {
	width: 19%;
	margin-top: 1.041666rem;
	padding: 0.260416rem;
	box-sizing: border-box;
	overflow: hidden;
	cursor: pointer;
	margin-right: calc(5% / 4);
}
.txtitem:nth-child(5n+5){
	margin-right: 0;
}
.moretxtitem {
	width: 19%;
	height: 16.489583rem;
	margin-top: 1.041666rem;
	padding: 0.260416rem;
	box-sizing: border-box;
	border-radius: 0.260416rem;
	overflow: hidden;
	cursor: pointer;
}

.moretxtitem2 {
	width: 19%;
	height: 7.372395rem;
	margin-top: 1.041666rem;
	padding: 0.260416rem;
	box-sizing: border-box;
	border-radius: 0.260416rem;
	overflow: hidden;
	cursor: pointer;
}

.morebox {
	height: 100%;
	background: #c00714;
	border-radius: 0.260416rem;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: center;
}

.morebox2 {
	height: 100%;
	background: #c00714;
	border-radius: 0.260416rem;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: center;
}

.morebox img {
	display: block;
}

.txtitem .topitem img {
	display: block;
	width: 100%;
	border-radius: 0.260416rem;
}

.mb div {
	display: flex;
	justify-content: center;
	padding-top: 0.260416rem;
	padding-bottom: 0.260416rem;
	font-size: 1.145833rem;
	font-weight: bold;
	color: #f8b967;
}

.mb2 div {
	display: flex;
	justify-content: center;
	padding-top: 0.260416rem;
	padding-bottom: 0.260416rem;
	font-size: 1.145833rem;
	font-weight: bold;
	color: #FFFFFF;
}

.topitem label img {
	padding-right: 0.260416rem;
}


.txtitem:hover {
	box-shadow: 0px 0px 10px #E4E4E4;
	background: #FFFFFF;
}

.topitem {
	position: relative;
}

.topitem label {
	background: rgba(0, 0, 0, 0.5);
	border-radius: 0.260416rem;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 0.625rem;
	color: #ffffff;
	padding: 0.104166rem 0.260416rem;
}

.topitem .icobox {
	position: absolute;
	bottom: 0.104166rem;
	right: 0.104166rem;
	left: 0.104166rem;
	display: flex;
	justify-content: flex-end;
}

.title {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	font-size: 0.9375rem;
	color: #333333;
}

.bottomitem {
	padding: 0.520833rem 0rem;
}

.zz {
	font-size: 0.729166rem;
	color: #cecece;
	padding-top: 0.260416rem;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
}

.more1 {
	background: #c00714 !important;
	height: 100%;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: center;
}

.more1 .iii {
	width: 100%;
	text-align: center;
}

.zzico {
	width: 0.46875rem !important;
	height: 0.520833rem !important;
	display: block;
}

.gklico {
	width: 0.677083rem !important;
	height: 0.416666rem !important;
	display: block;
}

.icobox label {
	margin-left: 0.260416rem;
}

.itemtype {
	position: absolute;
	top: 0.104166rem;
	left: 0.104166rem;
	color: #FFFFFF;
	font-size: 0.520833rem;
	padding: 0.104166rem 0.3125rem;
	border-radius: 0.260416rem;
}

.pdf {
	background: #f8b967;
}

.ppt,
.pptx {
	background: #f36933;
}

.video,
.mp4,
.rmvb {
	background: #7a92bb;
}

.titleactive {
	color: #c00714 !important;
	font-weight: bold;
}

.fybox {
	padding-bottom: 3.125rem;
}

.tjbox {
	height: 3.645833rem;
	border-bottom: 0.052083rem solid #dedede;
	display: flex;
	align-items: center;
	padding: 0px 0.78125rem;
}

.tjbox div {
	width: calc(100% - 7.8125rem);
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}

.tjbox label {
	font-size: 0.833333rem;
	color: #999999;
	padding-right: 1.041666rem;
}

.tjbox span {
	margin-left: 1.041666rem;
	margin-right: 1.041666rem;
	font-size: 0.833333rem;
	color: #666666;
	cursor: pointer;
	line-height: 1.5625rem;
}

.tjactive {
	color: #c00714 !important;
	font-weight: bold;
}

.noborder {
	border: none !important;
}

.tjbox select {
	width: 13.541666rem;
	height: 1.5625rem;
	margin-right: 1.5625rem;
	font-size: 0.833333rem;
	color: #666666;
	outline-style: none;
	border-radius: 0 !important;
	background: #f3f3f3;
	border-color: #cecece;
}

.bag {
	background: #f3f3f3 !important;
}

.kjtop {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.kjpfbtn {
	width: 6.25rem;
	height: 2.083333rem;
	background: #c00714;
	color: #FFFFFF;
	font-size: 0.833333rem;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 0.260416rem;
	cursor: pointer;
}

.kjpfbtn img {
	width: 0.9375rem;
	height: 0.9375rem;
	display: block;
	padding-right: 0.260416rem;
}

.kjname {
	font-size: 1.145833rem;
	color: #333333;
	font-weight: bold;
	padding-top: 1.041666rem;
}

.kjzz {
	display: flex;
	align-items: center;
	padding-top: 0.520833rem;
}

.kjzz label {
	color: #cecece;
	font-size: 0.833333rem;
	display: flex;
	align-items: center;
	padding-right: 0.520833rem;
}

.kjzz label img {
	display: block;
	width: 0.9375rem;
	padding-right: 0.260416rem;
}

.kjbox {
	display: none;
	justify-content: space-between;
	margin-top: 1.041666rem;
}

.kjview {
	width: 50rem;
	position: relative;
}

.kjdg {
	width: 15.625rem;
}

.kjlist {
	background: #FFFFFF;
	height: 26.822916rem;
	overflow-y: auto;
}

.kjitem {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding: 0.520833rem;
	background: #FFFFFF;
	cursor: pointer;
}

.kjitem .fm {
	width: 3.385416rem;
	height: 1.875rem;
	display: block;
	padding-right: 0.260416rem;
}

.kjitem .zt {
	width: 1.041666rem;
	display: block;
	padding-left: 0.260416rem;
}

.kjbt {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	color: #333333;
	font-size: 0.729166rem;
}

.kjdgname {
	background: #e1e1e1;
	color: #333333;
	font-size: 0.9375rem;
	font-weight: bold;
	height: 2.083333rem;
	line-height: 2.083333rem;
	padding: 0px 0.520833rem;
	border-bottom: 0.052083rem dashed #cbcbcb;
}

.kjss {
	background: #e1e1e1;
	height: 2.395833rem;
	display: flex;
	align-items: center;
}

.kjss div {
	width: 100%;
	background: #FFFFFF;
	box-sizing: border-box;
	border: 1px solid #c1c1c1;
	border-radius: 5.208333rem;
	margin-left: 0.520833rem;
	margin-right: 0.520833rem;
	display: flex;
	align-items: center;
	height: 1.5625rem;
	overflow: hidden;
}

.kjss div img {
	display: block;
	width: 0.9375rem;
	padding-left: 0.520833rem;
}

.kjss div input {
	border: none;
	outline-style: none;
	width: 100%;
	height: 1.5625rem;
	line-height: 1.5625rem;
	font-size: 0.833333rem;
	margin-right: 0.520833rem;
	margin-left: 0.260416rem;
}

.kjjj {
	background: #FFFFFF;
	margin-top: 1.5625rem;
	padding-bottom: 3.645833rem;
	margin-bottom: 3.125rem;
}

.topkjjjs {
	height: 3.802083rem;
	display: flex;
	justify-content: flex-start;
	align-items: center;
	border-bottom: 1px solid #cecece;
}

.topkjjjs div {
	width: 7.291666rem;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	font-size: 0.833333rem;
	color: #333333;
	height: 3.802083rem;
}

.aaakj {
	color: #c00714 !important;
	font-weight: bold;
	position: relative;
}

.aaakj::after {
	content: "";
	height: 0.104166rem;
	width: 100%;
	background: #c00714;
	position: absolute;
	left: 0;
	right: 0;
	bottom: -0.104166rem;
}

.topkjjjs div img {
	display: block;
	width: 0.833333rem;
	padding-right: 0.260416rem;
}

.kjxqbox {
	padding: 0.78125rem;
	background: #FFFFFF;
}

.kjboxtitle {
	color: #333333;
	font-size: 0.9375rem;
	font-weight: bold;
	line-height: 2.604166rem;
	margin-top: 1.041666rem;
}

.kjboxtitle:first-child {
	margin-top: 0px;
}

.jj p {
	text-indent: 2em;
	font-size: 0.833333rem;
	color: #999999;
	line-height: 1.25rem;
	padding-bottom: 0.520833rem;
}

.abq label {
	font-size: 0.833333rem;
	color: #c00714;
	text-decoration: underline;
	cursor: pointer;
}

.topbag {
	line-height: 2.604166rem;
	font-size: 0.9375rem;
	color: #FFFFFF;
	background: rgba(0, 0, 0, 0.7);
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	padding-left: 0.78125rem;
	padding-right: 0.78125rem;
	display: none;
}

#img {
	width: 100%;
	display: block;
}

.czbox {
	height: 3.125rem;
	background: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.czbox img {
	margin-left: 0.260416rem;
	width: 1.875rem;
	display: block;
	cursor: pointer;
}

.czbox div label {
	width: 5.729166rem;
	height: 1.875rem;
	background: #c00714;
	color: #FFFFFF;
	font-size: 0.833333rem;
	display: block;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 0.260416rem;
	margin-left: 0.260416rem;
	margin-right: 0.260416rem;
	cursor: pointer;
}

.czbox div {
	display: flex;
}

#qp {
	position: fixed;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.8);
	display: none;
	align-items: center;
	justify-content: center;
	z-index: 99;
}

#qp img {
	width: 60%;
	display: block;
}

.sjinfoview {
	background: #FFFFFF;
	padding: 2.083333rem;
	box-sizing: border-box;
	margin-top: 2.916666rem;
	display: flex;
}

.sjinfoleft {
	width: 15.104166rem;
}

.sjinfoleft img {
	display: block;
	width: 100%;
}

.sjinforight {
	width: calc(100% - 15.104166rem - 1.302083rem);
	padding-left: 1.302083rem;
	position: relative;
}

.sjname {
	font-size: 1.145833rem;
	color: #333333;
}

.sjname span {
	font-size: 0.729166rem;
	padding-left: 1.5625rem;
}

.sjstr {
	display: flex;
	padding-top: 0.520833rem;
	padding-bottom: 0.520833rem;
}

.sjstr label {
	display: flex;
	align-items: center;
	margin-right: 1.25rem;
}

.sjstr label span {
	font-size: 0.729166rem;
	color: #cecece;
	padding-left: 0.260416rem;
}

.sjjj {
	color: #999999;
	font-size: 0.833333rem;
	line-height: 1.354166rem;
	padding-top: 0.520833rem;
}

#sjbtn {
	position: absolute;
	left: 1.302083rem;
	bottom: 0;
	width: 7.291666rem;
	height: 2.1875rem;
	border-radius: 0.260416rem;
	color: #FFFFFF;
	font-size: 0.9375rem;
	background: #c00714;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.tjview {
	margin-top: 0.78125rem;
	background: #FFFFFF;
}

.tjtoptitle {
	height: 2.34375rem;
	display: flex;
	align-items: center;
	border-bottom: 0.052083rem solid #dedede;
}

.tjtoptitle span {
	font-size: 0.9375rem;
	color: #c00714;
	padding-left: 2.083333rem;
	padding-right: 2.083333rem;
	line-height: 2.34375rem;
	position: relative;
}

.tjtoptitle span::after {
	height: 0.104166rem;
	content: "";
	background: #c00714;
	position: absolute;
	bottom: -0.052083rem;
	left: 0;
	right: 0;
}

.swiper {
	overflow: hidden;
}

.swiper-slide img {
	width: 100%;
}

.pppp {
	padding: 2.083333rem 2.083333rem;
	display: flex;
	align-items: center;
}

#swiperleft,
#swiperright {
	display: flex;
	align-items: center;
}
#swiperleft{
	margin-right: 0.78125rem;
}
#swiperright{
	margin-left: 0.78125rem;
}
.sm {
	font-size: 0.9375rem;
	color: #333333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}
#swiperleft img,
#swiperright img {
	display: block;
	cursor: pointer;
}
.pjtitle{
	color: #333333;
	font-size: 0.9375rem;
	font-weight: bold;
	padding-left: 0.78125rem;
	padding-right: 0.78125rem;
	padding-top: 1.041666rem;
}
.xxbox{
	padding-left: 5.208333rem;
	padding-top: 0.78125rem;
	font-size: 0.9375rem;
	color: #333333;
}
.xxbox div{
	padding-bottom: 0.78125rem;
	display: flex;
	align-items: center;
}
.xxbox div label{
	margin-left: 0.520833rem;
	cursor: pointer;
}
.xxbox div span{
	padding-right: 0.520833rem;
}
.xx1{
	width: 0.78125rem;
	height: 0.78125rem;
	background: url(../img/xx1.png) no-repeat;
	background-size: cover;
}
.xx2{
	width: 0.78125rem;
	height: 0.78125rem;
	background: url(../img/xx2.png) no-repeat;
	background-size: cover;
}
.xx3{
	width: 0.78125rem;
	height: 0.78125rem;
	background: url(../img/xx3.png) no-repeat;
	background-size: cover;
}
.fs{
	padding: 0 !important;
	margin-left: 1.041666rem;
}
.pinjiabox{
	position: fixed;
	top: 0;
	right: 0;
	left: 0;
	bottom: 0;
	z-index: 999;
	background: rgba(0,0,0,0.8);
	align-items: center;
	justify-content: center;
	display: none;
}
.pjbbb{
	width: 17.708333rem;
	height: 16.666666rem;
	background: #FFFFFF;
	border-radius: 0.260416rem;
	overflow: hidden;
}
.topbags{
	background: url(../img/bjtt2.png) no-repeat;
	height: 3.125rem;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 0.9375rem;
	color: #FFFFFF;
}
.topbags img{
	width: 0.833333rem;
	padding-right: 0.260416rem;
	display: block;
}
.pjbbb .xxbox{
	padding: 0;
	padding-top: 1.5625rem;
}
.pjbbb .xxbox div{
	justify-content: center;
}
.gbgbh{
	background: #c00714 !important;
}
.xxbtnview{
	display: flex;
	width: 100%;
	justify-content: center;
	padding-top: 2.604166rem;
}
.str{
	font-size: 0.833333rem;
	color: #999999;
	text-align: center;
	padding-top: 0.520833rem;
}
.xxbtnview div{
	width: 6.25rem;
	height: 1.770833rem;
	font-size: 0.833333rem;
	color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 0.260416rem;
	cursor: pointer;
}
.xxbtnview div:first-child{
	background: #c00714;
	margin-right: 0.520833rem;
}
.xxbtnview div:last-child{
	background: #999999;
	margin-left: 0.520833rem;
}
#ypf{
	padding-top: 1.5625rem;
}
.copybtn{
	font-size: 0.729167rem;
	color: #FFFFFF;
	background: #c00714;
	border-radius: 5.208333rem;
	padding: 0.15625rem 0.520833rem;
	display: flex;
	width: 3.385417rem;
	align-items: center;
	justify-content: center;
	margin-top: 0.520833rem;
}