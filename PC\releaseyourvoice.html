<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-个人中心</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/wangEditor.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<!-- 导航增强脚本 -->
		<script src="./js/nav-enhancement.js" type="text/javascript" charset="utf-8"></script>
		<style>
			/* 导航菜单容器 */
			.leftitembox {
				background: transparent;
				padding: 20px 0;
				position: relative;
				z-index: 2;
				min-height: 450px; /* 确保导航栏最小高度一致 */
				box-sizing: border-box;
			}
			
			.leftitembox::before {
				content: '';
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 85%;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.15), transparent);
			}
			
			/* 导航菜单项 - 现代化设计与图标，移除slideIn动画 */
			.leftitem {
				height: auto !important;
				padding: 16px 24px !important;
				display: flex !important;
				justify-content: flex-start !important;
				align-items: center !important;
				font-size: 15px !important;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif !important;
				font-weight: 500 !important;
				color: #4a5568 !important;
				border-bottom: none !important;
				text-decoration: none !important;
				transition: color 0.2s ease, background-color 0.2s ease !important;
				position: relative !important;
				margin: 4px 16px !important;
				border-radius: 12px !important;
				background: transparent !important;
				overflow: hidden !important;
				min-height: 48px !important;
				box-sizing: border-box !important;
				/* 移除slideIn动画，导航项立即显示 */
				animation: none !important;
				opacity: 1 !important;
				transform: translateX(0) !important;
				animation-delay: 0s !important;
			}
			
			/* 为不同菜单项添加对应的SVG图标 */
			.leftitem::before {
				content: '';
				width: 20px;
				height: 20px;
				margin-right: 12px;
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				opacity: 0.7;
				transition: opacity 0.2s ease;
				flex-shrink: 0;
			}
			
			/* 菜单项悬浮态 */
			.leftitem:hover {
				background: linear-gradient(135deg, rgba(220, 53, 69, 0.06), rgba(231, 76, 60, 0.08)) !important;
				color: #dc3545 !important;
			}
			
			.leftitem:hover::before {
				opacity: 1;
			}
			
			/* 激活状态的菜单项 - 统一标准双色渐变背景 */
			.leftitem.activeleftitem,
			a.leftitem.activeleftitem,
			.leftitembox .leftitem.activeleftitem,
			#teambox .leftitem.activeleftitem {
				font-weight: 700 !important;
				color: white !important;
				background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
				background-size: 200% 200% !important;
				animation: activeGradient 3s ease infinite !important;
				box-shadow: 0 4px 16px rgba(220, 53, 69, 0.25) !important;
				border-radius: 12px !important;
				opacity: 1 !important;
				transform: translateX(0) !important;
			}
			
			@keyframes activeGradient {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.leftitem.activeleftitem::before,
			a.leftitem.activeleftitem::before,
			.leftitembox .leftitem.activeleftitem::before,
			#teambox .leftitem.activeleftitem::before {
				opacity: 1 !important;
				filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
			}
			
			/* 移除所有导航项的动画延迟 */
			.leftitem:nth-child(1),
			.leftitem:nth-child(2), 
			.leftitem:nth-child(3), 
			.leftitem:nth-child(4), 
			.leftitem:nth-child(5), 
			.leftitem:nth-child(6), 
			.leftitem:nth-child(7) { 
				animation: none !important;
				animation-delay: 0s !important;
				opacity: 1 !important;
				transform: translateX(0) !important;
			}
			
			/* 默认隐藏发布文章按钮界面，显示表单界面 */
			#view1 {
				display: none !important;
			}
			
			#view2 {
				display: block !important;
			}
		</style>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a href="studentasks.html" class="leftitem">学习任务</a>
						<a href="learningrecords.html" class="leftitem">学习路径</a>
						<a href="achievement.html" class="leftitem">考试成绩</a>
						<a class="leftitem activeleftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="righttopview">
						<label>文章发布</label>
					</div>
					<div id="view1">
						<div class="fbwzbox">
							<label onclick="showview()">发布文章</label>
						</div>
					</div>
					<div id="view2">
						<div class="wbview">
							<input id="wzbt" type="text" placeholder="请输入文章标题" />
							<input id="wzzt" type="text" placeholder="请输入文章主题" />
							<select id="wzfl">
							</select>
							<div id="E">

							</div>
							<div class="fbbtnview">
								<label onclick="submitwz()">发布文章</label>
								<label>取消</label>
							</div>
						</div>
						<div class="yfbwztop">
							已发布文章列表
						</div>
						<div class="bb"></div>
						<div class="wzlistbox" id="listbox">
							
							
						</div>
						<div class="fybox" id="fyq">
							<span id="sy">首页</span>
							<span id="syy">上一页</span>
							<div class="num" id="num">
							</div>
							<span id="xyy">下一页</span>
							<span id="wy">尾页</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let pageindex = 1
			let pagesize = 15
			let pages = 1
			let content = null //富文本内容
			const E = window.wangEditor
			const editor = new E("#E")
			editor.config.menus = [
				"head", "bold", "fontName", "italic", "underline", "strikeThrough", "indent", "lineHeight", "table",
				"image", "video", "splitLine", "undo", "redo"
			] //菜单栏目
			editor.config.uploadImgAccept = ['jpg', 'jpeg', 'png', 'gif'] //上传图片的格式
			editor.config.uploadVideoAccept = ['mp4'] //上传视屏的格式
			editor.config.showLinkImg = false //不上传网络图片
			editor.config.showLinkVideo = false //不上传网络视屏
			editor.config.uploadImgMaxLength = 1 //一次一张图片
			editor.config.zIndex = 1
			editor.config.customUploadImg = (resultFiles, insertImgFn) => {
				let formData = new FormData()
				formData.append('file', resultFiles[0])
				$.ajax({
					url: baseurl + "/attachments/upload",
					type: 'post',
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: formData,
					contentType: false,
					processData: false,
					success: (res) => {
						if (res.code == '200') {
							let imgurl = baseurl + res.data.fastFileId
							insertImgFn(imgurl)
						}
					}
				})
			}
			editor.config.customUploadVideo = (resultFiles, insertVideoFn) => {
				let formData = new FormData()
				formData.append('file', resultFiles[0])
				$.ajax({
					url: baseurl + "/attachments/upload",
					type: 'post',
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: formData,
					contentType: false,
					processData: false,
					success: (res) => {
						if (res.code == '200') {
							let videourl = baseurl + res.data.fastFileId
							insertVideoFn(videourl)
						}
					}
				})
			}
			editor.config.onchange = (html) => {
				content = html
			}
			editor.create()
			let type = 'student' //teacher/student
			var mySwiper = new Swiper('.museumboxitembottoml .swiper', {
				autoplay: false,
				loop: true,
				pagination: {
					el: '.museumboxitembottoml .swiper-pagination',
					clickable: true
				}
			})
			let classid = null
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getclassid() // 这里会在成功后自动调用getwzlist()
				getfooterlink()
				getclasslist()
			})
			function getclasslist(){
				$.ajax({
					url: baseurl + "/web/communityproperty/list/tree",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = '<option value="">未选择分类</option>'
							res.data.map((item)=>{
								html+='<option value="'+item.name+'">'+item.name+'</option>'
							})
							$("#wzfl").html(html)
						}
					}
				})
			}
			function getfooterlink(){
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item)=>{
								html+='<a href="'+item.url+'" target="_blank">'+item.name+'</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
function ininfo(item){
				window.location.href = "communitydetail.html?id="+$(item).attr("data-id")
			}
			function getwzlist() {
				// 添加调试信息
				console.log("开始获取文章列表");
				console.log("pageindex:", pageindex);
				console.log("pagesize:", pagesize);
				console.log("userinfo:", JSON.parse(userinfo));
				console.log("classid:", classid);
				
				// 检查必要参数
				if (!userinfo) {
					console.error("用户信息不存在");
					return;
				}
				
				let userObj = JSON.parse(userinfo);
				let creator = userObj.userAuth ? userObj.userAuth.identifier : userObj.id;
				console.log("creator:", creator);
				
				// 构建参数
				let params = "?pageNum="+pageindex+"&pageSize="+pagesize+"&creator="+creator;
				if (classid && classid !== null && classid !== 'null') {
					params += "&categoryId="+classid;
				}
				
				console.log("请求参数:", params);
				console.log("完整URL:", baseurl + "/posts" + params);
				
				$.ajax({
					url: baseurl + "/posts"+params,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						console.log("API响应:", res);
						if (res.code == '200') {
							console.log("文章数量:", res.data.list.length);
							let html = ""
							if (res.data.list.length === 0) {
								$("#listbox").html("<div style='text-align: center; padding: 40px; color: #666; font-size: 16px;'>暂无已发布的文章</div>");
								$("#fyq").hide();
								return;
							}
							
							res.data.list.map((item)=>{
								html+='<div class="wzitem">'+
								'<div class="itemnr2">'+
									'<div><label><img style="width: 1.041666rem;" src="img/flsq.png" />'
									if(item.communitypropertyType!=null){
										html+=item.communitypropertyType
									}else if(item.cmsCategoryList && item.cmsCategoryList.length > 0){
										html+=item.cmsCategoryList[0].name
									}else{
										html+='未分类'
									}
									html+='</label>'+
										'<label><img style="width: 0.9375rem;" src="img/sjsq.png" />'+setDate(item.createdAt)+'</label>'+
									'</div><div class="itemtitle">'+item.title+'</div></div>'
								if(item.approvalStatus=='1'){
									html+='<label class="ttt state ing">审核中</label>'
									html+='<label class="ttt btn">'+
										'<label class="no">查看文章</label></label></div>'
								}
								else if(item.approvalStatus=='2'){
									html+='<label class="ttt state error">审核未通过<span>（'+item.approvalOption+'）</span></label>'
									html+='<label class="ttt btn">'+
										'<label class="no">查看文章</label></label></div>'
								}
								else if(item.approvalStatus=='3'){
									html+='<label class="ttt state success">审核通过</label>'
									html+='<label class="ttt btn">'+
										'<label class="is" onclick="ininfo(this)" data-id="'+item.id+'">查看文章</label></label></div>'
								}
							})
							$("#listbox").html(html)
							pages = res.data.pages
							if (pages > 1) {
								let numhtml = ""
								for (let a = 1; a <= pages; a++) {
									if (pageindex == a) {
										numhtml += '<label class="actinum" onclick="getnewlist(' + a + ')">' + a +
											'</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
						} else {
							console.error("API返回错误:", res);
							$("#listbox").html("<div style='text-align: center; padding: 40px; color: #666; font-size: 16px;'>获取文章列表失败: " + (res.message || "未知错误") + "</div>");
							$("#fyq").hide();
						}
					},
					error: function(xhr, status, error) {
						console.error("请求失败:", {xhr, status, error});
						$("#listbox").html("<div style='text-align: center; padding: 40px; color: #666; font-size: 16px;'>网络请求失败，请检查网络连接</div>");
						$("#fyq").hide();
					}
				})
			}
			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是第最后一页了！")
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！")
					}
				} else {
					pageindex = index
					getwzlist()
				}
			}
			function submitwz() {
				let titles = $("#wzbt").val()
				if (!titles) {
					cocoMessage.warning(1000, "请输入标题！")
				} else if (!content) {
					cocoMessage.warning(1000, "请输入内容！")
				} else {
					let jsons = {
						title: titles,
						content: content,
						approvalStatus: 1,
						author: JSON.parse(userinfo).name,
						cmsCategoryList: [{id: classid}],
						communitypropertyType: $("#wzfl").val(),
						themename: $("#wzzt").val()
					}
					console.log("发布文章数据:", jsons);
					$.ajax({
						url: baseurl + "/posts/add",
						type: 'POST',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						data: JSON.stringify(jsons),
						dataType: 'json',
						success: (res) => {
							console.log("发布文章响应:", res);
							if (res.code == '200') {
								cocoMessage.success(1000, "发布心声成功！")
								$("#wzbt").val('')
								$("#wzzt").val('')
								$("#wzfl").val('')
								editor.txt.html('')
								content = null
								// 重新获取文章列表
								pageindex = 1 // 重置到第一页
								getwzlist()
							} else if(res.code == '400666'){
								cocoMessage.error(1000, "您输入的内容包含非法字符或者敏感词汇请检查！")
							} else {
								cocoMessage.error(1000, "发布心声失败！请稍后重试！")
							}
						},
						error: function(xhr, status, error) {
							console.error("发布文章失败:", {xhr, status, error});
							cocoMessage.error(1000, "发布心声失败！网络错误！")
						}
					})
				}
			}

			function showview() {
				$("#view1").hide()
				$("#view2").show()
				getwzlist()
			}

			function getclassid() {
				console.log("开始获取分类ID");
				$.ajax({
					url: baseurl + "/category/user",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						console.log("分类ID响应:", res);
						if (res.code == '200' && res.data && res.data.length > 0) {
							classid = res.data[0].id
							console.log("获取到分类ID:", classid);
							// 获取到classid后立即调用文章列表
							getwzlist();
						} else {
							console.log("未获取到分类ID，使用null");
							classid = null;
							// 即使没有classid也尝试获取文章列表
							getwzlist();
						}
					},
					error: function(xhr, status, error) {
						console.error("获取分类ID失败:", {xhr, status, error});
						classid = null;
						// 获取分类ID失败时也尝试获取文章列表
						getwzlist();
					}
				})
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 						.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
			
			function setDate(value){
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '年' + MM + '月' + d + '日 ' + h + ':' + m
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
