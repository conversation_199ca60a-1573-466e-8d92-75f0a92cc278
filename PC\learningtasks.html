<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-学习任务管理</title>
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/wangEditor.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<!-- 导航增强脚本 -->
		<script src="./js/nav-enhancement.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
		<style>
			#baogaotc {
				position: fixed;
				left: 0;
				right: 0;
				bottom: 0;
				top: 0;
				background: rgba(0, 0, 0, 0.7);
				z-index: 999;
				display: none;
				justify-content: center;
				align-items: center;
				margin-top: -6rem;
			}

			.rwselect {
				height: 1.25rem;
				font-size: 0.729166rem;
				color: #999999;
				border: none;
				outline-style: none;
				background: #f3f3f3;
				min-width: 6.25rem;
				margin-left: 0.364583rem;
				margin-right: 0.364583rem;
			}

			.selectrwbox {
				padding-bottom: 1.041666rem;
				display: flex;
				align-items: center;
				border-bottom: 0.052083rem solid #666666;
			}

			.selectrwbox input,
			.selectrwbox select {
				background: #f3f3f3;
				height: 1.25rem;
				color: #999999;
				border: none;
				outline-style: none;
				padding: 0px 0.260417rem;
				margin-right: 0.5rem;
				font-size: 0.72916rem;
			}

			.selectrwbox button {
				border: none;
				background: #c00714;
				color: #FFFFFF;
				height: 1.25rem;
				width: 3rem;
				cursor: pointer;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 0.72916rem;
				margin-right: 0.5rem;
				border-radius: 0.260416rem;
			}

			.loading-btn {
				background-color: #999;
				cursor: not-allowed;
			}

			#loading {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, 0.5);
				display: none;
				z-index: 10000;
				align-items: center;
				justify-content: center;
				flex-direction: column;
			}

			#loading .spinner {
				width: 40px;
				height: 40px;
				margin: 10px auto;
				border: 4px solid #f3f3f3;
				border-top: 4px solid #c00714;
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}

			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}

			#loading-tip {
				color: white;
				font-size: 14px;
				margin-top: 10px;
				text-align: center;
				max-width: 80%;
				padding: 10px;
				background-color: rgba(0, 0, 0, 0.7);
				border-radius: 5px;
			}

			.btn-primary {
				background-color: #c00714;
				color: white;
				border: none;
				padding: 8px 15px;
				border-radius: 3px;
				cursor: pointer;
				transition: background-color 0.3s;
			}

			.btn-primary:hover {
				background-color: #a30611;
			}

			.btn-primary:disabled {
				background-color: #999;
				cursor: not-allowed;
			}

			.btn-secondary {
				background-color: #333;
				color: white;
				border: none;
				padding: 8px 15px;
				border-radius: 3px;
				cursor: pointer;
				margin-left: 10px;
				transition: background-color 0.3s;
			}

			.btn-secondary:hover {
				background-color: #555;
			}

			.error-message {
				color: #c00714;
				font-size: 14px;
				margin-top: 5px;
				display: none;
			}

			.retry-options {
				display: flex;
				margin-top: 10px;
				justify-content: center;
			}
			
			/* 新增：悬浮进度组件样式 */
			#floating-progress {
				position: fixed;
				top: 10rem; /* 调整到topview下方 */
				right: 17rem;
				z-index: 9999;
				width: 64px;
				height: 64px;
				background-color: #c00714;
				border-radius: 8px;
				box-shadow: 0 3px 15px rgba(0, 0, 0, 0.3);
				cursor: pointer;
				display: none;
				flex-direction: column;
				justify-content: center;
				
				align-items: center;
				color: white;
				transition: all 0.3s ease;
				overflow: visible;
			}
			
			/* 下载按钮和报告图标组合样式 */
			#floating-progress::before {
				content: "";
				width: 36px;
				height: 36px;
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 384 512'%3E%3Cpath fill='white' d='M64 0C28.7 0 0 28.7 0 64V448c0 35.3 28.7 64 64 64H320c35.3 0 64-28.7 64-64V160H256c-17.7 0-32-14.3-32-32V0H64zM256 0V128H384L256 0zM216 232V334.1l31-31c9.4-9.4 24.6-9.4 33.9 0s9.4 24.6 0 33.9l-72 72c-9.4 9.4-24.6 9.4-33.9 0l-72-72c-9.4-9.4-9.4-24.6 0-33.9s24.6-9.4 33.9 0l31 31V232c0-13.3 10.7-24 24-24s24 10.7 24 24z'/%3E%3C/svg%3E");
				background-repeat: no-repeat;
				background-size: contain;
				margin-bottom: 4px;
				position: relative;
			}
			
			/* 下载箭头指示 */
			#floating-progress::after {
				content: "下载报告";
				font-size: 12px;
				font-weight: bold;
			}
			
			/* 任务数量标记 */
			#floating-progress span {
				position: absolute;
				top: -8px;
				right: -8px;
				background-color: #333;
				border-radius: 50%;
				width: 24px;
				height: 24px;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 13px;
				font-weight: bold;
				border: 2px solid white;
				box-shadow: 0 2px 5px rgba(0,0,0,0.2);
			}
			
			#floating-progress-panel {
				position: fixed;
				top: 10rem; /* 与图标上沿水平对齐 */
				right: 20px;
				z-index: 9998;
				width: 280px; /* 增加宽度以显示更多内容 */
				max-height: 400px;
				background-color: white;
				border-radius: 8px;
				box-shadow: 0 5px 15px rgba(0, 0, 0, 0.3);
				display: none;
				flex-direction: column;
				overflow: hidden;
				border: 1px solid #eee;
			}
			
			#floating-progress:hover {
				transform: scale(1.1);
			}
			
			#floating-progress.active {
				animation: pulse 2s infinite;
			}
			
			#floating-progress-panel .panel-header {
				background-color: #c00714;
				color: white;
				padding: 10px 15px;
				font-weight: bold;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
			
			#floating-progress-panel .panel-header .panel-actions {
				display: flex;
				align-items: center;
				gap: 10px;
			}
			
			#floating-progress-panel .panel-header #clear-queue-btn {
				background-color: rgba(255, 255, 255, 0.2);
				color: white;
				border: none;
				border-radius: 4px;
				font-size: 12px;
				padding: 4px 8px;
				cursor: pointer;
				transition: all 0.2s ease;
			}
			
			#floating-progress-panel .panel-header #clear-queue-btn:hover {
				background-color: rgba(255, 255, 255, 0.3);
			}
			
			#floating-progress-panel .panel-header .close-btn {
				cursor: pointer;
				font-size: 18px;
			}
			
			#floating-progress-panel .panel-body {
				padding: 10px 12px;
				overflow-y: auto;
				max-height: 340px;
			}
			
			#report-queue {
				list-style-type: none;
				padding: 0;
				margin: 0;
			}
			
			#report-queue li {
				margin-bottom: 12px;
				padding: 8px 10px;
				border-radius: 5px;
				background-color: #f5f5f5;
				border-left: 4px solid #c00714;
			}
			
			#report-queue li.completed {
				border-left-color: #28a745;
			}
			
			#report-queue li.error {
				border-left-color: #dc3545;
			}
			
			#report-queue li .report-info {
				margin-bottom: 5px;
				font-size: 13px;
				display: flex;
				justify-content: space-between;
				flex-wrap: wrap;
			}
			
			#report-queue li .report-info span:last-child {
				background-color: #f0f0f0;
				padding: 1px 6px;
				border-radius: 10px;
				font-size: 11px;
				white-space: nowrap;
				margin-left: 5px;
			}
			
			#report-queue li .report-name {
				font-weight: bold;
				max-width: 255px; /* 进一步增加宽度以适应更宽的面板 */
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				display: block; /* 确保独占一行 */
				margin-bottom: 3px; /* 增加与下方内容的间距 */
				font-size: 13px;
				line-height: 1.4;
			}
			
			/* 当悬停时显示完整内容 */
			#report-queue li .report-name:hover {
				white-space: normal;
				word-break: break-all;
				background-color: rgba(0,0,0,0.05);
				padding: 3px;
				border-radius: 3px;
				position: relative;
				z-index: 10;
			}
			
			#report-queue li .report-time {
				font-size: 11px;
				color: #666;
				display: block;
				margin-bottom: 5px;
			}
			
			#report-queue li .report-progress {
				height: 6px;
				background-color: #e9ecef;
				border-radius: 3px;
				overflow: hidden;
				margin-top: 5px;
			}
			
			#report-queue li .report-progress-bar {
				height: 100%;
				background-color: #c00714;
				border-radius: 3px;
				transition: width 0.3s ease;
			}
			
			#report-queue li .report-actions {
				margin-top: 8px;
				display: flex;
				justify-content: flex-end;
				gap: 5px;
			}
			
			#report-queue li .report-actions button {
				border: none;
				padding: 3px 8px;
				border-radius: 3px;
				font-size: 12px;
				cursor: pointer;
			}
			
			#report-queue li .report-actions .view-btn {
				background-color: #28a745;
				color: white;
				min-width: 40px;
				text-align: center;
			}
			
			#report-queue li .report-actions .retry-btn {
				background-color: #ffc107;
				color: black;
				min-width: 40px;
				text-align: center;
			}
			
			#report-queue li .report-actions .cancel-btn {
				background-color: #dc3545;
				color: white;
				min-width: 40px;
				text-align: center;
			}
			
			@keyframes pulse {
				0% {
					box-shadow: 0 0 0 0 rgba(192, 7, 20, 0.7);
				}
				70% {
					box-shadow: 0 0 0 10px rgba(192, 7, 20, 0);
				}
				100% {
					box-shadow: 0 0 0 0 rgba(192, 7, 20, 0);
				}
			}
			
			.no-scroll {
				overflow: hidden;
			}
			
			/* 本地缓存指示器 */
			.cache-indicator {
				display: inline-block;
				margin-left: 5px;
				font-size: 12px;
				padding: 2px 5px;
				border-radius: 3px;
				background-color: #28a745;
				color: white;
			}
			
			/* 悬浮消息 */
			.floating-message {
				position: fixed;
				bottom: -50px;
				left: 50%;
				transform: translateX(-50%);
				background-color: rgba(0, 0, 0, 0.8);
				color: white;
				padding: 10px 20px;
				border-radius: 5px;
				z-index: 10000;
				transition: all 0.3s ease;
				opacity: 0;
				font-size: 14px;
			}
			.floating-message.show {
				bottom: 30px;
				opacity: 1;
			}
			
			body {
				background: url(img/background.jpg) no-repeat;
				background-size: cover;
				background-attachment: fixed;
			}
			
			/* 现代化导航栏美化 - 与页面红色主题统一，红色偏橙色 */
			.boxleft {
				background: white;
				border-radius: 16px;
				box-shadow: 0 8px 32px rgba(220, 53, 69, 0.08);
				overflow: hidden;
				border: 1px solid rgba(231, 76, 60, 0.05);
				position: relative;
				backdrop-filter: blur(20px);
			}
			
			.boxleft::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8));
				pointer-events: none;
				border-radius: 16px;
			}
			
			/* 导航栏顶部标题区域 - 红色偏橙色渐变 */
			.lefttopview {
				width: 100%;
				height: 65px;
				background: linear-gradient(135deg, #dc3545, #e74c3c, #ff6b35);
				background-size: 300% 300%;
				animation: gradientShift 6s ease infinite;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 18px;
				color: #FFFFFF;
				font-weight: 700;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif;
				position: relative;
				box-shadow: 0 4px 20px rgba(220, 53, 69, 0.25);
				letter-spacing: 1.5px;
				border-radius: 16px 16px 0 0;
			}
			
			/* 渐变动画效果 */
			@keyframes gradientShift {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.lefttopview img {
				width: 24px;
				height: 24px;
				display: block;
				margin-right: 12px;
				filter: brightness(0) invert(1) drop-shadow(0 0 6px rgba(255, 255, 255, 0.4));
			}
			
			.lefttopview label {
				display: flex;
				align-items: center;
				text-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
			}
			
			/* 导航菜单容器 */
			.leftitembox {
				background: transparent;
				padding: 20px 0;
				position: relative;
				z-index: 2;
			}
			
			.leftitembox::before {
				content: '';
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 85%;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.15), transparent);
			}
			
			/* 导航菜单项 - 现代化设计与图标 */
			.leftitem {
				height: auto;
				padding: 16px 24px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				font-size: 15px;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif;
				font-weight: 500;
				color: #4a5568;
				border-bottom: none;
				text-decoration: none;
				transition: color 0.2s ease, background-color 0.2s ease;
				position: relative;
				margin: 4px 16px;
				border-radius: 12px;
				background: transparent;
				overflow: hidden;
				min-height: 48px;
			}
			
			/* 为不同菜单项添加对应的SVG图标 */
			.leftitem::before {
				content: '';
				width: 20px;
				height: 20px;
				margin-right: 12px;
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				opacity: 0.7;
				transition: opacity 0.2s ease;
				flex-shrink: 0;
			}
			
			/* 个人信息图标 */
			.leftitem[href*="userinfo"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
			}
			
			/* 学生成绩统计图标 */
			.leftitem[href*="studentachievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z'/%3E%3C/svg%3E");
			}
			
			/* 学习记录统计图标 */
			.leftitem[href*="studentlearning"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z'/%3E%3C/svg%3E");
			}
			
			/* 考试管理图标 */
			.leftitem[href*="examination"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E");
			}
			
			/* 学习任务管理图标 */
			.leftitem[href*="learning"]::before,
			.leftitem.activeleftitem::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
			}
			
			/* 成果展示图标 */
			.leftitem[href*="achievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E");
			}
			
			/* 发布心声图标 */
			.leftitem[href*="voice"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z'/%3E%3C/svg%3E");
			}
			
			/* 菜单项悬浮态 - 完全无抖动 */
			.leftitem:hover {
				background: linear-gradient(135deg, rgba(220, 53, 69, 0.06), rgba(231, 76, 60, 0.08));
				color: #dc3545;
			}
			
			.leftitem:hover::before {
				opacity: 1;
			}
			
			/* 激活状态的菜单项 - 红色偏橙色渐变与白色图标 */
			.activeleftitem {
				font-weight: 700 !important;
				color: white !important;
				background: linear-gradient(135deg, #dc3545, #e74c3c, #ff6b35) !important;
				background-size: 300% 300%;
				animation: activeGradient 4s ease infinite;
				box-shadow: 0 8px 32px rgba(220, 53, 69, 0.3);
				border-radius: 12px;
			}
			
			@keyframes activeGradient {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.activeleftitem::before {
				opacity: 1 !important;
				filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
			}
			
			.activeleftitem::after {
				content: '';
				position: absolute;
				right: 16px;
				top: 50%;
				transform: translateY(-50%);
				width: 8px;
				height: 8px;
				background: white;
				border-radius: 50%;
				box-shadow: 0 0 12px rgba(255, 255, 255, 0.8);
				animation: activePulse 2s ease infinite;
			}
			
			@keyframes activePulse {
				0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
				50% { opacity: 0.6; transform: translateY(-50%) scale(1.4); }
			}
			
			/* 添加精美的分隔线效果 */
			.leftitem:not(:last-child):not(.activeleftitem) {
				border-bottom: 1px solid rgba(231, 76, 60, 0.06);
				margin-bottom: 3px;
			}
			
			.leftitem:not(:last-child):not(.activeleftitem)::after {
				content: '';
				position: absolute;
				bottom: -1px;
				left: 40px;
				right: 24px;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.15), transparent);
				opacity: 0.5;
			}
			
			/* 整体容器增强效果 */
			.contentview .boxleft {
				background: rgba(255, 255, 255, 0.95);
				border-radius: 16px;
				box-shadow: 0 8px 40px rgba(220, 53, 69, 0.08);
				border: 1px solid rgba(231, 76, 60, 0.05);
				overflow: hidden;
				position: relative;
				backdrop-filter: blur(20px);
			}
			
			.contentview .boxleft::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				height: 3px;
				background: linear-gradient(90deg, #dc3545, #e74c3c, #ff6b35);
				opacity: 0;
				transition: opacity 0.4s ease;
			}
			
			.contentview .boxleft:hover::before {
				opacity: 1;
			}
			
			/* 响应式优化 */
			@media (max-width: 768px) {
				.boxleft {
					border-radius: 12px;
					margin-bottom: 20px;
				}
				
				.lefttopview {
					height: 55px;
					font-size: 16px;
					letter-spacing: 1px;
				}
				
				.lefttopview img {
					width: 20px;
					height: 20px;
					margin-right: 10px;
				}
				
				.leftitem {
					padding: 14px 20px;
					font-size: 14px;
					margin: 3px 12px;
					min-height: 44px;
				}
				
				.leftitem::before {
					width: 18px;
					height: 18px;
					margin-right: 10px;
				}
			}
			
			/* 字体优化 - 思源黑体 */
			.lefttopview,
			.leftitem {
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Source Han Sans', 'Noto Sans CJK SC', 'Microsoft YaHei UI', 'Microsoft YaHei', '微软雅黑', sans-serif;
			}
			
			/* 增强的交互反馈 */
			.leftitem {
				cursor: pointer;
			}
			
			.leftitem:active {
				transform: scale(0.98);
				transition: all 0.1s ease;
			}
			
			.activeleftitem:active {
				transform: scale(1.02);
			}
			
			/* 为菜单项添加微妙的进入动画 */
			.leftitem {
				animation: slideIn 0.5s ease-out forwards;
				opacity: 0;
				transform: translateX(-20px);
			}
			
			.leftitem:nth-child(1) { animation-delay: 0.1s; }
			.leftitem:nth-child(2) { animation-delay: 0.2s; }
			.leftitem:nth-child(3) { animation-delay: 0.3s; }
			.leftitem:nth-child(4) { animation-delay: 0.4s; }
			.leftitem:nth-child(5) { animation-delay: 0.5s; }
			.leftitem:nth-child(6) { animation-delay: 0.6s; }
			.leftitem:nth-child(7) { animation-delay: 0.7s; }
			
			@keyframes slideIn {
				to {
					opacity: 1;
					transform: translateX(0);
				}
			}
		</style>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a href="studentachievement.html" class="leftitem">学生成绩统计</a>
						<a href="studentlearning.html" class="leftitem">学习记录统计</a>
						<a href="userexamination2.html" class="leftitem">考试管理</a>
						<a class="leftitem activeleftitem">学习任务管理</a>
						<a href="achievements.html" class="leftitem">成果展示</a>
						<a href="releaseyourvoice2.html" class="leftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="userinfotopdiv">
						<a class="acccccg">任务列表</a>
						<a href="learningtasks2.html">发布任务</a>
					</div>
					<div class="paperscroll">
						<div class="selectrwbox">
							<input id="ssrwname" placeholder="请输入任务名称" />
							<select id="ssxkid">
								<option value="0">请选择学科</option>
							</select>
							<button onclick="ssrwlist()">查询</button>
							<button onclick="clearss()">清空</button>
						</div>
						<div class="xxrwlist" id="list">

						</div>
						<div class="fybox" id="fyq">
							<span id="sy">首页</span>
							<span id="syy">上一页</span>
							<div class="num" id="num">
							</div>
							<span id="xyy">下一页</span>
							<span id="wy">尾页</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>

		<div class="xxrwtc" style="margin-top: -6rem !important;">
			<div class="tcview" style="height: 700px;">
				<div class="rwtcbox">
					<div class="rwtitletc"><label id="tctitlerw"></label>
						<img onclick="closeyulan()" src="img/rwclose1.png" />
					</div>
					<div class="rwstr">
						<div class="rwstritem">
							<img src="img/rwtime1.png" />
							<label id="yldate"></label>
						</div>
						<div class="rwstritem">
							<img src="img/rwxk1.png" />
							<label id="ylxk"></label>
						</div>
						<!-- <div class="rwstritem">
							<img src="img/rwxy1.png"/>
							学院
						</div>
						<div class="rwstritem">
							<img src="img/rwzy1.png"/>
							专业
						</div>
						<div class="rwstritem">
							<img src="img/rwclass1.png"/>
							班级
						</div> -->
					</div>
					<div class="rwinfobox" id="rwlist">

					</div>
				</div>
			</div>
		</div>

		<div id="deletebox">
			<div class="deletesss">
				<div class="sjtitle2">
					<div>系统提示</div>
					<label class="paperyueclose" onclick="closedelte()"></label>
				</div>
				<div class="delstr">
					是否删除"<label id="delname"></label>"？
				</div>
				<div class="submitbox deldelpaper">
					<div class="bc" onclick="deletesubmit()">删除</div>
					<div class="gb" onclick="closedelte()">关闭</div>
				</div>
			</div>
		</div>

		<div id="baogaotc">
			<div class="deletesss" >
				<div class="sjtitle2">
					<div>任务报告</div>
					<label class="paperyueclose" onclick="closerwbg()"></label>
				</div>
				<div class="delstr">
					<select id="bgxyselect" class="rwselect" onchange="bgxychange()">
					</select>
					<select id="bgzyselect" class="rwselect" onchange="bgzychange()">
						<option value="0">请选择专业</option>
					</select>
					<select id="bgbjselect" class="rwselect">
						<option value="0">请选择班级</option>
					</select>
				</div>
				<div style="font-size: 0.85rem;text-align: center;color: #999999;">获取报告需要一段时间,请您耐心等待~</div>
				<div class="submitbox deldelpaper">
					<div class="bc" onclick="getrwbg()" id="getbgbtn">获取报告</div>
					<div class="gb" onclick="closerwbg()">关闭</div>
				</div>
			</div>
		</div>

		<!-- 加载状态提示 -->
		<div id="loading">
			<div class="spinner"></div>
			<div id="loading-tip">正在处理，请稍候...</div>
		</div>

		<!-- 新增：悬浮进度组件 -->
		<div id="floating-progress" title="查看报告生成进度">
			<span>1</span>
		</div>
		<div id="floating-progress-panel">
			<div class="panel-header">
				<span>任务名称</span>
				<div class="panel-actions">
					<button id="clear-queue-btn" title="清空所有报告">清空队列</button>
					<span class="close-btn" id="close-progress-panel">&times;</span>
				</div>
			</div>
			<div class="panel-body">
				<ul id="report-queue"></ul>
				<div id="no-reports" style="text-align:center;color:#666;padding:20px;">当前没有正在生成的报告</div>
			</div>
		</div>

		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
	
		
		<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
		<script>
		
			let taptaskid = null
			let bgxyid = null
			let bgzyid = null
			let isDownloading = false; // 下载状态标记
			let retryCount = 0; // 重试计数

			let bgxylist = []
			let bgzylist = []
			let bgbjlist = []
			
			// 添加loading控制函数
			function showLoading(message) {
				$("#loading").css("display", "flex");
				$("#loading-tip").html(message || "正在处理，请稍候...");
			}
			
			function hideLoading() {
				$("#loading").css("display", "none");
			}

			function bgxychange() { //学院发生变化
				bgxyid = $("#bgxyselect").val()
				bgxylist.forEach((item) => {
					if (item.id == bgxyid) {
						bgzylist = item.children
						return
					}
				})
				let html = '<option value="0">请选择专业</option>'
				bgzylist.forEach((item) => {
					html += '<option value="' + item.id + '">' + item.name + '</option>'
				})
				$("#bgzyselect").html(html)

				$("#bgbjselect").html('<option value="0">请选择班级</option>')
			}

			function bgzychange() { //专业发生改变
				bgzyid = $("#bgzyselect").val()
				bgzylist.forEach((item) => {
					if (item.id == bgzyid) {
						bgbjlist = item.children
						return
					}
				})
				let html = '<option value="0">请选择班级</option>'
				bgbjlist.forEach((item) => {
					html += '<option value="' + item.id + '">' + item.name + '</option>'
				})
				$("#bgbjselect").html(html)
			}

			function getrwbg() {
				// 检查报告是否正在下载中
				if (isDownloading) {
					alert("已有报告正在生成中，请等待完成...");
					return;
				}

				// 获取正确的选择器和值
				var collegeId = $("#bgxyselect").val();
				var majorId = $("#bgzyselect").val();
				var classId = $("#bgbjselect").val();
				
				// 使用taptaskid作为任务ID（全局变量）
				var taskId = taptaskid;
				
				// 验证必选字段
				if (!collegeId || collegeId == "0") {
					alert("请选择学院");
					return;
				}
				if (!majorId || majorId == "0") {
					alert("请选择专业");
					return;
				}
				if (!classId || classId == "0") {
					alert("请选择班级");
					return;
				}
				
				// 首先检查本地缓存
				const hasCachedPdf = checkReportCache(taskId, classId);
				
				// 获取班级、任务信息
				let taskName = $("#tctitlerw").text() || "任务报告";
				let className = "";
				
				// 尝试从选择框中获取班级名称
				$("#bgbjselect option:selected").each(function() {
					if ($(this).val() == classId) {
						className = $(this).text();
					}
				});
				
				if (className === "") {
					className = "班级" + classId;
				}
				
				// 将报告添加到队列并关闭对话框
				ReportQueueManager.addToQueue({
					taskId: taskId,
					classId: classId,
					collegeId: collegeId, 
					majorId: majorId,
					taskName: taskName,
					className: className,
					status: 'pending',
					progress: 0
				});
				
				// 关闭弹窗
				closerwbg();
				
				// 如果有缓存，提示用户
				if (hasCachedPdf) {
					ReportQueueManager.notifyUser(`${taskName} - ${className} 报告已从本地缓存加载`);
				} else {
					ReportQueueManager.notifyUser(`${taskName} - ${className} 报告已添加到队列，正在等待生成`);
				}
				
				// 自动显示报告队列面板
				setTimeout(() => {
					$("#floating-progress-panel").css("display", "flex");
				}, 500);
			}

			// 修改createPdfWithForm函数，增加更好的用户反馈
			function createPdfWithForm(taskId, classId) {
				$("#loading-tip").html(`
					<div>正在使用表单方式提交请求...</div>
					<div style="font-size:12px;margin-top:5px;color:#eee">
						新窗口将自动打开，可能需要等待30秒-2分钟
					</div>
				`);
				
				const form = document.createElement('form');
				form.method = 'POST';
				form.action = `${baseurl}/teacher/stat/pdf`;
				form.target = '_blank';
				form.style.display = 'none';
				
				// 添加必要的字段
				const fields = {
					'taskId': taskId,
					'classId': classId,
					'auth': sessionStorage.getItem("header"),
					'token': sessionStorage.getItem("header").replace("Bearer ", ""),
					'timestamp': new Date().getTime()
				};
				
				// 添加所有字段到表单
				Object.keys(fields).forEach(key => {
					const input = document.createElement('input');
					input.type = 'hidden';
					input.name = key;
					input.value = fields[key];
					form.appendChild(input);
				});
				
				// 提交表单
				document.body.appendChild(form);
				form.submit();
				
				setTimeout(() => {
					document.body.removeChild(form);
					
					// 更新状态提示
					$("#loading-tip").html(`
						<div>报告请求已提交</div>
						<div style="font-size:12px;margin-top:5px;color:#eee">
							如果新窗口没有自动打开，请检查浏览器是否阻止了弹出窗口
						</div>
						<div style="margin-top:10px;">
							<button class="btn-primary" onclick="checkPdfStatusAndRetry('${taskId}', '${classId}')">
								检查状态
							</button>
							<button class="btn-secondary" onclick="restoreButtonAndHideLoading()">关闭</button>
						</div>
					`);
				}, 1000);
			}

			// 修改checkPdfStatusAndRetry函数，优化用户体验
			function checkPdfStatusAndRetry(taskId, classId) {
				$("#loading-tip").html(`
					<div>正在检查报告状态...</div>
					<div style="font-size:12px;margin-top:5px;color:#eee">
						正在与服务器通信
					</div>
				`);
				
				// 获取完整的API基础URL
				const apiBaseUrl = baseurl || 'http://localhost:5500/api';
				
				// 检查PDF状态
				$.ajax({
					url: `${apiBaseUrl}/teacher/stat/pdf/status`,
					type: 'GET',
					data: {
						taskId: taskId,
						classId: classId,
						timestamp: new Date().getTime()
					},
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					timeout: 30000, // 30秒超时
					success: function(res) {
						if (res && res.code === '200' && res.data) {
							updatePdfStatusUI(res.data, taskId, classId);
						} else {
							// 状态检查失败，提供重试选项
							$("#loading-tip").html(`
								<div>无法获取报告状态</div>
								<div style="margin-top:10px;">
									<button class="btn-primary" onclick="createPdfWithForm('${taskId}', '${classId}')">
										使用表单提交
									</button>
									<button class="btn-secondary" onclick="restoreButtonAndHideLoading()">关闭</button>
								</div>
							`);
						}
					},
					error: function(xhr, status, error) {
						console.error("检查报告状态失败:", error);
						
						// 提供备选方案
						$("#loading-tip").html(`
							<div>检查状态失败，请尝试使用表单方式</div>
							<div style="margin-top:10px;">
								<button class="btn-primary" onclick="createPdfWithForm('${taskId}', '${classId}')">
									使用表单提交
								</button>
								<button class="btn-secondary" onclick="restoreButtonAndHideLoading()">取消</button>
							</div>
						`);
					}
				});
			}
			
			// 处理PDF错误
			function handlePdfError(message, taskId, classId) {
				console.error(message);
				$("#loading-tip").html(`
					<div>${message}</div>
					<div style="margin-top:10px;">
						<button class="btn-primary" onclick="checkPdfStatusAndRetry('${taskId}', '${classId}')">
							检查状态
						</button>
						<button class="btn-secondary" onclick="restoreButtonAndHideLoading()">关闭</button>
					</div>
				`);
			}

			// 添加恢复按钮并隐藏加载状态的辅助函数
			function restoreButtonAndHideLoading() {
				$("#getbgbtn").html('获取报告').prop('disabled', false);
				hideLoading();
			}

			// 恢复获取报告按钮的状态
			function restoreButtonState() {
				$("#getbgbtn").html('获取报告').prop('disabled', false);
			}

			// 更新updatePdfStatusUI函数，使用统一的样式和状态控制
			function updatePdfStatusUI(statusData, taskId, classId) {
				// 恢复按钮状态（为后续操作做准备）
				restoreButtonState();
				
				if (statusData.status === 'completed') {
					// PDF已生成，提供查看链接
					$("#loading-tip").html(`
						<div>报告已生成成功!</div>
						<div style="margin-top:10px;">
							<a href="${statusData.url}" target="_blank" class="btn-primary">
								查看报告
							</a>
							<button class="btn-secondary" onclick="restoreButtonAndHideLoading()">关闭</button>
						</div>
					`);
					
					if (statusData.url) {
						setTimeout(() => {
							window.open(statusData.url, '_blank');
						}, 1000);
					}
				} else if (statusData.status === 'processing') {
					// 继续等待
					const progress = statusData.progress || 0;
					$("#loading-tip").html(`
						<div>报告正在生成中</div>
						<div style="width:200px; height:8px; background:#333; border-radius:4px; margin:10px auto; overflow:hidden;">
							<div style="height:100%; width:${progress}%; background:#c00714;"></div>
						</div>
						<div>${progress}%</div>
						<div style="margin-top:10px;">
							<button class="btn-primary" id="refresh-pdf-btn">
								刷新进度
							</button>
							<button class="btn-secondary" onclick="hideLoading()">稍后查看</button>
						</div>
					`);
					
					// 修改生成报告按钮状态
					$("#getbgbtn").html('报告生成中').prop('disabled', true);
					
					$("#refresh-pdf-btn").on('click', function() {
						$(this).prop('disabled', true).text('检查中...');
						checkPdfStatusAndRetry(taskId, classId);
					});
					
					// 自动刷新
					if (progress < 100) {
						setTimeout(() => {
							if ($("#loading").is(":visible")) {
								checkPdfStatusAndRetry(taskId, classId);
							}
						}, 5000); // 5秒后自动刷新
					}
				} else {
					// 生成失败
					$("#loading-tip").html(`
						<div>报告生成失败</div>
						<div style="color:#ff9999; margin-top:5px; font-size:12px;">
							${statusData.message || '未知错误'}
						</div>
						<div style="margin-top:10px;">
							<button class="btn-primary" id="retry-pdf-btn">
								重试
							</button>
							<button class="btn-secondary" onclick="restoreButtonAndHideLoading()">取消</button>
						</div>
					`);
					
					$("#retry-pdf-btn").on('click', function() {
						$(this).prop('disabled', true).text('正在重试...');
						createPdfWithForm(taskId, classId);
					});
				}
			}

			// 修改closerwbg函数，确保状态正确重置
			function closerwbg() {
				$("#baogaotc").attr("style", "display: none;");
				restoreButtonState();
				hideLoading(); // 使用全局函数隐藏加载提示
				isDownloading = false; // 重置标记，虽然代码其他部分不再使用此标记
				retryCount = 0; // 重置重试计数
			}

			// 修改baogaotc函数，增加错误处理
			function baogaotc(item) {
				taptaskid = $(item).attr("data-id");
				if (!taptaskid) {
					alert("任务ID获取失败，请刷新页面重试");
					return;
				}
				
				$("#baogaotc").attr("style", "display: flex;");
				
				// 获取当前任务的班级权限数据
				getTaskClassData(taptaskid);
			}

			//开始时间和结束时间分开  两个input    修改的时候  组卷界面重写  记录原有数据
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()

				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getfooterlink()
				getrwlist()
				getxuekelist()
				
				// 初始化报告队列管理器
				ReportQueueManager.init();
			})
			let deleteid = null

			function deletess(str) {
				deleteid = $(str).attr("data-id")
				$("#delname").html($(str).attr("data-str"))
				$("#deletebox").attr("style", "display: flex;")
			}

			function deletesubmit() {
				$.ajax({
					url: baseurl + "/learning-tasks/delete/" + deleteid,
					type: 'delete',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							cocoMessage.success(1000, "删除成功！")
							getrwlist()
						} else {
							cocoMessage.error(1000, "删除失败！")
						}
						closedelte()
					}
				})
			}

			function closedelte() {
				$("#deletebox").attr("style", "display: none;")
			}

			// 优化getTaskClassData函数，添加错误处理和状态反馈
			function getTaskClassData(taskId) {
				// 显示加载状态
				$("#getbgbtn").html('加载班级...').prop('disabled', true);
				
				// 先清空下拉框
				$("#bgxyselect").html('<option value="0">请选择学院</option>');
				$("#bgzyselect").html('<option value="0">请选择专业</option>');
				$("#bgbjselect").html('<option value="0">请选择班级</option>');
				
				// 使用weblist接口的数据，因为它可以正常工作
				$.ajax({
					url: baseurl + "/learning-tasks/weblist",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: 1,
						pageSize: 100,
						tasksName: "",
						sectionId: ""
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// 找到匹配的任务ID
							let taskData = null;
							for (let i = 0; i < res.data.list.length; i++) {
								if (res.data.list[i].id === taskId) {
									taskData = res.data.list[i];
									break;
								}
							}
							
							if (taskData && taskData.tasksAuthorityList && taskData.tasksAuthorityList.length > 0) {
								// 构建学院-专业-班级层级结构
								bgxylist = [];
								let collegeMap = new Map();
								
								// 遍历权限列表，构建基础结构
								taskData.tasksAuthorityList.forEach((item) => {
									if (!collegeMap.has(item.collegeId)) {
										collegeMap.set(item.collegeId, {
											id: item.collegeId,
											name: "", // 稍后填充
											children: new Map() // 使用Map存储专业
										});
									}
									
									let college = collegeMap.get(item.collegeId);
									if (!college.children.has(item.majorId)) {
										college.children.set(item.majorId, {
											id: item.majorId,
											name: "", // 稍后填充
											children: [] // 班级列表
										});
									}
									
									// 添加班级ID到专业下
									let major = college.children.get(item.majorId);
									if (!major.children.some(c => c.id === item.classId)) {
										major.children.push({
											id: item.classId,
											name: "" // 稍后填充
										});
									}
								});
								
								// 获取班级名称数据
								$.ajax({
									url: baseurl + "/binding/teacher-class",
									type: 'GET',
									contentType: "application/json",
									headers: {
										"Authorization": sessionStorage.getItem("header")
									},
									dataType: 'json',
									success: (classRes) => {
										if (classRes.code == '200') {
											let allClassData = classRes.data;
											
											// 填充学院、专业、班级名称
											for (let college of allClassData) {
												if (collegeMap.has(college.id)) {
													let taskCollege = collegeMap.get(college.id);
													taskCollege.name = college.name;
													
													// 填充专业名称
													for (let major of college.children) {
														if (taskCollege.children.has(major.id)) {
															let taskMajor = taskCollege.children.get(major.id);
															taskMajor.name = major.name;
															
															// 填充班级名称
															for (let cls of major.children) {
																let taskClassIndex = taskMajor.children.findIndex(c => c.id == cls.id);
																if (taskClassIndex !== -1) {
																	taskMajor.children[taskClassIndex].name = cls.name;
																}
															}
														}
													}
												}
											}
											
											// 转换数据结构为数组形式
											let collegeArray = [];
											collegeMap.forEach((college) => {
												let majorArray = [];
												college.children.forEach((major) => {
													majorArray.push({
														id: major.id,
														name: major.name,
														children: major.children
													});
												});
												collegeArray.push({
													id: college.id,
													name: college.name,
													children: majorArray
												});
											});
											
											bgxylist = collegeArray;
											
											// 更新学院下拉框
											let html = '<option value="0">请选择学院</option>';
											bgxylist.forEach((item) => {
												html += '<option value="' + item.id + '">' + item.name + '</option>';
											});
											$("#bgxyselect").html(html);
											
											// 恢复按钮状态
											$("#getbgbtn").html('获取报告').prop('disabled', false);
											
											// console.log("任务班级数据加载完成:", bgxylist);
										} else {
											console.error("获取班级数据失败:", classRes);
											handleTaskDataError("获取班级数据失败: " + (classRes.message || "未知错误"));
										}
									},
									error: (err) => {
										console.error("获取班级数据请求出错:", err);
										handleTaskDataError("获取班级数据请求失败");
									}
								});
							} else {
								console.warn("当前任务没有班级权限数据或未找到匹配的任务");
								handleTaskDataError("当前任务没有班级权限数据");
							}
						} else {
							console.error("获取任务列表失败:", res);
							handleTaskDataError("获取任务列表失败: " + (res.message || "未知错误"));
						}
					},
					error: (err) => {
						console.error("获取任务列表请求出错:", err);
						handleTaskDataError("获取任务数据失败，请刷新页面重试");
					}
				});
			}

			// 处理任务数据加载错误
			function handleTaskDataError(message) {
				// 恢复按钮状态
				$("#getbgbtn").html('获取报告').prop('disabled', false);
				
				// 显示错误信息
				$("#bgxyselect").html('<option value="0">加载失败</option>');
				alert(message);
			}

			function showyulan(item) {
				$("#yldate").html($(item).attr("data-date"))
				$("#ylxk").html($(item).attr("data-xkname"))
				$("#tctitlerw").html($(item).attr("data-name"))
				$('.xxrwtc').show()
				$.ajax({
					url: baseurl + "/learning-tasks-resource/getByTaskIdMap/" + $(item).attr("data-id"),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ''
							// console.log(res.data)
							res.data.forEach((item) => {
								html +=
									`<div class="rwinfotitle">${item.name}(${item.List.length})</div><div class="rwinfolist">`
								item.List.forEach((item2, index) => {
									html +=
										`<div class="rwinfoitem"><div class="rwitemleft">资源${index+1}</div><div class="rwitemright"><div>资源标题: <label>${item2.resourceName}</label></div></div></div>`
								})
								html += `</div>`
							})

							$("#rwlist").html(html)
						}
					}
				})
			}

			function closeyulan() {
				$('.xxrwtc').hide()
			}
			let pageindex = 1
			let pageSize = 15

			function getnewlist(index) {
				pageindex = index
				getrwlist()
			}

			//<input id="ssrwname" placeholder="请输入任务名称"/>
			//<input id="ssjsname" placeholder="请输入发布教师名称"/>
			//<select id="ssxkid">
			//	<option value="0">请选择学科</option>
			//</select>
			//<button onclick="ssrwlist()">查询</button>
			//<button onclick="clearss()">清空</button>

			function getxuekelist() {
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'get',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						let html = '<option value="0">请选择学科</option>'
						res.data.forEach((item) => {
							html += `<option value="${item.id}">${item.name}</option>`
						})
						$("#ssxkid").html(html)
					}
				})
			}

			let ssrwname = null
			let sxkid = null

			function ssrwlist() { //搜索按钮被点击
				pageindex = 1
				ssrwname = $("#ssrwname").val()
				if ($("#ssxkid").val() == '0') {
					sxkid = null
				} else {
					sxkid = $("#ssxkid").val()
				}
				getrwlist()
			}

			function clearss() {
				ssrwname = null
				sxkid = null
				pageindex = 1
				$("#ssrwname").val("")
				$("#ssxkid").val("0")
				getrwlist()
			}

			function getrwlist() {
				$.ajax({
					url: baseurl + "/learning-tasks/weblist",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: pageindex,
						pageSize: pageSize,
						tasksName: ssrwname,//任务名称
						sectionId: sxkid //学科ID
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data)
							let html = ""
							res.data.list.forEach((item) => {
								html += `<div class="xxrwitem">
								<div class="xxrwiteml">
									<div class="xxrwtitle"><div>${item.tasksName}</div></div>
									<div class="xxrwstr">
										<label>${setDate(item.startTime)}-${setDate(item.endTime)}</label>
										<label>${item.sectionName}</label>
									</div>
								</div>
								<div class="xxrmitemr">
									<div class="xxrwyl" onclick="showyulan(this)" data-id="${item.id}" data-name="${item.tasksName}" data-date="${setDate(item.startTime)+'-'+setDate(item.endTime)}" data-xkname="${item.sectionName}">预览</div>
									<div class="xxrwsc" style="margin-right: 0.625rem;" onclick="deletess(this)" data-str="${item.tasksName}" data-id="${item.id}">删除</div>`
									if(item.isExpired == 0){
										html += `<div class="xxrwsc" style="background: #999999;">报告</div>`
									}else{
										html += `<div class="xxrwsc" onclick="baogaotc(this)" data-id="${item.id}">报告</div>`
									}
									
								html+=`</div>
							</div>`
							})
							$("#list").html(html)

							pages = res.data.pages
							if (pages > 1) {
								let numhtml = ""
								//当页数大于5
								if (pages > 5) {
									if (pageindex <= 5) { //当前页码小于5的时候右侧不变
										for (let a = 1; a <= 10; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
										numhtml += '<label onclick="getnewlist(11)">...</label>'
									} else if (5 < pageindex & pageindex < pages - 5) {
										numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) - 5) +
											')">...</label>'
										for (let a = pageindex - 4; a <= pageindex + 4; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
										numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) + 5) +
											')">...</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + (pages - 11) +
											')">...</label>'
										for (let a = pages - 10; a <= pages; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
									}
								} else {
									for (let a = 1; a <= pages; a++) {
										if (pageindex == a) {
											numhtml += '<label class="actinum" onclick="getnewlist(' + a +
												')">' + a +
												'</label>'
										} else {
											numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
										}
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
						}
					}
				})
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '年' + MM + '月' + d + '日 '
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
			
			// 处理图片base64数据，确保完整性
			function processImageData(base64String) {
				// 检查base64字符串是否完整
				if (!base64String.startsWith('data:image/')) {
					// 如果缺少前缀，添加默认前缀
					return 'data:image/png;base64,' + base64String;
				}
				return base64String;
			}
			
			// 图片预加载函数，确保图片加载完整后再生成报告
			function preloadImages(imageUrls) {
				return Promise.all(imageUrls.map(url => {
					return new Promise((resolve, reject) => {
						const img = new Image();
						img.onload = () => resolve(url);
						img.onerror = () => reject(new Error(`Failed to load image: ${url}`));
						img.src = url;
					});
				}));
			}
		</script>
		<script>
			// 新增：报告队列管理系统
			const ReportQueueManager = {
				queue: [],                   // 报告队列
				currentlyProcessing: false,  // 当前是否有报告正在处理
				maxRetries: 3,               // 最大重试次数
				
				// 初始化
				init: function() {
					// 设置悬浮图标点击事件
					$("#floating-progress").on("click", function() {
						$("#floating-progress-panel").css("display", "flex");
					});
					
					// 设置关闭面板事件
					$("#close-progress-panel").on("click", function() {
						$("#floating-progress-panel").hide();
					});
					
					// 设置清空队列按钮事件
					$("#clear-queue-btn").on("click", this.clearAllReports.bind(this));
					
					// 尝试从localStorage恢复队列
					this.restoreQueueFromStorage();
					
					// 初始化后，检查队列状态并开始处理
					this.updateUI();
					this.processNextInQueue();
					
					// 添加页面关闭前的提示
					window.addEventListener('beforeunload', (e) => {
						if (this.queue.length > 0 && this.currentlyProcessing) {
							const message = '有报告正在生成中，关闭页面可能会导致报告丢失。确定要离开吗？';
							e.returnValue = message;
							return message;
						}
					});
				},
				
				// 清空所有报告
				clearAllReports: function() {
					if(this.queue.length === 0) {
						this.notifyUser("队列已经为空");
						return;
					}
					
					if(this.currentlyProcessing && !confirm("有报告正在生成中，确定要清空所有报告吗？")) {
						return;
					}
					
					// 取消所有正在处理的请求（如果有的话）
					this.currentlyProcessing = false;
					
					// 清空队列
					this.queue = [];
					
					// 保存到存储
					this.saveQueueToStorage();
					
					// 更新UI
					this.updateUI();
					
					// 通知用户
					this.notifyUser("所有报告已从队列中移除");
					
					return true;
				},
				
				// 添加报告到队列
				addToQueue: function(report) {
					// 生成唯一ID
					const reportId = 'report_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
					
					// 默认报告对象
					const defaultReport = {
						id: reportId,
						taskId: null,
						classId: null,
						collegeId: null,
						majorId: null,
						taskName: '未命名报告',
						className: '未知班级',
						status: 'pending',  // pending, processing, completed, error
						progress: 0,
						createdAt: new Date(),
						updatedAt: new Date(),
						retryCount: 0,
						error: null,
						pdfBlob: null,
						pdfUrl: null
					};
					
					// 合并默认值和传入的报告对象
					const newReport = { ...defaultReport, ...report };
					
					// 检查是否已经存在相同任务和班级的报告
					const existingReportIndex = this.queue.findIndex(item => 
						item.taskId === newReport.taskId && 
						item.classId === newReport.classId
					);
					
					if (existingReportIndex !== -1) {
						// 如果存在相同报告，则更新现有报告
						// 保留状态为completed的报告，避免重复生成
						if (this.queue[existingReportIndex].status === 'completed') {
							// 如果已经完成，直接返回现有ID
							this.notifyUser(`${newReport.taskName} - ${newReport.className} 报告已存在`);
							return this.queue[existingReportIndex].id;
						}
						
						// 替换现有报告
						this.queue[existingReportIndex] = {
							...newReport,
							id: this.queue[existingReportIndex].id // 保留原ID
						};
					} else {
						// 添加到队列最前面（按从新到旧排序）
						this.queue.unshift(newReport);
					}
					
					// 保存到存储
					this.saveQueueToStorage();
					
					// 更新UI
					this.updateUI();
					
					// 如果当前没有处理中的报告，开始处理
					if (!this.currentlyProcessing) {
						this.processNextInQueue();
					}
					
					return reportId;
				},
				
				// 处理队列中的下一个报告
				processNextInQueue: function() {
					if (this.queue.length === 0 || this.currentlyProcessing) {
						return false;
					}
					
					// 找到第一个待处理的报告
					const pendingReportIndex = this.queue.findIndex(report => report.status === 'pending');
					
					if (pendingReportIndex === -1) {
						this.currentlyProcessing = false;
						this.updateUI();
						return false;
					}
					
					const report = this.queue[pendingReportIndex];
					this.currentlyProcessing = true;
					
					// 更新报告状态为处理中
					this.updateReportStatus(report.id, {
						status: 'processing',
						updatedAt: new Date()
					});
					
					// 检查本地缓存
					const cacheKey = `pdf_${report.taskId}_${report.classId}`;
					const cachedPdf = this.getPdfFromCache(cacheKey);
					
					if (cachedPdf) {
						console.log('使用本地缓存的PDF');
						
						// 恢复缓存的PDF
						this.updateReportStatus(report.id, {
							status: 'completed',
							progress: 100,
							pdfBlob: cachedPdf,
							pdfUrl: URL.createObjectURL(cachedPdf),
							updatedAt: new Date()
						});
						
						// 处理下一个
						this.currentlyProcessing = false;
						setTimeout(() => this.processNextInQueue(), 500);
						
						return true;
					}
					
					// 从服务器获取PDF
					this.fetchPdfFromServer(report);
					return true;
				},
				
				// 从服务器获取PDF
				fetchPdfFromServer: function(report) {
					const apiBaseUrl = baseurl || 'http://localhost:5500/api';
					const self = this;
					let progressInterval;
					
					// 模拟进度更新
					let fakeProgress = 5;
					progressInterval = setInterval(() => {
						if (fakeProgress < 90) {
							fakeProgress += Math.floor(Math.random() * 8) + 1;
							self.updateReportStatus(report.id, {
								progress: fakeProgress,
								updatedAt: new Date()
							});
						}
					}, 3000);
					
					$.ajax({
						url: `${apiBaseUrl}/teacher/stat/pdf`,
						type: 'POST',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						data: JSON.stringify({
							taskId: report.taskId,
							classId: report.classId,
							timestamp: new Date().getTime()
						}),
						xhrFields: {
							responseType: 'blob'
						},
						timeout: 300000, // 5分钟超时
						success: function(data, status, xhr) {
							clearInterval(progressInterval);
							
							const contentType = xhr.getResponseHeader('Content-Type');
							if (contentType && contentType.indexOf('application/pdf') !== -1) {
								// 创建PDF Blob
								const blob = new Blob([data], { type: 'application/pdf' });
								const url = URL.createObjectURL(blob);
								
								// 更新报告状态
								self.updateReportStatus(report.id, {
									status: 'completed',
									progress: 100,
									pdfBlob: blob,
									pdfUrl: url,
									updatedAt: new Date()
								});
								
								// 缓存PDF
								self.savePdfToCache(`pdf_${report.taskId}_${report.classId}`, blob);
								
								// 通知用户
								self.notifyUser(`${report.taskName} 报告已生成完成`);
							} else {
								try {
									const reader = new FileReader();
									reader.onload = function() {
										try {
											const errorData = JSON.parse(reader.result);
											self.handleReportError(report.id, errorData.message || '未知错误');
										} catch (e) {
											self.handleReportError(report.id, '返回数据解析失败');
										}
									};
									reader.readAsText(data);
								} catch (e) {
									self.handleReportError(report.id, '处理返回数据失败');
								}
							}
							
							// 处理下一个
							self.currentlyProcessing = false;
							setTimeout(() => self.processNextInQueue(), 500);
						},
						error: function(xhr, status, error) {
							clearInterval(progressInterval);
							
							let errorMessage = '获取报告失败';
							
							if (status === "timeout") {
								errorMessage = '报告生成超时，请重试';
							} else if (xhr.status === 401) {
								errorMessage = '登录已过期，请重新登录';
							} else if (xhr.status === 0) {
								errorMessage = '网络连接中断';
							} else {
								errorMessage = `服务器错误 (${xhr.status})`;
							}
							
							self.handleReportError(report.id, errorMessage);
							
							// 处理下一个
							self.currentlyProcessing = false;
							setTimeout(() => self.processNextInQueue(), 500);
						}
					});
				},
				
				// 更新报告状态
				updateReportStatus: function(reportId, updates) {
					const reportIndex = this.queue.findIndex(report => report.id === reportId);
					if (reportIndex === -1) return false;
					
					// 更新报告
					this.queue[reportIndex] = {
						...this.queue[reportIndex],
						...updates
					};
					
					// 保存到存储
					this.saveQueueToStorage();
					
					// 更新UI
					this.updateUI();
					
					return true;
				},
				
				// 处理报告错误
				handleReportError: function(reportId, errorMessage) {
					const reportIndex = this.queue.findIndex(report => report.id === reportId);
					if (reportIndex === -1) return false;
					
					const report = this.queue[reportIndex];
					
					// 增加重试计数
					const retryCount = (report.retryCount || 0) + 1;
					
					if (retryCount <= this.maxRetries) {
						// 更新状态为待处理，准备重试
						this.updateReportStatus(reportId, {
							status: 'pending',
							progress: 0,
							error: `${errorMessage} (尝试 ${retryCount}/${this.maxRetries})`,
							retryCount: retryCount,
							updatedAt: new Date()
						});
						
						// 通知用户
						this.notifyUser(`${report.taskName} 报告生成失败，将自动重试 (${retryCount}/${this.maxRetries})`);
					} else {
						// 超过重试次数，标记为错误
						this.updateReportStatus(reportId, {
							status: 'error',
							error: `${errorMessage} (已达到最大重试次数)`,
							updatedAt: new Date()
						});
						
						// 通知用户
						this.notifyUser(`${report.taskName} 报告生成失败，请手动重试`);
					}
					
					return true;
				},
				
				// 重试生成报告
				retryReport: function(reportId) {
					const reportIndex = this.queue.findIndex(report => report.id === reportId);
					if (reportIndex === -1) return false;
					
					// 重置报告状态
					this.updateReportStatus(reportId, {
						status: 'pending',
						progress: 0,
						error: null,
						retryCount: 0,
						updatedAt: new Date()
					});
					
					// 如果当前没有处理中的报告，开始处理
					if (!this.currentlyProcessing) {
						this.processNextInQueue();
					}
					
					return true;
				},
				
				// 取消报告生成
				cancelReport: function(reportId) {
					const reportIndex = this.queue.findIndex(report => report.id === reportId);
					if (reportIndex === -1) return false;
					
					// 从队列中移除
					this.queue.splice(reportIndex, 1);
					
					// 保存到存储
					this.saveQueueToStorage();
					
					// 更新UI
					this.updateUI();
					
					return true;
				},
				
				// 查看报告
				viewReport: function(reportId) {
					const reportIndex = this.queue.findIndex(report => report.id === reportId);
					if (reportIndex === -1) return false;
					
					const report = this.queue[reportIndex];
					
					if (report.status === 'completed' && report.pdfUrl) {
						window.open(report.pdfUrl, '_blank');
						return true;
					}
					
					return false;
				},
				
				// 清理已完成的报告
				clearCompletedReports: function() {
					this.queue = this.queue.filter(report => report.status !== 'completed');
					
					// 保存到存储
					this.saveQueueToStorage();
					
					// 更新UI
					this.updateUI();
					
					return true;
				},
				
				// 更新UI
				updateUI: function() {
					// 更新悬浮图标
					if (this.queue.length === 0) {
						$("#floating-progress").hide();
						$("#no-reports").show();
					} else {
						const pendingCount = this.queue.filter(report => report.status === 'pending' || report.status === 'processing').length;
						
						if (pendingCount > 0) {
							$("#floating-progress").text(pendingCount).addClass('active').show();
						} else {
							$("#floating-progress").text(this.queue.length).removeClass('active').show();
						}
						
						$("#no-reports").hide();
					}
					
					// 更新队列面板 - 按创建时间从新到旧排序
					const $queueList = $("#report-queue");
					$queueList.empty();
					
					// 创建一个新的排序数组，确保不影响原始队列顺序（处理队列使用原顺序）
					const sortedReports = [...this.queue].sort((a, b) => {
						return new Date(b.updatedAt) - new Date(a.updatedAt);
					});
					
					sortedReports.forEach(report => {
						const reportHtml = this.generateReportHTML(report);
						$queueList.append(reportHtml);
					});
					
					// 绑定报告操作事件
					this.bindReportEvents();
				},
				
				// 生成报告HTML
				generateReportHTML: function(report) {
					let statusText = '';
					let statusClass = '';
					let actionsHtml = '';
					
					switch (report.status) {
						case 'pending':
							statusText = '等待中';
							statusClass = '';
							actionsHtml = `
								<button class="cancel-btn" data-report-id="${report.id}">取消</button>
							`;
							break;
						case 'processing':
							statusText = '处理中';
							statusClass = '';
							actionsHtml = `
								<button class="cancel-btn" data-report-id="${report.id}">取消</button>
							`;
							break;
						case 'completed':
							statusText = '已完成';
							statusClass = 'completed';
							actionsHtml = `
								<button class="view-btn" data-report-id="${report.id}">查看</button>
							`;
							break;
						case 'error':
							statusText = '失败';
							statusClass = 'error';
							actionsHtml = `
								<button class="retry-btn" data-report-id="${report.id}">重试</button>
								<button class="cancel-btn" data-report-id="${report.id}">移除</button>
							`;
							break;
					}
					
					const dateStr = new Date(report.updatedAt).toLocaleString();
					let progressHtml = '';
					
					if (report.status === 'processing' || report.status === 'pending') {
						progressHtml = `
							<div class="report-progress">
								<div class="report-progress-bar" style="width: ${report.progress}%"></div>
							</div>
						`;
					}
					
					let errorHtml = '';
					if (report.error) {
						errorHtml = `<div style="color:#dc3545;font-size:12px;margin-top:3px;">${report.error}</div>`;
					}
					
					return `
						<li class="${statusClass}" data-report-id="${report.id}">
							<div class="report-info">
								<span class="report-name" title="${report.taskName} - ${report.className}">${report.taskName} - ${report.className}</span>
								<span>${statusText}</span>
							</div>
							<div class="report-time" style="font-size:11px;color:#666;">${dateStr}</div>
							${progressHtml}
							${errorHtml}
							<div class="report-actions">
								${actionsHtml}
							</div>
						</li>
					`;
				},
				
				// 绑定报告事件
				bindReportEvents: function() {
					const self = this;
					
					// 查看按钮
					$(".view-btn").on("click", function() {
						const reportId = $(this).data("report-id");
						self.viewReport(reportId);
					});
					
					// 重试按钮
					$(".retry-btn").on("click", function() {
						const reportId = $(this).data("report-id");
						self.retryReport(reportId);
					});
					
					// 取消按钮
					$(".cancel-btn").on("click", function() {
						const reportId = $(this).data("report-id");
						if (confirm('确定要取消这个报告吗？')) {
							self.cancelReport(reportId);
						}
					});
				},
				
				// 通知用户
				notifyUser: function(message) {
					if ('Notification' in window && Notification.permission === 'granted') {
						new Notification('报告生成状态', {
							body: message,
							icon: './img/logo.png'
						});
					} else if ('Notification' in window && Notification.permission !== 'denied') {
						Notification.requestPermission().then(function(permission) {
							if (permission === 'granted') {
								new Notification('报告生成状态', {
									body: message,
									icon: './img/logo.png'
								});
							}
						});
					}
					
					// 添加一个临时消息提示
					const $message = $(`<div class="floating-message">${message}</div>`);
					$("body").append($message);
					
					setTimeout(() => {
						$message.addClass('show');
						
						setTimeout(() => {
							$message.removeClass('show');
							setTimeout(() => $message.remove(), 500);
						}, 3000);
					}, 100);
				},
				
				// 保存队列到localStorage
				saveQueueToStorage: function() {
					try {
						// 创建一个可以序列化的队列副本
						const serializableQueue = this.queue.map(report => {
							const { pdfBlob, ...serializableReport } = report;
							return serializableReport;
						});
						
						localStorage.setItem('pdfReportQueue', JSON.stringify(serializableQueue));
					} catch (e) {
						console.error('保存队列到localStorage失败:', e);
					}
				},
				
				// 从localStorage恢复队列
				restoreQueueFromStorage: function() {
					try {
						const savedQueue = localStorage.getItem('pdfReportQueue');
						if (savedQueue) {
							this.queue = JSON.parse(savedQueue);
							
							// 恢复日期对象
							this.queue.forEach(report => {
								report.createdAt = new Date(report.createdAt);
								report.updatedAt = new Date(report.updatedAt);
								
								// 恢复本地缓存的PDF (如果可能)
								if (report.status === 'completed') {
									const cacheKey = `pdf_${report.taskId}_${report.classId}`;
									const cachedPdf = this.getPdfFromCache(cacheKey);
									if (cachedPdf) {
										report.pdfBlob = cachedPdf;
										report.pdfUrl = URL.createObjectURL(cachedPdf);
									}
								}
							});
							
							// 重置处理状态
							this.queue.forEach(report => {
								if (report.status === 'processing') {
									report.status = 'pending';
								}
							});
						}
					} catch (e) {
						console.error('从localStorage恢复队列失败:', e);
						this.queue = [];
					}
				},
				
				// 保存PDF到本地缓存
				savePdfToCache: function(cacheKey, blob) {
					try {
						// 转换Blob为base64字符串
						const reader = new FileReader();
						reader.onload = function() {
							const base64data = reader.result;
							try {
								localStorage.setItem(cacheKey, base64data);
								console.log('PDF已缓存到localStorage:', cacheKey);
							} catch (e) {
								// 可能是因为大小超过限制
								console.error('无法缓存PDF到localStorage:', e);
								// 尝试使用indexedDB
							}
						};
						reader.readAsDataURL(blob);
					} catch (e) {
						console.error('保存PDF缓存失败:', e);
					}
				},
				
				// 从缓存获取PDF
				getPdfFromCache: function(cacheKey) {
					try {
						const base64data = localStorage.getItem(cacheKey);
						if (base64data) {
							// 判断是否是base64字符串
							if (base64data.indexOf('data:application/pdf;base64,') === 0) {
								// 转换base64字符串为Blob
								const binary = atob(base64data.split(',')[1]);
								const array = [];
								for (let i = 0; i < binary.length; i++) {
									array.push(binary.charCodeAt(i));
								}
								return new Blob([new Uint8Array(array)], {type: 'application/pdf'});
							}
						}
						return null;
					} catch (e) {
						console.error('获取PDF缓存失败:', e);
						return null;
					}
				},
				
				// 从缓存删除PDF
				removePdfFromCache: function(cacheKey) {
					try {
						localStorage.removeItem(cacheKey);
					} catch (e) {
						console.error('删除PDF缓存失败:', e);
					}
				},
				
				// 清除所有缓存
				clearAllCache: function() {
					try {
						// 查找所有PDF缓存
						const pdfKeys = [];
						for (let i = 0; i < localStorage.length; i++) {
							const key = localStorage.key(i);
							if (key.startsWith('pdf_')) {
								pdfKeys.push(key);
							}
						}
						
						// 删除所有PDF缓存
						pdfKeys.forEach(key => localStorage.removeItem(key));
						
						console.log('已清除所有PDF缓存');
					} catch (e) {
						console.error('清除缓存失败:', e);
					}
				}
			};
			
			// 检查本地是否有报告缓存
			function checkReportCache(taskId, classId) {
				const cacheKey = `pdf_${taskId}_${classId}`;
				return ReportQueueManager.getPdfFromCache(cacheKey) !== null;
			}
			
			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()

				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getfooterlink()
				getrwlist()
				getxuekelist()
				
				// 初始化报告队列管理器
				ReportQueueManager.init();
			})
		</script>
	</body>
</html>