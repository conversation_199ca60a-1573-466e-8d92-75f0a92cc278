/*index.css*/
.index {
	background: #fbfbfb;
}

.index #indexswiper {
	width: 100%;
	overflow: hidden;
	position: relative;
}

.index #indexswiper img {
	width: 100%;
	display: block;
	height: auto;
}

.index .swipertxt {
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	background: rgba(111, 7, 10, 0.9);
	height: 3.125rem;
	line-height: 3.125rem;
	display: flex;
	justify-content: center;
	font-size: 1.041666rem;
	color: #FFFFFF;
	opacity: 0;
}

.index .swipertxt div {
	width: 46.666666rem;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	padding-right: 20rem;
}

.index #indexswiperpagination {
	height: 3.125rem;
	bottom: 0 !important;
	display: flex;
	align-items: center;
	justify-content: flex-end;
	width: 66.666666rem;
	left: 0;
	right: 0;
	margin: auto;
}

.index #indexswiperpagination .swiper-pagination-bullet {
	width: 0.625rem;
	height: 0.625rem;
	margin: 0px 0.729166rem;
	background: #e56c74;
}

.index #indexswiperpagination .swiper-pagination-bullet-active {
	background: #FFFFFF;
}

.index .contextview {
	width: 66.666666rem;
	margin: 0px auto;
}

.index .flexview {
	display: flex;
	justify-content: space-between;
	margin-top: 3.125rem;
}

.index .flexview .item {
	width: 49%;
}

.index .itemtop {
	border-bottom: 1px solid #dedede;
}

.index .item .title {
	font-size: 1.145833rem;
	line-height: 1.822916rem;
	height: 1.822916rem;
	display: inline-block;
	cursor: pointer;
	color: #000000;
	margin-right: 1.041666rem;
}

.index .item .titleactive {
	color: #c00714;
	font-weight: bold;
	position: relative;
}

.index .item .titleactive::after {
	content: "";
	height: 2px;
	background: #c00714;
	left: 0;
	right: 0;
	bottom: -1px;
	position: absolute;
}

.index .itemtopboxitem {
	display: flex;
	align-items: center;
	height: 3.125rem;
	border-bottom: 1px dashed #f2f2f2;
	cursor: pointer;
}

.index .itemtopboxitem:last-child {
	border: none;
}

.index .itemtopboxitem .txt {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	font-size: 0.9375rem;
	color: #333333;
	width: calc(100% - 2.34375rem - 6.770833rem);
}

.index .itemtopboxitem .new {
	color: #e6a16f;
	font-weight: bold;
	font-size: 0.625rem;
	width: 2.34375rem;
}

.index .itemtopboxitem .time {
	color: #999999;
	font-size: 0.9375rem;
	width: 6.770833rem;
	text-align: right;
}

.index .item .border {
	height: 2px;
	background-image: linear-gradient(to right, #c00714, #c00714, #ffc156);
}

.index .flex {
	display: flex;
	flex-wrap: nowrap;
	justify-content: space-between;
}

.index .itemleft,
.index .itemright {
	width: 49%;
}

.index .itemright img {
	display: block;
	width: 100%;
	cursor: pointer;
}

.index .itemleft {
	position: relative;
	background: url(../img/newsbag.png) no-repeat;
	background-position: bottom;
	background-size: 100% auto;
}

.index .itemlefttitle {
	display: flex;
	flex-wrap: nowrap;
	justify-content: space-between;
	line-height: 1.822916rem;
	align-items: center;
}

.index .itemlefttitletxt {
	color: #c00714;
	font-size: 1.145833rem;
	font-weight: bold;
}

.index .paddleft {
	padding-left: 0.78125rem;
	padding-right: 0.78125rem;
	margin-top: 0.78125rem;
}

.index .itemgengduo {
	font-size: 0.729166rem;
	color: #c1c1c1;
	cursor: pointer;
}

.index .xwitem {
	display: flex;
	flex-wrap: nowrap;
	justify-content: space-between;
	border-bottom: 1px dashed #f2f2f2;
	padding-top: 0.260416rem;
	padding-bottom: 0.260416rem;
	cursor: pointer;
}

.index .xwitem:last-child {
	border-bottom: none;
}

.index .date {
	width: 3.125rem;
	display: flex;
	flex-wrap: wrap;
}

.index .date label {
	width: 100%;
	color: #f8b967;
	font-size: 14px;
	line-height: 1.40625rem;
}

.index .datatxt {
	width: calc(100% - 3.125rem);
	display: -webkit-box;
	text-overflow: ellipsis;
	overflow: hidden;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	font-size: 0.833333rem;
	line-height: 1.40625rem;
}

#news {
	overflow: hidden;
}

#newspagination {
	background: #7b1b1e;
	width: auto;
	border-radius: 100px;
	height: 15px;
	display: flex;
	align-items: center;
	padding-left: 0.260416rem;
	padding-right: 0.260416rem;
}

#newspagination .swiper-pagination-bullet-active {
	background: #FFFFFF !important;
}

#newspagination .swiper-pagination-bullet {
	background: #FFFFFF;
}

.index .newbottom {
	width: 100%;
	position: absolute;
	bottom: 0.260416rem;
	left: 0;
	right: 0;
	height: 0.78125rem;
	display: flex;
	justify-content: center;
}

.index .newbottom span {
	margin-left: 0.208333rem;
	margin-right: 0.208333rem;
}

.index .hsyx .itemtop {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.index .more {
	font-size: 0.729166rem;
	color: #c1c1c1;
	cursor: pointer;
}

.index .hsyxbox {
	padding-top: 2rem;
}

.index .hsyxitembox {
	display: flex;
	flex-wrap: wrap;
	justify-content: space-between;
}

.index .hsyxitembox .item {
	width: 11.979166rem;
	margin-top: 1.5625rem;
	cursor: pointer;
}

.index .hsyxitembox .item img {
	width: 100%;
	border-radius: 5px;
	display: block;
}

.index .hsyxitembox .item .itemname {
	font-size: 0.9rem;
	color: #333333;
	line-height: 2.083333rem;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.index .hsyxitembox .item .itemcode {
	font-size: 0.729166rem;
	color: #666666;
	line-height: 1.041666rem;
}

.index .xssqleft {
	width: 24%;
}

.index .xssqlefttop {
	background: url(../img/sfsykj.png) no-repeat;
	background-size: 100%;
	height: 10.833333rem;
}

.index .xssqright {
	width: 74%;
}

.xssq {
	display: flex;
	justify-content: space-between;
	padding-bottom: 4.166666rem;
}

.xssq .border {
	height: 2px;
	background-image: linear-gradient(to right, #c00714, #c00714, #ffc156);
}

.xssqtitle {
	color: #c00714;
	font-size: 1.145833rem;
	font-weight: bold;
	padding-top: 2.864583rem;
	padding-bottom: 1.40625rem;
	display: flex;
	align-items: center;
	justify-content: center;
}
.xssqtitle img{
	width: 1.875rem;
	display: block;
	padding-right: 0.260416rem;
}

.xssqbox {
	display: flex;
	justify-content: space-evenly;
}

.xssqbox .item {
	background: #FFFFFF;
	text-align: center;
	padding: 0.78125rem;
	border-radius: 5px;
	border-bottom: 3px solid #d29545;
	cursor: pointer;
}

.xssqbox .item img {
	width: 2.083333rem;
}

.xssqbox .item .itemname {
	font-size: 0.833333rem;
	color: #d29545;
	margin-top: 0.260416rem;
}

.hszt {
	width: 100%;
	display: flex;
	justify-content: center;
	margin-top: 1.302083rem;
	padding-bottom: 1.302083rem;
}

.hszt .ff {
	width: 11.88rem;
	text-align: center;
	background: #c00714;
	border-radius: 5px;
	border-bottom: 3px solid #921317;
	cursor: pointer;
}

.hszt .ff img {
	width: 2.083333rem;
	padding-top: 0.78125rem;
}

.hszt .hszttitle {
	font-size: 0.833333rem;
	color: #ffe080;
	padding-bottom: 0.78125rem;
}

.xssqleftbottom {
	margin-top: 1.5625rem;
	cursor: pointer;
}

.xfkj {
	height: 6.666666rem;
	background: url(../img/ysbwg.png) no-repeat;
	background-size: contain;
	background-position: right bottom;
	display: flex;
	justify-content: space-around;
	align-items: center;
}
.xfkj label{
	display: flex;
	align-items: center;
}
.xfkj label img{
	width: 1.5625rem !important;
	height: auto !important;
	display: block;
	padding-right: 0.520833rem;
}
.xfkj img {
	width: 2.083333rem;
	height: 2.083333rem;
}

.xfkj label {
	color: #c00714;
	font-size: 1.145833rem;
	font-weight: bold;
}

.xssq .itemtop {
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.xssqrightcenter {
	display: flex;
	justify-content: space-between;
}

.xssqrightleft,
.xssqrightright {
	width: 49%;
}

.activetitle {
	font-size: 0.9375rem;
	color: #c00714;
	position: relative;
}

.titles {
	border-bottom: 1px solid #dedede;
	display: flex;
	justify-content: space-between;
	align-items: center;
}

.activetitle::after {
	position: absolute;
	content: "";
	height: 2px;
	left: 0;
	right: 0;
	bottom: -2px;
	background: #c00714;
}

.xssqright .box {
	background: #FFFFFF;
	padding: 10px;
	margin-top: 1.041666rem;
}

.riitem {
	display: flex;
	justify-content: space-between;
}

.riitem div {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.huo {
	width: 4.166666rem;
	display: flex;
	align-items: center;
	font-size: 0.729166rem;
	color: #f8b967;
	padding-right: 0.520833rem;
}

.huo img {
	margin-right: 5px;
	display: block;
	width: 0.833333rem;
}

.riitem {
	border-bottom: 1px dashed #dedede;
	height: 2.7599rem;
	cursor: pointer;
	align-items: center;
}

.riitem:last-child {
	border-bottom: none;
}

.riitem .txt {
	width: 100%;
	font-size: 0.9375rem;
	color: #333333;
}

.riitem .date {
	width: 10.416666rem;
	margin-left: 0.520833rem;
	font-size: 0.9375rem;
	color: #999999;
}

.jtdiv {
	display: flex;
	align-items: center;
}

.jtdiv span {
	margin-left: 0.260416rem;
	display: block;
	width: 1.145833rem;
	height: 1.145833rem;
	background: url(../img/jtb.png) no-repeat;
	cursor: pointer;
}

.jtdiv span:hover {
	background: url(../img/jta.png) no-repeat;
}

.jtdiv .leftimg {
	transform: rotateZ(180deg);
}

.jtdiv .rightimg:hover {
	transform: rotateZ(180deg);
}

.jtdiv .leftimg:hover {
	transform: rotateZ(0);
}

.ysbwg .itemtop {
	display: flex;
	justify-content: space-between;
}

.ysbwgview {
	padding-top: 2.083333rem;
	padding-bottom: 2.083333rem;
}

.ysbwg .item {
	width: 32%;
	cursor: pointer;
}

.ysbwgbox {
	display: flex;
	justify-content: space-between;
	margin-top: 1.5625rem;
}

.ysbwg .item img {
	width: 100%;
}

.ysbwgbox .itemname {
	font-size: 1.041666rem;
	color: #333333;
	line-height: 2.083333rem;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.ysbwgbox .itemcode {
	font-size: 0.729166rem;
	color: #666666;
	line-height: 1.041666rem;
}

.itemrightb {
	background: #c00714;
	color: #f8b967;
	font-size: 0.833333rem;
	height: 2.083333rem;
	line-height: 2.083333rem;
	text-align: center;
	position: absolute;
	bottom: 0;
	width: 100%;
	z-index: 3;
	cursor: pointer;
}

.itemright {
	position: relative;
}

.zxxx {
	display: flex;
	padding-top: 1.5625rem;
}

.zxxxleft {
	width: 2.760416rem;
	position: relative;
	background: #c00714;
	display: flex;
	align-items: center;
}

.hssjico {
	width: 1.145833rem;
	display: block;
	margin: 0px auto;
	padding-bottom: 0.520833rem;
}

.zxxxleft div {
	width: 100%;
	text-align: center;
	font-size: 1.145833rem;
	color: #ffffff;
	font-weight: bold;
}

.zxxxright {
	width: calc(100% - 2.760416rem);
	display: flex;
	box-sizing: border-box;
	padding: 2.083333rem;
	background: #fffaea;
	flex-wrap: wrap;
}

.topimghssj {
	position: absolute;
	right: 0;
	top: 0;
	width: 100%;
	display: block;
}

.bottomimghssj {
	position: absolute;
	left: 0;
	bottom: 0;
	width: 100%;
	display: block;
}

.zxxxrighttop {
	display: flex;
	width: 100%;
	border-bottom: 0.052083rem solid #fce9ab;
}

.hssjmore {
	width: 3.020833rem;
}

.hssjboxs {
	width: calc(100% - 3.020833rem);
}

.hssjboxs {
	display: flex;
	justify-content: space-evenly;
}

.hssjitem {
	width: 9.375rem;
	cursor: pointer;
}

.hssjitem img {
	width: 100%;
	display: block;
}

.hssjmore {
	height: 12.339583rem;
	background: #c00714;
	font-size: 0.833333rem;
	color: #f8b967;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	border-radius: 0.260416rem;
}

.moress img {
	display: block;
	width: 0.885416rem;
	padding-bottom: 0.260416rem;
}

.hssjtitle {
	font-size: 0.9375rem;
	color: #333333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	padding-top: 0.520833rem;
}

.zz {
	font-size: 0.729166rem;
	color: #cecece;
	padding-bottom: 1.5625rem;
}

.jxzyboxs {
	display: flex;
	width: 100%;
	padding-top: 1.5625rem;
}

.jxzyboxsleft {
	width: calc(100% - 3.020833rem);
}

.jxzyboxsmore {
	width: 3.020833rem;
	background: #f2d885;
	color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 0.833333rem;
}

.jxzyboxsmore img {
	width: 0.885416rem;
	display: block;
	padding-bottom: 0.260416rem;
}

.scrollsss {
	background: #FFFFFF;
	padding: 0 1.041666rem;
	margin-left: 1.041666rem;
	margin-right: 1.041666rem;
	overflow: auto;
	height: 10.416666rem;
	box-sizing: border-box;
}

.jxzyitem {
	height: calc(10.416666rem / 3);
	display: flex;
	align-items: center;
	box-sizing: border-box;
	border-bottom: 0.052083rem solid #fffaea;
	cursor: pointer;
}

.jxzyitem .llllllll {
	font-size: 0.520833rem;
	color: #FFFFFF;
	width: 1.875rem;
	height: 0.9375rem;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 0.260416rem;
	padding: 0.104166rem;
}

.pdf {
	background: #f8b967;
}

.ppt,
.pptx {
	background: #f36933;
}

.video,
.mp4,
.rmvb {
	background: #7a92bb;
}

.nrnrnr3 {
	width: 28.645833rem;
	box-sizing: border-box;
	padding-left: 0.520833rem;
}

.nrnrnr3 div {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.nrnrnr3 div:first-child {
	font-size: 0.9375rem;
	color: #333333;
}

.nrnrnr3 div:last-child {
	font-size: 0.729166rem;
	color: #c0c0c0;
}

.ririri {
	width: calc(100% - 28.645833rem - 1.875rem);
	display: flex;
	justify-content: flex-end;
}

.ririri label {
	color: #cecece;
	font-size: 0.833333rem;
	display: flex;
	align-items: center;
	margin-left: 1.041666rem;
}

.ririri label img {
	padding-right: 0.260416rem;
}

.ririri label img:first-child {
	width: 0.885416rem;
	display: block;
}

.ririri label img:last-child {
	width: 0.9375rem;
	display: block;
}

/* 设置滚动条的样式 */
.scrollsss::-webkit-scrollbar {
	width: 0.15625rem;
}

/* 滚动槽 */
.scrollsss::-webkit-scrollbar-track {
	-webkit-box-shadow: #f8e9b7;
	border-radius: 10px;
}

/* 滚动条滑块 */
.scrollsss::-webkit-scrollbar-thumb {
	border-radius: 10px;
	background: #dfbc4e;
	-webkit-box-shadow: #dfbc4e;
}

.scrollsss::-webkit-scrollbar-thumb:window-inactive {
	background: #f8e9b7;
}

.sjbox {
	display: flex;
	flex-wrap: wrap;
	justify-content: flex-start;
	margin-bottom: 2.083333rem;
	width: 100%;
}

.txtitem {
	width: 19%;
	margin-top: 1.041666rem;
	padding: 0.260416rem;
	box-sizing: border-box;
	border-radius: 0.260416rem;
	overflow: hidden;
	cursor: pointer;
}

.txtitem .topitem img {
	display: block;
	width: 100%;
	border-radius: 0.260416rem;
}

.sjbox {
	display: flex;
}

.zyitem {
	width: 19%;
	margin-top: 1.041666rem !important;
	padding: 0.260416rem !important;
	box-sizing: border-box;
	border-radius: 0.260416rem;
	overflow: hidden;
	cursor: pointer;
	margin-right: calc(5% / 4);
}
.zyitem:nth-child(5n){
	margin-right: 0;
}

.zyitemimgbox {
	position: relative;
}

.itemtype {
	position: absolute;
	top: 0.104166rem;
	left: 0.104166rem;
	color: #FFFFFF;
	font-size: 0.520833rem;
	padding: 0.104166rem 0.3125rem;
	border-radius: 0.260416rem;
}

.zyicobox {
	position: absolute;
	bottom: 0.104166rem;
	right: 0.104166rem;
	left: 0.104166rem;
	display: flex;
	justify-content: flex-end;
}

.zyicobox label {
	background: rgba(0, 0, 0, 0.5);
	border-radius: 0.260416rem;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 0.625rem;
	color: #ffffff;
	padding: 0.104166rem 0.260416rem;
	margin-left: 0.260416rem;
}

.zyicobox label:first-child img {
	width: 0.46875rem !important;
	height: 0.520833rem !important;
	display: block;
	padding-right: 0.260416rem;
}

.zyicobox label:last-child img {
	width: 0.677083rem !important;
	height: 0.416666rem !important;
	display: block;
	padding-right: 0.260416rem;
}

.fms {
	display: block;
	width: 100%;
}

.zytitle {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	font-size: 0.9375rem;
	color: #333333;
	padding-top: 0.520833rem;
}

.zyms {
	font-size: 0.729166rem;
	color: #cecece;
	padding-top: 0.260416rem;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
}
.bbbtn{
	width: 9.53125rem;
	height: 2.604166rem;
	background: #FFFFFF;
	border-radius: 0.260416rem;
	border-bottom: 0.15625rem solid #c00714;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #c00714;
	font-size: 0.833333rem;
	cursor: pointer;
}
.bbbbbbbbbbbbbbbbbbbbb{
	display: flex;
	justify-content: center;
	align-items: center;
}

#iframe{
	position: fixed;
	z-index: 999;
	left: 0;
	right: 0;
	bottom: 0;
	top: 0;
	display: none;
	width: 100%;
	height: 100%;
}
#closes{
	position: fixed;
	z-index: 9999;
	right: 3.645833rem;
	top: 1.041666rem;
	font-size: 0.833333rem;
	color: #FFFFFF;
	display: none;
	cursor: pointer;
	background: red;
	padding: 0.260416rem 0.520833rem;
	border-radius: 0.260416rem;
}
