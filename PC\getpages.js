// 确保当mtype为null时显示所有资源的函数
function ensureAllResourcesDisplayed() {
  // 默认全部显示
  window.mtype = null;
  
  // 监听DOM加载完成
  document.addEventListener('DOMContentLoaded', function() {
    console.log("确保显示所有资源");
    
    // 确保重置筛选时mtype始终为null
    const originalResetFilters = window.resetFilters;
    if (originalResetFilters) {
      window.resetFilters = function() {
        if (originalResetFilters) originalResetFilters();
        window.mtype = null;
      };
    }
    
    // 修改getActiveFiltersCount函数不考虑mtype
    const countFunctions = document.querySelectorAll('script:not([src])');
    countFunctions.forEach(script => {
      if (script.textContent.includes('getActiveFiltersCount')) {
        const newScript = document.createElement('script');
        newScript.textContent = script.textContent.replace(
          /if \(mtype !== null\) count\+\+;/g, 
          '// 类别筛选已移除，默认显示所有资源'
        );
        script.parentNode.replaceChild(newScript, script);
      }
    });
  });
}

// 导出函数
if (typeof module !== 'undefined') {
  module.exports = { ensureAllResourcesDisplayed };
} 