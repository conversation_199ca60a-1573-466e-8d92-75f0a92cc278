body {
	background: #fbfbfb;
}

.content {
	width: 66.666666rem;
	margin: 0px auto;
	min-height: 34rem;
}

.contexttopview {
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1px solid #dedede;
	padding-bottom: 1.5625rem;
	padding-top: 3.125rem;
	padding-left: 0.78125rem;
	padding-right: 0.78125rem;
}

.ctitle {
	font-size: 0.9375rem;
	color: #c00714;
	font-weight: bold;
}

.inputview {
	border: 1px solid #c00714;
	border-radius: 5px;
	display: flex;
	overflow: hidden;
	height: 1.5625rem;
}

.inputview input {
	width: 6.770833rem;
	border: none;
	outline: none;
	padding-left: 0.520833rem;
	padding-right: 0.520833rem;
	font-size: 0.833333rem;
}

.inputview span {
	background: #c00714;
	color: #FFFFFF;
	font-size: 0.833333rem;
	line-height: 1.5625rem;
	width: 3.125rem;
	text-align: center;
	cursor: pointer;
}

.xuekebox {
	height: 3.802083rem;
	border-bottom: 1px solid #dedede;
	display: flex;
	align-items: center;
	padding: 0px 0.78125rem;
}

.xuekebox span {
	color: #999999;
	font-size: 0.9375rem;
}

.xuekebox label {
	color: #333333;
	font-size: 0.9375rem;
	margin-left: 2.083333rem;
}

.items {
	padding: 0.416667rem;
	box-sizing: border-box;
	cursor: pointer;
	width: calc((100% - 2.083333rem) / 3);
	margin: 0 calc(2.083333rem / 6);
}

.items:hover {
	background: #FFFFFF;
}
.xuekeitem{
	cursor: pointer;
}
.xuekeitem:hover{
	font-weight: bold;
	color: #c00714;
}
.accccc {
	font-weight: bold;
	color: #c00714 !important;
}

.itemboxss {
	margin-top: 2.083333rem;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	padding-bottom: 2.083333rem;
}

.fybox {
	padding-bottom: 2.083333rem;
	margin-top: 0 !important;
}

.imgboxs {
	width: 100%;
	position: relative;
	overflow: hidden;
}

.jianjie {
	transition: 0.3s all;
	-webkit-transition: 0.3s all;
	-moz-transition: 0.3s all;
	-o-transition: 0.3s all;
	-ms-transition: 0.3s all;
	position: absolute;
	bottom: -100%;
	left: 0;
	right: 0;
	background: rgba(0, 0, 0, 0.6);
	color: #f3f3f3;
	font-size: 0.729167rem;
	padding: 0.677083rem;
	box-sizing: border-box;
}

.items:hover .jianjie {
	bottom: 0;
}

.jianjie div {
	overflow-y: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
}

.imgboxs img {
	width: 100%;
	display: block;
}

.iibt {
	color: #333333;
	font-size: 0.9375rem;
	margin-top: 0.520833rem;
	line-height: 1.458333rem;
	overflow: hidden;
	text-overflow:ellipsis;
	white-space: nowrap;
}

.ibbb {
	display: flex;
	align-items: center;
	margin-top: 0.520833rem;
}

.ibbb label {
	font-size: 0.729167rem;
	color: #cecece;
}

.ibbb img {
	display: block;
	margin-right: 0.260417rem;
}

.bbbsbs {
	width: 1px;
	height: 1.041667rem;
	background: #cecece;
	margin: 0px 1.041667rem;
}
