/**
 * @licstart The following is the entire license notice for the
 * JavaScript code in this page
 *
 * Copyright 2022 Mozilla Foundation
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *     http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 * @licend The above is the entire license notice for the
 * JavaScript code in this page
 */
!function webpackUniversalModuleDefinition(t,e){"object"==typeof exports&&"object"==typeof module?module.exports=e():"function"==typeof define&&define.amd?define("pdfjs-dist/build/pdf",[],e):"object"==typeof exports?exports["pdfjs-dist/build/pdf"]=e():t["pdfjs-dist/build/pdf"]=t.pdfjsLib=e()}(globalThis,(()=>(()=>{"use strict";var __webpack_modules__=[,(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.VerbosityLevel=e.Util=e.UnknownErrorException=e.UnexpectedResponseException=e.UNSUPPORTED_FEATURES=e.TextRenderingMode=e.StreamType=e.RenderingIntentFlag=e.PermissionFlag=e.PasswordResponses=e.PasswordException=e.PageActionEventType=e.OPS=e.MissingPDFException=e.LINE_FACTOR=e.LINE_DESCENT_FACTOR=e.InvalidPDFException=e.ImageKind=e.IDENTITY_MATRIX=e.FormatError=e.FontType=e.FeatureTest=e.FONT_IDENTITY_MATRIX=e.DocumentActionEventType=e.CMapCompressionType=e.BaseException=e.AnnotationType=e.AnnotationStateModelType=e.AnnotationReviewState=e.AnnotationReplyType=e.AnnotationMode=e.AnnotationMarkedState=e.AnnotationFlag=e.AnnotationFieldFlag=e.AnnotationEditorType=e.AnnotationEditorPrefix=e.AnnotationEditorParamsType=e.AnnotationBorderStyleType=e.AnnotationActionEventType=e.AbortException=void 0;e.arrayByteLength=arrayByteLength;e.arraysToBytes=function arraysToBytes(t){const e=t.length;if(1===e&&t[0]instanceof Uint8Array)return t[0];let s=0;for(let n=0;n<e;n++)s+=arrayByteLength(t[n]);let n=0;const i=new Uint8Array(s);for(let s=0;s<e;s++){let e=t[s];e instanceof Uint8Array||(e="string"==typeof e?stringToBytes(e):new Uint8Array(e));const r=e.byteLength;i.set(e,n);n+=r}return i};e.assert=function assert(t,e){t||unreachable(e)};e.bytesToString=function bytesToString(t){"object"==typeof t&&null!==t&&void 0!==t.length||unreachable("Invalid argument for bytesToString");const e=t.length,s=8192;if(e<s)return String.fromCharCode.apply(null,t);const n=[];for(let i=0;i<e;i+=s){const r=Math.min(i+s,e),a=t.subarray(i,r);n.push(String.fromCharCode.apply(null,a))}return n.join("")};e.createPromiseCapability=function createPromiseCapability(){const t=Object.create(null);let e=!1;Object.defineProperty(t,"settled",{get:()=>e});t.promise=new Promise((function(s,n){t.resolve=function(t){e=!0;s(t)};t.reject=function(t){e=!0;n(t)}}));return t};e.createValidAbsoluteUrl=function createValidAbsoluteUrl(t,e=null,s=null){if(!t)return null;try{if(s&&"string"==typeof t){if(s.addDefaultProtocol&&t.startsWith("www.")){const e=t.match(/\./g);e&&e.length>=2&&(t=`http://${t}`)}if(s.tryConvertEncoding)try{t=stringToUTF8String(t)}catch(t){}}const n=e?new URL(t,e):new URL(t);if(function _isValidProtocol(t){if(!t)return!1;switch(t.protocol){case"http:":case"https:":case"ftp:":case"mailto:":case"tel:":return!0;default:return!1}}(n))return n}catch(t){}return null};e.escapeString=function escapeString(t){return t.replace(/([()\\\n\r])/g,(t=>"\n"===t?"\\n":"\r"===t?"\\r":`\\${t}`))};e.getModificationDate=function getModificationDate(t=new Date){return[t.getUTCFullYear().toString(),(t.getUTCMonth()+1).toString().padStart(2,"0"),t.getUTCDate().toString().padStart(2,"0"),t.getUTCHours().toString().padStart(2,"0"),t.getUTCMinutes().toString().padStart(2,"0"),t.getUTCSeconds().toString().padStart(2,"0")].join("")};e.getVerbosityLevel=function getVerbosityLevel(){return i};e.info=function info(t){i>=n.INFOS&&console.log(`Info: ${t}`)};e.isArrayBuffer=function isArrayBuffer(t){return"object"==typeof t&&null!==t&&void 0!==t.byteLength};e.isArrayEqual=function isArrayEqual(t,e){if(t.length!==e.length)return!1;for(let s=0,n=t.length;s<n;s++)if(t[s]!==e[s])return!1;return!0};e.isAscii=function isAscii(t){return/^[\x00-\x7F]*$/.test(t)};e.objectFromMap=function objectFromMap(t){const e=Object.create(null);for(const[s,n]of t)e[s]=n;return e};e.objectSize=function objectSize(t){return Object.keys(t).length};e.setVerbosityLevel=function setVerbosityLevel(t){Number.isInteger(t)&&(i=t)};e.shadow=shadow;e.string32=function string32(t){return String.fromCharCode(t>>24&255,t>>16&255,t>>8&255,255&t)};e.stringToBytes=stringToBytes;e.stringToPDFString=function stringToPDFString(t){if(t[0]>="ï"){let e;"þ"===t[0]&&"ÿ"===t[1]?e="utf-16be":"ÿ"===t[0]&&"þ"===t[1]?e="utf-16le":"ï"===t[0]&&"»"===t[1]&&"¿"===t[2]&&(e="utf-8");if(e)try{const s=new TextDecoder(e,{fatal:!0}),n=stringToBytes(t);return s.decode(n)}catch(t){warn(`stringToPDFString: "${t}".`)}}const e=[];for(let s=0,n=t.length;s<n;s++){const n=o[t.charCodeAt(s)];e.push(n?String.fromCharCode(n):t.charAt(s))}return e.join("")};e.stringToUTF16BEString=function stringToUTF16BEString(t){const e=["þÿ"];for(let s=0,n=t.length;s<n;s++){const n=t.charCodeAt(s);e.push(String.fromCharCode(n>>8&255),String.fromCharCode(255&n))}return e.join("")};e.stringToUTF8String=stringToUTF8String;e.unreachable=unreachable;e.utf8StringToString=function utf8StringToString(t){return unescape(encodeURIComponent(t))};e.warn=warn;s(2);e.IDENTITY_MATRIX=[1,0,0,1,0,0];e.FONT_IDENTITY_MATRIX=[.001,0,0,.001,0,0];e.LINE_FACTOR=1.35;e.LINE_DESCENT_FACTOR=.35;e.RenderingIntentFlag={ANY:1,DISPLAY:2,PRINT:4,ANNOTATIONS_FORMS:16,ANNOTATIONS_STORAGE:32,ANNOTATIONS_DISABLE:64,OPLIST:256};e.AnnotationMode={DISABLE:0,ENABLE:1,ENABLE_FORMS:2,ENABLE_STORAGE:3};e.AnnotationEditorPrefix="pdfjs_internal_editor_";e.AnnotationEditorType={DISABLE:-1,NONE:0,FREETEXT:3,INK:15};e.AnnotationEditorParamsType={FREETEXT_SIZE:1,FREETEXT_COLOR:2,FREETEXT_OPACITY:3,INK_COLOR:11,INK_THICKNESS:12,INK_OPACITY:13};e.PermissionFlag={PRINT:4,MODIFY_CONTENTS:8,COPY:16,MODIFY_ANNOTATIONS:32,FILL_INTERACTIVE_FORMS:256,COPY_FOR_ACCESSIBILITY:512,ASSEMBLE:1024,PRINT_HIGH_QUALITY:2048};e.TextRenderingMode={FILL:0,STROKE:1,FILL_STROKE:2,INVISIBLE:3,FILL_ADD_TO_PATH:4,STROKE_ADD_TO_PATH:5,FILL_STROKE_ADD_TO_PATH:6,ADD_TO_PATH:7,FILL_STROKE_MASK:3,ADD_TO_PATH_FLAG:4};e.ImageKind={GRAYSCALE_1BPP:1,RGB_24BPP:2,RGBA_32BPP:3};e.AnnotationType={TEXT:1,LINK:2,FREETEXT:3,LINE:4,SQUARE:5,CIRCLE:6,POLYGON:7,POLYLINE:8,HIGHLIGHT:9,UNDERLINE:10,SQUIGGLY:11,STRIKEOUT:12,STAMP:13,CARET:14,INK:15,POPUP:16,FILEATTACHMENT:17,SOUND:18,MOVIE:19,WIDGET:20,SCREEN:21,PRINTERMARK:22,TRAPNET:23,WATERMARK:24,THREED:25,REDACT:26};e.AnnotationStateModelType={MARKED:"Marked",REVIEW:"Review"};e.AnnotationMarkedState={MARKED:"Marked",UNMARKED:"Unmarked"};e.AnnotationReviewState={ACCEPTED:"Accepted",REJECTED:"Rejected",CANCELLED:"Cancelled",COMPLETED:"Completed",NONE:"None"};e.AnnotationReplyType={GROUP:"Group",REPLY:"R"};e.AnnotationFlag={INVISIBLE:1,HIDDEN:2,PRINT:4,NOZOOM:8,NOROTATE:16,NOVIEW:32,READONLY:64,LOCKED:128,TOGGLENOVIEW:256,LOCKEDCONTENTS:512};e.AnnotationFieldFlag={READONLY:1,REQUIRED:2,NOEXPORT:4,MULTILINE:4096,PASSWORD:8192,NOTOGGLETOOFF:16384,RADIO:32768,PUSHBUTTON:65536,COMBO:131072,EDIT:262144,SORT:524288,FILESELECT:1048576,MULTISELECT:2097152,DONOTSPELLCHECK:4194304,DONOTSCROLL:8388608,COMB:16777216,RICHTEXT:33554432,RADIOSINUNISON:33554432,COMMITONSELCHANGE:67108864};e.AnnotationBorderStyleType={SOLID:1,DASHED:2,BEVELED:3,INSET:4,UNDERLINE:5};e.AnnotationActionEventType={E:"Mouse Enter",X:"Mouse Exit",D:"Mouse Down",U:"Mouse Up",Fo:"Focus",Bl:"Blur",PO:"PageOpen",PC:"PageClose",PV:"PageVisible",PI:"PageInvisible",K:"Keystroke",F:"Format",V:"Validate",C:"Calculate"};e.DocumentActionEventType={WC:"WillClose",WS:"WillSave",DS:"DidSave",WP:"WillPrint",DP:"DidPrint"};e.PageActionEventType={O:"PageOpen",C:"PageClose"};e.StreamType={UNKNOWN:"UNKNOWN",FLATE:"FLATE",LZW:"LZW",DCT:"DCT",JPX:"JPX",JBIG:"JBIG",A85:"A85",AHX:"AHX",CCF:"CCF",RLX:"RLX"};e.FontType={UNKNOWN:"UNKNOWN",TYPE1:"TYPE1",TYPE1STANDARD:"TYPE1STANDARD",TYPE1C:"TYPE1C",CIDFONTTYPE0:"CIDFONTTYPE0",CIDFONTTYPE0C:"CIDFONTTYPE0C",TRUETYPE:"TRUETYPE",CIDFONTTYPE2:"CIDFONTTYPE2",TYPE3:"TYPE3",OPENTYPE:"OPENTYPE",TYPE0:"TYPE0",MMTYPE1:"MMTYPE1"};const n={ERRORS:0,WARNINGS:1,INFOS:5};e.VerbosityLevel=n;e.CMapCompressionType={NONE:0,BINARY:1,STREAM:2};e.OPS={dependency:1,setLineWidth:2,setLineCap:3,setLineJoin:4,setMiterLimit:5,setDash:6,setRenderingIntent:7,setFlatness:8,setGState:9,save:10,restore:11,transform:12,moveTo:13,lineTo:14,curveTo:15,curveTo2:16,curveTo3:17,closePath:18,rectangle:19,stroke:20,closeStroke:21,fill:22,eoFill:23,fillStroke:24,eoFillStroke:25,closeFillStroke:26,closeEOFillStroke:27,endPath:28,clip:29,eoClip:30,beginText:31,endText:32,setCharSpacing:33,setWordSpacing:34,setHScale:35,setLeading:36,setFont:37,setTextRenderingMode:38,setTextRise:39,moveText:40,setLeadingMoveText:41,setTextMatrix:42,nextLine:43,showText:44,showSpacedText:45,nextLineShowText:46,nextLineSetSpacingShowText:47,setCharWidth:48,setCharWidthAndBounds:49,setStrokeColorSpace:50,setFillColorSpace:51,setStrokeColor:52,setStrokeColorN:53,setFillColor:54,setFillColorN:55,setStrokeGray:56,setFillGray:57,setStrokeRGBColor:58,setFillRGBColor:59,setStrokeCMYKColor:60,setFillCMYKColor:61,shadingFill:62,beginInlineImage:63,beginImageData:64,endInlineImage:65,paintXObject:66,markPoint:67,markPointProps:68,beginMarkedContent:69,beginMarkedContentProps:70,endMarkedContent:71,beginCompat:72,endCompat:73,paintFormXObjectBegin:74,paintFormXObjectEnd:75,beginGroup:76,endGroup:77,beginAnnotations:78,endAnnotations:79,beginAnnotation:80,endAnnotation:81,paintJpegXObject:82,paintImageMaskXObject:83,paintImageMaskXObjectGroup:84,paintImageXObject:85,paintInlineImageXObject:86,paintInlineImageXObjectGroup:87,paintImageXObjectRepeat:88,paintImageMaskXObjectRepeat:89,paintSolidColorImageMask:90,constructPath:91};e.UNSUPPORTED_FEATURES={unknown:"unknown",forms:"forms",javaScript:"javaScript",signatures:"signatures",smask:"smask",shadingPattern:"shadingPattern",font:"font",errorTilingPattern:"errorTilingPattern",errorExtGState:"errorExtGState",errorXObject:"errorXObject",errorFontLoadType3:"errorFontLoadType3",errorFontState:"errorFontState",errorFontMissing:"errorFontMissing",errorFontTranslate:"errorFontTranslate",errorColorSpace:"errorColorSpace",errorOperatorList:"errorOperatorList",errorFontToUnicode:"errorFontToUnicode",errorFontLoadNative:"errorFontLoadNative",errorFontBuildPath:"errorFontBuildPath",errorFontGetPath:"errorFontGetPath",errorMarkedContent:"errorMarkedContent",errorContentSubStream:"errorContentSubStream"};e.PasswordResponses={NEED_PASSWORD:1,INCORRECT_PASSWORD:2};let i=n.WARNINGS;function warn(t){i>=n.WARNINGS&&console.log(`Warning: ${t}`)}function unreachable(t){throw new Error(t)}function shadow(t,e,s){Object.defineProperty(t,e,{value:s,enumerable:!0,configurable:!0,writable:!1});return s}const r=function BaseExceptionClosure(){function BaseException(t,e){this.constructor===BaseException&&unreachable("Cannot initialize BaseException.");this.message=t;this.name=e}BaseException.prototype=new Error;BaseException.constructor=BaseException;return BaseException}();e.BaseException=r;e.PasswordException=class PasswordException extends r{constructor(t,e){super(t,"PasswordException");this.code=e}};e.UnknownErrorException=class UnknownErrorException extends r{constructor(t,e){super(t,"UnknownErrorException");this.details=e}};e.InvalidPDFException=class InvalidPDFException extends r{constructor(t){super(t,"InvalidPDFException")}};e.MissingPDFException=class MissingPDFException extends r{constructor(t){super(t,"MissingPDFException")}};e.UnexpectedResponseException=class UnexpectedResponseException extends r{constructor(t,e){super(t,"UnexpectedResponseException");this.status=e}};e.FormatError=class FormatError extends r{constructor(t){super(t,"FormatError")}};e.AbortException=class AbortException extends r{constructor(t){super(t,"AbortException")}};function stringToBytes(t){"string"!=typeof t&&unreachable("Invalid argument for stringToBytes");const e=t.length,s=new Uint8Array(e);for(let n=0;n<e;++n)s[n]=255&t.charCodeAt(n);return s}function arrayByteLength(t){if(void 0!==t.length)return t.length;if(void 0!==t.byteLength)return t.byteLength;unreachable("Invalid argument for arrayByteLength")}e.FeatureTest=class FeatureTest{static get isLittleEndian(){return shadow(this,"isLittleEndian",function isLittleEndian(){const t=new Uint8Array(4);t[0]=1;return 1===new Uint32Array(t.buffer,0,1)[0]}())}static get isEvalSupported(){return shadow(this,"isEvalSupported",function isEvalSupported(){try{new Function("");return!0}catch(t){return!1}}())}static get isOffscreenCanvasSupported(){return shadow(this,"isOffscreenCanvasSupported","undefined"!=typeof OffscreenCanvas)}};const a=[...Array(256).keys()].map((t=>t.toString(16).padStart(2,"0")));class Util{static makeHexColor(t,e,s){return`#${a[t]}${a[e]}${a[s]}`}static scaleMinMax(t,e){let s;if(t[0]){if(t[0]<0){s=e[0];e[0]=e[1];e[1]=s}e[0]*=t[0];e[1]*=t[0];if(t[3]<0){s=e[2];e[2]=e[3];e[3]=s}e[2]*=t[3];e[3]*=t[3]}else{s=e[0];e[0]=e[2];e[2]=s;s=e[1];e[1]=e[3];e[3]=s;if(t[1]<0){s=e[2];e[2]=e[3];e[3]=s}e[2]*=t[1];e[3]*=t[1];if(t[2]<0){s=e[0];e[0]=e[1];e[1]=s}e[0]*=t[2];e[1]*=t[2]}e[0]+=t[4];e[1]+=t[4];e[2]+=t[5];e[3]+=t[5]}static transform(t,e){return[t[0]*e[0]+t[2]*e[1],t[1]*e[0]+t[3]*e[1],t[0]*e[2]+t[2]*e[3],t[1]*e[2]+t[3]*e[3],t[0]*e[4]+t[2]*e[5]+t[4],t[1]*e[4]+t[3]*e[5]+t[5]]}static applyTransform(t,e){return[t[0]*e[0]+t[1]*e[2]+e[4],t[0]*e[1]+t[1]*e[3]+e[5]]}static applyInverseTransform(t,e){const s=e[0]*e[3]-e[1]*e[2];return[(t[0]*e[3]-t[1]*e[2]+e[2]*e[5]-e[4]*e[3])/s,(-t[0]*e[1]+t[1]*e[0]+e[4]*e[1]-e[5]*e[0])/s]}static getAxialAlignedBoundingBox(t,e){const s=Util.applyTransform(t,e),n=Util.applyTransform(t.slice(2,4),e),i=Util.applyTransform([t[0],t[3]],e),r=Util.applyTransform([t[2],t[1]],e);return[Math.min(s[0],n[0],i[0],r[0]),Math.min(s[1],n[1],i[1],r[1]),Math.max(s[0],n[0],i[0],r[0]),Math.max(s[1],n[1],i[1],r[1])]}static inverseTransform(t){const e=t[0]*t[3]-t[1]*t[2];return[t[3]/e,-t[1]/e,-t[2]/e,t[0]/e,(t[2]*t[5]-t[4]*t[3])/e,(t[4]*t[1]-t[5]*t[0])/e]}static apply3dTransform(t,e){return[t[0]*e[0]+t[1]*e[1]+t[2]*e[2],t[3]*e[0]+t[4]*e[1]+t[5]*e[2],t[6]*e[0]+t[7]*e[1]+t[8]*e[2]]}static singularValueDecompose2dScale(t){const e=[t[0],t[2],t[1],t[3]],s=t[0]*e[0]+t[1]*e[2],n=t[0]*e[1]+t[1]*e[3],i=t[2]*e[0]+t[3]*e[2],r=t[2]*e[1]+t[3]*e[3],a=(s+r)/2,o=Math.sqrt((s+r)**2-4*(s*r-i*n))/2,l=a+o||1,c=a-o||1;return[Math.sqrt(l),Math.sqrt(c)]}static normalizeRect(t){const e=t.slice(0);if(t[0]>t[2]){e[0]=t[2];e[2]=t[0]}if(t[1]>t[3]){e[1]=t[3];e[3]=t[1]}return e}static intersect(t,e){const s=Math.max(Math.min(t[0],t[2]),Math.min(e[0],e[2])),n=Math.min(Math.max(t[0],t[2]),Math.max(e[0],e[2]));if(s>n)return null;const i=Math.max(Math.min(t[1],t[3]),Math.min(e[1],e[3])),r=Math.min(Math.max(t[1],t[3]),Math.max(e[1],e[3]));return i>r?null:[s,i,n,r]}static bezierBoundingBox(t,e,s,n,i,r,a,o){const l=[],c=[[],[]];let h,d,u,p,g,f,m,b;for(let c=0;c<2;++c){if(0===c){d=6*t-12*s+6*i;h=-3*t+9*s-9*i+3*a;u=3*s-3*t}else{d=6*e-12*n+6*r;h=-3*e+9*n-9*r+3*o;u=3*n-3*e}if(Math.abs(h)<1e-12){if(Math.abs(d)<1e-12)continue;p=-u/d;0<p&&p<1&&l.push(p)}else{m=d*d-4*u*h;b=Math.sqrt(m);if(!(m<0)){g=(-d+b)/(2*h);0<g&&g<1&&l.push(g);f=(-d-b)/(2*h);0<f&&f<1&&l.push(f)}}}let A,_=l.length;const y=_;for(;_--;){p=l[_];A=1-p;c[0][_]=A*A*A*t+3*A*A*p*s+3*A*p*p*i+p*p*p*a;c[1][_]=A*A*A*e+3*A*A*p*n+3*A*p*p*r+p*p*p*o}c[0][y]=t;c[1][y]=e;c[0][y+1]=a;c[1][y+1]=o;c[0].length=c[1].length=y+2;return[Math.min(...c[0]),Math.min(...c[1]),Math.max(...c[0]),Math.max(...c[1])]}}e.Util=Util;const o=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,728,711,710,729,733,731,730,732,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,8226,8224,8225,8230,8212,8211,402,8260,8249,8250,8722,8240,8222,8220,8221,8216,8217,8218,8482,64257,64258,321,338,352,376,381,305,322,339,353,382,0,8364];function stringToUTF8String(t){return decodeURIComponent(escape(t))}},(t,e,s)=>{s(3)},(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0});e.isNodeJS=void 0;const s=!("object"!=typeof process||process+""!="[object process]"||process.versions.nw||process.versions.electron&&process.type&&"browser"!==process.type);e.isNodeJS=s},(__unused_webpack_module,exports,__w_pdfjs_require__)=>{Object.defineProperty(exports,"__esModule",{value:!0});exports.build=exports.RenderTask=exports.PDFWorkerUtil=exports.PDFWorker=exports.PDFPageProxy=exports.PDFDocumentProxy=exports.PDFDocumentLoadingTask=exports.PDFDataRangeTransport=exports.LoopbackPort=exports.DefaultStandardFontDataFactory=exports.DefaultCanvasFactory=exports.DefaultCMapReaderFactory=void 0;exports.getDocument=getDocument;exports.setPDFNetworkStreamFactory=setPDFNetworkStreamFactory;exports.version=void 0;var _util=__w_pdfjs_require__(1),_annotation_storage=__w_pdfjs_require__(5),_display_utils=__w_pdfjs_require__(8),_font_loader=__w_pdfjs_require__(11),_canvas=__w_pdfjs_require__(12),_worker_options=__w_pdfjs_require__(15),_is_node=__w_pdfjs_require__(3),_message_handler=__w_pdfjs_require__(16),_metadata=__w_pdfjs_require__(17),_optional_content_config=__w_pdfjs_require__(18),_transport_stream=__w_pdfjs_require__(19),_xfa_text=__w_pdfjs_require__(20);const DEFAULT_RANGE_CHUNK_SIZE=65536,RENDERING_CANCELLED_TIMEOUT=100;let DefaultCanvasFactory=_display_utils.DOMCanvasFactory;exports.DefaultCanvasFactory=DefaultCanvasFactory;let DefaultCMapReaderFactory=_display_utils.DOMCMapReaderFactory;exports.DefaultCMapReaderFactory=DefaultCMapReaderFactory;let DefaultStandardFontDataFactory=_display_utils.DOMStandardFontDataFactory,createPDFNetworkStream;exports.DefaultStandardFontDataFactory=DefaultStandardFontDataFactory;if(_is_node.isNodeJS){const{NodeCanvasFactory:t,NodeCMapReaderFactory:e,NodeStandardFontDataFactory:s}=__w_pdfjs_require__(21);exports.DefaultCanvasFactory=DefaultCanvasFactory=t;exports.DefaultCMapReaderFactory=DefaultCMapReaderFactory=e;exports.DefaultStandardFontDataFactory=DefaultStandardFontDataFactory=s}function setPDFNetworkStreamFactory(t){createPDFNetworkStream=t}function getDocument(t){const e=new PDFDocumentLoadingTask;let s;if("string"==typeof t||t instanceof URL)s={url:t};else if((0,_util.isArrayBuffer)(t))s={data:t};else if(t instanceof PDFDataRangeTransport)s={range:t};else{if("object"!=typeof t)throw new Error("Invalid parameter in getDocument, need either string, URL, TypedArray, or parameter object.");if(!t.url&&!t.data&&!t.range)throw new Error("Invalid parameter object: need either .data, .range or .url");s=t}const n=Object.create(null);let i=null,r=null;for(const t in s){const e=s[t];switch(t){case"url":if("undefined"!=typeof window)try{n[t]=new URL(e,window.location).href;continue}catch(t){(0,_util.warn)(`Cannot create valid URL: "${t}".`)}else if("string"==typeof e||e instanceof URL){n[t]=e.toString();continue}throw new Error("Invalid PDF url data: either string or URL-object is expected in the url property.");case"range":i=e;continue;case"worker":r=e;continue;case"data":if(_is_node.isNodeJS&&"undefined"!=typeof Buffer&&e instanceof Buffer)n[t]=new Uint8Array(e);else{if(e instanceof Uint8Array)break;if("string"==typeof e)n[t]=(0,_util.stringToBytes)(e);else if("object"!=typeof e||null===e||isNaN(e.length)){if(!(0,_util.isArrayBuffer)(e))throw new Error("Invalid PDF binary data: either TypedArray, string, or array-like object is expected in the data property.");n[t]=new Uint8Array(e)}else n[t]=new Uint8Array(e)}continue}n[t]=e}n.CMapReaderFactory=n.CMapReaderFactory||DefaultCMapReaderFactory;n.StandardFontDataFactory=n.StandardFontDataFactory||DefaultStandardFontDataFactory;n.ignoreErrors=!0!==n.stopAtErrors;n.fontExtraProperties=!0===n.fontExtraProperties;n.pdfBug=!0===n.pdfBug;n.enableXfa=!0===n.enableXfa;(!Number.isInteger(n.rangeChunkSize)||n.rangeChunkSize<1)&&(n.rangeChunkSize=DEFAULT_RANGE_CHUNK_SIZE);("string"!=typeof n.docBaseUrl||(0,_display_utils.isDataScheme)(n.docBaseUrl))&&(n.docBaseUrl=null);(!Number.isInteger(n.maxImageSize)||n.maxImageSize<-1)&&(n.maxImageSize=-1);"string"!=typeof n.cMapUrl&&(n.cMapUrl=null);"string"!=typeof n.standardFontDataUrl&&(n.standardFontDataUrl=null);"boolean"!=typeof n.useWorkerFetch&&(n.useWorkerFetch=n.CMapReaderFactory===_display_utils.DOMCMapReaderFactory&&n.StandardFontDataFactory===_display_utils.DOMStandardFontDataFactory);"boolean"!=typeof n.isEvalSupported&&(n.isEvalSupported=!0);"boolean"!=typeof n.disableFontFace&&(n.disableFontFace=_is_node.isNodeJS);"boolean"!=typeof n.useSystemFonts&&(n.useSystemFonts=!_is_node.isNodeJS&&!n.disableFontFace);"object"==typeof n.ownerDocument&&null!==n.ownerDocument||(n.ownerDocument=globalThis.document);"boolean"!=typeof n.disableRange&&(n.disableRange=!1);"boolean"!=typeof n.disableStream&&(n.disableStream=!1);"boolean"!=typeof n.disableAutoFetch&&(n.disableAutoFetch=!1);(0,_util.setVerbosityLevel)(n.verbosity);if(!r){const t={verbosity:n.verbosity,port:_worker_options.GlobalWorkerOptions.workerPort};r=t.port?PDFWorker.fromPort(t):new PDFWorker(t);e._worker=r}const a=e.docId;r.promise.then((function(){if(e.destroyed)throw new Error("Loading aborted");const t=_fetchDocument(r,n,i,a),s=new Promise((function(t){let e;i?e=new _transport_stream.PDFDataTransportStream({length:n.length,initialData:n.initialData,progressiveDone:n.progressiveDone,contentDispositionFilename:n.contentDispositionFilename,disableRange:n.disableRange,disableStream:n.disableStream},i):n.data||(e=createPDFNetworkStream({url:n.url,length:n.length,httpHeaders:n.httpHeaders,withCredentials:n.withCredentials,rangeChunkSize:n.rangeChunkSize,disableRange:n.disableRange,disableStream:n.disableStream}));t(e)}));return Promise.all([t,s]).then((function([t,s]){if(e.destroyed)throw new Error("Loading aborted");const i=new _message_handler.MessageHandler(a,t,r.port),o=new WorkerTransport(i,e,s,n);e._transport=o;i.send("Ready",null)}))})).catch(e._capability.reject);return e}async function _fetchDocument(t,e,s,n){if(t.destroyed)throw new Error("Worker was destroyed");if(s){e.length=s.length;e.initialData=s.initialData;e.progressiveDone=s.progressiveDone;e.contentDispositionFilename=s.contentDispositionFilename}const i=await t.messageHandler.sendWithPromise("GetDocRequest",{docId:n,apiVersion:"2.16.105",source:{data:e.data,url:e.url,password:e.password,disableAutoFetch:e.disableAutoFetch,rangeChunkSize:e.rangeChunkSize,length:e.length},maxImageSize:e.maxImageSize,disableFontFace:e.disableFontFace,docBaseUrl:e.docBaseUrl,ignoreErrors:e.ignoreErrors,isEvalSupported:e.isEvalSupported,fontExtraProperties:e.fontExtraProperties,enableXfa:e.enableXfa,useSystemFonts:e.useSystemFonts,cMapUrl:e.useWorkerFetch?e.cMapUrl:null,standardFontDataUrl:e.useWorkerFetch?e.standardFontDataUrl:null});e.data&&(e.data=null);if(t.destroyed)throw new Error("Worker was destroyed");return i}class PDFDocumentLoadingTask{static#t=0;constructor(){this._capability=(0,_util.createPromiseCapability)();this._transport=null;this._worker=null;this.docId="d"+PDFDocumentLoadingTask.#t++;this.destroyed=!1;this.onPassword=null;this.onProgress=null;this.onUnsupportedFeature=null}get promise(){return this._capability.promise}async destroy(){this.destroyed=!0;await(this._transport?.destroy());this._transport=null;if(this._worker){this._worker.destroy();this._worker=null}}}exports.PDFDocumentLoadingTask=PDFDocumentLoadingTask;class PDFDataRangeTransport{constructor(t,e,s=!1,n=null){this.length=t;this.initialData=e;this.progressiveDone=s;this.contentDispositionFilename=n;this._rangeListeners=[];this._progressListeners=[];this._progressiveReadListeners=[];this._progressiveDoneListeners=[];this._readyCapability=(0,_util.createPromiseCapability)()}addRangeListener(t){this._rangeListeners.push(t)}addProgressListener(t){this._progressListeners.push(t)}addProgressiveReadListener(t){this._progressiveReadListeners.push(t)}addProgressiveDoneListener(t){this._progressiveDoneListeners.push(t)}onDataRange(t,e){for(const s of this._rangeListeners)s(t,e)}onDataProgress(t,e){this._readyCapability.promise.then((()=>{for(const s of this._progressListeners)s(t,e)}))}onDataProgressiveRead(t){this._readyCapability.promise.then((()=>{for(const e of this._progressiveReadListeners)e(t)}))}onDataProgressiveDone(){this._readyCapability.promise.then((()=>{for(const t of this._progressiveDoneListeners)t()}))}transportReady(){this._readyCapability.resolve()}requestDataRange(t,e){(0,_util.unreachable)("Abstract method PDFDataRangeTransport.requestDataRange")}abort(){}}exports.PDFDataRangeTransport=PDFDataRangeTransport;class PDFDocumentProxy{constructor(t,e){this._pdfInfo=t;this._transport=e;Object.defineProperty(this,"fingerprint",{get(){(0,_display_utils.deprecated)("`PDFDocumentProxy.fingerprint`, please use `PDFDocumentProxy.fingerprints` instead.");return this.fingerprints[0]}});Object.defineProperty(this,"getStats",{value:async()=>{(0,_display_utils.deprecated)("`PDFDocumentProxy.getStats`, please use the `PDFDocumentProxy.stats`-getter instead.");return this.stats||{streamTypes:{},fontTypes:{}}}})}get annotationStorage(){return this._transport.annotationStorage}get numPages(){return this._pdfInfo.numPages}get fingerprints(){return this._pdfInfo.fingerprints}get stats(){return this._transport.stats}get isPureXfa(){return!!this._transport._htmlForXfa}get allXfaHtml(){return this._transport._htmlForXfa}getPage(t){return this._transport.getPage(t)}getPageIndex(t){return this._transport.getPageIndex(t)}getDestinations(){return this._transport.getDestinations()}getDestination(t){return this._transport.getDestination(t)}getPageLabels(){return this._transport.getPageLabels()}getPageLayout(){return this._transport.getPageLayout()}getPageMode(){return this._transport.getPageMode()}getViewerPreferences(){return this._transport.getViewerPreferences()}getOpenAction(){return this._transport.getOpenAction()}getAttachments(){return this._transport.getAttachments()}getJavaScript(){return this._transport.getJavaScript()}getJSActions(){return this._transport.getDocJSActions()}getOutline(){return this._transport.getOutline()}getOptionalContentConfig(){return this._transport.getOptionalContentConfig()}getPermissions(){return this._transport.getPermissions()}getMetadata(){return this._transport.getMetadata()}getMarkInfo(){return this._transport.getMarkInfo()}getData(){return this._transport.getData()}getDownloadInfo(){return this._transport.downloadInfoCapability.promise}cleanup(t=!1){return this._transport.startCleanup(t||this.isPureXfa)}destroy(){return this.loadingTask.destroy()}get loadingParams(){return this._transport.loadingParams}get loadingTask(){return this._transport.loadingTask}saveDocument(){this._transport.annotationStorage.size<=0&&(0,_display_utils.deprecated)("saveDocument called while `annotationStorage` is empty, please use the getData-method instead.");return this._transport.saveDocument()}getFieldObjects(){return this._transport.getFieldObjects()}hasJSActions(){return this._transport.hasJSActions()}getCalculationOrderIds(){return this._transport.getCalculationOrderIds()}}exports.PDFDocumentProxy=PDFDocumentProxy;class PDFPageProxy{constructor(t,e,s,n,i=!1){this._pageIndex=t;this._pageInfo=e;this._ownerDocument=n;this._transport=s;this._stats=i?new _display_utils.StatTimer:null;this._pdfBug=i;this.commonObjs=s.commonObjs;this.objs=new PDFObjects;this._bitmaps=new Set;this.cleanupAfterRender=!1;this.pendingCleanup=!1;this._intentStates=new Map;this._annotationPromises=new Map;this.destroyed=!1}get pageNumber(){return this._pageIndex+1}get rotate(){return this._pageInfo.rotate}get ref(){return this._pageInfo.ref}get userUnit(){return this._pageInfo.userUnit}get view(){return this._pageInfo.view}getViewport({scale:t,rotation:e=this.rotate,offsetX:s=0,offsetY:n=0,dontFlip:i=!1}={}){return new _display_utils.PageViewport({viewBox:this.view,scale:t,rotation:e,offsetX:s,offsetY:n,dontFlip:i})}getAnnotations({intent:t="display"}={}){const e=this._transport.getRenderingIntent(t);let s=this._annotationPromises.get(e.cacheKey);if(!s){s=this._transport.getAnnotations(this._pageIndex,e.renderingIntent);this._annotationPromises.set(e.cacheKey,s);s=s.then((t=>{for(const e of t){void 0!==e.titleObj&&Object.defineProperty(e,"title",{get(){(0,_display_utils.deprecated)("`title`-property on annotation, please use `titleObj` instead.");return e.titleObj.str}});void 0!==e.contentsObj&&Object.defineProperty(e,"contents",{get(){(0,_display_utils.deprecated)("`contents`-property on annotation, please use `contentsObj` instead.");return e.contentsObj.str}})}return t}))}return s}getJSActions(){return this._jsActionsPromise||=this._transport.getPageJSActions(this._pageIndex)}async getXfa(){return this._transport._htmlForXfa?.children[this._pageIndex]||null}render({canvasContext:t,viewport:e,intent:s="display",annotationMode:n=_util.AnnotationMode.ENABLE,transform:i=null,imageLayer:r=null,canvasFactory:a=null,background:o=null,optionalContentConfigPromise:l=null,annotationCanvasMap:c=null,pageColors:h=null,printAnnotationStorage:d=null}){if(void 0!==arguments[0]?.renderInteractiveForms){(0,_display_utils.deprecated)("render no longer accepts the `renderInteractiveForms`-option, please use the `annotationMode`-option instead.");!0===arguments[0].renderInteractiveForms&&n===_util.AnnotationMode.ENABLE&&(n=_util.AnnotationMode.ENABLE_FORMS)}if(void 0!==arguments[0]?.includeAnnotationStorage){(0,_display_utils.deprecated)("render no longer accepts the `includeAnnotationStorage`-option, please use the `annotationMode`-option instead.");!0===arguments[0].includeAnnotationStorage&&n===_util.AnnotationMode.ENABLE&&(n=_util.AnnotationMode.ENABLE_STORAGE)}this._stats&&this._stats.time("Overall");const u=this._transport.getRenderingIntent(s,n,d);this.pendingCleanup=!1;l||(l=this._transport.getOptionalContentConfig());let p=this._intentStates.get(u.cacheKey);if(!p){p=Object.create(null);this._intentStates.set(u.cacheKey,p)}if(p.streamReaderCancelTimeout){clearTimeout(p.streamReaderCancelTimeout);p.streamReaderCancelTimeout=null}const g=a||new DefaultCanvasFactory({ownerDocument:this._ownerDocument}),f=!!(u.renderingIntent&_util.RenderingIntentFlag.PRINT);if(!p.displayReadyCapability){p.displayReadyCapability=(0,_util.createPromiseCapability)();p.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null};this._stats&&this._stats.time("Page Request");this._pumpOperatorList(u)}const complete=t=>{p.renderTasks.delete(m);(this.cleanupAfterRender||f)&&(this.pendingCleanup=!0);this._tryCleanup();if(t){m.capability.reject(t);this._abortOperatorList({intentState:p,reason:t instanceof Error?t:new Error(t)})}else m.capability.resolve();if(this._stats){this._stats.timeEnd("Rendering");this._stats.timeEnd("Overall")}},m=new InternalRenderTask({callback:complete,params:{canvasContext:t,viewport:e,transform:i,imageLayer:r,background:o},objs:this.objs,commonObjs:this.commonObjs,annotationCanvasMap:c,operatorList:p.operatorList,pageIndex:this._pageIndex,canvasFactory:g,useRequestAnimationFrame:!f,pdfBug:this._pdfBug,pageColors:h});(p.renderTasks||=new Set).add(m);const b=m.task;Promise.all([p.displayReadyCapability.promise,l]).then((([t,e])=>{if(this.pendingCleanup)complete();else{this._stats&&this._stats.time("Rendering");m.initializeGraphics({transparency:t,optionalContentConfig:e});m.operatorListChanged()}})).catch(complete);return b}getOperatorList({intent:t="display",annotationMode:e=_util.AnnotationMode.ENABLE,printAnnotationStorage:s=null}={}){const n=this._transport.getRenderingIntent(t,e,s,!0);let i,r=this._intentStates.get(n.cacheKey);if(!r){r=Object.create(null);this._intentStates.set(n.cacheKey,r)}if(!r.opListReadCapability){i=Object.create(null);i.operatorListChanged=function operatorListChanged(){if(r.operatorList.lastChunk){r.opListReadCapability.resolve(r.operatorList);r.renderTasks.delete(i)}};r.opListReadCapability=(0,_util.createPromiseCapability)();(r.renderTasks||=new Set).add(i);r.operatorList={fnArray:[],argsArray:[],lastChunk:!1,separateAnnots:null};this._stats&&this._stats.time("Page Request");this._pumpOperatorList(n)}return r.opListReadCapability.promise}streamTextContent({disableCombineTextItems:t=!1,includeMarkedContent:e=!1}={}){return this._transport.messageHandler.sendWithStream("GetTextContent",{pageIndex:this._pageIndex,combineTextItems:!0!==t,includeMarkedContent:!0===e},{highWaterMark:100,size:t=>t.items.length})}getTextContent(t={}){if(this._transport._htmlForXfa)return this.getXfa().then((t=>_xfa_text.XfaText.textContent(t)));const e=this.streamTextContent(t);return new Promise((function(t,s){const n=e.getReader(),i={items:[],styles:Object.create(null)};!function pump(){n.read().then((function({value:e,done:s}){if(s)t(i);else{Object.assign(i.styles,e.styles);i.items.push(...e.items);pump()}}),s)}()}))}getStructTree(){return this._structTreePromise||=this._transport.getStructTree(this._pageIndex)}_destroy(){this.destroyed=!0;const t=[];for(const e of this._intentStates.values()){this._abortOperatorList({intentState:e,reason:new Error("Page was destroyed."),force:!0});if(!e.opListReadCapability)for(const s of e.renderTasks){t.push(s.completed);s.cancel()}}this.objs.clear();for(const t of this._bitmaps)t.close();this._bitmaps.clear();this._annotationPromises.clear();this._jsActionsPromise=null;this._structTreePromise=null;this.pendingCleanup=!1;return Promise.all(t)}cleanup(t=!1){this.pendingCleanup=!0;return this._tryCleanup(t)}_tryCleanup(t=!1){if(!this.pendingCleanup)return!1;for(const{renderTasks:t,operatorList:e}of this._intentStates.values())if(t.size>0||!e.lastChunk)return!1;this._intentStates.clear();this.objs.clear();this._annotationPromises.clear();this._jsActionsPromise=null;this._structTreePromise=null;t&&this._stats&&(this._stats=new _display_utils.StatTimer);for(const t of this._bitmaps)t.close();this._bitmaps.clear();this.pendingCleanup=!1;return!0}_startRenderPage(t,e){const s=this._intentStates.get(e);if(s){this._stats&&this._stats.timeEnd("Page Request");s.displayReadyCapability&&s.displayReadyCapability.resolve(t)}}_renderPageChunk(t,e){for(let s=0,n=t.length;s<n;s++){e.operatorList.fnArray.push(t.fnArray[s]);e.operatorList.argsArray.push(t.argsArray[s])}e.operatorList.lastChunk=t.lastChunk;e.operatorList.separateAnnots=t.separateAnnots;for(const t of e.renderTasks)t.operatorListChanged();t.lastChunk&&this._tryCleanup()}_pumpOperatorList({renderingIntent:t,cacheKey:e,annotationStorageMap:s}){const n=this._transport.messageHandler.sendWithStream("GetOperatorList",{pageIndex:this._pageIndex,intent:t,cacheKey:e,annotationStorage:s}).getReader(),i=this._intentStates.get(e);i.streamReader=n;const pump=()=>{n.read().then((({value:t,done:e})=>{if(e)i.streamReader=null;else if(!this._transport.destroyed){this._renderPageChunk(t,i);pump()}}),(t=>{i.streamReader=null;if(!this._transport.destroyed){if(i.operatorList){i.operatorList.lastChunk=!0;for(const t of i.renderTasks)t.operatorListChanged();this._tryCleanup()}if(i.displayReadyCapability)i.displayReadyCapability.reject(t);else{if(!i.opListReadCapability)throw t;i.opListReadCapability.reject(t)}}}))};pump()}_abortOperatorList({intentState:t,reason:e,force:s=!1}){if(t.streamReader){if(!s){if(t.renderTasks.size>0)return;if(e instanceof _display_utils.RenderingCancelledException){t.streamReaderCancelTimeout=setTimeout((()=>{this._abortOperatorList({intentState:t,reason:e,force:!0});t.streamReaderCancelTimeout=null}),RENDERING_CANCELLED_TIMEOUT);return}}t.streamReader.cancel(new _util.AbortException(e.message)).catch((()=>{}));t.streamReader=null;if(!this._transport.destroyed){for(const[e,s]of this._intentStates)if(s===t){this._intentStates.delete(e);break}this.cleanup()}}}get stats(){return this._stats}}exports.PDFPageProxy=PDFPageProxy;class LoopbackPort{constructor(){this._listeners=[];this._deferred=Promise.resolve()}postMessage(t,e){const s={data:structuredClone(t,e)};this._deferred.then((()=>{for(const t of this._listeners)t.call(this,s)}))}addEventListener(t,e){this._listeners.push(e)}removeEventListener(t,e){const s=this._listeners.indexOf(e);this._listeners.splice(s,1)}terminate(){this._listeners.length=0}}exports.LoopbackPort=LoopbackPort;const PDFWorkerUtil={isWorkerDisabled:!1,fallbackWorkerSrc:null,fakeWorkerId:0};exports.PDFWorkerUtil=PDFWorkerUtil;if(_is_node.isNodeJS&&"function"==typeof require){PDFWorkerUtil.isWorkerDisabled=!0;PDFWorkerUtil.fallbackWorkerSrc="./pdf.worker.js"}else if("object"==typeof document){const t=document?.currentScript?.src;t&&(PDFWorkerUtil.fallbackWorkerSrc=t.replace(/(\.(?:min\.)?js)(\?.*)?$/i,".worker$1$2"))}PDFWorkerUtil.isSameOrigin=function(t,e){let s;try{s=new URL(t);if(!s.origin||"null"===s.origin)return!1}catch(t){return!1}const n=new URL(e,s);return s.origin===n.origin};PDFWorkerUtil.createCDNWrapper=function(t){const e=`importScripts("${t}");`;return URL.createObjectURL(new Blob([e]))};class PDFWorker{static#e=new WeakMap;constructor({name:t=null,port:e=null,verbosity:s=(0,_util.getVerbosityLevel)()}={}){if(e&&PDFWorker.#e.has(e))throw new Error("Cannot use more than one PDFWorker per port.");this.name=t;this.destroyed=!1;this.verbosity=s;this._readyCapability=(0,_util.createPromiseCapability)();this._port=null;this._webWorker=null;this._messageHandler=null;if(e){PDFWorker.#e.set(e,this);this._initializeFromPort(e)}else this._initialize()}get promise(){return this._readyCapability.promise}get port(){return this._port}get messageHandler(){return this._messageHandler}_initializeFromPort(t){this._port=t;this._messageHandler=new _message_handler.MessageHandler("main","worker",t);this._messageHandler.on("ready",(function(){}));this._readyCapability.resolve()}_initialize(){if(!PDFWorkerUtil.isWorkerDisabled&&!PDFWorker._mainThreadWorkerMessageHandler){let{workerSrc:t}=PDFWorker;try{PDFWorkerUtil.isSameOrigin(window.location.href,t)||(t=PDFWorkerUtil.createCDNWrapper(new URL(t,window.location).href));const e=new Worker(t),s=new _message_handler.MessageHandler("main","worker",e),terminateEarly=()=>{e.removeEventListener("error",onWorkerError);s.destroy();e.terminate();this.destroyed?this._readyCapability.reject(new Error("Worker was destroyed")):this._setupFakeWorker()},onWorkerError=()=>{this._webWorker||terminateEarly()};e.addEventListener("error",onWorkerError);s.on("test",(t=>{e.removeEventListener("error",onWorkerError);if(this.destroyed)terminateEarly();else if(t){this._messageHandler=s;this._port=e;this._webWorker=e;this._readyCapability.resolve();s.send("configure",{verbosity:this.verbosity})}else{this._setupFakeWorker();s.destroy();e.terminate()}}));s.on("ready",(t=>{e.removeEventListener("error",onWorkerError);if(this.destroyed)terminateEarly();else try{sendTest()}catch(t){this._setupFakeWorker()}}));const sendTest=()=>{const t=new Uint8Array;s.send("test",t,[t.buffer])};sendTest();return}catch(t){(0,_util.info)("The worker has been disabled.")}}this._setupFakeWorker()}_setupFakeWorker(){if(!PDFWorkerUtil.isWorkerDisabled){(0,_util.warn)("Setting up fake worker.");PDFWorkerUtil.isWorkerDisabled=!0}PDFWorker._setupFakeWorkerGlobal.then((t=>{if(this.destroyed){this._readyCapability.reject(new Error("Worker was destroyed"));return}const e=new LoopbackPort;this._port=e;const s="fake"+PDFWorkerUtil.fakeWorkerId++,n=new _message_handler.MessageHandler(s+"_worker",s,e);t.setup(n,e);const i=new _message_handler.MessageHandler(s,s+"_worker",e);this._messageHandler=i;this._readyCapability.resolve();i.send("configure",{verbosity:this.verbosity})})).catch((t=>{this._readyCapability.reject(new Error(`Setting up fake worker failed: "${t.message}".`))}))}destroy(){this.destroyed=!0;if(this._webWorker){this._webWorker.terminate();this._webWorker=null}PDFWorker.#e.delete(this._port);this._port=null;if(this._messageHandler){this._messageHandler.destroy();this._messageHandler=null}}static fromPort(t){if(!t?.port)throw new Error("PDFWorker.fromPort - invalid method signature.");return this.#e.has(t.port)?this.#e.get(t.port):new PDFWorker(t)}static get workerSrc(){if(_worker_options.GlobalWorkerOptions.workerSrc)return _worker_options.GlobalWorkerOptions.workerSrc;if(null!==PDFWorkerUtil.fallbackWorkerSrc){_is_node.isNodeJS||(0,_display_utils.deprecated)('No "GlobalWorkerOptions.workerSrc" specified.');return PDFWorkerUtil.fallbackWorkerSrc}throw new Error('No "GlobalWorkerOptions.workerSrc" specified.')}static get _mainThreadWorkerMessageHandler(){try{return globalThis.pdfjsWorker?.WorkerMessageHandler||null}catch(t){return null}}static get _setupFakeWorkerGlobal(){const loader=async()=>{const mainWorkerMessageHandler=this._mainThreadWorkerMessageHandler;if(mainWorkerMessageHandler)return mainWorkerMessageHandler;if(_is_node.isNodeJS&&"function"==typeof require){const worker=eval("require")(this.workerSrc);return worker.WorkerMessageHandler}await(0,_display_utils.loadScript)(this.workerSrc);return window.pdfjsWorker.WorkerMessageHandler};return(0,_util.shadow)(this,"_setupFakeWorkerGlobal",loader())}}exports.PDFWorker=PDFWorker;PDFWorker.getWorkerSrc=function(){(0,_display_utils.deprecated)("`PDFWorker.getWorkerSrc()`, please use `PDFWorker.workerSrc` instead.");return this.workerSrc};class WorkerTransport{#s=null;#n=new Map;#i=new Map;#r=null;constructor(t,e,s,n){this.messageHandler=t;this.loadingTask=e;this.commonObjs=new PDFObjects;this.fontLoader=new _font_loader.FontLoader({docId:e.docId,onUnsupportedFeature:this._onUnsupportedFeature.bind(this),ownerDocument:n.ownerDocument,styleElement:n.styleElement});this._params=n;if(!n.useWorkerFetch){this.CMapReaderFactory=new n.CMapReaderFactory({baseUrl:n.cMapUrl,isCompressed:n.cMapPacked});this.StandardFontDataFactory=new n.StandardFontDataFactory({baseUrl:n.standardFontDataUrl})}this.destroyed=!1;this.destroyCapability=null;this._passwordCapability=null;this._networkStream=s;this._fullReader=null;this._lastProgress=null;this.downloadInfoCapability=(0,_util.createPromiseCapability)();this.setupMessageHandler()}get annotationStorage(){return(0,_util.shadow)(this,"annotationStorage",new _annotation_storage.AnnotationStorage)}get stats(){return this.#s}getRenderingIntent(t,e=_util.AnnotationMode.ENABLE,s=null,n=!1){let i=_util.RenderingIntentFlag.DISPLAY,r=null;switch(t){case"any":i=_util.RenderingIntentFlag.ANY;break;case"display":break;case"print":i=_util.RenderingIntentFlag.PRINT;break;default:(0,_util.warn)(`getRenderingIntent - invalid intent: ${t}`)}switch(e){case _util.AnnotationMode.DISABLE:i+=_util.RenderingIntentFlag.ANNOTATIONS_DISABLE;break;case _util.AnnotationMode.ENABLE:break;case _util.AnnotationMode.ENABLE_FORMS:i+=_util.RenderingIntentFlag.ANNOTATIONS_FORMS;break;case _util.AnnotationMode.ENABLE_STORAGE:i+=_util.RenderingIntentFlag.ANNOTATIONS_STORAGE;r=(i&_util.RenderingIntentFlag.PRINT&&s instanceof _annotation_storage.PrintAnnotationStorage?s:this.annotationStorage).serializable;break;default:(0,_util.warn)(`getRenderingIntent - invalid annotationMode: ${e}`)}n&&(i+=_util.RenderingIntentFlag.OPLIST);return{renderingIntent:i,cacheKey:`${i}_${_annotation_storage.AnnotationStorage.getHash(r)}`,annotationStorageMap:r}}destroy(){if(this.destroyCapability)return this.destroyCapability.promise;this.destroyed=!0;this.destroyCapability=(0,_util.createPromiseCapability)();this._passwordCapability&&this._passwordCapability.reject(new Error("Worker was destroyed during onPassword callback"));const t=[];for(const e of this.#n.values())t.push(e._destroy());this.#n.clear();this.#i.clear();this.hasOwnProperty("annotationStorage")&&this.annotationStorage.resetModified();const e=this.messageHandler.sendWithPromise("Terminate",null);t.push(e);Promise.all(t).then((()=>{this.commonObjs.clear();this.fontLoader.clear();this.#r=null;this._getFieldObjectsPromise=null;this._hasJSActionsPromise=null;this._networkStream&&this._networkStream.cancelAllRequests(new _util.AbortException("Worker was terminated."));if(this.messageHandler){this.messageHandler.destroy();this.messageHandler=null}this.destroyCapability.resolve()}),this.destroyCapability.reject);return this.destroyCapability.promise}setupMessageHandler(){const{messageHandler:t,loadingTask:e}=this;t.on("GetReader",((t,e)=>{(0,_util.assert)(this._networkStream,"GetReader - no `IPDFStream` instance available.");this._fullReader=this._networkStream.getFullReader();this._fullReader.onProgress=t=>{this._lastProgress={loaded:t.loaded,total:t.total}};e.onPull=()=>{this._fullReader.read().then((function({value:t,done:s}){if(s)e.close();else{(0,_util.assert)((0,_util.isArrayBuffer)(t),"GetReader - expected an ArrayBuffer.");e.enqueue(new Uint8Array(t),1,[t])}})).catch((t=>{e.error(t)}))};e.onCancel=t=>{this._fullReader.cancel(t);e.ready.catch((t=>{if(!this.destroyed)throw t}))}}));t.on("ReaderHeadersReady",(t=>{const s=(0,_util.createPromiseCapability)(),n=this._fullReader;n.headersReady.then((()=>{if(!n.isStreamingSupported||!n.isRangeSupported){this._lastProgress&&e.onProgress?.(this._lastProgress);n.onProgress=t=>{e.onProgress?.({loaded:t.loaded,total:t.total})}}s.resolve({isStreamingSupported:n.isStreamingSupported,isRangeSupported:n.isRangeSupported,contentLength:n.contentLength})}),s.reject);return s.promise}));t.on("GetRangeReader",((t,e)=>{(0,_util.assert)(this._networkStream,"GetRangeReader - no `IPDFStream` instance available.");const s=this._networkStream.getRangeReader(t.begin,t.end);if(s){e.onPull=()=>{s.read().then((function({value:t,done:s}){if(s)e.close();else{(0,_util.assert)((0,_util.isArrayBuffer)(t),"GetRangeReader - expected an ArrayBuffer.");e.enqueue(new Uint8Array(t),1,[t])}})).catch((t=>{e.error(t)}))};e.onCancel=t=>{s.cancel(t);e.ready.catch((t=>{if(!this.destroyed)throw t}))}}else e.close()}));t.on("GetDoc",(({pdfInfo:t})=>{this._numPages=t.numPages;this._htmlForXfa=t.htmlForXfa;delete t.htmlForXfa;e._capability.resolve(new PDFDocumentProxy(t,this))}));t.on("DocException",(function(t){let s;switch(t.name){case"PasswordException":s=new _util.PasswordException(t.message,t.code);break;case"InvalidPDFException":s=new _util.InvalidPDFException(t.message);break;case"MissingPDFException":s=new _util.MissingPDFException(t.message);break;case"UnexpectedResponseException":s=new _util.UnexpectedResponseException(t.message,t.status);break;case"UnknownErrorException":s=new _util.UnknownErrorException(t.message,t.details);break;default:(0,_util.unreachable)("DocException - expected a valid Error.")}e._capability.reject(s)}));t.on("PasswordRequest",(t=>{this._passwordCapability=(0,_util.createPromiseCapability)();if(e.onPassword){const updatePassword=t=>{t instanceof Error?this._passwordCapability.reject(t):this._passwordCapability.resolve({password:t})};try{e.onPassword(updatePassword,t.code)}catch(t){this._passwordCapability.reject(t)}}else this._passwordCapability.reject(new _util.PasswordException(t.message,t.code));return this._passwordCapability.promise}));t.on("DataLoaded",(t=>{e.onProgress?.({loaded:t.length,total:t.length});this.downloadInfoCapability.resolve(t)}));t.on("StartRenderPage",(t=>{if(this.destroyed)return;this.#n.get(t.pageIndex)._startRenderPage(t.transparency,t.cacheKey)}));t.on("commonobj",(([e,s,n])=>{if(!this.destroyed&&!this.commonObjs.has(e))switch(s){case"Font":const i=this._params;if("error"in n){const t=n.error;(0,_util.warn)(`Error during font loading: ${t}`);this.commonObjs.resolve(e,t);break}let r=null;i.pdfBug&&globalThis.FontInspector?.enabled&&(r={registerFont(t,e){globalThis.FontInspector.fontAdded(t,e)}});const a=new _font_loader.FontFaceObject(n,{isEvalSupported:i.isEvalSupported,disableFontFace:i.disableFontFace,ignoreErrors:i.ignoreErrors,onUnsupportedFeature:this._onUnsupportedFeature.bind(this),fontRegistry:r});this.fontLoader.bind(a).catch((s=>t.sendWithPromise("FontFallback",{id:e}))).finally((()=>{!i.fontExtraProperties&&a.data&&(a.data=null);this.commonObjs.resolve(e,a)}));break;case"FontPath":case"Image":this.commonObjs.resolve(e,n);break;default:throw new Error(`Got unknown common object type ${s}`)}}));t.on("obj",(([t,e,s,n])=>{if(this.destroyed)return;const i=this.#n.get(e);if(!i.objs.has(t))switch(s){case"Image":i.objs.resolve(t,n);const e=8e6;if(n){let t;if(n.bitmap){const{bitmap:e,width:s,height:r}=n;t=s*r*4;i._bitmaps.add(e)}else t=n.data?.length||0;t>e&&(i.cleanupAfterRender=!0)}break;case"Pattern":i.objs.resolve(t,n);break;default:throw new Error(`Got unknown object type ${s}`)}}));t.on("DocProgress",(t=>{this.destroyed||e.onProgress?.({loaded:t.loaded,total:t.total})}));t.on("DocStats",(t=>{this.destroyed||(this.#s=Object.freeze({streamTypes:Object.freeze(t.streamTypes),fontTypes:Object.freeze(t.fontTypes)}))}));t.on("UnsupportedFeature",this._onUnsupportedFeature.bind(this));t.on("FetchBuiltInCMap",(t=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.CMapReaderFactory?this.CMapReaderFactory.fetch(t):Promise.reject(new Error("CMapReaderFactory not initialized, see the `useWorkerFetch` parameter."))));t.on("FetchStandardFontData",(t=>this.destroyed?Promise.reject(new Error("Worker was destroyed.")):this.StandardFontDataFactory?this.StandardFontDataFactory.fetch(t):Promise.reject(new Error("StandardFontDataFactory not initialized, see the `useWorkerFetch` parameter."))))}_onUnsupportedFeature({featureId:t}){this.destroyed||this.loadingTask.onUnsupportedFeature?.(t)}getData(){return this.messageHandler.sendWithPromise("GetData",null)}getPage(t){if(!Number.isInteger(t)||t<=0||t>this._numPages)return Promise.reject(new Error("Invalid page request."));const e=t-1,s=this.#i.get(e);if(s)return s;const n=this.messageHandler.sendWithPromise("GetPage",{pageIndex:e}).then((t=>{if(this.destroyed)throw new Error("Transport destroyed");const s=new PDFPageProxy(e,t,this,this._params.ownerDocument,this._params.pdfBug);this.#n.set(e,s);return s}));this.#i.set(e,n);return n}getPageIndex(t){return"object"!=typeof t||null===t||!Number.isInteger(t.num)||t.num<0||!Number.isInteger(t.gen)||t.gen<0?Promise.reject(new Error("Invalid pageIndex request.")):this.messageHandler.sendWithPromise("GetPageIndex",{num:t.num,gen:t.gen})}getAnnotations(t,e){return this.messageHandler.sendWithPromise("GetAnnotations",{pageIndex:t,intent:e})}saveDocument(){return this.messageHandler.sendWithPromise("SaveDocument",{isPureXfa:!!this._htmlForXfa,numPages:this._numPages,annotationStorage:this.annotationStorage.serializable,filename:this._fullReader?.filename??null}).finally((()=>{this.annotationStorage.resetModified()}))}getFieldObjects(){return this._getFieldObjectsPromise||=this.messageHandler.sendWithPromise("GetFieldObjects",null)}hasJSActions(){return this._hasJSActionsPromise||=this.messageHandler.sendWithPromise("HasJSActions",null)}getCalculationOrderIds(){return this.messageHandler.sendWithPromise("GetCalculationOrderIds",null)}getDestinations(){return this.messageHandler.sendWithPromise("GetDestinations",null)}getDestination(t){return"string"!=typeof t?Promise.reject(new Error("Invalid destination request.")):this.messageHandler.sendWithPromise("GetDestination",{id:t})}getPageLabels(){return this.messageHandler.sendWithPromise("GetPageLabels",null)}getPageLayout(){return this.messageHandler.sendWithPromise("GetPageLayout",null)}getPageMode(){return this.messageHandler.sendWithPromise("GetPageMode",null)}getViewerPreferences(){return this.messageHandler.sendWithPromise("GetViewerPreferences",null)}getOpenAction(){return this.messageHandler.sendWithPromise("GetOpenAction",null)}getAttachments(){return this.messageHandler.sendWithPromise("GetAttachments",null)}getJavaScript(){return this.messageHandler.sendWithPromise("GetJavaScript",null)}getDocJSActions(){return this.messageHandler.sendWithPromise("GetDocJSActions",null)}getPageJSActions(t){return this.messageHandler.sendWithPromise("GetPageJSActions",{pageIndex:t})}getStructTree(t){return this.messageHandler.sendWithPromise("GetStructTree",{pageIndex:t})}getOutline(){return this.messageHandler.sendWithPromise("GetOutline",null)}getOptionalContentConfig(){return this.messageHandler.sendWithPromise("GetOptionalContentConfig",null).then((t=>new _optional_content_config.OptionalContentConfig(t)))}getPermissions(){return this.messageHandler.sendWithPromise("GetPermissions",null)}getMetadata(){return this.#r||=this.messageHandler.sendWithPromise("GetMetadata",null).then((t=>({info:t[0],metadata:t[1]?new _metadata.Metadata(t[1]):null,contentDispositionFilename:this._fullReader?.filename??null,contentLength:this._fullReader?.contentLength??null})))}getMarkInfo(){return this.messageHandler.sendWithPromise("GetMarkInfo",null)}async startCleanup(t=!1){await this.messageHandler.sendWithPromise("Cleanup",null);if(!this.destroyed){for(const t of this.#n.values()){if(!t.cleanup())throw new Error(`startCleanup: Page ${t.pageNumber} is currently rendering.`)}this.commonObjs.clear();t||this.fontLoader.clear();this.#r=null;this._getFieldObjectsPromise=null;this._hasJSActionsPromise=null}}get loadingParams(){const t=this._params;return(0,_util.shadow)(this,"loadingParams",{disableAutoFetch:t.disableAutoFetch,enableXfa:t.enableXfa})}}class PDFObjects{#a=Object.create(null);#o(t){const e=this.#a[t];return e||(this.#a[t]={capability:(0,_util.createPromiseCapability)(),data:null})}get(t,e=null){if(e){const s=this.#o(t);s.capability.promise.then((()=>e(s.data)));return null}const s=this.#a[t];if(!s?.capability.settled)throw new Error(`Requesting object that isn't resolved yet ${t}.`);return s.data}has(t){return this.#a[t]?.capability.settled||!1}resolve(t,e=null){const s=this.#o(t);s.data=e;s.capability.resolve()}clear(){this.#a=Object.create(null)}}class RenderTask{#l=null;constructor(t){this.#l=t;this.onContinue=null}get promise(){return this.#l.capability.promise}cancel(){this.#l.cancel()}get separateAnnots(){const{separateAnnots:t}=this.#l.operatorList;if(!t)return!1;const{annotationCanvasMap:e}=this.#l;return t.form||t.canvas&&e?.size>0}}exports.RenderTask=RenderTask;class InternalRenderTask{static#c=new WeakSet;constructor({callback:t,params:e,objs:s,commonObjs:n,annotationCanvasMap:i,operatorList:r,pageIndex:a,canvasFactory:o,useRequestAnimationFrame:l=!1,pdfBug:c=!1,pageColors:h=null}){this.callback=t;this.params=e;this.objs=s;this.commonObjs=n;this.annotationCanvasMap=i;this.operatorListIdx=null;this.operatorList=r;this._pageIndex=a;this.canvasFactory=o;this._pdfBug=c;this.pageColors=h;this.running=!1;this.graphicsReadyCallback=null;this.graphicsReady=!1;this._useRequestAnimationFrame=!0===l&&"undefined"!=typeof window;this.cancelled=!1;this.capability=(0,_util.createPromiseCapability)();this.task=new RenderTask(this);this._cancelBound=this.cancel.bind(this);this._continueBound=this._continue.bind(this);this._scheduleNextBound=this._scheduleNext.bind(this);this._nextBound=this._next.bind(this);this._canvas=e.canvasContext.canvas}get completed(){return this.capability.promise.catch((function(){}))}initializeGraphics({transparency:t=!1,optionalContentConfig:e}){if(this.cancelled)return;if(this._canvas){if(InternalRenderTask.#c.has(this._canvas))throw new Error("Cannot use the same canvas during multiple render() operations. Use different canvas or ensure previous operations were cancelled or completed.");InternalRenderTask.#c.add(this._canvas)}if(this._pdfBug&&globalThis.StepperManager?.enabled){this.stepper=globalThis.StepperManager.create(this._pageIndex);this.stepper.init(this.operatorList);this.stepper.nextBreakPoint=this.stepper.getNextBreakPoint()}const{canvasContext:s,viewport:n,transform:i,imageLayer:r,background:a}=this.params;this.gfx=new _canvas.CanvasGraphics(s,this.commonObjs,this.objs,this.canvasFactory,r,e,this.annotationCanvasMap,this.pageColors);this.gfx.beginDrawing({transform:i,viewport:n,transparency:t,background:a});this.operatorListIdx=0;this.graphicsReady=!0;this.graphicsReadyCallback&&this.graphicsReadyCallback()}cancel(t=null){this.running=!1;this.cancelled=!0;this.gfx&&this.gfx.endDrawing();this._canvas&&InternalRenderTask.#c.delete(this._canvas);this.callback(t||new _display_utils.RenderingCancelledException(`Rendering cancelled, page ${this._pageIndex+1}`,"canvas"))}operatorListChanged(){if(this.graphicsReady){this.stepper&&this.stepper.updateOperatorList(this.operatorList);this.running||this._continue()}else this.graphicsReadyCallback||(this.graphicsReadyCallback=this._continueBound)}_continue(){this.running=!0;this.cancelled||(this.task.onContinue?this.task.onContinue(this._scheduleNextBound):this._scheduleNext())}_scheduleNext(){this._useRequestAnimationFrame?window.requestAnimationFrame((()=>{this._nextBound().catch(this._cancelBound)})):Promise.resolve().then(this._nextBound).catch(this._cancelBound)}async _next(){if(!this.cancelled){this.operatorListIdx=this.gfx.executeOperatorList(this.operatorList,this.operatorListIdx,this._continueBound,this.stepper);if(this.operatorListIdx===this.operatorList.argsArray.length){this.running=!1;if(this.operatorList.lastChunk){this.gfx.endDrawing();this._canvas&&InternalRenderTask.#c.delete(this._canvas);this.callback()}}}}}const version="2.16.105";exports.version=version;const build="172ccdbe5";exports.build=build},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.PrintAnnotationStorage=e.AnnotationStorage=void 0;var n=s(1),i=s(6),r=s(10);class AnnotationStorage{constructor(){this._storage=new Map;this._modified=!1;this.onSetModified=null;this.onResetModified=null;this.onAnnotationEditor=null}getValue(t,e){const s=this._storage.get(t);return void 0===s?e:Object.assign(e,s)}getRawValue(t){return this._storage.get(t)}remove(t){this._storage.delete(t);0===this._storage.size&&this.resetModified();if("function"==typeof this.onAnnotationEditor){for(const t of this._storage.values())if(t instanceof i.AnnotationEditor)return;this.onAnnotationEditor(null)}}setValue(t,e){const s=this._storage.get(t);let n=!1;if(void 0!==s){for(const[t,i]of Object.entries(e))if(s[t]!==i){n=!0;s[t]=i}}else{n=!0;this._storage.set(t,e)}n&&this.#h();e instanceof i.AnnotationEditor&&"function"==typeof this.onAnnotationEditor&&this.onAnnotationEditor(e.constructor._type)}has(t){return this._storage.has(t)}getAll(){return this._storage.size>0?(0,n.objectFromMap)(this._storage):null}get size(){return this._storage.size}#h(){if(!this._modified){this._modified=!0;"function"==typeof this.onSetModified&&this.onSetModified()}}resetModified(){if(this._modified){this._modified=!1;"function"==typeof this.onResetModified&&this.onResetModified()}}get print(){return new PrintAnnotationStorage(this)}get serializable(){if(0===this._storage.size)return null;const t=new Map;for(const[e,s]of this._storage){const n=s instanceof i.AnnotationEditor?s.serialize():s;n&&t.set(e,n)}return t}static getHash(t){if(!t)return"";const e=new r.MurmurHash3_64;for(const[s,n]of t)e.update(`${s}:${JSON.stringify(n)}`);return e.hexdigest()}}e.AnnotationStorage=AnnotationStorage;class PrintAnnotationStorage extends AnnotationStorage{#d=null;constructor(t){super();this.#d=structuredClone(t.serializable)}get print(){(0,n.unreachable)("Should not call PrintAnnotationStorage.print")}get serializable(){return this.#d}}e.PrintAnnotationStorage=PrintAnnotationStorage},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.AnnotationEditor=void 0;var n=s(7),i=s(1);class AnnotationEditor{#u=this.focusin.bind(this);#p=this.focusout.bind(this);#g=!1;#f=!1;#m=!1;#b=AnnotationEditor._zIndex++;static _colorManager=new n.ColorManager;static _zIndex=1;constructor(t){this.constructor===AnnotationEditor&&(0,i.unreachable)("Cannot initialize AnnotationEditor.");this.parent=t.parent;this.id=t.id;this.width=this.height=null;this.pageIndex=t.parent.pageIndex;this.name=t.name;this.div=null;const[e,s]=this.parent.viewportBaseDimensions;this.x=t.x/e;this.y=t.y/s;this.rotation=this.parent.viewport.rotation;this.isAttachedToDOM=!1}static get _defaultLineColor(){return(0,i.shadow)(this,"_defaultLineColor",this._colorManager.getHexCode("CanvasText"))}setInBackground(){this.div.style.zIndex=0}setInForeground(){this.div.style.zIndex=this.#b}focusin(t){this.#g?this.#g=!1:this.parent.setSelected(this)}focusout(t){if(!this.isAttachedToDOM)return;if(!t.relatedTarget?.closest(`#${this.id}`)){t.preventDefault();this.parent.isMultipleSelection||this.commitOrRemove()}}commitOrRemove(){this.isEmpty()?this.remove():this.commit()}commit(){this.parent.addToAnnotationStorage(this)}dragstart(t){const e=this.parent.div.getBoundingClientRect();this.startX=t.clientX-e.x;this.startY=t.clientY-e.y;t.dataTransfer.setData("text/plain",this.id);t.dataTransfer.effectAllowed="move"}setAt(t,e,s,n){const[i,r]=this.parent.viewportBaseDimensions;[s,n]=this.screenToPageTranslation(s,n);this.x=(t+s)/i;this.y=(e+n)/r;this.div.style.left=100*this.x+"%";this.div.style.top=100*this.y+"%"}translate(t,e){const[s,n]=this.parent.viewportBaseDimensions;[t,e]=this.screenToPageTranslation(t,e);this.x+=t/s;this.y+=e/n;this.div.style.left=100*this.x+"%";this.div.style.top=100*this.y+"%"}screenToPageTranslation(t,e){const{rotation:s}=this.parent.viewport;switch(s){case 90:return[e,-t];case 180:return[-t,-e];case 270:return[-e,t];default:return[t,e]}}setDims(t,e){const[s,n]=this.parent.viewportBaseDimensions;this.div.style.width=100*t/s+"%";this.div.style.height=100*e/n+"%"}getInitialTranslation(){return[0,0]}render(){this.div=document.createElement("div");this.div.setAttribute("data-editor-rotation",(360-this.rotation)%360);this.div.className=this.name;this.div.setAttribute("id",this.id);this.div.setAttribute("tabIndex",0);this.setInForeground();this.div.addEventListener("focusin",this.#u);this.div.addEventListener("focusout",this.#p);const[t,e]=this.getInitialTranslation();this.translate(t,e);(0,n.bindEvents)(this,this.div,["dragstart","pointerdown"]);return this.div}pointerdown(t){const e=n.KeyboardManager.platform.isMac;if(0!==t.button||t.ctrlKey&&e)t.preventDefault();else{t.ctrlKey&&!e||t.shiftKey||t.metaKey&&e?this.parent.toggleSelected(this):this.parent.setSelected(this);this.#g=!0}}getRect(t,e){const[s,n]=this.parent.viewportBaseDimensions,[i,r]=this.parent.pageDimensions,a=i*t/s,o=r*e/n,l=this.x*i,c=this.y*r,h=this.width*i,d=this.height*r;switch(this.rotation){case 0:return[l+a,r-c-o-d,l+a+h,r-c-o];case 90:return[l+o,r-c+a,l+o+d,r-c+a+h];case 180:return[l-a-h,r-c+o,l-a,r-c+o+d];case 270:return[l-o-d,r-c-a-h,l-o,r-c-a];default:throw new Error("Invalid rotation")}}getRectInCurrentCoords(t,e){const[s,n,i,r]=t,a=i-s,o=r-n;switch(this.rotation){case 0:return[s,e-r,a,o];case 90:return[s,e-n,o,a];case 180:return[i,e-n,a,o];case 270:return[i,e-r,o,a];default:throw new Error("Invalid rotation")}}onceAdded(){}isEmpty(){return!1}enableEditMode(){this.#m=!0}disableEditMode(){this.#m=!1}isInEditMode(){return this.#m}shouldGetKeyboardEvents(){return!1}needsToBeRebuilt(){return this.div&&!this.isAttachedToDOM}rebuild(){this.div?.addEventListener("focusin",this.#u)}serialize(){(0,i.unreachable)("An editor must be serializable")}static deserialize(t,e){const s=new this.prototype.constructor({parent:e,id:e.getNextId()});s.rotation=t.rotation;const[n,i]=e.pageDimensions,[r,a,o,l]=s.getRectInCurrentCoords(t.rect,i);s.x=r/n;s.y=a/i;s.width=o/n;s.height=l/i;return s}remove(){this.div.removeEventListener("focusin",this.#u);this.div.removeEventListener("focusout",this.#p);this.isEmpty()||this.commit();this.parent.remove(this)}select(){this.div?.classList.add("selectedEditor")}unselect(){this.div?.classList.remove("selectedEditor")}updateParams(t,e){}disableEditing(){}enableEditing(){}get propertiesToUpdate(){return{}}get contentDiv(){return this.div}get isEditing(){return this.#f}set isEditing(t){this.#f=t;if(t){this.parent.setSelected(this);this.parent.setActiveEditor(this)}else this.parent.setActiveEditor(null)}}e.AnnotationEditor=AnnotationEditor},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.KeyboardManager=e.CommandManager=e.ColorManager=e.AnnotationEditorUIManager=void 0;e.bindEvents=function bindEvents(t,e,s){for(const n of s)e.addEventListener(n,t[n].bind(t))};e.opacityToHex=function opacityToHex(t){return Math.round(Math.min(255,Math.max(1,255*t))).toString(16).padStart(2,"0")};var n=s(1),i=s(8);class IdManager{#A=0;getId(){return`${n.AnnotationEditorPrefix}${this.#A++}`}}class CommandManager{#_=[];#y=!1;#v;#S=-1;constructor(t=128){this.#v=t}add({cmd:t,undo:e,mustExec:s,type:n=NaN,overwriteIfSameType:i=!1,keepUndo:r=!1}){s&&t();if(this.#y)return;const a={cmd:t,undo:e,type:n};if(-1===this.#S){this.#_.length>0&&(this.#_.length=0);this.#S=0;this.#_.push(a);return}if(i&&this.#_[this.#S].type===n){r&&(a.undo=this.#_[this.#S].undo);this.#_[this.#S]=a;return}const o=this.#S+1;if(o===this.#v)this.#_.splice(0,1);else{this.#S=o;o<this.#_.length&&this.#_.splice(o)}this.#_.push(a)}undo(){if(-1!==this.#S){this.#y=!0;this.#_[this.#S].undo();this.#y=!1;this.#S-=1}}redo(){if(this.#S<this.#_.length-1){this.#S+=1;this.#y=!0;this.#_[this.#S].cmd();this.#y=!1}}hasSomethingToUndo(){return-1!==this.#S}hasSomethingToRedo(){return this.#S<this.#_.length-1}destroy(){this.#_=null}}e.CommandManager=CommandManager;class KeyboardManager{constructor(t){this.buffer=[];this.callbacks=new Map;this.allKeys=new Set;const e=KeyboardManager.platform.isMac;for(const[s,n]of t)for(const t of s){const s=t.startsWith("mac+");if(e&&s){this.callbacks.set(t.slice(4),n);this.allKeys.add(t.split("+").at(-1))}else if(!e&&!s){this.callbacks.set(t,n);this.allKeys.add(t.split("+").at(-1))}}}static get platform(){const t="undefined"!=typeof navigator?navigator.platform:"";return(0,n.shadow)(this,"platform",{isWin:t.includes("Win"),isMac:t.includes("Mac")})}#x(t){t.altKey&&this.buffer.push("alt");t.ctrlKey&&this.buffer.push("ctrl");t.metaKey&&this.buffer.push("meta");t.shiftKey&&this.buffer.push("shift");this.buffer.push(t.key);const e=this.buffer.join("+");this.buffer.length=0;return e}exec(t,e){if(!this.allKeys.has(e.key))return;const s=this.callbacks.get(this.#x(e));if(s){s.bind(t)();e.stopPropagation();e.preventDefault()}}}e.KeyboardManager=KeyboardManager;class ClipboardManager{#E=null;copy(t){if(t){Array.isArray(t)?this.#E=t.map((t=>t.serialize())):this.#E=[t.serialize()];this.#E=this.#E.filter((t=>!!t));0===this.#E.length&&(this.#E=null)}}paste(){return this.#E}isEmpty(){return null===this.#E}destroy(){this.#E=null}}class ColorManager{static _colorsMapping=new Map([["CanvasText",[0,0,0]],["Canvas",[255,255,255]]]);get _colors(){const t=new Map([["CanvasText",null],["Canvas",null]]);(0,i.getColorValues)(t);return(0,n.shadow)(this,"_colors",t)}convert(t){const e=(0,i.getRGB)(t);if(!window.matchMedia("(forced-colors: active)").matches)return e;for(const[t,s]of this._colors)if(s.every(((t,s)=>t===e[s])))return ColorManager._colorsMapping.get(t);return e}getHexCode(t){const e=this._colors.get(t);return e?n.Util.makeHexColor(...e):t}}e.ColorManager=ColorManager;class AnnotationEditorUIManager{#C=null;#P=new Map;#T=new Map;#w=new ClipboardManager;#k=new CommandManager;#F=0;#M=null;#R=null;#D=new IdManager;#I=!1;#O=n.AnnotationEditorType.NONE;#L=new Set;#N=this.keydown.bind(this);#B=this.onEditingAction.bind(this);#j=this.onPageChanging.bind(this);#U={isEditing:!1,isEmpty:!0,hasEmptyClipboard:!0,hasSomethingToUndo:!1,hasSomethingToRedo:!1,hasSelectedEditor:!1};#q=null;static _keyboardManager=new KeyboardManager([[["ctrl+a","mac+meta+a"],AnnotationEditorUIManager.prototype.selectAll],[["ctrl+c","mac+meta+c"],AnnotationEditorUIManager.prototype.copy],[["ctrl+v","mac+meta+v"],AnnotationEditorUIManager.prototype.paste],[["ctrl+x","mac+meta+x"],AnnotationEditorUIManager.prototype.cut],[["ctrl+z","mac+meta+z"],AnnotationEditorUIManager.prototype.undo],[["ctrl+y","ctrl+shift+Z","mac+meta+shift+Z"],AnnotationEditorUIManager.prototype.redo],[["Backspace","alt+Backspace","ctrl+Backspace","shift+Backspace","mac+Backspace","mac+alt+Backspace","mac+ctrl+Backspace","Delete","ctrl+Delete","shift+Delete"],AnnotationEditorUIManager.prototype.delete],[["Escape","mac+Escape"],AnnotationEditorUIManager.prototype.unselectAll]]);constructor(t,e){this.#q=t;this.#R=e;this.#R._on("editingaction",this.#B);this.#R._on("pagechanging",this.#j)}destroy(){this.#W();this.#R._off("editingaction",this.#B);this.#R._off("pagechanging",this.#j);for(const t of this.#T.values())t.destroy();this.#T.clear();this.#P.clear();this.#C=null;this.#L.clear();this.#w.destroy();this.#k.destroy()}onPageChanging({pageNumber:t}){this.#F=t-1}focusMainContainer(){this.#q.focus()}#z(){this.#q.addEventListener("keydown",this.#N)}#W(){this.#q.removeEventListener("keydown",this.#N)}keydown(t){this.getActive()?.shouldGetKeyboardEvents()||AnnotationEditorUIManager._keyboardManager.exec(this,t)}onEditingAction(t){["undo","redo","cut","copy","paste","delete","selectAll"].includes(t.name)&&this[t.name]()}#G(t){Object.entries(t).some((([t,e])=>this.#U[t]!==e))&&this.#R.dispatch("annotationeditorstateschanged",{source:this,details:Object.assign(this.#U,t)})}#H(t){this.#R.dispatch("annotationeditorparamschanged",{source:this,details:t})}setEditingState(t){if(t){this.#z();this.#G({isEditing:this.#O!==n.AnnotationEditorType.NONE,isEmpty:this.#V(),hasSomethingToUndo:this.#k.hasSomethingToUndo(),hasSomethingToRedo:this.#k.hasSomethingToRedo(),hasSelectedEditor:!1,hasEmptyClipboard:this.#w.isEmpty()})}else{this.#W();this.#G({isEditing:!1})}}registerEditorTypes(t){this.#M=t;for(const t of this.#M)this.#H(t.defaultPropertiesToUpdate)}getId(){return this.#D.getId()}addLayer(t){this.#T.set(t.pageIndex,t);this.#I?t.enable():t.disable()}removeLayer(t){this.#T.delete(t.pageIndex)}updateMode(t){this.#O=t;if(t===n.AnnotationEditorType.NONE){this.setEditingState(!1);this.#X()}else{this.setEditingState(!0);this.#$();for(const e of this.#T.values())e.updateMode(t)}}updateToolbar(t){t!==this.#O&&this.#R.dispatch("switchannotationeditormode",{source:this,mode:t})}updateParams(t,e){for(const s of this.#L)s.updateParams(t,e);for(const s of this.#M)s.updateDefaultParams(t,e)}#$(){if(!this.#I){this.#I=!0;for(const t of this.#T.values())t.enable()}}#X(){this.unselectAll();if(this.#I){this.#I=!1;for(const t of this.#T.values())t.disable()}}getEditors(t){const e=[];for(const s of this.#P.values())s.pageIndex===t&&e.push(s);return e}getEditor(t){return this.#P.get(t)}addEditor(t){this.#P.set(t.id,t)}removeEditor(t){this.#P.delete(t.id);this.unselect(t)}#Y(t){const e=this.#T.get(t.pageIndex);e?e.addOrRebuild(t):this.addEditor(t)}setActiveEditor(t){if(this.#C!==t){this.#C=t;t&&this.#H(t.propertiesToUpdate)}}toggleSelected(t){if(this.#L.has(t)){this.#L.delete(t);t.unselect();this.#G({hasSelectedEditor:this.hasSelection})}else{this.#L.add(t);t.select();this.#H(t.propertiesToUpdate);this.#G({hasSelectedEditor:!0})}}setSelected(t){for(const e of this.#L)e!==t&&e.unselect();this.#L.clear();this.#L.add(t);t.select();this.#H(t.propertiesToUpdate);this.#G({hasSelectedEditor:!0})}isSelected(t){return this.#L.has(t)}unselect(t){t.unselect();this.#L.delete(t);this.#G({hasSelectedEditor:this.hasSelection})}get hasSelection(){return 0!==this.#L.size}undo(){this.#k.undo();this.#G({hasSomethingToUndo:this.#k.hasSomethingToUndo(),hasSomethingToRedo:!0,isEmpty:this.#V()})}redo(){this.#k.redo();this.#G({hasSomethingToUndo:!0,hasSomethingToRedo:this.#k.hasSomethingToRedo(),isEmpty:this.#V()})}addCommands(t){this.#k.add(t);this.#G({hasSomethingToUndo:!0,hasSomethingToRedo:!1,isEmpty:this.#V()})}#V(){if(0===this.#P.size)return!0;if(1===this.#P.size)for(const t of this.#P.values())return t.isEmpty();return!1}delete(){this.#C&&this.#C.commitOrRemove();if(!this.hasSelection)return;const t=[...this.#L];this.addCommands({cmd:()=>{for(const e of t)e.remove()},undo:()=>{for(const e of t)this.#Y(e)},mustExec:!0})}copy(){this.#C&&this.#C.commitOrRemove();if(this.hasSelection){const t=[];for(const e of this.#L)e.isEmpty()||t.push(e);if(0===t.length)return;this.#w.copy(t);this.#G({hasEmptyClipboard:!1})}}cut(){this.copy();this.delete()}paste(){if(this.#w.isEmpty())return;this.unselectAll();const t=this.#T.get(this.#F),e=this.#w.paste().map((e=>t.deserialize(e)));this.addCommands({cmd:()=>{for(const t of e)this.#Y(t);this.#K(e)},undo:()=>{for(const t of e)t.remove()},mustExec:!0})}#K(t){this.#L.clear();for(const e of t)if(!e.isEmpty()){this.#L.add(e);e.select()}this.#G({hasSelectedEditor:!0})}selectAll(){for(const t of this.#L)t.commit();this.#K(this.#P.values())}unselectAll(){if(this.#C)this.#C.commitOrRemove();else if(0!==this.#K.size){for(const t of this.#L)t.unselect();this.#L.clear();this.#G({hasSelectedEditor:!1})}}isActive(t){return this.#C===t}getActive(){return this.#C}getMode(){return this.#O}}e.AnnotationEditorUIManager=AnnotationEditorUIManager},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.StatTimer=e.RenderingCancelledException=e.PixelsPerInch=e.PageViewport=e.PDFDateString=e.DOMStandardFontDataFactory=e.DOMSVGFactory=e.DOMCanvasFactory=e.DOMCMapReaderFactory=e.AnnotationPrefix=void 0;e.deprecated=function deprecated(t){console.log("Deprecated API usage: "+t)};e.getColorValues=function getColorValues(t){const e=document.createElement("span");e.style.visibility="hidden";document.body.append(e);for(const s of t.keys()){e.style.color=s;const n=window.getComputedStyle(e).color;t.set(s,getRGB(n))}e.remove()};e.getCurrentTransform=function getCurrentTransform(t){const{a:e,b:s,c:n,d:i,e:r,f:a}=t.getTransform();return[e,s,n,i,r,a]};e.getCurrentTransformInverse=function getCurrentTransformInverse(t){const{a:e,b:s,c:n,d:i,e:r,f:a}=t.getTransform().invertSelf();return[e,s,n,i,r,a]};e.getFilenameFromUrl=function getFilenameFromUrl(t){const e=t.indexOf("#"),s=t.indexOf("?"),n=Math.min(e>0?e:t.length,s>0?s:t.length);return t.substring(t.lastIndexOf("/",n)+1,n)};e.getPdfFilenameFromUrl=function getPdfFilenameFromUrl(t,e="document.pdf"){if("string"!=typeof t)return e;if(isDataScheme(t)){(0,i.warn)('getPdfFilenameFromUrl: ignore "data:"-URL for performance reasons.');return e}const s=/[^/?#=]+\.pdf\b(?!.*\.pdf\b)/i,n=/^(?:(?:[^:]+:)?\/\/[^/]+)?([^?#]*)(\?[^#]*)?(#.*)?$/.exec(t);let r=s.exec(n[1])||s.exec(n[2])||s.exec(n[3]);if(r){r=r[0];if(r.includes("%"))try{r=s.exec(decodeURIComponent(r))[0]}catch(t){}}return r||e};e.getRGB=getRGB;e.getXfaPageViewport=function getXfaPageViewport(t,{scale:e=1,rotation:s=0}){const{width:n,height:i}=t.attributes.style,r=[0,0,parseInt(n),parseInt(i)];return new PageViewport({viewBox:r,scale:e,rotation:s})};e.isDataScheme=isDataScheme;e.isPdfFile=function isPdfFile(t){return"string"==typeof t&&/\.pdf$/i.test(t)};e.isValidFetchUrl=isValidFetchUrl;e.loadScript=function loadScript(t,e=!1){return new Promise(((s,n)=>{const i=document.createElement("script");i.src=t;i.onload=function(t){e&&i.remove();s(t)};i.onerror=function(){n(new Error(`Cannot load script at: ${i.src}`))};(document.head||document.documentElement).append(i)}))};var n=s(9),i=s(1);e.AnnotationPrefix="pdfjs_internal_id_";class PixelsPerInch{static CSS=96;static PDF=72;static PDF_TO_CSS_UNITS=this.CSS/this.PDF}e.PixelsPerInch=PixelsPerInch;class DOMCanvasFactory extends n.BaseCanvasFactory{constructor({ownerDocument:t=globalThis.document}={}){super();this._document=t}_createCanvas(t,e){const s=this._document.createElement("canvas");s.width=t;s.height=e;return s}}e.DOMCanvasFactory=DOMCanvasFactory;async function fetchData(t,e=!1){if(isValidFetchUrl(t,document.baseURI)){const s=await fetch(t);if(!s.ok)throw new Error(s.statusText);return e?new Uint8Array(await s.arrayBuffer()):(0,i.stringToBytes)(await s.text())}return new Promise(((s,n)=>{const r=new XMLHttpRequest;r.open("GET",t,!0);e&&(r.responseType="arraybuffer");r.onreadystatechange=()=>{if(r.readyState===XMLHttpRequest.DONE){if(200===r.status||0===r.status){let t;e&&r.response?t=new Uint8Array(r.response):!e&&r.responseText&&(t=(0,i.stringToBytes)(r.responseText));if(t){s(t);return}}n(new Error(r.statusText))}};r.send(null)}))}class DOMCMapReaderFactory extends n.BaseCMapReaderFactory{_fetchData(t,e){return fetchData(t,this.isCompressed).then((t=>({cMapData:t,compressionType:e})))}}e.DOMCMapReaderFactory=DOMCMapReaderFactory;class DOMStandardFontDataFactory extends n.BaseStandardFontDataFactory{_fetchData(t){return fetchData(t,!0)}}e.DOMStandardFontDataFactory=DOMStandardFontDataFactory;class DOMSVGFactory extends n.BaseSVGFactory{_createSVG(t){return document.createElementNS("http://www.w3.org/2000/svg",t)}}e.DOMSVGFactory=DOMSVGFactory;class PageViewport{constructor({viewBox:t,scale:e,rotation:s,offsetX:n=0,offsetY:i=0,dontFlip:r=!1}){this.viewBox=t;this.scale=e;this.rotation=s;this.offsetX=n;this.offsetY=i;const a=(t[2]+t[0])/2,o=(t[3]+t[1])/2;let l,c,h,d,u,p,g,f;(s%=360)<0&&(s+=360);switch(s){case 180:l=-1;c=0;h=0;d=1;break;case 90:l=0;c=1;h=1;d=0;break;case 270:l=0;c=-1;h=-1;d=0;break;case 0:l=1;c=0;h=0;d=-1;break;default:throw new Error("PageViewport: Invalid rotation, must be a multiple of 90 degrees.")}if(r){h=-h;d=-d}if(0===l){u=Math.abs(o-t[1])*e+n;p=Math.abs(a-t[0])*e+i;g=Math.abs(t[3]-t[1])*e;f=Math.abs(t[2]-t[0])*e}else{u=Math.abs(a-t[0])*e+n;p=Math.abs(o-t[1])*e+i;g=Math.abs(t[2]-t[0])*e;f=Math.abs(t[3]-t[1])*e}this.transform=[l*e,c*e,h*e,d*e,u-l*e*a-h*e*o,p-c*e*a-d*e*o];this.width=g;this.height=f}clone({scale:t=this.scale,rotation:e=this.rotation,offsetX:s=this.offsetX,offsetY:n=this.offsetY,dontFlip:i=!1}={}){return new PageViewport({viewBox:this.viewBox.slice(),scale:t,rotation:e,offsetX:s,offsetY:n,dontFlip:i})}convertToViewportPoint(t,e){return i.Util.applyTransform([t,e],this.transform)}convertToViewportRectangle(t){const e=i.Util.applyTransform([t[0],t[1]],this.transform),s=i.Util.applyTransform([t[2],t[3]],this.transform);return[e[0],e[1],s[0],s[1]]}convertToPdfPoint(t,e){return i.Util.applyInverseTransform([t,e],this.transform)}}e.PageViewport=PageViewport;class RenderingCancelledException extends i.BaseException{constructor(t,e){super(t,"RenderingCancelledException");this.type=e}}e.RenderingCancelledException=RenderingCancelledException;function isDataScheme(t){const e=t.length;let s=0;for(;s<e&&""===t[s].trim();)s++;return"data:"===t.substring(s,s+5).toLowerCase()}e.StatTimer=class StatTimer{constructor(){this.started=Object.create(null);this.times=[]}time(t){t in this.started&&(0,i.warn)(`Timer is already running for ${t}`);this.started[t]=Date.now()}timeEnd(t){t in this.started||(0,i.warn)(`Timer has not been started for ${t}`);this.times.push({name:t,start:this.started[t],end:Date.now()});delete this.started[t]}toString(){const t=[];let e=0;for(const t of this.times){const s=t.name;s.length>e&&(e=s.length)}for(const s of this.times){const n=s.end-s.start;t.push(`${s.name.padEnd(e)} ${n}ms\n`)}return t.join("")}};function isValidFetchUrl(t,e){try{const{protocol:s}=e?new URL(t,e):new URL(t);return"http:"===s||"https:"===s}catch(t){return!1}}let r;e.PDFDateString=class PDFDateString{static toDateObject(t){if(!t||"string"!=typeof t)return null;r||(r=new RegExp("^D:(\\d{4})(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?(\\d{2})?([Z|+|-])?(\\d{2})?'?(\\d{2})?'?"));const e=r.exec(t);if(!e)return null;const s=parseInt(e[1],10);let n=parseInt(e[2],10);n=n>=1&&n<=12?n-1:0;let i=parseInt(e[3],10);i=i>=1&&i<=31?i:1;let a=parseInt(e[4],10);a=a>=0&&a<=23?a:0;let o=parseInt(e[5],10);o=o>=0&&o<=59?o:0;let l=parseInt(e[6],10);l=l>=0&&l<=59?l:0;const c=e[7]||"Z";let h=parseInt(e[8],10);h=h>=0&&h<=23?h:0;let d=parseInt(e[9],10)||0;d=d>=0&&d<=59?d:0;if("-"===c){a+=h;o+=d}else if("+"===c){a-=h;o-=d}return new Date(Date.UTC(s,n,i,a,o,l))}};function getRGB(t){if(t.startsWith("#")){const e=parseInt(t.slice(1),16);return[(16711680&e)>>16,(65280&e)>>8,255&e]}if(t.startsWith("rgb("))return t.slice(4,-1).split(",").map((t=>parseInt(t)));if(t.startsWith("rgba("))return t.slice(5,-1).split(",").map((t=>parseInt(t))).slice(0,3);(0,i.warn)(`Not a valid color format: "${t}"`);return[0,0,0]}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.BaseStandardFontDataFactory=e.BaseSVGFactory=e.BaseCanvasFactory=e.BaseCMapReaderFactory=void 0;var n=s(1);class BaseCanvasFactory{constructor(){this.constructor===BaseCanvasFactory&&(0,n.unreachable)("Cannot initialize BaseCanvasFactory.")}create(t,e){if(t<=0||e<=0)throw new Error("Invalid canvas size");const s=this._createCanvas(t,e);return{canvas:s,context:s.getContext("2d")}}reset(t,e,s){if(!t.canvas)throw new Error("Canvas is not specified");if(e<=0||s<=0)throw new Error("Invalid canvas size");t.canvas.width=e;t.canvas.height=s}destroy(t){if(!t.canvas)throw new Error("Canvas is not specified");t.canvas.width=0;t.canvas.height=0;t.canvas=null;t.context=null}_createCanvas(t,e){(0,n.unreachable)("Abstract method `_createCanvas` called.")}}e.BaseCanvasFactory=BaseCanvasFactory;class BaseCMapReaderFactory{constructor({baseUrl:t=null,isCompressed:e=!1}){this.constructor===BaseCMapReaderFactory&&(0,n.unreachable)("Cannot initialize BaseCMapReaderFactory.");this.baseUrl=t;this.isCompressed=e}async fetch({name:t}){if(!this.baseUrl)throw new Error('The CMap "baseUrl" parameter must be specified, ensure that the "cMapUrl" and "cMapPacked" API parameters are provided.');if(!t)throw new Error("CMap name must be specified.");const e=this.baseUrl+t+(this.isCompressed?".bcmap":""),s=this.isCompressed?n.CMapCompressionType.BINARY:n.CMapCompressionType.NONE;return this._fetchData(e,s).catch((t=>{throw new Error(`Unable to load ${this.isCompressed?"binary ":""}CMap at: ${e}`)}))}_fetchData(t,e){(0,n.unreachable)("Abstract method `_fetchData` called.")}}e.BaseCMapReaderFactory=BaseCMapReaderFactory;class BaseStandardFontDataFactory{constructor({baseUrl:t=null}){this.constructor===BaseStandardFontDataFactory&&(0,n.unreachable)("Cannot initialize BaseStandardFontDataFactory.");this.baseUrl=t}async fetch({filename:t}){if(!this.baseUrl)throw new Error('The standard font "baseUrl" parameter must be specified, ensure that the "standardFontDataUrl" API parameter is provided.');if(!t)throw new Error("Font filename must be specified.");const e=`${this.baseUrl}${t}`;return this._fetchData(e).catch((t=>{throw new Error(`Unable to load font data at: ${e}`)}))}_fetchData(t){(0,n.unreachable)("Abstract method `_fetchData` called.")}}e.BaseStandardFontDataFactory=BaseStandardFontDataFactory;class BaseSVGFactory{constructor(){this.constructor===BaseSVGFactory&&(0,n.unreachable)("Cannot initialize BaseSVGFactory.")}create(t,e,s=!1){if(t<=0||e<=0)throw new Error("Invalid SVG dimensions");const n=this._createSVG("svg:svg");n.setAttribute("version","1.1");if(!s){n.setAttribute("width",`${t}px`);n.setAttribute("height",`${e}px`)}n.setAttribute("preserveAspectRatio","none");n.setAttribute("viewBox",`0 0 ${t} ${e}`);return n}createElement(t){if("string"!=typeof t)throw new Error("Invalid SVG element type");return this._createSVG(t)}_createSVG(t){(0,n.unreachable)("Abstract method `_createSVG` called.")}}e.BaseSVGFactory=BaseSVGFactory},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.MurmurHash3_64=void 0;var n=s(1);const i=3285377520,r=4294901760,a=65535;e.MurmurHash3_64=class MurmurHash3_64{constructor(t){this.h1=t?4294967295&t:i;this.h2=t?4294967295&t:i}update(t){let e,s;if("string"==typeof t){e=new Uint8Array(2*t.length);s=0;for(let n=0,i=t.length;n<i;n++){const i=t.charCodeAt(n);if(i<=255)e[s++]=i;else{e[s++]=i>>>8;e[s++]=255&i}}}else{if(!(0,n.isArrayBuffer)(t))throw new Error("Wrong data format in MurmurHash3_64_update. Input must be a string or array.");e=t.slice();s=e.byteLength}const i=s>>2,o=s-4*i,l=new Uint32Array(e.buffer,0,i);let c=0,h=0,d=this.h1,u=this.h2;const p=3432918353,g=461845907,f=11601,m=13715;for(let t=0;t<i;t++)if(1&t){c=l[t];c=c*p&r|c*f&a;c=c<<15|c>>>17;c=c*g&r|c*m&a;d^=c;d=d<<13|d>>>19;d=5*d+3864292196}else{h=l[t];h=h*p&r|h*f&a;h=h<<15|h>>>17;h=h*g&r|h*m&a;u^=h;u=u<<13|u>>>19;u=5*u+3864292196}c=0;switch(o){case 3:c^=e[4*i+2]<<16;case 2:c^=e[4*i+1]<<8;case 1:c^=e[4*i];c=c*p&r|c*f&a;c=c<<15|c>>>17;c=c*g&r|c*m&a;1&i?d^=c:u^=c}this.h1=d;this.h2=u}hexdigest(){let t=this.h1,e=this.h2;t^=e>>>1;t=3981806797*t&r|36045*t&a;e=4283543511*e&r|(2950163797*(e<<16|t>>>16)&r)>>>16;t^=e>>>1;t=444984403*t&r|60499*t&a;e=3301882366*e&r|(3120437893*(e<<16|t>>>16)&r)>>>16;t^=e>>>1;const s=(t>>>0).toString(16),n=(e>>>0).toString(16);return s.padStart(8,"0")+n.padStart(8,"0")}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.FontLoader=e.FontFaceObject=void 0;var n=s(1);class BaseFontLoader{constructor({docId:t,onUnsupportedFeature:e,ownerDocument:s=globalThis.document,styleElement:i=null}){this.constructor===BaseFontLoader&&(0,n.unreachable)("Cannot initialize BaseFontLoader.");this.docId=t;this._onUnsupportedFeature=e;this._document=s;this.nativeFontFaces=[];this.styleElement=null}addNativeFontFace(t){this.nativeFontFaces.push(t);this._document.fonts.add(t)}insertRule(t){let e=this.styleElement;if(!e){e=this.styleElement=this._document.createElement("style");e.id=`PDFJS_FONT_STYLE_TAG_${this.docId}`;this._document.documentElement.getElementsByTagName("head")[0].append(e)}const s=e.sheet;s.insertRule(t,s.cssRules.length)}clear(){for(const t of this.nativeFontFaces)this._document.fonts.delete(t);this.nativeFontFaces.length=0;if(this.styleElement){this.styleElement.remove();this.styleElement=null}}async bind(t){if(t.attached||t.missingFile)return;t.attached=!0;if(this.isFontLoadingAPISupported){const e=t.createNativeFontFace();if(e){this.addNativeFontFace(e);try{await e.loaded}catch(s){this._onUnsupportedFeature({featureId:n.UNSUPPORTED_FEATURES.errorFontLoadNative});(0,n.warn)(`Failed to load font '${e.family}': '${s}'.`);t.disableFontFace=!0;throw s}}return}const e=t.createFontFaceRule();if(e){this.insertRule(e);if(this.isSyncFontLoadingSupported)return;await new Promise((s=>{const n=this._queueLoadingCallback(s);this._prepareFontLoadEvent([e],[t],n)}))}}_queueLoadingCallback(t){(0,n.unreachable)("Abstract method `_queueLoadingCallback`.")}get isFontLoadingAPISupported(){const t=!!this._document?.fonts;return(0,n.shadow)(this,"isFontLoadingAPISupported",t)}get isSyncFontLoadingSupported(){(0,n.unreachable)("Abstract method `isSyncFontLoadingSupported`.")}get _loadTestFont(){(0,n.unreachable)("Abstract method `_loadTestFont`.")}_prepareFontLoadEvent(t,e,s){(0,n.unreachable)("Abstract method `_prepareFontLoadEvent`.")}}let i;e.FontLoader=i;e.FontLoader=i=class GenericFontLoader extends BaseFontLoader{constructor(t){super(t);this.loadingContext={requests:[],nextRequestId:0};this.loadTestFontId=0}get isSyncFontLoadingSupported(){let t=!1;if("undefined"==typeof navigator)t=!0;else{/Mozilla\/5.0.*?rv:(\d+).*? Gecko/.exec(navigator.userAgent)?.[1]>=14&&(t=!0)}return(0,n.shadow)(this,"isSyncFontLoadingSupported",t)}_queueLoadingCallback(t){const e=this.loadingContext,s={id:"pdfjs-font-loading-"+e.nextRequestId++,done:!1,complete:function completeRequest(){(0,n.assert)(!s.done,"completeRequest() cannot be called twice.");s.done=!0;for(;e.requests.length>0&&e.requests[0].done;){const t=e.requests.shift();setTimeout(t.callback,0)}},callback:t};e.requests.push(s);return s}get _loadTestFont(){return(0,n.shadow)(this,"_loadTestFont",atob("T1RUTwALAIAAAwAwQ0ZGIDHtZg4AAAOYAAAAgUZGVE1lkzZwAAAEHAAAABxHREVGABQAFQAABDgAAAAeT1MvMlYNYwkAAAEgAAAAYGNtYXABDQLUAAACNAAAAUJoZWFk/xVFDQAAALwAAAA2aGhlYQdkA+oAAAD0AAAAJGhtdHgD6AAAAAAEWAAAAAZtYXhwAAJQAAAAARgAAAAGbmFtZVjmdH4AAAGAAAAAsXBvc3T/hgAzAAADeAAAACAAAQAAAAEAALZRFsRfDzz1AAsD6AAAAADOBOTLAAAAAM4KHDwAAAAAA+gDIQAAAAgAAgAAAAAAAAABAAADIQAAAFoD6AAAAAAD6AABAAAAAAAAAAAAAAAAAAAAAQAAUAAAAgAAAAQD6AH0AAUAAAKKArwAAACMAooCvAAAAeAAMQECAAACAAYJAAAAAAAAAAAAAQAAAAAAAAAAAAAAAFBmRWQAwAAuAC4DIP84AFoDIQAAAAAAAQAAAAAAAAAAACAAIAABAAAADgCuAAEAAAAAAAAAAQAAAAEAAAAAAAEAAQAAAAEAAAAAAAIAAQAAAAEAAAAAAAMAAQAAAAEAAAAAAAQAAQAAAAEAAAAAAAUAAQAAAAEAAAAAAAYAAQAAAAMAAQQJAAAAAgABAAMAAQQJAAEAAgABAAMAAQQJAAIAAgABAAMAAQQJAAMAAgABAAMAAQQJAAQAAgABAAMAAQQJAAUAAgABAAMAAQQJAAYAAgABWABYAAAAAAAAAwAAAAMAAAAcAAEAAAAAADwAAwABAAAAHAAEACAAAAAEAAQAAQAAAC7//wAAAC7////TAAEAAAAAAAABBgAAAQAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAEAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAMAAAAAAAD/gwAyAAAAAQAAAAAAAAAAAAAAAAAAAAABAAQEAAEBAQJYAAEBASH4DwD4GwHEAvgcA/gXBIwMAYuL+nz5tQXkD5j3CBLnEQACAQEBIVhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYWFhYAAABAQAADwACAQEEE/t3Dov6fAH6fAT+fPp8+nwHDosMCvm1Cvm1DAz6fBQAAAAAAAABAAAAAMmJbzEAAAAAzgTjFQAAAADOBOQpAAEAAAAAAAAADAAUAAQAAAABAAAAAgABAAAAAAAAAAAD6AAAAAAAAA=="))}_prepareFontLoadEvent(t,e,s){function int32(t,e){return t.charCodeAt(e)<<24|t.charCodeAt(e+1)<<16|t.charCodeAt(e+2)<<8|255&t.charCodeAt(e+3)}function spliceString(t,e,s,n){return t.substring(0,e)+n+t.substring(e+s)}let i,r;const a=this._document.createElement("canvas");a.width=1;a.height=1;const o=a.getContext("2d");let l=0;const c=`lt${Date.now()}${this.loadTestFontId++}`;let h=this._loadTestFont;h=spliceString(h,976,c.length,c);const d=1482184792;let u=int32(h,16);for(i=0,r=c.length-3;i<r;i+=4)u=u-d+int32(c,i)|0;i<c.length&&(u=u-d+int32(c+"XXX",i)|0);h=spliceString(h,16,4,(0,n.string32)(u));const p=`@font-face {font-family:"${c}";src:${`url(data:font/opentype;base64,${btoa(h)});`}}`;this.insertRule(p);const g=[];for(const t of e)g.push(t.loadedName);g.push(c);const f=this._document.createElement("div");f.style.visibility="hidden";f.style.width=f.style.height="10px";f.style.position="absolute";f.style.top=f.style.left="0px";for(const t of g){const e=this._document.createElement("span");e.textContent="Hi";e.style.fontFamily=t;f.append(e)}this._document.body.append(f);!function isFontReady(t,e){l++;if(l>30){(0,n.warn)("Load test font never loaded.");e();return}o.font="30px "+t;o.fillText(".",0,20);o.getImageData(0,0,1,1).data[3]>0?e():setTimeout(isFontReady.bind(null,t,e))}(c,(()=>{f.remove();s.complete()}))}};e.FontFaceObject=class FontFaceObject{constructor(t,{isEvalSupported:e=!0,disableFontFace:s=!1,ignoreErrors:n=!1,onUnsupportedFeature:i,fontRegistry:r=null}){this.compiledGlyphs=Object.create(null);for(const e in t)this[e]=t[e];this.isEvalSupported=!1!==e;this.disableFontFace=!0===s;this.ignoreErrors=!0===n;this._onUnsupportedFeature=i;this.fontRegistry=r}createNativeFontFace(){if(!this.data||this.disableFontFace)return null;let t;if(this.cssFontInfo){const e={weight:this.cssFontInfo.fontWeight};this.cssFontInfo.italicAngle&&(e.style=`oblique ${this.cssFontInfo.italicAngle}deg`);t=new FontFace(this.cssFontInfo.fontFamily,this.data,e)}else t=new FontFace(this.loadedName,this.data,{});this.fontRegistry&&this.fontRegistry.registerFont(this);return t}createFontFaceRule(){if(!this.data||this.disableFontFace)return null;const t=(0,n.bytesToString)(this.data),e=`url(data:${this.mimetype};base64,${btoa(t)});`;let s;if(this.cssFontInfo){let t=`font-weight: ${this.cssFontInfo.fontWeight};`;this.cssFontInfo.italicAngle&&(t+=`font-style: oblique ${this.cssFontInfo.italicAngle}deg;`);s=`@font-face {font-family:"${this.cssFontInfo.fontFamily}";${t}src:${e}}`}else s=`@font-face {font-family:"${this.loadedName}";src:${e}}`;this.fontRegistry&&this.fontRegistry.registerFont(this,e);return s}getPathGenerator(t,e){if(void 0!==this.compiledGlyphs[e])return this.compiledGlyphs[e];let s;try{s=t.get(this.loadedName+"_path_"+e)}catch(t){if(!this.ignoreErrors)throw t;this._onUnsupportedFeature({featureId:n.UNSUPPORTED_FEATURES.errorFontGetPath});(0,n.warn)(`getPathGenerator - ignoring character: "${t}".`);return this.compiledGlyphs[e]=function(t,e){}}if(this.isEvalSupported&&n.FeatureTest.isEvalSupported){const t=[];for(const e of s){const s=void 0!==e.args?e.args.join(","):"";t.push("c.",e.cmd,"(",s,");\n")}return this.compiledGlyphs[e]=new Function("c","size",t.join(""))}return this.compiledGlyphs[e]=function(t,e){for(const n of s){"scale"===n.cmd&&(n.args=[e,-e]);t[n.cmd].apply(t,n.args)}}}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.CanvasGraphics=void 0;var n=s(8),i=s(1),r=s(13),a=s(14),o=s(3);const l=4096,c=o.isNodeJS&&"undefined"==typeof Path2D?-1:1e3,h=16;class CachedCanvases{constructor(t){this.canvasFactory=t;this.cache=Object.create(null)}getCanvas(t,e,s){let n;if(void 0!==this.cache[t]){n=this.cache[t];this.canvasFactory.reset(n,e,s)}else{n=this.canvasFactory.create(e,s);this.cache[t]=n}return n}delete(t){delete this.cache[t]}clear(){for(const t in this.cache){const e=this.cache[t];this.canvasFactory.destroy(e);delete this.cache[t]}}}function drawImageAtIntegerCoords(t,e,s,i,r,a,o,l,c,h){const[d,u,p,g,f,m]=(0,n.getCurrentTransform)(t);if(0===u&&0===p){const n=o*d+f,b=Math.round(n),A=l*g+m,_=Math.round(A),y=(o+c)*d+f,v=Math.abs(Math.round(y)-b)||1,S=(l+h)*g+m,x=Math.abs(Math.round(S)-_)||1;t.setTransform(Math.sign(d),0,0,Math.sign(g),b,_);t.drawImage(e,s,i,r,a,0,0,v,x);t.setTransform(d,u,p,g,f,m);return[v,x]}if(0===d&&0===g){const n=l*p+f,b=Math.round(n),A=o*u+m,_=Math.round(A),y=(l+h)*p+f,v=Math.abs(Math.round(y)-b)||1,S=(o+c)*u+m,x=Math.abs(Math.round(S)-_)||1;t.setTransform(0,Math.sign(u),Math.sign(p),0,b,_);t.drawImage(e,s,i,r,a,0,0,x,v);t.setTransform(d,u,p,g,f,m);return[x,v]}t.drawImage(e,s,i,r,a,o,l,c,h);return[Math.hypot(d,u)*c,Math.hypot(p,g)*h]}class CanvasExtraState{constructor(t,e){this.alphaIsShape=!1;this.fontSize=0;this.fontSizeScale=1;this.textMatrix=i.IDENTITY_MATRIX;this.textMatrixScale=1;this.fontMatrix=i.FONT_IDENTITY_MATRIX;this.leading=0;this.x=0;this.y=0;this.lineX=0;this.lineY=0;this.charSpacing=0;this.wordSpacing=0;this.textHScale=1;this.textRenderingMode=i.TextRenderingMode.FILL;this.textRise=0;this.fillColor="#000000";this.strokeColor="#000000";this.patternFill=!1;this.fillAlpha=1;this.strokeAlpha=1;this.lineWidth=1;this.activeSMask=null;this.transferMaps=null;this.startNewPathAndClipBox([0,0,t,e])}clone(){const t=Object.create(this);t.clipBox=this.clipBox.slice();return t}setCurrentPoint(t,e){this.x=t;this.y=e}updatePathMinMax(t,e,s){[e,s]=i.Util.applyTransform([e,s],t);this.minX=Math.min(this.minX,e);this.minY=Math.min(this.minY,s);this.maxX=Math.max(this.maxX,e);this.maxY=Math.max(this.maxY,s)}updateRectMinMax(t,e){const s=i.Util.applyTransform(e,t),n=i.Util.applyTransform(e.slice(2),t);this.minX=Math.min(this.minX,s[0],n[0]);this.minY=Math.min(this.minY,s[1],n[1]);this.maxX=Math.max(this.maxX,s[0],n[0]);this.maxY=Math.max(this.maxY,s[1],n[1])}updateScalingPathMinMax(t,e){i.Util.scaleMinMax(t,e);this.minX=Math.min(this.minX,e[0]);this.maxX=Math.max(this.maxX,e[1]);this.minY=Math.min(this.minY,e[2]);this.maxY=Math.max(this.maxY,e[3])}updateCurvePathMinMax(t,e,s,n,r,a,o,l,c,h){const d=i.Util.bezierBoundingBox(e,s,n,r,a,o,l,c);if(h){h[0]=Math.min(h[0],d[0],d[2]);h[1]=Math.max(h[1],d[0],d[2]);h[2]=Math.min(h[2],d[1],d[3]);h[3]=Math.max(h[3],d[1],d[3])}else this.updateRectMinMax(t,d)}getPathBoundingBox(t=r.PathType.FILL,e=null){const s=[this.minX,this.minY,this.maxX,this.maxY];if(t===r.PathType.STROKE){e||(0,i.unreachable)("Stroke bounding box must include transform.");const t=i.Util.singularValueDecompose2dScale(e),n=t[0]*this.lineWidth/2,r=t[1]*this.lineWidth/2;s[0]-=n;s[1]-=r;s[2]+=n;s[3]+=r}return s}updateClipFromPath(){const t=i.Util.intersect(this.clipBox,this.getPathBoundingBox());this.startNewPathAndClipBox(t||[0,0,0,0])}isEmptyClip(){return this.minX===1/0}startNewPathAndClipBox(t){this.clipBox=t;this.minX=1/0;this.minY=1/0;this.maxX=0;this.maxY=0}getClippedPathBoundingBox(t=r.PathType.FILL,e=null){return i.Util.intersect(this.clipBox,this.getPathBoundingBox(t,e))}}function putBinaryImageData(t,e,s=null){if("undefined"!=typeof ImageData&&e instanceof ImageData){t.putImageData(e,0,0);return}const n=e.height,r=e.width,a=n%h,o=(n-a)/h,l=0===a?o:o+1,c=t.createImageData(r,h);let d,u=0;const p=e.data,g=c.data;let f,m,b,A,_,y,v,S;if(s)switch(s.length){case 1:_=s[0];y=s[0];v=s[0];S=s[0];break;case 4:_=s[0];y=s[1];v=s[2];S=s[3]}if(e.kind===i.ImageKind.GRAYSCALE_1BPP){const e=p.byteLength,s=new Uint32Array(g.buffer,0,g.byteLength>>2),n=s.length,A=r+7>>3;let _=4294967295,y=i.FeatureTest.isLittleEndian?4278190080:255;S&&255===S[0]&&0===S[255]&&([_,y]=[y,_]);for(f=0;f<l;f++){b=f<o?h:a;d=0;for(m=0;m<b;m++){const t=e-u;let n=0;const i=t>A?r:8*t-7,a=-8&i;let o=0,l=0;for(;n<a;n+=8){l=p[u++];s[d++]=128&l?_:y;s[d++]=64&l?_:y;s[d++]=32&l?_:y;s[d++]=16&l?_:y;s[d++]=8&l?_:y;s[d++]=4&l?_:y;s[d++]=2&l?_:y;s[d++]=1&l?_:y}for(;n<i;n++){if(0===o){l=p[u++];o=128}s[d++]=l&o?_:y;o>>=1}}for(;d<n;)s[d++]=0;t.putImageData(c,0,f*h)}}else if(e.kind===i.ImageKind.RGBA_32BPP){const e=!!(_||y||v);m=0;A=r*h*4;for(f=0;f<o;f++){g.set(p.subarray(u,u+A));u+=A;if(e)for(let t=0;t<A;t+=4){_&&(g[t+0]=_[g[t+0]]);y&&(g[t+1]=y[g[t+1]]);v&&(g[t+2]=v[g[t+2]])}t.putImageData(c,0,m);m+=h}if(f<l){A=r*a*4;g.set(p.subarray(u,u+A));if(e)for(let t=0;t<A;t+=4){_&&(g[t+0]=_[g[t+0]]);y&&(g[t+1]=y[g[t+1]]);v&&(g[t+2]=v[g[t+2]])}t.putImageData(c,0,m)}}else{if(e.kind!==i.ImageKind.RGB_24BPP)throw new Error(`bad image kind: ${e.kind}`);{const e=!!(_||y||v);b=h;A=r*b;for(f=0;f<l;f++){if(f>=o){b=a;A=r*b}d=0;for(m=A;m--;){g[d++]=p[u++];g[d++]=p[u++];g[d++]=p[u++];g[d++]=255}if(e)for(let t=0;t<d;t+=4){_&&(g[t+0]=_[g[t+0]]);y&&(g[t+1]=y[g[t+1]]);v&&(g[t+2]=v[g[t+2]])}t.putImageData(c,0,f*h)}}}}function putBinaryImageMask(t,e){if(e.bitmap){t.drawImage(e.bitmap,0,0);return}const s=e.height,n=e.width,i=s%h,r=(s-i)/h,o=0===i?r:r+1,l=t.createImageData(n,h);let c=0;const d=e.data,u=l.data;for(let e=0;e<o;e++){const s=e<r?h:i;({srcPos:c}=(0,a.applyMaskImageData)({src:d,srcPos:c,dest:u,width:n,height:s}));t.putImageData(l,0,e*h)}}function copyCtxState(t,e){const s=["strokeStyle","fillStyle","fillRule","globalAlpha","lineWidth","lineCap","lineJoin","miterLimit","globalCompositeOperation","font"];for(let n=0,i=s.length;n<i;n++){const i=s[n];void 0!==t[i]&&(e[i]=t[i])}if(void 0!==t.setLineDash){e.setLineDash(t.getLineDash());e.lineDashOffset=t.lineDashOffset}}function resetCtxToDefault(t,e){t.strokeStyle=t.fillStyle=e||"#000000";t.fillRule="nonzero";t.globalAlpha=1;t.lineWidth=1;t.lineCap="butt";t.lineJoin="miter";t.miterLimit=10;t.globalCompositeOperation="source-over";t.font="10px sans-serif";if(void 0!==t.setLineDash){t.setLineDash([]);t.lineDashOffset=0}}function composeSMaskBackdrop(t,e,s,n){const i=t.length;for(let r=3;r<i;r+=4){const i=t[r];if(0===i){t[r-3]=e;t[r-2]=s;t[r-1]=n}else if(i<255){const a=255-i;t[r-3]=t[r-3]*i+e*a>>8;t[r-2]=t[r-2]*i+s*a>>8;t[r-1]=t[r-1]*i+n*a>>8}}}function composeSMaskAlpha(t,e,s){const n=t.length;for(let i=3;i<n;i+=4){const n=s?s[t[i]]:t[i];e[i]=e[i]*n*.00392156862745098|0}}function composeSMaskLuminosity(t,e,s){const n=t.length;for(let i=3;i<n;i+=4){const n=77*t[i-3]+152*t[i-2]+28*t[i-1];e[i]=s?e[i]*s[n>>8]>>8:e[i]*n>>16}}function composeSMask(t,e,s,n){const i=n[0],r=n[1],a=n[2]-i,o=n[3]-r;if(0!==a&&0!==o){!function genericComposeSMask(t,e,s,n,i,r,a,o,l,c,h){const d=!!r,u=d?r[0]:0,p=d?r[1]:0,g=d?r[2]:0;let f;f="Luminosity"===i?composeSMaskLuminosity:composeSMaskAlpha;const m=Math.min(n,Math.ceil(1048576/s));for(let i=0;i<n;i+=m){const r=Math.min(m,n-i),b=t.getImageData(o-c,i+(l-h),s,r),A=e.getImageData(o,i+l,s,r);d&&composeSMaskBackdrop(b.data,u,p,g);f(b.data,A.data,a);e.putImageData(A,o,i+l)}}(e.context,s,a,o,e.subtype,e.backdrop,e.transferMap,i,r,e.offsetX,e.offsetY);t.save();t.globalAlpha=1;t.globalCompositeOperation="source-over";t.setTransform(1,0,0,1,0,0);t.drawImage(s.canvas,0,0);t.restore()}}function getImageSmoothingEnabled(t,e){const s=i.Util.singularValueDecompose2dScale(t);s[0]=Math.fround(s[0]);s[1]=Math.fround(s[1]);const r=Math.fround((globalThis.devicePixelRatio||1)*n.PixelsPerInch.PDF_TO_CSS_UNITS);return void 0!==e?e:s[0]<=r||s[1]<=r}const d=["butt","round","square"],u=["miter","round","bevel"],p={},g={};class CanvasGraphics{constructor(t,e,s,n,i,r,a,o){this.ctx=t;this.current=new CanvasExtraState(this.ctx.canvas.width,this.ctx.canvas.height);this.stateStack=[];this.pendingClip=null;this.pendingEOFill=!1;this.res=null;this.xobjs=null;this.commonObjs=e;this.objs=s;this.canvasFactory=n;this.imageLayer=i;this.groupStack=[];this.processingType3=null;this.baseTransform=null;this.baseTransformStack=[];this.groupLevel=0;this.smaskStack=[];this.smaskCounter=0;this.tempSMask=null;this.suspendedCtx=null;this.contentVisible=!0;this.markedContentStack=[];this.optionalContentConfig=r;this.cachedCanvases=new CachedCanvases(this.canvasFactory);this.cachedPatterns=new Map;this.annotationCanvasMap=a;this.viewportScale=1;this.outputScaleX=1;this.outputScaleY=1;this.backgroundColor=o?.background||null;this.foregroundColor=o?.foreground||null;this._cachedScaleForStroking=null;this._cachedGetSinglePixelWidth=null;this._cachedBitmapsMap=new Map}getObject(t,e=null){return"string"==typeof t?t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t):e}beginDrawing({transform:t,viewport:e,transparency:s=!1,background:i=null}){const r=this.ctx.canvas.width,a=this.ctx.canvas.height,o=i||"#ffffff";this.ctx.save();if(this.foregroundColor&&this.backgroundColor){this.ctx.fillStyle=this.foregroundColor;const t=this.foregroundColor=this.ctx.fillStyle;this.ctx.fillStyle=this.backgroundColor;const e=this.backgroundColor=this.ctx.fillStyle;let s=!0,i=o;this.ctx.fillStyle=o;i=this.ctx.fillStyle;s="string"==typeof i&&/^#[0-9A-Fa-f]{6}$/.test(i);if("#000000"===t&&"#ffffff"===e||t===e||!s)this.foregroundColor=this.backgroundColor=null;else{const[s,r,a]=(0,n.getRGB)(i),newComp=t=>(t/=255)<=.03928?t/12.92:((t+.055)/1.055)**2.4,o=Math.round(.2126*newComp(s)+.7152*newComp(r)+.0722*newComp(a));this.selectColor=(s,n,i)=>{const r=.2126*newComp(s)+.7152*newComp(n)+.0722*newComp(i);return Math.round(r)===o?e:t}}}this.ctx.fillStyle=this.backgroundColor||o;this.ctx.fillRect(0,0,r,a);this.ctx.restore();if(s){const t=this.cachedCanvases.getCanvas("transparent",r,a);this.compositeCtx=this.ctx;this.transparentCanvas=t.canvas;this.ctx=t.context;this.ctx.save();this.ctx.transform(...(0,n.getCurrentTransform)(this.compositeCtx))}this.ctx.save();resetCtxToDefault(this.ctx,this.foregroundColor);if(t){this.ctx.transform(...t);this.outputScaleX=t[0];this.outputScaleY=t[0]}this.ctx.transform(...e.transform);this.viewportScale=e.scale;this.baseTransform=(0,n.getCurrentTransform)(this.ctx);if(this.imageLayer){(0,n.deprecated)("The `imageLayer` functionality will be removed in the future.");this.imageLayer.beginLayout()}}executeOperatorList(t,e,s,n){const r=t.argsArray,a=t.fnArray;let o=e||0;const l=r.length;if(l===o)return o;const c=l-o>10&&"function"==typeof s,h=c?Date.now()+15:0;let d=0;const u=this.commonObjs,p=this.objs;let g;for(;;){if(void 0!==n&&o===n.nextBreakPoint){n.breakIt(o,s);return o}g=a[o];if(g!==i.OPS.dependency)this[g].apply(this,r[o]);else for(const t of r[o]){const e=t.startsWith("g_")?u:p;if(!e.has(t)){e.get(t,s);return o}}o++;if(o===l)return o;if(c&&++d>10){if(Date.now()>h){s();return o}d=0}}}#J(){for(;this.stateStack.length||this.inSMaskMode;)this.restore();this.ctx.restore();if(this.transparentCanvas){this.ctx=this.compositeCtx;this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.drawImage(this.transparentCanvas,0,0);this.ctx.restore();this.transparentCanvas=null}}endDrawing(){this.#J();this.cachedCanvases.clear();this.cachedPatterns.clear();for(const t of this._cachedBitmapsMap.values()){for(const e of t.values())"undefined"!=typeof HTMLCanvasElement&&e instanceof HTMLCanvasElement&&(e.width=e.height=0);t.clear()}this._cachedBitmapsMap.clear();this.imageLayer&&this.imageLayer.endLayout()}_scaleImage(t,e){const s=t.width,n=t.height;let i,r,a=Math.max(Math.hypot(e[0],e[1]),1),o=Math.max(Math.hypot(e[2],e[3]),1),l=s,c=n,h="prescale1";for(;a>2&&l>1||o>2&&c>1;){let e=l,s=c;if(a>2&&l>1){e=Math.ceil(l/2);a/=l/e}if(o>2&&c>1){s=Math.ceil(c/2);o/=c/s}i=this.cachedCanvases.getCanvas(h,e,s);r=i.context;r.clearRect(0,0,e,s);r.drawImage(t,0,0,l,c,0,0,e,s);t=i.canvas;l=e;c=s;h="prescale1"===h?"prescale2":"prescale1"}return{img:t,paintWidth:l,paintHeight:c}}_createMaskCanvas(t){const e=this.ctx,{width:s,height:a}=t,o=this.current.fillColor,l=this.current.patternFill,c=(0,n.getCurrentTransform)(e);let h,d,u,p;if((t.bitmap||t.data)&&t.count>1){const e=t.bitmap||t.data.buffer,s=c.slice(0,4);d=JSON.stringify(l?s:[s,o]);h=this._cachedBitmapsMap.get(e);if(!h){h=new Map;this._cachedBitmapsMap.set(e,h)}const n=h.get(d);if(n&&!l){return{canvas:n,offsetX:Math.round(Math.min(c[0],c[2])+c[4]),offsetY:Math.round(Math.min(c[1],c[3])+c[5])}}u=n}if(!u){p=this.cachedCanvases.getCanvas("maskCanvas",s,a);putBinaryImageMask(p.context,t)}let g=i.Util.transform(c,[1/s,0,0,-1/a,0,0]);g=i.Util.transform(g,[1,0,0,1,0,-a]);const f=i.Util.applyTransform([0,0],g),m=i.Util.applyTransform([s,a],g),b=i.Util.normalizeRect([f[0],f[1],m[0],m[1]]),A=Math.round(b[2]-b[0])||1,_=Math.round(b[3]-b[1])||1,y=this.cachedCanvases.getCanvas("fillCanvas",A,_),v=y.context,S=Math.min(f[0],m[0]),x=Math.min(f[1],m[1]);v.translate(-S,-x);v.transform(...g);if(!u){u=this._scaleImage(p.canvas,(0,n.getCurrentTransformInverse)(v));u=u.img;h&&l&&h.set(d,u)}v.imageSmoothingEnabled=getImageSmoothingEnabled((0,n.getCurrentTransform)(v),t.interpolate);drawImageAtIntegerCoords(v,u,0,0,u.width,u.height,0,0,s,a);v.globalCompositeOperation="source-in";const E=i.Util.transform((0,n.getCurrentTransformInverse)(v),[1,0,0,1,-S,-x]);v.fillStyle=l?o.getPattern(e,this,E,r.PathType.FILL):o;v.fillRect(0,0,s,a);if(h&&!l){this.cachedCanvases.delete("fillCanvas");h.set(d,y.canvas)}return{canvas:y.canvas,offsetX:Math.round(S),offsetY:Math.round(x)}}setLineWidth(t){t!==this.current.lineWidth&&(this._cachedScaleForStroking=null);this.current.lineWidth=t;this.ctx.lineWidth=t}setLineCap(t){this.ctx.lineCap=d[t]}setLineJoin(t){this.ctx.lineJoin=u[t]}setMiterLimit(t){this.ctx.miterLimit=t}setDash(t,e){const s=this.ctx;if(void 0!==s.setLineDash){s.setLineDash(t);s.lineDashOffset=e}}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(let e=0,s=t.length;e<s;e++){const s=t[e],n=s[0],i=s[1];switch(n){case"LW":this.setLineWidth(i);break;case"LC":this.setLineCap(i);break;case"LJ":this.setLineJoin(i);break;case"ML":this.setMiterLimit(i);break;case"D":this.setDash(i[0],i[1]);break;case"RI":this.setRenderingIntent(i);break;case"FL":this.setFlatness(i);break;case"Font":this.setFont(i[0],i[1]);break;case"CA":this.current.strokeAlpha=s[1];break;case"ca":this.current.fillAlpha=s[1];this.ctx.globalAlpha=s[1];break;case"BM":this.ctx.globalCompositeOperation=i;break;case"SMask":this.current.activeSMask=i?this.tempSMask:null;this.tempSMask=null;this.checkSMaskState();break;case"TR":this.current.transferMaps=i}}}get inSMaskMode(){return!!this.suspendedCtx}checkSMaskState(){const t=this.inSMaskMode;this.current.activeSMask&&!t?this.beginSMaskMode():!this.current.activeSMask&&t&&this.endSMaskMode()}beginSMaskMode(){if(this.inSMaskMode)throw new Error("beginSMaskMode called while already in smask mode");const t=this.ctx.canvas.width,e=this.ctx.canvas.height,s="smaskGroupAt"+this.groupLevel,i=this.cachedCanvases.getCanvas(s,t,e);this.suspendedCtx=this.ctx;this.ctx=i.context;const r=this.ctx;r.setTransform(...(0,n.getCurrentTransform)(this.suspendedCtx));copyCtxState(this.suspendedCtx,r);!function mirrorContextOperations(t,e){if(t._removeMirroring)throw new Error("Context is already forwarding operations.");t.__originalSave=t.save;t.__originalRestore=t.restore;t.__originalRotate=t.rotate;t.__originalScale=t.scale;t.__originalTranslate=t.translate;t.__originalTransform=t.transform;t.__originalSetTransform=t.setTransform;t.__originalResetTransform=t.resetTransform;t.__originalClip=t.clip;t.__originalMoveTo=t.moveTo;t.__originalLineTo=t.lineTo;t.__originalBezierCurveTo=t.bezierCurveTo;t.__originalRect=t.rect;t.__originalClosePath=t.closePath;t.__originalBeginPath=t.beginPath;t._removeMirroring=()=>{t.save=t.__originalSave;t.restore=t.__originalRestore;t.rotate=t.__originalRotate;t.scale=t.__originalScale;t.translate=t.__originalTranslate;t.transform=t.__originalTransform;t.setTransform=t.__originalSetTransform;t.resetTransform=t.__originalResetTransform;t.clip=t.__originalClip;t.moveTo=t.__originalMoveTo;t.lineTo=t.__originalLineTo;t.bezierCurveTo=t.__originalBezierCurveTo;t.rect=t.__originalRect;t.closePath=t.__originalClosePath;t.beginPath=t.__originalBeginPath;delete t._removeMirroring};t.save=function ctxSave(){e.save();this.__originalSave()};t.restore=function ctxRestore(){e.restore();this.__originalRestore()};t.translate=function ctxTranslate(t,s){e.translate(t,s);this.__originalTranslate(t,s)};t.scale=function ctxScale(t,s){e.scale(t,s);this.__originalScale(t,s)};t.transform=function ctxTransform(t,s,n,i,r,a){e.transform(t,s,n,i,r,a);this.__originalTransform(t,s,n,i,r,a)};t.setTransform=function ctxSetTransform(t,s,n,i,r,a){e.setTransform(t,s,n,i,r,a);this.__originalSetTransform(t,s,n,i,r,a)};t.resetTransform=function ctxResetTransform(){e.resetTransform();this.__originalResetTransform()};t.rotate=function ctxRotate(t){e.rotate(t);this.__originalRotate(t)};t.clip=function ctxRotate(t){e.clip(t);this.__originalClip(t)};t.moveTo=function(t,s){e.moveTo(t,s);this.__originalMoveTo(t,s)};t.lineTo=function(t,s){e.lineTo(t,s);this.__originalLineTo(t,s)};t.bezierCurveTo=function(t,s,n,i,r,a){e.bezierCurveTo(t,s,n,i,r,a);this.__originalBezierCurveTo(t,s,n,i,r,a)};t.rect=function(t,s,n,i){e.rect(t,s,n,i);this.__originalRect(t,s,n,i)};t.closePath=function(){e.closePath();this.__originalClosePath()};t.beginPath=function(){e.beginPath();this.__originalBeginPath()}}(r,this.suspendedCtx);this.setGState([["BM","source-over"],["ca",1],["CA",1]])}endSMaskMode(){if(!this.inSMaskMode)throw new Error("endSMaskMode called while not in smask mode");this.ctx._removeMirroring();copyCtxState(this.ctx,this.suspendedCtx);this.ctx=this.suspendedCtx;this.suspendedCtx=null}compose(t){if(!this.current.activeSMask)return;if(t){t[0]=Math.floor(t[0]);t[1]=Math.floor(t[1]);t[2]=Math.ceil(t[2]);t[3]=Math.ceil(t[3])}else t=[0,0,this.ctx.canvas.width,this.ctx.canvas.height];const e=this.current.activeSMask;composeSMask(this.suspendedCtx,e,this.ctx,t);this.ctx.save();this.ctx.setTransform(1,0,0,1,0,0);this.ctx.clearRect(0,0,this.ctx.canvas.width,this.ctx.canvas.height);this.ctx.restore()}save(){if(this.inSMaskMode){copyCtxState(this.ctx,this.suspendedCtx);this.suspendedCtx.save()}else this.ctx.save();const t=this.current;this.stateStack.push(t);this.current=t.clone()}restore(){0===this.stateStack.length&&this.inSMaskMode&&this.endSMaskMode();if(0!==this.stateStack.length){this.current=this.stateStack.pop();if(this.inSMaskMode){this.suspendedCtx.restore();copyCtxState(this.suspendedCtx,this.ctx)}else this.ctx.restore();this.checkSMaskState();this.pendingClip=null;this._cachedScaleForStroking=null;this._cachedGetSinglePixelWidth=null}}transform(t,e,s,n,i,r){this.ctx.transform(t,e,s,n,i,r);this._cachedScaleForStroking=null;this._cachedGetSinglePixelWidth=null}constructPath(t,e,s){const r=this.ctx,a=this.current;let o,l,c=a.x,h=a.y;const d=(0,n.getCurrentTransform)(r),u=0===d[0]&&0===d[3]||0===d[1]&&0===d[2],p=u?s.slice(0):null;for(let s=0,n=0,g=t.length;s<g;s++)switch(0|t[s]){case i.OPS.rectangle:c=e[n++];h=e[n++];const t=e[n++],s=e[n++],g=c+t,f=h+s;r.moveTo(c,h);if(0===t||0===s)r.lineTo(g,f);else{r.lineTo(g,h);r.lineTo(g,f);r.lineTo(c,f)}u||a.updateRectMinMax(d,[c,h,g,f]);r.closePath();break;case i.OPS.moveTo:c=e[n++];h=e[n++];r.moveTo(c,h);u||a.updatePathMinMax(d,c,h);break;case i.OPS.lineTo:c=e[n++];h=e[n++];r.lineTo(c,h);u||a.updatePathMinMax(d,c,h);break;case i.OPS.curveTo:o=c;l=h;c=e[n+4];h=e[n+5];r.bezierCurveTo(e[n],e[n+1],e[n+2],e[n+3],c,h);a.updateCurvePathMinMax(d,o,l,e[n],e[n+1],e[n+2],e[n+3],c,h,p);n+=6;break;case i.OPS.curveTo2:o=c;l=h;r.bezierCurveTo(c,h,e[n],e[n+1],e[n+2],e[n+3]);a.updateCurvePathMinMax(d,o,l,c,h,e[n],e[n+1],e[n+2],e[n+3],p);c=e[n+2];h=e[n+3];n+=4;break;case i.OPS.curveTo3:o=c;l=h;c=e[n+2];h=e[n+3];r.bezierCurveTo(e[n],e[n+1],c,h,c,h);a.updateCurvePathMinMax(d,o,l,e[n],e[n+1],c,h,c,h,p);n+=4;break;case i.OPS.closePath:r.closePath()}u&&a.updateScalingPathMinMax(d,p);a.setCurrentPoint(c,h)}closePath(){this.ctx.closePath()}stroke(t){t=void 0===t||t;const e=this.ctx,s=this.current.strokeColor;e.globalAlpha=this.current.strokeAlpha;if(this.contentVisible)if("object"==typeof s&&s?.getPattern){e.save();e.strokeStyle=s.getPattern(e,this,(0,n.getCurrentTransformInverse)(e),r.PathType.STROKE);this.rescaleAndStroke(!1);e.restore()}else this.rescaleAndStroke(!0);t&&this.consumePath(this.current.getClippedPathBoundingBox());e.globalAlpha=this.current.fillAlpha}closeStroke(){this.closePath();this.stroke()}fill(t){t=void 0===t||t;const e=this.ctx,s=this.current.fillColor;let i=!1;if(this.current.patternFill){e.save();e.fillStyle=s.getPattern(e,this,(0,n.getCurrentTransformInverse)(e),r.PathType.FILL);i=!0}const a=this.current.getClippedPathBoundingBox();if(this.contentVisible&&null!==a)if(this.pendingEOFill){e.fill("evenodd");this.pendingEOFill=!1}else e.fill();i&&e.restore();t&&this.consumePath(a)}eoFill(){this.pendingEOFill=!0;this.fill()}fillStroke(){this.fill(!1);this.stroke(!1);this.consumePath()}eoFillStroke(){this.pendingEOFill=!0;this.fillStroke()}closeFillStroke(){this.closePath();this.fillStroke()}closeEOFillStroke(){this.pendingEOFill=!0;this.closePath();this.fillStroke()}endPath(){this.consumePath()}clip(){this.pendingClip=p}eoClip(){this.pendingClip=g}beginText(){this.current.textMatrix=i.IDENTITY_MATRIX;this.current.textMatrixScale=1;this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0}endText(){const t=this.pendingTextPaths,e=this.ctx;if(void 0!==t){e.save();e.beginPath();for(const s of t){e.setTransform(...s.transform);e.translate(s.x,s.y);s.addToPath(e,s.fontSize)}e.restore();e.clip();e.beginPath();delete this.pendingTextPaths}else e.beginPath()}setCharSpacing(t){this.current.charSpacing=t}setWordSpacing(t){this.current.wordSpacing=t}setHScale(t){this.current.textHScale=t/100}setLeading(t){this.current.leading=-t}setFont(t,e){const s=this.commonObjs.get(t),n=this.current;if(!s)throw new Error(`Can't find font for ${t}`);n.fontMatrix=s.fontMatrix||i.FONT_IDENTITY_MATRIX;0!==n.fontMatrix[0]&&0!==n.fontMatrix[3]||(0,i.warn)("Invalid font matrix for font "+t);if(e<0){e=-e;n.fontDirection=-1}else n.fontDirection=1;this.current.font=s;this.current.fontSize=e;if(s.isType3Font)return;const r=s.loadedName||"sans-serif";let a="normal";s.black?a="900":s.bold&&(a="bold");const o=s.italic?"italic":"normal",l=`"${r}", ${s.fallbackName}`;let c=e;e<16?c=16:e>100&&(c=100);this.current.fontSizeScale=e/c;this.ctx.font=`${o} ${a} ${c}px ${l}`}setTextRenderingMode(t){this.current.textRenderingMode=t}setTextRise(t){this.current.textRise=t}moveText(t,e){this.current.x=this.current.lineX+=t;this.current.y=this.current.lineY+=e}setLeadingMoveText(t,e){this.setLeading(-e);this.moveText(t,e)}setTextMatrix(t,e,s,n,i,r){this.current.textMatrix=[t,e,s,n,i,r];this.current.textMatrixScale=Math.hypot(t,e);this.current.x=this.current.lineX=0;this.current.y=this.current.lineY=0}nextLine(){this.moveText(0,this.current.leading)}paintChar(t,e,s,r){const a=this.ctx,o=this.current,l=o.font,c=o.textRenderingMode,h=o.fontSize/o.fontSizeScale,d=c&i.TextRenderingMode.FILL_STROKE_MASK,u=!!(c&i.TextRenderingMode.ADD_TO_PATH_FLAG),p=o.patternFill&&!l.missingFile;let g;(l.disableFontFace||u||p)&&(g=l.getPathGenerator(this.commonObjs,t));if(l.disableFontFace||p){a.save();a.translate(e,s);a.beginPath();g(a,h);r&&a.setTransform(...r);d!==i.TextRenderingMode.FILL&&d!==i.TextRenderingMode.FILL_STROKE||a.fill();d!==i.TextRenderingMode.STROKE&&d!==i.TextRenderingMode.FILL_STROKE||a.stroke();a.restore()}else{d!==i.TextRenderingMode.FILL&&d!==i.TextRenderingMode.FILL_STROKE||a.fillText(t,e,s);d!==i.TextRenderingMode.STROKE&&d!==i.TextRenderingMode.FILL_STROKE||a.strokeText(t,e,s)}if(u){(this.pendingTextPaths||(this.pendingTextPaths=[])).push({transform:(0,n.getCurrentTransform)(a),x:e,y:s,fontSize:h,addToPath:g})}}get isFontSubpixelAAEnabled(){const{context:t}=this.cachedCanvases.getCanvas("isFontSubpixelAAEnabled",10,10);t.scale(1.5,1);t.fillText("I",0,10);const e=t.getImageData(0,0,10,10).data;let s=!1;for(let t=3;t<e.length;t+=4)if(e[t]>0&&e[t]<255){s=!0;break}return(0,i.shadow)(this,"isFontSubpixelAAEnabled",s)}showText(t){const e=this.current,s=e.font;if(s.isType3Font)return this.showType3Text(t);const a=e.fontSize;if(0===a)return;const o=this.ctx,l=e.fontSizeScale,c=e.charSpacing,h=e.wordSpacing,d=e.fontDirection,u=e.textHScale*d,p=t.length,g=s.vertical,f=g?1:-1,m=s.defaultVMetrics,b=a*e.fontMatrix[0],A=e.textRenderingMode===i.TextRenderingMode.FILL&&!s.disableFontFace&&!e.patternFill;o.save();o.transform(...e.textMatrix);o.translate(e.x,e.y+e.textRise);d>0?o.scale(u,-1):o.scale(u,1);let _;if(e.patternFill){o.save();const t=e.fillColor.getPattern(o,this,(0,n.getCurrentTransformInverse)(o),r.PathType.FILL);_=(0,n.getCurrentTransform)(o);o.restore();o.fillStyle=t}let y=e.lineWidth;const v=e.textMatrixScale;if(0===v||0===y){const t=e.textRenderingMode&i.TextRenderingMode.FILL_STROKE_MASK;t!==i.TextRenderingMode.STROKE&&t!==i.TextRenderingMode.FILL_STROKE||(y=this.getSinglePixelWidth())}else y/=v;if(1!==l){o.scale(l,l);y/=l}o.lineWidth=y;let S,x=0;for(S=0;S<p;++S){const e=t[S];if("number"==typeof e){x+=f*e*a/1e3;continue}let n=!1;const i=(e.isSpace?h:0)+c,r=e.fontChar,u=e.accent;let p,y,v,E=e.width;if(g){const t=e.vmetric||m,s=-(e.vmetric?t[1]:.5*E)*b,n=t[2]*b;E=t?-t[0]:E;p=s/l;y=(x+n)/l}else{p=x/l;y=0}if(s.remeasure&&E>0){const t=1e3*o.measureText(r).width/a*l;if(E<t&&this.isFontSubpixelAAEnabled){const e=E/t;n=!0;o.save();o.scale(e,1);p/=e}else E!==t&&(p+=(E-t)/2e3*a/l)}if(this.contentVisible&&(e.isInFont||s.missingFile))if(A&&!u)o.fillText(r,p,y);else{this.paintChar(r,p,y,_);if(u){const t=p+a*u.offset.x/l,e=y-a*u.offset.y/l;this.paintChar(u.fontChar,t,e,_)}}v=g?E*b-i*d:E*b+i*d;x+=v;n&&o.restore()}g?e.y-=x:e.x+=x*u;o.restore();this.compose()}showType3Text(t){const e=this.ctx,s=this.current,n=s.font,r=s.fontSize,a=s.fontDirection,o=n.vertical?1:-1,l=s.charSpacing,c=s.wordSpacing,h=s.textHScale*a,d=s.fontMatrix||i.FONT_IDENTITY_MATRIX,u=t.length;let p,g,f,m;if(!(s.textRenderingMode===i.TextRenderingMode.INVISIBLE)&&0!==r){this._cachedScaleForStroking=null;this._cachedGetSinglePixelWidth=null;e.save();e.transform(...s.textMatrix);e.translate(s.x,s.y);e.scale(h,a);for(p=0;p<u;++p){g=t[p];if("number"==typeof g){m=o*g*r/1e3;this.ctx.translate(m,0);s.x+=m*h;continue}const a=(g.isSpace?c:0)+l,u=n.charProcOperatorList[g.operatorListId];if(!u){(0,i.warn)(`Type3 character "${g.operatorListId}" is not available.`);continue}if(this.contentVisible){this.processingType3=g;this.save();e.scale(r,r);e.transform(...d);this.executeOperatorList(u);this.restore()}f=i.Util.applyTransform([g.width,0],d)[0]*r+a;e.translate(f,0);s.x+=f*h}e.restore();this.processingType3=null}}setCharWidth(t,e){}setCharWidthAndBounds(t,e,s,n,i,r){this.ctx.rect(s,n,i-s,r-n);this.ctx.clip();this.endPath()}getColorN_Pattern(t){let e;if("TilingPattern"===t[0]){const s=t[1],i=this.baseTransform||(0,n.getCurrentTransform)(this.ctx),a={createCanvasGraphics:t=>new CanvasGraphics(t,this.commonObjs,this.objs,this.canvasFactory)};e=new r.TilingPattern(t,s,this.ctx,a,i)}else e=this._getPattern(t[1],t[2]);return e}setStrokeColorN(){this.current.strokeColor=this.getColorN_Pattern(arguments)}setFillColorN(){this.current.fillColor=this.getColorN_Pattern(arguments);this.current.patternFill=!0}setStrokeRGBColor(t,e,s){const n=this.selectColor?.(t,e,s)||i.Util.makeHexColor(t,e,s);this.ctx.strokeStyle=n;this.current.strokeColor=n}setFillRGBColor(t,e,s){const n=this.selectColor?.(t,e,s)||i.Util.makeHexColor(t,e,s);this.ctx.fillStyle=n;this.current.fillColor=n;this.current.patternFill=!1}_getPattern(t,e=null){let s;if(this.cachedPatterns.has(t))s=this.cachedPatterns.get(t);else{s=(0,r.getShadingPattern)(this.objs.get(t));this.cachedPatterns.set(t,s)}e&&(s.matrix=e);return s}shadingFill(t){if(!this.contentVisible)return;const e=this.ctx;this.save();const s=this._getPattern(t);e.fillStyle=s.getPattern(e,this,(0,n.getCurrentTransformInverse)(e),r.PathType.SHADING);const a=(0,n.getCurrentTransformInverse)(e);if(a){const t=e.canvas,s=t.width,n=t.height,r=i.Util.applyTransform([0,0],a),o=i.Util.applyTransform([0,n],a),l=i.Util.applyTransform([s,0],a),c=i.Util.applyTransform([s,n],a),h=Math.min(r[0],o[0],l[0],c[0]),d=Math.min(r[1],o[1],l[1],c[1]),u=Math.max(r[0],o[0],l[0],c[0]),p=Math.max(r[1],o[1],l[1],c[1]);this.ctx.fillRect(h,d,u-h,p-d)}else this.ctx.fillRect(-1e10,-1e10,2e10,2e10);this.compose(this.current.getClippedPathBoundingBox());this.restore()}beginInlineImage(){(0,i.unreachable)("Should not call beginInlineImage")}beginImageData(){(0,i.unreachable)("Should not call beginImageData")}paintFormXObjectBegin(t,e){if(this.contentVisible){this.save();this.baseTransformStack.push(this.baseTransform);Array.isArray(t)&&6===t.length&&this.transform(...t);this.baseTransform=(0,n.getCurrentTransform)(this.ctx);if(e){const t=e[2]-e[0],s=e[3]-e[1];this.ctx.rect(e[0],e[1],t,s);this.current.updateRectMinMax((0,n.getCurrentTransform)(this.ctx),e);this.clip();this.endPath()}}}paintFormXObjectEnd(){if(this.contentVisible){this.restore();this.baseTransform=this.baseTransformStack.pop()}}beginGroup(t){if(!this.contentVisible)return;this.save();if(this.inSMaskMode){this.endSMaskMode();this.current.activeSMask=null}const e=this.ctx;t.isolated||(0,i.info)("TODO: Support non-isolated groups.");t.knockout&&(0,i.warn)("Knockout groups not supported.");const s=(0,n.getCurrentTransform)(e);t.matrix&&e.transform(...t.matrix);if(!t.bbox)throw new Error("Bounding box is required.");let r=i.Util.getAxialAlignedBoundingBox(t.bbox,(0,n.getCurrentTransform)(e));const a=[0,0,e.canvas.width,e.canvas.height];r=i.Util.intersect(r,a)||[0,0,0,0];const o=Math.floor(r[0]),c=Math.floor(r[1]);let h=Math.max(Math.ceil(r[2])-o,1),d=Math.max(Math.ceil(r[3])-c,1),u=1,p=1;if(h>l){u=h/l;h=l}if(d>l){p=d/l;d=l}this.current.startNewPathAndClipBox([0,0,h,d]);let g="groupAt"+this.groupLevel;t.smask&&(g+="_smask_"+this.smaskCounter++%2);const f=this.cachedCanvases.getCanvas(g,h,d),m=f.context;m.scale(1/u,1/p);m.translate(-o,-c);m.transform(...s);if(t.smask)this.smaskStack.push({canvas:f.canvas,context:m,offsetX:o,offsetY:c,scaleX:u,scaleY:p,subtype:t.smask.subtype,backdrop:t.smask.backdrop,transferMap:t.smask.transferMap||null,startTransformInverse:null});else{e.setTransform(1,0,0,1,0,0);e.translate(o,c);e.scale(u,p);e.save()}copyCtxState(e,m);this.ctx=m;this.setGState([["BM","source-over"],["ca",1],["CA",1]]);this.groupStack.push(e);this.groupLevel++}endGroup(t){if(!this.contentVisible)return;this.groupLevel--;const e=this.ctx,s=this.groupStack.pop();this.ctx=s;this.ctx.imageSmoothingEnabled=!1;if(t.smask){this.tempSMask=this.smaskStack.pop();this.restore()}else{this.ctx.restore();const t=(0,n.getCurrentTransform)(this.ctx);this.restore();this.ctx.save();this.ctx.setTransform(...t);const s=i.Util.getAxialAlignedBoundingBox([0,0,e.canvas.width,e.canvas.height],t);this.ctx.drawImage(e.canvas,0,0);this.ctx.restore();this.compose(s)}}beginAnnotation(t,e,s,r,a){this.#J();resetCtxToDefault(this.ctx,this.foregroundColor);this.ctx.save();this.save();this.baseTransform&&this.ctx.setTransform(...this.baseTransform);if(Array.isArray(e)&&4===e.length){const r=e[2]-e[0],o=e[3]-e[1];if(a&&this.annotationCanvasMap){(s=s.slice())[4]-=e[0];s[5]-=e[1];(e=e.slice())[0]=e[1]=0;e[2]=r;e[3]=o;const[a,l]=i.Util.singularValueDecompose2dScale((0,n.getCurrentTransform)(this.ctx)),{viewportScale:c}=this,h=Math.ceil(r*this.outputScaleX*c),d=Math.ceil(o*this.outputScaleY*c);this.annotationCanvas=this.canvasFactory.create(h,d);const{canvas:u,context:p}=this.annotationCanvas;this.annotationCanvasMap.set(t,u);this.annotationCanvas.savedCtx=this.ctx;this.ctx=p;this.ctx.setTransform(a,0,0,-l,0,o*l);resetCtxToDefault(this.ctx,this.foregroundColor)}else{resetCtxToDefault(this.ctx,this.foregroundColor);this.ctx.rect(e[0],e[1],r,o);this.ctx.clip();this.endPath()}}this.current=new CanvasExtraState(this.ctx.canvas.width,this.ctx.canvas.height);this.transform(...s);this.transform(...r)}endAnnotation(){if(this.annotationCanvas){this.ctx=this.annotationCanvas.savedCtx;delete this.annotationCanvas.savedCtx;delete this.annotationCanvas}}paintImageMaskXObject(t){if(!this.contentVisible)return;const e=t.count;(t=this.getObject(t.data,t)).count=e;const s=this.ctx,n=this.processingType3;if(n){void 0===n.compiled&&(n.compiled=function compileType3Glyph(t){const{width:e,height:s}=t;if(e>c||s>c)return null;const n=new Uint8Array([0,2,4,0,1,0,5,4,8,10,0,8,0,2,1,0]),i=e+1;let r,a,o,l=new Uint8Array(i*(s+1));const h=e+7&-8;let d=new Uint8Array(h*s),u=0;for(const e of t.data){let t=128;for(;t>0;){d[u++]=e&t?0:255;t>>=1}}let p=0;u=0;if(0!==d[u]){l[0]=1;++p}for(a=1;a<e;a++){if(d[u]!==d[u+1]){l[a]=d[u]?2:1;++p}u++}if(0!==d[u]){l[a]=2;++p}for(r=1;r<s;r++){u=r*h;o=r*i;if(d[u-h]!==d[u]){l[o]=d[u]?1:8;++p}let t=(d[u]?4:0)+(d[u-h]?8:0);for(a=1;a<e;a++){t=(t>>2)+(d[u+1]?4:0)+(d[u-h+1]?8:0);if(n[t]){l[o+a]=n[t];++p}u++}if(d[u-h]!==d[u]){l[o+a]=d[u]?2:4;++p}if(p>1e3)return null}u=h*(s-1);o=r*i;if(0!==d[u]){l[o]=8;++p}for(a=1;a<e;a++){if(d[u]!==d[u+1]){l[o+a]=d[u]?4:8;++p}u++}if(0!==d[u]){l[o+a]=4;++p}if(p>1e3)return null;const g=new Int32Array([0,i,-1,0,-i,0,0,0,1]),f=new Path2D;for(r=0;p&&r<=s;r++){let t=r*i;const s=t+e;for(;t<s&&!l[t];)t++;if(t===s)continue;f.moveTo(t%i,r);const n=t;let a=l[t];do{const e=g[a];do{t+=e}while(!l[t]);const s=l[t];if(5!==s&&10!==s){a=s;l[t]=0}else{a=s&51*a>>4;l[t]&=a>>2|a<<2}f.lineTo(t%i,t/i|0);l[t]||--p}while(n!==t);--r}d=null;l=null;return function(t){t.save();t.scale(1/e,-1/s);t.translate(0,-s);t.fill(f);t.beginPath();t.restore()}}(t));if(n.compiled){n.compiled(s);return}}const i=this._createMaskCanvas(t),r=i.canvas;s.save();s.setTransform(1,0,0,1,0,0);s.drawImage(r,i.offsetX,i.offsetY);s.restore();this.compose()}paintImageMaskXObjectRepeat(t,e,s=0,r=0,a,o){if(!this.contentVisible)return;t=this.getObject(t.data,t);const l=this.ctx;l.save();const c=(0,n.getCurrentTransform)(l);l.transform(e,s,r,a,0,0);const h=this._createMaskCanvas(t);l.setTransform(1,0,0,1,0,0);for(let t=0,n=o.length;t<n;t+=2){const n=i.Util.transform(c,[e,s,r,a,o[t],o[t+1]]),[d,u]=i.Util.applyTransform([0,0],n);l.drawImage(h.canvas,d,u)}l.restore();this.compose()}paintImageMaskXObjectGroup(t){if(!this.contentVisible)return;const e=this.ctx,s=this.current.fillColor,i=this.current.patternFill;for(const a of t){const{data:t,width:o,height:l,transform:c}=a,h=this.cachedCanvases.getCanvas("maskCanvas",o,l),d=h.context;d.save();putBinaryImageMask(d,this.getObject(t,a));d.globalCompositeOperation="source-in";d.fillStyle=i?s.getPattern(d,this,(0,n.getCurrentTransformInverse)(e),r.PathType.FILL):s;d.fillRect(0,0,o,l);d.restore();e.save();e.transform(...c);e.scale(1,-1);drawImageAtIntegerCoords(e,h.canvas,0,0,o,l,0,-1,1,1);e.restore()}this.compose()}paintImageXObject(t){if(!this.contentVisible)return;const e=this.getObject(t);e?this.paintInlineImageXObject(e):(0,i.warn)("Dependent image isn't ready yet")}paintImageXObjectRepeat(t,e,s,n){if(!this.contentVisible)return;const r=this.getObject(t);if(!r){(0,i.warn)("Dependent image isn't ready yet");return}const a=r.width,o=r.height,l=[];for(let t=0,i=n.length;t<i;t+=2)l.push({transform:[e,0,0,s,n[t],n[t+1]],x:0,y:0,w:a,h:o});this.paintInlineImageXObjectGroup(r,l)}paintInlineImageXObject(t){if(!this.contentVisible)return;const e=t.width,s=t.height,r=this.ctx;this.save();r.scale(1/e,-1/s);let a;if("function"==typeof HTMLElement&&t instanceof HTMLElement||!t.data)a=t;else{const n=this.cachedCanvases.getCanvas("inlineImage",e,s);putBinaryImageData(n.context,t,this.current.transferMaps);a=n.canvas}const o=this._scaleImage(a,(0,n.getCurrentTransformInverse)(r));r.imageSmoothingEnabled=getImageSmoothingEnabled((0,n.getCurrentTransform)(r),t.interpolate);const[l,c]=drawImageAtIntegerCoords(r,o.img,0,0,o.paintWidth,o.paintHeight,0,-s,e,s);if(this.imageLayer){const[e,r]=i.Util.applyTransform([0,-s],(0,n.getCurrentTransform)(this.ctx));this.imageLayer.appendImage({imgData:t,left:e,top:r,width:l,height:c})}this.compose();this.restore()}paintInlineImageXObjectGroup(t,e){if(!this.contentVisible)return;const s=this.ctx,r=t.width,a=t.height,o=this.cachedCanvases.getCanvas("inlineImage",r,a);putBinaryImageData(o.context,t,this.current.transferMaps);for(const l of e){s.save();s.transform(...l.transform);s.scale(1,-1);drawImageAtIntegerCoords(s,o.canvas,l.x,l.y,l.w,l.h,0,-1,1,1);if(this.imageLayer){const[e,s]=i.Util.applyTransform([l.x,l.y],(0,n.getCurrentTransform)(this.ctx));this.imageLayer.appendImage({imgData:t,left:e,top:s,width:r,height:a})}s.restore()}this.compose()}paintSolidColorImageMask(){if(this.contentVisible){this.ctx.fillRect(0,0,1,1);this.compose()}}markPoint(t){}markPointProps(t,e){}beginMarkedContent(t){this.markedContentStack.push({visible:!0})}beginMarkedContentProps(t,e){"OC"===t?this.markedContentStack.push({visible:this.optionalContentConfig.isVisible(e)}):this.markedContentStack.push({visible:!0});this.contentVisible=this.isContentVisible()}endMarkedContent(){this.markedContentStack.pop();this.contentVisible=this.isContentVisible()}beginCompat(){}endCompat(){}consumePath(t){const e=this.current.isEmptyClip();this.pendingClip&&this.current.updateClipFromPath();this.pendingClip||this.compose(t);const s=this.ctx;if(this.pendingClip){e||(this.pendingClip===g?s.clip("evenodd"):s.clip());this.pendingClip=null}this.current.startNewPathAndClipBox(this.current.clipBox);s.beginPath()}getSinglePixelWidth(){if(!this._cachedGetSinglePixelWidth){const t=(0,n.getCurrentTransform)(this.ctx);if(0===t[1]&&0===t[2])this._cachedGetSinglePixelWidth=1/Math.min(Math.abs(t[0]),Math.abs(t[3]));else{const e=Math.abs(t[0]*t[3]-t[2]*t[1]),s=Math.hypot(t[0],t[2]),n=Math.hypot(t[1],t[3]);this._cachedGetSinglePixelWidth=Math.max(s,n)/e}}return this._cachedGetSinglePixelWidth}getScaleForStroking(){if(!this._cachedScaleForStroking){const{lineWidth:t}=this.current,e=(0,n.getCurrentTransform)(this.ctx);let s,i;if(0===e[1]&&0===e[2]){const n=Math.abs(e[0]),r=Math.abs(e[3]);if(0===t){s=1/n;i=1/r}else{const e=n*t,a=r*t;s=e<1?1/e:1;i=a<1?1/a:1}}else{const n=Math.abs(e[0]*e[3]-e[2]*e[1]),r=Math.hypot(e[0],e[1]),a=Math.hypot(e[2],e[3]);if(0===t){s=a/n;i=r/n}else{const e=t*n;s=a>e?a/e:1;i=r>e?r/e:1}}this._cachedScaleForStroking=[s,i]}return this._cachedScaleForStroking}rescaleAndStroke(t){const{ctx:e}=this,{lineWidth:s}=this.current,[i,r]=this.getScaleForStroking();e.lineWidth=s||1;if(1===i&&1===r){e.stroke();return}let a,o,l;if(t){a=(0,n.getCurrentTransform)(e);o=e.getLineDash().slice();l=e.lineDashOffset}e.scale(i,r);const c=Math.max(i,r);e.setLineDash(e.getLineDash().map((t=>t/c)));e.lineDashOffset/=c;e.stroke();if(t){e.setTransform(...a);e.setLineDash(o);e.lineDashOffset=l}}isContentVisible(){for(let t=this.markedContentStack.length-1;t>=0;t--)if(!this.markedContentStack[t].visible)return!1;return!0}}e.CanvasGraphics=CanvasGraphics;for(const t in i.OPS)void 0!==CanvasGraphics.prototype[t]&&(CanvasGraphics.prototype[i.OPS[t]]=CanvasGraphics.prototype[t])},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.TilingPattern=e.PathType=void 0;e.getShadingPattern=function getShadingPattern(t){switch(t[0]){case"RadialAxial":return new RadialAxialShadingPattern(t);case"Mesh":return new MeshShadingPattern(t);case"Dummy":return new DummyShadingPattern}throw new Error(`Unknown IR type: ${t[0]}`)};var n=s(1),i=s(8),r=s(3);const a={FILL:"Fill",STROKE:"Stroke",SHADING:"Shading"};e.PathType=a;function applyBoundingBox(t,e){if(!e||r.isNodeJS)return;const s=e[2]-e[0],n=e[3]-e[1],i=new Path2D;i.rect(e[0],e[1],s,n);t.clip(i)}class BaseShadingPattern{constructor(){this.constructor===BaseShadingPattern&&(0,n.unreachable)("Cannot initialize BaseShadingPattern.")}getPattern(){(0,n.unreachable)("Abstract method `getPattern` called.")}}class RadialAxialShadingPattern extends BaseShadingPattern{constructor(t){super();this._type=t[1];this._bbox=t[2];this._colorStops=t[3];this._p0=t[4];this._p1=t[5];this._r0=t[6];this._r1=t[7];this.matrix=null}_createGradient(t){let e;"axial"===this._type?e=t.createLinearGradient(this._p0[0],this._p0[1],this._p1[0],this._p1[1]):"radial"===this._type&&(e=t.createRadialGradient(this._p0[0],this._p0[1],this._r0,this._p1[0],this._p1[1],this._r1));for(const t of this._colorStops)e.addColorStop(t[0],t[1]);return e}getPattern(t,e,s,r){let o;if(r===a.STROKE||r===a.FILL){const a=e.current.getClippedPathBoundingBox(r,(0,i.getCurrentTransform)(t))||[0,0,0,0],l=Math.ceil(a[2]-a[0])||1,c=Math.ceil(a[3]-a[1])||1,h=e.cachedCanvases.getCanvas("pattern",l,c,!0),d=h.context;d.clearRect(0,0,d.canvas.width,d.canvas.height);d.beginPath();d.rect(0,0,d.canvas.width,d.canvas.height);d.translate(-a[0],-a[1]);s=n.Util.transform(s,[1,0,0,1,a[0],a[1]]);d.transform(...e.baseTransform);this.matrix&&d.transform(...this.matrix);applyBoundingBox(d,this._bbox);d.fillStyle=this._createGradient(d);d.fill();o=t.createPattern(h.canvas,"no-repeat");const u=new DOMMatrix(s);try{o.setTransform(u)}catch(t){(0,n.warn)(`RadialAxialShadingPattern.getPattern: "${t?.message}".`)}}else{applyBoundingBox(t,this._bbox);o=this._createGradient(t)}return o}}function drawTriangle(t,e,s,n,i,r,a,o){const l=e.coords,c=e.colors,h=t.data,d=4*t.width;let u;if(l[s+1]>l[n+1]){u=s;s=n;n=u;u=r;r=a;a=u}if(l[n+1]>l[i+1]){u=n;n=i;i=u;u=a;a=o;o=u}if(l[s+1]>l[n+1]){u=s;s=n;n=u;u=r;r=a;a=u}const p=(l[s]+e.offsetX)*e.scaleX,g=(l[s+1]+e.offsetY)*e.scaleY,f=(l[n]+e.offsetX)*e.scaleX,m=(l[n+1]+e.offsetY)*e.scaleY,b=(l[i]+e.offsetX)*e.scaleX,A=(l[i+1]+e.offsetY)*e.scaleY;if(g>=A)return;const _=c[r],y=c[r+1],v=c[r+2],S=c[a],x=c[a+1],E=c[a+2],C=c[o],P=c[o+1],T=c[o+2],w=Math.round(g),k=Math.round(A);let F,M,R,D,I,O,L,N;for(let t=w;t<=k;t++){if(t<m){let e;e=t<g?0:(g-t)/(g-m);F=p-(p-f)*e;M=_-(_-S)*e;R=y-(y-x)*e;D=v-(v-E)*e}else{let e;e=t>A?1:m===A?0:(m-t)/(m-A);F=f-(f-b)*e;M=S-(S-C)*e;R=x-(x-P)*e;D=E-(E-T)*e}let e;e=t<g?0:t>A?1:(g-t)/(g-A);I=p-(p-b)*e;O=_-(_-C)*e;L=y-(y-P)*e;N=v-(v-T)*e;const s=Math.round(Math.min(F,I)),n=Math.round(Math.max(F,I));let i=d*t+4*s;for(let t=s;t<=n;t++){e=(F-t)/(F-I);e<0?e=0:e>1&&(e=1);h[i++]=M-(M-O)*e|0;h[i++]=R-(R-L)*e|0;h[i++]=D-(D-N)*e|0;h[i++]=255}}}function drawFigure(t,e,s){const n=e.coords,i=e.colors;let r,a;switch(e.type){case"lattice":const o=e.verticesPerRow,l=Math.floor(n.length/o)-1,c=o-1;for(r=0;r<l;r++){let e=r*o;for(let r=0;r<c;r++,e++){drawTriangle(t,s,n[e],n[e+1],n[e+o],i[e],i[e+1],i[e+o]);drawTriangle(t,s,n[e+o+1],n[e+1],n[e+o],i[e+o+1],i[e+1],i[e+o])}}break;case"triangles":for(r=0,a=n.length;r<a;r+=3)drawTriangle(t,s,n[r],n[r+1],n[r+2],i[r],i[r+1],i[r+2]);break;default:throw new Error("illegal figure")}}class MeshShadingPattern extends BaseShadingPattern{constructor(t){super();this._coords=t[2];this._colors=t[3];this._figures=t[4];this._bounds=t[5];this._bbox=t[7];this._background=t[8];this.matrix=null}_createMeshCanvas(t,e,s){const n=Math.floor(this._bounds[0]),i=Math.floor(this._bounds[1]),r=Math.ceil(this._bounds[2])-n,a=Math.ceil(this._bounds[3])-i,o=Math.min(Math.ceil(Math.abs(r*t[0]*1.1)),3e3),l=Math.min(Math.ceil(Math.abs(a*t[1]*1.1)),3e3),c=r/o,h=a/l,d={coords:this._coords,colors:this._colors,offsetX:-n,offsetY:-i,scaleX:1/c,scaleY:1/h},u=o+4,p=l+4,g=s.getCanvas("mesh",u,p,!1),f=g.context,m=f.createImageData(o,l);if(e){const t=m.data;for(let s=0,n=t.length;s<n;s+=4){t[s]=e[0];t[s+1]=e[1];t[s+2]=e[2];t[s+3]=255}}for(const t of this._figures)drawFigure(m,t,d);f.putImageData(m,2,2);return{canvas:g.canvas,offsetX:n-2*c,offsetY:i-2*h,scaleX:c,scaleY:h}}getPattern(t,e,s,r){applyBoundingBox(t,this._bbox);let o;if(r===a.SHADING)o=n.Util.singularValueDecompose2dScale((0,i.getCurrentTransform)(t));else{o=n.Util.singularValueDecompose2dScale(e.baseTransform);if(this.matrix){const t=n.Util.singularValueDecompose2dScale(this.matrix);o=[o[0]*t[0],o[1]*t[1]]}}const l=this._createMeshCanvas(o,r===a.SHADING?null:this._background,e.cachedCanvases);if(r!==a.SHADING){t.setTransform(...e.baseTransform);this.matrix&&t.transform(...this.matrix)}t.translate(l.offsetX,l.offsetY);t.scale(l.scaleX,l.scaleY);return t.createPattern(l.canvas,"no-repeat")}}class DummyShadingPattern extends BaseShadingPattern{getPattern(){return"hotpink"}}const o=1,l=2;class TilingPattern{static get MAX_PATTERN_SIZE(){return(0,n.shadow)(this,"MAX_PATTERN_SIZE",3e3)}constructor(t,e,s,n,i){this.operatorList=t[2];this.matrix=t[3]||[1,0,0,1,0,0];this.bbox=t[4];this.xstep=t[5];this.ystep=t[6];this.paintType=t[7];this.tilingType=t[8];this.color=e;this.ctx=s;this.canvasGraphicsFactory=n;this.baseTransform=i}createPatternCanvas(t){const e=this.operatorList,s=this.bbox,r=this.xstep,a=this.ystep,o=this.paintType,l=this.tilingType,c=this.color,h=this.canvasGraphicsFactory;(0,n.info)("TilingType: "+l);const d=s[0],u=s[1],p=s[2],g=s[3],f=n.Util.singularValueDecompose2dScale(this.matrix),m=n.Util.singularValueDecompose2dScale(this.baseTransform),b=[f[0]*m[0],f[1]*m[1]],A=this.getSizeAndScale(r,this.ctx.canvas.width,b[0]),_=this.getSizeAndScale(a,this.ctx.canvas.height,b[1]),y=t.cachedCanvases.getCanvas("pattern",A.size,_.size,!0),v=y.context,S=h.createCanvasGraphics(v);S.groupLevel=t.groupLevel;this.setFillAndStrokeStyleToContext(S,o,c);let x=d,E=u,C=p,P=g;if(d<0){x=0;C+=Math.abs(d)}if(u<0){E=0;P+=Math.abs(u)}v.translate(-A.scale*x,-_.scale*E);S.transform(A.scale,0,0,_.scale,0,0);v.save();this.clipBbox(S,x,E,C,P);S.baseTransform=(0,i.getCurrentTransform)(S.ctx);S.executeOperatorList(e);S.endDrawing();return{canvas:y.canvas,scaleX:A.scale,scaleY:_.scale,offsetX:x,offsetY:E}}getSizeAndScale(t,e,s){t=Math.abs(t);const n=Math.max(TilingPattern.MAX_PATTERN_SIZE,e);let i=Math.ceil(t*s);i>=n?i=n:s=i/t;return{scale:s,size:i}}clipBbox(t,e,s,n,r){const a=n-e,o=r-s;t.ctx.rect(e,s,a,o);t.current.updateRectMinMax((0,i.getCurrentTransform)(t.ctx),[e,s,n,r]);t.clip();t.endPath()}setFillAndStrokeStyleToContext(t,e,s){const i=t.ctx,r=t.current;switch(e){case o:const t=this.ctx;i.fillStyle=t.fillStyle;i.strokeStyle=t.strokeStyle;r.fillColor=t.fillStyle;r.strokeColor=t.strokeStyle;break;case l:const a=n.Util.makeHexColor(s[0],s[1],s[2]);i.fillStyle=a;i.strokeStyle=a;r.fillColor=a;r.strokeColor=a;break;default:throw new n.FormatError(`Unsupported paint type: ${e}`)}}getPattern(t,e,s,i){let r=s;if(i!==a.SHADING){r=n.Util.transform(r,e.baseTransform);this.matrix&&(r=n.Util.transform(r,this.matrix))}const o=this.createPatternCanvas(e);let l=new DOMMatrix(r);l=l.translate(o.offsetX,o.offsetY);l=l.scale(1/o.scaleX,1/o.scaleY);const c=t.createPattern(o.canvas,"repeat");try{c.setTransform(l)}catch(t){(0,n.warn)(`TilingPattern.getPattern: "${t?.message}".`)}return c}}e.TilingPattern=TilingPattern},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.applyMaskImageData=function applyMaskImageData({src:t,srcPos:e=0,dest:s,destPos:i=0,width:r,height:a,inverseDecode:o=!1}){const l=n.FeatureTest.isLittleEndian?4278190080:255,[c,h]=o?[0,l]:[l,0],d=r>>3,u=7&r,p=t.length;s=new Uint32Array(s.buffer);for(let n=0;n<a;n++){for(const n=e+d;e<n;e++){const n=e<p?t[e]:255;s[i++]=128&n?h:c;s[i++]=64&n?h:c;s[i++]=32&n?h:c;s[i++]=16&n?h:c;s[i++]=8&n?h:c;s[i++]=4&n?h:c;s[i++]=2&n?h:c;s[i++]=1&n?h:c}if(0===u)continue;const n=e<p?t[e++]:255;for(let t=0;t<u;t++)s[i++]=n&1<<7-t?h:c}return{srcPos:e,destPos:i}};var n=s(1)},(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0});e.GlobalWorkerOptions=void 0;const s=Object.create(null);e.GlobalWorkerOptions=s;s.workerPort=void 0===s.workerPort?null:s.workerPort;s.workerSrc=void 0===s.workerSrc?"":s.workerSrc},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.MessageHandler=void 0;var n=s(1);const i=1,r=2,a=1,o=2,l=3,c=4,h=5,d=6,u=7,p=8;function wrapReason(t){t instanceof Error||"object"==typeof t&&null!==t||(0,n.unreachable)('wrapReason: Expected "reason" to be a (possibly cloned) Error.');switch(t.name){case"AbortException":return new n.AbortException(t.message);case"MissingPDFException":return new n.MissingPDFException(t.message);case"PasswordException":return new n.PasswordException(t.message,t.code);case"UnexpectedResponseException":return new n.UnexpectedResponseException(t.message,t.status);case"UnknownErrorException":return new n.UnknownErrorException(t.message,t.details);default:return new n.UnknownErrorException(t.message,t.toString())}}e.MessageHandler=class MessageHandler{constructor(t,e,s){this.sourceName=t;this.targetName=e;this.comObj=s;this.callbackId=1;this.streamId=1;this.streamSinks=Object.create(null);this.streamControllers=Object.create(null);this.callbackCapabilities=Object.create(null);this.actionHandler=Object.create(null);this._onComObjOnMessage=t=>{const e=t.data;if(e.targetName!==this.sourceName)return;if(e.stream){this._processStreamMessage(e);return}if(e.callback){const t=e.callbackId,s=this.callbackCapabilities[t];if(!s)throw new Error(`Cannot resolve callback ${t}`);delete this.callbackCapabilities[t];if(e.callback===i)s.resolve(e.data);else{if(e.callback!==r)throw new Error("Unexpected callback case");s.reject(wrapReason(e.reason))}return}const n=this.actionHandler[e.action];if(!n)throw new Error(`Unknown action from worker: ${e.action}`);if(e.callbackId){const t=this.sourceName,a=e.sourceName;new Promise((function(t){t(n(e.data))})).then((function(n){s.postMessage({sourceName:t,targetName:a,callback:i,callbackId:e.callbackId,data:n})}),(function(n){s.postMessage({sourceName:t,targetName:a,callback:r,callbackId:e.callbackId,reason:wrapReason(n)})}))}else e.streamId?this._createStreamSink(e):n(e.data)};s.addEventListener("message",this._onComObjOnMessage)}on(t,e){const s=this.actionHandler;if(s[t])throw new Error(`There is already an actionName called "${t}"`);s[t]=e}send(t,e,s){this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,data:e},s)}sendWithPromise(t,e,s){const i=this.callbackId++,r=(0,n.createPromiseCapability)();this.callbackCapabilities[i]=r;try{this.comObj.postMessage({sourceName:this.sourceName,targetName:this.targetName,action:t,callbackId:i,data:e},s)}catch(t){r.reject(t)}return r.promise}sendWithStream(t,e,s,i){const r=this.streamId++,o=this.sourceName,l=this.targetName,c=this.comObj;return new ReadableStream({start:s=>{const a=(0,n.createPromiseCapability)();this.streamControllers[r]={controller:s,startCall:a,pullCall:null,cancelCall:null,isClosed:!1};c.postMessage({sourceName:o,targetName:l,action:t,streamId:r,data:e,desiredSize:s.desiredSize},i);return a.promise},pull:t=>{const e=(0,n.createPromiseCapability)();this.streamControllers[r].pullCall=e;c.postMessage({sourceName:o,targetName:l,stream:d,streamId:r,desiredSize:t.desiredSize});return e.promise},cancel:t=>{(0,n.assert)(t instanceof Error,"cancel must have a valid reason");const e=(0,n.createPromiseCapability)();this.streamControllers[r].cancelCall=e;this.streamControllers[r].isClosed=!0;c.postMessage({sourceName:o,targetName:l,stream:a,streamId:r,reason:wrapReason(t)});return e.promise}},s)}_createStreamSink(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,r=this.comObj,a=this,o=this.actionHandler[t.action],d={enqueue(t,a=1,o){if(this.isCancelled)return;const l=this.desiredSize;this.desiredSize-=a;if(l>0&&this.desiredSize<=0){this.sinkCapability=(0,n.createPromiseCapability)();this.ready=this.sinkCapability.promise}r.postMessage({sourceName:s,targetName:i,stream:c,streamId:e,chunk:t},o)},close(){if(!this.isCancelled){this.isCancelled=!0;r.postMessage({sourceName:s,targetName:i,stream:l,streamId:e});delete a.streamSinks[e]}},error(t){(0,n.assert)(t instanceof Error,"error must have a valid reason");if(!this.isCancelled){this.isCancelled=!0;r.postMessage({sourceName:s,targetName:i,stream:h,streamId:e,reason:wrapReason(t)})}},sinkCapability:(0,n.createPromiseCapability)(),onPull:null,onCancel:null,isCancelled:!1,desiredSize:t.desiredSize,ready:null};d.sinkCapability.resolve();d.ready=d.sinkCapability.promise;this.streamSinks[e]=d;new Promise((function(e){e(o(t.data,d))})).then((function(){r.postMessage({sourceName:s,targetName:i,stream:p,streamId:e,success:!0})}),(function(t){r.postMessage({sourceName:s,targetName:i,stream:p,streamId:e,reason:wrapReason(t)})}))}_processStreamMessage(t){const e=t.streamId,s=this.sourceName,i=t.sourceName,r=this.comObj,g=this.streamControllers[e],f=this.streamSinks[e];switch(t.stream){case p:t.success?g.startCall.resolve():g.startCall.reject(wrapReason(t.reason));break;case u:t.success?g.pullCall.resolve():g.pullCall.reject(wrapReason(t.reason));break;case d:if(!f){r.postMessage({sourceName:s,targetName:i,stream:u,streamId:e,success:!0});break}f.desiredSize<=0&&t.desiredSize>0&&f.sinkCapability.resolve();f.desiredSize=t.desiredSize;new Promise((function(t){t(f.onPull&&f.onPull())})).then((function(){r.postMessage({sourceName:s,targetName:i,stream:u,streamId:e,success:!0})}),(function(t){r.postMessage({sourceName:s,targetName:i,stream:u,streamId:e,reason:wrapReason(t)})}));break;case c:(0,n.assert)(g,"enqueue should have stream controller");if(g.isClosed)break;g.controller.enqueue(t.chunk);break;case l:(0,n.assert)(g,"close should have stream controller");if(g.isClosed)break;g.isClosed=!0;g.controller.close();this._deleteStreamController(g,e);break;case h:(0,n.assert)(g,"error should have stream controller");g.controller.error(wrapReason(t.reason));this._deleteStreamController(g,e);break;case o:t.success?g.cancelCall.resolve():g.cancelCall.reject(wrapReason(t.reason));this._deleteStreamController(g,e);break;case a:if(!f)break;new Promise((function(e){e(f.onCancel&&f.onCancel(wrapReason(t.reason)))})).then((function(){r.postMessage({sourceName:s,targetName:i,stream:o,streamId:e,success:!0})}),(function(t){r.postMessage({sourceName:s,targetName:i,stream:o,streamId:e,reason:wrapReason(t)})}));f.sinkCapability.reject(wrapReason(t.reason));f.isCancelled=!0;delete this.streamSinks[e];break;default:throw new Error("Unexpected stream case")}}async _deleteStreamController(t,e){await Promise.allSettled([t.startCall&&t.startCall.promise,t.pullCall&&t.pullCall.promise,t.cancelCall&&t.cancelCall.promise]);delete this.streamControllers[e]}destroy(){this.comObj.removeEventListener("message",this._onComObjOnMessage)}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.Metadata=void 0;var n=s(1);e.Metadata=class Metadata{#Q;#Z;constructor({parsedData:t,rawData:e}){this.#Q=t;this.#Z=e}getRaw(){return this.#Z}get(t){return this.#Q.get(t)??null}getAll(){return(0,n.objectFromMap)(this.#Q)}has(t){return this.#Q.has(t)}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.OptionalContentConfig=void 0;var n=s(1);const i=Symbol("INTERNAL");class OptionalContentGroup{#tt=!0;constructor(t,e){this.name=t;this.intent=e}get visible(){return this.#tt}_setVisible(t,e){t!==i&&(0,n.unreachable)("Internal method `_setVisible` called.");this.#tt=e}}e.OptionalContentConfig=class OptionalContentConfig{#et=!0;#st=new Map;#nt=null;#it=null;constructor(t){this.name=null;this.creator=null;if(null!==t){this.name=t.name;this.creator=t.creator;this.#it=t.order;for(const e of t.groups)this.#st.set(e.id,new OptionalContentGroup(e.name,e.intent));if("OFF"===t.baseState)for(const t of this.#st.values())t._setVisible(i,!1);for(const e of t.on)this.#st.get(e)._setVisible(i,!0);for(const e of t.off)this.#st.get(e)._setVisible(i,!1);this.#nt=new Map;for(const[t,e]of this.#st)this.#nt.set(t,e.visible)}}#rt(t){const e=t.length;if(e<2)return!0;const s=t[0];for(let i=1;i<e;i++){const e=t[i];let r;if(Array.isArray(e))r=this.#rt(e);else{if(!this.#st.has(e)){(0,n.warn)(`Optional content group not found: ${e}`);return!0}r=this.#st.get(e).visible}switch(s){case"And":if(!r)return!1;break;case"Or":if(r)return!0;break;case"Not":return!r;default:return!0}}return"And"===s}isVisible(t){if(0===this.#st.size)return!0;if(!t){(0,n.warn)("Optional content group not defined.");return!0}if("OCG"===t.type){if(!this.#st.has(t.id)){(0,n.warn)(`Optional content group not found: ${t.id}`);return!0}return this.#st.get(t.id).visible}if("OCMD"===t.type){if(t.expression)return this.#rt(t.expression);if(!t.policy||"AnyOn"===t.policy){for(const e of t.ids){if(!this.#st.has(e)){(0,n.warn)(`Optional content group not found: ${e}`);return!0}if(this.#st.get(e).visible)return!0}return!1}if("AllOn"===t.policy){for(const e of t.ids){if(!this.#st.has(e)){(0,n.warn)(`Optional content group not found: ${e}`);return!0}if(!this.#st.get(e).visible)return!1}return!0}if("AnyOff"===t.policy){for(const e of t.ids){if(!this.#st.has(e)){(0,n.warn)(`Optional content group not found: ${e}`);return!0}if(!this.#st.get(e).visible)return!0}return!1}if("AllOff"===t.policy){for(const e of t.ids){if(!this.#st.has(e)){(0,n.warn)(`Optional content group not found: ${e}`);return!0}if(this.#st.get(e).visible)return!1}return!0}(0,n.warn)(`Unknown optional content policy ${t.policy}.`);return!0}(0,n.warn)(`Unknown group type ${t.type}.`);return!0}setVisibility(t,e=!0){if(this.#st.has(t)){this.#st.get(t)._setVisible(i,!!e);this.#et=null}else(0,n.warn)(`Optional content group not found: ${t}`)}get hasInitialVisibility(){if(null!==this.#et)return this.#et;for(const[t,e]of this.#st){const s=this.#nt.get(t);if(e.visible!==s)return this.#et=!1}return this.#et=!0}getOrder(){return this.#st.size?this.#it?this.#it.slice():[...this.#st.keys()]:null}getGroups(){return this.#st.size>0?(0,n.objectFromMap)(this.#st):null}getGroup(t){return this.#st.get(t)||null}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.PDFDataTransportStream=void 0;var n=s(1),i=s(8);e.PDFDataTransportStream=class PDFDataTransportStream{constructor(t,e){(0,n.assert)(e,'PDFDataTransportStream - missing required "pdfDataRangeTransport" argument.');this._queuedChunks=[];this._progressiveDone=t.progressiveDone||!1;this._contentDispositionFilename=t.contentDispositionFilename||null;const s=t.initialData;if(s?.length>0){const t=new Uint8Array(s).buffer;this._queuedChunks.push(t)}this._pdfDataRangeTransport=e;this._isStreamingSupported=!t.disableStream;this._isRangeSupported=!t.disableRange;this._contentLength=t.length;this._fullRequestReader=null;this._rangeReaders=[];this._pdfDataRangeTransport.addRangeListener(((t,e)=>{this._onReceiveData({begin:t,chunk:e})}));this._pdfDataRangeTransport.addProgressListener(((t,e)=>{this._onProgress({loaded:t,total:e})}));this._pdfDataRangeTransport.addProgressiveReadListener((t=>{this._onReceiveData({chunk:t})}));this._pdfDataRangeTransport.addProgressiveDoneListener((()=>{this._onProgressiveDone()}));this._pdfDataRangeTransport.transportReady()}_onReceiveData(t){const e=new Uint8Array(t.chunk).buffer;if(void 0===t.begin)this._fullRequestReader?this._fullRequestReader._enqueue(e):this._queuedChunks.push(e);else{const s=this._rangeReaders.some((function(s){if(s._begin!==t.begin)return!1;s._enqueue(e);return!0}));(0,n.assert)(s,"_onReceiveData - no `PDFDataTransportStreamRangeReader` instance found.")}}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}_onProgress(t){if(void 0===t.total){const e=this._rangeReaders[0];e?.onProgress&&e.onProgress({loaded:t.loaded})}else{const e=this._fullRequestReader;e?.onProgress&&e.onProgress({loaded:t.loaded,total:t.total})}}_onProgressiveDone(){this._fullRequestReader&&this._fullRequestReader.progressiveDone();this._progressiveDone=!0}_removeRangeReader(t){const e=this._rangeReaders.indexOf(t);e>=0&&this._rangeReaders.splice(e,1)}getFullReader(){(0,n.assert)(!this._fullRequestReader,"PDFDataTransportStream.getFullReader can only be called once.");const t=this._queuedChunks;this._queuedChunks=null;return new PDFDataTransportStreamReader(this,t,this._progressiveDone,this._contentDispositionFilename)}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new PDFDataTransportStreamRangeReader(this,t,e);this._pdfDataRangeTransport.requestDataRange(t,e);this._rangeReaders.push(s);return s}cancelAllRequests(t){this._fullRequestReader&&this._fullRequestReader.cancel(t);for(const e of this._rangeReaders.slice(0))e.cancel(t);this._pdfDataRangeTransport.abort()}};class PDFDataTransportStreamReader{constructor(t,e,s=!1,n=null){this._stream=t;this._done=s||!1;this._filename=(0,i.isPdfFile)(n)?n:null;this._queuedChunks=e||[];this._loaded=0;for(const t of this._queuedChunks)this._loaded+=t.byteLength;this._requests=[];this._headersReady=Promise.resolve();t._fullRequestReader=this;this.onProgress=null}_enqueue(t){if(!this._done){if(this._requests.length>0){this._requests.shift().resolve({value:t,done:!1})}else this._queuedChunks.push(t);this._loaded+=t.byteLength}}get headersReady(){return this._headersReady}get filename(){return this._filename}get isRangeSupported(){return this._stream._isRangeSupported}get isStreamingSupported(){return this._stream._isStreamingSupported}get contentLength(){return this._stream._contentLength}async read(){if(this._queuedChunks.length>0){return{value:this._queuedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=(0,n.createPromiseCapability)();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}progressiveDone(){this._done||(this._done=!0)}}class PDFDataTransportStreamRangeReader{constructor(t,e,s){this._stream=t;this._begin=e;this._end=s;this._queuedChunk=null;this._requests=[];this._done=!1;this.onProgress=null}_enqueue(t){if(!this._done){if(0===this._requests.length)this._queuedChunk=t;else{this._requests.shift().resolve({value:t,done:!1});for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}this._done=!0;this._stream._removeRangeReader(this)}}get isStreamingSupported(){return!1}async read(){if(this._queuedChunk){const t=this._queuedChunk;this._queuedChunk=null;return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=(0,n.createPromiseCapability)();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._stream._removeRangeReader(this)}}},(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0});e.XfaText=void 0;class XfaText{static textContent(t){const e=[],s={items:e,styles:Object.create(null)};!function walk(t){if(!t)return;let s=null;const n=t.name;if("#text"===n)s=t.value;else{if(!XfaText.shouldBuildText(n))return;t?.attributes?.textContent?s=t.attributes.textContent:t.value&&(s=t.value)}null!==s&&e.push({str:s});if(t.children)for(const e of t.children)walk(e)}(t);return s}static shouldBuildText(t){return!("textarea"===t||"input"===t||"option"===t||"select"===t)}}e.XfaText=XfaText},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.NodeStandardFontDataFactory=e.NodeCanvasFactory=e.NodeCMapReaderFactory=void 0;var n=s(9);const fetchData=function(t){return new Promise(((e,s)=>{require("fs").readFile(t,((t,n)=>{!t&&n?e(new Uint8Array(n)):s(new Error(t))}))}))};class NodeCanvasFactory extends n.BaseCanvasFactory{_createCanvas(t,e){return require("canvas").createCanvas(t,e)}}e.NodeCanvasFactory=NodeCanvasFactory;class NodeCMapReaderFactory extends n.BaseCMapReaderFactory{_fetchData(t,e){return fetchData(t).then((t=>({cMapData:t,compressionType:e})))}}e.NodeCMapReaderFactory=NodeCMapReaderFactory;class NodeStandardFontDataFactory extends n.BaseStandardFontDataFactory{_fetchData(t){return fetchData(t)}}e.NodeStandardFontDataFactory=NodeStandardFontDataFactory},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.AnnotationEditorLayer=void 0;var n=s(7),i=s(1),r=s(23),a=s(24);class AnnotationEditorLayer{#at;#ot=!1;#lt=this.pointerup.bind(this);#ct=this.pointerdown.bind(this);#ht=new Map;#dt=!1;#ut=!1;#pt;static _initialized=!1;constructor(t){if(!AnnotationEditorLayer._initialized){AnnotationEditorLayer._initialized=!0;r.FreeTextEditor.initialize(t.l10n);a.InkEditor.initialize(t.l10n);t.uiManager.registerEditorTypes([r.FreeTextEditor,a.InkEditor])}this.#pt=t.uiManager;this.annotationStorage=t.annotationStorage;this.pageIndex=t.pageIndex;this.div=t.div;this.#at=t.accessibilityManager;this.#pt.addLayer(this)}updateToolbar(t){this.#pt.updateToolbar(t)}updateMode(t=this.#pt.getMode()){this.#gt();if(t===i.AnnotationEditorType.INK){this.addInkEditorIfNeeded(!1);this.disableClick()}else this.enableClick();this.#pt.unselectAll()}addInkEditorIfNeeded(t){if(!t&&this.#pt.getMode()!==i.AnnotationEditorType.INK)return;if(!t)for(const t of this.#ht.values())if(t.isEmpty()){t.setInBackground();return}this.#ft({offsetX:0,offsetY:0}).setInBackground()}setEditingState(t){this.#pt.setEditingState(t)}addCommands(t){this.#pt.addCommands(t)}enable(){this.div.style.pointerEvents="auto";for(const t of this.#ht.values())t.enableEditing()}disable(){this.div.style.pointerEvents="none";for(const t of this.#ht.values())t.disableEditing()}setActiveEditor(t){this.#pt.getActive()!==t&&this.#pt.setActiveEditor(t)}enableClick(){this.div.addEventListener("pointerdown",this.#ct);this.div.addEventListener("pointerup",this.#lt)}disableClick(){this.div.removeEventListener("pointerdown",this.#ct);this.div.removeEventListener("pointerup",this.#lt)}attach(t){this.#ht.set(t.id,t)}detach(t){this.#ht.delete(t.id);this.#at?.removePointerInTextLayer(t.contentDiv)}remove(t){this.#pt.removeEditor(t);this.detach(t);this.annotationStorage.remove(t.id);t.div.style.display="none";setTimeout((()=>{t.div.style.display="";t.div.remove();t.isAttachedToDOM=!1;document.activeElement===document.body&&this.#pt.focusMainContainer()}),0);this.#ut||this.addInkEditorIfNeeded(!1)}#mt(t){if(t.parent!==this){this.attach(t);t.pageIndex=this.pageIndex;t.parent?.detach(t);t.parent=this;if(t.div&&t.isAttachedToDOM){t.div.remove();this.div.append(t.div)}}}add(t){this.#mt(t);this.#pt.addEditor(t);this.attach(t);if(!t.isAttachedToDOM){const e=t.render();this.div.append(e);t.isAttachedToDOM=!0}this.moveEditorInDOM(t);t.onceAdded();this.addToAnnotationStorage(t)}moveEditorInDOM(t){this.#at?.moveElementInDOM(this.div,t.div,t.contentDiv,!0)}addToAnnotationStorage(t){t.isEmpty()||this.annotationStorage.has(t.id)||this.annotationStorage.setValue(t.id,t)}addOrRebuild(t){t.needsToBeRebuilt()?t.rebuild():this.add(t)}addANewEditor(t){this.addCommands({cmd:()=>{this.addOrRebuild(t)},undo:()=>{t.remove()},mustExec:!0})}addUndoableEditor(t){this.addCommands({cmd:()=>{this.addOrRebuild(t)},undo:()=>{t.remove()},mustExec:!1})}getNextId(){return this.#pt.getId()}#bt(t){switch(this.#pt.getMode()){case i.AnnotationEditorType.FREETEXT:return new r.FreeTextEditor(t);case i.AnnotationEditorType.INK:return new a.InkEditor(t)}return null}deserialize(t){switch(t.annotationType){case i.AnnotationEditorType.FREETEXT:return r.FreeTextEditor.deserialize(t,this);case i.AnnotationEditorType.INK:return a.InkEditor.deserialize(t,this)}return null}#ft(t){const e=this.getNextId(),s=this.#bt({parent:this,id:e,x:t.offsetX,y:t.offsetY});s&&this.add(s);return s}setSelected(t){this.#pt.setSelected(t)}toggleSelected(t){this.#pt.toggleSelected(t)}isSelected(t){return this.#pt.isSelected(t)}unselect(t){this.#pt.unselect(t)}pointerup(t){const e=n.KeyboardManager.platform.isMac;if(!(0!==t.button||t.ctrlKey&&e)&&t.target===this.div&&this.#dt){this.#dt=!1;this.#ot?this.#ft(t):this.#ot=!0}}pointerdown(t){const e=n.KeyboardManager.platform.isMac;if(0!==t.button||t.ctrlKey&&e)return;if(t.target!==this.div)return;this.#dt=!0;const s=this.#pt.getActive();this.#ot=!s||s.isEmpty()}drop(t){const e=t.dataTransfer.getData("text/plain"),s=this.#pt.getEditor(e);if(!s)return;t.preventDefault();t.dataTransfer.dropEffect="move";this.#mt(s);const n=this.div.getBoundingClientRect(),i=t.clientX-n.x,r=t.clientY-n.y;s.translate(i-s.startX,r-s.startY);this.moveEditorInDOM(s);s.div.focus()}dragover(t){t.preventDefault()}destroy(){this.#pt.getActive()?.parent===this&&this.#pt.setActiveEditor(null);for(const t of this.#ht.values()){this.#at?.removePointerInTextLayer(t.contentDiv);t.isAttachedToDOM=!1;t.div.remove();t.parent=null}this.div=null;this.#ht.clear();this.#pt.removeLayer(this)}#gt(){this.#ut=!0;for(const t of this.#ht.values())t.isEmpty()&&t.remove();this.#ut=!1}render(t){this.viewport=t.viewport;(0,n.bindEvents)(this,this.div,["dragover","drop"]);this.setDimensions();for(const t of this.#pt.getEditors(this.pageIndex))this.add(t);this.updateMode()}update(t){this.viewport=t.viewport;this.setDimensions();this.updateMode()}get scaleFactor(){return this.viewport.scale}get pageDimensions(){const[t,e,s,n]=this.viewport.viewBox;return[s-t,n-e]}get viewportBaseDimensions(){const{width:t,height:e,rotation:s}=this.viewport;return s%180==0?[t,e]:[e,t]}setDimensions(){const{width:t,height:e,rotation:s}=this.viewport,n=s%180!=0,i=Math.floor(t)+"px",r=Math.floor(e)+"px";this.div.style.width=n?r:i;this.div.style.height=n?i:r;this.div.setAttribute("data-main-rotation",s)}}e.AnnotationEditorLayer=AnnotationEditorLayer},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.FreeTextEditor=void 0;var n=s(1),i=s(7),r=s(6);class FreeTextEditor extends r.AnnotationEditor{#At=this.editorDivBlur.bind(this);#_t=this.editorDivFocus.bind(this);#yt=this.editorDivKeydown.bind(this);#vt;#St="";#xt=!1;#Et;static _freeTextDefaultContent="";static _l10nPromise;static _internalPadding=0;static _defaultColor=null;static _defaultFontSize=10;static _keyboardManager=new i.KeyboardManager([[["ctrl+Enter","mac+meta+Enter","Escape","mac+Escape"],FreeTextEditor.prototype.commitOrRemove]]);static _type="freetext";constructor(t){super({...t,name:"freeTextEditor"});this.#vt=t.color||FreeTextEditor._defaultColor||r.AnnotationEditor._defaultLineColor;this.#Et=t.fontSize||FreeTextEditor._defaultFontSize}static initialize(t){this._l10nPromise=new Map(["free_text_default_content","editor_free_text_aria_label"].map((e=>[e,t.get(e)])));const e=getComputedStyle(document.documentElement);this._internalPadding=parseFloat(e.getPropertyValue("--freetext-padding"))}static updateDefaultParams(t,e){switch(t){case n.AnnotationEditorParamsType.FREETEXT_SIZE:FreeTextEditor._defaultFontSize=e;break;case n.AnnotationEditorParamsType.FREETEXT_COLOR:FreeTextEditor._defaultColor=e}}updateParams(t,e){switch(t){case n.AnnotationEditorParamsType.FREETEXT_SIZE:this.#Ct(e);break;case n.AnnotationEditorParamsType.FREETEXT_COLOR:this.#Pt(e)}}static get defaultPropertiesToUpdate(){return[[n.AnnotationEditorParamsType.FREETEXT_SIZE,FreeTextEditor._defaultFontSize],[n.AnnotationEditorParamsType.FREETEXT_COLOR,FreeTextEditor._defaultColor||r.AnnotationEditor._defaultLineColor]]}get propertiesToUpdate(){return[[n.AnnotationEditorParamsType.FREETEXT_SIZE,this.#Et],[n.AnnotationEditorParamsType.FREETEXT_COLOR,this.#vt]]}#Ct(t){const setFontsize=t=>{this.editorDiv.style.fontSize=`calc(${t}px * var(--scale-factor))`;this.translate(0,-(t-this.#Et)*this.parent.scaleFactor);this.#Et=t;this.#Tt()},e=this.#Et;this.parent.addCommands({cmd:()=>{setFontsize(t)},undo:()=>{setFontsize(e)},mustExec:!0,type:n.AnnotationEditorParamsType.FREETEXT_SIZE,overwriteIfSameType:!0,keepUndo:!0})}#Pt(t){const e=this.#vt;this.parent.addCommands({cmd:()=>{this.#vt=t;this.editorDiv.style.color=t},undo:()=>{this.#vt=e;this.editorDiv.style.color=e},mustExec:!0,type:n.AnnotationEditorParamsType.FREETEXT_COLOR,overwriteIfSameType:!0,keepUndo:!0})}getInitialTranslation(){return[-FreeTextEditor._internalPadding*this.parent.scaleFactor,-(FreeTextEditor._internalPadding+this.#Et)*this.parent.scaleFactor]}rebuild(){super.rebuild();null!==this.div&&(this.isAttachedToDOM||this.parent.add(this))}enableEditMode(){if(!this.isInEditMode()){this.parent.setEditingState(!1);this.parent.updateToolbar(n.AnnotationEditorType.FREETEXT);super.enableEditMode();this.enableEditing();this.overlayDiv.classList.remove("enabled");this.editorDiv.contentEditable=!0;this.div.draggable=!1;this.editorDiv.addEventListener("keydown",this.#yt);this.editorDiv.addEventListener("focus",this.#_t);this.editorDiv.addEventListener("blur",this.#At)}}disableEditMode(){if(this.isInEditMode()){this.parent.setEditingState(!0);super.disableEditMode();this.disableEditing();this.overlayDiv.classList.add("enabled");this.editorDiv.contentEditable=!1;this.div.draggable=!0;this.editorDiv.removeEventListener("keydown",this.#yt);this.editorDiv.removeEventListener("focus",this.#_t);this.editorDiv.removeEventListener("blur",this.#At);this.div.focus();this.isEditing=!1}}focusin(t){super.focusin(t);t.target!==this.editorDiv&&this.editorDiv.focus()}onceAdded(){if(!this.width){this.enableEditMode();this.editorDiv.focus()}}isEmpty(){return!this.editorDiv||""===this.editorDiv.innerText.trim()}remove(){this.isEditing=!1;this.parent.setEditingState(!0);super.remove()}#wt(){const t=this.editorDiv.getElementsByTagName("div");if(0===t.length)return this.editorDiv.innerText;const e=[];for(let s=0,n=t.length;s<n;s++){const n=t[s].firstChild;"#text"===n?.nodeName?e.push(n.data):e.push("")}return e.join("\n")}#Tt(){const[t,e]=this.parent.viewportBaseDimensions,s=this.div.getBoundingClientRect();this.width=s.width/t;this.height=s.height/e}commit(){super.commit();if(!this.#xt){this.#xt=!0;this.parent.addUndoableEditor(this)}this.disableEditMode();this.#St=this.#wt().trimEnd();this.#Tt()}shouldGetKeyboardEvents(){return this.isInEditMode()}dblclick(t){this.enableEditMode();this.editorDiv.focus()}keydown(t){if(t.target===this.div&&"Enter"===t.key){this.enableEditMode();this.editorDiv.focus()}}editorDivKeydown(t){FreeTextEditor._keyboardManager.exec(this,t)}editorDivFocus(t){this.isEditing=!0}editorDivBlur(t){this.isEditing=!1}disableEditing(){this.editorDiv.setAttribute("role","comment");this.editorDiv.removeAttribute("aria-multiline")}enableEditing(){this.editorDiv.setAttribute("role","textbox");this.editorDiv.setAttribute("aria-multiline",!0)}render(){if(this.div)return this.div;let t,e;if(this.width){t=this.x;e=this.y}super.render();this.editorDiv=document.createElement("div");this.editorDiv.className="internal";this.editorDiv.setAttribute("id",`${this.id}-editor`);this.enableEditing();FreeTextEditor._l10nPromise.get("editor_free_text_aria_label").then((t=>this.editorDiv?.setAttribute("aria-label",t)));FreeTextEditor._l10nPromise.get("free_text_default_content").then((t=>this.editorDiv?.setAttribute("default-content",t)));this.editorDiv.contentEditable=!0;const{style:s}=this.editorDiv;s.fontSize=`calc(${this.#Et}px * var(--scale-factor))`;s.color=this.#vt;this.div.append(this.editorDiv);this.overlayDiv=document.createElement("div");this.overlayDiv.classList.add("overlay","enabled");this.div.append(this.overlayDiv);(0,i.bindEvents)(this,this.div,["dblclick","keydown"]);if(this.width){const[s,n]=this.parent.viewportBaseDimensions;this.setAt(t*s,e*n,this.width*s,this.height*n);for(const t of this.#St.split("\n")){const e=document.createElement("div");e.append(t?document.createTextNode(t):document.createElement("br"));this.editorDiv.append(e)}this.div.draggable=!0;this.editorDiv.contentEditable=!1}else{this.div.draggable=!1;this.editorDiv.contentEditable=!0}return this.div}get contentDiv(){return this.editorDiv}static deserialize(t,e){const s=super.deserialize(t,e);s.#Et=t.fontSize;s.#vt=n.Util.makeHexColor(...t.color);s.#St=t.value;return s}serialize(){if(this.isEmpty())return null;const t=FreeTextEditor._internalPadding*this.parent.scaleFactor,e=this.getRect(t,t),s=r.AnnotationEditor._colorManager.convert(getComputedStyle(this.editorDiv).color);return{annotationType:n.AnnotationEditorType.FREETEXT,color:s,fontSize:this.#Et,value:this.#St,pageIndex:this.parent.pageIndex,rect:e,rotation:this.rotation}}}e.FreeTextEditor=FreeTextEditor},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.InkEditor=void 0;Object.defineProperty(e,"fitCurve",{enumerable:!0,get:function(){return r.fitCurve}});var n=s(1),i=s(6),r=s(25),a=s(7);const o=16;class InkEditor extends i.AnnotationEditor{#kt=0;#Ft=0;#Mt=0;#Rt=this.canvasPointermove.bind(this);#Dt=this.canvasPointerleave.bind(this);#It=this.canvasPointerup.bind(this);#Ot=this.canvasPointerdown.bind(this);#Lt=!1;#Nt=!1;#Bt=null;#jt=null;#Ut=0;#qt=0;#Wt=null;static _defaultColor=null;static _defaultOpacity=1;static _defaultThickness=1;static _l10nPromise;static _type="ink";constructor(t){super({...t,name:"inkEditor"});this.color=t.color||null;this.thickness=t.thickness||null;this.opacity=t.opacity||null;this.paths=[];this.bezierPath2D=[];this.currentPath=[];this.scaleFactor=1;this.translationX=this.translationY=0;this.x=0;this.y=0}static initialize(t){this._l10nPromise=new Map(["editor_ink_canvas_aria_label","editor_ink_aria_label"].map((e=>[e,t.get(e)])))}static updateDefaultParams(t,e){switch(t){case n.AnnotationEditorParamsType.INK_THICKNESS:InkEditor._defaultThickness=e;break;case n.AnnotationEditorParamsType.INK_COLOR:InkEditor._defaultColor=e;break;case n.AnnotationEditorParamsType.INK_OPACITY:InkEditor._defaultOpacity=e/100}}updateParams(t,e){switch(t){case n.AnnotationEditorParamsType.INK_THICKNESS:this.#zt(e);break;case n.AnnotationEditorParamsType.INK_COLOR:this.#Pt(e);break;case n.AnnotationEditorParamsType.INK_OPACITY:this.#Gt(e)}}static get defaultPropertiesToUpdate(){return[[n.AnnotationEditorParamsType.INK_THICKNESS,InkEditor._defaultThickness],[n.AnnotationEditorParamsType.INK_COLOR,InkEditor._defaultColor||i.AnnotationEditor._defaultLineColor],[n.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*InkEditor._defaultOpacity)]]}get propertiesToUpdate(){return[[n.AnnotationEditorParamsType.INK_THICKNESS,this.thickness||InkEditor._defaultThickness],[n.AnnotationEditorParamsType.INK_COLOR,this.color||InkEditor._defaultColor||i.AnnotationEditor._defaultLineColor],[n.AnnotationEditorParamsType.INK_OPACITY,Math.round(100*(this.opacity??InkEditor._defaultOpacity))]]}#zt(t){const e=this.thickness;this.parent.addCommands({cmd:()=>{this.thickness=t;this.#Ht()},undo:()=>{this.thickness=e;this.#Ht()},mustExec:!0,type:n.AnnotationEditorParamsType.INK_THICKNESS,overwriteIfSameType:!0,keepUndo:!0})}#Pt(t){const e=this.color;this.parent.addCommands({cmd:()=>{this.color=t;this.#Vt()},undo:()=>{this.color=e;this.#Vt()},mustExec:!0,type:n.AnnotationEditorParamsType.INK_COLOR,overwriteIfSameType:!0,keepUndo:!0})}#Gt(t){t/=100;const e=this.opacity;this.parent.addCommands({cmd:()=>{this.opacity=t;this.#Vt()},undo:()=>{this.opacity=e;this.#Vt()},mustExec:!0,type:n.AnnotationEditorParamsType.INK_OPACITY,overwriteIfSameType:!0,keepUndo:!0})}rebuild(){super.rebuild();if(null!==this.div){if(!this.canvas){this.#Xt();this.#$t()}if(!this.isAttachedToDOM){this.parent.add(this);this.#Yt()}this.#Ht()}}remove(){if(null!==this.canvas){this.isEmpty()||this.commit();this.canvas.width=this.canvas.height=0;this.canvas.remove();this.canvas=null;this.#jt.disconnect();this.#jt=null;super.remove()}}enableEditMode(){if(!this.#Lt&&null!==this.canvas){super.enableEditMode();this.div.draggable=!1;this.canvas.addEventListener("pointerdown",this.#Ot);this.canvas.addEventListener("pointerup",this.#It)}}disableEditMode(){if(this.isInEditMode()&&null!==this.canvas){super.disableEditMode();this.div.draggable=!this.isEmpty();this.div.classList.remove("editing");this.canvas.removeEventListener("pointerdown",this.#Ot);this.canvas.removeEventListener("pointerup",this.#It)}}onceAdded(){this.div.draggable=!this.isEmpty()}isEmpty(){return 0===this.paths.length||1===this.paths.length&&0===this.paths[0].length}#Kt(){const{width:t,height:e,rotation:s}=this.parent.viewport;switch(s){case 90:return[0,t,t,e];case 180:return[t,e,t,e];case 270:return[e,0,t,e];default:return[0,0,t,e]}}#Jt(){this.ctx.lineWidth=this.thickness*this.parent.scaleFactor/this.scaleFactor;this.ctx.lineCap="round";this.ctx.lineJoin="round";this.ctx.miterLimit=10;this.ctx.strokeStyle=`${this.color}${(0,a.opacityToHex)(this.opacity)}`}#Qt(t,e){this.isEditing=!0;if(!this.#Nt){this.#Nt=!0;this.#Yt();this.thickness||=InkEditor._defaultThickness;this.color||=InkEditor._defaultColor||i.AnnotationEditor._defaultLineColor;this.opacity??=InkEditor._defaultOpacity}this.currentPath.push([t,e]);this.#Bt=null;this.#Jt();this.ctx.beginPath();this.ctx.moveTo(t,e);this.#Wt=()=>{if(this.#Wt){if(this.#Bt){if(this.isEmpty()){this.ctx.setTransform(1,0,0,1,0,0);this.ctx.clearRect(0,0,this.canvas.width,this.canvas.height)}else this.#Vt();this.ctx.lineTo(...this.#Bt);this.#Bt=null;this.ctx.stroke()}window.requestAnimationFrame(this.#Wt)}};window.requestAnimationFrame(this.#Wt)}#Zt(t,e){const[s,n]=this.currentPath.at(-1);if(t!==s||e!==n){this.currentPath.push([t,e]);this.#Bt=[t,e]}}#te(t,e){this.ctx.closePath();this.#Wt=null;t=Math.min(Math.max(t,0),this.canvas.width);e=Math.min(Math.max(e,0),this.canvas.height);const[s,n]=this.currentPath.at(-1);t===s&&e===n||this.currentPath.push([t,e]);let i;if(1!==this.currentPath.length)i=(0,r.fitCurve)(this.currentPath,30,null);else{const s=[t,e];i=[[s,s.slice(),s.slice(),s]]}const a=InkEditor.#ee(i);this.currentPath.length=0;this.parent.addCommands({cmd:()=>{this.paths.push(i);this.bezierPath2D.push(a);this.rebuild()},undo:()=>{this.paths.pop();this.bezierPath2D.pop();if(0===this.paths.length)this.remove();else{if(!this.canvas){this.#Xt();this.#$t()}this.#Ht()}},mustExec:!0})}#Vt(){if(this.isEmpty()){this.#se();return}this.#Jt();const{canvas:t,ctx:e}=this;e.setTransform(1,0,0,1,0,0);e.clearRect(0,0,t.width,t.height);this.#se();for(const t of this.bezierPath2D)e.stroke(t)}commit(){if(!this.#Lt){super.commit();this.isEditing=!1;this.disableEditMode();this.setInForeground();this.#Lt=!0;this.div.classList.add("disabled");this.#Ht(!0);this.parent.addInkEditorIfNeeded(!0);this.parent.moveEditorInDOM(this);this.div.focus()}}focusin(t){super.focusin(t);this.enableEditMode()}canvasPointerdown(t){if(0===t.button&&this.isInEditMode()&&!this.#Lt){this.setInForeground();"mouse"!==t.type&&this.div.focus();t.stopPropagation();this.canvas.addEventListener("pointerleave",this.#Dt);this.canvas.addEventListener("pointermove",this.#Rt);this.#Qt(t.offsetX,t.offsetY)}}canvasPointermove(t){t.stopPropagation();this.#Zt(t.offsetX,t.offsetY)}canvasPointerup(t){if(0===t.button&&this.isInEditMode()&&0!==this.currentPath.length){t.stopPropagation();this.#ne(t);this.setInBackground()}}canvasPointerleave(t){this.#ne(t);this.setInBackground()}#ne(t){this.#te(t.offsetX,t.offsetY);this.canvas.removeEventListener("pointerleave",this.#Dt);this.canvas.removeEventListener("pointermove",this.#Rt);this.parent.addToAnnotationStorage(this)}#Xt(){this.canvas=document.createElement("canvas");this.canvas.width=this.canvas.height=0;this.canvas.className="inkEditorCanvas";InkEditor._l10nPromise.get("editor_ink_canvas_aria_label").then((t=>this.canvas?.setAttribute("aria-label",t)));this.div.append(this.canvas);this.ctx=this.canvas.getContext("2d")}#$t(){this.#jt=new ResizeObserver((t=>{const e=t[0].contentRect;e.width&&e.height&&this.setDimensions(e.width,e.height)}));this.#jt.observe(this.div)}render(){if(this.div)return this.div;let t,e;if(this.width){t=this.x;e=this.y}super.render();InkEditor._l10nPromise.get("editor_ink_aria_label").then((t=>this.div?.setAttribute("aria-label",t)));const[s,n,i,r]=this.#Kt();this.setAt(s,n,0,0);this.setDims(i,r);this.#Xt();if(this.width){const[s,n]=this.parent.viewportBaseDimensions;this.setAt(t*s,e*n,this.width*s,this.height*n);this.#Nt=!0;this.#Yt();this.setDims(this.width*s,this.height*n);this.#Vt();this.#ie();this.div.classList.add("disabled")}else{this.div.classList.add("editing");this.enableEditMode()}this.#$t();return this.div}#Yt(){if(!this.#Nt)return;const[t,e]=this.parent.viewportBaseDimensions;this.canvas.width=Math.ceil(this.width*t);this.canvas.height=Math.ceil(this.height*e);this.#se()}setDimensions(t,e){const s=Math.round(t),n=Math.round(e);if(this.#Ut===s&&this.#qt===n)return;this.#Ut=s;this.#qt=n;this.canvas.style.visibility="hidden";if(this.#kt&&Math.abs(this.#kt-t/e)>.01){e=Math.ceil(t/this.#kt);this.setDims(t,e)}const[i,r]=this.parent.viewportBaseDimensions;this.width=t/i;this.height=e/r;this.#Lt&&this.#re(t,e);this.#Yt();this.#Vt();this.canvas.style.visibility="visible"}#re(t,e){const s=this.#ae(),n=(t-s)/this.#Mt,i=(e-s)/this.#Ft;this.scaleFactor=Math.min(n,i)}#se(){const t=this.#ae()/2;this.ctx.setTransform(this.scaleFactor,0,0,this.scaleFactor,this.translationX*this.scaleFactor+t,this.translationY*this.scaleFactor+t)}static#ee(t){const e=new Path2D;for(let s=0,n=t.length;s<n;s++){const[n,i,r,a]=t[s];0===s&&e.moveTo(...n);e.bezierCurveTo(i[0],i[1],r[0],r[1],a[0],a[1])}return e}#oe(t,e,s,n){const i=[],r=this.thickness/2;let a,o;for(const l of this.paths){a=[];o=[];for(let i=0,c=l.length;i<c;i++){const[c,h,d,u]=l[i],p=t*(c[0]+e)+r,g=n-t*(c[1]+s)-r,f=t*(h[0]+e)+r,m=n-t*(h[1]+s)-r,b=t*(d[0]+e)+r,A=n-t*(d[1]+s)-r,_=t*(u[0]+e)+r,y=n-t*(u[1]+s)-r;if(0===i){a.push(p,g);o.push(p,g)}a.push(f,m,b,A,_,y);this.#le(p,g,f,m,b,A,_,y,4,o)}i.push({bezier:a,points:o})}return i}#le(t,e,s,n,i,r,a,o,l,c){if(this.#ce(t,e,s,n,i,r,a,o))c.push(a,o);else{for(let h=1;h<l-1;h++){const d=h/l,u=1-d;let p=d*t+u*s,g=d*e+u*n,f=d*s+u*i,m=d*n+u*r;p=d*p+u*f;g=d*g+u*m;f=d*f+u*(d*i+u*a);m=d*m+u*(d*r+u*o);p=d*p+u*f;g=d*g+u*m;c.push(p,g)}c.push(a,o)}}#ce(t,e,s,n,i,r,a,o){const l=(3*s-2*t-a)**2,c=(3*n-2*e-o)**2,h=(3*i-t-2*a)**2,d=(3*r-e-2*o)**2;return Math.max(l,h)+Math.max(c,d)<=10}#he(){let t=1/0,e=-1/0,s=1/0,i=-1/0;for(const r of this.paths)for(const[a,o,l,c]of r){const r=n.Util.bezierBoundingBox(...a,...o,...l,...c);t=Math.min(t,r[0]);s=Math.min(s,r[1]);e=Math.max(e,r[2]);i=Math.max(i,r[3])}return[t,s,e,i]}#ae(){return this.#Lt?Math.ceil(this.thickness*this.parent.scaleFactor):0}#Ht(t=!1){if(this.isEmpty())return;if(!this.#Lt){this.#Vt();return}const e=this.#he(),s=this.#ae();this.#Mt=Math.max(o,e[2]-e[0]);this.#Ft=Math.max(o,e[3]-e[1]);const n=Math.ceil(s+this.#Mt*this.scaleFactor),i=Math.ceil(s+this.#Ft*this.scaleFactor),[r,a]=this.parent.viewportBaseDimensions;this.width=n/r;this.height=i/a;this.#kt=n/i;this.#ie();const l=this.translationX,c=this.translationY;this.translationX=-e[0];this.translationY=-e[1];this.#Yt();this.#Vt();this.#Ut=n;this.#qt=i;this.setDims(n,i);const h=t?s/this.scaleFactor/2:0;this.translate(l-this.translationX-h,c-this.translationY-h)}#ie(){const{style:t}=this.div;if(this.#kt>=1){t.minHeight="16px";t.minWidth=`${Math.round(this.#kt*o)}px`}else{t.minWidth="16px";t.minHeight=`${Math.round(o/this.#kt)}px`}}static deserialize(t,e){const s=super.deserialize(t,e);s.thickness=t.thickness;s.color=n.Util.makeHexColor(...t.color);s.opacity=t.opacity;const[i,r]=e.pageDimensions,a=s.width*i,l=s.height*r,c=e.scaleFactor,h=t.thickness/2;s.#kt=a/l;s.#Lt=!0;s.#Ut=Math.round(a);s.#qt=Math.round(l);for(const{bezier:e}of t.paths){const t=[];s.paths.push(t);let n=c*(e[0]-h),i=c*(l-e[1]-h);for(let s=2,r=e.length;s<r;s+=6){const r=c*(e[s]-h),a=c*(l-e[s+1]-h),o=c*(e[s+2]-h),d=c*(l-e[s+3]-h),u=c*(e[s+4]-h),p=c*(l-e[s+5]-h);t.push([[n,i],[r,a],[o,d],[u,p]]);n=u;i=p}const r=this.#ee(t);s.bezierPath2D.push(r)}const d=s.#he();s.#Mt=Math.max(o,d[2]-d[0]);s.#Ft=Math.max(o,d[3]-d[1]);s.#re(a,l);return s}serialize(){if(this.isEmpty())return null;const t=this.getRect(0,0),e=this.rotation%180==0?t[3]-t[1]:t[2]-t[0],s=i.AnnotationEditor._colorManager.convert(this.ctx.strokeStyle);return{annotationType:n.AnnotationEditorType.INK,color:s,thickness:this.thickness,opacity:this.opacity,paths:this.#oe(this.scaleFactor/this.parent.scaleFactor,this.translationX,this.translationY,e),pageIndex:this.parent.pageIndex,rect:t,rotation:this.rotation}}}e.InkEditor=InkEditor},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.fitCurve=void 0;const n=s(26);e.fitCurve=n},t=>{function fitCubic(t,e,s,n,i){var r,a,o,l,c,h,d,u,p,g,f,m,b;if(2===t.length){m=maths.vectorLen(maths.subtract(t[0],t[1]))/3;return[r=[t[0],maths.addArrays(t[0],maths.mulItems(e,m)),maths.addArrays(t[1],maths.mulItems(s,m)),t[1]]]}a=function chordLengthParameterize(t){var e,s,n,i=[];t.forEach(((t,r)=>{e=r?s+maths.vectorLen(maths.subtract(t,n)):0;i.push(e);s=e;n=t}));return i=i.map((t=>t/s))}(t);[r,l,h]=generateAndReport(t,a,a,e,s,i);if(0===l||l<n)return[r];if(l<n*n){o=a;c=l;d=h;for(b=0;b<20;b++){o=reparameterize(r,t,o);[r,l,h]=generateAndReport(t,a,o,e,s,i);if(l<n)return[r];if(h===d){let t=l/c;if(t>.9999&&t<1.0001)break}c=l;d=h}}f=[];if((u=maths.subtract(t[h-1],t[h+1])).every((t=>0===t))){u=maths.subtract(t[h-1],t[h]);[u[0],u[1]]=[-u[1],u[0]]}p=maths.normalize(u);g=maths.mulItems(p,-1);return f=(f=f.concat(fitCubic(t.slice(0,h+1),e,p,n,i))).concat(fitCubic(t.slice(h),g,s,n,i))}function generateAndReport(t,e,s,n,i,r){var a,o,l;a=function generateBezier(t,e,s,n){var i,r,a,o,l,c,h,d,u,p,g,f,m,b,A,_,y,v=t[0],S=t[t.length-1];i=[v,null,null,S];r=maths.zeros_Xx2x2(e.length);for(m=0,b=e.length;m<b;m++){y=1-(_=e[m]);(a=r[m])[0]=maths.mulItems(s,3*_*(y*y));a[1]=maths.mulItems(n,3*y*(_*_))}o=[[0,0],[0,0]];l=[0,0];for(m=0,b=t.length;m<b;m++){_=e[m];a=r[m];o[0][0]+=maths.dot(a[0],a[0]);o[0][1]+=maths.dot(a[0],a[1]);o[1][0]+=maths.dot(a[0],a[1]);o[1][1]+=maths.dot(a[1],a[1]);A=maths.subtract(t[m],bezier.q([v,v,S,S],_));l[0]+=maths.dot(a[0],A);l[1]+=maths.dot(a[1],A)}c=o[0][0]*o[1][1]-o[1][0]*o[0][1];h=o[0][0]*l[1]-o[1][0]*l[0];d=l[0]*o[1][1]-l[1]*o[0][1];u=0===c?0:d/c;p=0===c?0:h/c;f=maths.vectorLen(maths.subtract(v,S));if(u<(g=1e-6*f)||p<g){i[1]=maths.addArrays(v,maths.mulItems(s,f/3));i[2]=maths.addArrays(S,maths.mulItems(n,f/3))}else{i[1]=maths.addArrays(v,maths.mulItems(s,u));i[2]=maths.addArrays(S,maths.mulItems(n,p))}return i}(t,s,n,i);[o,l]=function computeMaxError(t,e,s){var n,i,r,a,o,l,c,h;i=0;r=Math.floor(t.length/2);const d=mapTtoRelativeDistances(e,10);for(o=0,l=t.length;o<l;o++){c=t[o];h=find_t(e,s[o],d,10);if((n=(a=maths.subtract(bezier.q(e,h),c))[0]*a[0]+a[1]*a[1])>i){i=n;r=o}}return[i,r]}(t,a,e);r&&r({bez:a,points:t,params:e,maxErr:o,maxPoint:l});return[a,o,l]}function reparameterize(t,e,s){return s.map(((s,n)=>newtonRaphsonRootFind(t,e[n],s)))}function newtonRaphsonRootFind(t,e,s){var n=maths.subtract(bezier.q(t,s),e),i=bezier.qprime(t,s),r=maths.mulMatrix(n,i),a=maths.sum(maths.squareItems(i))+2*maths.mulMatrix(n,bezier.qprimeprime(t,s));return 0===a?s:s-r/a}var mapTtoRelativeDistances=function(t,e){for(var s,n=[0],i=t[0],r=0,a=1;a<=e;a++){s=bezier.q(t,a/e);r+=maths.vectorLen(maths.subtract(s,i));n.push(r);i=s}return n=n.map((t=>t/r))};function find_t(t,e,s,n){if(e<0)return 0;if(e>1)return 1;for(var i,r,a,o,l=1;l<=n;l++)if(e<=s[l]){a=(l-1)/n;r=l/n;o=(e-(i=s[l-1]))/(s[l]-i)*(r-a)+a;break}return o}function createTangent(t,e){return maths.normalize(maths.subtract(t,e))}class maths{static zeros_Xx2x2(t){for(var e=[];t--;)e.push([0,0]);return e}static mulItems(t,e){return t.map((t=>t*e))}static mulMatrix(t,e){return t.reduce(((t,s,n)=>t+s*e[n]),0)}static subtract(t,e){return t.map(((t,s)=>t-e[s]))}static addArrays(t,e){return t.map(((t,s)=>t+e[s]))}static addItems(t,e){return t.map((t=>t+e))}static sum(t){return t.reduce(((t,e)=>t+e))}static dot(t,e){return maths.mulMatrix(t,e)}static vectorLen(t){return Math.hypot(...t)}static divItems(t,e){return t.map((t=>t/e))}static squareItems(t){return t.map((t=>t*t))}static normalize(t){return this.divItems(t,this.vectorLen(t))}}class bezier{static q(t,e){var s=1-e,n=maths.mulItems(t[0],s*s*s),i=maths.mulItems(t[1],3*s*s*e),r=maths.mulItems(t[2],3*s*e*e),a=maths.mulItems(t[3],e*e*e);return maths.addArrays(maths.addArrays(n,i),maths.addArrays(r,a))}static qprime(t,e){var s=1-e,n=maths.mulItems(maths.subtract(t[1],t[0]),3*s*s),i=maths.mulItems(maths.subtract(t[2],t[1]),6*s*e),r=maths.mulItems(maths.subtract(t[3],t[2]),3*e*e);return maths.addArrays(maths.addArrays(n,i),r)}static qprimeprime(t,e){return maths.addArrays(maths.mulItems(maths.addArrays(maths.subtract(t[2],maths.mulItems(t[1],2)),t[0]),6*(1-e)),maths.mulItems(maths.addArrays(maths.subtract(t[3],maths.mulItems(t[2],2)),t[1]),6*e))}}t.exports=function fitCurve(t,e,s){if(!Array.isArray(t))throw new TypeError("First argument should be an array");t.forEach((e=>{if(!Array.isArray(e)||e.some((t=>"number"!=typeof t))||e.length!==t[0].length)throw Error("Each point should be an array of numbers. Each point should have the same amount of numbers.")}));if((t=t.filter(((e,s)=>0===s||!e.every(((e,n)=>e===t[s-1][n]))))).length<2)return[];const n=t.length,i=createTangent(t[1],t[0]),r=createTangent(t[n-2],t[n-1]);return fitCubic(t,i,r,e,s)};t.exports.fitCubic=fitCubic;t.exports.createTangent=createTangent},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.AnnotationLayer=void 0;var n=s(1),i=s(8),r=s(5),a=s(28),o=s(29);const l=1e3,c=new WeakSet;function getRectDims(t){return{width:t[2]-t[0],height:t[3]-t[1]}}class AnnotationElementFactory{static create(t){switch(t.data.annotationType){case n.AnnotationType.LINK:return new LinkAnnotationElement(t);case n.AnnotationType.TEXT:return new TextAnnotationElement(t);case n.AnnotationType.WIDGET:switch(t.data.fieldType){case"Tx":return new TextWidgetAnnotationElement(t);case"Btn":return t.data.radioButton?new RadioButtonWidgetAnnotationElement(t):t.data.checkBox?new CheckboxWidgetAnnotationElement(t):new PushButtonWidgetAnnotationElement(t);case"Ch":return new ChoiceWidgetAnnotationElement(t)}return new WidgetAnnotationElement(t);case n.AnnotationType.POPUP:return new PopupAnnotationElement(t);case n.AnnotationType.FREETEXT:return new FreeTextAnnotationElement(t);case n.AnnotationType.LINE:return new LineAnnotationElement(t);case n.AnnotationType.SQUARE:return new SquareAnnotationElement(t);case n.AnnotationType.CIRCLE:return new CircleAnnotationElement(t);case n.AnnotationType.POLYLINE:return new PolylineAnnotationElement(t);case n.AnnotationType.CARET:return new CaretAnnotationElement(t);case n.AnnotationType.INK:return new InkAnnotationElement(t);case n.AnnotationType.POLYGON:return new PolygonAnnotationElement(t);case n.AnnotationType.HIGHLIGHT:return new HighlightAnnotationElement(t);case n.AnnotationType.UNDERLINE:return new UnderlineAnnotationElement(t);case n.AnnotationType.SQUIGGLY:return new SquigglyAnnotationElement(t);case n.AnnotationType.STRIKEOUT:return new StrikeOutAnnotationElement(t);case n.AnnotationType.STAMP:return new StampAnnotationElement(t);case n.AnnotationType.FILEATTACHMENT:return new FileAttachmentAnnotationElement(t);default:return new AnnotationElement(t)}}}class AnnotationElement{constructor(t,{isRenderable:e=!1,ignoreBorder:s=!1,createQuadrilaterals:n=!1}={}){this.isRenderable=e;this.data=t.data;this.layer=t.layer;this.page=t.page;this.viewport=t.viewport;this.linkService=t.linkService;this.downloadManager=t.downloadManager;this.imageResourcesPath=t.imageResourcesPath;this.renderForms=t.renderForms;this.svgFactory=t.svgFactory;this.annotationStorage=t.annotationStorage;this.enableScripting=t.enableScripting;this.hasJSActions=t.hasJSActions;this._fieldObjects=t.fieldObjects;this._mouseState=t.mouseState;e&&(this.container=this._createContainer(s));n&&(this.quadrilaterals=this._createQuadrilaterals(s))}_createContainer(t=!1){const e=this.data,s=this.page,i=this.viewport,r=document.createElement("section"),{width:a,height:o}=getRectDims(e.rect),[l,c,h,d]=i.viewBox,u=h-l,p=d-c;r.setAttribute("data-annotation-id",e.id);const g=n.Util.normalizeRect([e.rect[0],s.view[3]-e.rect[1]+s.view[1],e.rect[2],s.view[3]-e.rect[3]+s.view[1]]);if(!t&&e.borderStyle.width>0){r.style.borderWidth=`${e.borderStyle.width}px`;const t=e.borderStyle.horizontalCornerRadius,s=e.borderStyle.verticalCornerRadius;if(t>0||s>0){const e=`calc(${t}px * var(--scale-factor)) / calc(${s}px * var(--scale-factor))`;r.style.borderRadius=e}else if(this instanceof RadioButtonWidgetAnnotationElement){const t=`calc(${a}px * var(--scale-factor)) / calc(${o}px * var(--scale-factor))`;r.style.borderRadius=t}switch(e.borderStyle.style){case n.AnnotationBorderStyleType.SOLID:r.style.borderStyle="solid";break;case n.AnnotationBorderStyleType.DASHED:r.style.borderStyle="dashed";break;case n.AnnotationBorderStyleType.BEVELED:(0,n.warn)("Unimplemented border style: beveled");break;case n.AnnotationBorderStyleType.INSET:(0,n.warn)("Unimplemented border style: inset");break;case n.AnnotationBorderStyleType.UNDERLINE:r.style.borderBottomStyle="solid"}const i=e.borderColor||null;i?r.style.borderColor=n.Util.makeHexColor(0|i[0],0|i[1],0|i[2]):r.style.borderWidth=0}r.style.left=100*(g[0]-l)/u+"%";r.style.top=100*(g[1]-c)/p+"%";const{rotation:f}=e;if(e.hasOwnCanvas||0===f){r.style.width=100*a/u+"%";r.style.height=100*o/p+"%"}else this.setRotation(f,r);return r}setRotation(t,e=this.container){const[s,n,i,r]=this.viewport.viewBox,a=i-s,o=r-n,{width:l,height:c}=getRectDims(this.data.rect);let h,d;if(t%180==0){h=100*l/a;d=100*c/o}else{h=100*c/a;d=100*l/o}e.style.width=`${h}%`;e.style.height=`${d}%`;e.setAttribute("data-main-rotation",(360-t)%360)}get _commonActions(){const setColor=(t,e,s)=>{const n=s.detail[t];s.target.style[e]=a.ColorConverters[`${n[0]}_HTML`](n.slice(1))};return(0,n.shadow)(this,"_commonActions",{display:t=>{const e=t.detail.display%2==1;this.container.style.visibility=e?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{hidden:e,print:0===t.detail.display||3===t.detail.display})},print:t=>{this.annotationStorage.setValue(this.data.id,{print:t.detail.print})},hidden:t=>{this.container.style.visibility=t.detail.hidden?"hidden":"visible";this.annotationStorage.setValue(this.data.id,{hidden:t.detail.hidden})},focus:t=>{setTimeout((()=>t.target.focus({preventScroll:!1})),0)},userName:t=>{t.target.title=t.detail.userName},readonly:t=>{t.detail.readonly?t.target.setAttribute("readonly",""):t.target.removeAttribute("readonly")},required:t=>{this._setRequired(t.target,t.detail.required)},bgColor:t=>{setColor("bgColor","backgroundColor",t)},fillColor:t=>{setColor("fillColor","backgroundColor",t)},fgColor:t=>{setColor("fgColor","color",t)},textColor:t=>{setColor("textColor","color",t)},borderColor:t=>{setColor("borderColor","borderColor",t)},strokeColor:t=>{setColor("strokeColor","borderColor",t)},rotation:t=>{const e=t.detail.rotation;this.setRotation(e);this.annotationStorage.setValue(this.data.id,{rotation:e})}})}_dispatchEventFromSandbox(t,e){const s=this._commonActions;for(const n of Object.keys(e.detail)){const i=t[n]||s[n];i&&i(e)}}_setDefaultPropertiesFromJS(t){if(!this.enableScripting)return;const e=this.annotationStorage.getRawValue(this.data.id);if(!e)return;const s=this._commonActions;for(const[n,i]of Object.entries(e)){const r=s[n];if(r){r({detail:{[n]:i},target:t});delete e[n]}}}_createQuadrilaterals(t=!1){if(!this.data.quadPoints)return null;const e=[],s=this.data.rect;for(const s of this.data.quadPoints){this.data.rect=[s[2].x,s[2].y,s[1].x,s[1].y];e.push(this._createContainer(t))}this.data.rect=s;return e}_createPopup(t,e){let s=this.container;if(this.quadrilaterals){t=t||this.quadrilaterals;s=this.quadrilaterals[0]}if(!t){(t=document.createElement("div")).className="popupTriggerArea";s.append(t)}const n=new PopupElement({container:s,trigger:t,color:e.color,titleObj:e.titleObj,modificationDate:e.modificationDate,contentsObj:e.contentsObj,richText:e.richText,hideWrapper:!0}).render();n.style.left="100%";s.append(n)}_renderQuadrilaterals(t){for(const e of this.quadrilaterals)e.className=t;return this.quadrilaterals}render(){(0,n.unreachable)("Abstract method `AnnotationElement.render` called")}_getElementsByName(t,e=null){const s=[];if(this._fieldObjects){const i=this._fieldObjects[t];if(i)for(const{page:t,id:r,exportValues:a}of i){if(-1===t)continue;if(r===e)continue;const i="string"==typeof a?a:null,o=document.querySelector(`[data-element-id="${r}"]`);!o||c.has(o)?s.push({id:r,exportValue:i,domElement:o}):(0,n.warn)(`_getElementsByName - element not allowed: ${r}`)}return s}for(const n of document.getElementsByName(t)){const{id:t,exportValue:i}=n;t!==e&&(c.has(n)&&s.push({id:t,exportValue:i,domElement:n}))}return s}static get platform(){const t="undefined"!=typeof navigator?navigator.platform:"";return(0,n.shadow)(this,"platform",{isWin:t.includes("Win"),isMac:t.includes("Mac")})}}class LinkAnnotationElement extends AnnotationElement{constructor(t,e=null){super(t,{isRenderable:!0,ignoreBorder:!!e?.ignoreBorder,createQuadrilaterals:!0});this.isTooltipOnly=t.data.isTooltipOnly}render(){const{data:t,linkService:e}=this,s=document.createElement("a");s.setAttribute("data-element-id",t.id);let n=!1;if(t.url){e.addLinkAttributes(s,t.url,t.newWindow);n=!0}else if(t.action){this._bindNamedAction(s,t.action);n=!0}else if(t.dest){this._bindLink(s,t.dest);n=!0}else{if(t.actions&&(t.actions.Action||t.actions["Mouse Up"]||t.actions["Mouse Down"])&&this.enableScripting&&this.hasJSActions){this._bindJSAction(s,t);n=!0}if(t.resetForm){this._bindResetFormAction(s,t.resetForm);n=!0}else if(this.isTooltipOnly&&!n){this._bindLink(s,"");n=!0}}if(this.quadrilaterals)return this._renderQuadrilaterals("linkAnnotation").map(((t,e)=>{const n=0===e?s:s.cloneNode();t.append(n);return t}));this.container.className="linkAnnotation";n&&this.container.append(s);return this.container}_bindLink(t,e){t.href=this.linkService.getDestinationHash(e);t.onclick=()=>{e&&this.linkService.goToDestination(e);return!1};(e||""===e)&&(t.className="internalLink")}_bindNamedAction(t,e){t.href=this.linkService.getAnchorUrl("");t.onclick=()=>{this.linkService.executeNamedAction(e);return!1};t.className="internalLink"}_bindJSAction(t,e){t.href=this.linkService.getAnchorUrl("");const s=new Map([["Action","onclick"],["Mouse Up","onmouseup"],["Mouse Down","onmousedown"]]);for(const n of Object.keys(e.actions)){const i=s.get(n);i&&(t[i]=()=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e.id,name:n}});return!1})}t.onclick||(t.onclick=()=>!1);t.className="internalLink"}_bindResetFormAction(t,e){const s=t.onclick;s||(t.href=this.linkService.getAnchorUrl(""));t.className="internalLink";if(this._fieldObjects)t.onclick=()=>{s&&s();const{fields:t,refs:i,include:r}=e,a=[];if(0!==t.length||0!==i.length){const e=new Set(i);for(const s of t){const t=this._fieldObjects[s]||[];for(const{id:s}of t)e.add(s)}for(const t of Object.values(this._fieldObjects))for(const s of t)e.has(s.id)===r&&a.push(s)}else for(const t of Object.values(this._fieldObjects))a.push(...t);const o=this.annotationStorage,l=[];for(const t of a){const{id:e}=t;l.push(e);switch(t.type){case"text":{const s=t.defaultValue||"";o.setValue(e,{value:s});break}case"checkbox":case"radiobutton":{const s=t.defaultValue===t.exportValues;o.setValue(e,{value:s});break}case"combobox":case"listbox":{const s=t.defaultValue||"";o.setValue(e,{value:s});break}default:continue}const s=document.querySelector(`[data-element-id="${e}"]`);s&&(c.has(s)?s.dispatchEvent(new Event("resetform")):(0,n.warn)(`_bindResetFormAction - element not allowed: ${e}`))}this.enableScripting&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:"app",ids:l,name:"ResetForm"}});return!1};else{(0,n.warn)('_bindResetFormAction - "resetForm" action not supported, ensure that the `fieldObjects` parameter is provided.');s||(t.onclick=()=>!1)}}}class TextAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!!(t.data.hasPopup||t.data.titleObj?.str||t.data.contentsObj?.str||t.data.richText?.str)})}render(){this.container.className="textAnnotation";const t=document.createElement("img");t.src=this.imageResourcesPath+"annotation-"+this.data.name.toLowerCase()+".svg";t.alt="[{{type}} Annotation]";t.dataset.l10nId="text_annotation_type";t.dataset.l10nArgs=JSON.stringify({type:this.data.name});this.data.hasPopup||this._createPopup(t,this.data);this.container.append(t);return this.container}}class WidgetAnnotationElement extends AnnotationElement{render(){this.data.alternativeText&&(this.container.title=this.data.alternativeText);return this.container}_getKeyModifier(t){const{isWin:e,isMac:s}=AnnotationElement.platform;return e&&t.ctrlKey||s&&t.metaKey}_setEventListener(t,e,s,n){e.includes("mouse")?t.addEventListener(e,(t=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:n(t),shift:t.shiftKey,modifier:this._getKeyModifier(t)}})})):t.addEventListener(e,(t=>{this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:this.data.id,name:s,value:n(t)}})}))}_setEventListeners(t,e,s){for(const[n,i]of e)("Action"===i||this.data.actions?.[i])&&this._setEventListener(t,n,i,s)}_setBackgroundColor(t){const e=this.data.backgroundColor||null;t.style.backgroundColor=null===e?"transparent":n.Util.makeHexColor(e[0],e[1],e[2])}_setTextStyle(t){const e=["left","center","right"],{fontColor:s}=this.data.defaultAppearanceData,i=this.data.defaultAppearanceData.fontSize||9,r=t.style;let a;if(this.data.multiLine){const t=Math.abs(this.data.rect[3]-this.data.rect[1]),e=t/(Math.round(t/(n.LINE_FACTOR*i))||1);a=Math.min(i,Math.round(e/n.LINE_FACTOR))}else{const t=Math.abs(this.data.rect[3]-this.data.rect[1]);a=Math.min(i,Math.round(t/n.LINE_FACTOR))}r.fontSize=`calc(${a}px * var(--scale-factor))`;r.color=n.Util.makeHexColor(s[0],s[1],s[2]);null!==this.data.textAlignment&&(r.textAlign=e[this.data.textAlignment])}_setRequired(t,e){e?t.setAttribute("required",!0):t.removeAttribute("required");t.setAttribute("aria-required",e)}}class TextWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms||!t.data.hasAppearance&&!!t.data.fieldValue})}setPropertyOnSiblings(t,e,s,n){const i=this.annotationStorage;for(const r of this._getElementsByName(t.name,t.id)){r.domElement&&(r.domElement[e]=s);i.setValue(r.id,{[n]:s})}}render(){const t=this.annotationStorage,e=this.data.id;this.container.className="textWidgetAnnotation";let s=null;if(this.renderForms){const n=t.getValue(e,{value:this.data.fieldValue});let i=n.formattedValue||n.value||"";const r=t.getValue(e,{charLimit:this.data.maxLen}).charLimit;r&&i.length>r&&(i=i.slice(0,r));const a={userValue:i,formattedValue:null,valueOnFocus:""};if(this.data.multiLine){s=document.createElement("textarea");s.textContent=i;this.data.doNotScroll&&(s.style.overflowY="hidden")}else{s=document.createElement("input");s.type="text";s.setAttribute("value",i);this.data.doNotScroll&&(s.style.overflowX="hidden")}c.add(s);s.setAttribute("data-element-id",e);s.disabled=this.data.readOnly;s.name=this.data.fieldName;s.tabIndex=l;this._setRequired(s,this.data.required);r&&(s.maxLength=r);s.addEventListener("input",(n=>{t.setValue(e,{value:n.target.value});this.setPropertyOnSiblings(s,"value",n.target.value,"value")}));s.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue??"";s.value=a.userValue=e;a.formattedValue=null}));let blurListener=t=>{const{formattedValue:e}=a;null!=e&&(t.target.value=e);t.target.scrollLeft=0};if(this.enableScripting&&this.hasJSActions){s.addEventListener("focus",(t=>{a.userValue&&(t.target.value=a.userValue);a.valueOnFocus=t.target.value}));s.addEventListener("updatefromsandbox",(s=>{const n={value(s){a.userValue=s.detail.value??"";t.setValue(e,{value:a.userValue.toString()});s.target.value=a.userValue},formattedValue(s){const{formattedValue:n}=s.detail;a.formattedValue=n;null!=n&&s.target!==document.activeElement&&(s.target.value=n);t.setValue(e,{formattedValue:n})},selRange(t){t.target.setSelectionRange(...t.detail.selRange)},charLimit:s=>{const{charLimit:n}=s.detail,{target:i}=s;if(0===n){i.removeAttribute("maxLength");return}i.setAttribute("maxLength",n);let r=a.userValue;if(r&&!(r.length<=n)){r=r.slice(0,n);i.value=a.userValue=r;t.setValue(e,{value:r});this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:r,willCommit:!0,commitKey:1,selStart:i.selectionStart,selEnd:i.selectionEnd}})}}};this._dispatchEventFromSandbox(n,s)}));s.addEventListener("keydown",(t=>{let s=-1;"Escape"===t.key?s=0:"Enter"===t.key?s=2:"Tab"===t.key&&(s=3);if(-1===s)return;const{value:n}=t.target;if(a.valueOnFocus!==n){a.userValue=n;this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:n,willCommit:!0,commitKey:s,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}})}}));const n=blurListener;blurListener=null;s.addEventListener("blur",(t=>{const{value:s}=t.target;a.userValue=s;this._mouseState.isDown&&a.valueOnFocus!==s&&this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:s,willCommit:!0,commitKey:1,selStart:t.target.selectionStart,selEnd:t.target.selectionEnd}});n(t)}));this.data.actions?.Keystroke&&s.addEventListener("beforeinput",(t=>{const{data:s,target:n}=t,{value:i,selectionStart:r,selectionEnd:a}=n;let o=r,l=a;switch(t.inputType){case"deleteWordBackward":{const t=i.substring(0,r).match(/\w*[^\w]*$/);t&&(o-=t[0].length);break}case"deleteWordForward":{const t=i.substring(r).match(/^[^\w]*\w*/);t&&(l+=t[0].length);break}case"deleteContentBackward":r===a&&(o-=1);break;case"deleteContentForward":r===a&&(l+=1)}t.preventDefault();this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:i,change:s||"",willCommit:!1,selStart:o,selEnd:l}})}));this._setEventListeners(s,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.value))}blurListener&&s.addEventListener("blur",blurListener);if(this.data.comb){const t=(this.data.rect[2]-this.data.rect[0])/r;s.classList.add("comb");s.style.letterSpacing=`calc(${t}px * var(--scale-factor) - 1ch)`}}else{s=document.createElement("div");s.textContent=this.data.fieldValue;s.style.verticalAlign="middle";s.style.display="table-cell"}this._setTextStyle(s);this._setBackgroundColor(s);this._setDefaultPropertiesFromJS(s);this.container.append(s);return this.container}}class CheckboxWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){const t=this.annotationStorage,e=this.data,s=e.id;let n=t.getValue(s,{value:e.exportValue===e.fieldValue}).value;if("string"==typeof n){n="Off"!==n;t.setValue(s,{value:n})}this.container.className="buttonWidgetAnnotation checkBox";const i=document.createElement("input");c.add(i);i.setAttribute("data-element-id",s);i.disabled=e.readOnly;this._setRequired(i,this.data.required);i.type="checkbox";i.name=e.fieldName;n&&i.setAttribute("checked",!0);i.setAttribute("exportValue",e.exportValue);i.tabIndex=l;i.addEventListener("change",(n=>{const{name:i,checked:r}=n.target;for(const n of this._getElementsByName(i,s)){const s=r&&n.exportValue===e.exportValue;n.domElement&&(n.domElement.checked=s);t.setValue(n.id,{value:s})}t.setValue(s,{value:r})}));i.addEventListener("resetform",(t=>{const s=e.defaultFieldValue||"Off";t.target.checked=s===e.exportValue}));if(this.enableScripting&&this.hasJSActions){i.addEventListener("updatefromsandbox",(e=>{const n={value(e){e.target.checked="Off"!==e.detail.value;t.setValue(s,{value:e.target.checked})}};this._dispatchEventFromSandbox(n,e)}));this._setEventListeners(i,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}this._setBackgroundColor(i);this._setDefaultPropertiesFromJS(i);this.container.append(i);return this.container}}class RadioButtonWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.className="buttonWidgetAnnotation radioButton";const t=this.annotationStorage,e=this.data,s=e.id;let n=t.getValue(s,{value:e.fieldValue===e.buttonValue}).value;if("string"==typeof n){n=n!==e.buttonValue;t.setValue(s,{value:n})}const i=document.createElement("input");c.add(i);i.setAttribute("data-element-id",s);i.disabled=e.readOnly;this._setRequired(i,this.data.required);i.type="radio";i.name=e.fieldName;n&&i.setAttribute("checked",!0);i.tabIndex=l;i.addEventListener("change",(e=>{const{name:n,checked:i}=e.target;for(const e of this._getElementsByName(n,s))t.setValue(e.id,{value:!1});t.setValue(s,{value:i})}));i.addEventListener("resetform",(t=>{const s=e.defaultFieldValue;t.target.checked=null!=s&&s===e.buttonValue}));if(this.enableScripting&&this.hasJSActions){const n=e.buttonValue;i.addEventListener("updatefromsandbox",(e=>{const i={value:e=>{const i=n===e.detail.value;for(const n of this._getElementsByName(e.target.name)){const e=i&&n.id===s;n.domElement&&(n.domElement.checked=e);t.setValue(n.id,{value:e})}}};this._dispatchEventFromSandbox(i,e)}));this._setEventListeners(i,[["change","Validate"],["change","Action"],["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"]],(t=>t.target.checked))}this._setBackgroundColor(i);this._setDefaultPropertiesFromJS(i);this.container.append(i);return this.container}}class PushButtonWidgetAnnotationElement extends LinkAnnotationElement{constructor(t){super(t,{ignoreBorder:t.data.hasAppearance})}render(){const t=super.render();t.className="buttonWidgetAnnotation pushButton";this.data.alternativeText&&(t.title=this.data.alternativeText);const e=t.lastChild;if(this.enableScripting&&this.hasJSActions&&e){this._setDefaultPropertiesFromJS(e);e.addEventListener("updatefromsandbox",(t=>{this._dispatchEventFromSandbox({},t)}))}return t}}class ChoiceWidgetAnnotationElement extends WidgetAnnotationElement{constructor(t){super(t,{isRenderable:t.renderForms})}render(){this.container.className="choiceWidgetAnnotation";const t=this.annotationStorage,e=this.data.id,s=t.getValue(e,{value:this.data.fieldValue}),n=document.createElement("select");c.add(n);n.setAttribute("data-element-id",e);n.disabled=this.data.readOnly;this._setRequired(n,this.data.required);n.name=this.data.fieldName;n.tabIndex=l;let i=this.data.combo&&this.data.options.length>0;if(!this.data.combo){n.size=this.data.options.length;this.data.multiSelect&&(n.multiple=!0)}n.addEventListener("resetform",(t=>{const e=this.data.defaultFieldValue;for(const t of n.options)t.selected=t.value===e}));for(const t of this.data.options){const e=document.createElement("option");e.textContent=t.displayValue;e.value=t.exportValue;if(s.value.includes(t.exportValue)){e.setAttribute("selected",!0);i=!1}n.append(e)}let r=null;if(i){const t=document.createElement("option");t.value=" ";t.setAttribute("hidden",!0);t.setAttribute("selected",!0);n.prepend(t);r=()=>{t.remove();n.removeEventListener("input",r);r=null};n.addEventListener("input",r)}const getValue=(t,e)=>{const s=e?"value":"textContent",n=t.target.options;return t.target.multiple?Array.prototype.filter.call(n,(t=>t.selected)).map((t=>t[s])):-1===n.selectedIndex?null:n[n.selectedIndex][s]},getItems=t=>{const e=t.target.options;return Array.prototype.map.call(e,(t=>({displayValue:t.textContent,exportValue:t.value})))};if(this.enableScripting&&this.hasJSActions){n.addEventListener("updatefromsandbox",(s=>{const i={value(s){r?.();const i=s.detail.value,a=new Set(Array.isArray(i)?i:[i]);for(const t of n.options)t.selected=a.has(t.value);t.setValue(e,{value:getValue(s,!0)})},multipleSelection(t){n.multiple=!0},remove(s){const i=n.options,r=s.detail.remove;i[r].selected=!1;n.remove(r);if(i.length>0){-1===Array.prototype.findIndex.call(i,(t=>t.selected))&&(i[0].selected=!0)}t.setValue(e,{value:getValue(s,!0),items:getItems(s)})},clear(s){for(;0!==n.length;)n.remove(0);t.setValue(e,{value:null,items:[]})},insert(s){const{index:i,displayValue:r,exportValue:a}=s.detail.insert,o=n.children[i],l=document.createElement("option");l.textContent=r;l.value=a;o?o.before(l):n.append(l);t.setValue(e,{value:getValue(s,!0),items:getItems(s)})},items(s){const{items:i}=s.detail;for(;0!==n.length;)n.remove(0);for(const t of i){const{displayValue:e,exportValue:s}=t,i=document.createElement("option");i.textContent=e;i.value=s;n.append(i)}n.options.length>0&&(n.options[0].selected=!0);t.setValue(e,{value:getValue(s,!0),items:getItems(s)})},indices(s){const n=new Set(s.detail.indices);for(const t of s.target.options)t.selected=n.has(t.index);t.setValue(e,{value:getValue(s,!0)})},editable(t){t.target.disabled=!t.detail.editable}};this._dispatchEventFromSandbox(i,s)}));n.addEventListener("input",(s=>{const n=getValue(s,!0),i=getValue(s,!1);t.setValue(e,{value:n});this.linkService.eventBus?.dispatch("dispatcheventinsandbox",{source:this,detail:{id:e,name:"Keystroke",value:i,changeEx:n,willCommit:!0,commitKey:1,keyDown:!1}})}));this._setEventListeners(n,[["focus","Focus"],["blur","Blur"],["mousedown","Mouse Down"],["mouseenter","Mouse Enter"],["mouseleave","Mouse Exit"],["mouseup","Mouse Up"],["input","Action"]],(t=>t.target.checked))}else n.addEventListener("input",(function(s){t.setValue(e,{value:getValue(s,!0)})}));this.data.combo&&this._setTextStyle(n);this._setBackgroundColor(n);this._setDefaultPropertiesFromJS(n);this.container.append(n);return this.container}}class PopupAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!!(t.data.titleObj?.str||t.data.contentsObj?.str||t.data.richText?.str)})}render(){this.container.className="popupAnnotation";if(["Line","Square","Circle","PolyLine","Polygon","Ink"].includes(this.data.parentType))return this.container;const t=`[data-annotation-id="${this.data.parentId}"]`,e=this.layer.querySelectorAll(t);if(0===e.length)return this.container;const s=new PopupElement({container:this.container,trigger:Array.from(e),color:this.data.color,titleObj:this.data.titleObj,modificationDate:this.data.modificationDate,contentsObj:this.data.contentsObj,richText:this.data.richText}),i=this.page,r=n.Util.normalizeRect([this.data.parentRect[0],i.view[3]-this.data.parentRect[1]+i.view[1],this.data.parentRect[2],i.view[3]-this.data.parentRect[3]+i.view[1]]),a=r[0]+this.data.parentRect[2]-this.data.parentRect[0],o=r[1],[l,c,h,d]=this.viewport.viewBox,u=h-l,p=d-c;this.container.style.left=100*(a-l)/u+"%";this.container.style.top=100*(o-c)/p+"%";this.container.append(s.render());return this.container}}class PopupElement{constructor(t){this.container=t.container;this.trigger=t.trigger;this.color=t.color;this.titleObj=t.titleObj;this.modificationDate=t.modificationDate;this.contentsObj=t.contentsObj;this.richText=t.richText;this.hideWrapper=t.hideWrapper||!1;this.pinned=!1}render(){const t=document.createElement("div");t.className="popupWrapper";this.hideElement=this.hideWrapper?t:this.container;this.hideElement.hidden=!0;const e=document.createElement("div");e.className="popup";const s=this.color;if(s){const t=.7*(255-s[0])+s[0],i=.7*(255-s[1])+s[1],r=.7*(255-s[2])+s[2];e.style.backgroundColor=n.Util.makeHexColor(0|t,0|i,0|r)}const r=document.createElement("h1");r.dir=this.titleObj.dir;r.textContent=this.titleObj.str;e.append(r);const a=i.PDFDateString.toDateObject(this.modificationDate);if(a){const t=document.createElement("span");t.className="popupDate";t.textContent="{{date}}, {{time}}";t.dataset.l10nId="annotation_date_string";t.dataset.l10nArgs=JSON.stringify({date:a.toLocaleDateString(),time:a.toLocaleTimeString()});e.append(t)}if(!this.richText?.str||this.contentsObj?.str&&this.contentsObj.str!==this.richText.str){const t=this._formatContents(this.contentsObj);e.append(t)}else{o.XfaLayer.render({xfaHtml:this.richText.html,intent:"richText",div:e});e.lastChild.className="richText popupContent"}Array.isArray(this.trigger)||(this.trigger=[this.trigger]);for(const t of this.trigger){t.addEventListener("click",this._toggle.bind(this));t.addEventListener("mouseover",this._show.bind(this,!1));t.addEventListener("mouseout",this._hide.bind(this,!1))}e.addEventListener("click",this._hide.bind(this,!0));t.append(e);return t}_formatContents({str:t,dir:e}){const s=document.createElement("p");s.className="popupContent";s.dir=e;const n=t.split(/(?:\r\n?|\n)/);for(let t=0,e=n.length;t<e;++t){const i=n[t];s.append(document.createTextNode(i));t<e-1&&s.append(document.createElement("br"))}return s}_toggle(){this.pinned?this._hide(!0):this._show(!0)}_show(t=!1){t&&(this.pinned=!0);if(this.hideElement.hidden){this.hideElement.hidden=!1;this.container.style.zIndex=parseInt(this.container.style.zIndex)+1e3}}_hide(t=!0){t&&(this.pinned=!1);if(!this.hideElement.hidden&&!this.pinned){this.hideElement.hidden=!0;this.container.style.zIndex=parseInt(this.container.style.zIndex)-1e3}}}class FreeTextAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!!(t.data.hasPopup||t.data.titleObj?.str||t.data.contentsObj?.str||t.data.richText?.str),ignoreBorder:!0});this.textContent=t.data.textContent}render(){this.container.className="freeTextAnnotation";if(this.textContent){const t=document.createElement("div");t.className="annotationTextContent";t.setAttribute("role","comment");for(const e of this.textContent){const s=document.createElement("span");s.textContent=e;t.append(s)}this.container.append(t)}this.data.hasPopup||this._createPopup(null,this.data);return this.container}}class LineAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!!(t.data.hasPopup||t.data.titleObj?.str||t.data.contentsObj?.str||t.data.richText?.str),ignoreBorder:!0})}render(){this.container.className="lineAnnotation";const t=this.data,{width:e,height:s}=getRectDims(t.rect),n=this.svgFactory.create(e,s,!0),i=this.svgFactory.createElement("svg:line");i.setAttribute("x1",t.rect[2]-t.lineCoordinates[0]);i.setAttribute("y1",t.rect[3]-t.lineCoordinates[1]);i.setAttribute("x2",t.rect[2]-t.lineCoordinates[2]);i.setAttribute("y2",t.rect[3]-t.lineCoordinates[3]);i.setAttribute("stroke-width",t.borderStyle.width||1);i.setAttribute("stroke","transparent");i.setAttribute("fill","transparent");n.append(i);this.container.append(n);this._createPopup(i,t);return this.container}}class SquareAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!!(t.data.hasPopup||t.data.titleObj?.str||t.data.contentsObj?.str||t.data.richText?.str),ignoreBorder:!0})}render(){this.container.className="squareAnnotation";const t=this.data,{width:e,height:s}=getRectDims(t.rect),n=this.svgFactory.create(e,s,!0),i=t.borderStyle.width,r=this.svgFactory.createElement("svg:rect");r.setAttribute("x",i/2);r.setAttribute("y",i/2);r.setAttribute("width",e-i);r.setAttribute("height",s-i);r.setAttribute("stroke-width",i||1);r.setAttribute("stroke","transparent");r.setAttribute("fill","transparent");n.append(r);this.container.append(n);this._createPopup(r,t);return this.container}}class CircleAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!!(t.data.hasPopup||t.data.titleObj?.str||t.data.contentsObj?.str||t.data.richText?.str),ignoreBorder:!0})}render(){this.container.className="circleAnnotation";const t=this.data,{width:e,height:s}=getRectDims(t.rect),n=this.svgFactory.create(e,s,!0),i=t.borderStyle.width,r=this.svgFactory.createElement("svg:ellipse");r.setAttribute("cx",e/2);r.setAttribute("cy",s/2);r.setAttribute("rx",e/2-i/2);r.setAttribute("ry",s/2-i/2);r.setAttribute("stroke-width",i||1);r.setAttribute("stroke","transparent");r.setAttribute("fill","transparent");n.append(r);this.container.append(n);this._createPopup(r,t);return this.container}}class PolylineAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!!(t.data.hasPopup||t.data.titleObj?.str||t.data.contentsObj?.str||t.data.richText?.str),ignoreBorder:!0});this.containerClassName="polylineAnnotation";this.svgElementName="svg:polyline"}render(){this.container.className=this.containerClassName;const t=this.data,{width:e,height:s}=getRectDims(t.rect),n=this.svgFactory.create(e,s,!0);let i=[];for(const e of t.vertices){const s=e.x-t.rect[0],n=t.rect[3]-e.y;i.push(s+","+n)}i=i.join(" ");const r=this.svgFactory.createElement(this.svgElementName);r.setAttribute("points",i);r.setAttribute("stroke-width",t.borderStyle.width||1);r.setAttribute("stroke","transparent");r.setAttribute("fill","transparent");n.append(r);this.container.append(n);this._createPopup(r,t);return this.container}}class PolygonAnnotationElement extends PolylineAnnotationElement{constructor(t){super(t);this.containerClassName="polygonAnnotation";this.svgElementName="svg:polygon"}}class CaretAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!!(t.data.hasPopup||t.data.titleObj?.str||t.data.contentsObj?.str||t.data.richText?.str),ignoreBorder:!0})}render(){this.container.className="caretAnnotation";this.data.hasPopup||this._createPopup(null,this.data);return this.container}}class InkAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!!(t.data.hasPopup||t.data.titleObj?.str||t.data.contentsObj?.str||t.data.richText?.str),ignoreBorder:!0});this.containerClassName="inkAnnotation";this.svgElementName="svg:polyline"}render(){this.container.className=this.containerClassName;const t=this.data,{width:e,height:s}=getRectDims(t.rect),n=this.svgFactory.create(e,s,!0);for(const e of t.inkLists){let s=[];for(const n of e){const e=n.x-t.rect[0],i=t.rect[3]-n.y;s.push(`${e},${i}`)}s=s.join(" ");const i=this.svgFactory.createElement(this.svgElementName);i.setAttribute("points",s);i.setAttribute("stroke-width",t.borderStyle.width||1);i.setAttribute("stroke","transparent");i.setAttribute("fill","transparent");this._createPopup(i,t);n.append(i)}this.container.append(n);return this.container}}class HighlightAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!!(t.data.hasPopup||t.data.titleObj?.str||t.data.contentsObj?.str||t.data.richText?.str),ignoreBorder:!0,createQuadrilaterals:!0})}render(){this.data.hasPopup||this._createPopup(null,this.data);if(this.quadrilaterals)return this._renderQuadrilaterals("highlightAnnotation");this.container.className="highlightAnnotation";return this.container}}class UnderlineAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!!(t.data.hasPopup||t.data.titleObj?.str||t.data.contentsObj?.str||t.data.richText?.str),ignoreBorder:!0,createQuadrilaterals:!0})}render(){this.data.hasPopup||this._createPopup(null,this.data);if(this.quadrilaterals)return this._renderQuadrilaterals("underlineAnnotation");this.container.className="underlineAnnotation";return this.container}}class SquigglyAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!!(t.data.hasPopup||t.data.titleObj?.str||t.data.contentsObj?.str||t.data.richText?.str),ignoreBorder:!0,createQuadrilaterals:!0})}render(){this.data.hasPopup||this._createPopup(null,this.data);if(this.quadrilaterals)return this._renderQuadrilaterals("squigglyAnnotation");this.container.className="squigglyAnnotation";return this.container}}class StrikeOutAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!!(t.data.hasPopup||t.data.titleObj?.str||t.data.contentsObj?.str||t.data.richText?.str),ignoreBorder:!0,createQuadrilaterals:!0})}render(){this.data.hasPopup||this._createPopup(null,this.data);if(this.quadrilaterals)return this._renderQuadrilaterals("strikeoutAnnotation");this.container.className="strikeoutAnnotation";return this.container}}class StampAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!!(t.data.hasPopup||t.data.titleObj?.str||t.data.contentsObj?.str||t.data.richText?.str),ignoreBorder:!0})}render(){this.container.className="stampAnnotation";this.data.hasPopup||this._createPopup(null,this.data);return this.container}}class FileAttachmentAnnotationElement extends AnnotationElement{constructor(t){super(t,{isRenderable:!0});const{filename:e,content:s}=this.data.file;this.filename=(0,i.getFilenameFromUrl)(e);this.content=s;this.linkService.eventBus?.dispatch("fileattachmentannotation",{source:this,filename:e,content:s})}render(){this.container.className="fileAttachmentAnnotation";const t=document.createElement("div");t.className="popupTriggerArea";t.addEventListener("dblclick",this._download.bind(this));!this.data.hasPopup&&(this.data.titleObj?.str||this.data.contentsObj?.str||this.data.richText)&&this._createPopup(t,this.data);this.container.append(t);return this.container}_download(){this.downloadManager?.openOrDownloadData(this.container,this.content,this.filename)}}class AnnotationLayer{static#de(t,e,s,n){const r=t.firstChild||t;r.id=`${i.AnnotationPrefix}${e}`;s.append(t);n?.moveElementInDOM(s,t,r,!1)}static render(t){const{annotations:e,div:s,viewport:a,accessibilityManager:o}=t;this.#ue(s,a);let l=0;for(const c of e){if(c.annotationType!==n.AnnotationType.POPUP){const{width:t,height:e}=getRectDims(c.rect);if(t<=0||e<=0)continue}const e=AnnotationElementFactory.create({data:c,layer:s,page:t.page,viewport:a,linkService:t.linkService,downloadManager:t.downloadManager,imageResourcesPath:t.imageResourcesPath||"",renderForms:!1!==t.renderForms,svgFactory:new i.DOMSVGFactory,annotationStorage:t.annotationStorage||new r.AnnotationStorage,enableScripting:t.enableScripting,hasJSActions:t.hasJSActions,fieldObjects:t.fieldObjects,mouseState:t.mouseState||{isDown:!1}});if(e.isRenderable){const t=e.render();c.hidden&&(t.style.visibility="hidden");if(Array.isArray(t))for(const e of t){e.style.zIndex=l++;AnnotationLayer.#de(e,c.id,s,o)}else{t.style.zIndex=l++;e instanceof PopupAnnotationElement?s.prepend(t):AnnotationLayer.#de(t,c.id,s,o)}}}this.#pe(s,t.annotationCanvasMap)}static update(t){const{annotationCanvasMap:e,div:s,viewport:n}=t;this.#ue(s,n);this.#pe(s,e);s.hidden=!1}static#ue(t,{width:e,height:s,rotation:n}){const{style:i}=t,r=n%180!=0,a=Math.floor(e)+"px",o=Math.floor(s)+"px";i.width=r?o:a;i.height=r?a:o;t.setAttribute("data-main-rotation",n)}static#pe(t,e){if(e){for(const[s,n]of e){const e=t.querySelector(`[data-annotation-id="${s}"]`);if(!e)continue;const{firstChild:i}=e;i?"CANVAS"===i.nodeName?i.replaceWith(n):i.before(n):e.append(n)}e.clear()}}}e.AnnotationLayer=AnnotationLayer},(t,e)=>{Object.defineProperty(e,"__esModule",{value:!0});e.ColorConverters=void 0;function makeColorComp(t){return Math.floor(255*Math.max(0,Math.min(1,t))).toString(16).padStart(2,"0")}e.ColorConverters=class ColorConverters{static CMYK_G([t,e,s,n]){return["G",1-Math.min(1,.3*t+.59*s+.11*e+n)]}static G_CMYK([t]){return["CMYK",0,0,0,1-t]}static G_RGB([t]){return["RGB",t,t,t]}static G_HTML([t]){const e=makeColorComp(t);return`#${e}${e}${e}`}static RGB_G([t,e,s]){return["G",.3*t+.59*e+.11*s]}static RGB_HTML([t,e,s]){return`#${makeColorComp(t)}${makeColorComp(e)}${makeColorComp(s)}`}static T_HTML(){return"#00000000"}static CMYK_RGB([t,e,s,n]){return["RGB",1-Math.min(1,t+n),1-Math.min(1,s+n),1-Math.min(1,e+n)]}static CMYK_HTML(t){const e=this.CMYK_RGB(t).slice(1);return this.RGB_HTML(e)}static RGB_CMYK([t,e,s]){const n=1-t,i=1-e,r=1-s;return["CMYK",n,i,r,Math.min(n,i,r)]}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.XfaLayer=void 0;var n=s(20);e.XfaLayer=class XfaLayer{static setupStorage(t,e,s,n,i){const r=n.getValue(e,{value:null});switch(s.name){case"textarea":null!==r.value&&(t.textContent=r.value);if("print"===i)break;t.addEventListener("input",(t=>{n.setValue(e,{value:t.target.value})}));break;case"input":if("radio"===s.attributes.type||"checkbox"===s.attributes.type){r.value===s.attributes.xfaOn?t.setAttribute("checked",!0):r.value===s.attributes.xfaOff&&t.removeAttribute("checked");if("print"===i)break;t.addEventListener("change",(t=>{n.setValue(e,{value:t.target.checked?t.target.getAttribute("xfaOn"):t.target.getAttribute("xfaOff")})}))}else{null!==r.value&&t.setAttribute("value",r.value);if("print"===i)break;t.addEventListener("input",(t=>{n.setValue(e,{value:t.target.value})}))}break;case"select":if(null!==r.value)for(const t of s.children)t.attributes.value===r.value&&(t.attributes.selected=!0);t.addEventListener("input",(t=>{const s=t.target.options,i=-1===s.selectedIndex?"":s[s.selectedIndex].value;n.setValue(e,{value:i})}))}}static setAttributes({html:t,element:e,storage:s=null,intent:n,linkService:i}){const{attributes:r}=e,a=t instanceof HTMLAnchorElement;"radio"===r.type&&(r.name=`${r.name}-${n}`);for(const[e,s]of Object.entries(r))if(null!=s)switch(e){case"class":s.length&&t.setAttribute(e,s.join(" "));break;case"dataId":break;case"id":t.setAttribute("data-element-id",s);break;case"style":Object.assign(t.style,s);break;case"textContent":t.textContent=s;break;default:(!a||"href"!==e&&"newWindow"!==e)&&t.setAttribute(e,s)}a&&i.addLinkAttributes(t,r.href,r.newWindow);s&&r.dataId&&this.setupStorage(t,r.dataId,e,s)}static render(t){const e=t.annotationStorage,s=t.linkService,i=t.xfaHtml,r=t.intent||"display",a=document.createElement(i.name);i.attributes&&this.setAttributes({html:a,element:i,intent:r,linkService:s});const o=[[i,-1,a]],l=t.div;l.append(a);if(t.viewport){const e=`matrix(${t.viewport.transform.join(",")})`;l.style.transform=e}"richText"!==r&&l.setAttribute("class","xfaLayer xfaFont");const c=[];for(;o.length>0;){const[t,i,a]=o.at(-1);if(i+1===t.children.length){o.pop();continue}const l=t.children[++o.at(-1)[1]];if(null===l)continue;const{name:h}=l;if("#text"===h){const t=document.createTextNode(l.value);c.push(t);a.append(t);continue}let d;d=l?.attributes?.xmlns?document.createElementNS(l.attributes.xmlns,h):document.createElement(h);a.append(d);l.attributes&&this.setAttributes({html:d,element:l,storage:e,intent:r,linkService:s});if(l.children&&l.children.length>0)o.push([l,-1,d]);else if(l.value){const t=document.createTextNode(l.value);n.XfaText.shouldBuildText(h)&&c.push(t);d.append(t)}}for(const t of l.querySelectorAll(".xfaNonInteractive input, .xfaNonInteractive textarea"))t.setAttribute("readOnly",!0);return{textDivs:c}}static update(t){const e=`matrix(${t.viewport.transform.join(",")})`;t.div.style.transform=e;t.div.hidden=!1}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.TextLayerRenderTask=void 0;e.renderTextLayer=function renderTextLayer(t){const e=new TextLayerRenderTask({textContent:t.textContent,textContentStream:t.textContentStream,container:t.container,viewport:t.viewport,textDivs:t.textDivs,textContentItemsStr:t.textContentItemsStr,enhanceTextSelection:t.enhanceTextSelection});e._render(t.timeout);return e};var n=s(1),i=s(8);const r=30,a=new Map,o=/^\s+$/g;function appendText(t,e,s,i){const l=document.createElement("span"),c=t._enhanceTextSelection?{angle:0,canvasWidth:0,hasText:""!==e.str,hasEOL:e.hasEOL,originalTransform:null,paddingBottom:0,paddingLeft:0,paddingRight:0,paddingTop:0,scale:1,fontSize:0}:{angle:0,canvasWidth:0,hasText:""!==e.str,hasEOL:e.hasEOL,fontSize:0};t._textDivs.push(l);const h=n.Util.transform(t._viewport.transform,e.transform);let d=Math.atan2(h[1],h[0]);const u=s[e.fontName];u.vertical&&(d+=Math.PI/2);const p=Math.hypot(h[2],h[3]),g=p*function getAscent(t,e){const s=a.get(t);if(s)return s;e.save();e.font=`30px ${t}`;const n=e.measureText("");let i=n.fontBoundingBoxAscent,o=Math.abs(n.fontBoundingBoxDescent);if(i){e.restore();const s=i/(i+o);a.set(t,s);return s}e.strokeStyle="red";e.clearRect(0,0,r,r);e.strokeText("g",0,0);let l=e.getImageData(0,0,r,r).data;o=0;for(let t=l.length-1-3;t>=0;t-=4)if(l[t]>0){o=Math.ceil(t/4/r);break}e.clearRect(0,0,r,r);e.strokeText("A",0,r);l=e.getImageData(0,0,r,r).data;i=0;for(let t=0,e=l.length;t<e;t+=4)if(l[t]>0){i=r-Math.floor(t/4/r);break}e.restore();if(i){const e=i/(i+o);a.set(t,e);return e}a.set(t,.8);return.8}(u.fontFamily,i);let f,m;if(0===d){f=h[4];m=h[5]-g}else{f=h[4]+g*Math.sin(d);m=h[5]-g*Math.cos(d)}l.style.left=`${f}px`;l.style.top=`${m}px`;l.style.fontSize=`${p}px`;l.style.fontFamily=u.fontFamily;c.fontSize=p;l.setAttribute("role","presentation");l.textContent=e.str;l.dir=e.dir;t._fontInspectorEnabled&&(l.dataset.fontName=e.fontName);0!==d&&(c.angle=d*(180/Math.PI));let b=!1;if(e.str.length>1||t._enhanceTextSelection&&o.test(e.str))b=!0;else if(" "!==e.str&&e.transform[0]!==e.transform[3]){const t=Math.abs(e.transform[0]),s=Math.abs(e.transform[3]);t!==s&&Math.max(t,s)/Math.min(t,s)>1.5&&(b=!0)}b&&(u.vertical?c.canvasWidth=e.height*t._viewport.scale:c.canvasWidth=e.width*t._viewport.scale);t._textDivProperties.set(l,c);t._textContentStream&&t._layoutText(l);if(t._enhanceTextSelection&&c.hasText){let s=1,i=0;if(0!==d){s=Math.cos(d);i=Math.sin(d)}const r=(u.vertical?e.height:e.width)*t._viewport.scale,a=p;let o,c;if(0!==d){o=[s,i,-i,s,f,m];c=n.Util.getAxialAlignedBoundingBox([0,0,r,a],o)}else c=[f,m,f+r,m+a];t._bounds.push({left:c[0],top:c[1],right:c[2],bottom:c[3],div:l,size:[r,a],m:o})}}function render(t){if(t._canceled)return;const e=t._textDivs,s=t._capability,n=e.length;if(n>1e5){t._renderingDone=!0;s.resolve()}else{if(!t._textContentStream)for(let s=0;s<n;s++)t._layoutText(e[s]);t._renderingDone=!0;s.resolve()}}function findPositiveMin(t,e,s){let n=0;for(let i=0;i<s;i++){const s=t[e++];s>0&&(n=n?Math.min(s,n):s)}return n}function expand(t){const e=t._bounds,s=t._viewport,i=function expandBounds(t,e,s){const n=s.map((function(t,e){return{x1:t.left,y1:t.top,x2:t.right,y2:t.bottom,index:e,x1New:void 0,x2New:void 0}}));expandBoundsLTR(t,n);const i=new Array(s.length);for(const t of n){const e=t.index;i[e]={left:t.x1New,top:0,right:t.x2New,bottom:0}}s.map((function(e,s){const r=i[s],a=n[s];a.x1=e.top;a.y1=t-r.right;a.x2=e.bottom;a.y2=t-r.left;a.index=s;a.x1New=void 0;a.x2New=void 0}));expandBoundsLTR(e,n);for(const t of n){const e=t.index;i[e].top=t.x1New;i[e].bottom=t.x2New}return i}(s.width,s.height,e);for(let s=0;s<i.length;s++){const r=e[s].div,a=t._textDivProperties.get(r);if(0===a.angle){a.paddingLeft=e[s].left-i[s].left;a.paddingTop=e[s].top-i[s].top;a.paddingRight=i[s].right-e[s].right;a.paddingBottom=i[s].bottom-e[s].bottom;t._textDivProperties.set(r,a);continue}const o=i[s],l=e[s],c=l.m,h=c[0],d=c[1],u=[[0,0],[0,l.size[1]],[l.size[0],0],l.size],p=new Float64Array(64);for(let t=0,e=u.length;t<e;t++){const e=n.Util.applyTransform(u[t],c);p[t+0]=h&&(o.left-e[0])/h;p[t+4]=d&&(o.top-e[1])/d;p[t+8]=h&&(o.right-e[0])/h;p[t+12]=d&&(o.bottom-e[1])/d;p[t+16]=d&&(o.left-e[0])/-d;p[t+20]=h&&(o.top-e[1])/h;p[t+24]=d&&(o.right-e[0])/-d;p[t+28]=h&&(o.bottom-e[1])/h;p[t+32]=h&&(o.left-e[0])/-h;p[t+36]=d&&(o.top-e[1])/-d;p[t+40]=h&&(o.right-e[0])/-h;p[t+44]=d&&(o.bottom-e[1])/-d;p[t+48]=d&&(o.left-e[0])/d;p[t+52]=h&&(o.top-e[1])/-h;p[t+56]=d&&(o.right-e[0])/d;p[t+60]=h&&(o.bottom-e[1])/-h}const g=1+Math.min(Math.abs(h),Math.abs(d));a.paddingLeft=findPositiveMin(p,32,16)/g;a.paddingTop=findPositiveMin(p,48,16)/g;a.paddingRight=findPositiveMin(p,0,16)/g;a.paddingBottom=findPositiveMin(p,16,16)/g;t._textDivProperties.set(r,a)}}function expandBoundsLTR(t,e){e.sort((function(t,e){return t.x1-e.x1||t.index-e.index}));const s=[{start:-1/0,end:1/0,boundary:{x1:-1/0,y1:-1/0,x2:0,y2:1/0,index:-1,x1New:0,x2New:0}}];for(const t of e){let e=0;for(;e<s.length&&s[e].end<=t.y1;)e++;let n,i,r=s.length-1;for(;r>=0&&s[r].start>=t.y2;)r--;let a,o,l=-1/0;for(a=e;a<=r;a++){n=s[a];i=n.boundary;let e;e=i.x2>t.x1?i.index>t.index?i.x1New:t.x1:void 0===i.x2New?(i.x2+t.x1)/2:i.x2New;e>l&&(l=e)}t.x1New=l;for(a=e;a<=r;a++){n=s[a];i=n.boundary;void 0===i.x2New?i.x2>t.x1?i.index>t.index&&(i.x2New=i.x2):i.x2New=l:i.x2New>l&&(i.x2New=Math.max(l,i.x2))}const c=[];let h=null;for(a=e;a<=r;a++){n=s[a];i=n.boundary;const e=i.x2>t.x2?i:t;if(h===e)c.at(-1).end=n.end;else{c.push({start:n.start,end:n.end,boundary:e});h=e}}if(s[e].start<t.y1){c[0].start=t.y1;c.unshift({start:s[e].start,end:t.y1,boundary:s[e].boundary})}if(t.y2<s[r].end){c.at(-1).end=t.y2;c.push({start:t.y2,end:s[r].end,boundary:s[r].boundary})}for(a=e;a<=r;a++){n=s[a];i=n.boundary;if(void 0!==i.x2New)continue;let t=!1;for(o=e-1;!t&&o>=0&&s[o].start>=i.y1;o--)t=s[o].boundary===i;for(o=r+1;!t&&o<s.length&&s[o].end<=i.y2;o++)t=s[o].boundary===i;for(o=0;!t&&o<c.length;o++)t=c[o].boundary===i;t||(i.x2New=l)}Array.prototype.splice.apply(s,[e,r-e+1,...c])}for(const e of s){const s=e.boundary;void 0===s.x2New&&(s.x2New=Math.max(t,s.x2))}}class TextLayerRenderTask{constructor({textContent:t,textContentStream:e,container:s,viewport:r,textDivs:a,textContentItemsStr:o,enhanceTextSelection:l}){l&&(0,i.deprecated)("The `enhanceTextSelection` functionality will be removed in the future.");this._textContent=t;this._textContentStream=e;this._container=s;this._document=s.ownerDocument;this._viewport=r;this._textDivs=a||[];this._textContentItemsStr=o||[];this._enhanceTextSelection=!!l;this._fontInspectorEnabled=!!globalThis.FontInspector?.enabled;this._reader=null;this._layoutTextLastFontSize=null;this._layoutTextLastFontFamily=null;this._layoutTextCtx=null;this._textDivProperties=new WeakMap;this._renderingDone=!1;this._canceled=!1;this._capability=(0,n.createPromiseCapability)();this._renderTimer=null;this._bounds=[];this._devicePixelRatio=globalThis.devicePixelRatio||1;this._capability.promise.finally((()=>{this._enhanceTextSelection||(this._textDivProperties=null);if(this._layoutTextCtx){this._layoutTextCtx.canvas.width=0;this._layoutTextCtx.canvas.height=0;this._layoutTextCtx=null}})).catch((()=>{}))}get promise(){return this._capability.promise}cancel(){this._canceled=!0;if(this._reader){this._reader.cancel(new n.AbortException("TextLayer task cancelled.")).catch((()=>{}));this._reader=null}if(null!==this._renderTimer){clearTimeout(this._renderTimer);this._renderTimer=null}this._capability.reject(new Error("TextLayer task cancelled."))}_processItems(t,e){for(let s=0,n=t.length;s<n;s++)if(void 0!==t[s].str){this._textContentItemsStr.push(t[s].str);appendText(this,t[s],e,this._layoutTextCtx)}else if("beginMarkedContentProps"===t[s].type||"beginMarkedContent"===t[s].type){const e=this._container;this._container=document.createElement("span");this._container.classList.add("markedContent");null!==t[s].id&&this._container.setAttribute("id",`${t[s].id}`);e.append(this._container)}else"endMarkedContent"===t[s].type&&(this._container=this._container.parentNode)}_layoutText(t){const e=this._textDivProperties.get(t);let s="";if(0!==e.canvasWidth&&e.hasText){const{fontFamily:n}=t.style,{fontSize:i}=e;if(i!==this._layoutTextLastFontSize||n!==this._layoutTextLastFontFamily){this._layoutTextCtx.font=`${i*this._devicePixelRatio}px ${n}`;this._layoutTextLastFontSize=i;this._layoutTextLastFontFamily=n}const{width:r}=this._layoutTextCtx.measureText(t.textContent);if(r>0){const t=this._devicePixelRatio*e.canvasWidth/r;this._enhanceTextSelection&&(e.scale=t);s=`scaleX(${t})`}}0!==e.angle&&(s=`rotate(${e.angle}deg) ${s}`);if(s.length>0){this._enhanceTextSelection&&(e.originalTransform=s);t.style.transform=s}e.hasText&&this._container.append(t);if(e.hasEOL){const t=document.createElement("br");t.setAttribute("role","presentation");this._container.append(t)}}_render(t=0){const e=(0,n.createPromiseCapability)();let s=Object.create(null);const i=this._document.createElement("canvas");i.height=i.width=r;this._layoutTextCtx=i.getContext("2d",{alpha:!1});if(this._textContent){const t=this._textContent.items,s=this._textContent.styles;this._processItems(t,s);e.resolve()}else{if(!this._textContentStream)throw new Error('Neither "textContent" nor "textContentStream" parameters specified.');{const pump=()=>{this._reader.read().then((({value:t,done:n})=>{if(n)e.resolve();else{Object.assign(s,t.styles);this._processItems(t.items,s);pump()}}),e.reject)};this._reader=this._textContentStream.getReader();pump()}}e.promise.then((()=>{s=null;t?this._renderTimer=setTimeout((()=>{render(this);this._renderTimer=null}),t):render(this)}),this._capability.reject)}expandTextDivs(t=!1){if(!this._enhanceTextSelection||!this._renderingDone)return;if(null!==this._bounds){expand(this);this._bounds=null}const e=[],s=[];for(let n=0,i=this._textDivs.length;n<i;n++){const i=this._textDivs[n],r=this._textDivProperties.get(i);if(r.hasText)if(t){e.length=0;s.length=0;r.originalTransform&&e.push(r.originalTransform);if(r.paddingTop>0){s.push(`${r.paddingTop}px`);e.push(`translateY(${-r.paddingTop}px)`)}else s.push(0);r.paddingRight>0?s.push(r.paddingRight/r.scale+"px"):s.push(0);r.paddingBottom>0?s.push(`${r.paddingBottom}px`):s.push(0);if(r.paddingLeft>0){s.push(r.paddingLeft/r.scale+"px");e.push(`translateX(${-r.paddingLeft/r.scale}px)`)}else s.push(0);i.style.padding=s.join(" ");e.length&&(i.style.transform=e.join(" "))}else{i.style.padding=null;i.style.transform=r.originalTransform}}}}e.TextLayerRenderTask=TextLayerRenderTask},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.SVGGraphics=void 0;var n=s(8),i=s(1),r=s(3);let a=class{constructor(){(0,i.unreachable)("Not implemented: SVGGraphics")}};e.SVGGraphics=a;{const o={fontStyle:"normal",fontWeight:"normal",fillColor:"#000000"},l="http://www.w3.org/XML/1998/namespace",c="http://www.w3.org/1999/xlink",h=["butt","round","square"],d=["miter","round","bevel"],createObjectURL=function(t,e="",s=!1){if(URL.createObjectURL&&"undefined"!=typeof Blob&&!s)return URL.createObjectURL(new Blob([t],{type:e}));const n="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789+/=";let i=`data:${e};base64,`;for(let e=0,s=t.length;e<s;e+=3){const r=255&t[e],a=255&t[e+1],o=255&t[e+2];i+=n[r>>2]+n[(3&r)<<4|a>>4]+n[e+1<s?(15&a)<<2|o>>6:64]+n[e+2<s?63&o:64]}return i},u=function(){const t=new Uint8Array([137,80,78,71,13,10,26,10]),e=new Int32Array(256);for(let t=0;t<256;t++){let s=t;for(let t=0;t<8;t++)s=1&s?3988292384^s>>1&2147483647:s>>1&2147483647;e[t]=s}function writePngChunk(t,s,n,i){let r=i;const a=s.length;n[r]=a>>24&255;n[r+1]=a>>16&255;n[r+2]=a>>8&255;n[r+3]=255&a;r+=4;n[r]=255&t.charCodeAt(0);n[r+1]=255&t.charCodeAt(1);n[r+2]=255&t.charCodeAt(2);n[r+3]=255&t.charCodeAt(3);r+=4;n.set(s,r);r+=s.length;const o=function crc32(t,s,n){let i=-1;for(let r=s;r<n;r++){const s=255&(i^t[r]);i=i>>>8^e[s]}return-1^i}(n,i+4,r);n[r]=o>>24&255;n[r+1]=o>>16&255;n[r+2]=o>>8&255;n[r+3]=255&o}function deflateSyncUncompressed(t){let e=t.length;const s=65535,n=Math.ceil(e/s),i=new Uint8Array(2+e+5*n+4);let r=0;i[r++]=120;i[r++]=156;let a=0;for(;e>s;){i[r++]=0;i[r++]=255;i[r++]=255;i[r++]=0;i[r++]=0;i.set(t.subarray(a,a+s),r);r+=s;a+=s;e-=s}i[r++]=1;i[r++]=255&e;i[r++]=e>>8&255;i[r++]=255&~e;i[r++]=(65535&~e)>>8&255;i.set(t.subarray(a),r);r+=t.length-a;const o=function adler32(t,e,s){let n=1,i=0;for(let r=e;r<s;++r){n=(n+(255&t[r]))%65521;i=(i+n)%65521}return i<<16|n}(t,0,t.length);i[r++]=o>>24&255;i[r++]=o>>16&255;i[r++]=o>>8&255;i[r++]=255&o;return i}function encode(e,s,n,a){const o=e.width,l=e.height;let c,h,d;const u=e.data;switch(s){case i.ImageKind.GRAYSCALE_1BPP:h=0;c=1;d=o+7>>3;break;case i.ImageKind.RGB_24BPP:h=2;c=8;d=3*o;break;case i.ImageKind.RGBA_32BPP:h=6;c=8;d=4*o;break;default:throw new Error("invalid format")}const p=new Uint8Array((1+d)*l);let g=0,f=0;for(let t=0;t<l;++t){p[g++]=0;p.set(u.subarray(f,f+d),g);f+=d;g+=d}if(s===i.ImageKind.GRAYSCALE_1BPP&&a){g=0;for(let t=0;t<l;t++){g++;for(let t=0;t<d;t++)p[g++]^=255}}const m=new Uint8Array([o>>24&255,o>>16&255,o>>8&255,255&o,l>>24&255,l>>16&255,l>>8&255,255&l,c,h,0,0,0]),b=function deflateSync(t){if(!r.isNodeJS)return deflateSyncUncompressed(t);try{let e;e=parseInt(process.versions.node)>=8?t:Buffer.from(t);const s=require("zlib").deflateSync(e,{level:9});return s instanceof Uint8Array?s:new Uint8Array(s)}catch(t){(0,i.warn)("Not compressing PNG because zlib.deflateSync is unavailable: "+t)}return deflateSyncUncompressed(t)}(p),A=t.length+36+m.length+b.length,_=new Uint8Array(A);let y=0;_.set(t,y);y+=t.length;writePngChunk("IHDR",m,_,y);y+=12+m.length;writePngChunk("IDATA",b,_,y);y+=12+b.length;writePngChunk("IEND",new Uint8Array(0),_,y);return createObjectURL(_,"image/png",n)}return function convertImgDataToPng(t,e,s){return encode(t,void 0===t.kind?i.ImageKind.GRAYSCALE_1BPP:t.kind,e,s)}}();class SVGExtraState{constructor(){this.fontSizeScale=1;this.fontWeight=o.fontWeight;this.fontSize=0;this.textMatrix=i.IDENTITY_MATRIX;this.fontMatrix=i.FONT_IDENTITY_MATRIX;this.leading=0;this.textRenderingMode=i.TextRenderingMode.FILL;this.textMatrixScale=1;this.x=0;this.y=0;this.lineX=0;this.lineY=0;this.charSpacing=0;this.wordSpacing=0;this.textHScale=1;this.textRise=0;this.fillColor=o.fillColor;this.strokeColor="#000000";this.fillAlpha=1;this.strokeAlpha=1;this.lineWidth=1;this.lineJoin="";this.lineCap="";this.miterLimit=0;this.dashArray=[];this.dashPhase=0;this.dependencies=[];this.activeClipUrl=null;this.clipGroup=null;this.maskId=""}clone(){return Object.create(this)}setCurrentPoint(t,e){this.x=t;this.y=e}}function opListToTree(t){let e=[];const s=[];for(const n of t)if("save"!==n.fn)"restore"===n.fn?e=s.pop():e.push(n);else{e.push({fnId:92,fn:"group",items:[]});s.push(e);e=e.at(-1).items}return e}function pf(t){if(Number.isInteger(t))return t.toString();const e=t.toFixed(10);let s=e.length-1;if("0"!==e[s])return e;do{s--}while("0"===e[s]);return e.substring(0,"."===e[s]?s:s+1)}function pm(t){if(0===t[4]&&0===t[5]){if(0===t[1]&&0===t[2])return 1===t[0]&&1===t[3]?"":`scale(${pf(t[0])} ${pf(t[3])})`;if(t[0]===t[3]&&t[1]===-t[2]){return`rotate(${pf(180*Math.acos(t[0])/Math.PI)})`}}else if(1===t[0]&&0===t[1]&&0===t[2]&&1===t[3])return`translate(${pf(t[4])} ${pf(t[5])})`;return`matrix(${pf(t[0])} ${pf(t[1])} ${pf(t[2])} ${pf(t[3])} ${pf(t[4])} ${pf(t[5])})`}let p=0,g=0,f=0;e.SVGGraphics=a=class{constructor(t,e,s=!1){(0,n.deprecated)("The SVG back-end is no longer maintained and *may* be removed in the future.");this.svgFactory=new n.DOMSVGFactory;this.current=new SVGExtraState;this.transformMatrix=i.IDENTITY_MATRIX;this.transformStack=[];this.extraStack=[];this.commonObjs=t;this.objs=e;this.pendingClip=null;this.pendingEOFill=!1;this.embedFonts=!1;this.embeddedFonts=Object.create(null);this.cssStyle=null;this.forceDataSchema=!!s;this._operatorIdMapping=[];for(const t in i.OPS)this._operatorIdMapping[i.OPS[t]]=t}save(){this.transformStack.push(this.transformMatrix);const t=this.current;this.extraStack.push(t);this.current=t.clone()}restore(){this.transformMatrix=this.transformStack.pop();this.current=this.extraStack.pop();this.pendingClip=null;this.tgrp=null}group(t){this.save();this.executeOpTree(t);this.restore()}loadDependencies(t){const e=t.fnArray,s=t.argsArray;for(let t=0,n=e.length;t<n;t++)if(e[t]===i.OPS.dependency)for(const e of s[t]){const t=e.startsWith("g_")?this.commonObjs:this.objs,s=new Promise((s=>{t.get(e,s)}));this.current.dependencies.push(s)}return Promise.all(this.current.dependencies)}transform(t,e,s,n,r,a){const o=[t,e,s,n,r,a];this.transformMatrix=i.Util.transform(this.transformMatrix,o);this.tgrp=null}getSVG(t,e){this.viewport=e;const s=this._initialize(e);return this.loadDependencies(t).then((()=>{this.transformMatrix=i.IDENTITY_MATRIX;this.executeOpTree(this.convertOpList(t));return s}))}convertOpList(t){const e=this._operatorIdMapping,s=t.argsArray,n=t.fnArray,i=[];for(let t=0,r=n.length;t<r;t++){const r=n[t];i.push({fnId:r,fn:e[r],args:s[t]})}return opListToTree(i)}executeOpTree(t){for(const e of t){const t=e.fn,s=e.fnId,n=e.args;switch(0|s){case i.OPS.beginText:this.beginText();break;case i.OPS.dependency:break;case i.OPS.setLeading:this.setLeading(n);break;case i.OPS.setLeadingMoveText:this.setLeadingMoveText(n[0],n[1]);break;case i.OPS.setFont:this.setFont(n);break;case i.OPS.showText:case i.OPS.showSpacedText:this.showText(n[0]);break;case i.OPS.endText:this.endText();break;case i.OPS.moveText:this.moveText(n[0],n[1]);break;case i.OPS.setCharSpacing:this.setCharSpacing(n[0]);break;case i.OPS.setWordSpacing:this.setWordSpacing(n[0]);break;case i.OPS.setHScale:this.setHScale(n[0]);break;case i.OPS.setTextMatrix:this.setTextMatrix(n[0],n[1],n[2],n[3],n[4],n[5]);break;case i.OPS.setTextRise:this.setTextRise(n[0]);break;case i.OPS.setTextRenderingMode:this.setTextRenderingMode(n[0]);break;case i.OPS.setLineWidth:this.setLineWidth(n[0]);break;case i.OPS.setLineJoin:this.setLineJoin(n[0]);break;case i.OPS.setLineCap:this.setLineCap(n[0]);break;case i.OPS.setMiterLimit:this.setMiterLimit(n[0]);break;case i.OPS.setFillRGBColor:this.setFillRGBColor(n[0],n[1],n[2]);break;case i.OPS.setStrokeRGBColor:this.setStrokeRGBColor(n[0],n[1],n[2]);break;case i.OPS.setStrokeColorN:this.setStrokeColorN(n);break;case i.OPS.setFillColorN:this.setFillColorN(n);break;case i.OPS.shadingFill:this.shadingFill(n[0]);break;case i.OPS.setDash:this.setDash(n[0],n[1]);break;case i.OPS.setRenderingIntent:this.setRenderingIntent(n[0]);break;case i.OPS.setFlatness:this.setFlatness(n[0]);break;case i.OPS.setGState:this.setGState(n[0]);break;case i.OPS.fill:this.fill();break;case i.OPS.eoFill:this.eoFill();break;case i.OPS.stroke:this.stroke();break;case i.OPS.fillStroke:this.fillStroke();break;case i.OPS.eoFillStroke:this.eoFillStroke();break;case i.OPS.clip:this.clip("nonzero");break;case i.OPS.eoClip:this.clip("evenodd");break;case i.OPS.paintSolidColorImageMask:this.paintSolidColorImageMask();break;case i.OPS.paintImageXObject:this.paintImageXObject(n[0]);break;case i.OPS.paintInlineImageXObject:this.paintInlineImageXObject(n[0]);break;case i.OPS.paintImageMaskXObject:this.paintImageMaskXObject(n[0]);break;case i.OPS.paintFormXObjectBegin:this.paintFormXObjectBegin(n[0],n[1]);break;case i.OPS.paintFormXObjectEnd:this.paintFormXObjectEnd();break;case i.OPS.closePath:this.closePath();break;case i.OPS.closeStroke:this.closeStroke();break;case i.OPS.closeFillStroke:this.closeFillStroke();break;case i.OPS.closeEOFillStroke:this.closeEOFillStroke();break;case i.OPS.nextLine:this.nextLine();break;case i.OPS.transform:this.transform(n[0],n[1],n[2],n[3],n[4],n[5]);break;case i.OPS.constructPath:this.constructPath(n[0],n[1]);break;case i.OPS.endPath:this.endPath();break;case 92:this.group(e.items);break;default:(0,i.warn)(`Unimplemented operator ${t}`)}}}setWordSpacing(t){this.current.wordSpacing=t}setCharSpacing(t){this.current.charSpacing=t}nextLine(){this.moveText(0,this.current.leading)}setTextMatrix(t,e,s,n,i,r){const a=this.current;a.textMatrix=a.lineMatrix=[t,e,s,n,i,r];a.textMatrixScale=Math.hypot(t,e);a.x=a.lineX=0;a.y=a.lineY=0;a.xcoords=[];a.ycoords=[];a.tspan=this.svgFactory.createElement("svg:tspan");a.tspan.setAttributeNS(null,"font-family",a.fontFamily);a.tspan.setAttributeNS(null,"font-size",`${pf(a.fontSize)}px`);a.tspan.setAttributeNS(null,"y",pf(-a.y));a.txtElement=this.svgFactory.createElement("svg:text");a.txtElement.append(a.tspan)}beginText(){const t=this.current;t.x=t.lineX=0;t.y=t.lineY=0;t.textMatrix=i.IDENTITY_MATRIX;t.lineMatrix=i.IDENTITY_MATRIX;t.textMatrixScale=1;t.tspan=this.svgFactory.createElement("svg:tspan");t.txtElement=this.svgFactory.createElement("svg:text");t.txtgrp=this.svgFactory.createElement("svg:g");t.xcoords=[];t.ycoords=[]}moveText(t,e){const s=this.current;s.x=s.lineX+=t;s.y=s.lineY+=e;s.xcoords=[];s.ycoords=[];s.tspan=this.svgFactory.createElement("svg:tspan");s.tspan.setAttributeNS(null,"font-family",s.fontFamily);s.tspan.setAttributeNS(null,"font-size",`${pf(s.fontSize)}px`);s.tspan.setAttributeNS(null,"y",pf(-s.y))}showText(t){const e=this.current,s=e.font,n=e.fontSize;if(0===n)return;const r=e.fontSizeScale,a=e.charSpacing,c=e.wordSpacing,h=e.fontDirection,d=e.textHScale*h,u=s.vertical,p=u?1:-1,g=s.defaultVMetrics,f=n*e.fontMatrix[0];let m=0;for(const i of t){if(null===i){m+=h*c;continue}if("number"==typeof i){m+=p*i*n/1e3;continue}const t=(i.isSpace?c:0)+a,o=i.fontChar;let l,d,b,A=i.width;if(u){let t;const e=i.vmetric||g;t=i.vmetric?e[1]:.5*A;t=-t*f;const s=e[2]*f;A=e?-e[0]:A;l=t/r;d=(m+s)/r}else{l=m/r;d=0}if(i.isInFont||s.missingFile){e.xcoords.push(e.x+l);u&&e.ycoords.push(-e.y+d);e.tspan.textContent+=o}b=u?A*f-t*h:A*f+t*h;m+=b}e.tspan.setAttributeNS(null,"x",e.xcoords.map(pf).join(" "));u?e.tspan.setAttributeNS(null,"y",e.ycoords.map(pf).join(" ")):e.tspan.setAttributeNS(null,"y",pf(-e.y));u?e.y-=m:e.x+=m*d;e.tspan.setAttributeNS(null,"font-family",e.fontFamily);e.tspan.setAttributeNS(null,"font-size",`${pf(e.fontSize)}px`);e.fontStyle!==o.fontStyle&&e.tspan.setAttributeNS(null,"font-style",e.fontStyle);e.fontWeight!==o.fontWeight&&e.tspan.setAttributeNS(null,"font-weight",e.fontWeight);const b=e.textRenderingMode&i.TextRenderingMode.FILL_STROKE_MASK;if(b===i.TextRenderingMode.FILL||b===i.TextRenderingMode.FILL_STROKE){e.fillColor!==o.fillColor&&e.tspan.setAttributeNS(null,"fill",e.fillColor);e.fillAlpha<1&&e.tspan.setAttributeNS(null,"fill-opacity",e.fillAlpha)}else e.textRenderingMode===i.TextRenderingMode.ADD_TO_PATH?e.tspan.setAttributeNS(null,"fill","transparent"):e.tspan.setAttributeNS(null,"fill","none");if(b===i.TextRenderingMode.STROKE||b===i.TextRenderingMode.FILL_STROKE){const t=1/(e.textMatrixScale||1);this._setStrokeAttributes(e.tspan,t)}let A=e.textMatrix;if(0!==e.textRise){A=A.slice();A[5]+=e.textRise}e.txtElement.setAttributeNS(null,"transform",`${pm(A)} scale(${pf(d)}, -1)`);e.txtElement.setAttributeNS(l,"xml:space","preserve");e.txtElement.append(e.tspan);e.txtgrp.append(e.txtElement);this._ensureTransformGroup().append(e.txtElement)}setLeadingMoveText(t,e){this.setLeading(-e);this.moveText(t,e)}addFontStyle(t){if(!t.data)throw new Error('addFontStyle: No font data available, ensure that the "fontExtraProperties" API parameter is set.');if(!this.cssStyle){this.cssStyle=this.svgFactory.createElement("svg:style");this.cssStyle.setAttributeNS(null,"type","text/css");this.defs.append(this.cssStyle)}const e=createObjectURL(t.data,t.mimetype,this.forceDataSchema);this.cssStyle.textContent+=`@font-face { font-family: "${t.loadedName}"; src: url(${e}); }\n`}setFont(t){const e=this.current,s=this.commonObjs.get(t[0]);let n=t[1];e.font=s;if(this.embedFonts&&!s.missingFile&&!this.embeddedFonts[s.loadedName]){this.addFontStyle(s);this.embeddedFonts[s.loadedName]=s}e.fontMatrix=s.fontMatrix||i.FONT_IDENTITY_MATRIX;let r="normal";s.black?r="900":s.bold&&(r="bold");const a=s.italic?"italic":"normal";if(n<0){n=-n;e.fontDirection=-1}else e.fontDirection=1;e.fontSize=n;e.fontFamily=s.loadedName;e.fontWeight=r;e.fontStyle=a;e.tspan=this.svgFactory.createElement("svg:tspan");e.tspan.setAttributeNS(null,"y",pf(-e.y));e.xcoords=[];e.ycoords=[]}endText(){const t=this.current;if(t.textRenderingMode&i.TextRenderingMode.ADD_TO_PATH_FLAG&&t.txtElement?.hasChildNodes()){t.element=t.txtElement;this.clip("nonzero");this.endPath()}}setLineWidth(t){t>0&&(this.current.lineWidth=t)}setLineCap(t){this.current.lineCap=h[t]}setLineJoin(t){this.current.lineJoin=d[t]}setMiterLimit(t){this.current.miterLimit=t}setStrokeAlpha(t){this.current.strokeAlpha=t}setStrokeRGBColor(t,e,s){this.current.strokeColor=i.Util.makeHexColor(t,e,s)}setFillAlpha(t){this.current.fillAlpha=t}setFillRGBColor(t,e,s){this.current.fillColor=i.Util.makeHexColor(t,e,s);this.current.tspan=this.svgFactory.createElement("svg:tspan");this.current.xcoords=[];this.current.ycoords=[]}setStrokeColorN(t){this.current.strokeColor=this._makeColorN_Pattern(t)}setFillColorN(t){this.current.fillColor=this._makeColorN_Pattern(t)}shadingFill(t){const e=this.viewport.width,s=this.viewport.height,n=i.Util.inverseTransform(this.transformMatrix),r=i.Util.applyTransform([0,0],n),a=i.Util.applyTransform([0,s],n),o=i.Util.applyTransform([e,0],n),l=i.Util.applyTransform([e,s],n),c=Math.min(r[0],a[0],o[0],l[0]),h=Math.min(r[1],a[1],o[1],l[1]),d=Math.max(r[0],a[0],o[0],l[0]),u=Math.max(r[1],a[1],o[1],l[1]),p=this.svgFactory.createElement("svg:rect");p.setAttributeNS(null,"x",c);p.setAttributeNS(null,"y",h);p.setAttributeNS(null,"width",d-c);p.setAttributeNS(null,"height",u-h);p.setAttributeNS(null,"fill",this._makeShadingPattern(t));this.current.fillAlpha<1&&p.setAttributeNS(null,"fill-opacity",this.current.fillAlpha);this._ensureTransformGroup().append(p)}_makeColorN_Pattern(t){return"TilingPattern"===t[0]?this._makeTilingPattern(t):this._makeShadingPattern(t)}_makeTilingPattern(t){const e=t[1],s=t[2],n=t[3]||i.IDENTITY_MATRIX,[r,a,o,l]=t[4],c=t[5],h=t[6],d=t[7],u="shading"+f++,[p,g,m,b]=i.Util.normalizeRect([...i.Util.applyTransform([r,a],n),...i.Util.applyTransform([o,l],n)]),[A,_]=i.Util.singularValueDecompose2dScale(n),y=c*A,v=h*_,S=this.svgFactory.createElement("svg:pattern");S.setAttributeNS(null,"id",u);S.setAttributeNS(null,"patternUnits","userSpaceOnUse");S.setAttributeNS(null,"width",y);S.setAttributeNS(null,"height",v);S.setAttributeNS(null,"x",`${p}`);S.setAttributeNS(null,"y",`${g}`);const x=this.svg,E=this.transformMatrix,C=this.current.fillColor,P=this.current.strokeColor,T=this.svgFactory.create(m-p,b-g);this.svg=T;this.transformMatrix=n;if(2===d){const t=i.Util.makeHexColor(...e);this.current.fillColor=t;this.current.strokeColor=t}this.executeOpTree(this.convertOpList(s));this.svg=x;this.transformMatrix=E;this.current.fillColor=C;this.current.strokeColor=P;S.append(T.childNodes[0]);this.defs.append(S);return`url(#${u})`}_makeShadingPattern(t){"string"==typeof t&&(t=this.objs.get(t));switch(t[0]){case"RadialAxial":const e="shading"+f++,s=t[3];let n;switch(t[1]){case"axial":const s=t[4],i=t[5];n=this.svgFactory.createElement("svg:linearGradient");n.setAttributeNS(null,"id",e);n.setAttributeNS(null,"gradientUnits","userSpaceOnUse");n.setAttributeNS(null,"x1",s[0]);n.setAttributeNS(null,"y1",s[1]);n.setAttributeNS(null,"x2",i[0]);n.setAttributeNS(null,"y2",i[1]);break;case"radial":const r=t[4],a=t[5],o=t[6],l=t[7];n=this.svgFactory.createElement("svg:radialGradient");n.setAttributeNS(null,"id",e);n.setAttributeNS(null,"gradientUnits","userSpaceOnUse");n.setAttributeNS(null,"cx",a[0]);n.setAttributeNS(null,"cy",a[1]);n.setAttributeNS(null,"r",l);n.setAttributeNS(null,"fx",r[0]);n.setAttributeNS(null,"fy",r[1]);n.setAttributeNS(null,"fr",o);break;default:throw new Error(`Unknown RadialAxial type: ${t[1]}`)}for(const t of s){const e=this.svgFactory.createElement("svg:stop");e.setAttributeNS(null,"offset",t[0]);e.setAttributeNS(null,"stop-color",t[1]);n.append(e)}this.defs.append(n);return`url(#${e})`;case"Mesh":(0,i.warn)("Unimplemented pattern Mesh");return null;case"Dummy":return"hotpink";default:throw new Error(`Unknown IR type: ${t[0]}`)}}setDash(t,e){this.current.dashArray=t;this.current.dashPhase=e}constructPath(t,e){const s=this.current;let n=s.x,r=s.y,a=[],o=0;for(const s of t)switch(0|s){case i.OPS.rectangle:n=e[o++];r=e[o++];const t=n+e[o++],s=r+e[o++];a.push("M",pf(n),pf(r),"L",pf(t),pf(r),"L",pf(t),pf(s),"L",pf(n),pf(s),"Z");break;case i.OPS.moveTo:n=e[o++];r=e[o++];a.push("M",pf(n),pf(r));break;case i.OPS.lineTo:n=e[o++];r=e[o++];a.push("L",pf(n),pf(r));break;case i.OPS.curveTo:n=e[o+4];r=e[o+5];a.push("C",pf(e[o]),pf(e[o+1]),pf(e[o+2]),pf(e[o+3]),pf(n),pf(r));o+=6;break;case i.OPS.curveTo2:a.push("C",pf(n),pf(r),pf(e[o]),pf(e[o+1]),pf(e[o+2]),pf(e[o+3]));n=e[o+2];r=e[o+3];o+=4;break;case i.OPS.curveTo3:n=e[o+2];r=e[o+3];a.push("C",pf(e[o]),pf(e[o+1]),pf(n),pf(r),pf(n),pf(r));o+=4;break;case i.OPS.closePath:a.push("Z")}a=a.join(" ");if(s.path&&t.length>0&&t[0]!==i.OPS.rectangle&&t[0]!==i.OPS.moveTo)a=s.path.getAttributeNS(null,"d")+a;else{s.path=this.svgFactory.createElement("svg:path");this._ensureTransformGroup().append(s.path)}s.path.setAttributeNS(null,"d",a);s.path.setAttributeNS(null,"fill","none");s.element=s.path;s.setCurrentPoint(n,r)}endPath(){const t=this.current;t.path=null;if(!this.pendingClip)return;if(!t.element){this.pendingClip=null;return}const e="clippath"+p++,s=this.svgFactory.createElement("svg:clipPath");s.setAttributeNS(null,"id",e);s.setAttributeNS(null,"transform",pm(this.transformMatrix));const n=t.element.cloneNode(!0);"evenodd"===this.pendingClip?n.setAttributeNS(null,"clip-rule","evenodd"):n.setAttributeNS(null,"clip-rule","nonzero");this.pendingClip=null;s.append(n);this.defs.append(s);if(t.activeClipUrl){t.clipGroup=null;for(const t of this.extraStack)t.clipGroup=null;s.setAttributeNS(null,"clip-path",t.activeClipUrl)}t.activeClipUrl=`url(#${e})`;this.tgrp=null}clip(t){this.pendingClip=t}closePath(){const t=this.current;if(t.path){const e=`${t.path.getAttributeNS(null,"d")}Z`;t.path.setAttributeNS(null,"d",e)}}setLeading(t){this.current.leading=-t}setTextRise(t){this.current.textRise=t}setTextRenderingMode(t){this.current.textRenderingMode=t}setHScale(t){this.current.textHScale=t/100}setRenderingIntent(t){}setFlatness(t){}setGState(t){for(const[e,s]of t)switch(e){case"LW":this.setLineWidth(s);break;case"LC":this.setLineCap(s);break;case"LJ":this.setLineJoin(s);break;case"ML":this.setMiterLimit(s);break;case"D":this.setDash(s[0],s[1]);break;case"RI":this.setRenderingIntent(s);break;case"FL":this.setFlatness(s);break;case"Font":this.setFont(s);break;case"CA":this.setStrokeAlpha(s);break;case"ca":this.setFillAlpha(s);break;default:(0,i.warn)(`Unimplemented graphic state operator ${e}`)}}fill(){const t=this.current;if(t.element){t.element.setAttributeNS(null,"fill",t.fillColor);t.element.setAttributeNS(null,"fill-opacity",t.fillAlpha);this.endPath()}}stroke(){const t=this.current;if(t.element){this._setStrokeAttributes(t.element);t.element.setAttributeNS(null,"fill","none");this.endPath()}}_setStrokeAttributes(t,e=1){const s=this.current;let n=s.dashArray;1!==e&&n.length>0&&(n=n.map((function(t){return e*t})));t.setAttributeNS(null,"stroke",s.strokeColor);t.setAttributeNS(null,"stroke-opacity",s.strokeAlpha);t.setAttributeNS(null,"stroke-miterlimit",pf(s.miterLimit));t.setAttributeNS(null,"stroke-linecap",s.lineCap);t.setAttributeNS(null,"stroke-linejoin",s.lineJoin);t.setAttributeNS(null,"stroke-width",pf(e*s.lineWidth)+"px");t.setAttributeNS(null,"stroke-dasharray",n.map(pf).join(" "));t.setAttributeNS(null,"stroke-dashoffset",pf(e*s.dashPhase)+"px")}eoFill(){this.current.element&&this.current.element.setAttributeNS(null,"fill-rule","evenodd");this.fill()}fillStroke(){this.stroke();this.fill()}eoFillStroke(){this.current.element&&this.current.element.setAttributeNS(null,"fill-rule","evenodd");this.fillStroke()}closeStroke(){this.closePath();this.stroke()}closeFillStroke(){this.closePath();this.fillStroke()}closeEOFillStroke(){this.closePath();this.eoFillStroke()}paintSolidColorImageMask(){const t=this.svgFactory.createElement("svg:rect");t.setAttributeNS(null,"x","0");t.setAttributeNS(null,"y","0");t.setAttributeNS(null,"width","1px");t.setAttributeNS(null,"height","1px");t.setAttributeNS(null,"fill",this.current.fillColor);this._ensureTransformGroup().append(t)}paintImageXObject(t){const e=t.startsWith("g_")?this.commonObjs.get(t):this.objs.get(t);e?this.paintInlineImageXObject(e):(0,i.warn)(`Dependent image with object ID ${t} is not ready yet`)}paintInlineImageXObject(t,e){const s=t.width,n=t.height,i=u(t,this.forceDataSchema,!!e),r=this.svgFactory.createElement("svg:rect");r.setAttributeNS(null,"x","0");r.setAttributeNS(null,"y","0");r.setAttributeNS(null,"width",pf(s));r.setAttributeNS(null,"height",pf(n));this.current.element=r;this.clip("nonzero");const a=this.svgFactory.createElement("svg:image");a.setAttributeNS(c,"xlink:href",i);a.setAttributeNS(null,"x","0");a.setAttributeNS(null,"y",pf(-n));a.setAttributeNS(null,"width",pf(s)+"px");a.setAttributeNS(null,"height",pf(n)+"px");a.setAttributeNS(null,"transform",`scale(${pf(1/s)} ${pf(-1/n)})`);e?e.append(a):this._ensureTransformGroup().append(a)}paintImageMaskXObject(t){const e=this.current,s=t.width,n=t.height,i=e.fillColor;e.maskId="mask"+g++;const r=this.svgFactory.createElement("svg:mask");r.setAttributeNS(null,"id",e.maskId);const a=this.svgFactory.createElement("svg:rect");a.setAttributeNS(null,"x","0");a.setAttributeNS(null,"y","0");a.setAttributeNS(null,"width",pf(s));a.setAttributeNS(null,"height",pf(n));a.setAttributeNS(null,"fill",i);a.setAttributeNS(null,"mask",`url(#${e.maskId})`);this.defs.append(r);this._ensureTransformGroup().append(a);this.paintInlineImageXObject(t,r)}paintFormXObjectBegin(t,e){Array.isArray(t)&&6===t.length&&this.transform(t[0],t[1],t[2],t[3],t[4],t[5]);if(e){const t=e[2]-e[0],s=e[3]-e[1],n=this.svgFactory.createElement("svg:rect");n.setAttributeNS(null,"x",e[0]);n.setAttributeNS(null,"y",e[1]);n.setAttributeNS(null,"width",pf(t));n.setAttributeNS(null,"height",pf(s));this.current.element=n;this.clip("nonzero");this.endPath()}}paintFormXObjectEnd(){}_initialize(t){const e=this.svgFactory.create(t.width,t.height),s=this.svgFactory.createElement("svg:defs");e.append(s);this.defs=s;const n=this.svgFactory.createElement("svg:g");n.setAttributeNS(null,"transform",pm(t.transform));e.append(n);this.svg=n;return e}_ensureClipGroup(){if(!this.current.clipGroup){const t=this.svgFactory.createElement("svg:g");t.setAttributeNS(null,"clip-path",this.current.activeClipUrl);this.svg.append(t);this.current.clipGroup=t}return this.current.clipGroup}_ensureTransformGroup(){if(!this.tgrp){this.tgrp=this.svgFactory.createElement("svg:g");this.tgrp.setAttributeNS(null,"transform",pm(this.transformMatrix));this.current.activeClipUrl?this._ensureClipGroup().append(this.tgrp):this.svg.append(this.tgrp)}return this.tgrp}}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.PDFNodeStream=void 0;var n=s(1),i=s(33);const r=require("fs"),a=require("http"),o=require("https"),l=require("url"),c=/^file:\/\/\/[a-zA-Z]:\//;e.PDFNodeStream=class PDFNodeStream{constructor(t){this.source=t;this.url=function parseUrl(t){const e=l.parse(t);if("file:"===e.protocol||e.host)return e;if(/^[a-z]:[/\\]/i.test(t))return l.parse(`file:///${t}`);e.host||(e.protocol="file:");return e}(t.url);this.isHttp="http:"===this.url.protocol||"https:"===this.url.protocol;this.isFsUrl="file:"===this.url.protocol;this.httpHeaders=this.isHttp&&t.httpHeaders||{};this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){(0,n.assert)(!this._fullRequestReader,"PDFNodeStream.getFullReader can only be called once.");this._fullRequestReader=this.isFsUrl?new PDFNodeStreamFsFullReader(this):new PDFNodeStreamFullReader(this);return this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=this.isFsUrl?new PDFNodeStreamFsRangeReader(this,t,e):new PDFNodeStreamRangeReader(this,t,e);this._rangeRequestReaders.push(s);return s}cancelAllRequests(t){this._fullRequestReader&&this._fullRequestReader.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}};class BaseFullReader{constructor(t){this._url=t.url;this._done=!1;this._storedError=null;this.onProgress=null;const e=t.source;this._contentLength=e.length;this._loaded=0;this._filename=null;this._disableRange=e.disableRange||!1;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!e.disableStream;this._isRangeSupported=!e.disableRange;this._readableStream=null;this._readCapability=(0,n.createPromiseCapability)();this._headersCapability=(0,n.createPromiseCapability)()}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t){this._readCapability=(0,n.createPromiseCapability)();return this.read()}this._loaded+=t.length;this.onProgress&&this.onProgress({loaded:this._loaded,total:this._contentLength});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t;this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t;t.on("readable",(()=>{this._readCapability.resolve()}));t.on("end",(()=>{t.destroy();this._done=!0;this._readCapability.resolve()}));t.on("error",(t=>{this._error(t)}));!this._isStreamingSupported&&this._isRangeSupported&&this._error(new n.AbortException("streaming is disabled"));this._storedError&&this._readableStream.destroy(this._storedError)}}class BaseRangeReader{constructor(t){this._url=t.url;this._done=!1;this._storedError=null;this.onProgress=null;this._loaded=0;this._readableStream=null;this._readCapability=(0,n.createPromiseCapability)();const e=t.source;this._isStreamingSupported=!e.disableStream}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;if(this._done)return{value:void 0,done:!0};if(this._storedError)throw this._storedError;const t=this._readableStream.read();if(null===t){this._readCapability=(0,n.createPromiseCapability)();return this.read()}this._loaded+=t.length;this.onProgress&&this.onProgress({loaded:this._loaded});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._readableStream?this._readableStream.destroy(t):this._error(t)}_error(t){this._storedError=t;this._readCapability.resolve()}_setReadableStream(t){this._readableStream=t;t.on("readable",(()=>{this._readCapability.resolve()}));t.on("end",(()=>{t.destroy();this._done=!0;this._readCapability.resolve()}));t.on("error",(t=>{this._error(t)}));this._storedError&&this._readableStream.destroy(this._storedError)}}function createRequestOptions(t,e){return{protocol:t.protocol,auth:t.auth,host:t.hostname,port:t.port,path:t.path,method:"GET",headers:e}}class PDFNodeStreamFullReader extends BaseFullReader{constructor(t){super(t);const handleResponse=e=>{if(404===e.statusCode){const t=new n.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=t;this._headersCapability.reject(t);return}this._headersCapability.resolve();this._setReadableStream(e);const getResponseHeader=t=>this._readableStream.headers[t.toLowerCase()],{allowRangeRequests:s,suggestedLength:r}=(0,i.validateRangeRequestCapabilities)({getResponseHeader:getResponseHeader,isHttp:t.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=s;this._contentLength=r||this._contentLength;this._filename=(0,i.extractFilenameFromHeader)(getResponseHeader)};this._request=null;"http:"===this._url.protocol?this._request=a.request(createRequestOptions(this._url,t.httpHeaders),handleResponse):this._request=o.request(createRequestOptions(this._url,t.httpHeaders),handleResponse);this._request.on("error",(t=>{this._storedError=t;this._headersCapability.reject(t)}));this._request.end()}}class PDFNodeStreamRangeReader extends BaseRangeReader{constructor(t,e,s){super(t);this._httpHeaders={};for(const e in t.httpHeaders){const s=t.httpHeaders[e];void 0!==s&&(this._httpHeaders[e]=s)}this._httpHeaders.Range=`bytes=${e}-${s-1}`;const handleResponse=t=>{if(404!==t.statusCode)this._setReadableStream(t);else{const t=new n.MissingPDFException(`Missing PDF "${this._url}".`);this._storedError=t}};this._request=null;"http:"===this._url.protocol?this._request=a.request(createRequestOptions(this._url,this._httpHeaders),handleResponse):this._request=o.request(createRequestOptions(this._url,this._httpHeaders),handleResponse);this._request.on("error",(t=>{this._storedError=t}));this._request.end()}}class PDFNodeStreamFsFullReader extends BaseFullReader{constructor(t){super(t);let e=decodeURIComponent(this._url.path);c.test(this._url.href)&&(e=e.replace(/^\//,""));r.lstat(e,((t,s)=>{if(t){"ENOENT"===t.code&&(t=new n.MissingPDFException(`Missing PDF "${e}".`));this._storedError=t;this._headersCapability.reject(t)}else{this._contentLength=s.size;this._setReadableStream(r.createReadStream(e));this._headersCapability.resolve()}}))}}class PDFNodeStreamFsRangeReader extends BaseRangeReader{constructor(t,e,s){super(t);let n=decodeURIComponent(this._url.path);c.test(this._url.href)&&(n=n.replace(/^\//,""));this._setReadableStream(r.createReadStream(n,{start:e,end:s-1}))}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.createResponseStatusError=function createResponseStatusError(t,e){if(404===t||0===t&&e.startsWith("file:"))return new n.MissingPDFException('Missing PDF "'+e+'".');return new n.UnexpectedResponseException(`Unexpected server response (${t}) while retrieving PDF "${e}".`,t)};e.extractFilenameFromHeader=function extractFilenameFromHeader(t){const e=t("Content-Disposition");if(e){let t=(0,i.getFilenameFromContentDispositionHeader)(e);if(t.includes("%"))try{t=decodeURIComponent(t)}catch(t){}if((0,r.isPdfFile)(t))return t}return null};e.validateRangeRequestCapabilities=function validateRangeRequestCapabilities({getResponseHeader:t,isHttp:e,rangeChunkSize:s,disableRange:n}){const i={allowRangeRequests:!1,suggestedLength:void 0},r=parseInt(t("Content-Length"),10);if(!Number.isInteger(r))return i;i.suggestedLength=r;if(r<=2*s)return i;if(n||!e)return i;if("bytes"!==t("Accept-Ranges"))return i;if("identity"!==(t("Content-Encoding")||"identity"))return i;i.allowRangeRequests=!0;return i};e.validateResponseStatus=function validateResponseStatus(t){return 200===t||206===t};var n=s(1),i=s(34),r=s(8)},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.getFilenameFromContentDispositionHeader=function getFilenameFromContentDispositionHeader(t){let e=!0,s=toParamRegExp("filename\\*","i").exec(t);if(s){s=s[1];let t=rfc2616unquote(s);t=unescape(t);t=rfc5987decode(t);t=rfc2047decode(t);return fixupEncoding(t)}s=function rfc2231getparam(t){const e=[];let s;const n=toParamRegExp("filename\\*((?!0\\d)\\d+)(\\*?)","ig");for(;null!==(s=n.exec(t));){let[,t,n,i]=s;t=parseInt(t,10);if(t in e){if(0===t)break}else e[t]=[n,i]}const i=[];for(let t=0;t<e.length&&t in e;++t){let[s,n]=e[t];n=rfc2616unquote(n);if(s){n=unescape(n);0===t&&(n=rfc5987decode(n))}i.push(n)}return i.join("")}(t);if(s){return fixupEncoding(rfc2047decode(s))}s=toParamRegExp("filename","i").exec(t);if(s){s=s[1];let t=rfc2616unquote(s);t=rfc2047decode(t);return fixupEncoding(t)}function toParamRegExp(t,e){return new RegExp("(?:^|;)\\s*"+t+'\\s*=\\s*([^";\\s][^;\\s]*|"(?:[^"\\\\]|\\\\"?)+"?)',e)}function textdecode(t,s){if(t){if(!/^[\x00-\xFF]+$/.test(s))return s;try{const i=new TextDecoder(t,{fatal:!0}),r=(0,n.stringToBytes)(s);s=i.decode(r);e=!1}catch(t){}}return s}function fixupEncoding(t){if(e&&/[\x80-\xff]/.test(t)){t=textdecode("utf-8",t);e&&(t=textdecode("iso-8859-1",t))}return t}function rfc2616unquote(t){if(t.startsWith('"')){const e=t.slice(1).split('\\"');for(let t=0;t<e.length;++t){const s=e[t].indexOf('"');if(-1!==s){e[t]=e[t].slice(0,s);e.length=t+1}e[t]=e[t].replace(/\\(.)/g,"$1")}t=e.join('"')}return t}function rfc5987decode(t){const e=t.indexOf("'");if(-1===e)return t;return textdecode(t.slice(0,e),t.slice(e+1).replace(/^[^']*'/,""))}function rfc2047decode(t){return!t.startsWith("=?")||/[\x00-\x19\x80-\xff]/.test(t)?t:t.replace(/=\?([\w-]*)\?([QqBb])\?((?:[^?]|\?(?!=))*)\?=/g,(function(t,e,s,n){if("q"===s||"Q"===s)return textdecode(e,n=(n=n.replace(/_/g," ")).replace(/=([0-9a-fA-F]{2})/g,(function(t,e){return String.fromCharCode(parseInt(e,16))})));try{n=atob(n)}catch(t){}return textdecode(e,n)}))}return""};var n=s(1)},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.PDFNetworkStream=void 0;var n=s(1),i=s(33);class NetworkManager{constructor(t,e={}){this.url=t;this.isHttp=/^https?:/i.test(t);this.httpHeaders=this.isHttp&&e.httpHeaders||Object.create(null);this.withCredentials=e.withCredentials||!1;this.getXhr=e.getXhr||function NetworkManager_getXhr(){return new XMLHttpRequest};this.currXhrId=0;this.pendingRequests=Object.create(null)}requestRange(t,e,s){const n={begin:t,end:e};for(const t in s)n[t]=s[t];return this.request(n)}requestFull(t){return this.request(t)}request(t){const e=this.getXhr(),s=this.currXhrId++,n=this.pendingRequests[s]={xhr:e};e.open("GET",this.url);e.withCredentials=this.withCredentials;for(const t in this.httpHeaders){const s=this.httpHeaders[t];void 0!==s&&e.setRequestHeader(t,s)}if(this.isHttp&&"begin"in t&&"end"in t){e.setRequestHeader("Range",`bytes=${t.begin}-${t.end-1}`);n.expectedStatus=206}else n.expectedStatus=200;e.responseType="arraybuffer";t.onError&&(e.onerror=function(s){t.onError(e.status)});e.onreadystatechange=this.onStateChange.bind(this,s);e.onprogress=this.onProgress.bind(this,s);n.onHeadersReceived=t.onHeadersReceived;n.onDone=t.onDone;n.onError=t.onError;n.onProgress=t.onProgress;e.send(null);return s}onProgress(t,e){const s=this.pendingRequests[t];s&&s.onProgress?.(e)}onStateChange(t,e){const s=this.pendingRequests[t];if(!s)return;const i=s.xhr;if(i.readyState>=2&&s.onHeadersReceived){s.onHeadersReceived();delete s.onHeadersReceived}if(4!==i.readyState)return;if(!(t in this.pendingRequests))return;delete this.pendingRequests[t];if(0===i.status&&this.isHttp){s.onError?.(i.status);return}const r=i.status||200;if(!(200===r&&206===s.expectedStatus)&&r!==s.expectedStatus){s.onError?.(i.status);return}const a=function getArrayBuffer(t){const e=t.response;return"string"!=typeof e?e:(0,n.stringToBytes)(e).buffer}(i);if(206===r){const t=i.getResponseHeader("Content-Range"),e=/bytes (\d+)-(\d+)\/(\d+)/.exec(t);s.onDone({begin:parseInt(e[1],10),chunk:a})}else a?s.onDone({begin:0,chunk:a}):s.onError?.(i.status)}getRequestXhr(t){return this.pendingRequests[t].xhr}isPendingRequest(t){return t in this.pendingRequests}abortRequest(t){const e=this.pendingRequests[t].xhr;delete this.pendingRequests[t];e.abort()}}e.PDFNetworkStream=class PDFNetworkStream{constructor(t){this._source=t;this._manager=new NetworkManager(t.url,{httpHeaders:t.httpHeaders,withCredentials:t.withCredentials});this._rangeChunkSize=t.rangeChunkSize;this._fullRequestReader=null;this._rangeRequestReaders=[]}_onRangeRequestReaderClosed(t){const e=this._rangeRequestReaders.indexOf(t);e>=0&&this._rangeRequestReaders.splice(e,1)}getFullReader(){(0,n.assert)(!this._fullRequestReader,"PDFNetworkStream.getFullReader can only be called once.");this._fullRequestReader=new PDFNetworkStreamFullRequestReader(this._manager,this._source);return this._fullRequestReader}getRangeReader(t,e){const s=new PDFNetworkStreamRangeRequestReader(this._manager,t,e);s.onClosed=this._onRangeRequestReaderClosed.bind(this);this._rangeRequestReaders.push(s);return s}cancelAllRequests(t){this._fullRequestReader?.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}};class PDFNetworkStreamFullRequestReader{constructor(t,e){this._manager=t;const s={onHeadersReceived:this._onHeadersReceived.bind(this),onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=e.url;this._fullRequestId=t.requestFull(s);this._headersReceivedCapability=(0,n.createPromiseCapability)();this._disableRange=e.disableRange||!1;this._contentLength=e.length;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._isStreamingSupported=!1;this._isRangeSupported=!1;this._cachedChunks=[];this._requests=[];this._done=!1;this._storedError=void 0;this._filename=null;this.onProgress=null}_onHeadersReceived(){const t=this._fullRequestId,e=this._manager.getRequestXhr(t),getResponseHeader=t=>e.getResponseHeader(t),{allowRangeRequests:s,suggestedLength:n}=(0,i.validateRangeRequestCapabilities)({getResponseHeader:getResponseHeader,isHttp:this._manager.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});s&&(this._isRangeSupported=!0);this._contentLength=n||this._contentLength;this._filename=(0,i.extractFilenameFromHeader)(getResponseHeader);this._isRangeSupported&&this._manager.abortRequest(t);this._headersReceivedCapability.resolve()}_onDone(t){if(t)if(this._requests.length>0){this._requests.shift().resolve({value:t.chunk,done:!1})}else this._cachedChunks.push(t.chunk);this._done=!0;if(!(this._cachedChunks.length>0)){for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0}}_onError(t){this._storedError=(0,i.createResponseStatusError)(t,this._url);this._headersReceivedCapability.reject(this._storedError);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0;this._cachedChunks.length=0}_onProgress(t){this.onProgress?.({loaded:t.loaded,total:t.lengthComputable?t.total:this._contentLength})}get filename(){return this._filename}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}get contentLength(){return this._contentLength}get headersReady(){return this._headersReceivedCapability.promise}async read(){if(this._storedError)throw this._storedError;if(this._cachedChunks.length>0){return{value:this._cachedChunks.shift(),done:!1}}if(this._done)return{value:void 0,done:!0};const t=(0,n.createPromiseCapability)();this._requests.push(t);return t.promise}cancel(t){this._done=!0;this._headersReceivedCapability.reject(t);for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._fullRequestId)&&this._manager.abortRequest(this._fullRequestId);this._fullRequestReader=null}}class PDFNetworkStreamRangeRequestReader{constructor(t,e,s){this._manager=t;const n={onDone:this._onDone.bind(this),onError:this._onError.bind(this),onProgress:this._onProgress.bind(this)};this._url=t.url;this._requestId=t.requestRange(e,s,n);this._requests=[];this._queuedChunk=null;this._done=!1;this._storedError=void 0;this.onProgress=null;this.onClosed=null}_close(){this.onClosed?.(this)}_onDone(t){const e=t.chunk;if(this._requests.length>0){this._requests.shift().resolve({value:e,done:!1})}else this._queuedChunk=e;this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._close()}_onError(t){this._storedError=(0,i.createResponseStatusError)(t,this._url);for(const t of this._requests)t.reject(this._storedError);this._requests.length=0;this._queuedChunk=null}_onProgress(t){this.isStreamingSupported||this.onProgress?.({loaded:t.loaded})}get isStreamingSupported(){return!1}async read(){if(this._storedError)throw this._storedError;if(null!==this._queuedChunk){const t=this._queuedChunk;this._queuedChunk=null;return{value:t,done:!1}}if(this._done)return{value:void 0,done:!0};const t=(0,n.createPromiseCapability)();this._requests.push(t);return t.promise}cancel(t){this._done=!0;for(const t of this._requests)t.resolve({value:void 0,done:!0});this._requests.length=0;this._manager.isPendingRequest(this._requestId)&&this._manager.abortRequest(this._requestId);this._close()}}},(t,e,s)=>{Object.defineProperty(e,"__esModule",{value:!0});e.PDFFetchStream=void 0;var n=s(1),i=s(33);function createFetchOptions(t,e,s){return{method:"GET",headers:t,signal:s.signal,mode:"cors",credentials:e?"include":"same-origin",redirect:"follow"}}function createHeaders(t){const e=new Headers;for(const s in t){const n=t[s];void 0!==n&&e.append(s,n)}return e}e.PDFFetchStream=class PDFFetchStream{constructor(t){this.source=t;this.isHttp=/^https?:/i.test(t.url);this.httpHeaders=this.isHttp&&t.httpHeaders||{};this._fullRequestReader=null;this._rangeRequestReaders=[]}get _progressiveDataLength(){return this._fullRequestReader?._loaded??0}getFullReader(){(0,n.assert)(!this._fullRequestReader,"PDFFetchStream.getFullReader can only be called once.");this._fullRequestReader=new PDFFetchStreamReader(this);return this._fullRequestReader}getRangeReader(t,e){if(e<=this._progressiveDataLength)return null;const s=new PDFFetchStreamRangeReader(this,t,e);this._rangeRequestReaders.push(s);return s}cancelAllRequests(t){this._fullRequestReader&&this._fullRequestReader.cancel(t);for(const e of this._rangeRequestReaders.slice(0))e.cancel(t)}};class PDFFetchStreamReader{constructor(t){this._stream=t;this._reader=null;this._loaded=0;this._filename=null;const e=t.source;this._withCredentials=e.withCredentials||!1;this._contentLength=e.length;this._headersCapability=(0,n.createPromiseCapability)();this._disableRange=e.disableRange||!1;this._rangeChunkSize=e.rangeChunkSize;this._rangeChunkSize||this._disableRange||(this._disableRange=!0);this._abortController=new AbortController;this._isStreamingSupported=!e.disableStream;this._isRangeSupported=!e.disableRange;this._headers=createHeaders(this._stream.httpHeaders);const s=e.url;fetch(s,createFetchOptions(this._headers,this._withCredentials,this._abortController)).then((t=>{if(!(0,i.validateResponseStatus)(t.status))throw(0,i.createResponseStatusError)(t.status,s);this._reader=t.body.getReader();this._headersCapability.resolve();const getResponseHeader=e=>t.headers.get(e),{allowRangeRequests:e,suggestedLength:r}=(0,i.validateRangeRequestCapabilities)({getResponseHeader:getResponseHeader,isHttp:this._stream.isHttp,rangeChunkSize:this._rangeChunkSize,disableRange:this._disableRange});this._isRangeSupported=e;this._contentLength=r||this._contentLength;this._filename=(0,i.extractFilenameFromHeader)(getResponseHeader);!this._isStreamingSupported&&this._isRangeSupported&&this.cancel(new n.AbortException("Streaming is disabled."))})).catch(this._headersCapability.reject);this.onProgress=null}get headersReady(){return this._headersCapability.promise}get filename(){return this._filename}get contentLength(){return this._contentLength}get isRangeSupported(){return this._isRangeSupported}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._headersCapability.promise;const{value:t,done:e}=await this._reader.read();if(e)return{value:t,done:e};this._loaded+=t.byteLength;this.onProgress&&this.onProgress({loaded:this._loaded,total:this._contentLength});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._reader&&this._reader.cancel(t);this._abortController.abort()}}class PDFFetchStreamRangeReader{constructor(t,e,s){this._stream=t;this._reader=null;this._loaded=0;const r=t.source;this._withCredentials=r.withCredentials||!1;this._readCapability=(0,n.createPromiseCapability)();this._isStreamingSupported=!r.disableStream;this._abortController=new AbortController;this._headers=createHeaders(this._stream.httpHeaders);this._headers.append("Range",`bytes=${e}-${s-1}`);const a=r.url;fetch(a,createFetchOptions(this._headers,this._withCredentials,this._abortController)).then((t=>{if(!(0,i.validateResponseStatus)(t.status))throw(0,i.createResponseStatusError)(t.status,a);this._readCapability.resolve();this._reader=t.body.getReader()})).catch(this._readCapability.reject);this.onProgress=null}get isStreamingSupported(){return this._isStreamingSupported}async read(){await this._readCapability.promise;const{value:t,done:e}=await this._reader.read();if(e)return{value:t,done:e};this._loaded+=t.byteLength;this.onProgress&&this.onProgress({loaded:this._loaded});return{value:new Uint8Array(t).buffer,done:!1}}cancel(t){this._reader&&this._reader.cancel(t);this._abortController.abort()}}}],__webpack_module_cache__={};function __w_pdfjs_require__(t){var e=__webpack_module_cache__[t];if(void 0!==e)return e.exports;var s=__webpack_module_cache__[t]={exports:{}};__webpack_modules__[t](s,s.exports,__w_pdfjs_require__);return s.exports}var __webpack_exports__={};(()=>{var t=__webpack_exports__;Object.defineProperty(t,"__esModule",{value:!0});Object.defineProperty(t,"AnnotationEditorLayer",{enumerable:!0,get:function(){return i.AnnotationEditorLayer}});Object.defineProperty(t,"AnnotationEditorParamsType",{enumerable:!0,get:function(){return e.AnnotationEditorParamsType}});Object.defineProperty(t,"AnnotationEditorType",{enumerable:!0,get:function(){return e.AnnotationEditorType}});Object.defineProperty(t,"AnnotationEditorUIManager",{enumerable:!0,get:function(){return r.AnnotationEditorUIManager}});Object.defineProperty(t,"AnnotationLayer",{enumerable:!0,get:function(){return a.AnnotationLayer}});Object.defineProperty(t,"AnnotationMode",{enumerable:!0,get:function(){return e.AnnotationMode}});Object.defineProperty(t,"CMapCompressionType",{enumerable:!0,get:function(){return e.CMapCompressionType}});Object.defineProperty(t,"GlobalWorkerOptions",{enumerable:!0,get:function(){return o.GlobalWorkerOptions}});Object.defineProperty(t,"InvalidPDFException",{enumerable:!0,get:function(){return e.InvalidPDFException}});Object.defineProperty(t,"LoopbackPort",{enumerable:!0,get:function(){return s.LoopbackPort}});Object.defineProperty(t,"MissingPDFException",{enumerable:!0,get:function(){return e.MissingPDFException}});Object.defineProperty(t,"OPS",{enumerable:!0,get:function(){return e.OPS}});Object.defineProperty(t,"PDFDataRangeTransport",{enumerable:!0,get:function(){return s.PDFDataRangeTransport}});Object.defineProperty(t,"PDFDateString",{enumerable:!0,get:function(){return n.PDFDateString}});Object.defineProperty(t,"PDFWorker",{enumerable:!0,get:function(){return s.PDFWorker}});Object.defineProperty(t,"PasswordResponses",{enumerable:!0,get:function(){return e.PasswordResponses}});Object.defineProperty(t,"PermissionFlag",{enumerable:!0,get:function(){return e.PermissionFlag}});Object.defineProperty(t,"PixelsPerInch",{enumerable:!0,get:function(){return n.PixelsPerInch}});Object.defineProperty(t,"RenderingCancelledException",{enumerable:!0,get:function(){return n.RenderingCancelledException}});Object.defineProperty(t,"SVGGraphics",{enumerable:!0,get:function(){return h.SVGGraphics}});Object.defineProperty(t,"UNSUPPORTED_FEATURES",{enumerable:!0,get:function(){return e.UNSUPPORTED_FEATURES}});Object.defineProperty(t,"UnexpectedResponseException",{enumerable:!0,get:function(){return e.UnexpectedResponseException}});Object.defineProperty(t,"Util",{enumerable:!0,get:function(){return e.Util}});Object.defineProperty(t,"VerbosityLevel",{enumerable:!0,get:function(){return e.VerbosityLevel}});Object.defineProperty(t,"XfaLayer",{enumerable:!0,get:function(){return d.XfaLayer}});Object.defineProperty(t,"build",{enumerable:!0,get:function(){return s.build}});Object.defineProperty(t,"createPromiseCapability",{enumerable:!0,get:function(){return e.createPromiseCapability}});Object.defineProperty(t,"createValidAbsoluteUrl",{enumerable:!0,get:function(){return e.createValidAbsoluteUrl}});Object.defineProperty(t,"getDocument",{enumerable:!0,get:function(){return s.getDocument}});Object.defineProperty(t,"getFilenameFromUrl",{enumerable:!0,get:function(){return n.getFilenameFromUrl}});Object.defineProperty(t,"getPdfFilenameFromUrl",{enumerable:!0,get:function(){return n.getPdfFilenameFromUrl}});Object.defineProperty(t,"getXfaPageViewport",{enumerable:!0,get:function(){return n.getXfaPageViewport}});Object.defineProperty(t,"isPdfFile",{enumerable:!0,get:function(){return n.isPdfFile}});Object.defineProperty(t,"loadScript",{enumerable:!0,get:function(){return n.loadScript}});Object.defineProperty(t,"renderTextLayer",{enumerable:!0,get:function(){return c.renderTextLayer}});Object.defineProperty(t,"shadow",{enumerable:!0,get:function(){return e.shadow}});Object.defineProperty(t,"version",{enumerable:!0,get:function(){return s.version}});var e=__w_pdfjs_require__(1),s=__w_pdfjs_require__(4),n=__w_pdfjs_require__(8),i=__w_pdfjs_require__(22),r=__w_pdfjs_require__(7),a=__w_pdfjs_require__(27),o=__w_pdfjs_require__(15),l=__w_pdfjs_require__(3),c=__w_pdfjs_require__(30),h=__w_pdfjs_require__(31),d=__w_pdfjs_require__(29);if(l.isNodeJS){const{PDFNodeStream:t}=__w_pdfjs_require__(32);(0,s.setPDFNetworkStreamFactory)((e=>new t(e)))}else{const{PDFNetworkStream:t}=__w_pdfjs_require__(35),{PDFFetchStream:e}=__w_pdfjs_require__(36);(0,s.setPDFNetworkStreamFactory)((s=>(0,n.isValidFetchUrl)(s.url)?new e(s):new t(s)))}})();return __webpack_exports__})()));