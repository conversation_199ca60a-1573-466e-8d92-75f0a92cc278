<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-个人中心</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<!-- 导航增强脚本 -->
		<script src="./js/nav-enhancement.js" type="text/javascript" charset="utf-8"></script>
		<style>
			/* 新的上传区域样式 */
			.upload-container {
				margin-top: 10px;
			}
			
			.upload-area {
				border: 2px dashed #d9d9d9;
				border-radius: 8px;
				padding: 40px 20px;
				text-align: center;
				background-color: #fafafa;
				cursor: pointer;
				transition: all 0.3s ease;
				margin-bottom: 20px;
			}
			
			.upload-area:hover {
				border-color: #1890ff;
				background-color: #f0f8ff;
			}
			
			.upload-area.dragover {
				border-color: #1890ff;
				background-color: #e6f7ff;
			}
			
			.upload-icon {
				font-size: 48px;
				margin-bottom: 15px;
				opacity: 0.6;
			}
			
			.upload-text p {
				margin: 5px 0;
				color: #666;
			}
			
			.upload-desc {
				font-size: 14px;
				color: #999;
			}
			
			.upload-size {
				font-size: 12px;
				color: #999;
			}
			
			.file-list {
				margin-top: 15px;
			}
			
			.file-item {
				display: flex;
				align-items: center;
				padding: 12px;
				border: 1px solid #e8e8e8;
				border-radius: 6px;
				margin-bottom: 8px;
				background-color: #fff;
			}
			
			.file-icon {
				font-size: 24px;
				margin-right: 12px;
			}
			
			.file-info {
				flex: 1;
			}
			
			.file-name {
				font-weight: 500;
				color: #333;
				margin-bottom: 4px;
			}
			
			.file-size {
				font-size: 12px;
				color: #999;
			}
			
			.file-actions {
				display: flex;
				align-items: center;
				gap: 10px;
			}
			
			.progress-bar {
				width: 100px;
				height: 6px;
				background-color: #f0f0f0;
				border-radius: 3px;
				overflow: hidden;
			}
			
			.progress-fill {
				height: 100%;
				background-color: #1890ff;
				transition: width 0.3s ease;
			}
			
			.remove-btn {
				background: none;
				border: none;
				color: #ff4d4f;
				cursor: pointer;
				font-size: 16px;
				padding: 4px;
				border-radius: 4px;
			}
			
			.remove-btn:hover {
				background-color: #fff2f0;
			}
			
			.cover-upload {
				margin-top: 20px;
				padding: 20px;
				border: 2px solid #e8e8e8;
				border-radius: 8px;
				background-color: #f9f9f9;
			}
			
			.cover-title {
				margin-bottom: 15px;
				font-weight: 600;
				color: #333;
				font-size: 14px;
			}
			
			.cover-preview {
				width: 140px;
				height: 100px;
				border: 2px dashed #d9d9d9;
				border-radius: 6px;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				background-color: #fff;
				overflow: hidden;
				position: relative;
				transition: all 0.3s ease;
			}
			
			.cover-preview:hover {
				border-color: #1890ff;
				background-color: #f0f8ff;
			}
			
			.cover-placeholder {
				color: #999;
				font-size: 13px;
				text-align: center;
				font-weight: 500;
			}
			
			.cover-image {
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
			
			.cover-remove {
				position: absolute;
				top: -8px;
				right: -8px;
				width: 20px;
				height: 20px;
				background-color: #ff4d4f;
				color: white;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				font-size: 14px;
				font-weight: bold;
				box-shadow: 0 2px 4px rgba(0,0,0,0.2);
			}
			
			/* 重新设计的表单样式 */
			.form-container {
				background: #fff;
				border-radius: 12px;
				padding: 30px;
				box-shadow: 0 2px 12px rgba(0,0,0,0.08);
				margin-bottom: 20px;
			}
			
			.form-title {
				font-size: 20px;
				font-weight: 600;
				color: #333;
				margin-bottom: 30px;
				padding-bottom: 15px;
				border-bottom: 2px solid #f0f0f0;
			}
			
			.form-grid {
				display: grid;
				grid-template-columns: 1fr 1fr;
				gap: 25px 30px;
				margin-bottom: 25px;
			}
			
			.form-item {
				display: flex;
				align-items: center;
				gap: 15px;
			}
			
			.form-item.full-width {
				grid-column: 1 / -1;
			}
			
			.form-label {
				font-weight: 500;
				color: #333;
				white-space: nowrap;
				min-width: 80px;
				font-size: 14px;
			}
			
			.form-input {
				flex: 1;
				padding: 10px 15px;
				border: 1px solid #d9d9d9;
				border-radius: 6px;
				font-size: 14px;
				transition: all 0.3s ease;
				background-color: #fff;
			}
			
			.form-input:focus {
				border-color: #1890ff;
				box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
				outline: none;
			}
			
			.form-select {
				flex: 1;
				padding: 10px 15px;
				border: 1px solid #d9d9d9;
				border-radius: 6px;
				font-size: 14px;
				background-color: #fff;
				cursor: pointer;
				transition: all 0.3s ease;
			}
			
			.form-select:focus {
				border-color: #1890ff;
				box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
				outline: none;
			}
			
			.form-textarea {
				flex: 1;
				padding: 12px 15px;
				border: 1px solid #d9d9d9;
				border-radius: 6px;
				font-size: 14px;
				min-height: 80px;
				resize: vertical;
				font-family: inherit;
				transition: all 0.3s ease;
			}
			
			.form-textarea:focus {
				border-color: #1890ff;
				box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.1);
				outline: none;
			}
			
			.upload-section {
				background: #f8f9fa;
				border-radius: 12px;
				padding: 25px;
				margin-bottom: 25px;
			}
			
			.upload-section-title {
				font-size: 16px;
				font-weight: 600;
				color: #333;
				margin-bottom: 20px;
				display: flex;
				align-items: center;
				gap: 8px;
			}
			
			.upload-grid {
				display: grid;
				grid-template-columns: 1fr 1fr;
				gap: 25px;
			}
			
			.upload-item {
				background: #fff;
				border-radius: 8px;
				padding: 20px;
				border: 1px solid #e8e8e8;
			}
			
			.upload-item-title {
				font-weight: 600;
				color: #333;
				margin-bottom: 15px;
				font-size: 14px;
			}
			
			.history-suggestions {
				margin-top: 8px;
				display: flex;
				flex-wrap: wrap;
				gap: 6px;
			}
			
			.suggestion-tag {
				background: #f0f8ff;
				color: #1890ff;
				padding: 4px 8px;
				border-radius: 12px;
				font-size: 12px;
				cursor: pointer;
				transition: all 0.2s ease;
				border: 1px solid #d4edda;
			}
			
			.suggestion-tag:hover {
				background: #1890ff;
				color: #fff;
			}
			
			.submit-section {
				text-align: center;
				padding-top: 20px;
				border-top: 1px solid #f0f0f0;
			}
			
			.submit-btn {
				background: linear-gradient(135deg, #1890ff, #40a9ff);
				color: #fff;
				border: none;
				padding: 12px 40px;
				border-radius: 8px;
				font-size: 16px;
				font-weight: 600;
				cursor: pointer;
				transition: all 0.3s ease;
				box-shadow: 0 4px 12px rgba(24, 144, 255, 0.3);
			}
			
			.submit-btn:hover {
				transform: translateY(-2px);
				box-shadow: 0 6px 20px rgba(24, 144, 255, 0.4);
			}
			
			.submit-btn:active {
				transform: translateY(0);
			}
			
			/* 上传进度悬浮窗样式 */
			.upload-progress-modal {
				position: fixed;
				top: 20px;
				right: 20px;
				width: 380px;
				background: white;
				border-radius: 12px;
				box-shadow: 0 8px 32px rgba(0,0,0,0.15);
				z-index: 1000;
				display: none;
				backdrop-filter: blur(10px);
			}
			
			.progress-header {
				padding: 20px 25px 15px;
				border-bottom: 1px solid #e8e8e8;
				display: flex;
				justify-content: space-between;
				align-items: center;
				background: linear-gradient(135deg, #1890ff, #40a9ff);
				color: white;
				border-radius: 12px 12px 0 0;
			}
			
			.progress-title {
				font-weight: 600;
				font-size: 16px;
			}
			
			.progress-actions {
				display: flex;
				align-items: center;
				gap: 10px;
			}
			
			.progress-clear-btn {
				background: rgba(255,255,255,0.2);
				border: none;
				padding: 6px 12px;
				border-radius: 4px;
				cursor: pointer;
				color: white;
				font-size: 12px;
				transition: all 0.2s ease;
			}
			
			.progress-clear-btn:hover {
				background: rgba(255,255,255,0.3);
			}
			
			.progress-close {
				background: rgba(255,255,255,0.2);
				border: none;
				width: 28px;
				height: 28px;
				border-radius: 50%;
				cursor: pointer;
				color: white;
				font-size: 14px;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.2s ease;
			}
			
			.progress-close:hover {
				background: rgba(255,255,255,0.3);
			}
			
			.progress-content {
				padding: 20px 25px;
				max-height: 400px;
				overflow-y: auto;
			}
			
			.progress-item {
				margin-bottom: 20px;
				padding: 15px;
				background: #f8f9fa;
				border-radius: 8px;
				border-left: 4px solid #1890ff;
			}
			
			.progress-item:last-child {
				margin-bottom: 0;
			}
			
			.progress-item.uploading {
				border-left-color: #52c41a;
				background: #f6ffed;
			}
			
			.progress-item.waiting {
				border-left-color: #faad14;
				background: #fffbe6;
			}
			
			.progress-item.completed {
				border-left-color: #52c41a;
				background: #f6ffed;
			}
			
			.progress-item.error {
				border-left-color: #ff4d4f;
				background: #fff2f0;
			}
			
			.progress-file-name {
				font-size: 14px;
				color: #333;
				margin-bottom: 10px;
				font-weight: 500;
				display: flex;
				align-items: center;
				gap: 8px;
			}
			
			.progress-status {
				font-size: 12px;
				padding: 2px 8px;
				border-radius: 12px;
				font-weight: 500;
			}
			
			.progress-status.uploading {
				background: #e6f7ff;
				color: #1890ff;
			}
			
			.progress-status.waiting {
				background: #fff7e6;
				color: #fa8c16;
			}
			
			.progress-status.completed {
				background: #f6ffed;
				color: #52c41a;
			}
			
			.progress-status.error {
				background: #fff2f0;
				color: #ff4d4f;
			}
			
			.progress-bar-container {
				display: flex;
				align-items: center;
				gap: 12px;
			}
			
			.progress-bar-full {
				flex: 1;
				height: 8px;
				background-color: #f0f0f0;
				border-radius: 4px;
				overflow: hidden;
			}
			
			.progress-percentage {
				font-size: 12px;
				color: #666;
				min-width: 40px;
				font-weight: 500;
			}
			
			/* 后台队列通知 */
			.queue-notification {
				position: fixed;
				bottom: 20px;
				right: 20px;
				background: linear-gradient(135deg, #52c41a, #73d13d);
				color: white;
				padding: 15px 20px;
				border-radius: 8px;
				box-shadow: 0 4px 20px rgba(82, 196, 26, 0.3);
				z-index: 999;
				display: none;
				cursor: pointer;
				transition: all 0.3s ease;
			}
			
			.queue-notification:hover {
				transform: translateY(-2px);
				box-shadow: 0 6px 24px rgba(82, 196, 26, 0.4);
			}
			
			.notification-title {
				font-weight: 600;
				margin-bottom: 5px;
			}
			
			.notification-desc {
				font-size: 12px;
				opacity: 0.9;
			}
			
			/* 悬浮上传按钮 */
			.upload-float-btn {
				position: fixed;
				bottom: 30px;
				right: 30px;
				width: 60px;
				height: 60px;
				background: linear-gradient(135deg, #1890ff, #40a9ff);
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				box-shadow: 0 4px 20px rgba(24, 144, 255, 0.3);
				z-index: 998;
				transition: all 0.3s ease;
				color: white;
				font-size: 24px;
			}
			
			.upload-float-btn:hover {
				transform: translateY(-3px);
				box-shadow: 0 8px 30px rgba(24, 144, 255, 0.4);
			}
			
			.upload-float-btn.has-tasks {
				animation: pulse 2s infinite;
			}
			
			@keyframes pulse {
				0% { box-shadow: 0 4px 20px rgba(24, 144, 255, 0.3); }
				50% { box-shadow: 0 6px 25px rgba(24, 144, 255, 0.6); }
				100% { box-shadow: 0 4px 20px rgba(24, 144, 255, 0.3); }
			}
			
			.task-count-badge {
				position: absolute;
				top: -8px;
				right: -8px;
				background: #ff4d4f;
				color: white;
				border-radius: 50%;
				width: 24px;
				height: 24px;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 12px;
				font-weight: bold;
			}
			
			/* 完成通知 */
			.completion-toast {
				position: fixed;
				top: 20px;
				right: 20px;
				background: linear-gradient(135deg, #52c41a, #73d13d);
				color: white;
				padding: 15px 20px;
				border-radius: 8px;
				box-shadow: 0 4px 20px rgba(82, 196, 26, 0.3);
				z-index: 1001;
				display: none;
				animation: slideInRight 0.3s ease;
			}
			
			@keyframes slideInRight {
				from { transform: translateX(100%); opacity: 0; }
				to { transform: translateX(0); opacity: 1; }
			}
			
			.toast-content {
				display: flex;
				align-items: center;
				gap: 12px;
			}
			
			.toast-icon {
				font-size: 20px;
			}
			
			.toast-text {
				font-weight: 500;
			}
			
			/* 响应式设计 */
			@media (max-width: 768px) {
				.form-grid {
					grid-template-columns: 1fr;
					gap: 20px;
				}
				
				.upload-grid {
					grid-template-columns: 1fr;
					gap: 20px;
				}
				
				.upload-progress-modal {
					width: calc(100vw - 40px);
					right: 20px;
				}
			}
			
			/* 历史记录建议样式增强 */
			.history-suggestions:empty {
				display: none;
			}
			
			.history-suggestions {
				margin-top: 8px;
				animation: fadeIn 0.3s ease;
			}
			
			@keyframes fadeIn {
				from { opacity: 0; transform: translateY(-5px); }
				to { opacity: 1; transform: translateY(0); }
			}
			
			/* 加载状态样式 */
			.loading-overlay {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(255,255,255,0.8);
				display: flex;
				align-items: center;
				justify-content: center;
				z-index: 9999;
				backdrop-filter: blur(4px);
			}
			
			.loading-content {
				text-align: center;
				background: white;
				padding: 40px;
				border-radius: 12px;
				box-shadow: 0 8px 32px rgba(0,0,0,0.1);
			}
			
			.loading-spinner {
				width: 40px;
				height: 40px;
				border: 4px solid #f0f0f0;
				border-top: 4px solid #1890ff;
				border-radius: 50%;
				animation: spin 1s linear infinite;
				margin: 0 auto 20px;
			}
			
			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}
			
			/* 导航菜单容器 */
			.leftitembox {
				background: transparent;
				padding: 20px 0;
				position: relative;
				z-index: 2;
				min-height: 450px; /* 确保导航栏最小高度一致 */
				box-sizing: border-box;
			}
			
			.leftitembox::before {
				content: '';
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 85%;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.15), transparent);
			}
			
			/* 导航菜单项 - 现代化设计与图标，移除slideIn动画 */
			.leftitem {
				height: auto !important;
				padding: 16px 24px !important;
				display: flex !important;
				justify-content: flex-start !important;
				align-items: center !important;
				font-size: 15px !important;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif !important;
				font-weight: 500 !important;
				color: #4a5568 !important;
				border-bottom: none !important;
				text-decoration: none !important;
				transition: color 0.2s ease, background-color 0.2s ease !important;
				position: relative !important;
				margin: 4px 16px !important;
				border-radius: 12px !important;
				background: transparent !important;
				overflow: hidden !important;
				min-height: 48px !important;
				box-sizing: border-box !important;
				/* 移除slideIn动画，导航项立即显示 */
				animation: none !important;
				opacity: 1 !important;
				transform: translateX(0) !important;
				animation-delay: 0s !important;
			}
			
			/* 为不同菜单项添加对应的SVG图标 */
			.leftitem::before {
				content: '';
				width: 20px;
				height: 20px;
				margin-right: 12px;
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				opacity: 0.7;
				transition: opacity 0.2s ease;
				flex-shrink: 0;
			}
			
			/* 个人信息图标 */
			.leftitem[href*="userinfo"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
			}
			
			/* 学生成绩统计图标 */
			.leftitem[href*="studentachievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z'/%3E%3C/svg%3E");
			}
			
			/* 学习记录统计图标 */
			.leftitem[href*="studentlearning"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z'/%3E%3C/svg%3E");
			}
			
			/* 考试管理图标 */
			.leftitem[href*="examination"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E");
			}
			
			/* 学习任务管理图标 */
			.leftitem[href*="learning"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
			}
			
			/* 成果展示图标 */
			.leftitem[href*="achievement"]:not(.activeleftitem)::before,
			.leftitem[href*="uploadresources"]:not(.activeleftitem)::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E");
			}
			
			/* 发布心声图标 */
			.leftitem[href*="voice"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z'/%3E%3C/svg%3E");
			}
			
			/* 菜单项悬浮态 */
			.leftitem:hover {
				background: linear-gradient(135deg, rgba(220, 53, 69, 0.06), rgba(231, 76, 60, 0.08)) !important;
				color: #dc3545 !important;
			}
			
			.leftitem:hover::before {
				opacity: 1;
			}
			
			/* 激活状态的菜单项 - 统一标准双色渐变背景 */
			.leftitem.activeleftitem,
			a.leftitem.activeleftitem,
			.leftitembox .leftitem.activeleftitem,
			#teambox .leftitem.activeleftitem {
				font-weight: 700 !important;
				color: white !important;
				background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
				background-size: 200% 200% !important;
				animation: activeGradient 3s ease infinite !important;
				box-shadow: 0 4px 16px rgba(220, 53, 69, 0.25) !important;
				border-radius: 12px !important;
				opacity: 1 !important;
				transform: translateX(0) !important;
			}
			
			@keyframes activeGradient {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.leftitem.activeleftitem::before,
			a.leftitem.activeleftitem::before,
			.leftitembox .leftitem.activeleftitem::before,
			#teambox .leftitem.activeleftitem::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E") !important;
				opacity: 1 !important;
				filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
			}
			
			/* 移除所有导航项的动画延迟 */
			.leftitem:nth-child(1),
			.leftitem:nth-child(2), 
			.leftitem:nth-child(3), 
			.leftitem:nth-child(4), 
			.leftitem:nth-child(5), 
			.leftitem:nth-child(6), 
			.leftitem:nth-child(7) { 
				animation: none !important;
				animation-delay: 0s !important;
				opacity: 1 !important;
				transform: translateX(0) !important;
			}
		</style>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a href="studentachievement.html" class="leftitem">学生成绩统计</a>
						<a href="studentlearning.html" class="leftitem">学习记录统计</a>
						<a href="userexamination1.html" class="leftitem">考试管理</a>
						<a href="learningtasks.html" class="leftitem">学习任务管理</a>
						<a class="leftitem activeleftitem">成果展示</a>
						<a href="releaseyourvoice2.html" class="leftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="userinfotopdiv">
						<a href="achievements.html">成果列表</a>
						<a class="acccccg">发布资源</a>
					</div>
					
					<div id="loading" style="display: none;" class="updatebox">
						<div class="info">
							<img id="loadimg" src="img/loading.png"/>
							正在添加资源！请不要刷新网页！
							<div style="margin-top: 10px;">
								<button onclick="showUploadProgress()" style="background: #1890ff; color: white; border: none; padding: 8px 16px; border-radius: 4px; cursor: pointer;">
									📤 查看上传进度
								</button>
							</div>
						</div>
					</div>
					<div id="success" style="display: none;" class="updatebox">
						<div class="successbox">
							<div class="info"><img src="img/upsuccess.png"/>发布成功!</div>
							<div class="successbtn">
								<div onclick="showinfo()">点击查看</div>
								<div onclick="jixufabu()">继续发布</div>
							</div>
						</div>
					</div>
					<div id="update" style="display: block;" class="updatebox">
						<div class="form-container">
							<div class="form-title">📝 发布资源</div>
							
							<div class="form-grid">
								<div class="form-item">
									<label class="form-label">标题:</label>
									<input id="bt" class="form-input" placeholder="请输入标题" />
									<div class="history-suggestions" id="titleSuggestions"></div>
							</div>
								
								<div class="form-item">
									<label class="form-label">作者:</label>
									<input id="zz" class="form-input" placeholder="请输入作者" />
									<div class="history-suggestions" id="authorSuggestions"></div>
						</div>
								
								<div class="form-item">
									<label class="form-label">组织:</label>
									<input id="zuzhi" class="form-input" placeholder="请输入学校或出版社" />
									<div class="history-suggestions" id="orgSuggestions"></div>
							</div>
								
								<div class="form-item">
									<label class="form-label">知识点:</label>
									<input id="zhishidian" class="form-input" placeholder="请输入知识点(多个知识点用逗号隔开)" />
									<div class="history-suggestions" id="knowledgeSuggestions"></div>
						</div>
								
								<div class="form-item">
									<label class="form-label">属性:</label>
									<select id="shuxing" class="form-select">
										<option value="0">请选择属性</option>
							</select>
						</div>
								
								<div class="form-item">
									<label class="form-label">学科:</label>
									<select id="xueke" class="form-select" onchange="xuekechange()">
										<option value="0">请选择学科</option>
							</select>
						</div>
								
								<div class="form-item full-width">
									<label class="form-label">章:</label>
									<select id="zhang" class="form-select" onchange="zhangchange()">
								<option value="0">请选择章</option>
							</select>
						</div>
								
								<div class="form-item full-width">
									<label class="form-label">课程简介:</label>
									<textarea id="jj" class="form-textarea" placeholder="请输入简介"></textarea>
							</div>
						</div>
						
							<!-- 隐藏的资源类型选择 -->
							<input type="hidden" id="types" value="0" />
							<input type="hidden" id="jie" value="0" />
							<input type="hidden" id="xiaojie" value="0" />
							</div>

						<div class="upload-section">
							<div class="upload-section-title">
								📤 资源上传
									</div>
							<div class="upload-grid">
								<div class="upload-item">
									<div class="upload-item-title">📷 封面图片 (可选)</div>
									<div class="cover-preview" id="coverPreview">
										<div class="cover-placeholder">点击添加封面<br><small style="color: #999; font-size: 11px;">建议: 不超过1M, 比例16:9</small></div>
								</div>
									<input type="file" id="coverFile" accept=".jpg,.png,.jpeg" style="display: none;" />
								</div>
								
								<div class="upload-item">
									<div class="upload-item-title">📁 资源文件</div>
									<div class="upload-area" id="uploadArea">
										<div class="upload-icon">📁</div>
										<div class="upload-text">
											<p>点击或拖拽文件到此处</p>
											<p class="upload-desc">支持: DOCX、DOC、PPTX、PPT、MP4、MP3</p>
											<p class="upload-size">大小不超过100MB，每次仅限一个文件</p>
									</div>
								</div>
									<input type="file" id="fileUpload" 
										   accept=".docx,.doc,.pptx,.ppt,.mp4,.mp3" 
										   style="display: none;" />
							</div>
						</div>
							
							<div class="file-list" id="fileList">
								<!-- 上传的文件列表将显示在这里 -->
							</div>
						</div>
						
						<div class="submit-section">
							<button class="submit-btn" onclick="submitNew()">
								🚀 确认发布
							</button>
							</div>
									</div>
								</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		
		<!-- 上传进度悬浮窗 -->
		<div class="upload-progress-modal" id="uploadProgressModal">
			<div class="progress-header">
				<div class="progress-title">📤 上传进度</div>
				<div class="progress-actions">
					<button class="progress-clear-btn" onclick="clearUploadQueue()">🗑 清空</button>
					<button class="progress-close" onclick="hideUploadProgress()">×</button>
				</div>
			</div>
			<div class="progress-content" id="progressContent">
				<!-- 上传进度项将动态添加到这里 -->
			</div>
		</div>
		
		<!-- 悬浮上传按钮 -->
		<div class="upload-float-btn" id="uploadFloatBtn" onclick="toggleUploadProgress()" style="display: none;">
			📤
			<div class="task-count-badge" id="taskCountBadge" style="display: none;">0</div>
		</div>
		
		<!-- 完成通知 -->
		<div class="completion-toast" id="completionToast">
			<div class="toast-content">
				<div class="toast-icon">✅</div>
				<div class="toast-text">资源上传完成！</div>
			</div>
		</div>
		
		<!-- 后台队列通知 -->
		<div class="queue-notification" id="queueNotification" onclick="showUploadProgress()">
			<div class="notification-title">📤 后台上传中</div>
			<div class="notification-desc">点击查看详细进度</div>
		</div>
		
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let shuxinglist = null
			let leixinglist = null
			let xuekelist = null
			let upclassid = null
			let type = 'teacher' //teacher/student
			var mySwiper = new Swiper('.museumboxitembottoml .swiper', {
				autoplay: false,
				loop: true,
				pagination: {
					el: '.museumboxitembottoml .swiper-pagination',
					clickable: true
				}
			})
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				let userinfo = sessionStorage.getItem("userinfo")
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getclassid()
				getxueke()
				getshuxin()
				getfenlei()
				getfooterlink()
				
				// ========== 文件上传功能初始化 ==========
				// 点击上传区域选择文件
				$("#uploadArea").on('click', function(e) {
					console.log('Upload area clicked'); // 调试用
					$("#fileUpload")[0].click();
				});
				
				// 文件选择事件
				$("#fileUpload").on('change', function() {
					console.log('File selected:', this.files); // 调试用
					const file = this.files[0]; // 只取第一个文件
					if (file) {
						// 清空之前的文件
						uploadedFiles = [];
						addFileToList(file);
					}
					// 清空input，允许重复选择同一文件
					this.value = '';
				});
				
				// 拖拽上传
				$("#uploadArea")
					.on('dragover', function(e) {
						e.preventDefault();
						e.stopPropagation();
						$(this).addClass('dragover');
					})
					.on('dragleave', function(e) {
						e.preventDefault();
						e.stopPropagation();
						$(this).removeClass('dragover');
					})
					.on('drop', function(e) {
						e.preventDefault();
						e.stopPropagation();
						$(this).removeClass('dragover');
						
						const file = e.originalEvent.dataTransfer.files[0]; // 只取第一个文件
						if (file) {
							// 清空之前的文件
							uploadedFiles = [];
							addFileToList(file);
						}
					});
				
				// 封面上传
				$("#coverPreview").on('click', function() {
					console.log('Cover preview clicked'); // 调试用
					$("#coverFile")[0].click();
				});
				
				$("#coverFile").on('change', function() {
					const file = this.files[0];
					console.log('Cover file selected:', file); // 调试用
					if (file) {
						// 验证是否为图片
						if (!file.type.startsWith('image/')) {
							cocoMessage.warning(2000, '请选择图片文件！');
							return;
						}
						
						// 验证文件大小 (1MB = 1024 * 1024 bytes)
						if (file.size > 1024 * 1024) {
							cocoMessage.warning(3000, '封面图片建议不要超过1MB，当前文件大小: ' + formatFileSize(file.size));
						}
						
						coverFile = file;
						const imageUrl = URL.createObjectURL(file);
						
						// 创建图片对象检查比例
						const img = new Image();
						img.onload = function() {
							const ratio = this.width / this.height;
							const idealRatio = 16 / 9;
							const tolerance = 0.1; // 允许10%的误差
							
							if (Math.abs(ratio - idealRatio) > tolerance) {
								cocoMessage.info(3000, `当前图片比例为 ${this.width}:${this.height}，建议使用16:9比例的图片以获得最佳显示效果`);
							}
							
							$("#coverPreview").html(`
								<img src="${imageUrl}" class="cover-image" />
								<div class="cover-remove" onclick="removeCover()" title="删除封面">×</div>
							`);
						};
						img.src = imageUrl;
					}
				});
				
				// ========== 表单记忆功能初始化 ==========
				initFormMemory();
				
				// ========== 检查后台上传队列 ==========
				checkUploadQueue();
			})
			function getfooterlink(){
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item)=>{
								html+='<a href="'+item.url+'" target="_blank">'+item.name+'</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
			function xuekechange(){//学科被选择时
				if($("#xueke").val()=='0'){
					$("#zhang").html('<option value=0>请选择章</option>')
				}else{
					xuekelist.map((item)=>{
						if(item.id==$("#xueke").val()){
							let zhtml = "<option value=0>请选择章</option>"
							item.children.map((item2)=>{
								zhtml+='<option value="'+item2.id+'">'+item2.name+'</option>'
							})
							$("#zhang").html(zhtml)
						}
					})
				}
			}
			function zhangchange(){//章被选择时
				if($("#zhang").val()=='0'){
					$("#jie").html('<option value=0>请选择节</option>')
				}else{
					xuekelist.map((item)=>{
						if(item.id==$("#xueke").val()){
							item.children.map((item2)=>{
								if(item2.id == $("#zhang").val()){
									let jiehtml = "<option value=0>请选择节</option>"
									item2.children.map((item3)=>{
										jiehtml+='<option value="'+item3.id+'">'+item3.name+'</option>'
									})
									$("#jie").html(jiehtml)
								}
							})
							$("#zhang").html(zhtml)
						}
					})
				}
			}
			
			function jiechange(){//当节被选择
				if($("#jie").val()=='0'){
					$("#xiaojie").html('<option value=0>请选择小节</option>')
				}else{
					xuekelist.map((item)=>{
						if(item.id==$("#xueke").val()){
							item.children.map((item2)=>{
								if(item2.id == $("#zhang").val()){
									item2.children.map((item3)=>{
										if(item3.id == $("#jie").val()){
											let xiaojiehtml = "<option value=0>请选择小节</option>"
											item3.children.map((item4)=>{
												xiaojiehtml+='<option value="'+item4.id+'">'+item4.name+'</option>' 
											})
											$("#xiaojie").html(xiaojiehtml)
										}
									})
								}
							})
							$("#zhang").html(zhtml)
						}
					})
				}
			}
			function getfenlei(){//获取类型
				$.ajax({
					url: baseurl + "/types",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log('类型',res.data)
							leixinglist = res.data
						}
					}
				})
			}
			function getshuxin(){//获取属性
				$.ajax({
					url: baseurl + "/attributes",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log('属性',res.data)
							shuxinglist = res.data
							let html = "<option value=0>请选择属性</option>"
							shuxinglist.map((item)=>{
								html += '<option value="'+item.id+'">'+item.name+'</option>'
							})
							$("#shuxing").html(html)
						}
					}
				})
			}
			function getxueke(){//获取学科树
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log('学科',res.data)
							xuekelist = res.data
							let xuekehtml = "<option value=0>请选择学科</option>"
							xuekelist.map((item)=>{
								xuekehtml+='<option value="'+item.id+'">'+item.name+'</option>'
							})
							$("#xueke").html(xuekehtml)
						}
					}
				})
			}
			function getclassid(){
				$.ajax({
					url: baseurl + "/category/teaching",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							upclassid = res.data[0].id
						}
					}
				})
			}
			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 						.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
			let addjson = {
				title: null,//标题
				author: null,//作者
				introduction: null,//简介
				projectId: null,//学科
				sectionId: null,//章
				nodeId: null,//节
				barId: null,//小节
				attributesId: null,//属性
				categoryId: null,//分类ID
				organization: null,//组织
				type: 0,
				cmsResourcesCourseMetaList:[],
				knowledge: null
			}
			let fjdata = {
				attachId: null,
				coverId: null,
				attachType: null,
				typeId: null,
				attachName: null
			}
			let kjdata = {
				attachId: null,
				coverId: null,
				attachType: null,
				typeId: null,
				attachName: null
			}
			let stdata = {
				attachId: null,
				coverId: null,
				attachType: null,
				typeId: null,
				attachName: null
			}
			let fjfile = null
			let kjfile = null
			let stfile = null
			let imgfj = null
			let imgkj = null
			let imgst = null
			function selectfj(){ //附件选择
				let file = document.getElementById('filefj').files[0]
				fjfile = file //赋值
				// console.log(file)
				if(file){
					$("#fjlist").html('<div class="filesitem"><div>'+file.name+'</div><img onclick="fjclose()" class="close" src="img/closered.png" /></div>')
				}
			}
			function fjclose(){ //附件清空
				$("#fjlist").html('')
				let fles = document.getElementById('filefj')
				fles.outerHTML=fles.outerHTML;
				fjfile = null //清空
			}
			function selectfjfm(){//附件封面选择
				let file = document.getElementById('filefjfm').files[0]
				imgfj = file //赋值
				// console.log(file)
				if(file){
					$("#fjfmlist").html('<div class="filesitem"><img src="'+window.URL.createObjectURL(file)+'" /><img onclick="fjfmclose()" class="close" src="img/closered.png" /></div>')
				}
			}
			function fjfmclose(){//清空附件封面
				$("#fjfmlist").html('')
				let fles = document.getElementById('filefjfm')
				fles.outerHTML=fles.outerHTML;
				imgfj = null //清空
			}
			function selectkj(){//选择课件
				let file = document.getElementById('filekj').files[0]
				kjfile = file //赋值
				// console.log(file)
				if(file){
					$("#kjlist").html('<div class="filesitem"><div>'+file.name+'</div><img onclick="kjclose()" class="close" src="img/closered.png" /></div>')
				}
			}
			function kjclose(){//清空课件
				$("#kjlist").html('')
				let fles = document.getElementById('filekj')
				fles.outerHTML=fles.outerHTML;
				kjfile = null //清空
			}
			function selectkjfm(){//选择课件封面
				let file = document.getElementById('filekjfm').files[0]
				imgkj = file //赋值
				// console.log(file)
				if(file){
					$("#kjfmlist").html('<div class="filesitem"><img src="'+window.URL.createObjectURL(file)+'" /><img onclick="kjfmclose()" class="close" src="img/closered.png" /></div>')
				}
			}
			function kjfmclose(){//清空课件封面
				$("#kjfmlist").html('')
				let fles = document.getElementById('filekjfm')
				fles.outerHTML=fles.outerHTML;
				imgkj = null //清空
			}
			function selectst(){//选择试题
				let file = document.getElementById('filest').files[0]
				stfile = file //赋值
				// console.log(file)
				if(file){
					$("#stlist").html('<div class="filesitem"><div>'+file.name+'</div><img onclick="stclose()" class="close" src="img/closered.png" /></div>')
				}
			}
			function stclose(){//清空试题
				$("#stlist").html('')
				let fles = document.getElementById('filest')
				fles.outerHTML=fles.outerHTML;
				stfile = null //清空
			}
			function selectstfm(){//选择试题封面
				let file = document.getElementById('filestfm').files[0]
				imgst = file //赋值
				// console.log(file)
				if(file){
					$("#stfmlist").html('<div class="filesitem"><img src="'+window.URL.createObjectURL(file)+'" /><img onclick="stfmclose()" class="close" src="img/closered.png" /></div>')
				}
			}
			function stfmclose(){//清空试题封面
				$("#stfmlist").html('')
				let fles = document.getElementById('filestfm')
				fles.outerHTML=fles.outerHTML;
				imgst = null //清空
			}
			
			// ========== 新的文件上传系统 ==========
			let uploadedFiles = []; // 存储上传的文件
			let coverFile = null; // 封面文件
			let uploadInProgress = false; // 是否正在上传
			
			// 文件类型图标映射
			function getFileIcon(fileName) {
				const ext = fileName.toLowerCase().split('.').pop();
				const iconMap = {
					'docx': '📄', 'doc': '📄',
					'pptx': '📊', 'ppt': '📊', 
					'mp4': '🎥', 'mp3': '🎵',
					'pdf': '📋'
				};
				return iconMap[ext] || '📎';
			}
			
			// 格式化文件大小
			function formatFileSize(bytes) {
				if (bytes === 0) return '0 Bytes';
				const k = 1024;
				const sizes = ['Bytes', 'KB', 'MB', 'GB'];
				const i = Math.floor(Math.log(bytes) / Math.log(k));
				return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
			}
			
			// 验证文件
			function validateFile(file) {
				const maxSize = 100 * 1024 * 1024; // 100MB
				const allowedTypes = ['.docx', '.doc', '.pptx', '.ppt', '.mp4', '.mp3'];
				const fileName = file.name.toLowerCase();
				const fileExt = '.' + fileName.split('.').pop();
				
				if (file.size > maxSize) {
					return { valid: false, message: '文件大小不能超过100MB' };
				}
				
				if (!allowedTypes.includes(fileExt)) {
					return { valid: false, message: '不支持的文件格式' };
				}
				
				return { valid: true };
			}
			
			// 添加文件到列表
			function addFileToList(file) {
				const validation = validateFile(file);
				if (!validation.valid) {
					cocoMessage.warning(2000, validation.message);
					return false;
				}
				
				// 检查是否已存在同名文件
				if (uploadedFiles.find(f => f.name === file.name)) {
					cocoMessage.warning(2000, '文件已存在');
					return false;
				}
				
				const fileId = 'file_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
				const fileObj = {
					id: fileId,
					file: file,
					name: file.name,
					size: file.size,
					progress: 0,
					uploaded: false
				};
				
				uploadedFiles.push(fileObj);
				renderFileList();
				
				return true;
			}
			
			// 渲染文件列表
			function renderFileList() {
				let html = '';
				uploadedFiles.forEach(file => {
					html += `
						<div class="file-item" id="${file.id}">
							<div class="file-icon">${getFileIcon(file.name)}</div>
							<div class="file-info">
								<div class="file-name">${file.name}</div>
								<div class="file-size">${formatFileSize(file.size)}</div>
							</div>
							<div class="file-actions">
								${file.uploaded ? 
									'<span style="color: #52c41a;">✓ 已完成</span>' : 
									`<div class="progress-bar">
										<div class="progress-fill" style="width: ${file.progress}%"></div>
									</div>`
								}
								<button class="remove-btn" onclick="removeFile('${file.id}')">🗑</button>
							</div>
						</div>
					`;
				});
				$("#fileList").html(html);
			}
			
			// 移除文件
			function removeFile(fileId) {
				uploadedFiles = uploadedFiles.filter(f => f.id !== fileId);
				renderFileList();
			}
			
			// 上传进度窗口控制
			function showUploadProgress() {
				$("#uploadProgressModal").show();
			}
			
			function hideUploadProgress() {
				$("#uploadProgressModal").hide();
			}
			
			// 更新上传进度
			function updateUploadProgress() {
				let html = '';
				uploadedFiles.forEach(file => {
					html += `
						<div class="progress-item">
							<div class="progress-file-name">${file.name}</div>
							<div class="progress-bar-container">
								<div class="progress-bar-full">
									<div class="progress-fill" style="width: ${file.progress}%"></div>
								</div>
								<div class="progress-percentage">${file.progress}%</div>
							</div>
						</div>
					`;
				});
				$("#progressContent").html(html);
			}
			
			// 新的提交函数
			function submitNew() {
				// 验证必填字段
				if(!$("#bt").val()){
					cocoMessage.warning(1000, "请输入标题！");
					return;
				}
				if(!$("#zz").val()){
					cocoMessage.warning(1000, "请输入作者！");
					return;
				}
				if(!$("#zuzhi").val()){
					cocoMessage.warning(1000, "请输入组织！");
					return;
				}
				if(!$("#jj").val()){
					cocoMessage.warning(1000, "请输入简介！");
					return;
				}
				if($("#shuxing").val()=='0'){
					cocoMessage.warning(1000, "请选择属性！");
					return;
				}
				
				// 检查是否有文件要上传
				if(uploadedFiles.length === 0){
					cocoMessage.warning(1000, "请至少上传一个文件！");
					return;
				}
				
				// 保存表单数据到历史记录
				saveToHistory('bt', $("#bt").val());
				saveToHistory('zz', $("#zz").val());
				saveToHistory('zuzhi', $("#zuzhi").val());
				saveToHistory('zhishidian', $("#zhishidian").val());
				
				// 创建上传任务
				const uploadTask = {
					data: {
						title: $("#bt").val(),
						author: $("#zz").val(),
						organization: $("#zuzhi").val(),
						introduction: $("#jj").val(),
						attributesId: $("#shuxing").val(),
						knowledge: $("#zhishidian").val(),
						projectId: $("#xueke").val() !== '0' ? $("#xueke").val() : null,
						sectionId: $("#zhang").val() !== '0' ? $("#zhang").val() : null
					},
					files: uploadedFiles.map(f => f.file),
					coverFile: coverFile,
					name: $("#bt").val()
				};
				
				// 添加到上传队列
				addToUploadQueue(uploadTask);
				
				// 显示成功消息
				cocoMessage.success(2000, "任务已添加到上传队列！");
				
				// 重置表单内容（不刷新页面）
				resetForm();
			}
			
			// 重置表单函数
			function resetForm() {
				// 清空表单字段
				$("#bt").val('');
				$("#zz").val('');
				$("#zuzhi").val('');
				$("#jj").val('');
				$("#zhishidian").val('');
				$("#shuxing").val('0');
				$("#xueke").val('0');
				$("#zhang").val('0');
				
				// 重置上传区域
				uploadedFiles = [];
				coverFile = null;
				renderFileList();
				removeCover();
				
				// 清空建议
				$('.history-suggestions').html('');
			}
			
			// ========== 后台队列上传系统 ==========
			let uploadQueue = []; // 上传队列
			let currentUpload = null; // 当前上传任务
			let queueRunning = false; // 队列是否正在运行
			
			// 队列持久化相关常量
			const UPLOAD_QUEUE_KEY = 'upload_queue_storage';
			const QUEUE_STATUS_KEY = 'queue_running_status';
			
			// 保存队列到本地存储
			function saveQueueToStorage() {
				try {
					const queueData = {
						queue: uploadQueue,
						running: queueRunning,
						currentUpload: currentUpload,
						timestamp: Date.now()
					};
					localStorage.setItem(UPLOAD_QUEUE_KEY, JSON.stringify(queueData));
				} catch (e) {
					console.error('保存队列失败:', e);
				}
			}
			
			// 从本地存储恢复队列
			function loadQueueFromStorage() {
				try {
					const queueData = JSON.parse(localStorage.getItem(UPLOAD_QUEUE_KEY) || '{}');
					if (queueData.queue && Array.isArray(queueData.queue)) {
						uploadQueue = queueData.queue;
						queueRunning = queueData.running || false;
						currentUpload = queueData.currentUpload || null;
						
						// 恢复文件对象（文件对象无法序列化，需要重新处理）
						uploadQueue.forEach(task => {
							if (task.files && task.files.length > 0) {
								// 文件对象丢失，将状态重置为等待
								if (task.status === 'uploading') {
									task.status = 'waiting';
									task.progress = 0;
								}
							}
						});
						
						console.log('队列已恢复:', uploadQueue.length, '个任务');
						return true;
					}
				} catch (e) {
					console.error('恢复队列失败:', e);
				}
				return false;
			}
			
			// 清除队列存储
			function clearQueueStorage() {
				localStorage.removeItem(UPLOAD_QUEUE_KEY);
			}
			
			// 添加任务到队列
			function addToUploadQueue(task) {
				task.id = 'task_' + Date.now() + '_' + Math.random().toString(36).substr(2, 9);
				task.status = 'waiting'; // waiting, uploading, completed, error
				task.progress = 0;
				task.createdAt = new Date();
				
				// 保存文件信息（用于恢复时显示）
				if (task.files && task.files.length > 0) {
					task.fileInfo = task.files.map(file => ({
						name: file.name,
						size: file.size,
						type: file.type
					}));
				}
				
				if (task.coverFile) {
					task.coverFileInfo = {
						name: task.coverFile.name,
						size: task.coverFile.size,
						type: task.coverFile.type
					};
				}
				
				uploadQueue.push(task);
				updateQueueDisplay();
				updateFloatButton();
				saveQueueToStorage(); // 保存到本地存储
				
				// 如果队列没有运行，启动队列
				if (!queueRunning) {
					processUploadQueue();
				}
				
				return task.id;
			}
			
			// 处理上传队列
			function processUploadQueue() {
				if (queueRunning || uploadQueue.length === 0) {
					return;
				}
				
				queueRunning = true;
				saveQueueToStorage(); // 保存状态
				showQueueNotification();
				processNextTask();
			}
			
			// 处理下一个任务
			function processNextTask() {
				const nextTask = uploadQueue.find(task => task.status === 'waiting');
				if (!nextTask) {
					// 队列完成
					queueRunning = false;
					currentUpload = null;
					saveQueueToStorage(); // 保存状态
					hideQueueNotification();
					updateFloatButton();
					showCompletionToast();
					return;
				}
				
				currentUpload = nextTask;
				nextTask.status = 'uploading';
				updateQueueDisplay();
				updateFloatButton();
				saveQueueToStorage(); // 保存状态
				
				// 开始上传
				uploadTask(nextTask)
					.then(() => {
						nextTask.status = 'completed';
						nextTask.progress = 100;
						updateQueueDisplay();
						updateFloatButton();
						saveQueueToStorage(); // 保存状态
						
						// 延迟处理下一个任务
						setTimeout(() => {
							processNextTask();
						}, 1000);
					})
					.catch((error) => {
						nextTask.status = 'error';
						nextTask.error = error;
						updateQueueDisplay();
						updateFloatButton();
						saveQueueToStorage(); // 保存状态
						
						// 继续处理下一个任务
						setTimeout(() => {
							processNextTask();
						}, 2000);
					});
			}
			
			// 上传单个任务
			function uploadTask(task) {
				return new Promise((resolve, reject) => {
					// 检查文件对象是否存在（页面刷新后会丢失）
					if (!task.files || task.files.length === 0 || !task.files[0].name) {
						// 文件对象丢失，标记为错误
						reject('文件对象丢失，请重新选择文件');
						return;
					}
					
					// 准备数据
					const addjson = {
						categoryId: upclassid,
						title: task.data.title,
						type: 0,
						author: task.data.author,
						organization: task.data.organization,
						introduction: task.data.introduction,
						attributesId: task.data.attributesId,
						knowledge: task.data.knowledge,
						projectId: task.data.projectId,
						sectionId: task.data.sectionId,
						nodeId: null,
						barId: null,
						cmsResourcesCourseMetaList: []
					};
					
					// 依次上传文件
					uploadTaskFiles(task)
						.then(() => uploadTaskCover(task))
						.then(() => {
							// 设置附件列表
							addjson.cmsResourcesCourseMetaList = task.attachments || [];
							
							// 提交课程
								$.ajax({
								url: baseurl + "/course/add",
								type: 'POST',
								contentType: "application/json",
									headers: {
										"Authorization": sessionStorage.getItem("header")
									},
								data: JSON.stringify(addjson),
								dataType: 'json',
									success: (res) => {
									if (res.code == '200') {
										resolve();
									} else {
										reject('课程提交失败');
									}
								},
								error: () => {
									reject('课程提交失败');
								}
							});
						})
						.catch(reject);
				});
			}
			
			// 上传任务的文件
			function uploadTaskFiles(task) {
				return new Promise((resolve, reject) => {
					if (!task.files || task.files.length === 0) {
						resolve();
						return;
					}
					
					// 检查文件对象是否有效
					const validFiles = task.files.filter(file => file && file.name && file.size);
					if (validFiles.length === 0) {
						reject('文件对象无效，请重新选择文件');
						return;
					}
					
					let uploadedCount = 0;
					const totalFiles = validFiles.length;
					
					function uploadNextFile(index) {
						if (index >= totalFiles) {
							resolve();
							return;
						}
						
						const file = validFiles[index];
						const formData = new FormData();
						formData.append('files', file);
						
								$.ajax({
									url: baseurl + "/attachments/uploads",
									type: 'post',
									headers: {
										"Authorization": sessionStorage.getItem("header")
									},
									data: formData,
									contentType: false,
									processData: false,
							xhr: function() {
								const xhr = new XMLHttpRequest();
								xhr.upload.addEventListener('progress', function(e) {
									if (e.lengthComputable) {
										const fileProgress = Math.round((e.loaded / e.total) * 100);
										const overallProgress = Math.round(((uploadedCount + (fileProgress / 100)) / totalFiles) * 100);
										task.progress = overallProgress;
										updateQueueDisplay();
										saveQueueToStorage(); // 保存进度
									}
								});
								return xhr;
							},
									success: (res) => {
										if(res.code == '200'){
									// 处理文件类型
									const fileName = file.name.toLowerCase();
									let typeId = null;
									let typeName = "微课库";
									
									if (fileName.includes('.ppt') || fileName.includes('.pptx')) {
										typeName = "课件库";
									} else if (fileName.includes('.pdf')) {
										typeName = "试题库";
									}
									
									leixinglist.forEach((item) => {
										if(item.name === typeName){
											typeId = item.id;
										}
									});
									
									task.attachments = task.attachments || [];
									task.attachments.push({
										attachId: res.data[0].id,
										attachName: res.data[0].fileName,
										attachType: res.data[0].type,
										typeId: typeId,
										coverId: null
									});
									
									uploadedCount++;
									uploadNextFile(index + 1);
								} else {
									reject(`文件 "${file.name}" 上传失败`);
												}
							},
							error: () => {
								reject(`文件 "${file.name}" 上传失败`);
									}
						});
					}
					
					uploadNextFile(0);
				});
			}
			
			// 上传任务的封面
			function uploadTaskCover(task) {
				return new Promise((resolve, reject) => {
					if (!task.coverFile || !task.coverFile.name || !task.coverFile.size) {
						resolve();
						return;
					}
					
					const formData = new FormData();
					formData.append('files', task.coverFile);
					
								$.ajax({
									url: baseurl + "/attachments/uploads",
									type: 'post',
									headers: {
										"Authorization": sessionStorage.getItem("header")
									},
									data: formData,
									contentType: false,
									processData: false,
									success: (res) => {
										if(res.code == '200'){
								if (task.attachments && task.attachments.length > 0) {
									task.attachments[0].coverId = res.data[0].id;
								}
								resolve();
							} else {
								reject('封面上传失败');
										}
						},
						error: () => {
							reject('封面上传失败');
							}
					});
				});
			}
			
			// 更新队列显示
			function updateQueueDisplay() {
				if ($("#uploadProgressModal").is(':visible')) {
					updateUploadProgressDisplay();
				}
				updateFloatButton();
			}
			
			// 更新上传进度显示
			function updateUploadProgressDisplay() {
				let html = '';
				uploadQueue.forEach(task => {
					const statusClass = task.status;
					const statusText = {
						waiting: '等待中',
						uploading: '上传中',
						completed: '已完成',
						error: '失败'
					}[task.status];
					
					// 获取任务名称，优先使用title，否则使用文件名
					let taskName = task.data.title || '未知任务';
					if (task.fileInfo && task.fileInfo.length > 0) {
						taskName = task.data.title + ' (' + task.fileInfo[0].name + ')';
					}
					
					html += `
						<div class="progress-item ${statusClass}">
							<div class="progress-file-name">
								${getFileIcon(taskName)} ${taskName}
								<span class="progress-status ${statusClass}">${statusText}</span>
							</div>
							<div class="progress-bar-container">
								<div class="progress-bar-full">
									<div class="progress-fill" style="width: ${task.progress}%"></div>
								</div>
								<div class="progress-percentage">${task.progress}%</div>
							</div>
							${task.error ? `<div style="color: #ff4d4f; font-size: 12px; margin-top: 5px;">${task.error}</div>` : ''}
						</div>
					`;
				});
				$("#progressContent").html(html);
			}
			
			// 显示队列通知
			function showQueueNotification() {
				$("#queueNotification").show();
			}
			
			// 隐藏队列通知
			function hideQueueNotification() {
				$("#queueNotification").hide();
			}
			
			// 检查上传队列
			function checkUploadQueue() {
				// 页面加载时从本地存储恢复队列
				const restored = loadQueueFromStorage();
				
				if (restored && uploadQueue.length > 0) {
					console.log('恢复了队列，共', uploadQueue.length, '个任务');
					updateQueueDisplay();
					updateFloatButton();
					
					// 如果有等待或上传中的任务，继续处理队列
					const pendingTasks = uploadQueue.filter(task => 
						task.status === 'waiting' || task.status === 'uploading'
					);
					
					if (pendingTasks.length > 0 && !queueRunning) {
						console.log('继续处理队列，剩余任务:', pendingTasks.length);
						// 重置上传中的任务为等待状态（因为文件对象丢失）
						uploadQueue.forEach(task => {
							if (task.status === 'uploading') {
								task.status = 'waiting';
								task.progress = 0;
							}
						});
						
						processUploadQueue();
					}
					
					// 如果有任务正在进行，显示悬浮按钮
					if (uploadQueue.length > 0) {
						$("#uploadFloatBtn").show();
					}
				}
			}
			
			// ========== 表单记忆功能 ==========
			const FORM_HISTORY_KEY = 'upload_form_history';
			const MAX_HISTORY_ITEMS = 10;
			
			// 初始化表单记忆
			function initFormMemory() {
				loadFormHistory();
				bindFormEvents();
			}
			
			// 绑定表单事件
			function bindFormEvents() {
				// 输入框失焦时保存历史
				$('#bt, #zz, #zuzhi, #zhishidian').on('blur', function() {
					const field = this.id;
					const value = $(this).val().trim();
					if (value) {
						saveToHistory(field, value);
						updateSuggestions(field);
					}
				});
				
				// 点击建议标签
				$(document).on('click', '.suggestion-tag', function() {
					const field = $(this).data('field');
					const value = $(this).text();
					$('#' + field).val(value);
				});
			}
			
			// 保存到历史记录
			function saveToHistory(field, value) {
				const history = getFormHistory();
				
				if (!history[field]) {
					history[field] = [];
				}
				
				// 移除已存在的值
				history[field] = history[field].filter(item => item !== value);
				
				// 添加到开头
				history[field].unshift(value);
				
				// 限制数量
				history[field] = history[field].slice(0, MAX_HISTORY_ITEMS);
				
				localStorage.setItem(FORM_HISTORY_KEY, JSON.stringify(history));
			}
			
			// 获取表单历史
			function getFormHistory() {
				try {
					return JSON.parse(localStorage.getItem(FORM_HISTORY_KEY) || '{}');
				} catch (e) {
					return {};
				}
			}
			
			// 加载表单历史
			function loadFormHistory() {
				const fields = ['bt', 'zz', 'zuzhi', 'zhishidian'];
				fields.forEach(field => {
					updateSuggestions(field);
				});
			}
			
			// 更新建议
			function updateSuggestions(field) {
				const history = getFormHistory();
				const suggestions = history[field] || [];
				const fieldMap = {
					'bt': 'titleSuggestions',
					'zz': 'authorSuggestions',
					'zuzhi': 'orgSuggestions',
					'zhishidian': 'knowledgeSuggestions'
				};
				
				const containerId = fieldMap[field];
				if (!containerId) return;
				
				let html = '';
				suggestions.slice(0, 5).forEach(item => {
					html += `<span class="suggestion-tag" data-field="${field}">${item}</span>`;
				});
				
				$('#' + containerId).html(html);
			}
			
			// 删除封面函数
			function removeCover() {
				coverFile = null;
				$("#coverPreview").html('<div class="cover-placeholder">点击添加封面<br><small style="color: #999; font-size: 11px;">建议: 不超过1M, 比例16:9</small></div>');
				// 清空文件输入
				$("#coverFile")[0].value = '';
			}
			
			// ========== 保留的必要函数 ==========
			function showinfo(){//查看发布的资源
				window.location.href = 'achievements.html'
			}
			
			function jixufabu(){
				window.location.reload()
			}
			
			// ========== 悬浮按钮和通知相关函数 ==========
			
			// 切换上传进度显示
			function toggleUploadProgress() {
				if ($("#uploadProgressModal").is(':visible')) {
					hideUploadProgress();
				} else {
					showUploadProgress();
				}
			}
			
			// 更新悬浮按钮状态
			function updateFloatButton() {
				const activeTasks = uploadQueue.filter(task => task.status !== 'completed').length;
				const completedTasks = uploadQueue.filter(task => task.status === 'completed').length;
				
				if (uploadQueue.length > 0) {
					$("#uploadFloatBtn").show();
					$("#taskCountBadge").text(activeTasks).toggle(activeTasks > 0);
					$("#uploadFloatBtn").toggleClass('has-tasks', activeTasks > 0);
				} else {
					$("#uploadFloatBtn").hide();
				}
			}
			
			// 显示完成通知
			function showCompletionToast() {
				$("#completionToast").show();
				setTimeout(() => {
					$("#completionToast").fadeOut(300);
				}, 3000);
			}
			
			// 清空上传队列
			function clearUploadQueue() {
				// 只清空已完成和失败的任务
				uploadQueue = uploadQueue.filter(task => task.status === 'uploading' || task.status === 'waiting');
				updateQueueDisplay();
				updateFloatButton();
				saveQueueToStorage(); // 保存状态
				
				if (uploadQueue.length === 0) {
					hideUploadProgress();
					clearQueueStorage(); // 如果队列为空，清除存储
				}
				
				cocoMessage.success(1000, '已清空完成的任务');
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
