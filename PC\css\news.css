body {
	background: #fbfbfb !important;
}

.newsbox {
	width: 66.666666rem;
	display: flex;
	margin: 0px auto;
}

.newboxleft {
	width: 46.145833rem;
	background: #FFFFFF;
}

.padd {
	padding: 2.083333rem;
}

.newsright {
	width: calc(100% - 46.145833rem - 0.78125rem);
	margin-left: 0.78125rem;
}

.content {
	padding-top: 3.125rem;
	padding-bottom: 3.125rem;
	min-height: 28rem;
}

#swiper,#swiper1,#swiper2,#swiper3 {
	overflow: hidden;
	position: relative;
}

.swiperdiv {
	position: absolute;
	bottom: 0px;
	left: 0;
	right: 0;
	background: rgba(111, 7, 10, 0.9);
	padding: 0.9375rem;
	display: flex;
	align-items: center;
}

.swiperdiv .swipertitle {
	font-size: 0.9375rem;
	color: #fefefe;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.swiperdiv .swipercontent {
	font-size: 0.729166rem;
	color: #c79697;
	display: -webkit-box;
	text-overflow: ellipsis;
	overflow: hidden;
	-webkit-box-orient: vertical;
	-webkit-line-clamp: 2;
	padding-top: 0.364583rem;
}

#swiper img {
	width: 100%;
	display: block;
	border-radius: 0.260416rem;
}

.border {
	background: linear-gradient(to right, #c00714, #c00714, #ffc156);
	height: 2px;
}

.newtitles1 {
	height: 2.864583rem;
	display: flex;
	align-items: center;
	font-size: 0.9375rem;
	color: #c00714;
	font-weight: bold;
	border-bottom: 0.052083rem solid #e1e1e1;
	padding-left: 0.78125rem;
	padding-right: 0.78125rem;
	background: #FFFFFF;
	justify-content: space-between;
}

.boxxx {
	background: #FFFFFF;
}

.sjsj {
	width: 8.59375rem;
	font-size: 0.729166rem;
	color: #c79697;
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.lllll {
	width: calc(100% - 8.59375rem);
}

.sjsj img {
	width: 0.9375rem !important;
	display: block !important;
	padding-right: 0.260416rem;
}

.lefttop {
	border-bottom: 2px solid #c00714;
	display: flex;
	align-items: center;
	background: url(../img/topimg2.png) no-repeat;
}
.ssss{
	background: #FFFFFF !important;
}
.lefttop label{
	display: flex;
	height: 3.385416rem;
	align-items: center;
}
.lefttop label:first-child{
	color: #e56c74;
	font-size: 1.145833rem;
	padding-left: 1.041666rem;
}
.lefttop label:last-child{
	color: #FFFFFF;
	font-size: 1.145833rem;
	font-weight: bold;
}

.lefttop div {
	width: 11.458333rem;
	height: 3.385416rem;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 1.145833rem;
	color: #333333;
	cursor: pointer;
}

.activetitles2 {
	background: url(../img/newtopbag.png) no-repeat;
	background-size: 100%;
	color: #ffffff !important;
	font-weight: bold;
}

.boxitem {
	display: flex;
	align-items: center;
	height: 2.239583rem;
	border-bottom: 0.052083rem dashed #e1e1e1;
	margin-left: 0.78125rem;
	margin-right: 0.260416rem;
	cursor: pointer;
}

.boxitem:last-child {
	border: none;
}

.itemnr {
	font-size: 0.833333rem;
	color: #333333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	width: calc(100% - 5.208333rem - 0.78125rem);
	box-sizing: border-box;
	padding-left: 0.78125rem;
	position: relative;
}

.itemnr::after {
	position: absolute;
	content: "";
	width: 0.15625rem;
	height: 0.15625rem;
	background: #cecece;
	left: 0.260416rem;
	top: 0;
	bottom: 0;
	margin: auto;
	border-radius: 100rem;
}

.itemsj {
	font-size: 0.729166rem;
	color: #cecece;
	width: 5.208333rem;
	padding-left: 0.78125rem;
}

.bbbb {
	height: 0.78125rem;
}

.newitem {
	display: flex;
	align-items: center;
	height: 3.385416rem;
	border-bottom: 0.052083rem dashed #e1e1e1;
	padding: 0px 0.520833rem;
	cursor: pointer;
}

.newitem:last-child {
	border: none;
}

.newitemtitle {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	width: calc(100% - 13.020833rem);
	font-size: 0.9375rem;
	color: #333333;
}

.newitemsj {
	display: flex;
	align-items: center;
	width: 13.020833rem;
	justify-content: flex-end;
	font-size: 0.729166rem;
	color: #cecece;
}

.newitemsj img {
	width: 0.9375rem;
	display: block;
	padding-right: 0.260416rem;
}

#swiperpagination {
	right: 0.260416rem;
	bottom: 3.90625rem;
	display: flex;
	justify-content: flex-end;
	background: #7b1b1e;
	padding: 0.260416rem 0.260416rem;
	border-radius: 10rem;
}

#swiperpagination span {
	margin: 0px 0.416666rem;
}

#swiperpagination .swiper-pagination-bullet-active {
	background: #FFFFFF !important;
}

#swiperpagination .swiper-pagination-bullet {
	background: #c79697;
}

#swpbox {
	position: relative;
}

.newtitle {
	font-size: 1.25rem;
	font-weight: bold;
	color: #333333;
	text-align: center;
	padding-top: 1.041666rem;
	padding-bottom: 1.041666rem;
}

.newnr p {
	font-size: 0.9375rem;
	color: #666666;
	text-indent: 2em;
	line-height: 1.5625rem;
	padding-bottom: 0.78125rem;
	padding-top: 0.78125rem;
}

.newnr img {
	width: 100%;
	display: block;
}

.bbbbbbbbbbb {
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding: 0.78125rem;
	padding-top: 1.5625rem;
}

.bbbbbbbbbbb a {
	width: 45%;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	font-size: 0.9375rem;
	color: #333333;
	text-decoration: underline;
}
.sqtopsss{
	height: 2.864583rem;
	display: flex;
	align-items: center;
	border-bottom: 0.052083rem dashed #e1e1e1;
	padding: 0px 0.78125rem;
}
.sqtopsss div{
	width: 50%;
	display: flex;
	align-items: center;
}
.sqtopsss div label{
	display: flex;
	align-items: center;
	color: #cecece;
	font-size: 0.729166rem;
	margin-right: 0.78125rem;
}
.sqtopsss div label img{
	display: block;
	width: 0.9375rem;
	padding-right: 0.260416rem;
}
.sqtopsss div:last-child{
	justify-content: flex-end;
}
.sqtopsss div:last-child label{
	margin-left: 0.78125rem;
	margin-left: 0px;
}
.jtdiv {
	display: flex;
	align-items: center;
}

.jtdiv span {
	margin-left: 0.260416rem;
	display: block;
	width: 1.145833rem;
	height: 1.145833rem;
	background: url(../img/jtb.png) no-repeat;
	cursor: pointer;
}

.jtdiv span:hover {
	background: url(../img/jta.png) no-repeat;
}

.jtdiv .leftimg {
	transform: rotateZ(180deg);
}

.jtdiv .rightimg:hover {
	transform: rotateZ(180deg);
}

.jtdiv .leftimg:hover {
	transform: rotateZ(0);
}

.dzbox{
	display: flex;
	align-items: center;
	justify-content: center;
	padding-top: 2.083333rem;
	padding-bottom: 2.083333rem;
}
.mz div:first-child{
	width: 7.291666rem;
	height: 2.916666rem;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}
.mz1 div:first-child{
	background: #f3f3f3;
	border-radius: 0.520833rem;
}
.mz2 div:first-child{
	background: #c00714;
	border-radius: 0.520833rem;
}
.mz div:last-child{
	font-size: 0.729166rem;
	color: #333333;
	text-align: center;
	padding-top: 0.260416rem;
}
#dz2{
	display: none;
}