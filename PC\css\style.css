/* 思源黑体字体声明 */
@font-face {
	font-family: 'Source Han Sans CN';
	font-style: normal;
	font-weight: 300;
	font-display: swap;
	src: url('./vendor/webfonts/SourceHanSansCN-Light.woff2') format('woff2');
	unicode-range: U+4E00-9FFF, U+3400-4DBF, U+20000-2A6DF, U+2A700-2B73F, U+2B740-2B81F, U+2B820-2CEAF, U+F900-FAFF, U+2F800-2FA1F;
}

@font-face {
	font-family: 'Source Han Sans CN';
	font-style: normal;
	font-weight: 400;
	font-display: swap;
	src: url('./vendor/webfonts/SourceHanSansCN-Regular.woff2') format('woff2');
	unicode-range: U+4E00-9FFF, U+3400-4DBF, U+20000-2A6DF, U+2A700-2B73F, U+2B740-2B81F, U+2B820-2CEAF, U+F900-FAFF, U+2F800-2FA1F;
}

@font-face {
	font-family: 'Source Han Sans CN';
	font-style: normal;
	font-weight: 500;
	font-display: swap;
	src: url('./vendor/webfonts/SourceHanSansCN-Medium.woff2') format('woff2');
	unicode-range: U+4E00-9FFF, U+3400-4DBF, U+20000-2A6DF, U+2A700-2B73F, U+2B740-2B81F, U+2B820-2CEAF, U+F900-FAFF, U+2F800-2FA1F;
}

@font-face {
	font-family: 'Source Han Sans CN';
	font-style: normal;
	font-weight: 700;
	font-display: swap;
	src: url('./vendor/webfonts/SourceHanSansCN-Bold.woff2') format('woff2');
	unicode-range: U+4E00-9FFF, U+3400-4DBF, U+20000-2A6DF, U+2A700-2B73F, U+2B740-2B81F, U+2B820-2CEAF, U+F900-FAFF, U+2F800-2FA1F;
}

/* 页面平滑过渡和防闪烁设置 + 响应式字体大小系统 */
html {
	scroll-behavior: smooth;
	/* PC端基础字体大小 - 设置为固定值以稳定rem单位 */
	font-size: 16px; /* Default font size for PC */
}

/* 页面内容渐入效果，减少切换时闪烁 */
body {
	opacity: 1;
	transition: opacity 0.2s ease-in-out;
	/* padding-top: 6rem; */
	/* Standard system font stack for body - 微软雅黑优先 */
	font-family: 'Microsoft YaHei', 'Microsoft YaHei UI', '微软雅黑', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
}

/* 页面加载时的渐入效果 */
body.loading {
	opacity: 0.95;
}

/* 页面切换时的平滑过渡效果 */
.page-transition {
	position: relative;
	overflow: hidden;
}

/* 内容区域切换动画 */
.content-wrapper {
	transition: opacity 0.3s ease-in-out, transform 0.3s ease-in-out;
	will-change: opacity, transform;
}

.content-wrapper.fade-out {
	opacity: 0;
	transform: translateY(20px);
}

.content-wrapper.fade-in {
	opacity: 1;
	transform: translateY(0);
}

/* 导航栏保持固定，防止重新渲染 */
.topview {
	width: 100%;
	height: 6rem;
	/* margin-bottom: -1rem; */
	display: flex;
	flex-wrap: wrap;
	justify-content: center;
	background: url(../img/indextopbag.png);
	background-size: cover;
	position: fixed;
	top: 0;
	left: 0;
	z-index: 1000;
	transition: all 0.2s ease-in-out;
	will-change: auto;
	backface-visibility: hidden;
	-webkit-backface-visibility: hidden;
	transform: translateZ(0);
	-webkit-transform: translateZ(0);
}

/* 菜单项激活状态的平滑切换 */
.menuaaaa {
	will-change: color, font-weight;
	backface-visibility: hidden;
	-webkit-backface-visibility: hidden;
}

/* 主要内容区域样式优化 */
.contextview,
.content,
main {
	transition: opacity 0.3s ease-in-out;
	will-change: opacity;
}

/* 页面切换加载指示器 */
.page-loading {
	position: fixed;
	top: 6rem;
	left: 0;
	right: 0;
	height: 3px;
	background: linear-gradient(90deg, transparent, #ffd05f, transparent);
	transform: translateX(-100%);
	animation: loading-bar 1s ease-in-out infinite;
	z-index: 999;
	display: none;
}

@keyframes loading-bar {
	0% { transform: translateX(-100%); }
	100% { transform: translateX(100%); }
}

.page-loading.active {
	display: block;
}

/* 全局样式重置 */
* {
	margin: 0;
	padding: 0;
	list-style: none;
}

/* 链接样式 - 移除默认下划线 */
a {
	text-decoration: none !important;
}
html {
	font-size: 1vw;
}

/* 移动端响应式适配 */
@media screen and (max-width: 1024px) {
	/* 平板竖屏 */
	html {
		font-size: 1.2vw;
	}
}

@media screen and (max-width: 768px) {
	/* 手机横屏和小平板 */
	html {
		font-size: 1.5vw;
	}
}

@media screen and (max-width: 480px) {
	/* 手机竖屏 */
	html {
		font-size: 2.5vw;
	}
}

/* 移动端横屏特殊处理 - 与PC端保持缩放一致 */
@media screen and (max-width: 932px) and (orientation: landscape) {
	html {
		/* 横屏时使用高度作为基准，确保与PC端缩放一致 */
		font-size: 1.2vh;
	}
}

@media screen and (max-width: 768px) and (orientation: landscape) {
	html {
		font-size: 1.4vh;
	}
}

@media screen and (max-width: 667px) and (orientation: landscape) {
	html {
		font-size: 1.6vh;
	}
}

/* 超大屏幕适配 */
@media screen and (min-width: 1920px) {
	html {
		/* 限制最大字体大小，避免超大屏显示过大 */
		font-size: 18px; /* Adjusted for larger screens */
	}
}

/* 超小屏幕保底设置 */
@media screen and (max-width: 320px) {
	html {
		font-size: max(14px, 3vw);
	}
}

/* 全局字体设置 - 使用思源黑体 */
body {
	/* font-family: 'Source Han Sans CN', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol'; */ /* This is now handled by the primary body rule */
	padding-top: 6rem;
}

/* 针对中文字体的优化设置 */
.chinese-text {
	font-family: 'Microsoft YaHei', 'Microsoft YaHei UI', '微软雅黑', 'Source Han Sans CN', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', sans-serif;
	font-feature-settings: 'liga' 1, 'kern' 1;
	text-rendering: optimizelegibility;
}

/* 主要文本区域应用思源黑体 */
.contextview,
.itemtopboxitem,
.title,
.txt,
.topview,
.hssjitem,
.riitem {
	font-family: inherit; /* 继承body的系统字体 */
}

/* 心声社区列表特别优化 */
.index .itemtopboxitem .txt {
	font-family: inherit !important;
	font-weight: 400 !important;
}

.index .itemtopboxitem:hover .txt {
	font-weight: 500 !important;
}

/* 标题使用中等字重但保持系统字体 */
.title,
.titleactive {
	font-weight: 500 !important;
	font-family: inherit;
}

/* 顶部导航第一行 - 包含logo和登录区域 */
.topview_1 {
	width: 66.666666rem;
	height: 3.333333rem;
	display: flex;
	flex-wrap: nowrap;
	justify-content: space-between;
	align-items: center;
}

/* 登录视图区域样式 */
.topview_1 .loginview {
	display: flex;
	align-items: center;
	cursor: pointer;
}

/* 登录区域图标样式 */
.topview_1 .loginview img {
	padding-right: 0.260416rem;
	width: 0.78125rem;
	height: auto;
	display: block;
}

/* 搜索输入框样式 */
.topview_1 input {
	width: 12.5rem;
	height: 1.5625rem;
	line-height: 1.5625rem;
	border: none;
	outline-style: none;
	font-size: 0.833333rem;
}

/* 搜索图标样式 */
.topview_1 .ssview img {
	padding-right: 0.520833rem;
	width: 0.9375rem;
	height: auto;
	display: block;
}

/* 搜索框整体样式 */
.topview_1 .ssview {
	background: #FFFFFF;
	display: flex;
	align-items: center;
	padding-left: 0.520833rem;
	padding-right: 0.520833rem;
	border: 1px solid #cc4347;
	border-radius: 5px;
	overflow: hidden;
	margin-right: 1.302083rem;
}

/* 登录区域文本样式 */
.topview_1 .loginview label,
.topview_1 .loginview a {
	font-size: 0.833333rem;
	cursor: pointer;
	color: #FFFFFF;
	text-decoration: underline;
}

/* 页脚样式 */
footer {
	width: 100%;
	min-height: 13.541666rem;
	background: url(../img/footerbag.png) no-repeat;
	background-size: cover;
	display: flex;
	justify-content: center;
	flex-wrap: wrap;
}

/* 页脚备案信息样式 */
footer .ba {
	width: 100%;
	color: #FFFFFF;
	font-size: 0.9375rem;
	display: flex;
	justify-content: center;
	align-items: center;
}

/* 页脚备案图标样式 */
footer .ba img {
	margin-left: 0.260417rem;
	margin-right: 0.260417rem;
}

/* 页脚友情链接区域样式 */
footer .yqlj {
	width: 66.666666rem;
	min-height: 6.770833rem;
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	padding-bottom: 1.25rem;
}

/* 友情链接标题样式 */
footer .yqlj .title {
	width: 100%;
	font-size: 0.9375rem;
	color: #c79697;
}

/* 友情链接容器样式 */
footer .yqlj .box {
	width: 100%;
	display: flex;
	justify-content: flex-start;
	font-size: 0.729166rem;
	border-left: 0.0625rem solid #c79697;
	padding-left: 3.125rem;
	box-sizing: border-box;
}

/* 友情链接项样式 */
footer .yqlj .box a {
	padding-right: 1.875rem;
	color: #c79697;
	text-decoration: underline;
	line-height: 1.5rem;
}

/* 友情链接悬停效果 */
footer .yqlj .box a:hover {
	color: #f8b967;
}

/* 返回顶部按钮样式 */
#backtop {
	position: fixed;
	right: 6.25rem;
	background: #c00714;
	top: 75%;
	cursor: pointer;
	padding: 0 0 !important;
	width: 4rem !important;
	height: 3rem !important;
	z-index: 999;
}

/* 返回顶部图标样式 */
#backtop img {
	width: 1.302083rem;
	height: 1.302083rem;
	display: block;
	margin: 0px auto;
	margin-top: 1.041666rem;
}

/* 返回顶部文字样式 */
#backtop div {
	width: 100%;
	height: 1.5625rem;
	text-align: center;
	font-size: 0.833333rem;
	color: #FFFFFF;
	margin-top: 0;
	line-height: 1.5625rem;
}

/* 分页区域样式 */
.fybox {
	display: flex;
	justify-content: center;
	align-items: center;
	margin-top: 1.5625rem;
}

/* 分页按钮样式 */
.fybox span {
	color: #c79697;
	font-size: 0.729166rem;
	width: 3.645833rem;
	height: 1.5625rem;
	border: 1px solid #c79697;
	text-align: center;
	line-height: 1.5625rem;
	margin-left: 0.390625rem;
	margin-right: 0.390625rem;
	cursor: pointer;
}

/* 分页数字区域样式 */
.num {
	display: flex;
	align-items: center;
	font-size: 0.729166rem;
	color: #c79697;
}

/* 分页数字标签样式 */
.num label {
	margin-left: 0.260416rem;
	margin-right: 0.260416rem;
	cursor: pointer;
}

/* 分页按钮悬停效果 */
.fybox span:hover {
	color: #c00714;
	border-color: #c00714;
}

/* 分页数字悬停效果 */
.num label:hover {
	color: #c00714;
	font-weight: bold;
	text-decoration: underline;
}

/* 当前页码高亮样式 */
.num .actinum {
	color: #c00714;
	font-weight: bold;
	text-decoration: underline;
}

/* 退出登录按钮样式 */
.tclogin {
	margin-left: 1.5625rem;
}

/* 返回首页链接样式 */
.backhome {
	font-size: 0.833333rem;
	cursor: pointer;
	color: #FFFFFF;
	text-decoration: underline;
}

/* 提示消息成功样式 */
.coco-msg.success {
	color: #333333;
	background-color: rgba(255, 255, 255, 0.9);
	box-shadow: none;
}

/* 提示消息基础样式 */
.coco-msg {
	border-radius: 5px;
	height: 3.125rem;
	line-height: 3.125rem;
	padding: 0.9375rem 1.09375rem;
}

/* 提示消息内容样式 */
.coco-msg-content {
	font-size: 0.833333rem;
	width: 100%;
	margin: 0px 0.729166rem;
}

/* 提示消息图标样式 */
.coco-msg-icon img {
	width: 1.25rem;
	height: auto;
}

/* 提示消息进度条样式 */
.coco-msg-progress {
	width: 0.833333rem;
	height: 0.833333rem;
}

/* 提示消息位置样式 */
.coco-msg-stage {
	top: 6.25rem;
}

/* 内容标题区域样式 */
.contenttitlebox {
	height: 3.125rem;
	background: #ffffff;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 1rem;
}

/* 内容标题容器样式 */
.contenttitlebox div {
	width: 66.666666rem;
	display: flex;
	align-items: center;
}

/* 内容标题图标样式 */
.contenttitlebox div img {
	width: 0.78125rem;
	display: block;
	padding-left: 0.260416rem;
	padding-right: 0.260416rem;
}

/* 内容标题文本样式 */
.contenttitlebox div a,
.contenttitlebox div {
	font-size: 0.833333rem;
	color: #cecece;
}

/* 当前位置标记样式 */
.aa {
	color: #333333;
}

/* iframe清除边框 */
iframe {
	border: none;
	display: block;
}

/* PDF阅读器顶部栏样式 */
.topviewsss {
	width: 41.666666rem;
	margin: 0px auto;
	height: 3.645833rem;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #323639;
	color: #FFFFFF;
	font-size: 1.145833rem;
	font-weight: bold;
}

/* PDF阅读器顶部栏位置调整 */
.topviewsss {
	position: relative;
	width: 100%;
	border-bottom: 0.052083rem solid #474a4d;
}

/* PDF阅读器关闭按钮样式 */
.topviewsss a {
	position: absolute;
	right: 0.520833rem;
	top: 0.520833rem;
	color: #FFFFFF;
	font-size: 0.729166rem;
	font-weight: normal;
	cursor: pointer;
}

/* PDF阅读器内容区域样式 */
.pdfbox {
	width: 100%;
	height: 100%;
}

/* PDF查看器样式 */
#pdf {
	width: 100% !important;
	height: 100% !important;
}

/* PDF查看器iframe样式 */
#pdf iframe {
	width: 100% !important;
	height: 100% !important;
}

/* 全屏弹出层样式 - 强制覆盖整个视口 */
.tcbox {
	position: fixed !important;
	top: 0 !important;
	left: 0 !important;
	right: 0 !important;
	bottom: 0 !important;
	width: 100vw !important;
	height: 100vh !important;
	margin: 0 !important;
	padding: 0 !important;
	background: rgba(0, 0, 0, 0.8) !important;
	z-index: 10000 !important;
	box-sizing: border-box !important;
	display: none;
}

/* PDF媒体样式调整 */
.pdfbox .media {
	margin: 0px auto;
}

/* Centering for content within .tcbox */
.tcbox > .popup-dialog-content {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    background-color: #fff; /* Assuming a white background, adjust as needed */
    padding: 1.25rem; /* 20px */
    border-radius: 0.5rem; /* 8px */
    box-shadow: 0 0.25rem 0.75rem rgba(0,0,0,0.1); /* 0 4px 12px */
    /* Set a default width and max-width; adjust as needed for your popups */
    width: 90%;
    max-width: 31.25rem; /* 500px */
    /* For taller popups, you might want to add max-height and overflow-y: auto */
    /* max-height: 80vh; */
    /* overflow-y: auto; */
}

/* 顶部导航第二行 - 菜单区域 */
.topview_2{
	width: 66.666666rem;
	height: 2.75rem;
}

/* 菜单项容器样式 */
.topview_2 .itembox{
	display: flex;
	height: 100%;
}

/* 带下拉菜单的菜单项样式 */
.topview_2 .menuitemaaa{
	height: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	flex: 1;
	position: relative;
	width: 6.666666666666rem;
}

/* 下拉菜单显示触发 */
.menuitemaaa:hover .itemcboxaaa{
	display: block;
}

/* 菜单文本样式 */
.menuaaaa{
	font-size: 0.833333rem;
	color: #FFFFFF;
	display: flex;
	height: 100%;
	width: 100%;
	align-items: center;
	justify-content: center;
	font-family: 'Source Han Sans CN', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
	font-weight: 400;
	font-feature-settings: 'liga' 1, 'kern' 1;
	text-rendering: optimizelegibility;
	transition: color 0.2s ease, font-weight 0.2s ease;
}

/* 下拉菜单容器样式 */
.itemcboxaaa{
	position: absolute;
	top: 100%;
	background: rgb(126,8,11);
	padding: 0.625rem;
	left: 0;
	right: 0;
	display: none;
	z-index: 99;
}

/* 下拉菜单项样式 */
.itemcboxaaa a{
	display: block;
	width: 100%;
	text-align: center;
	font-size: 0.833333rem;
	color: #FFFFFF;
}

/* 下拉菜单项悬停效果 */
.itemcboxaaa a:hover{
	color: #ffd05f;
	font-weight: bold;
	transition: color 0.15s ease, font-weight 0.15s ease;
}

/* 第二级菜单项间距 */
.itema2aaa{
	line-height: 1.8rem;
}

/* 菜单项悬停效果 */
.menuaaaa:hover{
	color: #ffd05f;
	font-weight: bold;
	transition: color 0.2s ease, font-weight 0.2s ease;
}

/* 激活状态的菜单项样式 */
.active{
	color: #ffd05f;
	font-weight: bold;
	position: relative;
	transition: all 0.2s ease;
}

/* 激活菜单项下划线效果 */
.active::after{
	position: absolute;
	content: "";
	height: 2px;
	background: #ffd05f;
	width: 100%;
	bottom: 0;
	transition: all 0.2s ease;
}

/* 头部导航区域专用思源黑体 */
.topview,
.topview *,
.topview_1,
.topview_1 *,
.topview_2,
.topview_2 *,
.menuaaaa,
.loginview,
.loginview * {
	font-family: 'Source Han Sans CN', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif !important;
	font-feature-settings: 'liga' 1, 'kern' 1;
	text-rendering: optimizelegibility;
}

/* 移除其他区域的思源黑体设置，使用系统默认字体 */
.contextview,
.itemtopboxitem,
.title,
.txt,
.hssjitem,
.riitem {
	font-family: inherit; /* 继承body的系统字体 */
}

/* 心声社区列表使用系统字体 */
.index .itemtopboxitem .txt {
	font-family: inherit !important;
	font-weight: 400 !important;
}

.index .itemtopboxitem:hover .txt {
	font-weight: 500 !important;
}

/* 标题使用中等字重但保持系统字体 */
.title,
.titleactive {
	font-weight: 500 !important;
	font-family: inherit;
}


