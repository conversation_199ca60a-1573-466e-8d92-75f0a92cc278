<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>VR红色游学 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        /* VR红色游学页面专用样式 */
        .vr-header {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
            padding: 20px 16px;
            margin-top: 56px;
        }
        
        .vr-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .vr-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .search-section {
            background: white;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            position: sticky;
            top: 56px;
            z-index: 100;
        }
        
        .search-input-group {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .search-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #f0f0f0;
            border-radius: 8px;
            font-size: 16px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            border-color: #43e97b;
            background: white;
            box-shadow: 0 0 0 3px rgba(67, 233, 123, 0.1);
        }
        
        .search-btn {
            background: #43e97b;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .search-btn:hover {
            background: #38d9a9;
        }
        
        .hot-keywords {
            background: white;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .hot-keywords-title {
            font-size: 14px;
            color: #666;
            margin-bottom: 12px;
        }
        
        .keywords-list {
            display: flex;
            flex-wrap: wrap;
            gap: 8px;
        }
        
        .keyword-tag {
            padding: 6px 12px;
            background: #f0f8ff;
            color: #43e97b;
            border: 1px solid #43e97b;
            border-radius: 16px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .keyword-tag:hover {
            background: #43e97b;
            color: white;
        }
        
        .vr-content {
            padding: 16px;
        }
        
        .vr-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        
        .vr-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            cursor: pointer;
        }
        
        .vr-card:active {
            transform: scale(0.98);
        }
        
        .vr-image {
            width: 100%;
            height: 120px;
            position: relative;
            overflow: hidden;
        }
        
        .vr-image img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .vr-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: linear-gradient(45deg, rgba(67, 233, 123, 0.8), rgba(56, 249, 215, 0.8));
            display: flex;
            align-items: center;
            justify-content: center;
            opacity: 0;
            transition: opacity 0.3s ease;
        }
        
        .vr-card:hover .vr-overlay {
            opacity: 1;
        }
        
        .vr-play-btn {
            width: 48px;
            height: 48px;
            background: rgba(255, 255, 255, 0.9);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #43e97b;
            font-size: 20px;
        }
        
        .vr-info {
            padding: 12px;
        }
        
        .vr-title-text {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 6px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .vr-keywords {
            font-size: 12px;
            color: #999;
            line-height: 1.3;
            display: -webkit-box;
            -webkit-line-clamp: 1;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .vr-modal {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.9);
            z-index: 2000;
            display: none;
            flex-direction: column;
        }
        
        .vr-modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 16px;
            background: rgba(0, 0, 0, 0.8);
            color: white;
        }
        
        .vr-modal-title {
            font-size: 16px;
            font-weight: 500;
        }
        
        .vr-close-btn {
            background: #ff4757;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .vr-close-btn:hover {
            background: #ff3742;
        }
        
        .vr-iframe {
            flex: 1;
            border: none;
            width: 100%;
            background: white;
        }
        
        .loading-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #43e97b;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }
        
        .empty-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 16px;
            opacity: 0.5;
        }
        
        .empty-title {
            font-size: 16px;
            margin-bottom: 8px;
            color: #666;
        }
        
        .empty-description {
            font-size: 14px;
            line-height: 1.5;
        }
        
        @media (max-width: 375px) {
            .vr-header {
                padding: 16px 12px;
            }
            
            .vr-content {
                padding: 12px;
            }
            
            .vr-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            
            .vr-image {
                height: 160px;
            }
        }
    </style>
</head>
<body class="mobile-vr">
    <!-- 顶部导航栏 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="menu-btn" id="menuBtn">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="logo">
                <h2 style="margin: 0; font-size: 16px; color: #333;">VR红色游学</h2>
            </div>
            <div class="header-actions">
                <div class="user-info" id="userInfo">
                    <button class="login-btn" id="loginBtn">登录</button>
                    <div class="user-avatar" id="userAvatar" style="display: none;">
                        <span id="userName"></span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- VR头部 -->
    <section class="vr-header">
        <div class="vr-title">
            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 28px; height: 28px;">
                <path d="M7 9C7 8.4 7.4 8 8 8H16C16.6 8 17 8.4 17 9V15C17 15.6 16.6 16 16 16H8C7.4 16 7 15.6 7 15V9Z"/>
                <circle cx="10" cy="12" r="1.5"/>
                <circle cx="14" cy="12" r="1.5"/>
            </svg>
            VR红色游学
        </div>
        <div class="vr-subtitle">沉浸式红色文化体验，传承革命精神</div>
    </section>

    <!-- 搜索区域 -->
    <section class="search-section">
        <div class="search-input-group">
            <input type="text" class="search-input" id="searchInput" placeholder="搜索VR游学内容...">
            <button class="search-btn" onclick="searchVR()">搜索</button>
        </div>
    </section>

    <!-- 热词区域 -->
    <section class="hot-keywords" id="hotKeywords" style="display: none;">
        <div class="hot-keywords-title">热词：</div>
        <div class="keywords-list" id="keywordsList">
            <!-- 热词将通过JavaScript动态加载 -->
        </div>
    </section>

    <!-- VR内容 -->
    <main class="vr-content">
        <div class="vr-grid" id="vrGrid">
            <!-- VR项目将通过JavaScript动态加载 -->
        </div>
    </main>

    <!-- VR模态框 -->
    <div class="vr-modal" id="vrModal">
        <div class="vr-modal-header">
            <div class="vr-modal-title" id="vrModalTitle">VR红色游学</div>
            <button class="vr-close-btn" onclick="closeVR()">返回列表</button>
        </div>
        <iframe class="vr-iframe" id="vrIframe" src=""></iframe>
    </div>

    <!-- 侧边菜单 -->
    <nav class="side-menu" id="sideMenu">
        <div class="menu-overlay" id="menuOverlay"></div>
        <div class="menu-content">
            <div class="menu-header">
                <div class="menu-logo">
                    <img src="../../PC/img/logo.png" alt="思政一体化平台">
                    <h3>思政一体化平台</h3>
                </div>
                <button class="menu-close" id="menuClose">×</button>
            </div>
            <div class="menu-items" id="menuItems">
                <!-- 菜单项将通过JavaScript动态加载 -->
            </div>
        </div>
    </nav>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 4C16.55 4 17 4.45 17 5V9.5L20.5 13L19 14.5L15.5 11H15V5C15 4.45 15.45 4 16 4Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>我的</span>
        </a>
    </nav>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83L16.59 15.41L18 14L12 8L6 14Z"/>
        </svg>
    </button>

    <script>
        let classid = null;
        let isLoading = false;
        let hotKeywords = [];

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 检查登录状态
            updateLoginUI();
            
            // 加载VR数据
            loadVRData();
            
            // 检查URL参数，如果有ID则直接打开
            const urlParams = new URLSearchParams(window.location.search);
            const vrId = urlParams.get('id');
            if (vrId) {
                loadVirtualTourById(vrId);
            }
        });

        function loadVRData() {
            if (isLoading) return;
            
            isLoading = true;
            const container = document.getElementById('vrGrid');
            
            container.innerHTML = `
                <div class="loading-state" style="grid-column: 1 / -1;">
                    <div class="loading-spinner"></div>
                    <p>加载中...</p>
                </div>
            `;
            
            // 首先获取VR分类ID
            $.ajax({
                url: baseurl + "/web/category/vr",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200' && res.data && res.data.length > 0) {
                        classid = res.data[0].id;
                        loadVRList();
                    } else {
                        renderEmptyState();
                    }
                },
                error: (err) => {
                    console.error('获取VR分类失败:', err);
                    renderEmptyState();
                }
            });
        }

        function loadVRList(searchKeyword = null) {
            $.ajax({
                url: baseurl + "/web/posts",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: {
                    pageNum: 1,
                    pageSize: 999999,
                    categoryId: classid,
                    title: searchKeyword
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        renderVRList(res.data.list || []);
                        updateHotKeywords(res.data.list || []);
                    } else {
                        renderEmptyState();
                    }
                    isLoading = false;
                },
                error: (err) => {
                    console.error('加载VR列表失败:', err);
                    renderEmptyState();
                    isLoading = false;
                }
            });
        }

        function renderVRList(vrList) {
            const container = document.getElementById('vrGrid');
            
            if (!vrList || vrList.length === 0) {
                renderEmptyState();
                return;
            }
            
            let html = '';
            vrList.forEach(item => {
                html += `
                    <div class="vr-card" onclick="openVR('${item.id}', '${item.redirectUrl}', '${item.title}')">
                        <div class="vr-image">
                            <img src="${baseurl}${item.thumbPath[0]}" alt="${item.title}" onerror="this.style.display='none'">
                            <div class="vr-overlay">
                                <div class="vr-play-btn">
                                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 24px; height: 24px;">
                                        <path d="M8 5v14l11-7z"/>
                                    </svg>
                                </div>
                            </div>
                        </div>
                        <div class="vr-info">
                            <div class="vr-title-text">${item.title}</div>
                            <div class="vr-keywords">${item.keyWords || '红色文化 VR体验'}</div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
        }

        function updateHotKeywords(vrList) {
            const hotKeywordsSection = document.getElementById('hotKeywords');
            const keywordsList = document.getElementById('keywordsList');
            
            // 提取关键词
            const keywords = [];
            vrList.forEach(item => {
                if (item.keyWords) {
                    keywords.push(item.keyWords);
                }
            });
            
            if (keywords.length > 0) {
                hotKeywords = keywords.slice(0, 5); // 取前5个关键词
                
                let html = '';
                hotKeywords.forEach(keyword => {
                    html += `<span class="keyword-tag" onclick="searchByKeyword('${keyword}')">${keyword}</span>`;
                });
                
                keywordsList.innerHTML = html;
                hotKeywordsSection.style.display = 'block';
            } else {
                hotKeywordsSection.style.display = 'none';
            }
        }

        function searchVR() {
            const keyword = document.getElementById('searchInput').value.trim();
            loadVRList(keyword);
        }

        function searchByKeyword(keyword) {
            document.getElementById('searchInput').value = keyword;
            loadVRList(keyword);
        }

        function openVR(id, url, title) {
            // 检查用户是否登录
            const loginStatus = checkLoginStatus();
            if (!loginStatus.isLoggedIn) {
                MobileUtils.showToast('请先登录', 'warning');
                setTimeout(() => {
                    window.location.href = '../login.html';
                }, 1000);
                return;
            }
            
            // 记录点击数
            clicknum(id);
            
            // 记录学习记录
            recordVRLearning(id);
            
            // 打开VR模态框
            const modal = document.getElementById('vrModal');
            const iframe = document.getElementById('vrIframe');
            const modalTitle = document.getElementById('vrModalTitle');
            
            modalTitle.textContent = title;
            iframe.src = url;
            modal.style.display = 'flex';
            
            // 添加错误处理
            iframe.onload = function() {
                console.log('VR内容加载成功');
            };
            
            iframe.onerror = function() {
                closeVR();
                MobileUtils.showToast('VR内容加载失败，请稍后重试', 'error');
            };
        }

        function closeVR() {
            const modal = document.getElementById('vrModal');
            const iframe = document.getElementById('vrIframe');
            
            modal.style.display = 'none';
            iframe.src = '';
            iframe.onload = null;
            iframe.onerror = null;
        }

        function recordVRLearning(id) {
            const json = {
                infoId: id,
                categoryId: classid,
                totalInfo: "1",
                positioning: "1",
                progress: "100%",
                learningTime: 60,
                type: "VR红色游学"
            };
            
            $.ajax({
                url: baseurl + "/study/record/add",
                type: 'post',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: JSON.stringify(json),
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        console.log('学习记录保存成功');
                    } else {
                        console.error('保存学习记录失败:', res.message);
                    }
                },
                error: (err) => {
                    console.error('保存学习记录失败:', err);
                }
            });
        }

        function loadVirtualTourById(id) {
            $.ajax({
                url: baseurl + "/web/posts/" + id,
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200' && res.data) {
                        // 延迟执行，确保页面已加载
                        setTimeout(function() {
                            openVR(id, res.data.redirectUrl, res.data.title);
                        }, 500);
                        
                        // 同时加载列表
                        loadVRData();
                    } else {
                        MobileUtils.showToast("获取内容失败，请刷新重试", "error");
                        loadVRData();
                    }
                },
                error: (err) => {
                    console.error('获取VR内容失败:', err);
                    MobileUtils.showToast("获取内容失败，请刷新重试", "error");
                    loadVRData();
                }
            });
        }

        function renderEmptyState() {
            const container = document.getElementById('vrGrid');
            container.innerHTML = `
                <div class="empty-state" style="grid-column: 1 / -1;">
                    <svg class="empty-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M7 9C7 8.4 7.4 8 8 8H16C16.6 8 17 8.4 17 9V15C17 15.6 16.6 16 16 16H8C7.4 16 7 15.6 7 15V9Z"/>
                        <circle cx="10" cy="12" r="1.5"/>
                        <circle cx="14" cy="12" r="1.5"/>
                    </svg>
                    <div class="empty-title">暂无VR游学内容</div>
                    <div class="empty-description">VR红色游学正在筹备中，敬请期待</div>
                </div>
            `;
            isLoading = false;
        }

        // 搜索框回车事件
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchVR();
            }
        });

        // 阻止模态框内的触摸事件冒泡
        document.getElementById('vrModal').addEventListener('touchmove', function(e) {
            e.stopPropagation();
        });
    </script>
</body>
</html>
