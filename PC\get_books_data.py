import requests
import pandas as pd
import time
import os
import json
from datetime import datetime

def get_categories():
    """获取所有书籍类别"""
    url = "http://localhost:5500/api/web/redbook/list"
    
    try:
        response = requests.get(url)
        
        if response.status_code == 200:
            try:
                data = response.json()
                if isinstance(data, dict) and 'data' in data:
                    data = data['data']
                    if isinstance(data, dict) and 'list' in data:
                        return data['list']  # 直接返回列表部分
                
                print(f"API返回的数据格式异常: {type(data)}")
                print(f"数据内容: {json.dumps(data, ensure_ascii=False)[:200]}...")
                return []
            except Exception as e:
                print(f"解析类别数据失败: {e}")
                print(f"响应内容: {response.text[:200]}...")
                return []
        else:
            print(f"获取类别失败，状态码: {response.status_code}")
            return []
    except Exception as e:
        print(f"请求类别API时出错: {e}")
        return []

def get_books_by_category(category_id):
    """根据类别ID获取书籍列表，使用redBookId参数"""
    all_books = []
    page_num = 1
    total_pages = 999  # 初始化为一个较大的值，将在第一次请求后更新
    
    while page_num <= total_pages:
        try:
            # 添加时间戳
            timestamp = int(time.time() * 1000)
            url = f"http://localhost:5500/api/web/posts?redBookId={category_id}&pageSize=20&pageNum={page_num}&_t={timestamp}&sort=publishedTime%2Cdesc"
            
            print(f"请求第 {page_num} 页书籍数据...")
            
            response = requests.get(url)
            
            if response.status_code == 200:
                try:
                    data = response.json()
                    if isinstance(data, dict) and 'data' in data:
                        data = data['data']
                        
                        # 更新总页数信息
                        if isinstance(data, dict):
                            total_pages = data.get('pages', 0)
                            print(f"总页数: {total_pages}")
                            
                            # 获取当前页的书籍列表
                            current_books = data.get('list', [])
                            if current_books:
                                all_books.extend(current_books)
                                print(f"第 {page_num} 页找到 {len(current_books)} 本书籍")
                            else:
                                print(f"第 {page_num} 页没有书籍数据")
                                break
                        else:
                            print(f"API返回的书籍数据不是字典: {type(data)}")
                            break
                    else:
                        print(f"API返回的书籍数据没有'data'字段")
                        break
                except Exception as e:
                    print(f"解析第 {page_num} 页书籍数据失败: {e}")
                    break
            else:
                print(f"获取第 {page_num} 页书籍失败，状态码: {response.status_code}")
                break
                
            page_num += 1
        except Exception as e:
            print(f"请求第 {page_num} 页书籍API时出错: {e}")
            break
    
    return all_books

def format_date(date_str):
    """格式化日期字符串"""
    if not date_str:
        return ""
    
    try:
        # 处理类似于 "2023-04-18T15:33:47.000+00:00" 的格式
        dt = datetime.fromisoformat(date_str.replace('Z', '+00:00'))
        return dt.strftime('%Y-%m-%d %H:%M:%S')
    except Exception:
        return date_str

def main():
    """主函数，获取所有书籍数据并生成Excel文件"""
    print("开始获取书籍数据...")
    
    # 获取所有类别
    categories = get_categories()
    
    if not categories:
        print("没有找到书籍类别，请检查API是否可用")
        return
    
    print(f"找到 {len(categories)} 个书籍类别")
    
    # 存储所有书籍数据
    all_books = []
    
    # 遍历每个类别
    for idx, category in enumerate(categories):
        print(f"\n处理第 {idx+1}/{len(categories)} 个类别:")
        
        try:
            if not isinstance(category, dict):
                print(f"跳过非字典格式的类别: {type(category)}")
                continue
                
            category_id = category.get('id')
            category_name = category.get('name', '')
            
            if not category_id:
                print(f"跳过没有ID的类别")
                continue
                
            print(f"正在获取类别 '{category_name}' (ID: {category_id}) 的书籍...")
            
            # 获取该类别下的所有书籍
            books = get_books_by_category(category_id)
            
            if not books:
                print(f"类别 '{category_name}' 未找到书籍")
                continue
                
            print(f"类别 '{category_name}' 共找到 {len(books)} 本书籍")
            
            for book in books:
                if not isinstance(book, dict):
                    print(f"跳过非字典格式的书籍数据: {type(book)}")
                    continue
                    
                # 提取书籍信息
                book_info = {
                    '分类': category_name,
                    '书名': book.get('title', ''),
                    '出版社': book.get('publishName', ''),
                    '作者': book.get('author', ''),
                    '发布时间': format_date(book.get('publishedTime', '')),
                    '简介': book.get('excerpt', '')
                }
                
                all_books.append(book_info)
        except Exception as e:
            print(f"处理类别时出错: {e}")
    
    # 创建DataFrame
    if all_books:
        # 按照分类和书名排序
        df = pd.DataFrame(all_books)
        df = df.sort_values(by=['分类', '书名'])
        
        # 创建输出目录（如果不存在）
        os.makedirs('output', exist_ok=True)
        
        # 生成带时间戳的文件名，避免覆盖
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        output_file = f'output/books_data_{timestamp}.xlsx'
        
        # 写入Excel文件，添加样式
        with pd.ExcelWriter(output_file, engine='openpyxl') as writer:
            df.to_excel(writer, index=False, sheet_name='书籍列表')
            
            # 获取工作簿和工作表
            workbook = writer.book
            worksheet = writer.sheets['书籍列表']
            
            # 自动调整列宽
            for column in worksheet.columns:
                max_length = 0
                column_letter = column[0].column_letter
                
                for cell in column:
                    if cell.value:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = min(len(str(cell.value)), 100)  # 限制最大宽度
                        except:
                            pass
                
                adjusted_width = (max_length + 2)
                worksheet.column_dimensions[column_letter].width = adjusted_width
        
        print(f"\n数据已成功导出到 {output_file}")
        print(f"共导出 {len(all_books)} 本书籍数据，跨 {len(df['分类'].unique())} 个分类")
    else:
        print("未找到任何书籍数据")

if __name__ == "__main__":
    main() 