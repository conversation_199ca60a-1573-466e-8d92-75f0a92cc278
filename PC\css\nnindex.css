/*index.css*/
.index {
	background: #fbfbfb;
}

.swiper {
	overflow: hidden;
}
.maptop{
	width: 100%;
}
.indexbanner {
	width: 100%;
	position: relative;
}

.indexbanner img {
	display: block;
	width: 100%;
}
.indexfyq .swiper-pagination-bullet {
	width: 6.25rem !important;
	height: 0.1042rem !important;
	background: rgba(255, 255, 255, 0.5);
}

.indexfyq .swiper-pagination-bullet-active{
	background: rgba(255, 255, 255, 1);
}

.nmain {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	padding-top: 2.0833rem;
}

.mainitem {
	width: 23.4375rem;
}

.ntitle {
	font-size: 1.3542rem;
	font-weight: bold;
	color: #333333;
}

.ntitle img {
	display: block;
	margin-right: 0.2604rem;
	width: 1.3021rem;
}

.nmore {
	font-size: 0.7292rem;
	color: #b1b1b1;
	cursor: pointer;
}

.nmore img {
	display: block;
	width: 0.7813rem;
	margin-left: 0.2604rem;
}

.nmanintitle {
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 0.0521rem solid #dedede;
	padding-bottom: 1.0417rem;
	position: relative;
}

.nmanintitle::after {
	content: "";
	width: 7.6042rem;
	height: 0.0521rem;
	background: #ca1515;
	position: absolute;
	bottom: -0.0521rem;
	left: 0;
}

.ngg {
	width: 21.6667rem !important;
	background: #FFFFFF;
	border-radius: 0.5208rem;
	overflow: hidden;
	height: 17.1875rem;
	position: relative;
}

.ntopnewsitem {
	line-height: 2.8125rem;
	border-bottom: 0.0521rem dashed #dedede;
	font-size: 0.9375rem;
	color: #333333;
	box-sizing: border-box;
	padding-left: 1.8229rem;
	position: relative;
	cursor: pointer;
	display: block;
}

.ntopnewsitem:last-child {
	border-bottom: none;
}

.nhotnews::after {
	content: "";
	width: 0.4167rem;
	height: 0.4167rem;
	position: absolute;
	top: 0;
	bottom: 0;
	margin: auto;
	left: 0.6771rem;
	background: #ff974b;
	border-radius: 52.0313rem;
}

.nothotnews::after {
	content: "";
	width: 0.4167rem;
	height: 0.4167rem;
	position: absolute;
	top: 0;
	bottom: 0;
	margin: auto;
	left: 0.6771rem;
	background: #cecece;
	border-radius: 52.0313rem;
}

.nggtitle {
	width: 100%;
	height: 2.6042rem;
	background: url(../img/ggbag.png) no-repeat;
	background-position: center;
	background-size: cover;
	font-size: 1.3542rem;
	color: #ffffff;
	font-weight: bold;
}

.nggtitle img {
	display: block;
	width: 1.4583rem;
	margin-right: 0.2604rem;
}

.ggitem {
	width: 100%;
	box-sizing: border-box;
	padding: 1.0417rem;
	background: #FFFFFF;
}

.ggitemtitle {
	font-size: 0.9375rem;
	color: #333333;
	border-bottom: 0.0521rem dashed #dedede;
	padding-bottom: 1.0417rem;
	cursor: pointer;
}

.ggitemtitle label {
	color: #ca1515;
	font-size: 0.7292rem;
	margin-right: 0.2604rem;
}

.ggitemstr {
	color: #999999;
	font-size: 0.8333rem;
	line-height: 1.3021rem;
	box-sizing: border-box;
	margin: 0.7813rem 0;
	text-indent: 2em;
}

.ggitemstr {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 6;
	-webkit-box-orient: vertical;
	display: -moz-box;
	-moz-line-clamp: 6;
	-moz-box-orient: vertical;
	word-wrap: break-word;
	word-break: break-all;
}

.ggfyq {
	bottom: 0.2604rem !important;
}

.ggfyq .swiper-pagination-bullet {
	background: #333333;
}

.main1 {
	padding-top: 3.125rem !important;
}

.mapleft {
	width: 26.1458rem;
	height: auto;
	overflow: hidden;
	border-radius: 0.5208rem;
}

.mapleft img {
	display: block;
	width: 100%;
}

.mapright {
	width: calc(100% - 26.1458rem);
	box-sizing: border-box;
	padding-left: 1.0417rem;
}

.mapstr {
	font-size: 0.8333rem;
	color: #999999;
	margin-top: 0.5208rem;
	line-height: 1.3021rem;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 6;
	-webkit-box-orient: vertical;
	display: -moz-box;
	-moz-line-clamp: 6;
	-moz-box-orient: vertical;
	word-wrap: break-word;
	word-break: break-all;
	text-indent: 2em;
}

.mapright {
	display: flex;
	align-items: flex-start;
	justify-content: space-between;
	flex-direction: column;
	flex-wrap: wrap;
	height: 16.8229rem;
}

.mapbtn {
	display: flex;
}

.mapbtn a {
	width: 7.0833rem;
	height: 1.7708rem;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #FFFFFF;
	font-size: 0.7292rem;
	border-radius: 52.0313rem;
	background: #ca1515;
	cursor: pointer;
}

.vrbox {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	margin-top: 1.0417rem;
}

.nvritem {
	width: calc((100% - 1.0417rem * 2) / 3);
	margin-right: 1.0417rem;
	background: #FFFFFF;
	border-radius: 0.5208rem;
	overflow: hidden;
	cursor: pointer;
}

.nvritem:nth-child(3n) {
	margin-right: 0;
}

.nvritem img {
	display: block;
	width: 100%;
}

.nvritemstr {
	box-sizing: border-box;
	padding: 0.7813rem;
}

.nvrtitle {
	font-size: 0.9375rem;
	color: #333333;
}

.nvrgjc {
	font-size: 0.7292rem;
	color: #999999;
	border-bottom: 0.0521rem solid #f1f1f1;
	padding-top: 0.4167rem;
	padding-bottom: 0.5208rem;
}

.nvrstr {
	font-size: 0.7292rem;
	color: #999999;
	padding-top: 0.7813rem;
}

.online {
	background: #FFFFFF;
	margin-top: 3.125rem !important;
	display: flex;
	height: 31.5104rem;
	box-sizing: border-box;
}

.onlineleft {
	width: 29.1667rem;
	height: calc(31.5104rem - 2.0833rem*2);
	padding: 0 2.0833rem;
	margin: 2.0833rem 0;
	box-sizing: border-box;
	border-right: 0.0521rem solid #dedede;
}

.onlineright {
	width: calc(100% - 2.7604rem - 29.1667rem);
	height: 31.5104rem;
	padding: 2.0833rem;
	box-sizing: border-box;
}

.onlineimg {
	width: 2.7604rem;
	height: 31.5104rem;
	background: url(../img/onlineimg.png) no-repeat;
	background-size: cover;
	background-position: center;
	font-size: 1.4583rem;
	font-weight: bold;
	color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
	flex-direction: column;
	flex-wrap: wrap;
}

.onlineimg div {
	width: 100%;
	text-align: center;
}

.onlineimg img {
	display: block;
	width: 1.4583rem;
	margin-bottom: 0.2604rem;
}

.onlineleftview {
	display: flex;
	align-items: flex-start;
	margin-top: 1.5625rem;
}

.oleftview {
	width: 10.4167rem;
}

.orightview {
	width: calc(100% - 10.4167rem);
	box-sizing: border-box;
	padding-left: 1.0417rem;
}

.oleftviewtitle {
	font-size: 0.9375rem;
	color: #333333;
}

.oleftviewzz {
	font-size: 0.7292rem;
	color: #666666;
	border-bottom: 0.0521rem solid #999999;
	padding-bottom: 1.0417rem;
	line-height: 1.5625rem;
}

.oleftviewstr {
	font-size: 0.7292rem;
	color: #999999;
	line-height: 1.3021rem;
	margin-top: 1.0417rem;

	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 10;
	-webkit-box-orient: vertical;
	display: -moz-box;
	-moz-line-clamp: 10;
	-moz-box-orient: vertical;
	word-wrap: break-word;
	word-break: break-all;
	text-indent: 2em;
	height: calc(1.3021rem * 10);
}

.oleftviewbtn {
	display: flex;
	margin-top: 3.6458rem;
}

.orightview {
	position: relative;
}

.oleftviewbtn label {
	width: 6.7708rem;
	height: 1.7708rem;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 0.7292rem;
	color: #ca1515;
	border-radius: 52.0313rem;
	border: 0.0521rem solid #ca1515;
	cursor: pointer;
}
.onlineswiper {
	position: relative;
}
.onlineswiper img {
	width: 100%;
}
#onlineswiperpage{
	width: 100%;
	bottom: -5rem;
	padding-right: 1.0417rem;
	box-sizing: border-box;
}
#onlineswiperpage .swiper-pagination-bullet{
	width: 8px;
	height: 8px;
	margin: 0 4px;
	background: #333333;
}

.onlineswiper .swiper-pagination-bullet {
	background: #333333;
}

.zyswiper {
	width: 100%;
	height: calc(24.8958rem - 1.5625rem);
	margin-top: 1.5625rem;
	position: relative;
}

.onlinerightview {
	width: 100%;
}

.zyswiper .swiper-pagination-bullet {
	background: #333333;
}

.nzyitem {
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.nzyleft {
	width: 12.5rem;
	overflow: hidden;
	border-radius: 0.5208rem;
	position: relative;
}

.nzyleft img {
	display: block;
	width: 100%;
}

.nzyright {
	width: calc(100% - 12.5rem);
	box-sizing: border-box;
	padding-left: 0.5208rem;
}

.nzytitle {
	font-size: 0.9375rem;
	color: #333333;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}

.nzystr {
	font-size: 0.7292rem;
	color: #999999;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	line-height: 2;
}

.nzybtn {
	font-size: 0.7292rem;
	color: #999999;
	display: flex;
	align-items: center;
	padding-top: 2.55rem;
}

.nzybtn label {
	display: flex;
	align-items: center;
	margin-right: 20px;
}

.nzybtn label img {
	width: 0.7292rem;
	display: block;
	padding-right: 3px;
}

.itemtype {
	position: absolute;
	top: 0.3646rem;
	left: 0.3646rem;
	color: #FFFFFF;
	font-size: 0.520833rem;
	padding: 0.104166rem 0.3125rem;
	border-radius: 0.260416rem;
}

.mp4 {
	background: #7a92bb;
}

.pdf {
	background: #f8b967;
}

.ppt,
.pptx {
	background: #f36933;
}

.ncgview {
	display: flex;
	align-items: center;
	margin-top: 1.8229rem;
}

.chenguo {
	margin-top: 3.125rem !important;
}

.cgitem {
	width: calc((100% - 1.0417rem * 3) / 4);
	margin-right: 1.0417rem;
	background: #FFFFFF;
	cursor: pointer;
	border-radius: 0.5208rem;
	overflow: hidden;
}

.cgitem:nth-child(4n) {
	margin-right: 0;
}

.cgimgview {
	position: relative;
}

.cgimgview .img {
	display: block;
	width: 100%;
}

.cgzz {
	position: absolute;
	bottom: 0.3646rem;
	right: 0.3646rem;
	display: flex;
	align-items: center;
}

.cgzz label {
	background: rgba(0, 0, 0, 0.5);
	border-radius: 0.260416rem;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 0.625rem;
	color: #ffffff;
	padding: 0.104166rem 0.260416rem;
	margin-left: 0.260416rem;
}

.cgstrview {
	padding: 0.5208rem;
	box-sizing: border-box;
}

.cgtitle {
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	font-size: 0.9375rem;
	color: #333333;
	padding-bottom: 0.5208rem;
}

.cgstr {
	font-size: 0.7292rem;
	color: #999999;
	line-height: 1.0417rem;
	height: calc(1.0417rem * 2);
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	display: -moz-box;
	-moz-line-clamp: 2;
	-moz-box-orient: vertical;
	word-wrap: break-word;
	word-break: break-all;
}

.xfview {
	margin-top: 3.125rem !important;
	padding-bottom: 4.1667rem;
	display: flex;
	align-items: flex-start;
}

.xfbox {
	width: calc(100% - 22.3958rem);
	box-sizing: border-box;
	padding-right: 2.0833rem;
}

.xsbox {
	width: 22.3958rem;
	background: #FFFFFF;
	height: 23.9583rem;
}

.xstoptitle {
	position: relative;
}

.xstoptitle .bag {
	width: 100%;
	display: block;
}

.xstoptitle div {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 1.25rem;
	color: #FFFFFF;
	font-weight: bold;
}

.xstoptitle div img {
	display: block;
	width: 1.5625rem;
	margin-right: 0.5208rem;
}

.nxflist {
	display: flex;
	align-items: center;
	margin-top: 1.0417rem;
}

.nxfitem {
	width: calc((100% - 1.0417rem) / 2);
	margin-right: 1.0417rem;
	border-radius: 0.5208rem;
	overflow: hidden;
	background: #FFFFFF;
}

.nxfitem:nth-child(2n) {
	margin-right: 0;
}

.nxfimg {
	position: relative;
	overflow: hidden;
	cursor: pointer;
}

.nxfimg img {
	display: block;
	width: 100%;
}

.nxfjj {
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 3;
	-webkit-box-orient: vertical;
	display: -moz-box;
	-moz-line-clamp: 3;
	-moz-box-orient: vertical;
	word-wrap: break-word;
	word-break: break-all;
	box-sizing: border-box;
	color: #f3f3f3;
	font-size: 0.7292rem;
}
.nxfjjbox{
	padding: 0.5208rem;
	background: rgba(0, 0, 0, 0.5);
	position: absolute;
	bottom: -4.1667rem;
	transition: all .2s;
}
.nxfimg:hover .nxfjjbox{
	bottom: 0;
}
.nxfstr{
	padding: 0.5208rem;
}
.nxftitle{
	font-size: 0.9375rem;
	color: #333333;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	display: -moz-box;
	-moz-line-clamp: 2;
	-moz-box-orient: vertical;
	word-wrap: break-word;
	word-break: break-all;
	box-sizing: border-box;
	line-height: 1.25rem;
	height: calc(1.25rem * 2);
}
.nxfzz{
	font-size: 0.7292rem;
	color: #999999;
	display: flex;
	align-items: center;
	margin-top: 0.5208rem;
	border-bottom: 0.0521rem solid #f1f1f1;
	padding-bottom: 0.5208rem;
}
.nxfzz label{
	display: flex;
	align-items: center;
	justify-content: center;
	padding-right: 0.5208rem;
}
.nxfzz label img{
	display: block;
	width: 0.7292rem;
	padding-right: 0.2604rem;
}
.nxfview{
	font-size: 0.7292rem;
	color: #c1c1c1;
	padding-top: 0.5208rem;
}
.nxslist{
	box-sizing: border-box;
	padding: 0 1.0417rem;
}
.nxsitem{
	font-size: 0.9375rem;
	color: #333333;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
	line-height: 2.6042rem;
	border-bottom: 0.0521rem dashed #f1f1f1;
	display: block;
}
.nxsitem:last-child{
	border: none;
}
.nxsitem label{
	font-size: 0.7292rem;
	color: #ca1515;
	padding-right: 0.5208rem;
}
.nxsbtn{
	height: 1.8229rem;
	display: flex;
	align-items: center;
	justify-content: center;
}
.nxsbtn label{
	margin: 0px 0.5208rem;
	width: 2.0833rem;
	height: 1.0417rem;
	background: url(../img/sxjtico2.png) no-repeat;
	background-size: cover;
	cursor: pointer;
}
.xsjtleft{
	transform: rotate(180deg);
}
.xsjtleft:hover{
	background: url(../img/xsjtico.png) no-repeat;
	transform: rotate(0deg);
}
.xsjtright:hover{
	background: url(../img/xsjtico.png) no-repeat;
	transform: rotate(180deg);
}

/**四个自信开始**/
.confident{
	background: url(../img/footprintbag.png) no-repeat;
	background-position: top;
	background-size: 100% auto;
	background-color: #fbfbfb;
}
.nzxview{
	margin: 0px auto;
	width: 64.0625rem;
	min-height: 31.25rem;
	margin-bottom: 5rem;
}
.nzxtitleview{
	display: flex;
	align-items: center;
	justify-content: center;
	height: 9.375rem;
	margin-top: 1.0417rem;
}
.nzxtitleview img{
	display: block;
}
.nzxtopbar{
	width: 100%;
	height: 5.2083rem;
	display: flex;
	align-items: flex-end;
	margin-top: -0.8854rem;
}
.zxbaritem{
	width: calc(100% / 4);
	height: 4.3229rem;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #6f070a;
	font-size: 1.1458rem;
	color: #c79697;
	cursor: pointer;
}
.zxbaritem:last-child{
	border-top-right-radius: 0.5208rem;
}
.zxbaritem:first-child{
	border-top-left-radius: 0.5208rem;
}
.zxactive{
	background: #c61218;
	height: 5.2083rem;
	font-size: 1.6667rem;
	color: #fff7c5;
	font-weight: bold;
	text-shadow: 0.1042rem 0.1042rem 0.1042rem grey;
	border-top-left-radius: 0.5208rem;
	border-top-right-radius: 0.5208rem;
}
.zxstrbox{
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 2.7083rem;
	background: #FFFFFF;
	border-bottom: 0.0521rem dashed #e1e1e1;
	font-size: 0.7292rem;
	color: #e1e1e1;
	box-sizing: border-box;
}
.zxstrbox div,.zxstrbox div label{
	display: flex;
	align-items: center;
}
.zxstrbox img{
	display: block;
	margin-right: 0.2604rem;
}
.zxstrbox div label{
	padding: 0px 0.78125rem;
}
.zxcontent{
	box-sizing: border-box;
	padding: 70px 140px;
	background: #FFFFFF;
	border-bottom-left-radius: 0.5208rem;
	border-bottom-right-radius: 0.5208rem;
}
/**四个自信结束**/

/**精神谱系开始**/
.toppx{
	background: url(../img/topimgbag.png) no-repeat;
	background-size: cover;
	width: 100%;
	height: 6.25rem;
	display: flex;
	align-items: center;
	font-size: 1.6667rem;
	font-weight: bold;
	box-sizing: border-box;
	padding: 0px 1.0417rem;
	color: #FFFFFF;
}
.jspxcontent{
	display: flex;
	align-items: flex-start;
}
.jspxleft{
	width: 15.625rem;
	height: 36.4583rem;
	background: #f3f3f3;
	box-sizing: border-box;
	padding: 1.0417rem;
}
.jspxleftitem{
	font-size: 0.8333rem;
	color: #999999;
	cursor: pointer;
	padding: 0.7813rem 0px;
	line-height: 1.3021rem;
	display: flex;
}
.jspxacc{
	color: #c00714;
	font-weight: bold;
}
.jspxacc span{
	background: #c00714;
	border-color: #c00714;
}
.jspxleftitem:hover span{
	background: #c00714;
	border-color: #c00714;
}
.jspxleftitem:hover{
	color: #c00714;
	font-weight: bold;
}
.jspxleftitem label{
	width: 1.3021rem;
	height: 1.3021rem;
	display: flex;
	align-items: center;
	justify-content: center;
}
.jspxleftitem label span{
	width: 0.3646rem;
	height: 0.3646rem;
	transform: rotate(45deg);
	box-sizing: border-box;
	border: 0.0521rem solid #999999;
}
.jspxleftitem div{
	width: calc(100% - 1.3021rem);
}
.jspxright{
	width: calc(100% - 15.625rem);
	background: #ffffff;
	min-height: 36.4583rem;
	padding: 2.3438rem;
	box-sizing: border-box;
}
.jspxtitle{
	width: 15.625rem;
	height: 1.6667rem;
	display: flex;
	align-items: center;
	background: url(../img/titlebagpx.png) no-repeat;
	background-size: 100% 100%;
	background-color: #FFFFFF;
	font-size: 0.9375rem;
	font-weight: bold;
	color: #FFFFFF;
	box-sizing: border-box;
	padding-left: 0.2604rem;
}
.jspxtitle label{
	width: 0.3646rem;
	height: 0.3646rem;
	transform: rotate(45deg);
	background: #FFFFFF;
	margin: 0px 0.2604rem;
}

.jspxgroup{
	margin-top: 1.5625rem;
}

.jspxgrouptitle{
	display: flex;
	align-items: center;
	font-size: 0.9375rem;
	color: #666666;
}
.jspxgrouptitle label{
	background: #666666;
	width: 0.3125rem;
	height: 0.3125rem;
	border-radius: 52.0313rem;
	margin: 0px 0.2604rem;
}
.jspxgrouplist{
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}
.jspxgroupitem{
	width: calc((100% - 1.5625rem * 2) / 3);
	margin-right: 1.5625rem;
	margin-top: 0.7813rem;
	border-radius: 0.5208rem;
	overflow: hidden;
	position: relative;
	cursor: pointer;
}
.jspxgitembar{
	position: absolute;
	z-index: 9;
	bottom: 0;
	left: 0;
	right: 0;
	box-sizing: border-box;
	padding: 0.7813rem;
	background-image: linear-gradient(rgba(0,0,0,0),rgba(0,0,0,0.5),rgba(0,0,0,0.8));
}
.jspxgroupitem:nth-child(3n){
	margin-right: 0;
}
.jspxgroupitem img{
	width: 100%;
}
.jspxgitemtitle{
	font-size: 0.9375rem;
	color: #FFFFFF;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.jspxgitemstr{
	font-size: 0.8333rem;
	color: #aaaaaa;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	-webkit-box-orient: vertical;
	display: -moz-box;
	-moz-line-clamp: 2;
	-moz-box-orient: vertical;
	word-wrap: break-word;
	word-break: break-all;
	line-height: 1.0417rem;
	height: 2.0833rem;
	margin-top: 0.2604rem;
}
/**精神谱系结束**/

/**精神谱系详情**/
.jspxinfotopview{
	background: #FFFFFF;
}
.jspxinfotopview div{
	width: 64.0625rem;
	margin: 0px auto;
	height: 3.3333rem;
	display: flex;
	align-items: center;
	color: #333333;
	font-size: 0.8333rem;
}
.jspxinfotopview div a{
	color: #cecece;
	font-size: 0.8333rem;
}
.jspxinfotopview label{
	padding: 0px 0.5208rem;
	color: #cecece;
}
.jspxinfocontent{
	background: #FFFFFF;
	box-sizing: border-box;
	padding: 0px 8.8542rem;
	padding-top: 2.0833rem;
	padding-bottom: 3.125rem;
}