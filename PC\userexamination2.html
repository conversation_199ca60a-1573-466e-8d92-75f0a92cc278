<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-考试管理</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/nav-enhancement.js" type="text/javascript" charset="utf-8"></script>
		<style>
			/* 新增样式 */
			.zjbtn {
				background: linear-gradient(135deg, #ff8c00, #ff7043) !important;
				border-radius: 8px;
				
				transition: all 0.3s ease;
				box-shadow: 0 4px 12px rgba(255, 140, 0, 0.3);
				border: none;
				color: white;
				font-weight: 500;
				padding: 12px 20px;
				display: flex;
				align-items: center;
				gap: 8px;
				cursor: pointer;
			}
			
			.zjbtn:hover {
				background: linear-gradient(135deg, #ff7043, #ff5722) !important;
				transform: translateY(-2px);
				box-shadow: 0 6px 16px rgba(255, 140, 0, 0.4);
			}
			
			.zjbtn img {
				width: 16px;
				height: 16px;
			}
			
			/* 美化试卷列表卡片 */
			.paper {
				background: white;
				border-radius: 8px;
				box-shadow: 0 1px 4px rgba(0, 0, 0, 0.06);
				transition: all 0.3s ease;
				border: 1px solid #f0f0f0;
				overflow: hidden;
				margin-bottom: 0;
			}
			
			.paper:hover {
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
				transform: translateY(-1px);
				border-color: #ff8c00;
			}
			
			/* 单行布局 - 顶部和底部信息合并 */
			.papertop {
				padding: 16px 20px;
				background: white;
				border-bottom: none;
				display: flex;
				align-items: center;
				justify-content: space-between;
				gap: 20px;
			}
			
			.papername {
				display: flex;
				align-items: center;
				gap: 12px;
				margin-bottom: 0;
				flex: 1;
			}
			
			.papername > div {
				font-size: 15px;
				font-weight: 600;
				color: #2c3e50;
				flex: 1;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				max-width: 200px;
			}
			
			/* 试卷信息紧凑显示 */
			.paper-info-compact {
				display: flex;
				align-items: center;
				gap: 16px;
				font-size: 12px;
				color: #6c757d;
				flex-wrap: wrap;
			}
			
			.paper-info-compact > div {
				display: flex;
				align-items: center;
				gap: 4px;
				white-space: nowrap;
			}
			
			.paper-info-compact img {
				width: 12px;
				height: 12px;
				opacity: 0.7;
			}
			
			/* 移除原来的 paperbottom，信息整合到 papertop */
			.paperbottom {
				display: none;
			}
			
			/* 状态标签美化 - 更小更精致 */
			.papername label {
				padding: 3px 8px;
				border-radius: 12px;
				font-size: 11px;
				font-weight: 500;
				color: white;
				text-align: center;
				min-width: 40px;
			}
			
			.papername .defaule {
				background: linear-gradient(135deg, #6c757d, #5a6268);
			}
			
			.papername .open {
				background: linear-gradient(135deg, #28a745, #20c997);
			}
			
			.papername .closes {
				background: linear-gradient(135deg, #dc3545, #c82333);
			}
			
			/* 按钮组优化 - 更紧凑 */
			.paperbtn {
				display: flex;
				gap: 6px;
				align-items: center;
				flex-shrink: 0;
			}
			
			.paperbtn label {
				padding: 4px 10px;
				border-radius: 4px;
				font-size: 11px;
				font-weight: 500;
				cursor: pointer;
				transition: all 0.2s ease;
				border: none;
				color: white;
				text-align: center;
				min-width: 40px;
				white-space: nowrap;
			}
			
			.paperbtn .yl {
				background: linear-gradient(135deg, #17a2b8, #138496);
			}
			
			.paperbtn .yl:hover {
				background: linear-gradient(135deg, #138496, #117a8b);
				transform: translateY(-1px);
			}
			
			.paperbtn .xg {
				background: linear-gradient(135deg, #ffc107, #e0a800);
			}
			
			.paperbtn .xg:hover {
				background: linear-gradient(135deg, #e0a800, #d39e00);
				transform: translateY(-1px);
			}
			
			.paperbtn .fz {
				background: linear-gradient(135deg, #6f42c1, #5a32a3);
			}
			
			.paperbtn .fz:hover {
				background: linear-gradient(135deg, #5a32a3, #4e2a8e);
				transform: translateY(-1px);
			}
			
			.paperbtn .sc {
				background: linear-gradient(135deg, #dc3545, #c82333);
			}
			
			.paperbtn .sc:hover {
				background: linear-gradient(135deg, #c82333, #bd2130);
				transform: translateY(-1px);
			}
			
			/* 美化筛选区域 */
			.paperselectview {
				display: flex;
				gap: 16px;
				margin-bottom: 20px;
				padding: 20px;
				background: linear-gradient(135deg, #f8f9fa, #ffffff);
				border-radius: 12px;
				border: 1px solid #e9ecef;
			}
			
			.paperselectview select {
				flex: 1;
				padding: 10px 16px;
				border: 1px solid #ddd;
				border-radius: 8px;
				background: white;
				font-size: 14px;
				color: #495057;
				transition: all 0.2s ease;
				cursor: pointer;
			}
			
			.paperselectview select:focus {
				outline: none;
				border-color: #ff8c00;
				box-shadow: 0 0 0 3px rgba(255, 140, 0, 0.1);
			}
			
			.paperselectview select:hover {
				border-color: #ff8c00;
			}
			
			/* 美化表单输入框 */
			.addpaperbox input, .addpaperbox select,
			.editboxsss input, .editboxsss select {
				width: 100%;
				padding: 12px 16px;
				border: 1px solid #e9ecef;
				border-radius: 8px;
				background: white;
				font-size: 14px;
				color: #495057;
				transition: all 0.2s ease;
				box-sizing: border-box;
			}
			
			.addpaperbox input:focus, .addpaperbox select:focus,
			.editboxsss input:focus, .editboxsss select:focus {
				outline: none;
				border-color: #ff8c00;
				box-shadow: 0 0 0 3px rgba(255, 140, 0, 0.1);
				background: #fff;
			}
			
			.addpaperbox input::placeholder {
				color: #adb5bd;
			}
			
			/* 美化按钮组 */
			.bbtn {
				display: flex;
				gap: 12px;
				justify-content: center;
			}
			
			.bbtn .bc, .bbtn .gb {
				padding: 12px 24px;
				border-radius: 8px;
				font-size: 14px;
				font-weight: 500;
				cursor: pointer;
				transition: all 0.2s ease;
				border: none;
				min-width: 80px;
				text-align: center;
			}
			
			.bbtn .bc {
				background: linear-gradient(135deg, #28a745, #20c997);
				color: white;
			}
			
			.bbtn .bc:hover {
				background: linear-gradient(135deg, #20c997, #17a085);
				transform: translateY(-1px);
				box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
			}
			
			.bbtn .gb {
				background: linear-gradient(135deg, #6c757d, #5a6268);
				color: white;
			}
			
			.bbtn .gb:hover {
				background: linear-gradient(135deg, #5a6268, #495057);
				transform: translateY(-1px);
				box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
			}
			
			/* 美化分页组件 */
			.fybox {
				display: flex;
				justify-content: center;
				align-items: center;
				gap: 4px;
				margin-top: 12px;
				padding: 8px 0;
			}
			
			.fybox span, .fybox .num label {
				padding: 6px 10px;
				border-radius: 3px;
				border: 1px solid #e9ecef;
				background: white;
				color: #495057;
				cursor: pointer;
				transition: all 0.2s ease;
				font-size: 13px;
				
				min-width: 32px;
				text-align: center;
				user-select: none;
			}
			
			.fybox span:hover, .fybox .num label:hover {
				border-color: #ff8c00;
				background: #fff5f0;
				color: #ff8c00;
			}
			
			.fybox .actinum {
				background: linear-gradient(135deg, #ff8c00, #ff7043) !important;
				color: white !important;
				border-color: #ff8c00 !important;
			}
			
			/* 美化页面顶部标题 */
			.papaerboxtopview {
				background: linear-gradient(135deg, #ffffff, #f8f9fa);
				padding: 16px 20px;
				border-radius: 8px;
				margin-bottom: 16px;
				border: 1px solid #e9ecef;
				font-size: 16px;
				font-weight: 600;
				color: #2c3e50;
				text-align: center;
			}
			
			/* 美化新建试卷按钮 */
			.xjsjview > div {
				background: linear-gradient(135deg, #ff8c00, #ff7043);
				color: white;
				padding: 6px 12px; /* 减小内边距 */
				border-radius: 4px;
				text-align: center;
				font-size: 12px; /* 减小字体 */
				font-weight: 500;
				cursor: pointer;
				transition: all 0.2s ease;
				border: none;
				white-space: nowrap;
				box-shadow: 0 1px 3px rgba(255, 140, 0, 0.3);
			}
			
			.xjsjview > div:hover {
				background: linear-gradient(135deg, #ff7043, #ff5722);
				transform: translateY(-1px);
				box-shadow: 0 2px 6px rgba(255, 140, 0, 0.4);
			}
			
			/* 美化单选按钮组 */
			.bradio {
				display: flex;
				gap: 16px;
				justify-content: center;
				margin-bottom: 20px;
			}
			
			.radioview {
				display: flex;
				align-items: center;
				gap: 8px;
				padding: 8px 16px;
				border-radius: 8px;
				border: 1px solid #e9ecef;
				background: white;
				cursor: pointer;
				transition: all 0.2s ease;
				font-size: 14px;
				color: #495057;
			}
			
			.radioview:hover {
				border-color: #ff8c00;
				background: #fff5f0;
			}
			
			.radioview label {
				width: 16px;
				height: 16px;
				border: 2px solid #ddd;
				border-radius: 50%;
				background: white;
				position: relative;
				transition: all 0.2s ease;
			}
			
			.radioview label.radioacc {
				border-color: #ff8c00;
				background: #ff8c00;
			}
			
			.radioview label.radioacc::after {
				content: '';
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				width: 6px;
				height: 6px;
				border-radius: 50%;
				background: white;
			}
			
			/* 美化组卷状态显示 */
			.zjstr {
				display: flex;
				align-items: center;
				gap: 12px;
				flex-wrap: wrap;
				padding: 16px;
				background: linear-gradient(135deg, #f8f9fa, #ffffff);
				border-radius: 8px;
				border: 1px solid #e9ecef;
				margin-left: 16px;
				flex: 1;
			}
			
			.zjstr span {
				color: #6c757d;
				font-size: 14px;
			}
			
			.zjstr label {
				color: #ff8c00;
				font-weight: 600;
				font-size: 14px;
			}
			
			#zchengbox label, #zchengboxedit label {
				background: linear-gradient(135deg, #e9ecef, #f8f9fa);
				color: #495057;
				padding: 4px 8px;
				border-radius: 4px;
				font-size: 12px;
				margin-right: 8px;
				border: 1px solid #dee2e6;
			}
			
			/* 美化无数据状态 */
			.nodata {
				text-align: center;
				padding: 60px 20px;
				color: #6c757d;
				font-size: 16px;
				background: linear-gradient(135deg, #f8f9fa, #ffffff);
				border-radius: 12px;
				border: 1px solid #e9ecef;
			}
			
			/* 响应式优化 */
			@media (max-width: 768px) {
				.paperselectview {
					flex-direction: column;
					gap: 12px;
				}
				
				.paperbtn {
					flex-wrap: wrap;
					gap: 6px;
				}
				
				.paperbtn label {
					font-size: 11px;
					padding: 4px 8px;
					min-width: 40px;
				}
				
				.fybox {
					gap: 4px;
				}
				
				.fybox span, .fybox .num label {
					padding: 6px 8px;
					font-size: 12px;
					min-width: 32px;
				}
				
				.bradio {
					flex-direction: column;
					gap: 8px;
				}
			}
			#tmbox{
				margin-top: -6rem !important;
				overflow: hidden !important;
			}
			
			/* 美化试题组卷弹窗 */
			#tmbox .tmborderview {
				border-radius: 8px;
				box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
				max-width: 95vw;
				width: 1200px;
			}
			
			#tmbox .tmboxcontent {
				border-radius: 8px;
				max-height: 85vh;
				overflow: hidden;
				display: flex;
				flex-direction: column;
				background-color: #fff;
			}
			
			#tmListView, #tmDetailView {
				flex-grow: 1;
				display: flex;
				flex-direction: column;
				overflow: hidden;
			}

			#tmbox .sjtitle {
				background: linear-gradient(135deg, #e74c3c, #c0392b);
				color: white;
				border-radius: 8px 8px 0 0;
				padding: 16px 24px;
				font-size: 18px;
				font-weight: 600;
				position: relative;
			}
			
			/* 统计信息区域 */
			.tm-stats-area {
				background: linear-gradient(135deg, #f8f9fa, #e9ecef);
				border-bottom: 1px solid #e0e0e0;
				padding: 8px 12px; /* 减少内边距 */
				display: flex;
				flex-wrap: wrap;
				gap: 12px; /* 减少间距 */
				justify-content: space-around;
			}
			
			.stat-item {
				display: flex;
				flex-direction: column;
				align-items: center;
				min-width: 60px; /* 减少最小宽度 */
			}
			
			.stat-label {
				font-size: 11px; /* 减小字体 */
				color: #6c757d;
				margin-bottom: 2px; /* 减少间距 */
			}
			
			.stat-value {
				font-size: 14px; /* 减小字体 */
				font-weight: 600;
				color: #495057;
			}
			
			/* 筛选区域 */
			.tm-filter-area {
				background-color: #ffffff;
				border-bottom: 1px solid #e0e0e0;
				padding: 12px 16px; /* 减少内边距 */
			}
			
			.filter-main {
				display: flex;
				gap: 12px; /* 减少间距 */
				margin-bottom: 10px; /* 减少间距 */
				flex-wrap: wrap;
			}
			
			.filter-group {
				display: flex;
				align-items: center;
				gap: 6px; /* 减少间距 */
				flex: 1;
				min-width: 180px; /* 减少最小宽度 */
			}
			
			.filter-group label {
				font-size: 13px; /* 减小字体 */
				color: #495057;
				font-weight: 500;
				white-space: nowrap;
			}
			
			.filter-group select {
				flex: 1;
				padding: 6px 10px; /* 减少内边距 */
				border: 1px solid #ced4da;
				border-radius: 4px; /* 减小圆角 */
				font-size: 13px; /* 减小字体 */
				background-color: #fff;
				transition: all 0.2s;
			}
			
			.filter-group select:focus {
				border-color: #ff8c00;
				outline: none;
				box-shadow: 0 0 0 2px rgba(255, 140, 0, 0.2);
			}
			
			.filter-search {
				margin-bottom: 10px; /* 减少间距 */
				position: relative;
			}
			
			.filter-search input {
				width: 100%;
				padding: 8px 35px 8px 12px; /* 减少内边距 */
				border: 1px solid #ced4da;
				border-radius: 4px; /* 减小圆角 */
				font-size: 13px; /* 减小字体 */
				transition: all 0.2s;
			}
			
			.filter-search input:focus {
				border-color: #ff8c00;
				outline: none;
				box-shadow: 0 0 0 2px rgba(255, 140, 0, 0.2);
			}
			
			.search-icon {
				position: absolute;
				right: 10px;
				top: 50%;
				transform: translateY(-50%);
				color: #6c757d;
				cursor: pointer;
			}
			
			.filter-tags {
				display: flex;
				gap: 6px; /* 减少间距 */
				flex-wrap: wrap;
			}
			
			.filter-tag {
				padding: 4px 10px; /* 减少内边距 */
				background-color: #f8f9fa;
				border: 1px solid #e9ecef;
				border-radius: 15px; /* 减小圆角 */
				font-size: 11px; /* 减小字体 */
				color: #495057;
				cursor: pointer;
				transition: all 0.2s;
				white-space: nowrap;
			}
			
			.filter-tag:hover, .filter-tag.active {
				background-color: #ff8c00;
				color: white;
				border-color: #ff8c00;
				transform: translateY(-1px);
			}
			
			/* 试题列表区域 */
			.tm-list-area {
				flex: 1;
				display: flex;
				flex-direction: column;
				overflow: hidden;
				margin: 0 12px; /* 减少左右边距 */
			}
			
			.tm-list-header {
				display: flex;
				background: linear-gradient(135deg, #f1f3f4, #e8eaed);
				border: 1px solid #dadce0;
				border-radius: 4px 4px 0 0; /* 减小圆角 */
				padding: 8px 12px; /* 减少内边距 */
				font-weight: 600;
				color: #5f6368;
				font-size: 12px; /* 减小字体 */
				white-space: nowrap;
				align-items: center;
			}
			
			.tm-list-content {
				flex: 1;
				overflow-y: auto;
				border: 1px solid #dadce0;
				border-top: none;
				border-radius: 0 0 4px 4px; /* 减小圆角 */
				background-color: #fff;
			}
			
			/* 确保表头和列表项的列宽完全一致 */
			.tm-list-header > div,
			.tm-item > div {
				box-sizing: border-box;
			}
			
			/* 表头列样式 - 调整为占满容器宽度 */
			.tm-list-header .col-check { 
				flex: 0 0 50px; /* 减少宽度 */
				display: flex; 
				align-items: center; 
				gap: 4px; /* 减少间距 */
				font-size: 12px;
				justify-content: center;
			}
			.tm-list-header .col-title { 
				flex: 1;
				font-size: 12px;
				text-align: left;
				padding-right: 6px; /* 减少内边距 */
				min-width: 180px; /* 减少最小宽度 */
			}
			.tm-list-header .col-type { 
				flex: 0 0 70px; /* 减少宽度 */
				text-align: center; 
				font-size: 12px;
			}
			.tm-list-header .col-subject { 
				flex: 0 0 100px; /* 减少宽度 */
				text-align: center; 
				font-size: 12px;
				padding: 0 3px; /* 减少内边距 */
			}
			.tm-list-header .col-score { 
				flex: 0 0 50px; /* 减少宽度 */
				text-align: center; 
				font-size: 12px;
			}
			.tm-list-header .col-action { 
				flex: 0 0 60px; /* 减少宽度 */
				text-align: center; 
				font-size: 12px;
			}
			
			/* 题目悬浮提示框 */
			.col-title .title-tooltip {
				position: absolute;
				top: -10px;
				left: 0;
				transform: translateY(-100%);
				background-color: #333;
				color: white;
				padding: 8px 12px;
				border-radius: 6px;
				font-size: 12px;
				line-height: 1.4;
				max-width: 400px;
				word-wrap: break-word;
				box-shadow: 0 4px 12px rgba(0,0,0,0.15);
				z-index: 1000;
				opacity: 0;
				visibility: hidden;
				transition: opacity 0.2s, visibility 0.2s;
				pointer-events: none;
			}
			
			.col-title .title-tooltip.show {
				opacity: 1;
				visibility: visible;
			}
			
			.col-title .title-tooltip::after {
				content: '';
				position: absolute;
				top: 100%;
				left: 20px;
				border: 5px solid transparent;
				border-top-color: #333;
			}
			
			/* 当题目太长时的提示 */
			.col-title .title-tooltip .view-detail-hint {
				display: block;
				margin-top: 6px;
				padding-top: 6px;
				border-top: 1px solid #555;
				font-size: 11px;
				color: #ccc;
				font-style: italic;
			}
			
			/* 占位符样式 */
			.tm-placeholder {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				padding: 30px 15px; /* 减少内边距 */
				text-align: center;
				color: #6c757d;
			}
			
			.placeholder-icon {
				font-size: 36px; /* 减小图标大小 */
				margin-bottom: 12px; /* 减少间距 */
				opacity: 0.6;
			}
			
			.placeholder-text {
				font-size: 14px; /* 减小字体 */
				font-weight: 500;
				margin-bottom: 6px; /* 减少间距 */
				color: #495057;
			}
			
			.placeholder-hint {
				font-size: 13px; /* 减小字体 */
				color: #6c757d;
			}
			
			/* 试题列表项 */
			.tm-item {
				display: flex;
				align-items: center;
				padding: 6px 12px; /* 减少内边距 */
				border-bottom: 1px solid #f0f0f0;
				transition: background-color 0.2s;
				min-height: 32px; /* 减少最小高度 */
				font-size: 12px; /* 减小字体 */
			}
			
			.tm-item:hover {
				background-color: #f8f9fa;
			}
			
			.tm-item:last-child {
				border-bottom: none;
			}
			
			/* 为各列在tm-item中添加统一样式 */
			.tm-item .col-check {
				flex: 0 0 50px; /* 与表头一致 */
				display: flex;
				align-items: center;
				justify-content: center;
				gap: 4px;
			}
			
			.tm-item .col-title {
				flex: 1;
				font-size: 12px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				position: relative;
				cursor: pointer;
				line-height: 1.3; /* 减少行高 */
				padding-right: 6px;
				text-align: left;
				min-width: 180px;
			}
			
			.tm-item .col-type,
			.tm-item .col-subject,
			.tm-item .col-score,
			.tm-item .col-action {
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 12px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
			
			.tm-item .col-type {
				flex: 0 0 70px;
			}
			
			.tm-item .col-subject {
				flex: 0 0 100px;
				padding: 0 3px;
			}
			
			.tm-item .col-score {
				flex: 0 0 50px;
			}
			
			.tm-item .col-action {
				flex: 0 0 60px;
			}
			
			/* 底部按钮 */
			.tm-actions {
				padding: 12px 16px; /* 减少内边距 */
				display: flex;
				justify-content: center;
				gap: 12px; /* 减少间距 */
				border-top: 1px solid #e0e0e0;
				background-color: #f8f9fa;
			}
			
			.btn-save, .btn-cancel, .btn-back {
				padding: 8px 20px; /* 减少内边距 */
				border: none;
				border-radius: 4px; /* 减小圆角 */
				font-size: 13px; /* 减小字体 */
				font-weight: 500;
				cursor: pointer;
				transition: all 0.2s;
			}
			
			.btn-save {
				background-color: #28a745;
				color: white;
			}
			
			.btn-save:hover {
				background-color: #218838;
				transform: translateY(-1px);
			}
			
			.btn-cancel, .btn-back {
				background-color: #dc3545;
				color: white;
			}
			
			.btn-cancel:hover, .btn-back:hover {
				background-color: #c82333;
				transform: translateY(-1px);
			}
			
			.detail-btn {
				background-color: #17a2b8;
				color: white;
				padding: 3px 6px; /* 减小按钮内边距 */
				border-radius: 3px; /* 减小圆角 */
				cursor: pointer;
				font-size: 11px; /* 减小字体 */
				border: none;
				transition: background-color 0.2s;
				white-space: nowrap;
				min-width: 36px; /* 减少最小宽度 */
			}
			
			.detail-btn:hover {
				background-color: #138496;
			}
			
			/* 试题详情视图 */
			#tmDetailView {
				padding: 20px;
				flex-direction: column;
			}
			
			.detail-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 20px;
				padding-bottom: 16px;
				border-bottom: 2px solid #e9ecef;
			}
			
			.detail-header h3 {
				margin: 0;
				color: #c0392b;
				font-size: 20px;
			}
			
			.detail-content {
				flex: 1;
				overflow-y: auto;
				background-color: #f8f9fa;
				border-radius: 8px;
				padding: 20px;
			}
			
			.detail-row {
				margin-bottom: 16px;
				display: flex;
				gap: 12px;
			}
			
			.detail-row label {
				font-weight: 600;
				color: #495057;
				min-width: 80px;
			}
			
			.detail-row span {
				flex: 1;
				color: #212529;
				line-height: 1.5;
			}
			
			.detail-options {
				flex: 1;
			}
			
			.detail-options p {
				margin: 4px 0;
				padding: 4px 8px;
				background-color: #e9ecef;
				border-radius: 4px;
			}
			
			/* 复选框样式 */
			.chenckboxall, .chenckbox {
				width: 12px; /* 减小复选框尺寸 */
				height: 12px;
				border: 2px solid #ced4da;
				border-radius: 2px; /* 减小圆角 */
				background-color: white;
				cursor: pointer;
				position: relative;
				transition: all 0.2s;
				flex-shrink: 0;
			}
			
			.chenckboxall.ac, .chenckbox.ac {
				background-color: #ff8c00;
				border-color: #ff8c00;
			}
			
			.chenckboxall.ac::after, .chenckbox.ac::after {
				content: '✓';
				position: absolute;
				top: -4px; /* 调整勾号位置 */
				left: -1px;
				color: white;
				font-size: 9px; /* 调整勾号大小 */
				font-weight: bold;
				line-height: 1;
			}
			
			/* 章节显示效果 */
			.tmzj {
				position: relative;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				max-width: 100px; /* 调整章节列最大宽度 */
				min-width: 70px;
			}
			
			.tmzj:hover::after {
				content: attr(data-full);
				position: absolute;
				left: 50%;
				transform: translateX(-50%);
				top: -35px; /* 调整提示位置 */
				background-color: #333;
				color: white;
				padding: 5px 10px;
				border-radius: 4px;
				z-index: 1000; /* 确保在最上层 */
				white-space: normal;
				min-width: 180px;
				box-shadow: 0 2px 5px rgba(0,0,0,0.2);
				font-size: 12px;
			}
			
			.tmlx {
				min-width: 70px; /* 调整类型列最小宽度 */
				white-space: nowrap;
			}
			
			.tmxk {
				min-width: 90px; /* 调整学科列最小宽度 */
				max-width: 130px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
			
			.fnz {
				min-width: 40px;
			}
			
			/* 题型快速筛选标签 */
			.quick-filter {
				display: flex;
				flex-wrap: wrap;
				margin: 10px 10px 5px 10px; /* 调整margin */
				gap: 8px; /* 调整间距 */
			}
			
			.filter-tag {
				padding: 6px 12px; /* 调整padding */
				background-color: #f8f9fa;
				border: 1px solid #ddd;
				border-radius: 20px;
				cursor: pointer;
				transition: all 0.3s;
				white-space: nowrap;
				font-size: 13px;
			}
			
			.filter-tag:hover, .filter-tag.active {
				background-color: #ff8c00;
				color: white;
				border-color: #ff8c00;
			}
			
			/* 搜索框 */
			.search-box {
				display: flex;
				margin: 10px;
				position: relative;
			}
			
			.search-box input {
				flex: 1;
				padding: 8px 15px;
				padding-right: 30px; /* 为搜索图标留出空间 */
				border: 1px solid #ddd;
				border-radius: 4px;
				outline: none;
				font-size: 14px;
			}
			
			.search-box input:focus {
				border-color: #ff8c00;
				box-shadow: 0 0 5px rgba(255, 140, 0, 0.3);
			}
			
			.search-icon {
				position: absolute;
				right: 10px;
				top: 50%;
				transform: translateY(-50%);
				color: #999;
				cursor: pointer;
			}
			
			/* 美化表单 */
			.addpaperbox .inputviewa, .addpaperbox .inputviewb {
				margin-bottom: 15px;
			}
			
			.addpaperbox input, .addpaperbox select {
				border: 1px solid #ddd;
				border-radius: 4px;
				padding: 8px 10px;
				transition: all 0.3s;
			}
			
			.addpaperbox input:focus, .addpaperbox select:focus {
				border-color: #ff8c00;
				box-shadow: 0 0 5px rgba(255, 140, 0, 0.3);
				outline: none;
			}
			
			.editboxsss input, .editboxsss select {
				border: 1px solid #ddd;
				border-radius: 4px;
				padding: 8px 10px;
				transition: all 0.3s;
			}
			
			.editboxsss input:focus, .editboxsss select:focus {
				border-color: #ff8c00;
				box-shadow: 0 0 5px rgba(255, 140, 0, 0.3);
				outline: none;
			}
			
			/* 题目详情页样式 */
			.tm-detail-content p {
				margin-bottom: 10px;
				line-height: 1.6;
				font-size: 14px;
			}
			.tm-detail-content p strong {
				color: #555;
				margin-right: 5px;
			}
			#detailTmOptions p {
				margin-bottom: 5px;
			}

			/* 全选按钮 */
			.checkboxview {
				display: flex;
				align-items: center;
				min-width: 80px;
				justify-content: center;
			}
			.checkboxview label {
				margin-right: 5px;
			}
			
			/* 重新组织页面上半部分布局 */
			.paperscroll {
				display: flex;
				flex-direction: column;
				gap: 6px; /* 减少间距 */
			}
			
			/* 顶部控制栏 - 新建试卷和筛选在一行 */
			.top-control-bar {
				display: flex;
				align-items: center;
				gap: 20px;
				background: linear-gradient(135deg, #ffffff, #f8f9fa);
				padding: 20px;
				border-radius: 12px;
				border: 1px solid #e9ecef;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
			}
			
			/* 新建试卷按钮在左侧 */
			.xjsjview {
				flex-shrink: 0;
			}
			
			.xjsjview > div {
				background: linear-gradient(135deg, #ff8c00, #ff7043);
				color: white;
				padding: 6px 12px; /* 减小内边距 */
				border-radius: 4px;
				text-align: center;
				font-size: 12px; /* 减小字体 */
				font-weight: 500;
				cursor: pointer;
				transition: all 0.2s ease;
				border: none;
				white-space: nowrap;
				box-shadow: 0 1px 3px rgba(255, 140, 0, 0.3);
			}
			
			.xjsjview > div:hover {
				background: linear-gradient(135deg, #ff7043, #ff5722);
				transform: translateY(-1px);
				box-shadow: 0 2px 6px rgba(255, 140, 0, 0.4);
			}
			
			/* 筛选区域在右侧 */
			.paperselectview {
				display: flex;
				gap: 16px;
				margin-bottom: 0;
				padding: 0;
				background: none;
				border: none;
				flex: 1;
				justify-content: flex-end;
			}
			
			.paperselectview select {
				flex: 0 0 auto;
				min-width: 140px;
				padding: 10px 16px;
				border: 1px solid #ddd;
				border-radius: 8px;
				background: white;
				font-size: 14px;
				color: #495057;
				transition: all 0.2s ease;
				cursor: pointer;
			}
			
			.paperselectview select:focus {
				outline: none;
				border-color: #ff8c00;
				box-shadow: 0 0 0 3px rgba(255, 140, 0, 0.1);
			}
			
			.paperselectview select:hover {
				border-color: #ff8c00;
			}
			
			/* 美化页面顶部标题 */
			.papaerboxtopview {
				background: linear-gradient(135deg, #ffffff, #f8f9fa);
				padding: 16px 20px;
				border-radius: 8px;
				margin-bottom: 0;
				border: 1px solid #e9ecef;
				font-size: 16px;
				font-weight: 600;
				color: #2c3e50;
				text-align: center;
				order: -1; /* 让标题显示在最前面 */
			}
			
			/* 优化试卷列表显示 - 更紧凑的单行布局 */
			.paperlist {
				display: flex;
				flex-direction: column;
				gap: 12px;
			}
			
			/* 美化预览弹窗样式 - 与页面风格一致 */
			#tmbox2 .tmborderview {
				border-radius: 12px;
				box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
				max-width: 90vw;
				width: 1000px;
				background: white;
			}
			
			#tmbox2 .tmboxcontent {
				border-radius: 12px;
				max-height: 85vh;
				overflow: hidden;
				display: flex;
				flex-direction: column;
				background-color: #fff;
			}
			
			#tmbox2 .sjtitle {
				background: linear-gradient(135deg, #ff8c00, #ff7043);
				color: white;
				border-radius: 12px 12px 0 0;
				padding: 20px 24px;
				font-size: 18px;
				font-weight: 600;
				position: relative;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}
			
			/* 预览内容区域 */
			#ylbox1 {
				flex: 1;
				overflow-y: auto;
				padding: 24px;
				background: #f8f9fa;
			}
			
			#ylbox2 {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				padding: 60px;
				background: #f8f9fa;
				color: #6c757d;
			}
			
			#ylbox2 img {
				width: 48px;
				height: 48px;
				margin-bottom: 16px;
			}
			
			/* 试卷信息展示区域 */
			.tmlxview.tmlxview2 {
				background: white;
				border-radius: 8px;
				padding: 20px;
				margin-bottom: 20px;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
				border: 1px solid #e9ecef;
			}
			
			.tmlxview2 > div {
				display: inline-block;
				margin-right: 24px;
				margin-bottom: 8px;
				font-size: 14px;
				color: #495057;
			}
			
			.tmlxview2 label {
				color: #ff8c00;
				font-weight: 600;
			}
			
			/* 试题滚动区域 */
			.tmscroll.tmscroll2 {
				background: white;
				border-radius: 8px;
				padding: 20px;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
				border: 1px solid #e9ecef;
				max-height: 60vh;
				overflow-y: auto;
			}
			
			/* 试题项样式 */
			.yltm {
				background: #f8f9fa;
				border-radius: 8px;
				padding: 16px;
				margin-bottom: 16px;
				border: 1px solid #e9ecef;
				transition: all 0.2s ease;
			}
			
			.yltm:hover {
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.08);
			}
			
			.yltm:last-child {
				margin-bottom: 0;
			}
			
			.ylname {
				font-size: 15px;
				font-weight: 600;
				color: #2c3e50;
				margin-bottom: 12px;
				line-height: 1.5;
			}
			
			.ylname label {
				color: #ff8c00;
				margin-right: 8px;
			}
			
			/* 题型标签 */
			.tmlxlabel {
				margin-bottom: 12px;
			}
			
			.tmlxlabel label {
				background: linear-gradient(135deg, #ff8c00, #ff7043);
				color: white;
				padding: 4px 12px;
				border-radius: 12px;
				font-size: 12px;
				font-weight: 500;
			}
			
			/* 选项区域 */
			.xuanxiang {
				margin-bottom: 12px;
			}
			
			.xxbooo {
				font-weight: 600;
				color: #495057;
				margin-bottom: 8px;
				font-size: 14px;
			}
			
			.ylxx {
				background: white;
				padding: 8px 12px;
				border-radius: 6px;
				margin-bottom: 4px;
				border: 1px solid #e9ecef;
				font-size: 14px;
				color: #495057;
			}
			
			.ylxx:last-child {
				margin-bottom: 0;
			}
			
			/* 答案区域 */
			.dayl {
				background: linear-gradient(135deg, #e8f5e8, #f0f8f0);
				border: 1px solid #d4edda;
				border-radius: 6px;
				padding: 8px 12px;
				font-size: 14px;
				color: #495057;
			}
			
			.dayl label {
				color: #28a745;
				font-weight: 600;
			}
			
			/* 关闭按钮样式 */
			.paperyueclose2 {
				background: rgba(255, 255, 255, 0.2);
				color: white;
				border: none;
				border-radius: 50%;
				width: 32px;
				height: 32px;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				transition: all 0.2s ease;
				font-size: 18px;
			}
			
			.paperyueclose2:hover {
				background: rgba(255, 255, 255, 0.3);
				transform: scale(1.1);
			}
			
			.paperyueclose2::before {
				content: '×';
				font-weight: bold;
			}
			
			/* 重新设计紧凑清爽的布局 */
			.paperscroll {
				display: flex;
				flex-direction: column;
				gap: 6px; /* 减少间距 */
			}
			
			/* 页面标题样式 */
			.papaerboxtopview {
				background: linear-gradient(135deg, #ffffff, #f8f9fa);
				padding: 12px 20px;
				border-radius: 6px;
				margin-bottom: 0;
				border: 1px solid #e9ecef;
				font-size: 16px;
				font-weight: 600;
				color: #2c3e50;
				text-align: left;
				box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
			}
			
			/* 筛选控制栏 - 紧凑设计 */
			.filter-control-bar {
				display: flex;
				align-items: center;
				justify-content: space-between;
				gap: 16px;
				background: white;
				padding: 12px 16px;
				border-radius: 6px;
				border: 1px solid #e9ecef;
				box-shadow: 0 1px 3px rgba(0, 0, 0, 0.04);
			}
			
			/* 新建按钮 - 更紧凑 */
			.xjsjview > div {
				background: linear-gradient(135deg, #ff8c00, #ff7043);
				color: white;
				padding: 6px 12px; /* 减小内边距 */
				border-radius: 4px;
				text-align: center;
				font-size: 12px; /* 减小字体 */
				font-weight: 500;
				cursor: pointer;
				transition: all 0.2s ease;
				border: none;
				white-space: nowrap;
				box-shadow: 0 1px 3px rgba(255, 140, 0, 0.3);
			}
			
			.xjsjview > div:hover {
				background: linear-gradient(135deg, #ff7043, #ff5722);
				transform: translateY(-1px);
				box-shadow: 0 2px 6px rgba(255, 140, 0, 0.4);
			}
			
			/* 筛选下拉框 */
			.filter-selects {
				display: flex;
				gap: 12px;
				align-items: center;
			}
			
			.filter-selects select {
				padding: 6px 12px;
				border: 1px solid #ddd;
				border-radius: 4px;
				background: white;
				font-size: 13px;
				color: #495057;
				cursor: pointer;
				min-width: 120px;
				transition: all 0.2s ease;
			}
			
			.filter-selects select:focus {
				outline: none;
				border-color: #ff8c00;
				box-shadow: 0 0 0 2px rgba(255, 140, 0, 0.1);
			}
			
			.filter-selects select:hover {
				border-color: #ff8c00;
			}
			
			/* 试卷表格样式 - 优化间距 */
			.paper-table-container {
				background: white;
				border-radius: 4px; /* 减小圆角 */
				border: 1px solid #e9ecef;
				box-shadow: 0 1px 2px rgba(0, 0, 0, 0.04);
				overflow: hidden;
			}
			
			.paper-table {
				width: 100%;
				border-collapse: collapse;
				font-size: 14px; /* 增大表格字体 */
				table-layout: fixed;
			}
			
			.paper-table thead {
				background: linear-gradient(135deg, #f8f9fa, #e9ecef);
			}
			
			.paper-table th {
				padding: 8px 10px; /* 增加内边距 */
				text-align: center; /* 统一居中对齐 */
				font-weight: 600;
				color: #495057;
				border-bottom: 1px solid #dee2e6; /* 减少边框厚度 */
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				font-size: 13px; /* 增大表头字体 */
			}
			
			/* 重新分配列宽 - 更紧凑 */
			.paper-table th:first-child { width: 40px; } /* 序号 */
			.paper-table th:nth-child(2) { width: 18%; min-width: 100px; } /* 试卷名称 */
			.paper-table th:nth-child(3) { width: 10%; min-width: 70px; } /* 学科 */
			.paper-table th:nth-child(4) { width: 22%; min-width: 140px; } /* 考试时间 */
			.paper-table th:nth-child(5) { width: 8%; min-width: 50px; } /* 时长 */
			.paper-table th:nth-child(6) { width: 8%; min-width: 40px; } /* 总分 */
			.paper-table th:nth-child(7) { width: 32%; min-width: 120px; } /* 操作 */
			
			.paper-table tbody tr {
				border-bottom: 1px solid #f0f0f0;
				transition: background-color 0.2s ease;
			}
			
			.paper-table tbody tr:hover {
				background-color: #f8f9fa;
			}
			
			.paper-table tbody tr:last-child {
				border-bottom: none;
			}
			
			.paper-table td {
				padding: 8px 10px; /* 增加内边距 */
				vertical-align: middle;
				color: #495057;
				border: none;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
				text-align: center;
				position: relative;
				font-size: 14px; /* 增大表格内容字体 */
			}
			
			.paper-table td:nth-child(2) {
				text-align: left; /* 试卷名称左对齐 */
			}
			
			/* 操作列允许换行 */
			.paper-table td:nth-child(7) {
				white-space: normal;
			}
			
			/* 试卷名称列 - 添加悬浮提示 */
			.paper-name {
				font-weight: 500;
				color: #2c3e50;
				cursor: pointer;
				position: relative;
			}
			
			.paper-name:hover::after {
				content: attr(data-full-name);
				position: absolute;
				top: -8px;
				left: 0;
				transform: translateY(-100%);
				background-color: #333;
				color: white;
				padding: 8px 12px;
				border-radius: 4px;
				font-size: 13px;
				white-space: normal;
				min-width: 200px;
				max-width: 400px;
				word-wrap: break-word;
				box-shadow: 0 2px 8px rgba(0,0,0,0.15);
				z-index: 1000;
				opacity: 1;
				visibility: visible;
				pointer-events: none;
			}
			
			/* 学科名称悬浮提示 */
			.subject-name {
				cursor: pointer;
				position: relative;
			}
			
			.subject-name:hover::after {
				content: attr(data-full-subject);
				position: absolute;
				top: -8px;
				left: 50%;
				transform: translate(-50%, -100%);
				background-color: #333;
				color: white;
				padding: 6px 10px;
				border-radius: 3px;
				font-size: 12px;
				white-space: nowrap;
				box-shadow: 0 2px 6px rgba(0,0,0,0.15);
				z-index: 1000;
				opacity: 1;
				visibility: visible;
				pointer-events: none;
			}
			
			/* 考试时间悬浮提示 */
			.exam-time {
				cursor: pointer;
				position: relative;
			}
			
			.exam-time:hover::after {
				content: attr(data-full-time);
				position: absolute;
				top: -8px;
				left: 50%;
				transform: translate(-50%, -100%);
				background-color: #333;
				color: white;
				padding: 6px 10px;
				border-radius: 3px;
				font-size: 12px;
				white-space: nowrap;
				box-shadow: 0 2px 6px rgba(0,0,0,0.15);
				z-index: 1000;
				opacity: 1;
				visibility: visible;
				pointer-events: none;
			}
			
			/* 操作按钮组 - 双行显示，更大按钮 */
			.action-buttons {
				display: flex;
				gap: 4px;
				justify-content: center;
				align-items: center;
				flex-wrap: wrap; /* 允许换行 */
				max-width: 100%;
			}
			
			.action-btn {
				padding: 6px 12px; /* 增大按钮内边距 */
				border-radius: 4px;
				font-size: 13px; /* 增大字体 */
				font-weight: 500;
				cursor: pointer;
				transition: all 0.2s ease;
				border: none;
				color: white;
				text-decoration: none;
				white-space: nowrap;
				min-width: 50px; /* 增大最小宽度 */
				text-align: center;
				margin: 1px; /* 添加小间距 */
			}
			
			.action-btn.preview {
				background: linear-gradient(135deg, #17a2b8, #138496);
			}
			
			.action-btn.edit {
				background: linear-gradient(135deg, #ffc107, #e0a800);
			}
			
			.action-btn.copy {
				background: linear-gradient(135deg, #6f42c1, #5a32a3);
			}
			
			.action-btn.delete {
				background: linear-gradient(135deg, #dc3545, #c82333);
			}
			
			.action-btn:hover {
				transform: translateY(-1px);
				filter: brightness(110%);
				box-shadow: 0 2px 4px rgba(0,0,0,0.2);
			}
			
			/* 无数据状态 */
			.no-data-row {
				text-align: center;
				padding: 30px 20px;
				color: #6c757d;
				font-size: 14px;
				background: #f8f9fa;
			}
			
			/* 预览弹窗中答案区域 - 提升答案可见度 */
			.dayl {
				background: transparent; /* 去除背景色 */
				border: none; /* 去除边框 */
				border-radius: 0;
				padding: 6px 0; /* 增加内边距 */
				font-size: 15px; /* 增大字体 */
				color: #495057;
				margin-top: 8px;
			}
			
			.dayl label {
				color: #dc3545; /* 改为更醒目的红色 */
				font-weight: 700; /* 加粗 */
				font-size: 15px;
			}
			
			/* 或者使用绿色但更深 */
			/* .dayl label {
				color: #155724;
				font-weight: 700;
				font-size: 15px;
			} */
			
			/* 修改试卷弹窗相关样式优化 */
			#tmbox3 {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(0, 0, 0, 0.5);
				z-index: 9999;
				display: none;
				align-items: center;
				justify-content: center;
			}
			
			#editbox1 {
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				min-height: 200px;
				text-align: center;
			}
			
			#editbox1 #eeeee {
				margin-bottom: 20px;
			}
			
			#editbox1 #loadimg2 {
				width: 50px;
				height: 50px;
				animation: spin 1s linear infinite;
			}
			
			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}
			
			#editbox2 {
				display: none;
			}
			
			#editbox2.show {
				display: block;
			}
			
			#editbox1.show {
				display: flex;
			}
			
			#editbox1.hide {
				display: none !important;
			}
			
			#editbox2.hide {
				display: none !important;
			}
			
			/* 简化试卷列表标题 - 去除容器样式 */
			.papaerboxtopview {
				font-size: 16px;
				font-weight: 600;
				color: #2c3e50;
				margin-bottom: 8px;
				padding: 0;
				background: none;
				border: none;
				box-shadow: none;
				text-align: left;
			}
			
			/* 整体内容区域优化 */
			.boxright {
				padding: 10px; /* 减少内边距 */
			}
			
			.userinfotopdiv {
				margin-bottom: 8px; /* 减少下边距 */
			}
			
			.userinfotopdiv a {
				padding: 6px 12px; /* 减少内边距 */
				font-size: 18px; /* 减小字体 */
			}
			
			/* 左侧菜单优化 */
			.leftitembox {
				padding: 20px 0;
				position: relative;
				z-index: 2;
				min-height: 450px; /* 添加固定容器最小高度，确保一致性 */
				box-sizing: border-box;
			}
			
			.leftitem {
				padding: 16px 24px; /* 修正为标准间距 */
				font-size: 15px; /* 修正为标准字体大小 */
				min-height: 48px; /* 添加统一的最小高度 */
				box-sizing: border-box; /* 确保盒模型一致 */
			}
			
			/* 进一步优化整体间距 */
			/* .contentview { */
				/* padding: 8px 0; 减少上下内边距 */
			/* } */
			
			.box {
				margin-top: 10px; /* 减少上边距 */
			}
			
			/* 响应式优化 - 更紧凑 */
			@media (max-width: 768px) {
				.filter-control-bar {
					flex-direction: column;
					align-items: stretch;
					gap: 6px; /* 减少间距 */
					padding: 4px 6px; /* 减少内边距 */
				}
				
				.filter-selects {
					justify-content: center;
					gap: 6px; /* 减少间距 */
				}
				
				.filter-selects select {
					min-width: 80px; /* 减少最小宽度 */
					font-size: 11px; /* 减小字体 */
					padding: 3px 6px; /* 减少内边距 */
				}
				
				.paper-table-container {
					overflow-x: auto;
				}
				
				.paper-table {
					min-width: 600px;
					font-size: 12px; /* 移动端稍微增大字体 */
				}
				
				.paper-table th,
				.paper-table td {
					padding: 6px 8px; /* 稍微增加内边距 */
					font-size: 12px; /* 移动端增大字体 */
				}
				
				.action-buttons {
					gap: 2px; /* 稍微增加间距 */
				}
				
				.action-btn {
					font-size: 11px; /* 移动端增大字体 */
					padding: 4px 6px; /* 稍微增加内边距 */
					min-width: 35px; /* 稍微增大最小宽度 */
				}
				
				.fybox span, .fybox .num label {
					padding: 3px 6px; /* 减少内边距 */
					font-size: 11px; /* 减小字体 */
					min-width: 24px; /* 减少最小宽度 */
				}
				
				.paperscroll {
					gap: 4px; /* 进一步减少间距 */
				}
				
				.boxright {
					padding: 6px; /* 减少内边距 */
				}
			}
			
			/* 现代化导航栏美化 - 与页面红色主题统一，红色偏橙色 */
			.boxleft {
				background: white;
				border-radius: 16px;
				box-shadow: 0 8px 32px rgba(220, 53, 69, 0.08);
				overflow: hidden;
				border: 1px solid rgba(231, 76, 60, 0.05);
				position: relative;
				backdrop-filter: blur(20px);
			}
			
			.boxleft::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8));
				pointer-events: none;
				border-radius: 16px;
			}
			
			/* 导航栏顶部标题区域 - 红色偏橙色渐变 */
			.lefttopview {
				width: 100%;
				height: 65px;
				background: linear-gradient(135deg, #dc3545, #e74c3c, #ff6b35);
				background-size: 300% 300%;
				animation: gradientShift 6s ease infinite;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 18px;
				color: #FFFFFF;
				font-weight: 700;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif;
				position: relative;
				box-shadow: 0 4px 20px rgba(220, 53, 69, 0.25);
				letter-spacing: 1.5px;
				border-radius: 16px 16px 0 0;
			}
			
			/* 渐变动画效果 */
			@keyframes gradientShift {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.lefttopview::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(45deg, transparent, rgba(255, 255, 255, 0.15), transparent);
				transform: translateX(-100%);
				transition: transform 0.8s ease;
			}
			
			.lefttopview:hover::before {
				transform: translateX(100%);
			}
			
			.lefttopview img {
				width: 24px;
				height: 24px;
				display: block;
				margin-right: 12px;
				filter: brightness(0) invert(1) drop-shadow(0 0 6px rgba(255, 255, 255, 0.4));
				transition: all 0.4s ease;
			}
			
			.lefttopview:hover img {
				transform: scale(1.15) rotate(8deg);
			}
			
			.lefttopview label {
				display: flex;
				align-items: center;
				text-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
				transition: all 0.4s ease;
			}
			
			.lefttopview:hover label {
				transform: scale(1.05);
			}
			
			/* 导航菜单容器 */
			.leftitembox {
				background: transparent;
				padding: 20px 0;
				position: relative;
				z-index: 2;
				min-height: 450px; /* 添加固定容器最小高度，确保一致性 */
				box-sizing: border-box;
			}
			
			.leftitembox::before {
				content: '';
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 85%;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.15), transparent);
			}
			
			/* 导航菜单项 - 现代化设计与图标 */
			.leftitem {
				height: auto;
				padding: 16px 24px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				font-size: 15px;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif;
				font-weight: 500;
				color: #4a5568;
				border-bottom: none;
				text-decoration: none;
				transition: color 0.2s ease, background-color 0.2s ease;
				position: relative;
				margin: 4px 16px;
				border-radius: 12px;
				background: transparent;
				overflow: hidden;
				min-height: 48px;
			}
			
			/* 为不同菜单项添加对应的SVG图标 */
			.leftitem::before {
				content: '';
				width: 20px;
				height: 20px;
				margin-right: 12px;
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				opacity: 0.7;
				transition: opacity 0.2s ease;
				flex-shrink: 0;
			}
			
			/* 个人信息图标 */
			.leftitem[href*="userinfo"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
			}
			
			/* 学生成绩统计图标 */
			.leftitem[href*="studentachievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z'/%3E%3C/svg%3E");
			}
			
			/* 学习记录统计图标 */
			.leftitem[href*="studentlearning"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z'/%3E%3C/svg%3E");
			}
			
			/* 考试管理图标 */
			.leftitem.activeleftitem::before,
			.leftitem[href*="examination"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E");
			}
			
			/* 学习任务管理图标 */
			.leftitem[href*="learning"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
			}
			
			/* 成果展示图标 */
			.leftitem[href*="achievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E");
			}
			
			/* 发布心声图标 */
			.leftitem[href*="voice"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z'/%3E%3C/svg%3E");
			}
			
			/* 菜单项悬浮态 - 完全无抖动 */
			.leftitem:hover {
				background: linear-gradient(135deg, rgba(220, 53, 69, 0.06), rgba(231, 76, 60, 0.08));
				color: #dc3545;
			}
			
			.leftitem:hover::before {
				opacity: 1;
			}
			
			/* 激活状态的菜单项 - 统一标准双色渐变背景 */
			.activeleftitem {
				font-weight: 700 !important;
				color: white !important;
				background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
				background-size: 200% 200%;
				animation: activeGradient 3s ease infinite;
				box-shadow: 0 4px 16px rgba(220, 53, 69, 0.25);
				border-radius: 12px;
			}
			
			@keyframes activeGradient {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.activeleftitem::before {
				opacity: 1 !important;
				filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
			}
			
			.activeleftitem::after {
				content: '';
				position: absolute;
				right: 16px;
				top: 50%;
				transform: translateY(-50%);
				width: 8px;
				height: 8px;
				background: white;
				border-radius: 50%;
				box-shadow: 0 0 12px rgba(255, 255, 255, 0.8);
				animation: activePulse 2s ease infinite;
			}
			
			@keyframes activePulse {
				0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
				50% { opacity: 0.6; transform: translateY(-50%) scale(1.4); }
			}
			
			/* 添加精美的分隔线效果 */
			.leftitem:not(:last-child):not(.activeleftitem) {
				border-bottom: 1px solid rgba(231, 76, 60, 0.06);
				margin-bottom: 3px;
			}
			
			.leftitem:not(:last-child):not(.activeleftitem)::after {
				content: '';
				position: absolute;
				bottom: -1px;
				left: 40px;
				right: 24px;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.15), transparent);
				opacity: 0.5;
			}
			
			/* 整体容器增强效果 */
			.contentview .boxleft {
				background: rgba(255, 255, 255, 0.95);
				border-radius: 16px;
				box-shadow: 0 8px 40px rgba(220, 53, 69, 0.08);
				border: 1px solid rgba(231, 76, 60, 0.05);
				overflow: hidden;
				position: relative;
				backdrop-filter: blur(20px);
			}
			
			.contentview .boxleft::before {
					content: '';
					position: absolute;
					top: 0;
					left: 0;
					right: 0;
					height: 3px;
					background: linear-gradient(90deg, #dc3545, #e74c3c, #ff6b35);
					opacity: 0;
					transition: opacity 0.4s ease;
				}
				
				.contentview .boxleft:hover::before {
					opacity: 1;
				}
				
				/* 响应式优化 */
				@media (max-width: 768px) {
					.boxleft {
						border-radius: 12px;
						margin-bottom: 20px;
					}
					
					.lefttopview {
						height: 55px;
						font-size: 16px;
						letter-spacing: 1px;
					}
					
					.lefttopview img {
						width: 20px;
						height: 20px;
						margin-right: 10px;
					}
					
					.leftitem {
						padding: 14px 20px;
						font-size: 14px;
						margin: 3px 12px;
						min-height: 44px;
					}
					
					.leftitem::before {
						width: 18px;
						height: 18px;
						margin-right: 10px;
					}
				}
				
				/* 字体优化 - 思源黑体 */
				.lefttopview,
				.leftitem {
					font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Source Han Sans', 'Noto Sans CJK SC', 'Microsoft YaHei UI', 'Microsoft YaHei', '微软雅黑', sans-serif;
				}
				
				/* 增强的交互反馈 */
				.leftitem {
					cursor: pointer;
				}
				
				.leftitem:active {
					transform: scale(0.98);
					transition: all 0.1s ease;
				}
				
				.activeleftitem:active {
					transform: scale(1.02);
				}
				
				/* 移除所有slideIn动画，导航项立即显示 */
				.leftitem {
					animation: none !important;
					opacity: 1 !important;
					transform: translateX(0) !important;
					animation-delay: 0s !important;
				}
				
				.leftitem:nth-child(1),
				.leftitem:nth-child(2),
				.leftitem:nth-child(3),
				.leftitem:nth-child(4),
				.leftitem:nth-child(5),
				.leftitem:nth-child(6),
				.leftitem:nth-child(7) { 
					animation: none !important;
					animation-delay: 0s !important;
					opacity: 1 !important;
					transform: translateX(0) !important;
				}
				
				/* 最终修复 - 确保导航间距与其他页面完全一致 */
				.leftitembox {
					min-height: 450px !important;
					padding: 20px 0 !important;
					box-sizing: border-box !important;
				}
				
				.leftitem {
					padding: 16px 24px !important;
					font-size: 15px !important;
					min-height: 48px !important;
					margin: 4px 16px !important;
					box-sizing: border-box !important;
					display: flex !important;
					align-items: center !important;
					/* 强制移除所有动画 */
					animation: none !important;
					animation-delay: 0s !important;
					opacity: 1 !important;
					transform: translateX(0) !important;
				}
		</style>
	</head>
	<body class="index">
		<div class="tcbox" id="tcbox">
			<div class="topviewsss">
				<a onclick="closetc()">关闭</a>
				<label id="pdfname"></label>
			</div>
			<div class="panel-body pdfbox">
				<a id="pdf" class="media" href=""></a>
			</div>
		</div>
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a href="studentachievement.html" class="leftitem">学生成绩统计</a>
						<a href="studentlearning.html" class="leftitem">学习记录统计</a>
						<a class="leftitem activeleftitem">考试管理</a>
						<a href="learningtasks.html" class="leftitem">学习任务管理</a>
						<a href="achievements.html" class="leftitem">成果展示</a>
						<a href="releaseyourvoice2.html" class="leftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="userinfotopdiv">
						<a class="acccccg">试卷组合</a>
						<a href="userexamination1.html">试卷审阅</a>
					</div>
					<div class="paperscroll">
						<!-- 页面标题 -->
						<div class="papaerboxtopview">
							已创建试卷列表
						</div>
						
						<!-- 筛选控制栏 -->
						<div class="filter-control-bar">
						<div id="pbox1" class="xjsjview">
							<div onclick="addpaper()">新建试卷</div>
						</div>
							<div class="filter-selects">
								<select id="xueke" onchange="xuekechange(this)">
								</select>
								<select id="years" onchange="yearschange(this)">
								</select>
							</div>
						</div>
						
						<!-- 新建试卷表单 -->
						<div id="pbox2" class="addpaperbox">
							<div class="inputviewa">
								<input id="addpapername" placeholder="请输入试卷名称" />
							</div>
							<div class="inputviewa">
								<input id="addpapersm" placeholder="请输入试卷说明" />
							</div>
							<div class="inputviewa">
								<select id="xueke2">
								</select>
							</div>
							<div class="inputviewb">
								<input id="papersj" type="number" placeholder="输入考试时间（分钟）" />
								<input id="paperzf" type="number" placeholder="输入总分" />
								<input id="paperfsx" type="number" placeholder="输入通过分数线" />
							</div>
							<div class="inputviewb">
								<input id="addstartdate" type="date" placeholder="开始时间" />
								<input id="addenddate" type="date" placeholder="结束时间" />
								<input id="paperqz" type="number" placeholder="请输入权重比" value="100" />
							</div>
							<div class="tmview">
								<div class="zjbtn" onclick="showaddpaper()">
									<img src="img/zj.png" />
									组卷
								</div>
								<div class="zjstr">
									<span>总分：</span>
									<label id="tmzf2">0</label>
									<label class="xt">|</label>
									<span>组成：</span>
									<div id="zchengbox">
									</div>
								</div>
							</div>
							<div class="bottomview">
								<div class="bradio">
									<div class="radioview" onclick="selectradio(this)" data-id="draft">
										<label class="radioacc"></label>
										默认
									</div>
									<div class="radioview" onclick="selectradio(this)" data-id="open">
										<label></label>
										开放
									</div>
									<div class="radioview" onclick="selectradio(this)" data-id="closed">
										<label></label>
										关闭
									</div>
								</div>
								<div class="bbtn">
									<div class="bc" onclick="addsubmit()">保存</div>
									<div class="gb" onclick="closeaddpaper()">取消</div>
								</div>
							</div>
						</div>
						
						<!-- 试卷表格 -->
						<div class="paper-table-container">
							<table class="paper-table">
								<thead>
									<tr>
										<th>序号</th>
										<th>试卷名称</th>
										<th>学科</th>
										<th>考试时间</th>
										<th>时长</th>
										<th>总分</th>
										<th>操作</th>
									</tr>
								</thead>
								<tbody id="paperbox">
									<!-- 表格内容将由JavaScript生成 -->
								</tbody>
							</table>
							</div>

						<!-- 分页 -->
							<div class="fybox" style="padding-top: 1.041666rem;" id="fyq">
								<span id="sy">首页</span>
								<span id="syy">上一页</span>
								<div class="num" id="num">
								</div>
								<span id="xyy">下一页</span>
								<span id="wy">尾页</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>

		<div id="tmbox">
			<div class="tmborderview">
				<div class="tmboxcontent">
					<div class="sjtitle">
						<div>试题组卷</div>
						<label onclick="closeaddpaperssss()" class="paperyueclose2"></label>
					</div>

					<!-- 试题列表视图 -->
					<div id="tmListView">
						<!-- 统计信息区域 -->
						<div class="tm-stats-area">
							<div class="stat-item">
								<span class="stat-label">总分值</span>
								<span class="stat-value" id="tmzf">0分</span>
							</div>
							<div class="stat-item">
								<span class="stat-label">单选题</span>
								<span class="stat-value" id="oxt">0道</span>
							</div>
							<div class="stat-item">
								<span class="stat-label">多选题</span>
								<span class="stat-value" id="dxt">0道</span>
							</div>
							<div class="stat-item">
								<span class="stat-label">判断题</span>
								<span class="stat-value" id="pdt">0道</span>
							</div>
							<div class="stat-item">
								<span class="stat-label">简答题</span>
								<span class="stat-value" id="jdt">0道</span>
							</div>
							<div class="stat-item">
								<span class="stat-label">材料分析题</span>
								<span class="stat-value" id="fxt">0道</span>
							</div>
							<div class="stat-item">
								<span class="stat-label">论述题</span>
								<span class="stat-value" id="lst">0道</span>
							</div>
						</div>

						<!-- 筛选区域 -->
						<div class="tm-filter-area">
							<!-- 主要筛选 -->
							<div class="filter-main">
								<div class="filter-group">
									<label>学科：</label>
									<select id="xueke3" onchange="xueke3change(this)">
										<option value="0">请选择学科</option>
									</select>
								</div>
								<div class="filter-group">
									<label>章节：</label>
									<select id="zj1" onchange="zjchange(this)">
										<option value="0">全部章节</option>
									</select>
								</div>
								<div class="filter-group">
									<label>类型：</label>
									<select id="tmtype" onchange="tmtypechange(this)">
										<option value="">全部类型</option>
										<option value="0">单选题</option>
										<option value="1">多选题</option>
										<option value="2">判断题</option>
										<option value="3">简答题</option>
										<option value="4">论述题</option>
										<option value="5">材料分析题</option>
									</select>
								</div>
							</div>

							<!-- 搜索框 -->
							<div class="filter-search">
								<input type="text" id="tmSearchInput" placeholder="输入关键词搜索题目..." />
								<span class="search-icon">🔍</span>
							</div>

							<!-- 快速筛选标签 -->
							<div class="filter-tags">
								<span class="filter-tag" onclick="quickFilterType('all')">全部</span>
								<span class="filter-tag" onclick="quickFilterType('0')">单选题</span>
								<span class="filter-tag" onclick="quickFilterType('1')">多选题</span>
								<span class="filter-tag" onclick="quickFilterType('2')">判断题</span>
								<span class="filter-tag" onclick="quickFilterType('3')">简答题</span>
								<span class="filter-tag" onclick="quickFilterType('4')">论述题</span>
								<span class="filter-tag" onclick="quickFilterType('5')">材料分析题</span>
							</div>
						</div>

						<!-- 试题列表区域 -->
						<div class="tm-list-area">
							<div class="tm-list-header">
								<div class="col-check">
									<label class="chenckboxall" onclick="selectall(this)" data-id="false"></label>
									<span>全选</span>
								</div>
								<div class="col-title">题目</div>
								<div class="col-type">类型</div>
								<div class="col-subject">学科</div>
								<div class="col-score">分值</div>
								<div class="col-action">操作</div>
							</div>
							<div class="tm-list-content" id="tmboxlist">
								<div class="tm-placeholder">
									<div class="placeholder-icon">📚</div>
									<div class="placeholder-text">请先选择学科，然后加载试题</div>
									<div class="placeholder-hint">选择学科后将显示该学科下的所有试题</div>
								</div>
							</div>
						</div>

						<!-- 底部按钮 -->
						<div class="tm-actions">
							<button class="btn-save" onclick="closeaddpaperssss()">保存</button>
							<button class="btn-cancel" onclick="closeaddpaperssss()">关闭</button>
						</div>
					</div>

					<!-- 试题详情视图 -->
					<div id="tmDetailView" style="display:none;">
						<div class="detail-header">
							<h3>试题详情</h3>
							<button onclick="hideTmDetail()" class="btn-back">返回列表</button>
						</div>
						<div class="detail-content">
							<div class="detail-row">
								<label>题目：</label>
								<span id="detailTmName">-</span>
							</div>
							<div class="detail-row">
								<label>类型：</label>
								<span id="detailTmType">-</span>
							</div>
							<div class="detail-row">
								<label>学科：</label>
								<span id="detailTmXueke">-</span>
							</div>
							<div class="detail-row">
								<label>章节：</label>
								<span id="detailTmZhangjie">-</span>
							</div>
							<div class="detail-row">
								<label>分值：</label>
								<span id="detailTmScore">-</span>
							</div>
							<div class="detail-row" id="detailTmOptionsContainer">
								<label>选项：</label>
								<div id="detailTmOptions" class="detail-options"></div>
							</div>
							<div class="detail-row">
								<label>答案：</label>
								<span id="detailTmAnswer">-</span>
							</div>
							<div class="detail-row">
								<label>解析：</label>
								<span id="detailTmAnalysis">-</span>
							</div>
						</div>
					</div>

				</div>
			</div>
		</div>
		
		<div id="deletebox">
			<div class="deletesss">
				<div class="sjtitle2">
					<div>系统提示</div>
					<label class="paperyueclose" onclick="qxdelete()"></label>
				</div>
				<div class="delstr">
					是否删除"<label id="delname"></label>"？
				</div>
				<div class="submitbox deldelpaper">
					<div class="bc" onclick="deleteitem()">删除</div>
					<div class="gb" onclick="qxdelete()">关闭</div>
				</div>
			</div>
		</div>
		
		<div id="deletebox2">
			<div class="deletesss">
				<div class="sjtitle3">
					<div>系统提示</div>
					<label class="paperyueclose" onclick="qxdelete2()"></label>
				</div>
				<div class="delstr">
					是否复制"<label id="delname2"></label>"？
				</div>
				<div class="submitbox deldelpaper">
					<div class="bc" onclick="fuzhisubmit()">复制</div>
					<div class="gb" onclick="qxdelete2()">关闭</div>
				</div>
			</div>
		</div>
		
		
		<div id="tmbox2">
			<div class="tmborderview">
				<div class="tmboxcontent">
					<div class="sjtitle">
						<div>预览试卷</div>
						<label onclick="closeyulan()" class="paperyueclose2"></label>
					</div>
					<div id="ylbox2">
						<div id="sssss"><img src="img/loading.png" id="loadimg"/></div>
						<div>请稍候...</div>
					</div>
					<div id="ylbox1">
						<div class="tmlxview tmlxview2">
							<div>考试时间 ：<label id="ylkssj"></label></div>
							<div>总分 ：<label id="ylzf"></label></div>
							<div>通过分数线 ：<label id="ylfsx"></label></div>
							<div>学科 ：<label id="ylxkname"></label></div>
						</div>
						<div class="tmscroll tmscroll2" id="yltmbox">
							
						</div>
					</div>
				</div>
			</div>
		</div>
		
		
		<div id="tmbox3">
			<div class="tmborderview">
				<div class="tmboxcontent">
					<div class="sjtitle">
						<div>修改试卷</div>
						<label onclick="closeedit()" class="paperyueclose2"></label>
					</div>
					<div id="editbox1">
						<div id="eeeee"><img src="img/loading.png" id="loadimg2"/></div>
						<div>请稍候...</div>
					</div>
					<div id="editbox2" class="editboxsss">
						<div class="inputviewa">
							<input id="addpapername2" placeholder="请输入试卷名称" />
						</div>
						<div class="inputviewa">
							<input id="addpapersm2" placeholder="请输入试卷说明" />
						</div>
						<div class="inputviewa">
							<select id="xuekeedit">
							</select>
						</div>
						<div class="inputviewb">
							<input id="papersj2" type="number" placeholder="输入考试时间（分钟）" />
							<input id="paperzf2" type="number" placeholder="输入总分" />
							<input id="paperfsx2" type="number" placeholder="输入通过分数线" />
						</div>
						<div class="inputviewb">
							<input id="editstartdate" type="date" placeholder="开始时间" />
							<input id="editenddate" type="date" placeholder="结束时间" />
							<input id="paperqz2" type="number" placeholder="请输入权重比" value="100" />
						</div>
						<div class="tmview">
							<div class="zjbtn" onclick="showEditPaperComposition()">
								<img src="img/zj.png" />
								重新组卷
							</div>
							<div class="zjstr">
								<span>总分：</span>
								<label id="tmzfedit">0</label>
								<label class="xt">|</label>
								<span>组成：</span>
								<div id="zchengboxedit">
								</div>
							</div>
						</div>
						<div class="bottomview">
							<div class="bradio">
								<div class="radioview edits" onclick="selectradio2(this)" data-id="draft">
									<label class="radioacc"></label>
									默认
								</div>
								<div class="radioview edits" onclick="selectradio2(this)" data-id="open">
									<label></label>
									开放
								</div>
								<div class="radioview edits" onclick="selectradio2(this)" data-id="closed">
									<label></label>
									关闭
								</div>
							</div>
						</div>
						<div class="bbtn bbbbbbbbb">
							<div class="bc" onclick="editsubmit()">保存</div>
							<div class="gb" onclick="closeedit()">取消</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			//开始时间和结束时间分开  两个input    修改的时候  组卷界面重写  记录原有数据
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()

				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getfooterlink()
				getpaperlist()
				getpaperxueke()
				getpaperallyears()
				
				// 设置默认权重为100
				$("#paperqz").val(100);
				
				// 初始化筛选标签
				$(".filter-tag").first().addClass("active");
				
				// 初始化组卷弹窗显示
				$("#tmSearchInput").on('click', function(e) {
					e.stopPropagation();
				});
				
				// 确保窗口大小改变时弹窗布局正确
				$(window).on('resize', function() {
					if($("#tmbox").is(":visible")) {
						adjustTmboxLayout();
					}
				});
			})
			
			// 调整组卷弹窗布局
			function adjustTmboxLayout() {
				let windowHeight = $(window).height();
				let availableHeight = windowHeight * 0.85; // 使用85%的窗口高度，给更多空间

				if ($("#tmListView").is(":visible")) {
					let titleHeight = $(".sjtitle").outerHeight(true) || 50; // 标题栏高度
					let statsHeight = $(".tm-stats-area").outerHeight(true) || 0;
					let filterHeight = $(".tm-filter-area").outerHeight(true) || 0;
					let headerHeight = $(".tm-list-header").outerHeight(true) || 0;
					let actionsHeight = $(".tm-actions").outerHeight(true) || 0;
					
					// 计算试题列表内容区域的可用高度
					let listContentHeight = availableHeight - titleHeight - statsHeight - filterHeight - headerHeight - actionsHeight - 20; // 减少边距
					listContentHeight = Math.max(listContentHeight, 300); // 最小高度调整为300px
					$(".tm-list-content").css('height', listContentHeight + 'px');
				} 
				
				if ($("#tmDetailView").is(":visible")) {
					let titleHeight = $(".sjtitle").outerHeight(true) || 50; // 标题栏高度
					let detailHeaderHeight = $(".detail-header").outerHeight(true) || 0;
					let detailContentHeight = availableHeight - titleHeight - detailHeaderHeight - 20; // 减少边距
					detailContentHeight = Math.max(detailContentHeight, 300); // 最小高度调整为300px
					$(".detail-content").css('height', detailContentHeight + 'px');
				}
			}
			
			let pagesize = 10
			let pageindex = 1
			let pages = 1

			let alltmlist = null

			let xuekeid = null
			let starttime = null
			let endtime = null
			let xuekelist = null
			
			let checkedDate = [] //已选题
			
			let deleteid = null
			
			// 添加全局变量标记是否正在编辑试卷
			let isEditingPaper = false
			
			function yulan(paper){
				$("#ylbox1").hide()
				$("#ylbox2").show()
				$("#tmbox2").attr("style","display: flex")
				let paperid = $(paper).attr("data-id")
				$.ajax({
					url: baseurl + "/paper/"+paperid,
					type: 'get',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let kssj = res.data.limitedTime //考试时间
							let zfnum = res.data.score //总分
							let tgfnum = res.data.passedScore
							let xkname = res.data.projectName
							
							$("#ylkssj").html(kssj+'分钟')
							$("#ylzf").html(zfnum+'分')
							$("#ylfsx").html(tgfnum+'分')
							$("#ylxkname").html(xkname)
							
							let tmlist = res.data.cmsSubjectList
							let html = ''
							tmlist.map((item,index)=>{
									html+='<div class="yltm">'+
									'<div class="ylname"><label>第'+(index+1)+'题 ：</label>'+item.name+'</div>'+
									'<div class="tmlxlabel">'
									if(item.type == '0'){
										html+='<label>单选题</label>'
										html+='</div><div class="xuanxiang"><div class="xxbooo">选项</div>'
											item.cmsSubjectOption.map((item2)=>{
												html+='<div class="ylxx">'+item2.keyword +'. '+item2.name+'</div>'
											})
										html+='</div><div class="dayl">答案 ：<label>'+item.answer+'</label></div></div>'
									}else if(item.type == '1'){
										html+='<label>多选题</label>'
										html+='</div><div class="xuanxiang"><div class="xxbooo">选项</div>'
											item.cmsSubjectOption.map((item2)=>{
												html+='<div class="ylxx">'+item2.keyword +'. '+item2.name+'</div>'
											})
										html+='</div><div class="dayl">答案 ：<label>'
										item.answer.split(',').map((item3)=>{
											html+=item3
										})
										html+='</label></div></div>'
									}else if(item.type == '2'){
										html+='<label>判断题</label>'
										html+='<div class="dayl">答案 ：<label>'
										if(item.answer=='true'){
											html+='正确'
										}else{
											html+='错误'
										}
										html+='</label></div></div></div>'
									}else if(item.type == '3'){
										html+='<label>简答题</label>'
										html+='<div class="dayl">答案 ：'
										if(item.answer==null){
											html+='无'
										}else{
											html+=item.answer
										}
										html+='</div></div></div>'
									}
									else if(item.type == '4'){
										html+='<label>论述题</label>'
										html+='<div class="dayl">答案 ：'
										if(item.answer==null){
											html+='无'
										}else{
											html+=item.answer
										}
										html+='</div></div></div>'
									}
									else if(item.type == '5'){
										html+='<label>材料分析题</label>'
										html+='<div class="dayl">答案 ：'
										if(item.answer==null){
											html+='无'
										}else{
											html+=item.answer
										}
										html+='</div></div></div>'
									}
									
							})
							$("#yltmbox").html(html)
							$("#ylbox1").show()
							$("#ylbox2").hide()
							
							
							$("#yltmbox").animate({
								scrollTop: 0
							}, 0)
						}
					}
				})
			}
			
			function editsubmit(){
				editjson = {
					id: editsid,
					name: $("#addpapername2").val(),
					description: $("#addpapersm2").val(),
					projectId: $("#xuekeedit").val(),
					limitedTime: $("#papersj2").val(),
					score: $("#paperzf2").val(),
					passedScore: $("#paperfsx2").val(),
					theWeight: $("#paperqz2").val(),
					status: editstatus,
					startTime : $("#editstartdate").val(), //开始时间
					endTime : $("#editenddate").val() //结束时间
				}
				// console.log(checkedDate)
				
				if (!editjson.name) {
					cocoMessage.warning(1000, "请输入试卷名称！")
				} else if (!editjson.description) {
					cocoMessage.warning(1000, "请输入试卷说明！")
				} else if (editjson.projectId == '0') {
					cocoMessage.warning(1000, "请选择学科！")
				} else if (!editjson.limitedTime) {
					cocoMessage.warning(1000, "请输入考试时间！")
				} else if (!editjson.score) {
					cocoMessage.warning(1000, "请输入总分！")
				} else if (!editjson.passedScore) {
					cocoMessage.warning(1000, "请输入通过分数线！")
				}else if (!editjson.startTime) {
					cocoMessage.warning(1000, "请选择开始时间！")
				}else if (!editjson.endTime) {
					cocoMessage.warning(1000, "请选择结束时间！")
				}else if (!editjson.theWeight) {
					cocoMessage.warning(1000, "请输入权重比！")
				}else {
					$("#tmbox3").attr("style","display: none;")
					new Promise((resolve, reject)=>{
						//修改试卷
						$.ajax({
							url: baseurl + "/paper/edit",
							type: 'put',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: JSON.stringify(editjson),
							dataType: 'json',
							success: (res) => {
								if (res.code == '200') {
									editjson = null
									if(checkedDate.length>0){
										resolve(editsid)
									}else{
										cocoMessage.success(1000, "修改试卷成功！")
										pageindex = 1
										projectId = null
										seachstartTime = null
										seachendTime = null
										shuaxin()
										getpaperlist()
									}
								}else{
									cocoMessage.error(1000, "修改试卷失败！")
								}
							}
						})
					}).then(res=>{
						let json = {
							id: res,
							subjectIds: []
						}
						checkedDate.map((item)=>{
							json.subjectIds.push(item.id)
						})
						$.ajax({
							url: baseurl + "/paper/combination",
							type: 'PUT',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: JSON.stringify(json),
							dataType: 'json',
							success: (res) => {
								if (res.code == '200') {
									cocoMessage.success(1000, "修改试卷成功！")
									checkedDate = []
								}else{
									cocoMessage.error(1000, "修改试卷失败！")
								}
								pageindex = 1
								projectId = null
								seachstartTime = null
								seachendTime = null
								shuaxin()
								getpaperlist()
							}
						})
					}).catch(err=>{
						cocoMessage.error(1000, "修改试卷失败！")
					})
				}
			}
			let editstatus = null
			let editsid = null
			let editjson = null
			function xiugai(paper){
				console.log("开始修改试卷...");
				editsid = $(paper).attr("data-id")
				console.log("试卷ID:", editsid);
				console.log("baseurl:", baseurl);
				console.log("Authorization header:", sessionStorage.getItem("header"));
				
				// 显示弹窗
				$("#tmbox3").attr("style","display: flex")
				
				// 确保显示加载状态，使用CSS类
				$("#editbox1").removeClass("hide").addClass("show")
				$("#editbox2").removeClass("show").addClass("hide")
				
				// 构建完整的URL
				let fullUrl = baseurl + "/paper/" + editsid;
				console.log("请求URL:", fullUrl);
				
				// 添加超时处理
				let requestTimeout = setTimeout(function() {
					console.error("请求超时");
					cocoMessage.error(1000, "请求超时，请检查网络连接！")
					$("#tmbox3").attr("style","display: none")
				}, 15000); // 15秒超时
				
				$.ajax({
					url: fullUrl,
					type: 'get',
					contentType: "application/json",
					timeout: 10000, // 10秒Ajax超时
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					beforeSend: function(xhr) {
						console.log("发送请求前...", xhr);
					},
					success: (res) => {
						clearTimeout(requestTimeout); // 清除超时计时器
						console.log("获取试卷信息成功:", res);
						
						if (res.code == '200') {
							let mdata = res.data
							console.log("试卷数据:", mdata);
							
							$("#addpapername2").val(mdata.name) //试卷名称
							$("#addpapersm2").val(mdata.description) //试卷说明
							$("#xuekeedit").val(mdata.projectId) //学科
							$("#papersj2").val(mdata.limitedTime) //考试时间
							$("#paperzf2").val(mdata.score) //总分
							$("#paperfsx2").val(mdata.passedScore) //通过分数线
							$("#paperqz2").val(mdata.theWeight ? mdata.theWeight : 100) //权重，如果没有则默认100
							$("#editstartdate").val(setDate2(mdata.startTime))
							$("#editenddate").val(setDate2(mdata.endTime))
							editstatus =  mdata.status
							let alls = $(".edits")
							for (let i = 0; i < alls.length; i++) {
								if($(alls[i]).attr("data-id") == editstatus){
									$(alls[i]).find("label").attr("class", "radioacc")
								}else{
									$(alls[i]).find("label").attr("class", "")
								}
							}
							checkedDate = res.data.cmsSubjectList || []
							let zfnum = 0 //总分
							let dxtnum = 0 //单选数量
							let dxtsnum = 0 //多选题数量
							let pdtsum = 0 //判断题数量
							let jdsum = 0 //简答题数量
							let lstnum = 0 //论述题数量
							let fxtsum = 0 //材料分析题数量
							if (checkedDate && checkedDate.length > 0) {
								checkedDate.map((item) => {
									zfnum += parseInt(item.score || 0)
									if (item.type == '0') {
										dxtnum += 1
									} else if (item.type == '1') {
										dxtsnum += 1
									} else if (item.type == '2') {
										pdtsum += 1
									} else if (item.type == '3') {
										jdsum += 1
									} else if (item.type == '4') {
										lstnum += 1
									} else if (item.type == '5') {
										fxtsum += 1
									}
								})
								$("#tmzfedit").html(zfnum + '分')
								let htmlss = ''
								if(dxtnum>0){
									htmlss+='<label>单选题('+dxtnum+'道)</label>'
								}
								if(dxtsnum>0){
									htmlss+='<label>多选题('+dxtsnum+'道)</label>'
								}
								if(pdtsum>0){
									htmlss+='<label>判断题('+pdtsum+'道)</label>'
								}
								if(jdsum>0){
									htmlss+='<label>简答题('+jdsum+'道)</label>'
								}
								if(lstnum>0){
									htmlss+='<label>论述题('+lstnum+'道)</label>'
								}
								if(fxtsum>0){
									htmlss+='<label>材料分析题('+fxtsum+'道)</label>'
								}
								$("#zchengboxedit").html(htmlss)
							} else {
								$("#tmzfedit").html('0分')
								$("#zchengboxedit").html('<label>暂无题目</label>')
							}
							
							console.log("数据填充完成，切换到编辑表单");
							
							// 使用CSS类切换，确保DOM操作完成
							setTimeout(function() {
								$("#editbox1").removeClass("show").addClass("hide")
								$("#editbox2").removeClass("hide").addClass("show")
								console.log("界面切换完成");
							}, 200);
							
						} else {
							clearTimeout(requestTimeout);
							console.error("获取试卷信息失败:", res);
							cocoMessage.error(1000, res.message || "加载试卷信息失败！")
							$("#tmbox3").attr("style","display: none")
						}
					},
					error: function(xhr, status, error) {
						clearTimeout(requestTimeout);
						console.error("网络请求失败:", xhr, status, error);
						console.error("响应文本:", xhr.responseText);
						
						if (status === 'timeout') {
							cocoMessage.error(1000, "请求超时，请检查网络连接！")
						} else if (xhr.status === 0) {
							cocoMessage.error(1000, "网络连接失败，请检查网络！")
						} else if (xhr.status === 401) {
							cocoMessage.error(1000, "登录已过期，请重新登录！")
							// 可以添加重新登录逻辑
						} else {
							cocoMessage.error(1000, "网络错误（" + xhr.status + "），请稍后重试！")
						}
						$("#tmbox3").attr("style","display: none")
					}
				})
			}
			function closeedit(){
				checkedDate = []
				shuaxin()
				// 重置编辑状态标记
				isEditingPaper = false
				$("#editbox1").removeClass("hide").addClass("show")
				$("#editbox2").removeClass("show").addClass("hide")
				$("#tmbox3").attr("style","display: none")
			}
			function closeyulan(){
				$("#tmbox2").attr("style","display: none")
			}
			function qxdelete(){
				$("#deletebox").attr("style","display: none")
			}
			function qxdelete2(){
				$("#deletebox2").attr("style","display: none")
			}
			function showfuzhi(item){
				let name = $(item).attr("data-name")
				deleteid = $(item).attr("data-id")
				$("#delname2").html(name)
				$("#deletebox2").attr("style","display: flex")
			}
			function showdelete(item){
				let name = $(item).attr("data-name")
				deleteid = $(item).attr("data-id")
				$("#delname").html(name)
				$("#deletebox").attr("style","display: flex")
			}
			function fuzhisubmit(){
				$.ajax({
					url: baseurl + "/paper/copy/"+deleteid,
					type: 'get',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							qxdelete2()
							cocoMessage.success(1000, "复制试卷成功！")
							pageindex = 1
							projectId = null
							seachstartTime = null
							seachendTime = null
							getpaperlist()
						}
					}
				})
			}
			function deleteitem(){
				$.ajax({
					url: baseurl + "/paper/delete/"+deleteid,
					type: 'delete',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							qxdelete()
							cocoMessage.success(1000, "删除试卷成功！")
							pageindex = 1
							projectId = null
							seachstartTime = null
							seachendTime = null
							getpaperlist()
						}
					}
				})
			}
			
			function getpaperlist() {
				//获取试卷列表  
				$.ajax({
					url: baseurl + "/paper",
					type: 'GET',
					data: {
						pageNum: pageindex,
						pageSize: pagesize,
						projectId: xuekeid,
						seachstartTime: starttime,
						seachendTime: endtime
					},
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							if (res.data.list.length > 0) {
								res.data.list.map((item, index) => {
									// 计算序号
									let rowNum = (pageindex - 1) * pagesize + index + 1;
									
									// 试卷名称处理 - 截断并添加悬浮提示
									let paperName = item.name || '';
									let truncatedName = paperName.length > 15 ? paperName.substring(0, 15) + '...' : paperName;
									
									// 学科名称处理
									let subjectName = item.projectName || '';
									let truncatedSubject = subjectName.length > 6 ? subjectName.substring(0, 6) + '...' : subjectName;
									
									// 考试时间处理
									let startDate = setDate2(item.startTime);
									let endDate = setDate2(item.endTime);
									let fullTimeText = startDate + ' ~ ' + endDate;
									let truncatedTime = startDate.substring(5) + '~' + endDate.substring(5); // 只显示月日
									
									// 生成表格行 - 去掉状态列
									html += '<tr>' +
										'<td>' + rowNum + '</td>' +
										'<td><div class="paper-name" data-full-name="' + paperName.replace(/"/g, '&quot;') + '" title="' + paperName.replace(/"/g, '&quot;') + '">' + truncatedName + '</div></td>' +
										'<td><div class="subject-name" data-full-subject="' + subjectName.replace(/"/g, '&quot;') + '" title="' + subjectName.replace(/"/g, '&quot;') + '">' + truncatedSubject + '</div></td>' +
										'<td><div class="exam-time" data-full-time="' + fullTimeText + '" title="' + fullTimeText + '">' + truncatedTime + '</div></td>' +
										'<td>' + item.limitedTime + '分</td>' +
										'<td>' + item.score + '</td>' +
										'<td>' +
											'<div class="action-buttons">' +
												'<span class="action-btn preview" onclick="yulan(this)" data-id="' + item.id + '" title="预览试卷">预览</span>' +
												'<span class="action-btn edit" onclick="xiugai(this)" data-id="' + item.id + '" title="修改试卷">修改</span>' +
												'<span class="action-btn copy" onclick="showfuzhi(this)" data-id="' + item.id + '" data-name="' + item.name + '" title="复制试卷">复制</span>' +
												'<span class="action-btn delete" onclick="showdelete(this)" data-id="' + item.id + '" data-name="' + item.name + '" title="删除试卷">删除</span>' +
											'</div>' +
										'</td>' +
									'</tr>';
								})
							} else {
								html = '<tr><td colspan="7" class="no-data-row">暂时没有数据...</td></tr>'
							}
							$("#paperbox").html(html)
							// console.log(res.data.pages)
							pages = res.data.pages
							if (pages > 1) {
								let numhtml = ""
								//当页数大于5
								if (pages > 5) {
									if (pageindex <= 5) { //当前页码小于5的时候右侧不变
										for (let a = 1; a <= 10; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
										numhtml += '<label onclick="getnewlist(11)">...</label>'
									} else if (5 < pageindex & pageindex < pages - 5) {
										numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) - 5) +
											')">...</label>'
										for (let a = pageindex - 4; a <= pageindex + 4; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
										numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) + 5) +
											')">...</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + (pages - 11) +
											')">...</label>'
										for (let a = pages - 10; a <= pages; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
									}
								} else {
									for (let a = 1; a <= pages; a++) {
										if (pageindex == a) {
											numhtml += '<label class="actinum" onclick="getnewlist(' + a +
												')">' + a +
												'</label>'
										} else {
											numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
										}
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
						}
					}
				})
			}

			function yearschange(select) {
				let str = $(select).val()
				let strs = str.split('-')
				if (str == '0') {
					starttime = null
					endtime = null
				} else {
					if (strs[1] == '上学期') {
						//3-1  8-1
						starttime = strs[0] + '-3-2'
						endtime = strs[0] + '-8-1'
					} else {
						// 9- 1  2-29
						starttime = strs[0] + '-9-1'
						endtime = (parseInt(strs[0]) + 1) + '-3-1'
					}
				}
				// console.log(starttime, endtime)
				pageindex = 1
				getpaperlist()
			}

			function xuekechange(select) {
				if ($(select).val() == '0') {
					xuekeid = null
				} else {
					xuekeid = $(select).val()
				}
				pageindex = 1
				getpaperlist()
			}

			function getpaperxueke() { //获取学科
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							xuekelist = res.data
							let xuekehtml = "<option value=0>全部学科</option>"
							xuekelist.map((item) => {
								xuekehtml += '<option value="' + item.id + '">' + item.name + '</option>'
							})
							$("#xueke").html(xuekehtml)
							$("#xueke2").html(xuekehtml)
							$("#xueke3").html(xuekehtml)
							$("#xuekeedit").html(xuekehtml)
						}
					}
				})
			}

			function getpaperallyears() { //获取所有年份
				let date = new Date()
				let nowyears = date.getFullYear()
				let smallYears = nowyears - 5
				let Years = nowyears - smallYears
				let array = []
				for (let i = 0; i <= Years; i++) {
					let yy = nowyears--
					array.push(yy + '-下学期')
					array.push(yy + '-上学期')
				}
				let yearstr = '<option value="0">全部年份</option>'
				array.map((item) => {
					yearstr += '<option value="' + item + '">' + item + '</option>'
				})
				$("#years").html(yearstr)
			}

			function showaddpaper() {
				$("#tmbox").attr("style", "display: flex;");
				hideTmDetail(); // 确保打开时显示列表页
				
				// 如果有已选题目，智能设置学科和加载题目
				if (checkedDate && checkedDate.length > 0) {
					// 从已选题目中获取第一个题目的学科ID
					let firstSelectedSubject = checkedDate[0].projectSectionId;
					if (firstSelectedSubject) {
						shitixkid = firstSelectedSubject;
						$("#xueke3").val(firstSelectedSubject);
						
						// 保持其他筛选条件的重置
						shitizjid = null;
						usertypes = null;
						$("#zj1").html('<option value="0">全部章节</option>');
						$("#tmtype").val("");
						$("#tmSearchInput").val("");
						
						$(".filter-tag").removeClass("active");
						$(".filter-tag[onclick=\"quickFilterType('all')\"]").addClass("active");
						
						$('.chenckboxall').attr("data-id", "false").removeClass("ac");
						
						// 加载对应学科的题目并显示已选状态
						getalltm();
						getzjlist();
					} else {
						// 如果已选题目没有学科信息，显示占位符
						showPlaceholder();
					}
				} else {
					// 如果没有已选题目，重置所有筛选条件
					shitixkid = null; 
					shitizjid = null; 
					usertypes = null; 
					
					$("#xueke3").val("0");
					$("#zj1").html('<option value="0">全部章节</option>');
					$("#tmtype").val("");
					$("#tmSearchInput").val("");
					
					$(".filter-tag").removeClass("active");
					$(".filter-tag[onclick=\"quickFilterType('all')\"]").addClass("active");
					
					$('.chenckboxall').attr("data-id", "false").removeClass("ac");
					
					// 显示占位符，不自动加载试题
					showPlaceholder();
				}
				
				jisuan();
				
				setTimeout(function() {
					adjustTmboxLayout();
				}, 150);
			}

			function showPlaceholder() {
				const placeholderHtml = `
					<div class="tm-placeholder">
						<div class="placeholder-icon">📚</div>
						<div class="placeholder-text">请先选择学科，然后加载试题</div>
						<div class="placeholder-hint">选择学科后将显示该学科下的所有试题</div>
					</div>
				`;
				$("#tmboxlist").html(placeholderHtml);
			}

			function xueke3change(select) { //题目列表 学科选择
				shitixkid = $(select).val();
				shitizjid = null;
				$("#zj1").html('<option value="0">全部章节</option>');
				
				if (shitixkid == '0') {
					shitixkid = null;
					showPlaceholder();
					return;
				}
				
				// 选择了学科，加载试题
				getalltm();
				getzjlist();
			}

			function getalltm() { //获取所有试题
				// 如果没有选择学科，显示占位符
				if (!shitixkid) {
					showPlaceholder();
					return;
				}

				// 显示加载状态
				$("#tmboxlist").html(`
					<div class="tm-placeholder">
						<div class="placeholder-icon">⏳</div>
						<div class="placeholder-text">正在加载试题...</div>
					</div>
				`);

				$.ajax({
					url: baseurl + "/subject/listall",
					type: 'GET',
					contentType: "application/json",
					data: {
							projectSectionId: shitixkid,
							chapterId: shitizjid,
							type: usertypes
						},
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							alltmlist = res.data;
							if (!alltmlist) alltmlist = [];

							// 标记已选中的试题
							checkedDate.forEach(item1 => {
								let found = alltmlist.find(item2 => item1.id == item2.id);
								if (found) found.analysis = 'cc';
							});
		
							let html = '';
							if(alltmlist.length === 0) {
								html = `
									<div class="tm-placeholder">
										<div class="placeholder-icon">📝</div>
										<div class="placeholder-text">该学科下暂无试题</div>
										<div class="placeholder-hint">请选择其他学科或联系管理员添加试题</div>
									</div>
								`;
							} else {
								alltmlist.forEach((item) => {
									let isChecked = item.analysis === 'cc';
									// 判断题目是否过长（超过400字符认为太长）
									let isLongTitle = item.name && item.name.length > 400;
									// 对题目文本进行转义处理
									let escapedTitle = item.name ? item.name.replace(/'/g, "&#39;").replace(/"/g, "&quot;") : '';
									html += `
										<div class="tm-item">
											<div class="col-check">
												<label class="chenckbox ${isChecked ? 'ac' : ''}" onclick="selecttm(this)" data-check="${isChecked}" data-id="${item.id}"></label>
											</div>
											<div class="col-title" onmouseenter="showTitleTooltip(this, '${escapedTitle}', ${isLongTitle})" onmouseleave="hideTitleTooltip(this)">
												${item.name}
												<div class="title-tooltip"></div>
											</div>
											<div class="col-type">${getTmTypeName(item.type)}</div>
											<div class="col-subject" title="${item.projectSectionName || '未知学科'}">${item.projectSectionName || '未知学科'}</div>
											<div class="col-score">${item.score}</div>
											<div class="col-action">
												<button class="detail-btn" onclick="showTmDetail('${item.id}')">详情</button>
											</div>
										</div>
									`;
								});
								
								// 检查是否全部已选，更新全选按钮状态
								let allChecked = alltmlist.length > 0 && alltmlist.every(item => item.analysis === 'cc');
								if (allChecked) {
									$('.chenckboxall').attr("data-id", "true").addClass("ac");
								} else {
									$('.chenckboxall').attr("data-id", "false").removeClass("ac");
								}
							}
							$("#tmboxlist").html(html);
							
							// 重新绑定搜索功能
							$("#tmSearchInput").off('keyup').on('keyup', function() {
								searchTmList($(this).val());
							});
							
							// 应用筛选
							if(usertypes) {
								quickFilterType(usertypes);
							}
						} else {
							$("#tmboxlist").html(`
								<div class="tm-placeholder">
									<div class="placeholder-icon">❌</div>
									<div class="placeholder-text">获取试题失败</div>
									<div class="placeholder-hint">${res.message || '请稍后重试'}</div>
								</div>
							`);
						}
						adjustTmboxLayout();
					},
					error: function(jqXHR, textStatus, errorThrown) {
						$("#tmboxlist").html(`
							<div class="tm-placeholder">
								<div class="placeholder-icon">⚠️</div>
								<div class="placeholder-text">网络请求失败</div>
								<div class="placeholder-hint">请检查网络连接后重试</div>
							</div>
						`);
						adjustTmboxLayout();
					}
				})
			}

			// 修改搜索功能
			function searchTmList(keyword) {
				if(!keyword) {
					$(".tm-item").show();
					return;
				}
				
				keyword = keyword.toLowerCase();
				$(".tm-item").each(function() {
					let tmText = $(this).find(".col-title").text().toLowerCase();
					if(tmText.indexOf(keyword) > -1) {
						$(this).show();
					} else {
						$(this).hide();
					}
				});
			}
			
			// 修改快速筛选功能
			function quickFilterType(type) {
				$(".filter-tag").removeClass("active");
				$(".filter-tag").each(function() {
					if($(this).attr("onclick").indexOf(type) > -1) {
						$(this).addClass("active");
					}
				});
				
				if(type === 'all') {
					$("#tmtype").val("");
					$(".tm-item").show();
				} else {
					$("#tmtype").val(type);
					
					$(".tm-item").each(function() {
						let tmType = $(this).find(".col-type").text().trim();
						let matchType = false;
						
						if(type === '0' && tmType === '单选题') matchType = true;
						if(type === '1' && tmType === '多选题') matchType = true;
						if(type === '2' && tmType === '判断题') matchType = true;
						if(type === '3' && tmType === '简答题') matchType = true;
						if(type === '4' && tmType === '论述题') matchType = true;
						if(type === '5' && tmType === '材料分析题') matchType = true;
						
						if(matchType) {
							$(this).show();
						} else {
							$(this).hide();
						}
					});
				}
			}

			function selecttm(tm) {
				let rowid = $(tm).attr("data-id");
				let ischeck = $(tm).attr("data-check");
				let rows = getArray(alltmlist, $(tm).attr("data-id"));
				
				if (ischeck == 'true') {
					$(tm).attr("data-check", "false").removeClass("ac");
					// 删除这条数据
					let newlist = [];
					checkedDate.forEach((item) => {
						if(item.id != rowid){
							newlist.push(item);
						}
					});
					checkedDate = newlist;
				} else {
					$(tm).attr("data-check", "true").addClass("ac");
					// 保存这条数据
					let exists = checkedDate.some(item => item.id == rowid);
					if(!exists && rows){
						checkedDate.push(rows);
					}
				}
				jisuan();
			}

			function selectall(all) { //全选
				let alltmel = $(".chenckbox");
				if ($(all).attr("data-id") == 'false') {
					$(all).attr("data-id", "true").addClass("ac");
					
					alltmel.each(function(){
						$(this).attr("data-check", "true").addClass("ac");
					});
					
					// 将当前显示的所有数据都放进去（去重）
					if(alltmlist && alltmlist.length > 0) {
						alltmlist.forEach(item => {
							let exists = checkedDate.some(selected => selected.id == item.id);
							if (!exists) {
								checkedDate.push(item);
							}
						});
					}
				} else {
					$(all).attr("data-id", "false").removeClass("ac");
					alltmel.each(function(){
						$(this).attr("data-check", "false").removeClass("ac");
					});
					
					// 从已选列表中移除当前显示的题目
					if(alltmlist && alltmlist.length > 0) {
						let currentIds = alltmlist.map(item => item.id);
						checkedDate = checkedDate.filter(item => !currentIds.includes(item.id));
					}
				}
				jisuan();
			}

			function zjchange(select) { //章节选择
				shitizjid = $(select).val()
				if (shitizjid == '0') {
					shitizjid = null
				}
				getalltm()
			}

			function getzjlist() {
				if (shitixkid && xuekelist) {
					// console.log(xuekelist)
					let zjlist = null
					xuekelist.map((item) => {
						if (item.id == shitixkid) {
							zjlist = item.children
						}
					})
					let zjhtml = '<option value=0>全部章节</option>'
					if (zjlist && zjlist.length > 0) {
						zjlist.map((item) => {
							zjhtml += '<option value="' + item.id + '">' + item.name + '</option>'
						})
					}
					$("#zj1").html(zjhtml)
				}
			}

			function tmtypechange(select) {
				usertypes = $(select).val()
				// console.log(usertypes)
				if (!usertypes) {
					usertypes = null;
					// 同步更新快速筛选标签
					$(".filter-tag").removeClass("active");
					$(".filter-tag[onclick=\"quickFilterType('all')\"]").addClass("active");
				} else {
					// 同步更新快速筛选标签
					$(".filter-tag").removeClass("active");
					$(".filter-tag[onclick=\"quickFilterType('" + usertypes + "')\"]").addClass("active");
				}
				getalltm()
			}

			function getTmTypeName(typeCode) {
				const typeMap = {
					'0': '单选题',
					'1': '多选题',
					'2': '判断题',
					'3': '简答题',
					'4': '论述题',
					'5': '材料分析题'
				};
				return typeMap[typeCode] || '未知类型';
			}

			// 显示题目悬浮提示
			function showTitleTooltip(element, title, isLongTitle) {
				let tooltip = $(element).find('.title-tooltip');
				// 先将HTML转义字符还原
				let decodedTitle = $('<div>').html(title).text();
				let tooltipContent = decodedTitle;
				
				// 如果题目太长，添加查看详情的提示
				if (isLongTitle) {
					// 限制悬浮框中显示的字符数
					if (decodedTitle.length > 200) {
						tooltipContent = decodedTitle.substring(0, 200) + '...';
					}
					tooltipContent += '<span class="view-detail-hint">题目过长，点击"详情"按钮查看完整内容</span>';
				}
				
				tooltip.html(tooltipContent);
				tooltip.addClass('show');
			}

			// 隐藏题目悬浮提示
			function hideTitleTooltip(element) {
				let tooltip = $(element).find('.title-tooltip');
				tooltip.removeClass('show');
			}

			function showTmDetail(tmId) {
				let tm = alltmlist.find(item => item.id == tmId);
				if(tm) {
					$("#detailTmName").text(tm.name || '-');
					$("#detailTmType").text(getTmTypeName(tm.type));
					$("#detailTmXueke").text(tm.projectSectionName || '-');
					$("#detailTmZhangjie").text(tm.chapterName || '-');
					$("#detailTmScore").text(tm.score || '-');
					
					let optionsHtml = '';
					if (tm.type === '0' || tm.type === '1') { // 单选或多选
						$("#detailTmOptionsContainer").show();
						if (tm.cmsSubjectOption && tm.cmsSubjectOption.length > 0) {
							tm.cmsSubjectOption.forEach(opt => {
								optionsHtml += '<p>' + opt.keyword + '. ' + opt.name + '</p>';
							});
						} else {
							optionsHtml = '<p>无选项信息</p>';
						}
					} else {
						$("#detailTmOptionsContainer").hide();
					}
					$("#detailTmOptions").html(optionsHtml);

					if (tm.type === '2') { // 判断题
						$("#detailTmAnswer").text(tm.answer === 'true' ? '正确' : (tm.answer === 'false' ? '错误' : (tm.answer || '-')));
					} else {
						$("#detailTmAnswer").text(tm.answer || '-');
					}
					$("#detailTmAnalysis").text(tm.analysis && tm.analysis !=='cc' ? tm.analysis : '暂无解析'); //避免显示选中标记

					$("#tmListView").hide();
					$("#tmDetailView").css('display', 'flex'); // 使用flex来保持布局
				} else {
					cocoMessage.error(1000, "未找到试题详情！")
				}
			}

			function hideTmDetail() {
				$("#tmDetailView").hide();
				$("#tmListView").show();
				adjustTmboxLayout(); // 返回列表时也调整高度
			}
			
			// 添加题目搜索功能
			function searchTmList(keyword) {
				if(!keyword) {
					$(".tm-item").show(); // 如果没有关键词，显示所有题目
					return;
				}
				
				keyword = keyword.toLowerCase();
				$(".tm-item").each(function() {
					let tmText = $(this).find(".col-title").text().toLowerCase();
					if(tmText.indexOf(keyword) > -1) {
						$(this).show();
					} else {
						$(this).hide();
					}
				});
			}
			
			// 添加快速筛选功能
			function quickFilterType(type) {
				// 更新筛选标签样式
				$(".filter-tag").removeClass("active");
				$(".filter-tag").each(function() {
					if($(this).attr("onclick").indexOf(type) > -1) {
						$(this).addClass("active");
					}
				});
				
				// 更新下拉选项以保持一致性
				if(type === 'all') {
					$("#tmtype").val("");
					$(".tm-item").show();
				} else {
					$("#tmtype").val(type);
					
					$(".tm-item").each(function() {
						let tmType = $(this).find(".col-type").text().trim();
						let matchType = false;
						
						if(type === '0' && tmType === '单选题') matchType = true;
						if(type === '1' && tmType === '多选题') matchType = true;
						if(type === '2' && tmType === '判断题') matchType = true;
						if(type === '3' && tmType === '简答题') matchType = true;
						if(type === '4' && tmType === '论述题') matchType = true;
						if(type === '5' && tmType === '材料分析题') matchType = true;
						
						if(matchType) {
							$(this).show();
						} else {
							$(this).hide();
						}
					});
				}
			}

			function jisuan() { //计算题目数量  分数
				let zfnum = 0 //总分
				let dxtnum = 0 //单选数量
				let dxtsnum = 0 //多选题数量
				let pdtsum = 0 //判断题数量
				let jdsum = 0 //简答题数量
				let lstnum = 0 //论述题数量
				let fxtsum = 0 //材料分析题数量
				if (checkedDate) {
					checkedDate.map((item) => {
						zfnum += parseInt(item.score)
						if (item.type == '0') {
							dxtnum += 1
						} else if (item.type == '1') {
							dxtsnum += 1
						} else if (item.type == '2') {
							pdtsum += 1
						} else if (item.type == '3') {
							jdsum += 1
						} else if (item.type == '4') {
							lstnum += 1
						} else if (item.type == '5') {
							fxtsum += 1
						}
					})
				} else {
					zfnum = 0
					dxtnum = 0
					dxtsnum = 0
					pdtsum = 0
					jdsum = 0
					lstnum = 0
					fxtsum = 0
				}

				$("#tmzf").html(zfnum + '分')
				$("#oxt").html(dxtnum + '道')
				$("#dxt").html(dxtsnum + '道')
				$("#pdt").html(pdtsum + '道')
				$("#jdt").html(jdsum + '道')
				$("#fxt").html(fxtsum + '道')
				$("#lst").html(lstnum + '道')
			}

			function getArray(list, id) {
				let data = null
				list.map((item) => {
					if (item.id == id) {
						data = item
					}
				})
				return data
			}
			function shuaxin(){
				//将计算数据放到 list
				let zfnum = 0 //总分
				let dxtnum = 0 //单选数量
				let dxtsnum = 0 //多选题数量
				let pdtsum = 0 //判断题数量
				let jdsum = 0 //简答题数量
				let lstnum = 0 //论述题数量
				let fxtsum = 0 //材料分析题数量
				checkedDate.map((item) => {
					zfnum += parseInt(item.score)
					if (item.type == '0') {
						dxtnum += 1
					} else if (item.type == '1') {
						dxtsnum += 1
					} else if (item.type == '2') {
						pdtsum += 1
					} else if (item.type == '3') {
						jdsum += 1
					} else if (item.type == '4') {
						lstnum += 1
					} else if (item.type == '5') {
						fxtsum += 1
					}
				})
				$("#tmzf2").html(zfnum + '分')
				$("#tmzfedit").html(zfnum + '分')
				let htmlss = ''
				if(dxtnum>0){
					htmlss+='<label>单选题('+dxtnum+'道)</label>'
				}
				if(dxtsnum>0){
					htmlss+='<label>多选题('+dxtsnum+'道)</label>'
				}
				if(pdtsum>0){
					htmlss+='<label>判断题('+pdtsum+'道)</label>'
				}
				if(jdsum>0){
					htmlss+='<label>简答题('+jdsum+'道)</label>'
				}
				if(lstnum>0){
					htmlss+='<label>论述题('+lstnum+'道)</label>'
				}
				if(fxtsum>0){
					htmlss+='<label>材料分析题('+fxtsum+'道)</label>'
				}
				$("#zchengbox").html(htmlss)
				$("#zchengboxedit").html(htmlss)
			}
			function closeaddpaperssss() {
				$("#tmbox").attr("style", "display: none;")
				
				// 如果是在编辑模式下，关闭组卷界面后返回修改试卷界面
				if (isEditingPaper) {
					// 重新显示修改试卷弹窗
					$("#tmbox3").attr("style","display: flex")
					// 切换到编辑表单视图
					$("#editbox1").removeClass("show").addClass("hide")
					$("#editbox2").removeClass("hide").addClass("show")
					// 重置编辑状态标记
					isEditingPaper = false;
				}
				
				//将计算数据放到 list
				let zfnum = 0 //总分
				let dxtnum = 0 //单选数量
				let dxtsnum = 0 //多选题数量
				let pdtsum = 0 //判断题数量
				let jdsum = 0 //简答题数量
				let lstnum = 0 //论述题数量
				let fxtsum = 0 //材料分析题数量
				if (checkedDate) {
					checkedDate.map((item) => {
						zfnum += parseInt(item.score)
						if (item.type == '0') {
							dxtnum += 1
						} else if (item.type == '1') {
							dxtsnum += 1
						} else if (item.type == '2') {
							pdtsum += 1
						} else if (item.type == '3') {
							jdsum += 1
						} else if (item.type == '4') {
							lstnum += 1
						} else if (item.type == '5') {
							fxtsum += 1
						}
					})
					$("#tmzf2").html(zfnum + '分')
					$("#tmzfedit").html(zfnum + '分')
					let htmlss = ''
					if(dxtnum>0){
						htmlss+='<label>单选题('+dxtnum+'道)</label>'
					}
					if(dxtsnum>0){
						htmlss+='<label>多选题('+dxtsnum+'道)</label>'
					}
					if(pdtsum>0){
						htmlss+='<label>判断题('+pdtsum+'道)</label>'
					}
					if(jdsum>0){
						htmlss+='<label>简答题('+jdsum+'道)</label>'
					}
					if(lstnum>0){
						htmlss+='<label>论述题('+lstnum+'道)</label>'
					}
					if(fxtsum>0){
						htmlss+='<label>材料分析题('+fxtsum+'道)</label>'
					}
					$("#zchengbox").html(htmlss)
					$("#zchengboxedit").html(htmlss)
				} else {
					zfnum = 0
					dxtnum = 0
					dxtsnum = 0
					pdtsum = 0
					jdsum = 0
					lstnum = 0
					fxtsum = 0
				}
				
				// 隐藏所有题目详情
				$(".dabox").hide();
			}
			
			function closeaddpaperssssclearall() {
				$("#tmbox").attr("style", "display: none;")
				checkedDate = []
				//将计算数据放到 list
				let zfnum = 0 //总分
				let dxtnum = 0 //单选数量
				let dxtsnum = 0 //多选题数量
				let pdtsum = 0 //判断题数量
				let jdsum = 0 //简答题数量
				let lstnum = 0 //论述题数量
				let fxtsum = 0 //材料分析题数量
				if (checkedDate) {
					checkedDate.map((item) => {
						zfnum += parseInt(item.score)
						if (item.type == '0') {
							dxtnum += 1
						} else if (item.type == '1') {
							dxtsnum += 1
						} else if (item.type == '2') {
							pdtsum += 1
						} else if (item.type == '3') {
							jdsum += 1
						} else if (item.type == '4') {
							lstnum += 1
						} else if (item.type == '5') {
							fxtsum += 1
						}
					})
					$("#tmzf2").html(zfnum + '分')
					let htmlss = ''
					if(dxtnum>0){
						htmlss+='<label>单选题('+dxtnum+'道)</label>'
					}
					if(dxtsnum>0){
						htmlss+='<label>多选题('+dxtsnum+'道)</label>'
					}
					if(pdtsum>0){
						htmlss+='<label>判断题('+pdtsum+'道)</label>'
					}
					if(jdsum>0){
						htmlss+='<label>简答题('+jdsum+'道)</label>'
					}
					if(lstnum>0){
						htmlss+='<label>论述题('+lstnum+'道)</label>'
					}
					if(fxtsum>0){
						htmlss+='<label>材料分析题('+fxtsum+'道)</label>'
					}
					$("#zchengbox").html(htmlss)
				} else {
					zfnum = 0
					dxtnum = 0
					dxtsnum = 0
					pdtsum = 0
					jdsum = 0
					lstnum = 0
					fxtsum = 0
				}
				
				shitixkid = null
				shitizjid = null
				usertypes = null
				
				getalltm()
			}

			let paperstatus = 'draft' //试卷状态  默认
			
			function selectradio(radio) {
				let alls = $(".radioview")
				for (let i = 0; i < alls.length; i++) {
					$(alls[i]).find("label").attr("class", "")
				}
				$(radio).find("label").attr("class", "radioacc")
				paperstatus = $(radio).attr("data-id")
			}
			function selectradio2(radio) {
				let alls = $(".edits")
				for (let i = 0; i < alls.length; i++) {
					$(alls[i]).find("label").attr("class", "")
				}
				$(radio).find("label").attr("class", "radioacc")
				editstatus = $(radio).attr("data-id")
			}

			function addsubmit() { //添加试卷
				let name = $("#addpapername").val() //试卷名称
				let description = $("#addpapersm").val() //试卷说明
				let projectId = $("#xueke2").val() //学科
				let limitedTime = $("#papersj").val() //考试时间
				let score = $("#paperzf").val() //总分
				let passedScore = $("#paperfsx").val() //通过分数线
				let startTime = $("#addstartdate").val() //开始时间
				let endTime = $("#addenddate").val() //结束时间
				let theWeight = $("#paperqz").val() //权重
				// console.log(startTime,endTime)
				if (!name) {
					cocoMessage.warning(1000, "请输入试卷名称！")
				} else if (!description) {
					cocoMessage.warning(1000, "请输入试卷说明！")
				} else if (projectId == '0') {
					cocoMessage.warning(1000, "请选择学科！")
				} else if (!limitedTime) {
					cocoMessage.warning(1000, "请输入考试时间！")
				} else if (!score) {
					cocoMessage.warning(1000, "请输入总分！")
				} else if (!passedScore) {
					cocoMessage.warning(1000, "请输入通过分数线！")
				}else if (!startTime) {
					cocoMessage.warning(1000, "请选择开始时间！")
				}else if (!endTime) {
					cocoMessage.warning(1000, "请选择结束时间！")
				}else if (!theWeight) {
					cocoMessage.warning(1000, "请输入权重比！")
				}else if(checkedDate.length==0){
					cocoMessage.warning(1000, "请选择试题！")
				} else {
					//添加试卷
					closeaddpaper()
					let json = {
						name: name,
						description: description,
						limitedTime: limitedTime,
						score: score,
						passedScore: passedScore,
						theWeight: theWeight,
						status: paperstatus,
						projectId: projectId,
						startTime: startTime,
						endTime: endTime
					}
					new Promise((resolve, reject)=>{
						//先添加试卷
						$.ajax({
							url: baseurl + "/paper/add",
							type: 'POST',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: JSON.stringify(json),
							dataType: 'json',
							success: (res) => {
								if (res.code == '200') {
									resolve(res.data.id)
								}else{
									reject()
								}
							}
						})
					}).then(res=>{
						let json = {
							id: res,
							subjectIds: []
						}
						checkedDate.map((item)=>{
							json.subjectIds.push(item.id)
						})
						$.ajax({
							url: baseurl + "/paper/combination",
							type: 'PUT',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: JSON.stringify(json),
							dataType: 'json',
							success: (res) => {
								if (res.code == '200') {
									cocoMessage.success(1000, "添加试卷成功！")
									checkedDate = []
									shuaxin()
									$("#addpapername").val('') //试卷名称
									$("#addpapersm").val('') //试卷说明
									$("#xueke2").val('0') //学科
									$("#papersj").val('') //考试时间
									$("#paperzf").val('') //总分
									$("#paperfsx").val('') //通过分数线
									$("#paperqz").val('100') //权重
									$("#addstartdate").val('') //开始时间
									$("#addenddate").val('') //结束时间
									
									// 返回到试卷列表页面
									closeaddpaper()
									
								}else{
									cocoMessage.error(1000, "添加试卷失败！")
								}
								pageindex = 1
								projectId = null
								seachstartTime = null
								seachendTime = null
								getpaperlist()
							}
						})
					}).catch(err=>{
						cocoMessage.error(1000, "添加试卷失败！")
					})
				}
			}

			function openda(item) {
				let state = $(item).attr("data-id")
				if (state == 0) {
					$(item).attr("data-id", "1")
					$(item).attr("class", "zhuan")
					$(item).parent().parent().siblings(".dabox").show()
				} else {
					$(item).attr("data-id", "0")
					$(item).attr("class", "")
					$(item).parent().parent().siblings(".dabox").hide()
				}
			}

			function closeaddpaper() {
				// 显示试卷列表区域
				$(".papaerboxtopview").show()
				$(".filter-control-bar").show()
				$(".paper-table-container").show()
				$("#fyq").show()
				
				// 隐藏新建试卷表单
				$("#pbox1").show()
				$("#pbox2").hide()
			}

			function addpaper() {
				// 隐藏试卷列表区域
				$(".papaerboxtopview").hide()
				$(".filter-control-bar").hide()
				$(".paper-table-container").hide()
				$("#fyq").hide()
				
				// 显示新建试卷表单
				$("#pbox1").hide()
				$("#pbox2").show()
				// 设置默认权重为100
				$("#paperqz").val(100);
			}

			function closeyuepaper() {
				$("#tbox").hide()
			}

			function yuepaper() {
				$("#tbox").attr("style", "display: flex;")
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 						.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}

			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是第最后一页了！")
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！")
					}
				} else {
					pageindex = index
					getpaperlist()
				}
			}

			function setDate2(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d
			}
			
			// 修改试卷时重新组卷
			function showEditPaperComposition() {
				console.log("开始重新组卷，当前已选题目:", checkedDate);
				
				// 设置编辑状态标记
				isEditingPaper = true;
				
				// 关闭修改试卷弹窗
				$("#tmbox3").attr("style","display: none")
				
				// 打开组卷弹窗
				$("#tmbox").attr("style", "display: flex;");
				hideTmDetail(); // 确保打开时显示列表页
				
				// 如果有已选题目，智能设置学科和加载题目
				if (checkedDate && checkedDate.length > 0) {
					// 从已选题目中获取第一个题目的学科ID
					let firstSelectedSubject = checkedDate[0].projectSectionId;
					if (firstSelectedSubject) {
						shitixkid = firstSelectedSubject;
						$("#xueke3").val(firstSelectedSubject);
						
						// 保持其他筛选条件的重置
						shitizjid = null;
						usertypes = null;
						$("#zj1").html('<option value="0">全部章节</option>');
						$("#tmtype").val("");
						$("#tmSearchInput").val("");
						
						$(".filter-tag").removeClass("active");
						$(".filter-tag[onclick=\"quickFilterType('all')\"]").addClass("active");
						
						$('.chenckboxall').attr("data-id", "false").removeClass("ac");
						
						console.log("设置学科ID:", firstSelectedSubject);
						// 加载对应学科的题目并显示已选状态
						getalltm();
						getzjlist();
					} else {
						// 如果已选题目没有学科信息，显示占位符
						showPlaceholder();
					}
				} else {
					// 如果没有已选题目，重置所有筛选条件
					shitixkid = null; 
					shitizjid = null; 
					usertypes = null; 
					
					$("#xueke3").val("0");
					$("#zj1").html('<option value="0">全部章节</option>');
					$("#tmtype").val("");
					$("#tmSearchInput").val("");
					
					$(".filter-tag").removeClass("active");
					$(".filter-tag[onclick=\"quickFilterType('all')\"]").addClass("active");
					
					$('.chenckboxall').attr("data-id", "false").removeClass("ac");
					
					// 显示占位符，不自动加载试题
					showPlaceholder();
				}
				
				// 重新计算总分和题目组成
				jisuan();
				
				// 调整弹窗布局
				setTimeout(function() {
					adjustTmboxLayout();
				}, 150);
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
