<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>红色书籍详情 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    <link rel="stylesheet" href="../css/learning-styles.css">
    
    <style>
        .detail-container {
            background: #f5f5f5;
            min-height: 100vh;
            padding-bottom: 20px;
        }
        
        .detail-header {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            padding: 20px;
            position: relative;
        }
        
        .back-btn {
            position: absolute;
            left: 20px;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(255,255,255,0.2);
            border: none;
            color: white;
            width: 40px;
            height: 40px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
            cursor: pointer;
        }
        
        .detail-title {
            text-align: center;
            font-size: 18px;
            font-weight: 600;
            margin: 0;
            padding: 0 60px;
        }
        
        .book-info {
            background: white;
            margin: 20px;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .book-cover-section {
            display: flex;
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .book-cover {
            width: 120px;
            height: 160px;
            border-radius: 8px;
            overflow: hidden;
            flex-shrink: 0;
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 8px;
        }
        
        .book-cover img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 4px;
            box-shadow: 0 4px 8px rgba(0,0,0,0.3);
        }
        
        .book-meta {
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }
        
        .book-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            line-height: 1.4;
        }
        
        .book-author {
            color: #666;
            font-size: 14px;
            margin-bottom: 8px;
        }
        
        .book-category {
            display: inline-block;
            background: #c00714;
            color: white;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            margin-bottom: 8px;
        }
        
        .book-stats {
            display: flex;
            gap: 15px;
            font-size: 12px;
            color: #999;
        }
        
        .book-description {
            background: white;
            margin: 0 20px 20px;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .section-title {
            font-size: 16px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            padding-bottom: 8px;
            border-bottom: 2px solid #f0f0f0;
        }
        
        .description-content {
            color: #666;
            line-height: 1.6;
            font-size: 14px;
        }
        
        .action-buttons {
            position: fixed;
            bottom: 0;
            left: 0;
            right: 0;
            background: white;
            padding: 15px 20px;
            box-shadow: 0 -2px 8px rgba(0,0,0,0.1);
            display: flex;
            gap: 10px;
        }
        
        .action-btn {
            flex: 1;
            padding: 12px;
            border: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-primary {
            background: #c00714;
            color: white;
        }
        
        .btn-primary:hover {
            background: #a00610;
        }
        
        .btn-secondary {
            background: #f8f9fa;
            color: #333;
            border: 1px solid #e9ecef;
        }
        
        .btn-secondary:hover {
            background: #e9ecef;
        }
        
        .loading-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 50vh;
            color: #666;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 20px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .error-container {
            text-align: center;
            padding: 40px 20px;
            color: #666;
        }
        
        .error-icon {
            font-size: 48px;
            margin-bottom: 20px;
        }
        
        .error-message {
            font-size: 16px;
            margin-bottom: 20px;
        }
        
        .retry-btn {
            background: #c00714;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
        }
    </style>
</head>
<body>
    <div class="detail-container">
        <!-- 头部 -->
        <div class="detail-header">
            <button class="back-btn" onclick="goBack()">
                ←
            </button>
            <h1 class="detail-title">红色书籍详情</h1>
        </div>
        
        <!-- 内容区域 -->
        <div id="contentArea">
            <div class="loading-container">
                <div class="loading-spinner"></div>
                <p>正在加载书籍详情...</p>
            </div>
        </div>
        
        <!-- 操作按钮 -->
        <div class="action-buttons" id="actionButtons" style="display: none;">
            <button class="action-btn btn-secondary" onclick="addToFavorites()">
                ⭐ 收藏
            </button>
            <button class="action-btn btn-primary" onclick="startReading()">
                📖 开始阅读
            </button>
        </div>
    </div>

    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    
    <script>
        // 获取URL参数
        function getUrlParam(name) {
            const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
            const r = window.location.search.substr(1).match(reg);
            return r ? decodeURIComponent(r[2]) : null;
        }
        
        // 返回上一页
        function goBack() {
            if (window.history.length > 1) {
                window.history.back();
            } else {
                window.location.href = '../index.html';
            }
        }
        
        // 格式化时间
        function formatTime(timeStr) {
            if (!timeStr) return '未知时间';
            try {
                const time = new Date(timeStr);
                return time.toLocaleDateString('zh-CN');
            } catch (e) {
                return timeStr;
            }
        }
        
        // 加载书籍详情
        function loadBookDetail(bookId) {
            if (!bookId) {
                showError('缺少书籍ID参数');
                return;
            }
            
            $.ajax({
                url: baseurl + "/web/posts/" + bookId,
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200' && res.data) {
                        renderBookDetail(res.data);
                    } else {
                        showError('获取书籍详情失败：' + (res.message || '未知错误'));
                    }
                },
                error: (err) => {
                    console.error('加载书籍详情失败:', err);
                    showError('网络错误，请稍后重试');
                }
            });
        }
        
        // 渲染书籍详情
        function renderBookDetail(book) {
            // 保存书籍数据供其他函数使用
            window.currentBookData = book;

            const imageUrl = book.thumbPath && book.thumbPath[0] ?
                (baseurl + book.thumbPath[0]) : '../img/book_default.jpg';

            // 检查是否有PDF文件 - 参考PC版的数据结构
            let hasPDF = false;
            let pdfInfo = null;

            // 首先检查 attachmentDtoList (PC版使用的字段)
            if (book.attachmentDtoList && book.attachmentDtoList.length > 0) {
                const pdfAttachment = book.attachmentDtoList.find(item =>
                    item.attachmentPath && item.attachmentPath.toLowerCase().endsWith('.pdf')
                );
                if (pdfAttachment) {
                    hasPDF = true;
                    pdfInfo = {
                        path: pdfAttachment.attachmentPath,
                        fileName: pdfAttachment.fileName || '文档.pdf'
                    };
                }
            }

            // 备用检查 attachmentPath 字段
            if (!hasPDF && book.attachmentPath && book.attachmentPath.length > 0) {
                const pdfPath = book.attachmentPath.find(path => path.toLowerCase().endsWith('.pdf'));
                if (pdfPath) {
                    hasPDF = true;
                    pdfInfo = {
                        path: pdfPath,
                        fileName: book.title + '.pdf'
                    };
                }
            }

            // 保存PDF信息供后续使用
            if (pdfInfo) {
                window.currentBookData.pdfInfo = pdfInfo;
            }

            const html = `
                <div class="book-info">
                    <div class="book-cover-section">
                        <div class="book-cover">
                            <img src="${imageUrl}" alt="${book.title}" onerror="this.src='../img/book_default.jpg'">
                        </div>
                        <div class="book-meta">
                            <div class="book-title">${book.title || '无标题'}</div>
                            <div class="book-author">作者：${book.author || '未知'}</div>
                            <div class="book-category">${book.postName || '红色书籍'}</div>
                            <div class="book-stats">
                                <span>📅 ${formatTime(book.publishedTime || book.createTime)}</span>
                                <span>👁 ${book.clickCount || 0}次阅读</span>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="book-description">
                    <div class="section-title">📚 内容简介</div>
                    <div class="description-content">
                        ${book.content || book.summary || book.description || '暂无内容简介'}
                    </div>
                </div>
            `;
            
            document.getElementById('contentArea').innerHTML = html;

            // 更新操作按钮
            const actionButtons = document.getElementById('actionButtons');
            const readingBtnText = hasPDF ? '📄 阅读PDF' : '📖 开始阅读';
            actionButtons.innerHTML = `
                <button class="action-btn btn-secondary" onclick="addToFavorites()">
                    ⭐ 收藏
                </button>
                <button class="action-btn btn-primary" onclick="startReading()">
                    ${readingBtnText}
                </button>
            `;
            actionButtons.style.display = 'flex';

            // 更新页面标题
            document.title = `${book.title} - 红色书籍详情`;
        }
        
        // 显示错误信息
        function showError(message) {
            const html = `
                <div class="error-container">
                    <div class="error-icon">📚</div>
                    <div class="error-message">${message}</div>
                    <button class="retry-btn" onclick="location.reload()">重新加载</button>
                </div>
            `;
            document.getElementById('contentArea').innerHTML = html;
        }
        
        // 添加到收藏
        function addToFavorites() {
            // 这里可以添加收藏功能的实现
            alert('收藏功能开发中...');
        }
        
        // 开始阅读
        function startReading() {
            const bookId = getUrlParam('id');
            if (bookId && window.currentBookData) {
                // 检查是否有PDF文件信息
                if (window.currentBookData.pdfInfo) {
                    // 跳转到PDF查看器
                    const pdfUrl = baseurl + window.currentBookData.pdfInfo.path;
                    const title = window.currentBookData.pdfInfo.fileName || window.currentBookData.title || '无标题';
                    console.log('跳转到PDF查看器:', pdfUrl);
                    window.location.href = `pdf-viewer.html?url=${encodeURIComponent(pdfUrl)}&title=${encodeURIComponent(title)}`;
                } else {
                    // 跳转到普通阅读页面
                    console.log('跳转到普通阅读页面');
                    window.location.href = `reading.html?id=${bookId}&type=redbook`;
                }
            } else {
                alert('无法获取书籍信息');
            }
        }
        
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            const bookId = getUrlParam('id');
            loadBookDetail(bookId);
        });
    </script>
</body>
</html>
