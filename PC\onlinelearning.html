<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-在线学习</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/onlinelearning.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>
		<style>
			/* 加载动画 */
			.loading-overlay {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(255, 255, 255, 0.9);
				display: flex;
				justify-content: center;
				align-items: center;
				z-index: 9999;
				transition: opacity 0.3s;
			}
			
			.loading-spinner {
				width: 50px;
				height: 50px;
				border: 3px solid #f3f3f3;
				border-top: 3px solid #A65D57;
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}
			
			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}

			/* 内容项动画 */
			.contentitem a {
				transition: all 0.3s ease;
				position: relative;
				overflow: hidden;
			}
			
			.contentitem a:hover {
				transform: translateY(-2px);
				color: #A65D57;
			}
			
			.contentitem a::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 0;
				width: 0;
				height: 2px;
				background-color: #A65D57;
				transition: width 0.3s ease;
			}
			
			.contentitem a:hover::after {
				width: 100%;
			}

			/* 卡片动画效果 */
			.txtitem {
				transition: all 0.3s ease;
				cursor: pointer;
			}
			
			.txtitem:hover {
				transform: translateY(-5px);
				box-shadow: 0 5px 15px rgba(0,0,0,0.1);
			}
			
			.txtitem .topitem img {
				transition: all 0.3s ease;
			}
			
			.txtitem:hover .topitem img {
				transform: scale(1.05);
			}
			
			.txtitem .title {
				transition: color 0.3s ease;
			}
			
			.txtitem:hover .title {
				color: #A65D57;
			}

			/* PDF弹窗动画 */
			.tcbox {
				opacity: 0;
				visibility: hidden;
				transition: all 0.3s ease;
			}
			
			.tcbox.active {
				opacity: 1;
				visibility: visible;
			}

			/* 更多按钮动画 */
			.morebox, .morebox2 {
				transition: all 0.3s ease;
			}
			
			.morebox:hover, .morebox2:hover {
				transform: translateY(-3px);
				box-shadow: 0 3px 10px rgba(0,0,0,0.1);
			}

			/* 文件类型标签动画 */
			.itemtype {
				transition: all 0.3s ease;
			}
			
			.txtitem:hover .itemtype {
				transform: scale(1.1);
			}

			/* 平滑滚动 */
			html {
				scroll-behavior: smooth;
			}

			/* 返回顶部按钮动画 */
			#backtop {
				transition: all 0.3s ease;
				opacity: 0;
				visibility: hidden;
			}
			
			#backtop.visible {
				opacity: 1;
				visibility: visible;
			}
			
			#backtop:hover {
				transform: translateY(-5px);
			}

			/* 页面加载动画 */
			body {
				opacity: 0;
				animation: fadeIn 0.5s ease forwards;
			}
			
			@keyframes fadeIn {
				from { opacity: 0; }
				to { opacity: 1; }
			}

			/* 标题动画 */
			.cctitle {
				position: relative;
				overflow: hidden;
			}
			
			.cctitle::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 0;
				width: 0;
				height: 2px;
				background-color: #A65D57;
				transition: width 0.5s ease;
			}
			
			.cctitle:hover::after {
				width: 100%;
			}
		</style>
	</head>
	<body class="index">
		<!-- 添加加载动画 -->
		<div class="loading-overlay">
			<div class="loading-spinner"></div>
		</div>

		<div class="tcbox" id="tcbox">
			
			<div class="topviewsss">
				<a onclick="closetc()">关闭</a>
				<label id="pdfname"></label>
			</div>
			<div class="panel-body pdfbox">
			    <a id="pdf" class="media" href=""></a>  
			</div>
		</div>
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<!-- <div class="topbox">
			<div><label>在线学习</label></div>
		</div> -->
		<div class="content">
			<div class="contenttopview">
				<div class="contentitem" id="classbox">
				</div>
				<!-- <div class="contentitem sss">
					<div class="ssdiv">
						<div class="select" onclick="showselect()">
							<label id="selecttxt">书籍</label>
							<img src="img/ssx.png" />
							<img src="img/ssb.png" />
						</div>
						<input />
						<div class="ss">搜索</div>
					</div>
					<div class="dztsg">电子图书馆</div>
					<div class="opbox" id="select">
						<div>书籍</div>
						<div>红色游学</div>
						<div>书籍</div>
						<div>红色游学</div>
					</div>
				</div> -->
			</div>
			<div id="htmlbox">
				
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			function closetc(){
				$("#tcbox").hide()
				this.tiaozhuan()
			}
			let userinfo = sessionStorage.getItem("userinfo")
			$("#select div").on('click',function(){
				$("#selecttxt").html($(this).html())
				$("#select").hide()
			})
			function showselect(){
				$("#select").show()
			}
			
			let classdate = null//当前页面的分类数据
			//获取网站分类
			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					if (res.data[i].id == getUrlParam('id')||res.data[i].redirectUrl=='onlinelearning.html') {
			// 						classdate = res.data[i]
			// 						pflid = res.data[i].id
			// 						html += '<a class="menuitem active" href="' + res.data[i].redirectUrl + '?id=' + res
			// 							.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					} else {
			// 						html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res.data[
			// 							i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					}
			// 				}
			// 				getzylist()
			// 			}
			// 		}
			// 	})
			// }
			function getzylist(classdate){
				let classhtml = ""
				classdate.children.forEach((item)=>{
					classhtml += '<a href="'+item.redirectUrl+'?id='+item.id+'">'+item.name+'</a>'
				})
				$("#classbox").html(classhtml)
				let hssjid = classdate.children[0].id //红色书籍分类ID
				let jxzyid = classdate.children[1].id //教学资源分类ID
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data:{
						categoryId: hssjid,
						pageSize: 14
					},
					dataType: 'json',
					success: (res) => {
						if(res.code == '200'){
							$("#htmlbox").append('<div class="cctitle"><label>'+classdate.children[0].name+'</label></div>')
							
							let newhtml = '<div class="sjbox">'
								
							for(let k = 0;k < res.data.list.length;k++){
								newhtml += '<div class="txtitem" onclick="ininfo(this)" data-id="'+res.data.list[k].id+'">'+
										'<div class="topitem">'
										if(res.data.list[k].thumbPath!=null){
											newhtml+='<img src="'+baseurl+res.data.list[k].thumbPath[0]+'"/>'
										}
										newhtml+='<div class="icobox">'+
											'<label><img src="img/gkl.png"/>'+res.data.list[k].clickCount+'</label></div></div>'+
									'<div class="bottomitem">'+
										'<div class="title">'+res.data.list[k].title+'</div>'+
										'<div class="zz">作者: '+res.data.list[k].author+'</div><div class="zz">'+res.data.list[k].postName+'</div></div></div>'
							}
							newhtml += '<div class="moretxtitem"><div class="morebox" onclick="inlist()"><div class="mb">'+
										'<div><img src="img/more2.png"/></div><div>查看全部</div></div></div></div>'
							newhtml+='</div>'
							$("#htmlbox").append(newhtml)
							getzyzy(jxzyid,classdate)
						}
					}
				})
			}
			function getzyzy(jxzyid,classdate){
				// console.log(jxzyid)
				$.ajax({
					url: baseurl + "/web/course",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data:{
						categoryId: jxzyid,
						pageSize: 14
					},
					dataType: 'json',
					success: (res) => {
						if(res.code == '200'){
							$("#htmlbox").append('<div class="cctitle"><label>'+classdate.children[1].name+'</label></div>')
							let newhtml = '<div class="sjbox">'
							for(let k = 0;k < res.data.list.length;k++){
								if(res.data.list[k].attachType==='pdf'){
									newhtml += '<div class="txtitem" onclick="showpdf(this)" data-cid="'+res.data.list[k].courceId+'" data-id="'+res.data.list[k].metaId+'"><div class="topitem">'+
										'<div class="itemtype '+res.data.list[k].attachType+'">'+res.data.list[k].attachType+'</div>'
										if(res.data.list[k].coverPath!=null){
											newhtml+='<img src="'+baseurl+res.data.list[k].coverPath[0]+'"/>'
										}
										newhtml+='<div class="icobox">'+
											'<label><img class="zzico" src="img/fbr.png"/><span>'+res.data.list[k].author+'</span></label>'+
											'<label><img class="gklico" src="img/gkl.png"/>'+res.data.list[k].view+'</label></div></div>'+
									'<div class="bottomitem">'+
										'<div class="title">'+res.data.list[k].title+'</div>'+
										'<div class="zz">'+res.data.list[k].introduction+'</div></div></div>'
								}else{
									newhtml += '<div class="txtitem" onclick="inxx(this)" data-id="'+res.data.list[k].metaId+'"><div class="topitem">'+
										'<div class="itemtype '+res.data.list[k].attachType+'">'+res.data.list[k].attachType+'</div>'
										if(res.data.list[k].coverPath!=null){
											newhtml+='<img src="'+baseurl+res.data.list[k].coverPath[0]+'"/>'
										}
										newhtml +='<div class="icobox">'+
											'<label><img class="zzico" src="img/fbr.png"/><span>'+res.data.list[k].author+'</span></label>'+
											'<label><img class="gklico" src="img/gkl.png"/>'+res.data.list[k].view+'</label></div></div>'+
									'<div class="bottomitem">'+
										'<div class="title">'+res.data.list[k].title+'</div>'+
										'<div class="zz">'+res.data.list[k].introduction+'</div></div></div>'
								}
								
							}
							newhtml += '<div class="moretxtitem2" onclick="inss()"><div class="morebox2">'+
									'<div class="mb2"><div><img src="img/more1.png"/></div>'+
										'<div>查看全部</div></div></div></div>'
							newhtml+='</div>'
							$("#htmlbox").append(newhtml)
						}
					}
				})
			}
			function inlist(){
				window.location.href = "onlinelearning2.html"
			}
			function ininfo(item){
				window.location.href = "onlinelearning5.html?id="+$(item).attr("data-id")
			}
			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}
			let time1 = null
			let pflid = null
			let xkid = null
			let infoid = null
			function tiaozhuan() {
				if(JSON.parse(userinfo).roleName == '学生'){
					let time2 = Date.now()
					let value = time2 - time1
					var days = parseInt(value / (1000 * 60 * 60 * 24))
					var hours = parseInt((value % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
					var minutes = parseInt((value % (1000 * 60 * 60)) / (1000 * 60))
					var seconds = Math.floor((value % (1000 * 60)) / 1000)
					let json = {
						infoId: infoid, //信息id
						categoryId: pflid, //所属分类id
						totalInfo: "1", //总时长，总页数
						positioning: days+"天"+hours+"时"+minutes+"分"+seconds+"秒", //学习了多久，多少页    
						progress: "", //进度 百分比
						type: '在线学习2',
						learningTime: value,
						sectionId: xkid//学科ID
					}
					window.localStorage.setItem("jilu",JSON.stringify(json))
					if(window.localStorage.getItem("jilu")){
						$.ajax({
							url: baseurl + "/study/record/add",
							type: 'post',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: window.localStorage.getItem("jilu"),
							dataType: 'json',
							success: (res) => {
								window.localStorage.clear()
							}
						})
					}
				}
			}
			function inss(){
				window.location.href = 'onlinelearning3.html'
			}
			function showpdf(pdf){
				if(!userinfo){
					window.location.href ='https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				$.ajax({
					url: baseurl + "/course/view/"+$(pdf).attr("data-cid"),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
					}
				})
				$.ajax({
					url: baseurl + "/course/meta/"+$(pdf).attr("data-id"),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							$("#pdfname").html(res.data.cmsResourcesCourseMetaList[0].attachName)
							$("#tcbox").show()
							$("#pdf").attr("href",baseurl + res.data.cmsResourcesCourseMetaList[0].attachPath)
							$('a.media').media()
							time1 = Date.now()
							xkid = res.data.projectId
							infoid = $(pdf).attr("data-id")
						}
					}
				})
			}
			function inxx(item){
				window.location.href = "onlinelearning4.html?id="+$(item).attr("data-id")
			}
			$(function(){
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				if(userinfo){
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
					if(window.localStorage.getItem("jilu")){
						$.ajax({
							url: baseurl + "/study/record/add",
							type: 'post',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: window.localStorage.getItem("jilu"),
							dataType: 'json',
							success: (res) => {
								window.localStorage.clear()
							}
						})
					}
				}else{
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				getclass()
				getclassch()
				getfooterlink()
			})
			function getclassch(){
				$.ajax({
					url: baseurl + "/web/category/teacher",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							getzylist(res.data[0])
						}
					}
				})
			}
			function getfooterlink(){
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item)=>{
								html+='<a href="'+item.url+'" target="_blank">'+item.name+'</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
		
			// 添加新的交互脚本
			$(document).ready(function() {
				// 页面加载完成后隐藏加载动画
				setTimeout(function() {
					$('.loading-overlay').fadeOut();
				}, 500);
				
				// 图片懒加载
				$('.txtitem img').each(function() {
					$(this).attr('data-src', $(this).attr('src'));
					$(this).attr('src', '');
				});
				
				function lazyLoad() {
					$('.txtitem img').each(function() {
						if ($(this).offset().top < $(window).height() + $(window).scrollTop()) {
							$(this).attr('src', $(this).attr('data-src'));
						}
					});
				}
				
				lazyLoad();
				$(window).on('scroll', lazyLoad);
				
				// 平滑滚动
				$('a[href*="#"]').on('click', function(e) {
					e.preventDefault();
					$('html, body').animate({
						scrollTop: $($(this).attr('href')).offset().top
					}, 500);
				});
				
				// 返回顶部按钮动画
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$('#backtop').addClass('visible');
					} else {
						$('#backtop').removeClass('visible');
					}
				});

				// 修改 showpdf 函数添加动画
				window.showpdf = function(pdf) {
					if(!userinfo) {
						window.location.href ='https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html';
						return;
					}
					
					$.ajax({
						url: baseurl + "/course/view/"+$(pdf).attr("data-cid"),
						type: 'GET',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						dataType: 'json',
						success: (res) => {}
					});
					
					$.ajax({
						url: baseurl + "/course/meta/"+$(pdf).attr("data-id"),
						type: 'GET',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						dataType: 'json',
						success: (res) => {
							if (res.code == '200') {
								$("#pdfname").html(res.data.cmsResourcesCourseMetaList[0].attachName);
								$("#tcbox").addClass('active').show();
								$("#pdf").attr("href", baseurl + res.data.cmsResourcesCourseMetaList[0].attachPath);
								$('a.media').media();
								time1 = Date.now();
								xkid = res.data.projectId;
								infoid = $(pdf).attr("data-id");
							}
						}
					});
				};

				// 修改关闭函数添加动画
				window.closetc = function() {
					$("#tcbox").removeClass('active').fadeOut(300);
					tiaozhuan();
				};

				// 内容加载动画
				function animateContent() {
					$('.txtitem').each(function(index) {
						$(this).css({
							'animation': `fadeInUp 0.5s ease forwards ${index * 0.1}s`,
							'opacity': '0'
						});
					});
				}

				// 监听内容变化并触发动画
				const observer = new MutationObserver(function(mutations) {
					mutations.forEach(function(mutation) {
						if (mutation.addedNodes.length) {
							animateContent();
						}
					});
				});

				observer.observe(document.getElementById('htmlbox'), {
					childList: true,
					subtree: true
				});
			});
		</script>
	</body>
</html>
