<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
		<title>思政一体化平台-在线学习-红色书籍</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/onlinelearning.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>
		<style>
			html, body {
				overflow-x: hidden;
				max-width: 100%;
			}
			
			/* 确保下拉菜单样式正确 */
			.menuitemaaa {
				position: relative;
			}
			
			.menuitemaaa .itemcboxaaa {
				display: none;
				position: absolute;
				top: 100%;
				left: 0;
				width: 100%;
				background: rgb(126,8,11);
				padding: 0.625rem;
				z-index: 999;
			}
			
			.menuitemaaa:hover .itemcboxaaa {
				display: block;
			}
			
			.menuaaaa {
				cursor: pointer;
				font-size: 0.833333rem;
				color: #FFFFFF;
				display: flex;
				height: 100%;
				width: 100%;
				align-items: center;
				justify-content: center;
			}
			
			.itema2aaa {
				display: block;
				width: 100%;
				text-align: center;
				font-size: 0.833333rem;
				color: #FFFFFF;
				line-height: 1.8rem;
			}
			
			.itema2aaa:hover {
				color: #ffd05f;
				font-weight: bold;
			}
			
			/* PDF阅读器美化样式 */
			.tcbox {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, 0.85);
				z-index: 999;
				display: none;
			}

			.topviewsss {
				position: relative;
				height: 60px;
				background-color: #d71c1c;
				color: white;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 24px;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
				box-sizing: border-box;
				width: 100%;
				overflow: hidden;
			}

			.topviewsss a {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 8px 16px;
				border-radius: 4px;
				background-color: rgba(255, 255, 255, 0.2);
				color: white;
				cursor: pointer;
				transition: all 0.3s ease;
				font-size: 16px;
				flex-shrink: 0;
				margin-left: 10px;
				z-index: 10;
			}

			.topviewsss a:hover {
				background-color: rgba(255, 255, 255, 0.3);
			}

			.topviewsss label {
				font-size: 18px;
				font-weight: bold;
				flex: 1;
				text-align: center;
				text-overflow: ellipsis;
				white-space: nowrap;
				overflow: hidden;
				padding: 0 20px;
			}

			.pdfbox {
				height: calc(100% - 60px);
				width: 100%;
				background-color: #f5f5f5;
				position: relative;
				overflow: hidden;
				display: flex;
			}

			.pdfbox iframe {
				width: 100%;
				height: 100%;
				border: none;
			}

			/* PDF 目录侧边栏 */
			.pdf-sidebar {
				width: 280px;
				height: 100%;
				background-color: #f8f8f8;
				border-right: 1px solid #ddd;
				overflow-y: auto;
				transition: all 0.3s ease;
				display: none;
				flex-shrink: 0;
			}

			.pdf-sidebar-visible {
				display: block;
			}

			.pdf-sidebar-header {
				padding: 15px;
				background-color: #d71c1c;
				color: white;
				font-weight: bold;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			.pdf-sidebar-title {
				font-size: 16px;
			}

			.pdf-sidebar-close {
				cursor: pointer;
				font-size: 20px;
			}

			.pdf-outline {
				padding: 10px 0;
			}

			.pdf-outline-item {
				padding: 8px 15px;
				cursor: pointer;
				transition: all 0.2s;
				border-left: 3px solid transparent;
			}

			.pdf-outline-item:hover {
				background-color: #eee;
				border-left-color: #d71c1c;
			}

			.pdf-outline-item.active {
				background-color: rgba(215, 28, 28, 0.1);
				border-left-color: #d71c1c;
				font-weight: bold;
			}

			.pdf-main-container {
				flex: 1;
				height: 100%;
				position: relative;
				overflow: hidden;
			}

			/* 导航控件 */
			.pdf-controls {
				position: absolute;
				bottom: 20px;
				left: 50%;
				transform: translateX(-50%);
				background-color: rgba(0, 0, 0, 0.6);
				border-radius: 30px;
				padding: 8px 15px;
				display: flex;
				align-items: center;
				z-index: 100;
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
			}

			.pdf-control-btn {
				background-color: transparent;
				border: none;
				color: white;
				font-size: 14px;
				padding: 5px 10px;
				margin: 0 5px;
				cursor: pointer;
				border-radius: 4px;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.2s;
			}

			.pdf-control-btn:hover {
				background-color: rgba(255, 255, 255, 0.2);
			}

			.pdf-page-info {
				color: white;
				margin: 0 10px;
				min-width: 60px;
				text-align: center;
			}

			.pdf-zoom-controls {
				margin: 0 5px;
			}

			.pdf-view-mode-btn {
				margin-left: 10px;
				position: relative;
			}

			.pdf-view-mode-btn:after {
				content: "";
				position: absolute;
				left: -5px;
				top: 5px;
				height: 20px;
				width: 1px;
				background-color: rgba(255, 255, 255, 0.3);
			}

			/* 页面翻转效果 */
			.pdf-page-container {
				perspective: 2000px;
				height: 100%;
				position: relative;
				overflow: auto;
				background-color: #f0f0f0;
			}

			.pdf-book {
				width: 100%;
				height: 100%;
				position: relative;
				transform-style: preserve-3d;
				transform: translateZ(0);
				transition: transform 0.5s;
				backface-visibility: hidden;
				background-color: #f0f0f0;
				overflow: auto;
				padding: 20px 0;
			}

			.pdf-page {
				position: relative;
				width: 100%;
				height: 100%;
				background-color: white;
				overflow: hidden;
				box-sizing: border-box;
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 10px;
			}

			.pdf-page canvas {
				max-width: 100%;
				max-height: 100%;
				box-shadow: 0 0 10px rgba(0,0,0,0.1);
				background-color: white;
			}

			.pdf-page.turning {
				animation: page-turn 1.5s ease-in-out forwards;
				box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
			}

			@keyframes page-turn {
				0% { transform: rotateY(0deg); }
				100% { transform: rotateY(-180deg); }
			}

			/* 双页模式 */
			.pdf-book.double-page-mode {
				display: flex;
				width: 100%;
				height: 100%;
				justify-content: center;
				align-items: flex-start;
			}

			.pdf-book.double-page-mode .pdf-page {
				position: relative;
				width: 50%;
				height: auto;
				margin: 0;
				float: left;
				border-right: none;
				box-shadow: none;
			}

			.pdf-book.double-page-mode .pdf-page:nth-child(2n) {
				border-right: none;
				border-left: none;
			}
			
			.pdf-loading {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				text-align: center;
				width: 200px;
				background-color: rgba(255, 255, 255, 0.9);
				border-radius: 10px;
				padding: 20px;
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
				z-index: 120;
			}

			.pdf-loading-spinner {
				width: 60px;
				height: 60px;
				border: 5px solid rgba(215, 28, 28, 0.2);
				border-top: 5px solid #d71c1c;
				border-radius: 50%;
				animation: spin 1.2s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
				margin: 0 auto 15px;
			}
			
			.pdf-loading-text {
				color: #333;
				font-size: 16px;
				font-weight: 500;
				margin-top: 10px;
			}
			
			.pdf-loading-progress {
				height: 6px;
				background-color: rgba(215, 28, 28, 0.2);
				border-radius: 3px;
				margin-top: 10px;
				overflow: hidden;
			}
			
			.pdf-loading-progress-bar {
				height: 100%;
				background-color: #d71c1c;
				border-radius: 3px;
				width: 0%;
				animation: progress-animation 2s ease-in-out infinite;
			}

			/* 添加加载动画改进 */
			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}

			.pdf-loading-progress-text {
				position: absolute;
				left: 0;
				right: 0;
				text-align: center;
				font-size: 12px;
				color: #555;
				line-height: 6px;
				margin-top: -3px;
			}

			/* 添加PDF优化选项样式 */
			.pdf-optimizations {
				margin-top: 15px;
				padding-top: 10px;
				border-top: 1px solid #eee;
				text-align: left;
			}

			.pdf-optimizations-title {
				font-size: 14px;
				color: #333;
				margin-bottom: 10px;
			}

			.pdf-optimization-option {
				display: flex;
				align-items: center;
				margin-bottom: 8px;
			}

			.pdf-optimization-option input {
				margin-right: 8px;
			}

			.pdf-optimization-option label {
				font-size: 13px;
				color: #555;
			}

			.pdf-optimization-option label small {
				display: block;
				font-size: 11px;
				color: #888;
				margin-top: 2px;
			}

			/* 重试按钮 */
			.pdf-retry {
				background-color: #d71c1c;
				border: none;
				color: white;
				padding: 8px 15px;
				margin-top: 10px;
				border-radius: 4px;
				cursor: pointer;
				font-size: 14px;
				display: none;
			}

			.pdf-retry:hover {
				background-color: #c51818;
			}

			/* PDF加载超时提示 */
			.pdf-timeout-container {
				display: none;
				margin-top: 15px;
			}

			/* PDF遮罩层 */
			.pdf-placeholder {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				display: flex;
				flex-direction: column;
				align-items: center;
				justify-content: center;
				background-color: #f9f9f9;
				z-index: 10;
			}

			.pdf-placeholder-icon {
				width: 80px;
				height: 80px;
				margin-bottom: 20px;
				opacity: 0.7;
			}

			.pdf-placeholder-text {
				font-size: 16px;
				color: #333;
				text-align: center;
				max-width: 80%;
			}

			.pdf-placeholder-button {
				margin-top: 20px;
				background-color: #d71c1c;
				color: white;
				border: none;
				padding: 10px 15px;
				border-radius: 4px;
				cursor: pointer;
			}

			/* 移动设备适配 */
			@media screen and (max-width: 768px) {
				.pdf-sidebar {
					position: fixed;
					left: -280px;
					top: 0;
					bottom: 0;
					z-index: 150;
					box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
					transition: left 0.3s ease;
					width: 80%;
					max-width: 320px;
				}

				.pdf-sidebar.pdf-sidebar-visible {
					left: 0;
				}

				.pdf-controls {
					flex-wrap: wrap;
					width: 90%;
					justify-content: center;
					background-color: rgba(0, 0, 0, 0.7);
					padding: 10px;
				}

				.pdf-book.double-page-mode {
					flex-direction: column;
				}

				.pdf-book.double-page-mode .pdf-page {
					width: 100%;
					height: 50%;
				}
				
				.pdf-view-mode-btn {
					display: none;
				}
				
				/* 移动端滑动区域 */
				.swipe-area {
					position: absolute;
					top: 0;
					bottom: 0;
					width: 100%;
					z-index: 20;
				}
				
				.pdf-control-btn {
					padding: 8px 15px; 
					margin: 5px;
				}
				
				/* 移动端目录按钮更明显 */
				#toggle-sidebar {
					background: rgba(215, 28, 28, 0.7) !important;
					border-radius: 5px !important;
					padding: 8px 15px !important;
				}
				
				.topviewsss label {
					font-size: 16px;
					padding: 0 10px;
				}
				
				/* 移动端章节目录优化 */
				.pdf-outline-item {
					padding: 12px 15px;
					border-bottom: 1px solid #eee;
				}
			}
			
			/* 添加滑动翻页区域 */
			.swipe-area {
				position: absolute;
				top: 60px;
				left: 0;
				right: 0;
				bottom: 80px;
				z-index: 10;
			}
			
			.swipe-left, .swipe-right {
				position: absolute;
				top: 0;
				bottom: 0;
				width: 50%;
				z-index: 10;
				cursor: pointer;
			}
			
			.swipe-left {
				left: 0;
			}
			
			.swipe-right {
				right: 0;
			}

			/* 阅读按钮美化 */
			#sjbtn {
				background-color: #d71c1c;
				border: none;
				color: white;
				padding: 12px 30px;
				border-radius: 4px;
				font-size: 16px;
				cursor: pointer;
				transition: all 0.3s;
				display: inline-flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0 2px 5px rgba(215, 28, 28, 0.3);
			}

			#sjbtn:hover {
				background-color: #c51818;
				box-shadow: 0 4px 8px rgba(215, 28, 28, 0.4);
			}

			#sjbtn:before {
				content: "";
				display: inline-block;
				width: 20px;
				height: 20px;
				background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="white"><path d="M6 2c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6H6zm0 2h7v5h5v11H6V4zm2 8v2h8v-2H8zm0 4v2h5v-2H8z"/></svg>') no-repeat;
				background-size: contain;
				margin-right: 8px;
			}
			
			/* 响应式布局优化 */
			@media screen and (max-width: 768px) {
				.sjinfoview {
					flex-direction: column;
				}
				
				.sjinfoleft, .sjinforight {
					width: 100%;
				}
				
				.sjinfoleft {
					text-align: center;
					margin-bottom: 20px;
				}
				
				.swiper {
					width: 100%;
				}
				
				.swiper-slide {
					width: 90%;
				}
			}
			
			.content {
				overflow: hidden;
				max-width: 100%;
				padding: 0 15px;
			}
			
			footer {
				width: 100%;
				overflow: hidden;
			}

			/* 添加提示消息样式 */
			.pdf-message {
				position: fixed;
				bottom: 80px;
				left: 50%;
				transform: translateX(-50%);
				padding: 10px 20px;
				background-color: rgba(0, 0, 0, 0.7);
				color: white;
				border-radius: 5px;
				z-index: 1000;
				font-size: 14px;
				box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
				animation: fadeInOut 3s ease;
				text-align: center;
				min-width: 200px;
			}
			
			.pdf-message.error {
				background-color: rgba(215, 28, 28, 0.9);
			}
			
			@keyframes fadeInOut {
				0% { opacity: 0; transform: translate(-50%, 20px); }
				10% { opacity: 1; transform: translate(-50%, 0); }
				90% { opacity: 1; transform: translate(-50%, 0); }
				100% { opacity: 0; transform: translate(-50%, -20px); }
			}
			
			.pdf-outline-header {
				background-color: #d71c1c;
				color: white;
				padding: 12px 15px;
				font-weight: bold;
				margin-bottom: 10px;
			}
			
			/* 激活的目录项 */
			.pdf-outline-item.active {
				background-color: rgba(215, 28, 28, 0.1);
				border-left-color: #d71c1c !important;
				font-weight: bold;
			}
			
			/* 滚动条美化 */
			.pdf-sidebar::-webkit-scrollbar {
				width: 6px;
			}
			
			.pdf-sidebar::-webkit-scrollbar-track {
				background: #f1f1f1;
			}
			
			.pdf-sidebar::-webkit-scrollbar-thumb {
				background: #d71c1c;
				border-radius: 10px;
			}
			
			/* 相关推荐书籍封面A4比例样式 */
			.swiper-slide {
				height: auto;
			}
			
			.swiper-slide a {
				display: block;
				width: 100%;
				text-decoration: none;
			}
			
			.swiper-slide a img {
				width: 100%;
				aspect-ratio: 1/1.414; /* A4纸张比例 1:1.414 */
				object-fit: cover;
				background-color: #f5f5f5;
				box-shadow: 0 2px 6px rgba(0,0,0,0.1);
				border-radius: 4px;
				transition: transform 0.3s ease;
			}
			
			.swiper-slide a:hover img {
				transform: scale(1.05);
			}
			
			.swiper-slide .sm {
				margin-top: 10px;
				text-align: center;
				font-size: 14px;
				color: #333;
				max-height: 40px;
				overflow: hidden;
				text-overflow: ellipsis;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
			}

			/* 改进加载动画效果 */
			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}

			@keyframes pulse {
				0% { opacity: 0.6; }
				50% { opacity: 1; }
				100% { opacity: 0.6; }
			}

			/* 更美观的加载器 */
			.modern-loading {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				width: 260px;
				padding: 25px;
				background-color: white;
				border-radius: 10px;
				box-shadow: 0 5px 25px rgba(0, 0, 0, 0.15);
				text-align: center;
				z-index: 1000;
			}

			.modern-loading-spinner {
				width: 70px;
				height: 70px;
				margin: 0 auto 20px;
				border: 6px solid rgba(215, 28, 28, 0.1);
				border-left: 6px solid #d71c1c;
				border-radius: 50%;
				animation: spin 1.2s linear infinite;
			}

			.modern-loading-title {
				color: #333;
				font-size: 18px;
				font-weight: 600;
				margin-bottom: 15px;
			}

			.modern-loading-text {
				color: #666;
				font-size: 14px;
				margin-bottom: 15px;
			}

			.modern-loading-progress {
				height: 8px;
				background-color: rgba(215, 28, 28, 0.1);
				border-radius: 4px;
				overflow: hidden;
				margin-bottom: 10px;
			}

			.modern-loading-progress-bar {
				height: 100%;
				width: 0%;
				background-color: #d71c1c;
				border-radius: 4px;
				transition: width 0.3s ease;
			}

			.modern-loading-stats {
				display: flex;
				justify-content: space-between;
				font-size: 12px;
				color: #888;
				margin-bottom: 15px;
			}

			.modern-loading-options {
				margin-top: 10px;
				text-align: left;
				padding-top: 10px;
				border-top: 1px solid #eee;
			}

			.loading-option {
				display: flex;
				align-items: center;
				margin-bottom: 8px;
			}

			.loading-option input {
				margin-right: 8px;
			}

			.loading-option label {
				font-size: 13px;
				color: #666;
			}

			/* 页面加载指示器 */
			.page-loading-indicator {
				display: flex;
				align-items: center;
				justify-content: center;
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background-color: rgba(255, 255, 255, 0.9);
				z-index: 10;
			}

			.page-loading-spinner {
				width: 40px;
				height: 40px;
				border: 4px solid rgba(215, 28, 28, 0.1);
				border-left: 4px solid #d71c1c;
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}

			/* 页面进度指示器 */
			.pdf-page-progress {
				position: fixed;
				bottom: 70px;
				left: 50%;
				transform: translateX(-50%);
				background-color: rgba(0, 0, 0, 0.7);
				color: white;
				padding: 8px 15px;
				border-radius: 20px;
				font-size: 14px;
				z-index: 999;
			}

			/* 添加页面转换动画 */
			.pdf-page.changing {
				animation: pageChange 0.3s ease-in-out;
			}

			@keyframes pageChange {
				0% { opacity: 0.7; transform: translateY(10px); }
				100% { opacity: 1; transform: translateY(0); }
			}
		</style>
	</head>
	<body class="index">
		<div class="tcbox" id="tcbox">
			<div class="topviewsss">
				<button id="toggle-sidebar" style="background:none; border:none; color:white; cursor:pointer; padding:5px 10px; margin-right:10px; display:flex; align-items:center;">
					<svg width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M1 1H19M1 8H19M1 15H19" stroke="white" stroke-width="2" stroke-linecap="round"/>
					</svg>
					<span style="margin-left:5px;">目录</span>
				</button>
				<label id="pdfname"></label>
				<a onclick="closetc()">关闭 ×</a>
			</div>
			<div class="panel-body pdfbox">
				<div class="pdf-sidebar" id="pdf-sidebar">
					<div class="pdf-sidebar-header">
						<div class="pdf-sidebar-title">目录</div>
						<div class="pdf-sidebar-close" id="sidebar-close">×</div>
					</div>
					<div class="pdf-outline" id="pdf-outline">
						<!-- 目录内容将由JS动态生成 -->
						<div class="pdf-outline-loading" style="padding:15px; text-align:center;">
							加载目录中...
						</div>
					</div>
				</div>
				
				<div class="pdf-main-container">
					<div class="modern-loading" id="pdf-loading">
						<div class="modern-loading-spinner"></div>
						<div class="modern-loading-title">加载PDF文档</div>
						<div class="modern-loading-text" id="pdf-loading-text">正在准备加载...</div>
						<div class="modern-loading-progress">
							<div class="modern-loading-progress-bar" id="pdf-loading-progress-bar"></div>
						</div>
						<div class="modern-loading-stats">
							<span id="pdf-loading-progress-text">0%</span>
							<span id="pdf-loading-size">0KB / 0KB</span>
						</div>
						<div class="modern-loading-options" id="pdf-timeout" style="display:none">
							<p style="margin:0 0 10px;font-size:14px;color:#d71c1c;">加载时间较长，您可以:</p>
							<div class="loading-option">
								<input type="checkbox" id="opt-lazy-loading" checked>
								<label for="opt-lazy-loading">渐进式加载 (先加载前面页面)</label>
							</div>
							<div class="loading-option">
								<input type="checkbox" id="opt-low-quality" checked>
								<label for="opt-low-quality">低质量模式 (加快加载速度)</label>
							</div>
						</div>
						<button class="pdf-retry" id="pdf-retry">重新加载</button>
					</div>
					
					<div class="pdf-page-container" id="pdf-container">
						<div class="pdf-book" id="pdf-book">
							<!-- PDF页面将由JS动态加载 -->
						</div>
						<div class="pdf-page-progress" id="pdf-page-progress" style="display:none">
							页面加载中 <span id="pdf-pages-loaded">0</span>/<span id="pdf-pages-total">0</span>
						</div>
					</div>
					
					<div class="pdf-controls">
						<button class="pdf-control-btn" id="prev-page">
							<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M10 12L6 8L10 4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
							上一页
						</button>
						<div class="pdf-page-info" id="page-info">0 / 0</div>
						<button class="pdf-control-btn" id="next-page">
							下一页
							<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M6 4L10 8L6 12" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</button>
						<div class="pdf-zoom-controls">
							<button class="pdf-control-btn" id="zoom-out">
								<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M2 8H14" stroke="white" stroke-width="2" stroke-linecap="round"/>
								</svg>
							</button>
							<button class="pdf-control-btn" id="zoom-in">
								<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M8 2V14" stroke="white" stroke-width="2" stroke-linecap="round"/>
									<path d="M2 8H14" stroke="white" stroke-width="2" stroke-linecap="round"/>
								</svg>
							</button>
						</div>
						<button class="pdf-control-btn pdf-view-mode-btn" id="toggle-view-mode">
							<svg width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
								<rect x="1" y="1" width="8" height="14" rx="1" stroke="white" stroke-width="1.5"/>
								<rect x="11" y="1" width="8" height="14" rx="1" stroke="white" stroke-width="1.5"/>
							</svg>
						</button>
					</div>
				</div>
			</div>
		</div>
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
					<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>
					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
					<!-- 菜单项将由JS动态生成 -->
				</div>
			</div>
		</div>
		<div class="contenttitlebox">
			<div>
				<a id="zxxx" href="onlinelearning.html">在线学习</a>
				<img src="img/jtright.png" />
				<label class="aa" id="hsname"></label>
			</div>
		</div>
		<div class="content">
			<div class="sjinfoview">
				<div class="sjinfoleft">
					<img id="bookimg" src="" alt="书籍封面" />
				</div>
				<div class="sjinforight">
					<div class="sjname" id="sjname"></div>
					<div class="sjstr">
						<label>
							<img src="img/sjcbs.png" alt="出版社" />
							<span id="cbs"></span>
						</label>
						<label>
							<img src="img/sjfl.png" alt="分类" />
							<span id="fl"></span>
						</label>
						<label>
							<img src="img/sjyj.png" alt="阅读量" />
							<span id="yj"></span>
						</label>
					</div>
					<div class="sjjj" id="sjjj">

					</div>
					<label id="sjbtn" onclick="showpdf()">立即阅读</label>
				</div>
			</div>
			<div class="tjview" id="tjview" style="display: none;">
				<div class="tjtoptitle">
					<span>相关推荐</span>
				</div>
				<div class="pppp">
					<div id="swiperleft">
						<img src="img/sjleft.png" alt="上一页" />
					</div>
					<div class="swiper">
						<div class="swiper-wrapper" id="listbox">

						</div>
					</div>
					<div id="swiperright">
						<img src="img/sjright.png" alt="下一页" />
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" alt="备案图标" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" alt="回到顶部" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/clipboard.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		
		<!-- 修复require未定义错误 -->
		<script type="text/javascript">
			// 如果require未定义，提供一个空实现以避免错误
			if (typeof require === 'undefined') {
				window.require = function() {
					console.warn('Require被调用但未定义，这是一个模拟实现');
					return {};
				};
			}
		</script>
		
		<!-- 加载PDF.js库 -->
		<script src="https://cdn.jsdelivr.net/npm/pdfjs-dist@2.16.105/build/pdf.min.js"></script>
		<script src="https://cdn.jsdelivr.net/npm/pdfjs-dist@2.16.105/web/pdf_viewer.min.js"></script>
		
		<!-- 主脚本 -->
		<script type="text/javascript">
			// 确保pdfCleanupTimer变量声明在使用前
			let pdfCleanupTimer = null;
			
			// 基础变量
			let userinfo = sessionStorage.getItem("userinfo");
			let pdfname = null;
			let pdfurl = null;
			let pdfDoc = null;
			let pageNum = 1;
			let pageCount = 0;
			let currentZoom = 1.0;
			let doublePage = false;
			let touchStartX = 0;
			let touchEndY = 0;
			let touchStartY = 0;
			let touchEndX = 0;
			let outlineDestinations = {};  // 存储目录项与页码的映射
			let startReadingTime = null; // 添加阅读开始时间变量
			let flid = null; // 分类ID变量
			
			// PDF优化相关变量
			let pdfLoadingTimeout = null;
			let pdfLoadingStartTime = null;
			let isLazyLoading = true;  // 默认启用渐进式加载
			let isLowQuality = true;   // 默认启用低质量模式
			let loadedPages = {};      // 已加载页面缓存
			let maxInitialPages = 5;   // 初始加载的页数
			let pdfLoadingTask = null; // 存储PDF加载任务引用
			
			// 定义PDF优化参数
			const RANGE_CHUNK_SIZE = 65536 * 16; // 512KB的分片大小
			const CMAP_URL = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.16.105/cmaps/';
			const MAX_MOBILE_IMAGE_SIZE = 1024 * 1024; // 移动设备最大图像大小
			const CLEANUP_TIMEOUT = 30000; // 30秒后清理内存
			
			// 添加缺失的函数
			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: function(res) {
						if (res.code == '200') {
							let html = "";
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>';
							});
							$("#linkbox").html(html);
						} else {
							// console.log("获取友情链接失败:", res);
						}
					},
					error: function(err) {
						console.error("获取友情链接请求错误:", err);
						$("#linkbox").html("");
					}
				});
			}
			
			function getclass(redirect) {
				$.ajax({
					url: baseurl + "/web/category/all",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: function(res) {
						if (res.code == '200') {
							// 获取所有菜单数据
							let menuItems = res.data;
							// console.log("顶级菜单获取成功:", menuItems);
							
							// 生成菜单HTML
							let html = "";
							for (let i = 0; i < menuItems.length; i++) {
								const item = menuItems[i];
								const isActive = item.id == getUrlParam('id') || item.redirectUrl == redirect;
								
								if (isActive) {
									$("#zxxx").attr('href', redirect + "?id=" + item.id);
									$("#zxxx").html(item.name);
								}
								
								// 创建菜单项
								html += '<div class="menuitemaaa" id="menu-item-' + item.id + '">';
								
								// 菜单文本区域
								html += '<div class="menuaaaa' + (isActive ? ' active' : '') + '" data-href="' + item.redirectUrl + '?id=' + item.id + '">' + item.name + '</div>';
								
								// 只有当item.children存在且不为空时才添加子菜单
								if (item.children && item.children.length > 0) {
									html += '<div class="itemcboxaaa">';
									// 添加子菜单项
									item.children.forEach(function(subItem) {
										html += '<a href="' + subItem.redirectUrl + '?id=' + subItem.id + '" class="itema2aaa">' + subItem.name + '</a>';
									});
									html += '</div>'; // 关闭子菜单容器
								} else {
									// 没有子菜单则添加空的子菜单容器（隐藏）
									html += '<div class="itemcboxaaa" style="display:none;"></div>';
								}
								
								html += '</div>'; // 关闭菜单项
							}
							
							// 渲染菜单HTML
							$("#menubox").html(html);
							// console.log("渲染菜单HTML完成");
							
							// 添加主菜单项点击事件
							$(".menuaaaa").on("click", function() {
								const href = $(this).attr("data-href");
								if (href) {
									window.location.href = href;
								}
							});
							
							// 确保菜单容器样式正确
							$("#menubox").css({
								"display": "flex",
								"height": "100%"
							});
							
							// 添加明确的事件处理，而不仅依赖CSS
							$(".menuitemaaa").hover(
								function() {
									$(this).find(".itemcboxaaa").show();
								}, 
								function() {
									$(this).find(".itemcboxaaa").hide();
								}
							);
						} else {
							console.error("顶级菜单获取返回错误码:", res.code);
						}
					},
					error: function(err) {
						console.error("获取菜单分类失败:", err);
					}
				});
			}
			
			function getclassid() {
				$.ajax({
					url: baseurl + "/web/category/book",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: function(res) {
						if (res.code == '200' && res.data && res.data.length > 0) {
							$("#hsname").html(res.data[0].name);
							flid = res.data[0].id;
						} else {
							$("#hsname").html("红色书籍");
							flid = getUrlParam('id') || 0;
						}
					},
					error: function() {
						$("#hsname").html("红色书籍");
						flid = getUrlParam('id') || 0;
					}
				});
			}
			
			function getinfo() {
				if (!getUrlParam('id')) {
					window.location.href = 'onlinelearning2.html';
					return;
				}
				
				$.ajax({
					url: baseurl + "/web/post/" + getUrlParam('id'),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: function(res) {
						if (res.code == '200') {
							$("#sjname").html(res.data.title);
							let html = "";
							if(res.data.postSource) {
								$("#cbs").html(res.data.postSource);
							} else {
								$("#cbs").html("未知");
							}
							
							$("#fl").html(res.data.redBookName || "未分类");
							$("#yj").html(res.data.clickCount || 0);
							$("#sjjj").html(res.data.subTitle || "暂无简介");
							
							if(res.data.thumbPath && res.data.thumbPath.length > 0) {
								$("#bookimg").attr('src', baseurl + res.data.thumbPath[0]);
							} else {
								// 设置默认封面
								$("#bookimg").attr('src', './img/default-book-cover.png');
							}
							
							// 如果有PDF路径，保存以便后续打开
							if(res.data.filePath && res.data.filePath.length > 0) {
								pdfname = res.data.title;
								pdfurl = baseurl + res.data.filePath[0];
								// console.log("PDF文件路径:", pdfurl);
							} else {
								// console.log("没有PDF文件路径");
								$("#sjbtn").html("暂无PDF").css("background-color", "#999");
								$("#sjbtn").prop("disabled", true);
							}
							
							// 相关推荐
							if(res.data.recommend && res.data.recommend.length > 0) {
								let listhtml = "";
								res.data.recommend.forEach((item) => {
									listhtml += '<div class="swiper-slide">' +
												'<a href="onlinelearning5.html?id=' + item.id + '">' +
												'<img src="' + (item.thumbPath ? baseurl + item.thumbPath[0] : './img/default-book-cover.png') + '" alt="' + item.title + '">' +
												'<div class="sm">' + item.title + '</div>' +
												'</a></div>';
								});
								
								$("#listbox").html(listhtml);
								$("#tjview").show();
								
								// 初始化swiper
								var swiper = new Swiper('.swiper', {
									slidesPerView: 'auto',
									spaceBetween: 20,
									loop: res.data.recommend.length > 3,
									autoplay: res.data.recommend.length > 3 ? {
										delay: 3000,
										disableOnInteraction: false
									} : false,
									navigation: {
										nextEl: '#swiperright',
										prevEl: '#swiperleft',
									}
								});
							} else {
								$("#tjview").hide();
							}
						} else {
							cocoMessage.error(3000, "获取数据失败，请稍后重试");
							setTimeout(function() {
								window.location.href = 'onlinelearning2.html';
							}, 2000);
						}
					},
					error: function(err) {
						console.error("请求失败:", err);
						cocoMessage.error(3000, "获取数据失败，请检查网络连接");
						setTimeout(function() {
							window.location.href = 'onlinelearning2.html';
						}, 2000);
					}
				});
			}
			
			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
				var r = window.location.search.substr(1).match(reg);
				if (r != null) return unescape(r[2]);
				return null;
			}
			
			function edit() {
				sessionStorage.clear();
				$("#login").show();
				$("#user").hide();
				$("#edit").hide();
				cocoMessage.error(1000, "已退出登录！");
				setTimeout(function() {
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html';
				}, 1500);
			}
			
			// 显示PDF查看器
			function showpdf() {
				if (!pdfurl) {
					cocoMessage.error(3000, "无法获取PDF文件，请稍后重试");
					return;
				}
				
				$("#pdfname").html(pdfname || "文档阅读");
				$("#tcbox").show();
				$("#pdf-loading").show();
				$("#pdf-container").hide();
				$("#pdf-book").empty();
				
				// 重置阅读器状态
				currentZoom = 1.0;
				doublePage = false;
				loadedPages = {};
				pageNum = 1;
				startReadingTime = Date.now(); // 记录开始阅读时间
				
				// 加载PDF文档
				loadPDF(pdfurl);
			}
			
			// 显示消息提示
			function showMessage(message, type = 'info') {
				// 创建消息元素
				const messageElement = document.createElement('div');
				messageElement.className = 'pdf-message ' + type;
				messageElement.textContent = message;
				
				// 添加到DOM
				document.body.appendChild(messageElement);
				
				// 设置消失定时器
				setTimeout(() => {
					messageElement.style.opacity = '0';
					setTimeout(() => {
						if (messageElement.parentNode) {
							messageElement.parentNode.removeChild(messageElement);
						}
					}, 500);
				}, 3000);
			}
			
			$(function() {
				// 初始化页面
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html';
				});
				
				// 检查PDF.js是否已加载
				if (typeof pdfjsLib === 'undefined') {
					console.error("PDF.js 库未正确加载");
					setTimeout(function() {
						cocoMessage.error(5000, "PDF阅读器加载失败，请刷新页面或检查网络连接");
					}, 2000);
				} else {
					// 设置PDF.js worker
					pdfjsLib.GlobalWorkerOptions.workerSrc = 'https://cdn.jsdelivr.net/npm/pdfjs-dist@2.16.105/build/pdf.worker.min.js';
					// console.log("PDF.js加载成功");
				}
				
				// 用户登录状态检查
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide();
					$("#user").show();
					$("#user").html(JSON.parse(userinfo).name);
					$("#edit").show();
					getfooterlink();
					getclass('onlinelearning.html');
					getclassid();
					getinfo();
				} else {
					//未登录 则显示登录按钮
					$("#login").show();
					$("#user").hide();
					$("#edit").hide();
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html';
				}
				
				// 添加目录侧边栏切换事件
				$("#toggle-sidebar").on('click', function() {
					$("#pdf-sidebar").toggleClass('pdf-sidebar-visible');
				});
				
				$("#sidebar-close").on('click', function() {
					$("#pdf-sidebar").removeClass('pdf-sidebar-visible');
				});
				
				// 添加查看模式切换事件
				$("#toggle-view-mode").on('click', function() {
					doublePage = !doublePage;
					renderCurrentPage();
					if (doublePage) {
						$(this).addClass('active');
						showMessage("已切换到双页模式", "info");
					} else {
						$(this).removeClass('active');
						showMessage("已切换到单页模式", "info");
					}
				});
				
				// 添加加载优化选项事件
				$("#opt-lazy-loading").on('change', function() {
					isLazyLoading = $(this).prop('checked');
				});
				
				$("#opt-low-quality").on('change', function() {
					isLowQuality = $(this).prop('checked');
				});
				
				// 添加重试按钮事件
				$("#pdf-retry").on('click', function() {
					$("#pdf-retry").hide();
					showpdf();
				});
				
				// 设置回到顶部
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show();
					} else {
						$("#backtop").hide();
					}
				});
				
				$("#backtop").on('click', function() {
					$("body,html").animate({
						scrollTop: 0
					}, 300);
				});
				
				// 初始隐藏回到顶部按钮
				$("#backtop").hide();
			});
			
			// 其他方法保持不变，留在原文件中
			// PDF加载函数
			function loadPDF(url) {
				// console.log("开始加载PDF:", url);
				
				// 记录加载开始时间
				pdfLoadingStartTime = Date.now();
				
				// 重置UI状态
				$("#pdf-loading-progress-bar").css("width", "0%");
				$("#pdf-loading-progress-text").text("0%");
				$("#pdf-loading-size").text("0KB / 0KB");
				$("#pdf-loading-text").text("正在连接...");
				$("#pdf-timeout").hide();
				$("#pdf-retry").hide();
				
				// 设置加载超时检测
				pdfLoadingTimeout = setTimeout(function() {
					// 如果3秒后还没有进度，显示超时提示
					if ($("#pdf-loading-progress-bar").width() === 0) {
						$("#pdf-timeout").show();
						$("#pdf-loading-text").text("加载较慢，可能是网络问题");
					}
				}, 3000);
				
				// 创建加载任务
				const loadingTask = pdfjsLib.getDocument({
					url: url,
					cMapUrl: CMAP_URL,
					cMapPacked: true,
					rangeChunkSize: RANGE_CHUNK_SIZE
				});
				
				// 保存加载任务引用，以便可以取消
				pdfLoadingTask = loadingTask;
				
				// 监听加载进度
				loadingTask.onProgress = function(progressData) {
					if (progressData.total) {
						// 更新进度条
						const percent = Math.round((progressData.loaded / progressData.total) * 100);
						$("#pdf-loading-progress-bar").css("width", percent + "%");
						$("#pdf-loading-progress-text").text(percent + "%");
						
						// 更新加载大小信息
						const loadedSize = formatFileSize(progressData.loaded);
						const totalSize = formatFileSize(progressData.total);
						$("#pdf-loading-size").text(loadedSize + " / " + totalSize);
						
						// 估算剩余时间
						const elapsed = (Date.now() - pdfLoadingStartTime) / 1000; // 秒
						const percentComplete = progressData.loaded / progressData.total;
						let remainingSeconds = 0;
						
						if (percentComplete > 0.05) { // 至少有5%的进度才开始计算
							remainingSeconds = Math.round((elapsed / percentComplete) * (1 - percentComplete));
						}
						
						// 格式化剩余时间
						let timeStr = "";
						if (remainingSeconds < 60) {
							timeStr = remainingSeconds + "秒";
						} else {
							timeStr = Math.floor(remainingSeconds / 60) + "分" + (remainingSeconds % 60) + "秒";
						}
						
						// 更新加载文本
						if (percent < 100) {
							$("#pdf-loading-text").text(`加载中...${percent}% (剩余${timeStr})`);
						} else {
							$("#pdf-loading-text").text("解析PDF内容...");
						}
						
						// 如果文件大于10MB，自动开启低质量模式和渐进式加载
						const fileSizeMB = progressData.total / (1024 * 1024);
						if (fileSizeMB > 10) {
							$("#opt-lazy-loading").prop("checked", true);
							$("#opt-low-quality").prop("checked", true);
							isLazyLoading = true;
							isLowQuality = true;
						}
					}
				};
				
				// 加载PDF文档
				loadingTask.promise.then(function(pdf) {
					// 清除超时检测
					if (pdfLoadingTimeout) {
						clearTimeout(pdfLoadingTimeout);
						pdfLoadingTimeout = null;
					}
					
					// 存储PDF文档对象
					pdfDoc = pdf;
					pageCount = pdf.numPages;
					pageNum = 1;
					
					// 更新页面导航
					$("#page-info").text(`${pageNum} / ${pageCount}`);
					
					// 渲染第一页
					$("#pdf-loading-text").text("渲染第一页...");
					renderCurrentPage();
					
					// 隐藏加载提示，显示内容
					setTimeout(function() {
						$("#pdf-loading").fadeOut();
						$("#pdf-container").fadeIn();
					}, 500);
					
				}).catch(function(error) {
					console.error("PDF加载错误:", error);
					
					// 显示错误信息和重试选项
					$("#pdf-loading-text").text("PDF加载失败");
					$("#pdf-timeout").hide();
					$("#pdf-retry").show();
					
					// 显示错误消息
					showMessage("PDF加载失败，请检查网络连接或尝试重新加载", "error");
				});
			}
		</script>
		
		<script type="text/javascript">
			// 回到顶部功能
			$("#backtop").hide();
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show();
					} else {
						$("#backtop").hide();
					}
				});
				
				$("#backtop").on('click', function() {
					$("body,html").animate({
						scrollTop: 0
					}, 300);
				});
			});
		</script>
	</body>
</html>
