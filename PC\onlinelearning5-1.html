<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-在线学习-红色书籍</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/onlinelearning.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body class="index">
		<div class="tcbox" id="tcbox">

			<div class="topviewsss">
				<a onclick="closetc()">关闭</a>
				<label id="pdfname"></label>
			</div>
			<div class="panel-body pdfbox">
				<a id="pdf" class="media" href=""></a>
			</div>
		</div>
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="contenttitlebox">
			<div>
				<a id="zxxx" href="onlinelearning.html">在线学习</a>
				<img src="img/jtright.png" />
				<label class="aa" id="hsname"></label>
			</div>
		</div>
		<div class="content">
			<div class="sjinfoview">
				<div class="sjinfoleft">
					<img id="bookimg" src="" />
				</div>
				<div class="sjinforight">
					<div class="sjname" id="sjname"></div>
					<div class="sjstr">
						<label>
							<img src="img/sjcbs.png" />
							<span id="cbs"></span>
						</label>
						<label>
							<img src="img/sjfl.png" />
							<span id="fl"></span>
						</label>
						<label>
							<img src="img/sjyj.png" />
							<span id="yj"></span>
						</label>
					</div>
					<div class="sjjj" id="sjjj">

					</div>
					<label id="sjbtn" onclick="showpdf()">立即阅读</label>
				</div>
			</div>
			<div class="tjview" id="tjview" style="display: none;">
				<div class="tjtoptitle">
					<span>相关推荐</span>
				</div>
				<div class="pppp">
					<div id="swiperleft">
						<img src="img/sjleft.png" />
					</div>
					<div class="swiper">
						<div class="swiper-wrapper" id="listbox">

						</div>
					</div>
					<div id="swiperright">
						<img src="img/sjright.png" />
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/clipboard.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
					getfooterlink()
					getclass('onlinelearning.html')
					getclassid()
					getinfo()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href ='https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
			})
			let pdfname = null
			let pdfurl = null

			function closetc() {
				$("#tcbox").hide()
			}

			let flid = null
			function showpdf() {
				$("#pdfname").html(pdfname)
				$("#tcbox").show()
				$("#pdf").attr("href",pdfurl)
				$('a.media').media()
				if(getUrlParam('taskid')){
					let json = {
						infoId: getUrlParam('id'), //信息id
						categoryId: flid, //所属分类id
						totalInfo: "1", //总时长，总页数
						positioning: "", //学习了多久，多少页    
						progress: 0, //进度 百分比
						type: '红色书籍',
						learningTime: 0,
						sectionId: null,//学科ID
						status: "1",
						taskId: getUrlParam('taskid')
					}
					$.ajax({
						url: baseurl + "/study/record/add",
						type: 'post',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						data: JSON.stringify(json),
						dataType: 'json',
						success: (res) => {
						}
					})
				}
			}

			function getlist(classid, flid) {
				$.ajax({
					url: baseurl + "/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						categoryId: classid,
						pageSize: 10,
						redBookId: flid,
						pageNum: 1
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data)
							if (res.data.list.length < 5) {
								$("#tjview").hide()
							} else {
								let newhtml = ""
								res.data.list.map((item) => {
									newhtml +=
										'<div class="swiper-slide"><a href="onlinelearning5.html?id=' + item
										.id + '"><img src="' + baseurl + item.thumbPath[0] +
										'"/><div class="sm">' + item.title + '</div></a></div>'
								})
								$("#listbox").html(newhtml)
								var mySwiper = new Swiper('.swiper', {
									loop: true,
									slidesPerView: 5,
									centeredSlides: true,
									spaceBetween: 24,
									navigation: {
										nextEl: '#swiperleft',
										prevEl: '#swiperright',
									},
								})
								mySwiper.el.onmouseover = function() {
									mySwiper.autoplay.stop();
								}
								mySwiper.el.onmouseout = function() {
									mySwiper.autoplay.start();
								}
								$("#tjview").show()
							}
						}
					}
				})
			}
			function getinfo() {
				$.ajax({
					url: baseurl + "/posts/" + getUrlParam('id'),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data)
							let items = res.data
							pflid = items.cmsCategoryList[0].parentId
							$("#sjname").html(items.title + '<span>' + items.author + '&ensp;&ensp;著</span>')
							$("#bookimg").attr("src", baseurl + items.thumbPath[0])
							$("#cbs").html(items.postName)
							$("#fl").html(items.redBookName)
							$("#yj").html(items.clickCount)
							$("#sjjj").html(items.excerpt)
							pdfname = items.attachmentDtoList[0].fileName
							pdfurl = baseurl + items.attachmentDtoList[0].attachmentPath
							clicknum(items.id)
							getlist(items.cmsCategoryList[0].id, items.redBookId)
						}
					}
				})
			}

			function clicknum(id) {
				$.ajax({
					url: baseurl + "/posts/click/" + id,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {}
				})
			}

			function getclassid() {
				$.ajax({
					url: baseurl + "/web/category/book",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							$("#hsname").html(res.data[0].name)
							flid = res.data[0].id
						}
					}
				})
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					if (res.data[i].id == getUrlParam('id') || res.data[i].redirectUrl ==
			// 						'onlinelearning.html') {
			// 						$("#zxxx").attr('href', "onlinelearning.html?id=" + res.data[i].id)
			// 						$("#zxxx").html(res.data[i].name)
			// 						html += '<a class="menuitem active" href="' + res.data[i].redirectUrl + '?id=' +
			// 							res
			// 							.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					} else {
			// 						html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 							.data[
			// 								i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					}
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
