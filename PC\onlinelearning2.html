<!DOCTYPE html>
<html>
	<head>
	
	
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
		<title>思政一体化平台-在线学习-红色书籍</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/onlinelearning.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<style>
			/* 书籍行布局样式 - 每行5本书 */
			.book-row {
				display: flex;
				flex-wrap: wrap;
				justify-content: flex-start;
				margin-bottom: 20px;
				width: 100%;
				animation: fadeInUp 0.5s ease-out;
			}
			
			/* 新行淡入动画效果 */
			@keyframes fadeInUp {
				from {
					opacity: 0;
					transform: translateY(20px);
				}
				to {
					opacity: 1;
					transform: translateY(0);
				}
			}
			
			/* 添加3D按钮效果统一样式 - 新增ID类 */
			.button-3d {
				padding: 6px 12px;
				border-radius: 4px;
				position: relative;
				border: 1px solid #e0e0e0;
				background: linear-gradient(to bottom, #ffffff, #f5f5f5);
				color: #666;
				cursor: pointer;
				transition: all 0.2s ease;
				box-shadow: 0 2px 4px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.5);
				text-shadow: 0 1px 0 rgba(255,255,255,0.7);
				transform: translateY(0);
			}
			
			.button-3d:hover {
				background: linear-gradient(to bottom, #f9f9f9, #efefef);
				color: #d82121;
				border-color: #d82121;
				box-shadow: 0 3px 6px rgba(0,0,0,0.12), inset 0 1px 0 rgba(255,255,255,0.5);
				transform: translateY(-2px);
			}
			
			.button-3d:active {
				background: linear-gradient(to top, #f9f9f9, #efefef);
				color: #d82121;
				box-shadow: 0 1px 3px rgba(0,0,0,0.1), inset 0 1px 3px rgba(0,0,0,0.1);
				transform: translateY(1px);
			}
			
			.button-3d.active, 
			.button-3d.tjactive {
				background: linear-gradient(to bottom, #e02626, #d82121) !important;
				color: white !important;
				border-color: #b21a1a !important;
				text-shadow: 0 -1px 0 rgba(0,0,0,0.3) !important;
				box-shadow: 0 2px 4px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.3) !important;
				font-weight: 500 !important;
			}
			
			.button-3d.active:hover, 
			.button-3d.tjactive:hover {
				background: linear-gradient(to bottom, #ee2828, #d82121) !important;
				box-shadow: 0 4px 8px rgba(0,0,0,0.15), inset 0 1px 0 rgba(255,255,255,0.3) !important;
				transform: translateY(-2px);
			}
			
			/* 应用3D样式到分类容器 */
			.tjbox {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				padding: 10px 0;
				margin: 15px 0;
				background-color: #f8f9fa;
				border-radius: 6px;
				border: 1px solid #e9ecef;
				box-shadow: 0 2px 6px rgba(0,0,0,0.05);
				padding: 15px;
			}
			
			/* 应用3D样式到分类按钮 */
			.tjbox span {
				margin: 2px 5px;
				display: inline-block;
				/* 应用3D按钮样式 */
				padding: 6px 12px !important;
				border-radius: 4px !important;
				position: relative !important;
				border: 1px solid #e0e0e0 !important;
				background: linear-gradient(to bottom, #ffffff, #f5f5f5) !important;
				color: #666 !important;
				cursor: pointer !important;
				transition: all 0.2s ease !important;
				box-shadow: 0 2px 4px rgba(0,0,0,0.08), inset 0 1px 0 rgba(255,255,255,0.5) !important;
				text-shadow: 0 1px 0 rgba(255,255,255,0.7) !important;
				transform: translateY(0) !important;
			}
			
			.tjbox span:hover {
				background: linear-gradient(to bottom, #f9f9f9, #efefef) !important;
				color: #d82121 !important;
				border-color: #d82121 !important;
				box-shadow: 0 3px 6px rgba(0,0,0,0.12), inset 0 1px 0 rgba(255,255,255,0.5) !important;
				transform: translateY(-2px) !important;
			}
			
			.tjbox span:active {
				background: linear-gradient(to top, #f9f9f9, #efefef);
				color: #d82121;
				box-shadow: 0 1px 3px rgba(0,0,0,0.1), inset 0 1px 3px rgba(0,0,0,0.1);
				transform: translateY(1px);
			}
			
			/* 增加选择器优先级，添加!important确保样式生效 */
			.tjbox span.tjactive,
			#qbsj.tjactive,
			div.tjbox span.tjactive,
			span.tjactive {
				background: linear-gradient(to bottom, #e02626, #d82121) !important;
				color: white !important;
				border-color: #b21a1a !important;
				text-shadow: 0 -1px 0 rgba(0,0,0,0.3) !important;
				box-shadow: 0 2px 4px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.3) !important;
				font-weight: 500 !important;
			}
			
			.tjbox span.tjactive:hover,
			#qbsj.tjactive:hover,
			div.tjbox span.tjactive:hover,
			span.tjactive:hover {
				background: linear-gradient(to bottom, #ee2828, #d82121) !important;
				box-shadow: 0 4px 8px rgba(0,0,0,0.15), inset 0 1px 0 rgba(255,255,255,0.3) !important;
				transform: translateY(-2px);
			}
			
			/* 每个书籍项的样式 */
			.sjbox .txtitem {
				flex: 0 0 calc(20% - 16px);
				margin: 0 8px 16px 8px;
				box-sizing: border-box;
				max-width: calc(20% - 16px);
				transition: transform 0.3s ease, box-shadow 0.3s ease;
			}
			
			.sjbox .txtitem:hover {
				transform: translateY(-5px);
				box-shadow: 0 6px 12px rgba(0,0,0,0.1);
			}
			
			/* 空占位书籍的样式 */
			.empty-book {
				visibility: hidden;
			}
			
			/* 书籍封面样式 - 修复封面显示问题 */
			.txtitem .topitem {
				position: relative;
				width: 100%;
				height: 0;
				padding-bottom: 141.4%; /* A4纸张比例 */
				overflow: hidden;
				background-color: #f5f5f5;
				box-shadow: 0 2px 6px rgba(0,0,0,0.1);
				border-radius: 4px;
				cursor: pointer;
			}
			
			.txtitem .topitem img {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				object-fit: cover; /* 保持比例但填满容器 */
				transition: transform 0.3s ease;
				display: block; /* 确保图片以块级元素显示 */
				opacity: 1; /* 确保可见 */
				z-index: 1; /* 确保层级正确 */
				background-color: #f5f5f5; /* 加载前背景色 */
			}
			
			.txtitem .topitem:hover img {
				transform: scale(1.05);
			}
			
			.txtitem .topitem .icobox {
				position: absolute;
				bottom: 0;
				left: 0;
				width: 100%;
				background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
				padding: 10px 0 5px;
				z-index: 2;
			}
			
			.txtitem .topitem .icobox label {
				color: white;
				display: flex;
				align-items: center;
				justify-content: center;
				font-size: 12px;
				border-radius: 20px;
				background-color: rgba(0,0,0,0.4);
				padding: 3px 8px;
				backdrop-filter: blur(2px);
				box-shadow: 0 1px 3px rgba(0,0,0,0.2);
				margin-right: 10px;
				float: right;
				transition: all 0.3s ease;
				transform: translateY(0);
			}
			
			.txtitem:hover .topitem .icobox label {
				background-color: rgba(216, 33, 33, 0.7);
				transform: translateY(-3px);
				box-shadow: 0 3px 6px rgba(0,0,0,0.3);
			}
			
			.txtitem .topitem .icobox img {
				position: relative;
				width: 16px;
				height: 16px;
				margin-right: 5px;
				object-fit: contain;
				opacity: 1;
			}
			
			/* 添加新的点击率图标SVG样式 */
			.view-count-icon {
				margin-right: 4px;
				width: 14px;
				height: 14px;
				fill: currentColor;
				opacity: 0.9;
				vertical-align: -2px;
				transition: transform 0.3s ease;
			}
			
			.txtitem:hover .view-count-icon {
				transform: scale(1.1);
				animation: pulse 1.5s infinite ease-in-out;
			}
			
			@keyframes pulse {
				0% { transform: scale(1); }
				50% { transform: scale(1.1); }
				100% { transform: scale(1); }
			}
			
			/* 优化底部文字信息 */
			.txtitem .bottomitem {
				padding: 10px 5px;
				height: 80px;
				display: flex;
				flex-direction: column;
				overflow: hidden; /* 确保不出现滚动条 */
				position: relative; /* 设置相对定位，让子元素可以绝对定位 */
			}
			
			/* 确保鼠标悬浮时书名容器可以溢出显示完整内容 */
			.txtitem:hover .bottomitem {
				overflow: visible; /* 允许内容溢出显示 */
				z-index: 10; /* 提高层级，防止被其他元素遮挡 */
			}
			
			.txtitem .bottomitem .title {
				font-weight: bold;
				margin-bottom: 5px;
				height: 24px; /* 增加单行高度 */
				line-height: 24px; /* 设置行高与高度一致 */
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap; /* 强制单行 */
				transition: all 0.3s ease;
				position: relative;
				padding-right: 5px;
				display: flex; /* 使用flex布局 */
				align-items: center; /* 垂直居中 */
			}
			
			/* 鼠标悬浮时的滚动效果 */
			.txtitem:hover .bottomitem .title {
				animation: marquee 8s linear infinite; /* 放慢速度，更易阅读 */
				animation-play-state: running;
				text-overflow: clip; /* 移除省略号 */
				width: max-content; /* 确保宽度能完全容纳文本 */
				max-width: none; /* 移除最大宽度限制 */
				overflow: visible; /* 悬浮时允许内容溢出 */
			}
			
			/* 当标题文本宽度小于容器宽度时不需要滚动 */
			.txtitem .bottomitem .title.no-scroll {
				animation: none;
			}
			
			/* 滚动动画 - 从右向左滚动，确保完全显示书名 */
			@keyframes marquee {
				0%, 5% { transform: translateX(0); } /* 开始时停顿片刻 */
				95%, 100% { transform: translateX(calc(-100%)); } /* 完全滚动到末尾 */
			}
			
			/* 滚动时添加渐变遮罩 - 默认状态 */
			.txtitem .bottomitem .title:after {
				content: "";
				position: absolute;
				top: 0;
				right: 0;
				height: 100%;
				width: 20px;
				background: linear-gradient(to right, transparent, white);
				z-index: 1; /* 确保遮罩在文本上方 */
			}
			
			/* 鼠标悬浮时隐藏右侧遮罩，避免影响滚动文本 */
			.txtitem:hover .bottomitem .title:after {
				display: none; /* 完全移除遮罩，避免影响滚动 */
			}
			
			/* 添加阴影效果，提高文本在滚动时的可读性 */
			.txtitem:hover .bottomitem .title {
				text-shadow: 0px 0px 1px rgba(0,0,0,0.1); /* 轻微文本阴影 */
			}
			
			.txtitem .bottomitem .zz {
				font-size: 12px;
				color: #666;
				line-height: 1.5;
			}
			
			/* 懒加载图片样式 */
			.lazy-image {
				opacity: 0;
				transition: opacity 0.5s ease;
				background-color: #f5f5f5;
			}
			
			.lazy-image.loaded {
				opacity: 1;
			}
			
			/* 加载动画样式 */
			.loading-container {
				display: inline-flex;
				flex-direction: column;
				align-items: center;
				padding: 20px;
				border-radius: 8px;
				background-color: rgba(255, 255, 255, 0.95);
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
			}

			.loading-circle {
				width: 48px;
				height: 48px;
				border: 4px solid #f3f3f3;
				border-top: 4px solid #d82121;
				border-radius: 50%;
				margin-bottom: 15px;
				animation: spin 1s linear infinite;
			}

			.loading-text {
				font-size: 15px;
				color: #333;
				font-weight: 500;
			}
			
			/* 底部加载指示器 */
			#bottom-loading {
				text-align: center;
				padding: 15px 0;
				margin: 10px 0;
				display: none;
			}
			
			#bottom-loading .loading-dot {
				display: inline-block;
				width: 10px;
				height: 10px;
				border-radius: 50%;
				background-color: #d82121;
				margin: 0 4px;
				opacity: 0.3;
				animation: dot-pulse 1.4s infinite ease-in-out;
			}
			
			#bottom-loading .loading-dot:nth-child(2) {
				animation-delay: 0.2s;
			}
			
			#bottom-loading .loading-dot:nth-child(3) {
				animation-delay: 0.4s;
			}
			
			@keyframes dot-pulse {
				0%, 100% { opacity: 0.3; transform: scale(1); }
				50% { opacity: 1; transform: scale(1.2); }
			}
			
			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}

			@keyframes fadeIn {
				from { opacity: 0; }
				to { opacity: 1; }
			}

			#loading-indicator {
				animation: fadeIn 0.3s ease-in-out;
			}
			
			/* 添加默认书籍封面样式 */
			.default-book-cover {
				background-color: #f5f5f5;
				display: flex;
				align-items: center;
				justify-content: center;
				flex-direction: column;
				color: #666;
				font-size: 14px;
				width: 100%;
				height: 100%;
				position: absolute;
				top: 0;
				left: 0;
			}

			.default-book-cover svg {
				width: 40%;
				height: auto;
				margin-bottom: 10px;
				opacity: 0.7;
			}
			
			/* 修改分页器样式 */
			.fybox {
				display: flex;
				justify-content: center;
				align-items: center;
				margin: 30px 0;
				padding: 0 10px;
				font-size: 14px;
			}

			.fybox span {
				display: inline-flex;
				align-items: center;
				justify-content: center;
				
				min-width: 36px;
				height: 36px;
				margin: 0 4px;
				padding: 0 10px;
				border-radius: 4px;
				cursor: pointer;
				transition: all 0.2s ease;
				border: 1px solid #e0e0e0;
				color: #333;
				background-color: #fff;
				user-select: none;
			}

			.fybox span:hover:not(.disabled):not(.active):not(.ellipsis) {
				border-color: #d82121;
				color: #d82121;
			}

			.fybox span.active {
				background-color: #d82121;
				color: #fff;
				border-color: #d82121;
				font-weight: 500;
			}
			
			.fybox span.disabled {
				color: #ccc;
				cursor: not-allowed;
				background-color: #f9f9f9;
			}
			
			.fybox span.ellipsis {
				border: none;
				background: transparent;
				cursor: default;
				color: #999;
			}

			.fybox #num {
				display: flex;
				align-items: center;
			}

	
			
		
			
			

			/* 增加特殊筛选标记样式 */
			.filter-note {
				font-size: 12px;
				color: #666;
				margin-left: 15px;
				background-color: #f5f5f5;
				padding: 2px 8px;
				border-radius: 4px;
			}
			
			/* 排序选择器样式 */
			.sort-menu {
				margin-left: auto;
				padding-right: 20px;
			}
			
			.sort-selector {
				display: flex;
				align-items: center;
				font-size: 14px;
			}
			
			.sort-selector label {
				margin-right: 8px;
				color: #666;
			}
			
			.sort-selector select {
				padding: 5px 10px;
				border: 1px solid #ddd;
				border-radius: 4px;
				background-color: #fff;
				color: #333;
				font-size: 14px;
				cursor: pointer;
				outline: none;
				transition: all 0.3s ease;
			}
			
			.sort-selector select:hover {
				border-color: #d82121;
			}
			
			.sort-selector select:focus {
				border-color: #d82121;
				box-shadow: 0 0 0 2px rgba(216, 33, 33, 0.1);
			}
			
			/* 排序指示器样式 */
			.sort-indicator {
				display: inline-flex;
				align-items: center;
				padding: 4px 10px;
				background-color: #f9f9f9;
				border-radius: 4px;
				font-size: 12px;
				color: #666;
			}
			
			.sort-icon {
				display: inline-block;
				width: 12px;
				height: 12px;
				margin-right: 5px;
				background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="%23666"><path d="M3 18h6v-2H3v2zM3 6v2h18V6H3zm0 7h12v-2H3v2z"/></svg>');
				background-size: contain;
				background-repeat: no-repeat;
			}
			
			/* 排序信息框样式 */
			.sort-info-box {
				display: flex;
				justify-content: flex-end;
				margin: 10px 0;
				padding: 0 10px;
			}
			
			/* 恢复分类框样式 */
			.tjbox {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				padding: 10px 0;
			}
			
			.tjbox label {
				margin-right: 10px;
				color: #333;
				font-weight: bold;
			}
			
			.tjbox span {
				padding: 3px 12px;
				margin: 0 5px 5px 0;
				border-radius: 4px;
				cursor: pointer;
				background-color: #f5f5f5;
				color: #333; /* 默认文字颜色 */
				transition: all 0.2s ease;
			}
			
			.tjbox span:hover {
				background-color: #e8e8e8;
			}
			
			/* 增加选择器优先级，添加!important确保样式生效 */
			.tjbox span.tjactive,
			#qbsj.tjactive,
			div.tjbox span.tjactive,
			span.tjactive {
				background-color: #d82121 !important;
				color: white !important; /* 强制选中状态文字为白色 */
				font-weight: 500 !important; /* 稍微增加字重提高可读性 */
			}
			
			/* 针对特定选择器添加更强的优先级 */
			body div.content div.tjbox span.tjactive {
				color: white !important;
			}
			
			#flbox {
				display: flex;
				flex-wrap: wrap;
				align-items: center;
			}
			
		

			/* 课程网格样式 */
			.course-grid {
				display: flex !important;
				flex-wrap: wrap !important;
				justify-content: flex-start !important;
				align-items: center !important;
				gap: 8px !important; /* 适度间距 */
				padding: 5px !important;
				margin: 10px 0 !important;
				background-color: #f8f9fa !important;
				border-radius: 4px !important;
			}

			/* 章节网格样式 */
			.chapter-grid {
				display: flex !important;
				flex-wrap: wrap !important;
				justify-content: flex-start !important;
				align-items: center !important;
				gap: 8px !important; /* 适度间距 */
				margin-top: 0.75rem !important; /* 增加外边距以更好显示阴影 */
				padding: 5px !important; /* 增加内边距以更好显示阴影 */
				max-height: none !important; /* 移除固定高度，防止滚动条 */
				overflow: visible !important; /* 防止滚动条出现 */
			}

			/* 课程项、章节项和高级筛选按钮通用3D按钮样式 */
			.course-item,
			.chapter-item,
			.filter-container .filter-option,
			.filter-options .filter-option,  /* 新增 - 直接匹配高级筛选选项 */
			#qbsx .filter-option,            /* 新增 - 属性筛选选项 */
			#qblx .filter-option,            /* 新增 - 类型筛选选项 */
			#filetypebox .filter-option      /* 新增 - 文件格式筛选选项 */ {
				display: inline-flex !important;
				align-items: center !important;
				justify-content: center !important;
				white-space: nowrap !important;
				overflow: visible !important;
				text-overflow: initial !important;
				min-width: fit-content !important;
				width: auto !important;
				height: 30px !important; /* 减小高度 */
				padding: 4px 12px !important; /* 减小内边距 */
				margin: 3px !important; /* 减小外边距 */
				border-radius: 5px !important;
				cursor: pointer !important;
				text-align: center !important;
				font-size: 13px !important; /* 减小字体 */
				transition: all 0.25s ease-in-out !important; /* 更平滑沉稳的过渡 */
				position: relative !important;
				text-shadow: 0 1px 0 rgba(255,255,255,0.4) !important;
			}

			/* 未选择状态 - 增强3D感 */
			.course-item:not(.active):not(.tjactive),
			.chapter-item:not(.active):not(.tjactive),
			.filter-container .filter-option:not(.active),
			.filter-options .filter-option:not(.active),  /* 新增 - 直接匹配高级筛选选项 */
			#qbsx .filter-option:not(.active),            /* 新增 - 属性筛选选项 */
			#qblx .filter-option:not(.active),            /* 新增 - 类型筛选选项 */
			#filetypebox .filter-option:not(.active):not(.tjactive)  /* 新增 - 文件格式筛选选项 */ {
				background: linear-gradient(to bottom, #fdfdfd, #f0f0f0) !important; /* 更亮的渐变顶部 */
				border: 1px solid #c0c0c0 !important; /* 边框颜色调整为稍浅，与阴影区分 */
				color: #444 !important;
				font-weight: normal !important;
				box-shadow: 0 2px 0 #a0a0a0, /* 减小底部实线阴影 */
							0 3px 5px rgba(0,0,0,0.15), /* 减小环境阴影 */
							inset 0 1px 0 rgba(255,255,255,0.7);
				transform: translateY(0) !important;
			}

			/* 悬浮状态 (未选择时) - 文字和边框变红，背景不变，移除上浮 */
			.course-item:not(.active):not(.tjactive):hover,
			.chapter-item:not(.active):not(.tjactive):hover,
			.filter-container .filter-option:not(.active):hover,
			.filter-options .filter-option:not(.active):hover,  /* 新增 - 直接匹配高级筛选选项 */
			#qbsx .filter-option:not(.active):hover,            /* 新增 - 属性筛选选项 */
			#qblx .filter-option:not(.active):hover,            /* 新增 - 类型筛选选项 */
			#filetypebox .filter-option:not(.active):not(.tjactive):hover  /* 新增 - 文件格式筛选选项 */ {
				background: linear-gradient(to bottom, #fdfdfd, #f0f0f0) !important; /* 背景保持不变 */
				color: #d82121 !important; /* 文字变红 */
				border-color: #d82121 !important; /* 边框变红 */
				box-shadow: 0 2px 0 #c00, /* 减小底部红色阴影 */
							0 3px 5px rgba(0,0,0,0.15), /* 减小环境阴影 */
							inset 0 1px 0 rgba(255,255,255,0.7);
				transform: translateY(0) !important; /* 移除上浮，保持沉稳 */
			}
			
			/* 按下状态 (未选择时) - 凹陷效果 */
			.course-item:not(.active):not(.tjactive):active,
			.chapter-item:not(.active):not(.tjactive):active,
			.filter-container .filter-option:not(.active):active,
			.filter-options .filter-option:not(.active):active,  /* 新增 - 直接匹配高级筛选选项 */
			#qbsx .filter-option:not(.active):active,            /* 新增 - 属性筛选选项 */
			#qblx .filter-option:not(.active):active,            /* 新增 - 类型筛选选项 */
			#filetypebox .filter-option:not(.active):not(.tjactive):active  /* 新增 - 文件格式筛选选项 */ {
				background: linear-gradient(to top, #fdfdfd, #e8e8e8) !important; /* 背景轻微变暗，模拟按下 */
				box-shadow: inset 0 2px 3px rgba(0,0,0,0.25), /* 增强凹陷阴影 */
							inset 0 -1px 0 rgba(255,255,255,0.4); 
				transform: translateY(1px) !important; /* 减小向下移动幅度 */
				color: #d82121 !important; /* 按下时文字也保持红色 */
				border-color: #c00 !important; /* 按下时边框颜色加深 */
			}
			
			/* 选中状态 - 红色背景，白色文字，3D效果 */
			.course-item.active,
			.course-item.tjactive,
			.chapter-item.active,
			.chapter-item.tjactive,
			.filter-container .filter-option.active,
			.filter-options .filter-option.active,    /* 新增 - 直接匹配高级筛选选项 */
			#qbsx .filter-option.active,              /* 新增 - 属性筛选选项 */
			#qblx .filter-option.active,              /* 新增 - 类型筛选选项 */
			#filetypebox .filter-option.active,       /* 新增 - 文件格式筛选选项 */
			#filetypebox .filter-option.tjactive      /* 新增 - 文件格式筛选选项 */ {
				background: linear-gradient(to bottom, #e02626, #d82121) !important; /* 红色背景 */
				color: white !important; /* 白色文字 */
				border: 1px solid #a01818 !important; /* 更深的红色边框 */
				font-weight: bold !important; /* 文字加粗 */
				text-shadow: 0 -1px 0 rgba(0,0,0,0.3) !important; /* 文字下方阴影 */
				box-shadow: 0 2px 0 #a01818, /* 减小底部红色阴影 */
							0 3px 5px rgba(0,0,0,0.2), /* 减小环境阴影 */
							inset 0 1px 0 rgba(255,255,255,0.4); /* 内部高光 */
				transform: translateY(0) !important;
			}
			
			/* 悬浮状态 (已选择时) - 更沉稳 */
			.course-item.active:hover,
			.course-item.tjactive:hover,
			.chapter-item.active:hover,
			.chapter-item.tjactive:hover,
			.filter-container .filter-option.active:hover,
			.filter-options .filter-option.active:hover,    /* 新增 - 直接匹配高级筛选选项 */
			#qbsx .filter-option.active:hover,              /* 新增 - 属性筛选选项 */
			#qblx .filter-option.active:hover,              /* 新增 - 类型筛选选项 */
			#filetypebox .filter-option.active:hover,       /* 新增 - 文件格式筛选选项 */
			#filetypebox .filter-option.tjactive:hover      /* 新增 - 文件格式筛选选项 */ {
				background: linear-gradient(to bottom, #ed2d2d, #e02626) !important; /* 悬浮时略亮的红色 */
				box-shadow: 0 2px 0 #a01818, 
							0 3px 6px rgba(0,0,0,0.25), 
							inset 0 1px 0 rgba(255,255,255,0.4);
				transform: translateY(0px) !important; /* 移除上浮，保持沉稳 */
			}
			
			/* 按下状态 (已选择时) - 凹陷效果 */
			.course-item.active:active,
			.course-item.tjactive:active,
			.chapter-item.active:active,
			.chapter-item.tjactive:active,
			.filter-container .filter-option.active:active,
			.filter-options .filter-option.active:active,    /* 新增 - 直接匹配高级筛选选项 */
			#qbsx .filter-option.active:active,              /* 新增 - 属性筛选选项 */
			#qblx .filter-option.active:active,              /* 新增 - 类型筛选选项 */
			#filetypebox .filter-option.active:active,       /* 新增 - 文件格式筛选选项 */
			#filetypebox .filter-option.tjactive:active      /* 新增 - 文件格式筛选选项 */ {
				background: linear-gradient(to top, #d82121, #c01e1e) !important; /* 背景变暗 */
				box-shadow: inset 0 2px 3px rgba(0,0,0,0.35), /* 增强凹陷阴影 */
							inset 0 -1px 0 rgba(255,255,255,0.15); 
				transform: translateY(1px) !important; /* 减小向下移动幅度 */
				border-color: #801010 !important; /* 边框颜色加深 */
			}

			/* 课程项、章节项和高级筛选按钮文本样式 */
			.course-item span,
			.chapter-item span,
			.filter-container .filter-option span,
			.filter-options .filter-option span,    /* 新增 - 直接匹配高级筛选选项 */
			#qbsx .filter-option span,              /* 新增 - 属性筛选选项 */
			#qblx .filter-option span,              /* 新增 - 类型筛选选项 */
			#filetypebox .filter-option span        /* 新增 - 文件格式筛选选项 */ {
				display: inline-block !important;
				white-space: nowrap !important;
				overflow: visible !important;
				text-overflow: initial !important;
				padding-right: 0 !important;
				text-align: center !important;
			}

			/* 高级筛选子容器标题样式 - 保持清晰，与按钮文字协调 */
			.filter-container .filter-label-drawer,
			.filter-group .filter-label-drawer {    /* 新增 - 直接匹配高级筛选标签 */
				font-size: 14px !important; /* 略小于按钮文字，以示区别 */
				font-weight: 500 !important;
				color: #333 !important; /* 深灰色，保证可读性 */
				margin-bottom: 8px !important; /* 调整与下方按钮组的间距 */
				padding-left: 0 !important; /* 移除可能存在的内边距 */
			}
			
			/* 分类按钮3D效果增强 */
			.tjbox span,
			div[class^="分类"] span {
				display: inline-flex !important;
				align-items: center !important;
				justify-content: center !important;
				white-space: nowrap !important;
				overflow: visible !important;
				text-overflow: initial !important;
				min-width: fit-content !important;
				width: auto !important;
				height: 36px !important;
				padding: 6px 15px !important;
				margin: 5px !important;
				border-radius: 5px !important;
				font-size: 15px !important;
				transition: all 0.25s ease-in-out !important;
				position: relative !important;
				text-shadow: 0 1px 0 rgba(255,255,255,0.4) !important;
				cursor: pointer !important;
				background: linear-gradient(to bottom, #fdfdfd, #f0f0f0) !important;
				border: 1px solid #c0c0c0 !important;
				color: #444 !important;
				font-weight: normal !important;
				box-shadow: 0 3px 0 #a0a0a0, 
							0 5px 8px rgba(0,0,0,0.2), 
							inset 0 1px 0 rgba(255,255,255,0.7) !important;
				transform: translateY(0) !important;
			}
			
			/* 悬浮状态 - 文字和边框变红，背景不变，移除上浮 */
			.tjbox span:hover,
			div[class^="分类"] span:hover {
				background: linear-gradient(to bottom, #fdfdfd, #f0f0f0) !important;
				color: #d82121 !important;
				border-color: #d82121 !important;
				box-shadow: 0 3px 0 #c00, 
							0 5px 8px rgba(0,0,0,0.2), 
							inset 0 1px 0 rgba(255,255,255,0.7) !important;
				transform: translateY(0) !important;
			}
			
			/* 按下状态 - 凹陷效果 */
			.tjbox span:active,
			div[class^="分类"] span:active {
				background: linear-gradient(to top, #fdfdfd, #e8e8e8) !important;
				box-shadow: inset 0 2px 3px rgba(0,0,0,0.25), 
							inset 0 -1px 0 rgba(255,255,255,0.4) !important;
				transform: translateY(2px) !important;
				color: #d82121 !important;
				border-color: #c00 !important;
			}
			
			/* 选中状态 - 红色背景，白色文字，3D效果 */
			.tjbox span.tjactive,
			#qbsj.tjactive,
			div.tjbox span.tjactive,
			span.tjactive,
			div[class^="分类"] span.active {
				background: linear-gradient(to bottom, #e02626, #d82121) !important;
				color: white !important;
				border: 1px solid #a01818 !important;
				font-weight: bold !important;
				text-shadow: 0 -1px 0 rgba(0,0,0,0.3) !important;
				box-shadow: 0 3px 0 #a01818, 
							0 5px 8px rgba(0,0,0,0.25), 
							inset 0 1px 0 rgba(255,255,255,0.4) !important;
				transform: translateY(0) !important;
			}
			
			/* 悬浮状态 (已选择时) - 更沉稳 */
			.tjbox span.tjactive:hover,
			#qbsj.tjactive:hover,
			div.tjbox span.tjactive:hover,
			span.tjactive:hover,
			div[class^="分类"] span.active:hover {
				background: linear-gradient(to bottom, #ed2d2d, #e02626) !important;
				box-shadow: 0 3px 0 #a01818, 
							0 6px 10px rgba(0,0,0,0.3), 
							inset 0 1px 0 rgba(255,255,255,0.4) !important;
				transform: translateY(0px) !important;
			}
			
			/* 按下状态 (已选择时) - 凹陷效果 */
			.tjbox span.tjactive:active,
			#qbsj.tjactive:active,
			div.tjbox span.tjactive:active,
			span.tjactive:active,
			div[class^="分类"] span.active:active {
				background: linear-gradient(to top, #d82121, #c01e1e) !important;
				box-shadow: inset 0 2px 3px rgba(0,0,0,0.35), 
							inset 0 -1px 0 rgba(255,255,255,0.15) !important;
				transform: translateY(2px) !important;
				border-color: #801010 !important;
			}
			
			/* 分类标签样式优化 */
			.tjbox label {
				font-size: 15px !important;
				font-weight: 600 !important;
				color: #333 !important;
				margin-right: 12px !important;
				padding-left: 5px !important;
			}
			
			/* 顶部分类按钮3D效果 - 针对页面最上方的分类 */
			.topview_2 ~ div span,
			.分类 span {
				display: inline-flex !important;
				align-items: center !important;
				justify-content: center !important;
				white-space: nowrap !important;
				overflow: visible !important;
				text-overflow: initial !important;
				min-width: fit-content !important;
				width: auto !important;
				height: 36px !important;
				padding: 6px 15px !important;
				margin: 5px !important;
				border-radius: 5px !important;
				font-size: 15px !important;
				transition: all 0.25s ease-in-out !important;
				position: relative !important;
				text-shadow: 0 1px 0 rgba(255,255,255,0.4) !important;
				cursor: pointer !important;
				background: linear-gradient(to bottom, #fdfdfd, #f0f0f0) !important;
				border: 1px solid #c0c0c0 !important;
				color: #444 !important;
				font-weight: normal !important;
				box-shadow: 0 3px 0 #a0a0a0, 
							0 5px 8px rgba(0,0,0,0.2), 
							inset 0 1px 0 rgba(255,255,255,0.7) !important;
				transform: translateY(0) !important;
			}
			
			/* 悬浮状态 */
			.topview_2 ~ div span:hover,
			.分类 span:hover {
				background: linear-gradient(to bottom, #fdfdfd, #f0f0f0) !important;
				color: #d82121 !important;
				border-color: #d82121 !important;
				box-shadow: 0 3px 0 #c00, 
							0 5px 8px rgba(0,0,0,0.2), 
							inset 0 1px 0 rgba(255,255,255,0.7) !important;
				transform: translateY(0) !important;
			}
			
			/* 按下状态 */
			.topview_2 ~ div span:active,
			.分类 span:active {
				background: linear-gradient(to top, #fdfdfd, #e8e8e8) !important;
				box-shadow: inset 0 2px 3px rgba(0,0,0,0.25), 
							inset 0 -1px 0 rgba(255,255,255,0.4) !important;
				transform: translateY(2px) !important;
				color: #d82121 !important;
				border-color: #c00 !important;
			}
			
			/* 选中状态 */
			.topview_2 ~ div span.active,
			.topview_2 ~ div span.tjactive,
			.分类 span.active,
			.分类 span.tjactive {
				background: linear-gradient(to bottom, #e02626, #d82121) !important;
				color: white !important;
				border: 1px solid #a01818 !important;
				font-weight: bold !important;
				text-shadow: 0 -1px 0 rgba(0,0,0,0.3) !important;
				box-shadow: 0 3px 0 #a01818, 
							0 5px 8px rgba(0,0,0,0.25), 
							inset 0 1px 0 rgba(255,255,255,0.4) !important;
				transform: translateY(0) !important;
			}
			
			/* 强制应用3D效果到页面顶部分类按钮 */
			.分类:first-of-type span,
			body > div:nth-of-type(2) span {
				display: inline-flex !important;
				align-items: center !important;
				justify-content: center !important;
				white-space: nowrap !important;
				overflow: visible !important;
				height: 36px !important;
				padding: 6px 15px !important;
				margin: 5px !important;
				border-radius: 5px !important;
				font-size: 15px !important;
				box-shadow: 0 3px 0 #a0a0a0, 
							0 5px 8px rgba(0,0,0,0.2), 
							inset 0 1px 0 rgba(255,255,255,0.7) !important;
			}
			
			/* 全部按钮特殊样式 */
			.全部,
			span[data-id="0"],
			span:first-child {
				font-weight: 500 !important;
			}
			
			/* 特定按钮的增强3D效果 - 适用于页面顶部分类按钮 */
			.topview_2 + div span,
			.全部,
			#qbsj,
			.tjbox span {
				/* 增强的3D按钮效果 */
				display: inline-flex !important;
				align-items: center !important;
				justify-content: center !important;
				min-width: fit-content !important;
				height: 30px !important; /* 减小高度 */
				padding: 4px 12px !important; /* 减小内边距 */
				margin: 3px !important; /* 减小外边距 */
				border-radius: 5px !important;
				font-size: 13px !important; /* 减小字体 */
				position: relative !important;
				cursor: pointer !important;
				border: 1px solid #c0c0c0 !important;
				background: linear-gradient(to bottom, #fdfdfd, #f0f0f0) !important;
				color: #444 !important;
				font-weight: normal !important;
				box-shadow: 0 2px 0 #a0a0a0, 
						    0 3px 5px rgba(0,0,0,0.15), 
						    inset 0 1px 0 rgba(255,255,255,0.7) !important;
				text-shadow: 0 1px 0 rgba(255,255,255,0.4) !important;
				transition: all 0.2s ease !important;
			}
			
			/* 页面顶部分类按钮悬浮状态 */
			.topview_2 + div span:hover,
			.全部:hover,
			#qbsj:not(.tjactive):hover,
			.tjbox span:not(.tjactive):hover {
				color: #d82121 !important;
				border-color: #d82121 !important;
				box-shadow: 0 2px 0 #c00, 
						    0 3px 5px rgba(0,0,0,0.15), 
						    inset 0 1px 0 rgba(255,255,255,0.7) !important;
			}
			
			/* 页面顶部分类按钮按下状态 */
			.topview_2 + div span:active,
			.全部:active,
			#qbsj:not(.tjactive):active,
			.tjbox span:not(.tjactive):active {
				background: linear-gradient(to top, #fdfdfd, #e8e8e8) !important;
				box-shadow: inset 0 2px 3px rgba(0,0,0,0.25), 
						    inset 0 -1px 0 rgba(255,255,255,0.4) !important;
				transform: translateY(1px) !important;
				color: #d82121 !important;
				border-color: #c00 !important;
			}
			
			/* 页面顶部分类按钮选中状态 */
			.topview_2 + div span.active,
			.topview_2 + div span.tjactive,
			.全部.active,
			.全部.tjactive,
			#qbsj.tjactive,
			.tjbox span.tjactive {
				background: linear-gradient(to bottom, #e02626, #d82121) !important;
				color: white !important;
				border: 1px solid #a01818 !important;
				font-weight: bold !important;
				text-shadow: 0 -1px 0 rgba(0,0,0,0.3) !important;
				box-shadow: 0 2px 0 #a01818, 
						    0 3px 5px rgba(0,0,0,0.2), 
						    inset 0 1px 0 rgba(255,255,255,0.4) !important;
			}
			
			/* 页面顶部分类按钮选中状态的悬浮效果 */
			.topview_2 + div span.active:hover,
			.topview_2 + div span.tjactive:hover,
			.全部.active:hover,
			.全部.tjactive:hover,
			#qbsj.tjactive:hover,
			.tjbox span.tjactive:hover {
				background: linear-gradient(to bottom, #ed2d2d, #e02626) !important;
				box-shadow: 0 2px 0 #a01818, 
						    0 3px 6px rgba(0,0,0,0.25), 
						    inset 0 1px 0 rgba(255,255,255,0.4);
			}
			
			/* 页面顶部分类按钮选中状态的按下效果 */
			.topview_2 + div span.active:active,
			.topview_2 + div span.tjactive:active,
			.全部.active:active,
			.全部.tjactive:active,
			#qbsj.tjactive:active,
			.tjbox span.tjactive:active {
				background: linear-gradient(to top, #d82121, #c01e1e) !important;
				box-shadow: inset 0 2px 3px rgba(0,0,0,0.35), 
						    inset 0 -1px 0 rgba(255,255,255,0.15) !important;
				transform: translateY(1px) !important;
				border-color: #801010 !important; /* 边框颜色加深 */
			}
			
			/* 顶部分类按钮增强3D效果 */
			.分类 > span,
			.tjbox span,
			#qbsj {
				/* 强化3D效果 */
				box-shadow: 0 2px 0 rgba(0,0,0,0.2), 
				            0 3px 5px rgba(0,0,0,0.1), 
				            inset 0 1px 0 rgba(255,255,255,0.7) !important;
				border: 1px solid rgba(0,0,0,0.2) !important;
				border-radius: 5px !important;
				background: linear-gradient(to bottom, #ffffff, #f0f0f0) !important;
				transform: translateY(0) !important;
				transition: all 0.2s ease !important;
				/* 调整按钮尺寸 */
				font-size: 13px !important;
				height: 30px !important;
				padding: 4px 12px !important;
				margin: 3px !important;
				min-width: fit-content !important;
				display: inline-flex !important;
				align-items: center !important;
				justify-content: center !important;
			}
			
			/* 选中状态增强 */
			.分类 > span.active,
			.tjbox span.tjactive,
			#qbsj.tjactive {
				background: linear-gradient(to bottom, #e63939, #d82121) !important;
				color: white !important;
				box-shadow: 0 2px 0 #901010, 
				            0 3px 5px rgba(0,0,0,0.2), 
				            inset 0 1px 0 rgba(255,255,255,0.3) !important;
				text-shadow: 0 -1px 0 rgba(0,0,0,0.3) !important;
				font-weight: bold !important;
			}
			
			/* 强制应用紧凑按钮样式 - 使用高优先级选择器和!important */
			html body .tjbox span,
			html body #qbsj,
			html body .分类 span,
			html body .filter-option,
			html body .course-item,
			html body .chapter-item {
				display: inline-flex !important;
				align-items: center !important;
				justify-content: center !important;
				white-space: nowrap !important;
				overflow: visible !important;
				text-overflow: initial !important;
				min-width: fit-content !important;
				width: auto !important;
				/* 紧凑尺寸 */
				height: 30px !important;
				padding: 4px 12px !important;
				margin: 3px !important;
				border-radius: 5px !important;
				font-size: 13px !important;
				/* 3D效果 */
				position: relative !important;
				cursor: pointer !important;
				border: 1px solid #c0c0c0 !important;
				background: linear-gradient(to bottom, #fdfdfd, #f0f0f0) !important;
				color: #444 !important;
				font-weight: normal !important;
				box-shadow: 0 2px 0 #a0a0a0, 
							0 3px 5px rgba(0,0,0,0.15), 
							inset 0 1px 0 rgba(255,255,255,0.7) !important;
				text-shadow: 0 1px 0 rgba(255,255,255,0.4) !important;
				transition: all 0.2s ease !important;
			}
			
			/* 高优先级选择器 - 悬浮状态 */
			html body .tjbox span:hover:not(.tjactive):not(.active),
			html body #qbsj:hover:not(.tjactive):not(.active),
			html body .分类 span:hover:not(.tjactive):not(.active),
			html body .filter-option:hover:not(.active),
			html body .course-item:hover:not(.active),
			html body .chapter-item:hover:not(.active) {
				background: linear-gradient(to bottom, #fdfdfd, #f0f0f0) !important;
				color: #d82121 !important;
				border-color: #d82121 !important;
				box-shadow: 0 2px 0 #c00, 
							0 3px 5px rgba(0,0,0,0.15), 
							inset 0 1px 0 rgba(255,255,255,0.7) !important;
			}
			
			/* 高优先级选择器 - 按下状态 */
			html body .tjbox span:active:not(.tjactive):not(.active),
			html body #qbsj:active:not(.tjactive):not(.active),
			html body .分类 span:active:not(.tjactive):not(.active),
			html body .filter-option:active:not(.active),
			html body .course-item:active:not(.active),
			html body .chapter-item:active:not(.active) {
				background: linear-gradient(to top, #fdfdfd, #e8e8e8) !important;
				box-shadow: inset 0 2px 3px rgba(0,0,0,0.25), 
							inset 0 -1px 0 rgba(255,255,255,0.4) !important;
				transform: translateY(1px) !important;
				color: #d82121 !important;
				border-color: #c00 !important;
			}
			
			/* 高优先级选择器 - 选中状态 */
			html body .tjbox span.tjactive,
			html body #qbsj.tjactive,
			html body .分类 span.active,
			html body .filter-option.active,
			html body .course-item.active,
			html body .chapter-item.active {
				background: linear-gradient(to bottom, #e02626, #d82121) !important;
				color: white !important;
				border: 1px solid #a01818 !important;
				font-weight: bold !important;
				text-shadow: 0 -1px 0 rgba(0,0,0,0.3) !important;
				box-shadow: 0 2px 0 #a01818, 
							0 3px 5px rgba(0,0,0,0.2), 
							inset 0 1px 0 rgba(255,255,255,0.4) !important;
			}
			
			/* 导航栏激活样式 - 与其他页面保持一致 */
			.topview_2 .itembox .menuaaaa.active,
			.topview_2 .menuaaaa.active,
			div.topview_2 .menuaaaa.active {
				color: #ffd05f !important;
				font-weight: bold !important;
				position: relative !important;
			}
			
			/* 激活菜单项下划线效果 */
			.topview_2 .itembox .menuaaaa.active::after,
			.topview_2 .menuaaaa.active::after,
			div.topview_2 .menuaaaa.active::after {
				position: absolute !important;
				content: "" !important;
				height: 2px !important;
				background: #ffd05f !important;
				width: 100% !important;
				bottom: 0 !important;
				left: 0 !important;
			}
			
			/* 激活菜单项悬停状态 */
			.topview_2 .itembox .menuaaaa.active:hover,
			.topview_2 .menuaaaa.active:hover,
			div.topview_2 .menuaaaa.active:hover {
				color: #ffd05f !important;
				font-weight: bold !important;
			}
			
			/* 调整内容区域顶部间距，防止被固定的分类区域遮挡 */
			#htmlbox {
				margin-top: 15px !important;
			}
			
			/* 本页面专用：强制全屏弹窗完全覆盖，消除上方白色区域 */
			.tcbox {
				position: fixed !important;
				top: 0 !important;
				left: 0 !important;
				right: 0 !important;
				bottom: 0 !important;
				width: 100vw !important;
				height: 100vh !important;
				margin: 0 !important;
				padding: 0 !important;
				background: rgba(0, 0, 0, 0.8) !important;
				z-index: 99999 !important;
				box-sizing: border-box !important;
			}
			
			/* 确保弹窗内容居中且不受外部样式影响 */
			.tcbox * {
				box-sizing: border-box !important;
			}
			
			/* 针对可能的弹窗内容白色背景进行处理 */
			.tcbox > div,
			.tcbox > .popup-dialog-content,
			.tcbox .pdfbox,
			.tcbox .topviewsss {
				background: transparent !important;
			}
			
			/* 确保PDF查看器正确显示 */
			.tcbox .pdfbox {
				width: 100% !important;
				height: 100% !important;
				margin: 0 !important;
				padding: 0 !important;
			}
			
			/* 强制移除任何可能的上边距或填充 */
			.tcbox::before,
			.tcbox::after {
				display: none !important;
			}
			
			/* 确保弹窗覆盖页面顶部导航区域 */
			body.index .tcbox {
				position: fixed !important;
				top: 0 !important;
				left: 0 !important;
				right: 0 !important;
				bottom: 0 !important;
				width: 100vw !important;
				height: 100vh !important;
				background: rgba(0, 0, 0, 0.8) !important;
				z-index: 100000 !important;
			}
			
			/* 针对可能存在的iframe或其他内容 */
			.tcbox iframe,
			.tcbox #pdf,
			.tcbox .topviewsss {
				margin: 0 !important;
				padding: 0 !important;
				border: none !important;
			}
			
			/* 确保没有任何元素在弹窗之上 */
			.tcbox + * {
				z-index: -1 !important;
			}
		</style>
		
		<!-- 覆盖外部CSS中的移动端样式，确保横屏与PC端显示一致 -->
		<style>
			/* 重置移动端样式，让页面使用全局字体缩放系统 */
			@media screen and (max-width: 768px) {
				.topview {
					height: auto !important;
				}
				
				.topview_1 {
					width: auto !important;
					height: auto !important;
					padding: 0 !important;
				}
				
				.topview_2 {
					width: auto !important;
					height: auto !important;
				}
				
				.topview_1 input {
					width: auto !important;
					font-size: inherit !important;
				}
				
				.topview_1 .loginview label,
				.topview_1 .loginview a {
					font-size: inherit !important;
				}
				
				body {
					padding-top: 0 !important;
				}
				
				footer .yqlj {
					width: auto !important;
					padding: 0 !important;
				}
				
				footer .yqlj .box {
					padding-left: 0 !important;
					font-size: inherit !important;
				}
				
				footer .yqlj .box a {
					padding-right: 0 !important;
					line-height: inherit !important;
				}
				
				#backtop {
					right: auto !important;
					width: auto !important;
					height: auto !important;
				}
			}
			
			/* 重置横屏特殊样式 */
			@media screen and (max-width: 768px) and (orientation: landscape) {
				.topview {
					height: auto !important;
				}
				
				.topview_1 {
					height: auto !important;
				}
				
				.topview_2 {
					height: auto !important;
				}
				
				body {
					padding-top: 0 !important;
				}
				
				.topview_1 .loginview label,
				.topview_1 .loginview a {
					font-size: inherit !important;
				}
			}
			
			/* 重置超小屏幕样式 */
			@media screen and (max-width: 480px) {
				.topview_1 input {
					width: auto !important;
					font-size: inherit !important;
				}
				
				.topview_1 .ssview {
					margin-right: auto !important;
				}
				
				footer .yqlj .box {
					font-size: inherit !important;
					border-left: auto !important;
					padding-left: auto !important;
					justify-content: auto !important;
					flex-wrap: auto !important;
				}
				
				footer .yqlj .box a {
					padding: auto !important;
					width: auto !important;
					text-align: auto !important;
				}
			}
		</style>
		
		<!-- 分类区域响应式字体缩放和固定位置样式 -->
		<style>
			/* 分类区域固定在最上方并添加响应式字体缩放 */
			.tjbox {
				position: sticky !important;
				top: 0 !important;
				z-index: 100 !important;
				background: #f8f9fa !important;
				box-shadow: 0 2px 10px rgba(0,0,0,0.1) !important;
				border-bottom: 2px solid #e9ecef !important;
				margin: 0 0 20px 0 !important;
				padding: 10px !important;
				width: 100% !important;
				box-sizing: border-box !important;
				/* 修改为flexbox布局，支持换行 */
				display: flex !important;
				flex-wrap: wrap !important;
				align-items: center !important;
				gap: 0.2vw !important;
			}
			
			/* 分类标签样式 */
			.tjbox label {
				font-size: 0.9vw !important;
				font-weight: 600 !important;
				color: #333 !important;
				margin-right: 0.5vw !important;
				white-space: nowrap !important;
				flex-shrink: 0 !important; /* 防止标签被压缩 */
			}
			
			/* #flbox容器样式 - 让它与其他按钮在同一行 */
			#flbox {
				display: contents !important; /* 让子元素直接参与父级的flex布局 */
			}
			
			/* 分类按钮响应式字体缩放 - 减小尺寸 */
			.tjbox span,
			#qbsj,
			#flbox span {
				font-size: 0.8vw !important;
				padding: 0.3vw 0.7vw !important;
				margin: 0 !important; /* 移除margin，使用gap控制间距 */
				border-radius: 0.3vw !important;
				min-width: fit-content !important;
				white-space: nowrap !important;
				flex-shrink: 0 !important; /* 防止按钮被压缩 */
				/* 保持原有的3D效果和颜色 */
				display: inline-flex !important;
				align-items: center !important;
				justify-content: center !important;
				border: 1px solid #c0c0c0 !important;
				background: linear-gradient(to bottom, #fdfdfd, #f0f0f0) !important;
				color: #444 !important;
				font-weight: normal !important;
				box-shadow: 0 1px 0 #a0a0a0, 0 2px 3px rgba(0,0,0,0.15), inset 0 1px 0 rgba(255,255,255,0.7) !important;
				text-shadow: 0 1px 0 rgba(255,255,255,0.4) !important;
				transition: all 0.2s ease !important;
				cursor: pointer !important;
				height: auto !important; /* 移除固定高度 */
				line-height: 1.2 !important; /* 确保文本垂直居中 */
			}
			
			/* 分类按钮悬浮效果 */
			.tjbox span:hover:not(.tjactive),
			#qbsj:hover:not(.tjactive),
			#flbox span:hover:not(.tjactive) {
				color: #d82121 !important;
				border-color: #d82121 !important;
				box-shadow: 0 1px 0 #c00, 0 2px 3px rgba(0,0,0,0.15), inset 0 1px 0 rgba(255,255,255,0.7) !important;
				transform: translateY(0) !important;
			}
			
			/* 分类按钮激活状态 */
			.tjbox span.tjactive,
			#qbsj.tjactive,
			#flbox span.tjactive {
				background: linear-gradient(to bottom, #e02626, #d82121) !important;
				color: white !important;
				border: 1px solid #a01818 !important;
				font-weight: bold !important;
				text-shadow: 0 -1px 0 rgba(0,0,0,0.3) !important;
				box-shadow: 0 1px 0 #a01818, 0 2px 3px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.4) !important;
			}
			
			/* PC端大屏幕优化 (≥1920px) */
			@media screen and (min-width: 1920px) {
				.tjbox {
					gap: max(2px, 0.15vw) !important;
				}
				
				.tjbox label {
					font-size: max(14px, 0.7vw) !important;
					margin-right: max(8px, 0.4vw) !important;
				}
				
				.tjbox span,
				#qbsj,
				#flbox span {
					font-size: max(12px, 0.6vw) !important;
					padding: max(5px, 0.25vw) max(10px, 0.5vw) !important;
					border-radius: max(3px, 0.15vw) !important;
				}
			}
			
			/* 平板设备优化 (≤1024px) */
			@media screen and (max-width: 1024px) {
				.tjbox {
					gap: 0.3vw !important;
					padding: 8px !important;
				}
				
				.tjbox label {
					font-size: 1.1vw !important;
					margin-right: 0.8vw !important;
				}
				
				.tjbox span,
				#qbsj,
				#flbox span {
					font-size: 1vw !important;
					padding: 0.4vw 0.9vw !important;
					border-radius: 0.25vw !important;
				}
			}
			
			/* 平板横屏/小屏设备 (≤768px) */
			@media screen and (max-width: 768px) {
				.tjbox {
					gap: 0.4vw !important;
					padding: 6px !important;
				}
				
				.tjbox label {
					font-size: 1.3vw !important;
					margin-right: 1vw !important;
				}
				
				.tjbox span,
				#qbsj,
				#flbox span {
					font-size: 1.2vw !important;
					padding: 0.5vw 1vw !important;
					border-radius: 0.3vw !important;
				}
			}
			
			/* 手机设备 (≤480px) */
			@media screen and (max-width: 480px) {
				.tjbox {
					gap: max(4px, 0.5vw) !important;
					padding: 5px !important;
				}
				
				.tjbox label {
					font-size: max(14px, 2.2vw) !important;
					margin-right: max(8px, 1.2vw) !important;
				}
				
				.tjbox span,
				#qbsj,
				#flbox span {
					font-size: max(12px, 2vw) !important;
					padding: max(5px, 0.6vw) max(10px, 1.5vw) !important;
					border-radius: max(4px, 0.4vw) !important;
				}
			}
			
			/* 超小屏幕 (≤320px) */
			@media screen and (max-width: 320px) {
				.tjbox {
					gap: max(3px, 0.6vw) !important;
					padding: 4px !important;
				}
				
				.tjbox label {
					font-size: max(13px, 2.5vw) !important;
					margin-right: max(6px, 1.5vw) !important;
				}
				
				.tjbox span,
				#qbsj,
				#flbox span {
					font-size: max(11px, 2.3vw) !important;
					padding: max(4px, 0.7vw) max(8px, 2vw) !important;
					border-radius: max(3px, 0.5vw) !important;
				}
			}
			
			/* 横屏模式优化 - 使用视口高度为基准 */
			@media screen and (max-width: 932px) and (orientation: landscape) {
				.tjbox {
					gap: 0.3vh !important;
					padding: 0.5vh !important;
				}
				
				.tjbox label {
					font-size: 1.1vh !important;
					margin-right: 0.8vh !important;
				}
				
				.tjbox span,
				#qbsj,
				#flbox span {
					font-size: 1vh !important;
					padding: 0.4vh 0.8vh !important;
					border-radius: 0.25vh !important;
				}
			}
			
			@media screen and (max-width: 768px) and (orientation: landscape) {
				.tjbox {
					gap: 0.4vh !important;
					padding: 0.4vh !important;
				}
				
				.tjbox label {
					font-size: 1.2vh !important;
					margin-right: 0.9vh !important;
				}
				
				.tjbox span,
				#qbsj,
				#flbox span {
					font-size: 1.1vh !important;
					padding: 0.5vh 1vh !important;
					border-radius: 0.3vh !important;
				}
			}
			
			@media screen and (max-width: 667px) and (orientation: landscape) {
				.tjbox {
					gap: 0.5vh !important;
					padding: 0.3vh !important;
				}
				
				.tjbox label {
					font-size: 1.3vh !important;
					margin-right: 1vh !important;
				}
				
				.tjbox span,
				#qbsj,
				#flbox span {
					font-size: 1.2vh !important;
					padding: 0.6vh 1.2vh !important;
					border-radius: 0.35vh !important;
				}
			}
		</style>
	</head>
	<body class="index">
		<!-- 顶部导航 -->
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<!-- <div class="contenttitlebox">
			<div>
				<a id="zxxx" href="onlinelearning.html">在线学习</a>
				<img src="img/jtright.png" />
				<label class="aa" id="hsname">教学资源</label>
				<span class="filter-note">本页面只展示书籍类型内容</span>
			</div>
		</div> -->
		<div class="content">
			<div class="contenttopview">
				<!-- <div class="contentitem" id="classbox"> -->
					<!-- <a class="titleactive">红色书籍</a>
					<a href="onlinelearning3.html">教学资源</a> -->
				<!-- </div> -->
				<!-- 添加排序选择菜单 -->
				<!-- <div class="contentitem sort-menu">
					<div class="sort-selector">
						<label>排序方式：</label>
						<select id="sortSelect" onchange="changeSortOrder(this.value)">
							<option value="publishedTime,desc">发布时间（新到旧）</option>
							<option value="publishedTime">发布时间（旧到新）</option>
							<option value="createdAt,desc">上传时间（新到旧）</option>
							<option value="createdAt">上传时间（旧到新）</option>
						</select>
					</div>
				</div> -->
				<!-- <div class="contentitem sss">
					<div class="ssdiv">
						<div class="select" onclick="showselect()">
							<label id="selecttxt">书籍</label>
							<img src="img/ssx.png" />
							<img src="img/ssb.png" />
						</div>
						<input />
						<div class="ss">搜索</div>
					</div>
					<div class="dztsg">电子图书馆</div>
					<div class="opbox" id="select">
						<div>书籍</div>
						<div>红色游学</div>
						<div>书籍</div>
						<div>红色游学</div>
					</div>
				</div> -->
			</div>
			<div class="tjbox" style="border: none;">
				<label>分类:</label>
				<span onclick="selects(this)" id="qbsj" class="tjactive" data-id="0">全部</span>
				<div id="flbox" style="display: flex; flex-wrap: wrap; flex: 1; gap: 0.2vw;">
					
				</div>
			</div>
			
			<!-- 添加独立的排序指示器 -->
			<!-- <div class="sort-info-box">
				<div class="sort-indicator" id="sortIndicator">
					<i class="sort-icon"></i>
					<span>按上传时间排序</span>
				</div>
			</div>
			 -->
			<div id="htmlbox" class="sjbox">

			</div>
			
			<!-- 加载指示器 -->
			<div id="loading-indicator" style="display:none; text-align:center; padding:30px;">
				<div class="loading-container">
					<div class="loading-circle"></div>
					<div class="loading-text">正在加载数据，请稍候...</div>
				</div>
			</div>
			
			<div class="fybox" id="fyq">
				<span id="sy">首页</span>
				<span id="syy">上一页</span>
				<div class="num" id="num">
				</div>
				<span id="xyy">下一页</span>
				<span id="wy">尾页</span>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/clipboard.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				let userinfo = sessionStorage.getItem("userinfo")
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				getclass('onlinelearning2.html')
				getfooterlink()
				getclassid()
				getallfllist()
				getclassch()
				
				// 恢复用户排序偏好或设置默认排序为上传时间降序
				const preferredSort = localStorage.getItem("preferredSortOrder");
				if (preferredSort) {
					// console.log("恢复用户排序偏好:", preferredSort);
					$("#sortSelect").val(preferredSort);
					currentSort = preferredSort;
					isClientSorting = preferredSort.startsWith("createdAt");
					updateSortIndicator(preferredSort);
				} else {
					// 设置默认排序为上传时间降序
					// console.log("设置默认排序为上传时间降序");
					$("#sortSelect").val("createdAt,desc");
					currentSort = "createdAt,desc";
					isClientSorting = true;
					localStorage.setItem("preferredSortOrder", currentSort);
					updateSortIndicator("createdAt,desc");
				}
				
				// 添加全局图片错误处理
				$(document).on('error', 'img', function() {
					console.warn("图片加载失败:", this.src);
					if (!this.classList.contains('img-error')) {
						this.onerror = null;
						this.src = 'img/default-book-cover.jpg';
						this.classList.add('img-error');
					}
				});
				
				// 输出当前 baseurl 信息以便调试
				// console.log("当前baseurl:", baseurl);
			})
			
			// 添加格式化点击率数字的函数
			function formatViewCount(count) {
				if (!count && count !== 0) return '0';
				
				count = parseInt(count, 10);
				
				if (count < 1000) {
					return count.toString();
				} else if (count < 10000) {
					// 1K - 9.9K
					return (count / 1000).toFixed(1).replace('.0', '') + 'K';
				} else if (count < 1000000) {
					// 10K - 999K
					return Math.floor(count / 1000) + 'K';
				} else {
					// 1M+
					return (count / 1000000).toFixed(1).replace('.0', '') + 'M';
				}
			}
			let classid = null
			let selectid = 0
			
			let pageindex = 1
			let pageSize = 20  // 修改为每页20本书，与URL一致
			let pages = 1
			let currentSort = "publishedTime"; // 修改默认排序方式为按发布时间排列(不指定升降序)
			let totalPages = 0; // 总页数
			let isTimeReversed = false; // 不再需要反转时间
			let isLoading = false; // 添加加载状态标记，防止重复加载
			let isClientSorting = false; // 是否使用前端排序
			
			// 优化全局变量
			let collectedBooks = []; // 当前已收集的书籍
			let targetBooksCount = 20; // 初始目标书籍数量改为20
			let booksPerRow = 5; // 每行书籍数量
			let initialRows = 3; // 初始加载行数
			let rowsPerLoad = 1; // 每次滚动加载的行数
			let currentLoadingPage = 1; // 当前正在加载的页码
			let hasMoreData = true; // 是否还有更多数据可加载
			let displayedBooksCount = 0; // 当前已显示的书籍数量
			let isScrollLoadEnabled = false; // 是否启用滚动加载
			let maxPagePreload = 2; // 最多预加载页数
			
			// 新增排序函数：前端基于createdAt字段排序
			function sortBooksByTime(books, sortField, isDescending) {
				// console.log(`前端排序: 按${sortField}字段${isDescending ? '降序' : '升序'}排列`);
				
				// 创建副本以避免修改原始数据
				const sortedBooks = [...books];
				
				// 检查排序字段是否有效
				if (!sortField || typeof sortField !== 'string') {
					console.error("排序字段无效:", sortField);
					return sortedBooks;
				}
				
				return sortedBooks.sort((a, b) => {
					// 确保两个对象都有指定的日期字段
					if (!a[sortField] || !b[sortField]) {
						// 如果任一对象缺少时间字段，将其放在末尾
						if (!a[sortField]) return isDescending ? 1 : -1;
						if (!b[sortField]) return isDescending ? -1 : 1;
						return 0;
					}
					
					// 解析ISO 8601日期字符串为Date对象
					const dateA = new Date(a[sortField]);
					const dateB = new Date(b[sortField]);
					
					// 检查是否为有效日期
					if (isNaN(dateA.getTime()) || isNaN(dateB.getTime())) {
						console.warn("发现无效日期:", a[sortField], b[sortField]);
						if (isNaN(dateA.getTime())) return isDescending ? 1 : -1;
						if (isNaN(dateB.getTime())) return isDescending ? -1 : 1;
						return 0;
					}
					
					// 根据升序或降序进行排序
					return isDescending ? 
						(dateB.getTime() - dateA.getTime()) : 
						(dateA.getTime() - dateB.getTime());
				});
			}
			
			// 更改排序方式
			function changeSortOrder(sortValue) {
				// console.log("切换排序方式:", sortValue);
				
				// 更新排序方式
				currentSort = sortValue;
				
				// 判断是否需要前端排序
				isClientSorting = false; // 禁用前端排序，始终使用服务器排序
				
				// 更新排序指示器文本
				updateSortIndicator(sortValue);
				
				// 重置分页状态
				pageindex = 1;
				
				// 重新加载数据
				getInitialPageCount();
				
				// 记录用户偏好
				localStorage.setItem("preferredSortOrder", sortValue);
				
				// 更新排序按钮状态
				$("#sortSelect").val(sortValue);
			}
			
			// 更新排序指示器
			function updateSortIndicator(sortValue) {
				let indicatorText = "排序: ";
				
				if (sortValue === "publishedTime,desc") {
					indicatorText += "发布时间（新→旧）";
				} else if (sortValue === "publishedTime") {
					indicatorText += "发布时间（旧→新）";
				} else if (sortValue === "createdAt,desc") {
					indicatorText += "上传时间（新→旧）";
				} else if (sortValue === "createdAt") {
					indicatorText += "上传时间（旧→新）";
				}
				
				$("#sortIndicator span").text(indicatorText);
			}
			
			// 获取总页数并加载第一页数据
			function getInitialPageCount() {
				// 显示加载指示器
				$("#loading-indicator").show();
				$("#htmlbox").hide();
				$("#fyq").hide(); // 隐藏分页器，我们使用无限滚动
				isLoading = true; // 设置加载状态
				
				// 构建请求参数 - 使用正确的categoryId和参数
				const params = {
					categoryId: classid, // 使用动态获取的classid，不再硬编码
					pageSize: 20,
					pageNum: 1,
					_t: new Date().getTime(),
					sort: "publishedTime" // 确保始终包含sort参数
				};
				
				// 只有在不是"全部"类别时才添加redBookId参数
				if (selectid != 0) {
					params.redBookId = selectid;
				}
				// 注意：不再添加空的redBookId参数
				
				// console.log("初始请求参数:", params);
				
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: params,
					dataType: 'json',
					success: (res) => {
						// 添加调试信息输出
						debugApiResponse(res, 1);
						
						if (res.code == '200') {
							// console.log("获取总页数:", res.data);
							// 添加数据为空的判断
							if (!res.data || !res.data.list || res.data.list.length === 0) {
								console.warn("API返回数据为空");
								$("#loading-indicator").hide();
								$("#htmlbox").show();
								isLoading = false;
								showEmptyState("没有找到相关书籍数据", true);
								return;
							}
							
							totalPages = res.data.pages;
							// console.log("总页数:", totalPages);
							
							// 重置显示计数器
							displayedBooksCount = 0;
							
							// 直接使用第一页数据
							if (res.data.list && res.data.list.length > 0) {
								// 过滤出书籍数据 - 只保留有redBookId的数据
								let booksOnly = res.data.list.filter(item => {
									return item && item.redBookId;  // 只保留redBookId存在且不为空的数据
								});
								
								// console.log(`第1页原始数据项数:`, res.data.list.length, `过滤后书籍数:`, booksOnly.length);
								
								// 检查过滤后是否为空
								if (booksOnly.length === 0) {
									console.warn("过滤后没有符合条件的书籍数据");
									$("#loading-indicator").hide();
									$("#htmlbox").show();
									isLoading = false;
									showEmptyState("没有找到符合条件的书籍数据", true);
									return;
								}
								
								// 如果使用前端排序，执行排序
								if (isClientSorting) {
									const sortField = currentSort.split(',')[0]; // 提取字段名
									const isDescending = currentSort.endsWith('desc'); // 检查是否降序
									booksOnly = sortBooksByTime(booksOnly, sortField, isDescending);
									// console.log("前端排序后数据:", booksOnly);
								}
								
								// 重置收集的书籍
								collectedBooks = booksOnly;
								
								// 使用旧版页面的方式显示书籍
								let newhtml = "";
								for (let k = 0; k < booksOnly.length; k++) {
									const book = booksOnly[k];
									newhtml += '<div class="txtitem">' +
										'<div class="topitem" onclick="ininfo(this)" data-id="'+ book.id +'">';
									
									if (book.thumbPath != null && book.thumbPath.length > 0) {
										// 修复封面图片路径，确保使用正确的URL
										// 检查是否已经包含完整URL
										let imgSrc = book.thumbPath[0];
										if (imgSrc.startsWith('http')) {
											newhtml += '<img src="' + imgSrc + '"/>';
										} else {
											// 如果不是完整URL，加上baseurl
											newhtml += '<img src="' + baseurl + imgSrc + '" onerror="this.onerror=null; this.src=\'img/default-book-cover.jpg\'"/>';
										}
									} else {
										// 添加默认封面
										newhtml += '<div class="default-book-cover">' +
											'<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24">' +
											'<path fill="#999" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm4-8c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4 4 1.79 4 4z"/>' +
											'</svg>' +
											'<span>没有封面</span>' +
											'</div>';
									}
									
									newhtml += '<div class="icobox">' +
										'<label><svg class="view-count-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/></svg>' + formatViewCount(book.clickCount) +
										'</label></div></div>' +
										'<div class="bottomitem">' +
										'<div class="title">' + (book.title || '未命名书籍') + '</div>' +
										'<div class="zz">作者: ' + (book.author || '未知') + '</div>' + 
										'<div class="zz">' + (book.postName || '') + '</div></div></div>';
								}
								
															$("#htmlbox").html(newhtml);
							$("#loading-indicator").hide();
							$("#htmlbox").show();
							
							// 更新总页数
							totalPages = res.data.pages;
							pages = totalPages;
							
							// 使用老版本的分页显示方式
							updatePaginationOld();
							
							// 检测新加载的书籍标题是否需要滚动
							setTimeout(checkTitlesForScrolling, 500); // 增加延迟，确保DOM完全加载
							
							isLoading = false;
							} else {
								// 没有数据时显示空状态
								$("#loading-indicator").hide();
								$("#htmlbox").show();
								showEmptyState("没有找到书籍数据", true);
								isLoading = false;
							}
						} else {
							// 错误处理
							console.error("获取总页数失败:", res);
							isLoading = false;
							$("#loading-indicator").hide();
							$("#htmlbox").show();
							cocoMessage.error(3000, "获取数据失败，错误代码: " + res.code);
							
							// 显示空状态
							showEmptyState("加载失败，请稍后重试或重置筛选条件", true);
						}
					},
					error: (err) => {
						// 网络错误处理
						console.error("网络请求错误:", err);
						isLoading = false;
						$("#loading-indicator").hide();
						$("#htmlbox").show();
						cocoMessage.error(3000, "网络请求错误，请检查网络连接和API地址");
						
						// 显示空状态
						showEmptyState("网络连接错误，请检查API地址和参数", true);
					}
				});
			}
			
			// 显示空状态的通用函数
			function showEmptyState(message, showDebugInfo = false) {
				let debugInfoHtml = '';
				
				// 如果需要显示调试信息
				if (showDebugInfo) {
					debugInfoHtml = `
						<div style="margin-top:20px; font-size:12px; color:#888; text-align:left; padding:10px; background:#f9f9f9; border-radius:4px;">
							<p><strong>当前请求参数:</strong></p>
							<p>categoryId: ${classid}</p>
							<p>pageSize: 20</p>
							<p>sort: publishedTime</p>
							<p>redBookId: ${selectid != 0 ? selectid : '(未设置)'}</p>
						</div>
					`;
				}
				
				let emptyHtml = `
					<div style="text-align:center; padding:50px; color:#666;">
						<div style="font-size:60px; margin-bottom:20px; color:#ddd;">
							<i class="book-icon">📚</i>
						</div>
						<h3 style="margin-bottom:15px; color:#333;">${message || "没有找到相关书籍"}</h3>
						<p style="margin-bottom:15px;">当前分类或筛选条件下没有书籍数据</p>
						<div style="margin:15px 0;">
							<button onclick="resetFilters()" style="
								background-color: #d82121;
								color: white;
								border: none;
								padding: 8px 15px;
								border-radius: 4px;
								cursor: pointer;
								margin-right: 10px;
							">重置筛选条件</button>
							<button onclick="retryLoadData()" style="
								background-color: #ffffff;
								color: #d82121;
								border: 1px solid #d82121;
								padding: 8px 15px;
								border-radius: 4px;
								cursor: pointer;
								margin-right: 10px;
							">重试加载</button>
							<button onclick="loadAllBooks()" style="
								background-color: #ffffff;
								color: #d82121;
								border: 1px solid #d82121;
								padding: 8px 15px;
								border-radius: 4px;
								cursor: pointer;
							">直接加载全部书籍</button>
						</div>
						${debugInfoHtml}
					</div>
				`;
				$("#htmlbox").html(emptyHtml);
			}
			
			// 添加重试加载函数
			function retryLoadData() {
				cocoMessage.info(1000, "正在重新加载数据...");
				getInitialPageCount();
			}
			
			// 修改loadBooksPage函数，改进空数据处理
			function loadBooksPage(pageToLoad, isFirstLoad = false) {
				// 如果正在加载，不执行
				if (isLoading && !isFirstLoad) return;
				
				// 显示加载指示器
				if (isFirstLoad) {
					$("#loading-indicator").show();
					$("#htmlbox").html(''); // 清空内容区
					$("#htmlbox").hide();
				} else {
					// 显示底部加载指示器
					$("#bottom-loading").show();
				}
				
				$("#fyq").show(); // 显示分页器
				isLoading = true;
				
				// 构建请求参数 - 参考旧版本文件的处理方式
				const requestParams = {
					categoryId: classid, // 使用动态获取的classid，不再硬编码
					pageSize: 20,
					pageNum: pageToLoad,
					_t: new Date().getTime(),
					sort: "publishedTime" // 确保始终包含sort参数
				};
				
				// 只有在不是"全部"类别时才添加redBookId参数
				if (selectid != 0) {
					requestParams.redBookId = selectid;
				}
				// 注意：不再添加空的redBookId参数
				
				// console.log(`加载第${pageToLoad}页数据，参数:`, requestParams);
				
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: requestParams,
					dataType: 'json',
					success: (res) => {
						// 添加调试信息输出
						debugApiResponse(res, pageToLoad);
						
						if (res.code == '200') {
							// 检查数据是否为空
							if (!res.data || !res.data.list || res.data.list.length === 0) {
								console.warn(`第${pageToLoad}页数据为空`);
								isLoading = false;
								$("#loading-indicator").hide();
								$("#bottom-loading").hide();
								$("#htmlbox").show();
								
								if (isFirstLoad) {
									showEmptyState("没有找到书籍数据", true);
								} else {
									cocoMessage.info(3000, "已加载全部数据");
								}
								return;
							}
							
							// 过滤出书籍数据 - 只保留有redBookId的数据
							let displayList = res.data.list || [];
							let booksOnly = displayList.filter(item => {
								return item && item.redBookId;  // 只保留redBookId存在且不为空的数据
							});
							
							// console.log(`第${pageToLoad}页原始数据项数:`, displayList.length, `过滤后书籍数:`, booksOnly.length);
							
							// 检查过滤后是否为空
							if (booksOnly.length === 0) {
								console.warn(`第${pageToLoad}页过滤后没有符合条件的书籍`);
								isLoading = false;
								$("#loading-indicator").hide();
								$("#bottom-loading").hide();
								$("#htmlbox").show();
								
								if (isFirstLoad) {
									showEmptyState("没有找到符合条件的书籍", true);
								} else {
									cocoMessage.info(3000, "已加载全部有效数据");
								}
								return;
							}
							
							// 如果使用前端排序，执行排序
							if (isClientSorting) {
								const sortField = currentSort.split(',')[0]; // 提取字段名
								const isDescending = currentSort.endsWith('desc'); // 检查是否降序
								booksOnly = sortBooksByTime(booksOnly, sortField, isDescending);
								// console.log("前端排序后数据:", booksOnly);
							}
							
							// 重置收集的书籍和显示计数
							collectedBooks = booksOnly;
							displayedBooksCount = 0;
							
							// console.log("当前收集书籍总数:", collectedBooks.length);
							
							// 更新总页数
							pages = res.data.pages;
							totalPages = pages;
							pageindex = pageToLoad; // 更新页面索引
							
										// 添加这里：显示书籍数据
			let newhtml = "";
			for (let k = 0; k < booksOnly.length; k++) {
				const book = booksOnly[k];
				newhtml += '<div class="txtitem">' +
					'<div class="topitem" onclick="ininfo(this)" data-id="'+ book.id +'">';
								
								if (book.thumbPath != null && book.thumbPath.length > 0) {
									// 修复封面图片路径
									let imgSrc = processImageUrl(book.thumbPath[0]);
									newhtml += '<img src="' + imgSrc + '" ' +
										'onerror="this.onerror=null; this.src=\'img/default-book-cover.jpg\'"/>';
								} else {
									// 添加默认封面
									newhtml += '<div class="default-book-cover">' +
										'<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24">' +
										'<path fill="#999" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm4-8c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4 4 1.79 4 4z"/>' +
										'</svg>' +
										'<span>没有封面</span>' +
										'</div>';
								}
								
								newhtml += '<div class="icobox">' +
									'<label><svg class="view-count-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/></svg>' + formatViewCount(book.clickCount) +
									'</label></div></div>' +
									'<div class="bottomitem">' +
									'<div class="title">' + (book.title || '未命名书籍') + '</div>' +
									'<div class="zz">作者: ' + (book.author || '未知') + '</div>' + 
									'<div class="zz">' + (book.postName || '') + '</div></div></div>';
							}
							
							$("#htmlbox").html(newhtml);
							$("#loading-indicator").hide();
							$("#htmlbox").show();
							
							// 使用老版本的分页显示方式
							updatePaginationOld();
							
							isLoading = false;
							$("#bottom-loading").hide();
						} else {
							// 错误处理
							console.error(`加载第${pageToLoad}页数据失败:`, res);
							isLoading = false;
							$("#loading-indicator").hide();
							$("#bottom-loading").hide();
							$("#htmlbox").show();
							
							if (isFirstLoad) {
								showEmptyState(`加载失败，错误代码: ${res.code}`, true);
							} else {
								cocoMessage.error(3000, `获取数据失败，错误代码: ${res.code}`);
							}
						}
					},
					error: (err) => {
						// 网络错误处理
						console.error(`加载第${pageToLoad}页数据网络错误:`, err);
						isLoading = false;
						$("#loading-indicator").hide();
						$("#bottom-loading").hide();
						$("#htmlbox").show();
						
						if (isFirstLoad) {
							showEmptyState("网络连接错误，请检查API地址和参数", true);
						} else {
							cocoMessage.error(3000, "网络请求错误，请检查网络连接");
						}
						
						// 添加重试按钮
						$("#htmlbox").append(`
							<div style="text-align:center; margin-top:20px;">
								<button onclick="retryLoadData()" style="
									background-color: #ffffff;
									color: #d82121;
									border: 1px solid #d82121;
									padding: 8px 15px;
									border-radius: 4px;
									cursor: pointer;
								">重试加载</button>
							</div>
						`);
					}
				});
			}
			
			// 新函数：显示固定书籍
			function displayFixedBooks() {
				// 清空显示区域
				$("#htmlbox").html('');
				
				// 如果没有书籍，显示空状态
				if (!collectedBooks || collectedBooks.length === 0) {
					$("#loading-indicator").hide();
					$("#bottom-loading").hide();
					$("#htmlbox").show();
					showEmptyState("没有找到相关书籍", true);
					isLoading = false;
					return;
				}
				
				// 检查书籍数据是否有效
				const validBooks = collectedBooks.filter(book => book && book.id);
				if (validBooks.length === 0) {
					$("#loading-indicator").hide();
					$("#bottom-loading").hide();
					$("#htmlbox").show();
					showEmptyState("书籍数据无效", true);
					isLoading = false;
					return;
				}
				
				// 计算行数 - 每行5本，最多显示20本
				const booksPerRow = 5; // 每行5本书
				const maxBooks = Math.min(collectedBooks.length, 20); // 最多显示20本
				const rowsToShow = Math.ceil(maxBooks / booksPerRow);
				
				// console.log(`准备显示${maxBooks}本书籍，共${rowsToShow}行`);
				
				// 按行显示书籍
				for (let rowIndex = 0; rowIndex < rowsToShow; rowIndex++) {
					const rowStartIndex = rowIndex * booksPerRow;
					const rowEndIndex = Math.min(rowStartIndex + booksPerRow, maxBooks);
					
					// 获取这一行的书籍
					const rowBooks = collectedBooks.slice(rowStartIndex, rowEndIndex);
					
					// console.log(`生成第${rowIndex+1}行，索引${rowStartIndex}-${rowEndIndex-1}，书籍数量:${rowBooks.length}`);
					
					// 创建行元素
					const row = $('<div class="book-row"></div>');
					
					// 添加这一行的书籍
					rowBooks.forEach(book => {
						const bookHtml = createBookItemHtml(book);
						row.append(bookHtml);
					});
					
					// 如果这行不满，填充空白保持布局
					if (rowBooks.length < booksPerRow) {
						for (let i = 0; i < booksPerRow - rowBooks.length; i++) {
							row.append('<div class="txtitem empty-book"></div>');
						}
					}
					
					// 添加到页面
					$("#htmlbox").append(row);
				}
				
				// 初始化图片懒加载
				setupImageLazyLoading();
				
				// 隐藏加载指示器
				$("#loading-indicator").hide();
				$("#bottom-loading").hide();
				$("#htmlbox").show();
				
				// 重置加载状态
				isLoading = false;
				
				// console.log(`显示完成: ${maxBooks}本书籍（${rowsToShow}行）`);
			}

			// 更新分页控件
			function updatePagination() {
				// 清空页码容器
				$("#num").empty();
				
				// 如果总页数小于等于1，隐藏分页器
				if (totalPages <= 1) {
					$("#fyq").hide();
					return;
				}
				
				// 显示分页器
				$("#fyq").show();
				
				// 计算要显示的页码范围
				let startPage = Math.max(1, pageindex - 2);
				let endPage = Math.min(totalPages, pageindex + 2);
				
				// 调整起始页和结束页，确保显示5个页码
				if (endPage - startPage < 4) {
					if (startPage === 1) {
						endPage = Math.min(5, totalPages);
					} else {
						startPage = Math.max(1, endPage - 4);
					}
				}
				
				// 更新首页和上一页按钮状态
				$("#sy").toggleClass('disabled', pageindex === 1);
				$("#syy").toggleClass('disabled', pageindex === 1);
				
				// 生成页码
				for (let i = startPage; i <= endPage; i++) {
					let pageItem = $('<span>' + i + '</span>');
					if (i === pageindex) {
						pageItem.addClass('active');
					}
					pageItem.on('click', function() {
						if (pageindex != i && !isLoading) {
							pageindex = i;
							loadBooksPage(i, true);
						}
					});
					$("#num").append(pageItem);
				}
				
				// 更新下一页和尾页按钮状态
				$("#xyy").toggleClass('disabled', pageindex === totalPages);
				$("#wy").toggleClass('disabled', pageindex === totalPages);
				
				// 绑定导航按钮事件
				$("#sy").off('click').on('click', function() {
					if (pageindex > 1 && !isLoading && !$(this).hasClass('disabled')) {
						pageindex = 1;
						loadBooksPage(1, true);
					}
				});
				
				$("#syy").off('click').on('click', function() {
					if (pageindex > 1 && !isLoading && !$(this).hasClass('disabled')) {
						pageindex--;
						loadBooksPage(pageindex, true);
					}
				});
				
				$("#xyy").off('click').on('click', function() {
					if (pageindex < totalPages && !isLoading && !$(this).hasClass('disabled')) {
						pageindex++;
						loadBooksPage(pageindex, true);
					}
				});
				
				$("#wy").off('click').on('click', function() {
					if (pageindex < totalPages && !isLoading && !$(this).hasClass('disabled')) {
						pageindex = totalPages;
						loadBooksPage(totalPages, true);
					}
				});
			}

			// 不再需要初始化滚动监听，禁用自动加载功能
			function initScrollLoadListener() {
				// 不执行任何操作，禁用滚动加载
				// console.log("滚动加载功能已禁用，使用分页导航");
			}

			// 优化图片懒加载
			function setupImageLazyLoading() {
				// 检查浏览器是否支持IntersectionObserver
				if ('IntersectionObserver' in window) {
					const imageObserver = new IntersectionObserver((entries, observer) => {
						entries.forEach(entry => {
							if (entry.isIntersecting) {
								const img = entry.target;
								const src = img.getAttribute('data-src');
								
								if (src) {
									img.src = src;
									img.classList.add('loaded');
									observer.unobserve(img);
								}
							}
						});
					}, {
						rootMargin: '200px 0px', // 提前200px加载
						threshold: 0.01
					});
					
					// 为所有懒加载图片添加观察
					document.querySelectorAll('.lazy-image').forEach(img => {
						imageObserver.observe(img);
					});
				} else {
					// 回退方案：对于不支持IntersectionObserver的浏览器
					document.querySelectorAll('.lazy-image').forEach(img => {
						const src = img.getAttribute('data-src');
						if (src) {
							img.src = src;
							img.classList.add('loaded');
						}
					});
				}
			}

			// 创建书籍HTML内容
			function createBooksHtml(books, booksPerRow) {
				let html = '';
				
				// 如果没有书籍，显示空状态
				if (books.length === 0) {
					return `
						<div style="text-align:center; padding:50px; color:#666;">
							<div style="font-size:60px; margin-bottom:20px; color:#ddd;">
								<i class="book-icon">📚</i>
							</div>
							<h3 style="margin-bottom:15px; color:#333;">没有找到相关书籍</h3>
							<p>当前分类或筛选条件下没有书籍</p>
							<p style="margin-top:10px;">
								<button onclick="resetFilters()" style="
									background-color: #d82121;
									color: white;
									border: none;
									padding: 8px 15px;
									border-radius: 4px;
									cursor: pointer;
									margin-top: 10px;
								">重置筛选条件</button>
							</p>
						</div>
					`;
				}
				
				// 开始第一行
				html += '<div class="book-row">';
				
				// 遍历所有书籍
				for (let i = 0; i < books.length; i++) {
					// 每booksPerRow本书开始新行
					if (i > 0 && i % booksPerRow === 0) {
						html += '</div><div class="book-row">';
					}
					
					// 添加书籍项
					html += createBookItemHtml(books[i]);
				}
				
				// 如果最后一行不满，添加空项保持布局
				const lastRowItems = books.length % booksPerRow;
				if (lastRowItems > 0 && lastRowItems < booksPerRow) {
					for (let i = 0; i < booksPerRow - lastRowItems; i++) {
						html += '<div class="txtitem empty-book"></div>';
					}
				}
				
				// 结束最后一行
				html += '</div>';
				
				return html;
			}

			// 创建单个书籍的HTML
			function createBookItemHtml(item) {
				let html = '<div class="txtitem">' +
							'<div class="topitem" onclick="ininfo(this)" data-id="'+ item.id +'">';
				
						if (item.thumbPath != null && item.thumbPath.length > 0) {
					// 使用懒加载方式，使用data-src替代src
					// 使用实体字符作为占位，避免404错误
					html += '<img src="data:image/gif;base64,R0lGODlhAQABAAAAACH5BAEKAAEALAAAAAABAAEAAAICTAEAOw==" data-src="' + 
						baseurl + item.thumbPath[0] + '" alt="' + (item.title || '封面图') + '" class="lazy-image"/>';
						} else {
							// 没有封面图时显示默认封面
					html += '<div class="default-book-cover">' +
								'<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24">' +
								'<path fill="#999" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm4-8c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4 4 1.79 4 4z"/>' +
								'</svg>' +
								'<span>没有封面</span>' +
								'</div>';
						}
				
				html += '<div class="icobox">' +
							'<label><svg class="view-count-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/></svg>' + formatViewCount(item.clickCount) +
							'</label></div></div>' +
							'<div class="bottomitem">' +
							'<div class="title">' + (item.title || '未命名书籍') + '</div>' +
							'<div class="zz">作者: ' + (item.author || '未知') + '</div>' + 
							'<div class="zz">' + (item.postName || '') + '</div></div></div>';
				
				return html;
			}
			
			function ininfo(item){
				window.location.href = "onlinelearning5.html?id="+$(item).attr("data-id")
			}
			function getallfllist(){
				$.ajax({
					url: baseurl + "/web/redbook/list ",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200' && res.data && res.data.list && res.data.list.length > 0) {
							let html = "";
							// 过滤掉具有删除标记的分类
							let filteredList = res.data.list.filter(item => item.delFlag !== 1);
							// console.log("原始分类数量:", res.data.list.length, "过滤后分类数量:", filteredList.length);
							
							filteredList.forEach((item) => {
								if (item && item.id !== undefined) {
									if(item.id !== selectid){
										html += '<span onclick="selects(this)" data-id="' + item.id + '">' + (item.name || '未命名') + '</span>';
									} else {
										html += '<span class="tjactive" onclick="selects(this)" data-id="' + item.id + '">' + (item.name || '未命名') + '</span>';
									}
								}
							});
							
							if (html === "") {
								// 如果没有有效分类，创建默认分类
								createDefaultBookCategories();
							} else {
								$("#flbox").html(html);
								
								// 应用样式到选中的项
								if (selectid != 0) {
									// 如果选中的不是"全部"
									$('#flbox span.tjactive').css({"color": "white", "background-color": "#d82121", "font-weight": "500"});
									$("#qbsj").css({"color": "#333", "background-color": "#f5f5f5", "font-weight": "normal"});
								} else {
									// 如果选中的是"全部"
									$("#qbsj").css({"color": "white", "background-color": "#d82121", "font-weight": "500"});
								}
							}
						} else {
							// 创建默认分类数据
							createDefaultBookCategories();
						}
					},
					error: (err) => {
						console.error("获取分类列表失败:", err);
						// 创建默认分类数据
						createDefaultBookCategories();
					}
				});
				
				// 创建默认书籍分类
				function createDefaultBookCategories() {
					let html = '<span onclick="selects(this)" data-id="1">马列主义</span>' +
							   '<span onclick="selects(this)" data-id="2">毛泽东思想</span>' +
							   '<span onclick="selects(this)" data-id="3">邓小平理论</span>' +
							   '<span onclick="selects(this)" data-id="4">习近平思想</span>';
					$("#flbox").html(html);
					
					// 确保"全部"按钮样式正确
					$("#qbsj").css({"color": "white", "background-color": "#d82121", "font-weight": "500"});
					
					cocoMessage.info(3000, "使用默认分类数据");
				}
			}
			function selects(item){
				pageindex = 1
				let allfl = $("#flbox span")
				if($(item).attr("data-id")!=0){
					for(let i =0;i<allfl.length;i++){
						if($(allfl[i]).attr("data-id") == $(item).attr("data-id")){
							$(allfl[i]).attr("class","tjactive")
							// 添加内联样式确保文字为白色
							$(allfl[i]).css({"color": "white", "background-color": "#d82121", "font-weight": "500"});
						}else{
							$(allfl[i]).attr("class","")
							// 恢复默认样式
							$(allfl[i]).css({"color": "#333", "background-color": "#f5f5f5", "font-weight": "normal"});
						}
					}
					$("#qbsj").attr("class","")
					// 恢复"全部"按钮的默认样式
					$("#qbsj").css({"color": "#333", "background-color": "#f5f5f5", "font-weight": "normal"});
				}else{
					for(let i =0;i<allfl.length;i++){
						$(allfl[i]).attr("class","")
						// 恢复默认样式
						$(allfl[i]).css({"color": "#333", "background-color": "#f5f5f5", "font-weight": "normal"});
					}
					$("#qbsj").attr("class","tjactive")
					// 添加内联样式确保"全部"按钮文字为白色
					$("#qbsj").css({"color": "white", "background-color": "#d82121", "font-weight": "500"});
				}
				
				selectid = $(item).attr("data-id")
				
				// 重置所有状态
				currentLoadingPage = 1;
				hasMoreData = true;
				collectedBooks = [];
				
				// 分类变更后，重新获取总页数
				getInitialPageCount();
			}
			function getclassid() {
				// 添加重试计数和重试延迟变量
				let retryCount = 0;
				const maxRetries = 1;
				const retryDelay = 1000; // 1秒后重试
				
				function tryFetchCategories() {
					// 不再使用固定的categoryId，恢复API获取分类ID
					$.ajax({
						url: baseurl + "/web/category/book",
						type: 'GET',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						dataType: 'json',
						success: (res) => {
							if (res.code == '200' && res.data && res.data.length > 0) {
								classid = res.data[0].id;
								// 获取总页数并加载最后一页
								getInitialPageCount();
							} else {
								// console.log("获取分类数据失败或数据为空，尝试重试或使用备用数据");
								// 如果API返回为空，但有重试次数，则重试
								if (retryCount < maxRetries) {
									retryCount++;
									setTimeout(tryFetchCategories, retryDelay);
								} else {
									// 达到最大重试次数，使用备用方案
									useFallbackCategoryId();
								}
							}
						},
						error: (err) => {
							console.error("获取分类数据请求失败:", err);
							// 发生错误且有重试次数，则重试
							if (retryCount < maxRetries) {
								retryCount++;
								setTimeout(tryFetchCategories, retryDelay);
							} else {
								// 达到最大重试次数，使用备用方案
								useFallbackCategoryId();
							}
						}
					});
				}
				
				// 使用备用分类ID
				function useFallbackCategoryId() {
					// 尝试使用URL参数中的ID
					classid = getUrlParam('id');
					
					// 如果URL中没有ID，使用默认值
					if (!classid) {
						// 使用之前成功的值作为备用
						classid = 912354240784109568;
						cocoMessage.info(3000, "使用备用分类ID，部分内容可能受限");
					}
					
					// 继续加载数据
					getInitialPageCount();
				}
				
				// 开始尝试获取分类数据
				tryFetchCategories();
			}
			let clipboard = new ClipboardJS('.copybtn');
			clipboard.on('success', function(e) {
				e.clearSelection();
				cocoMessage.success(1000, "复制成功！")
			});
			
			clipboard.on('error', function(e) {
				cocoMessage.error(1000, "复制失败！")
			});
			function getsjlist() {
				// 重置书籍收集状态
				collectedBooks = [];
				currentLoadingPage = 1;
				hasMoreData = true;
				
				// 开始加载第一页数据
				loadBooksPage(1, true);
			}

			function resetFilters() {
				// 如果正在加载中，不执行重置
				if (isLoading) {
					cocoMessage.info(1000, "正在加载数据，请稍候...");
					return;
				}

				// 直接调用加载全部书籍的函数
				loadAllBooks();
				
				cocoMessage.success(1000, "已重置筛选条件");
			}

			// 添加缺失的函数
			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
			
			function getclassch(){
				// 添加重试计数和重试延迟变量
				let retryCount = 0;
				const maxRetries = 1;
				const retryDelay = 1000; // 1秒后重试
				
				function tryFetchData() {
					$.ajax({
						url: baseurl + "/web/category/teacher",
						type: 'GET',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						dataType: 'json',
						success: (res) => {
							if (res.code == '200' && res.data && res.data.length > 0) {
								classdate = res.data[0];
								getlist();
							} else {
								// console.log("获取教师分类数据失败或数据为空，尝试加载备用数据");
								// 如果API返回为空，但有重试次数，则重试
								if (retryCount < maxRetries) {
									retryCount++;
									setTimeout(tryFetchData, retryDelay);
								} else {
									// 达到最大重试次数，使用备用方案
									loadFallbackCategories();
								}
							}
						},
						error: (err) => {
							console.error("获取教师分类数据请求失败:", err);
							// 发生错误且有重试次数，则重试
							if (retryCount < maxRetries) {
								retryCount++;
								setTimeout(tryFetchData, retryDelay);
							} else {
								// 达到最大重试次数，使用备用方案
								loadFallbackCategories();
							}
						}
					});
				}
				
				// 备用分类加载函数 - 使用硬编码的分类数据
				function loadFallbackCategories() {
					// 创建一个基本的fallback对象
					classdate = { 
						children: [
							{ 
								name: '红色书籍', 
								redirectUrl: 'onlinelearning2.html',
								id: getUrlParam('id') || 0
							},
							{ 
								name: '教学资源', 
								redirectUrl: 'onlinelearning3.html',
								id: 0
							}
						] 
					};
					// 使用备用数据渲染UI
					getlist();
					// 显示轻微提示
					cocoMessage.info(3000, "分类信息加载中，部分功能可能受限");
				}
				
				// 开始尝试获取数据
				tryFetchData();
			}

			function getlist(data) {
				let classData = data || classdate;
				let classhtml = "";
				
				// 检查classData是否存在且有children属性
				if (!classData || !classData.children) {
					console.error("分类数据缺失或结构不正确", classData);
					$("#classbox").html("<p>分类数据加载失败</p>");
					return;
				}
				
				for (let i = 0; i < classData.children.length; i++) {
					if (classData.children[i].name == '红色书籍') {
						classhtml += '<a class="titleactive" href="' + classData.children[i].redirectUrl + '?id=' + classData
							.children[i].id + '">' + classData.children[i].name + '</a>';
						$("#hsname").html(classData.children[i].name);
					} else {
						classhtml += '<a href="' + classData.children[i].redirectUrl + '?id=' + classData.children[i].id + '">' +
							classData.children[i].name + '</a>';
					}
				}
				$("#classbox").html(classhtml);
			}

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			$("#select div").on('click', function() {
				$("#selecttxt").html($(this).html())
				$("#select").hide()
			})

			function showselect() {
				$("#select").show()
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}

			// 添加新函数: 加载反转页面数据
			function loadReversePage(pageToLoad) {
				// 转换页码显示
				const displayPageNum = totalPages - pageToLoad + 1;
				pageindex = displayPageNum;
				
				// console.log(`加载反转数据: 实际页码${pageToLoad}，显示为第${displayPageNum}页`);
				
				// 调用原有加载函数但传入实际页码
				loadBooksPage(pageToLoad, true);
			}

			// 添加与onlinelearning2-0512.html类似的分页实现
			function updatePaginationOld() {
				if (pages <= 1) {
					// 只有一页或没有数据时，隐藏分页
					$("#fyq").hide();
					return;
				}
				
				// 显示分页器
				$("#fyq").show();
				
				let numhtml = "";
				// 简化页码显示，最多显示5个页码
				const maxPagesToShow = 5;
				
				// 计算起始和结束页码
				let startPage = Math.max(1, pageindex - Math.floor(maxPagesToShow / 2));
				let endPage = Math.min(pages, startPage + maxPagesToShow - 1);
				
				// 调整startPage，确保我们显示最多maxPagesToShow个页码
				if (endPage - startPage + 1 < maxPagesToShow) {
					startPage = Math.max(1, endPage - maxPagesToShow + 1);
				}
				
				// 是否显示第一页的快捷方式
				if (startPage > 1) {
					numhtml += '<span onclick="getnewlist(1)">1</span>';
					if (startPage > 2) {
						numhtml += '<span class="ellipsis">...</span>';
					}
				}
				
				// 生成页码
				for (let a = startPage; a <= endPage; a++) {
					if (pageindex == a) {
						numhtml += '<span class="active" onclick="getnewlist(' + a + ')">' + a + '</span>';
					} else {
						numhtml += '<span onclick="getnewlist(' + a + ')">' + a + '</span>';
					}
				}
				
				// 是否显示最后一页的快捷方式
				if (endPage < pages) {
					if (endPage < pages - 1) {
						numhtml += '<span class="ellipsis">...</span>';
					}
					numhtml += '<span onclick="getnewlist(' + pages + ')">' + pages + '</span>';
				}
				
				// 设置分页按钮状态和点击事件
				// 首页和上一页
				if (pageindex <= 1) {
					$("#sy").addClass('disabled');
					$("#syy").addClass('disabled');
				} else {
					$("#sy").removeClass('disabled');
					$("#syy").removeClass('disabled');
					$("#sy").attr("onclick", "getnewlist(1)");
					$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")");
				}
				
				// 下一页和尾页
				if (pageindex >= pages) {
					$("#xyy").addClass('disabled');
					$("#wy").addClass('disabled');
				} else {
					$("#xyy").removeClass('disabled');
					$("#wy").removeClass('disabled');
					$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")");
					$("#wy").attr("onclick", "getnewlist(" + pages + ")");
				}
				
				// 更新页码显示
				$("#num").html(numhtml);
			}

			// 添加页码切换函数
			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是第最后一页了！");
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！");
					}
				} else {
					pageindex = index;
					loadBooksPage(index, true);
				}
			}

			// 添加重试加载全部书籍的函数
			function loadAllBooks() {
				// 重置选择状态
				selectid = 0;
				pageindex = 1;
				
				// 更新UI选择状态
				$("#flbox span").removeClass("tjactive");
				$("#qbsj").addClass("tjactive");
				
				// 显示加载指示器
				$("#loading-indicator").show();
				$("#htmlbox").hide();
				
				// 执行简化版的图书加载请求
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						categoryId: classid,
						pageSize: 20,
						pageNum: 1,
						_t: new Date().getTime(),
						sort: "publishedTime"
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let newhtml = "";
							if (res.data && res.data.list && res.data.list.length > 0) {
								for (let k = 0; k < res.data.list.length; k++) {
									const book = res.data.list[k];
									newhtml += '<div class="txtitem">' +
										'<div class="topitem" onclick="ininfo(this)" data-id="'+ book.id +'">';
									
									if (book.thumbPath != null && book.thumbPath.length > 0) {
										// 修复封面图片路径，确保使用正确的URL
										let imgSrc = book.thumbPath[0];
										// 使用统一的图片URL处理函数
										imgSrc = processImageUrl(imgSrc);
										
										newhtml += '<img src="' + imgSrc + '" ' +
											'onerror="this.onerror=null; this.src=\'img/default-book-cover.jpg\'; this.classList.add(\'img-error\');" ' +
											'onload="this.classList.add(\'img-loaded\');" ' +
											'alt="' + (book.title || '封面图') + '" />';
									} else {
										// 添加默认封面
										newhtml += '<div class="default-book-cover">' +
											'<svg xmlns="http://www.w3.org/2000/svg" width="40" height="40" viewBox="0 0 24 24">' +
											'<path fill="#999" d="M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm0 18c-4.41 0-8-3.59-8-8s3.59-8 8-8 8 3.59 8 8-3.59 8-8 8zm4-8c0 2.21-1.79 4-4 4s-4-1.79-4-4 1.79-4 4-4 4 1.79 4 4z"/>' +
											'</svg>' +
											'<span>没有封面</span>' +
											'</div>';
									}
									
									newhtml += '<div class="icobox">' +
										'<label><svg class="view-count-icon" viewBox="0 0 24 24" xmlns="http://www.w3.org/2000/svg"><path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/></svg>' + formatViewCount(book.clickCount) +
										'</label></div></div>' +
										'<div class="bottomitem">' +
										'<div class="title">' + (book.title || '未命名书籍') + '</div>' +
										'<div class="zz">作者: ' + (book.author || '未知') + '</div>' + 
										'<div class="zz">' + (book.postName || '') + '</div></div></div>';
								}
								$("#htmlbox").html(newhtml);
								$("#loading-indicator").hide();
								$("#htmlbox").show();
								
								// 更新分页
								pages = res.data.pages;
								updatePaginationOld();
							} else {
								// 没有数据
								$("#loading-indicator").hide();
								$("#htmlbox").show();
								$("#htmlbox").html('<div style="text-align:center; padding:30px;">没有找到书籍数据</div>');
								$("#fyq").hide();
							}
						} else {
							// 显示错误
							$("#loading-indicator").hide();
							$("#htmlbox").show();
							$("#htmlbox").html('<div style="text-align:center; padding:30px;">加载失败，请重试</div>');
							cocoMessage.error(3000, "加载失败: " + res.code);
						}
					},
					error: (err) => {
						// 显示错误
						$("#loading-indicator").hide();
						$("#htmlbox").show();
						$("#htmlbox").html('<div style="text-align:center; padding:30px;">网络请求失败，请重试</div>');
						cocoMessage.error(3000, "网络请求失败");
					}
				});
			}

			// 添加图片URL处理函数
			function processImageUrl(url) {
				if (!url) return 'img/default-book-cover.jpg';
				
				// 如果是完整URL，检查是否需要处理
				if (url.startsWith('http://') || url.startsWith('https://')) {
					// 替换localhost URL为相对路径
					if (url.includes('localhost:5500/api/')) {
						return url.replace(/http(s)?:\/\/localhost:5500\/api\//, baseurl + '/');
					}
					// 以http开头但不包含baseurl域名，可能需要处理
					if (baseurl && !url.includes(new URL(baseurl).hostname)) {
						// console.log("外部域名图片可能需要代理:", url);
					}
					return url;
				}
				
				// 如果以/api开头，确保使用正确的路径
				if (url.startsWith('/api/')) {
					return baseurl + url.substring(4); // 去掉/api前缀
				}
				
				// 如果以/开头
				if (url.startsWith('/')) {
					return baseurl + url;
				}
				
				// 否则添加baseurl
				return baseurl + '/' + url;
			}

			// 添加调试工具函数
			function debugApiResponse(response, page) {
				console.group(`页码${page}请求响应调试信息`);
				// console.log("响应状态:", response.code);
				// console.log("完整响应:", response);
				
				if (response.data) {
					// console.log("数据总条数:", response.data.total);
					// console.log("总页数:", response.data.pages);
					
					if (response.data.list) {
						// console.log("返回数据项数:", response.data.list.length);
						
						// 检查是否有有效的书籍数据
						const booksCount = response.data.list.filter(item => item && item.redBookId).length;
						// console.log("有效书籍数量:", booksCount);
						
						if (booksCount === 0 && response.data.list.length > 0) {
							console.warn("警告: 返回的数据中没有有效的书籍对象!");
							// console.log("第一个数据项:", response.data.list[0]);
						}
					} else {
						console.warn("警告: 响应中没有list数组!");
					}
				} else {
					console.warn("警告: 响应中没有data对象!");
				}
				
				console.groupEnd();
			}
		</script>
			<script>
		$("#backtop").hide()
		$(function() {
			$(window).scroll(function() {
				if ($(window).scrollTop() > 600) {
					$("#backtop").show()
				} else {
					$("#backtop").hide()
				}
			})
			
			// 页面加载完成后检测需要滚动的标题
			setTimeout(checkTitlesForScrolling, 800); // 延长时间确保完全加载
		})
		$("#backtop").on('click', function() {
			$("body,html").animate({
				scrollTop: 0
			}, 300)
		})
		
		// 检测哪些标题需要滚动
		function checkTitlesForScrolling() {
			$('.txtitem .bottomitem .title').each(function() {
				const $title = $(this);
				
				// 测量文本宽度和容器宽度
				const titleText = $title.text();
				const $testDiv = $('<div>').text(titleText)
					.css({
						'position': 'absolute', 
						'visibility': 'hidden', 
						'white-space': 'nowrap',
						'font-weight': $title.css('font-weight'),
						'font-size': $title.css('font-size'),
						'font-family': $title.css('font-family')
					})
					.appendTo('body');
				
				const textWidth = $testDiv.width();
				$testDiv.remove();
				
				const containerWidth = $title.width();
				
				// 如果文本宽度小于容器，不需要滚动
				if (textWidth <= containerWidth) {
					$title.addClass('no-scroll');
					// 为不需要滚动的项设置动画持续时间为0
					$title.attr('data-duration', '0s');
				} else {
					$title.removeClass('no-scroll');
					// 根据文本长度调整动画持续时间，最短8秒，最长15秒
					let duration = Math.min(15, Math.max(8, Math.ceil(textWidth / containerWidth * 8)));
					$title.attr('data-duration', duration + 's');
					
					// 设置自定义动画持续时间
					$title.on('mouseenter', function() {
						const duration = $(this).attr('data-duration') || '8s';
						$(this).css('animation-duration', duration);
					});
				}
			});
			
			// 添加鼠标离开时重置动画的处理
			$('.txtitem').off('mouseleave').on('mouseleave', function() {
				const $title = $(this).find('.bottomitem .title');
				// 移除动画，让标题恢复到初始状态
				$title.css({
					'animation': 'none',
					'width': '',
					'max-width': '',
					'overflow': ''
				});
				// 强制DOM重绘
				$title[0].offsetHeight;
				// 移除内联样式，让CSS规则重新生效
				$title.css({
					'animation': '',
					'width': '',
					'max-width': '',
					'overflow': ''
				});
			});
		}
	</script>
	</body>
	<!-- 底部加载指示器 -->
	<div id="bottom-loading">
		<div class="loading-dot"></div>
		<div class="loading-dot"></div>
		<div class="loading-dot"></div>
	</div>
	
	<!-- 添加强制应用样式的脚本 -->
	<script>
		// 确保所有激活状态的分类标签文字都是白色
		function enforceActiveStyles() {
			// 强制所有激活项显示为白色文字
			$('.tjactive').css({
				'color': 'white !important',
				'background-color': '#d82121 !important',
				'font-weight': '500 !important'
			});
			
			// 特别处理"全部"按钮
			if ($("#qbsj").hasClass('tjactive')) {
				$("#qbsj").attr('style', 'color: white !important; background-color: #d82121 !important; font-weight: 500 !important; box-shadow: 0 1px 0 #a01818, 0 2px 3px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.4) !important; text-shadow: 0 -1px 0 rgba(0,0,0,0.3) !important;');
			}
			
			// 特别处理其他分类按钮
			$("#flbox span.tjactive").attr('style', 'color: white !important; background-color: #d82121 !important; font-weight: 500 !important; box-shadow: 0 1px 0 #a01818, 0 2px 3px rgba(0,0,0,0.2), inset 0 1px 0 rgba(255,255,255,0.4) !important; text-shadow: 0 -1px 0 rgba(0,0,0,0.3) !important;');
			
			// 为所有分类按钮应用紧凑样式
			$('.tjbox span').each(function() {
				if (!$(this).hasClass('tjactive')) {
					$(this).css({
						'display': 'inline-flex',
						'align-items': 'center',
						'justify-content': 'center',
						'min-width': 'fit-content',
						'height': 'auto',
						'padding': '0.3vw 0.7vw',
						'margin': '0',
						'border-radius': '3px',
						'font-size': '0.8vw',
						'position': 'relative',
						'cursor': 'pointer',
						'border': '1px solid #c0c0c0',
						'background': 'linear-gradient(to bottom, #fdfdfd, #f0f0f0)',
						'color': '#444',
						'font-weight': 'normal',
						'box-shadow': '0 1px 0 #a0a0a0, 0 2px 3px rgba(0,0,0,0.15), inset 0 1px 0 rgba(255,255,255,0.7)',
						'text-shadow': '0 1px 0 rgba(255,255,255,0.4)',
						'transition': 'all 0.2s ease',
						'line-height': '1.2'
					});
				}
			});
		}
		
		// 页面加载后执行
		$(document).ready(function() {
			// 延迟执行以确保DOM完全加载
			setTimeout(enforceActiveStyles, 100);
			
			// 每次点击分类后都重新强制应用样式
			$(".tjbox span").on('click', function() {
				// 等待DOM更新后执行
				setTimeout(enforceActiveStyles, 100);
			});
			
			// 适配屏幕大小，动态调整按钮大小
			function adjustButtonSizes() {
				const vw = Math.max(document.documentElement.clientWidth || 0, window.innerWidth || 0);
				let fontSize, padding, borderRadius;
				
				if (vw >= 1920) {
					fontSize = '12px';
					padding = '5px 10px';
					borderRadius = '3px';
				} else if (vw >= 1024) {
					fontSize = Math.max(12, vw * 0.006) + 'px';
					padding = Math.max(4, vw * 0.004) + 'px ' + Math.max(8, vw * 0.005) + 'px';
					borderRadius = Math.max(3, vw * 0.0025) + 'px';
				} else if (vw >= 768) {
					fontSize = Math.max(11, vw * 0.012) + 'px';
					padding = Math.max(4, vw * 0.005) + 'px ' + Math.max(8, vw * 0.01) + 'px';
					borderRadius = Math.max(3, vw * 0.003) + 'px';
				} else {
					fontSize = Math.max(10, vw * 0.02) + 'px';
					padding = Math.max(3, vw * 0.006) + 'px ' + Math.max(6, vw * 0.015) + 'px';
					borderRadius = Math.max(2, vw * 0.004) + 'px';
				}
				
				$('.tjbox span, #qbsj, #flbox span').not('.tjactive').css({
					'font-size': fontSize,
					'padding': padding,
					'border-radius': borderRadius
				});
			}
			
			// 初始调整和窗口大小变化时调整
			adjustButtonSizes();
			$(window).resize(adjustButtonSizes);
		});
	</script>
</html>
