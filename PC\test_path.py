import os
import pandas as pd
from datetime import datetime

# 打印当前工作目录
print(f"当前工作目录: {os.getcwd()}")

# 创建输出目录
os.makedirs("output", exist_ok=True)

# 创建一个简单的DataFrame
df = pd.DataFrame({
    "序号": [1, 2, 3],
    "名称": ["测试1", "测试2", "测试3"]
})

# 保存到Excel文件
timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
output_path = f"output/测试_{timestamp}.xlsx"
df.to_excel(output_path, index=False)
print(f"文件已保存到: {output_path}")
print(f"文件的绝对路径: {os.path.abspath(output_path)}") 