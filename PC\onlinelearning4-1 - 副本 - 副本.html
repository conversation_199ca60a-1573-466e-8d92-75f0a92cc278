<!DOCTYPE html>
<html>
	<head>
		
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
		<title>思政一体化平台-在线学习-详情</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/onlinelearning.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>
		<style>
			.zsdlabel {
				background: #f3f3f3;
				border-radius: 0.3125rem;
				padding: 0.3125rem 0.625rem;
				font-size: 0.833333rem;
				color: #333333;
				display: inline-block;
				margin-bottom: 0.625rem;
				margin-right: 0.625rem;
			}

			.zsdlabel img {
				width: 1rem;
				float: left;
				padding-right: 0.625rem;
			}
			
			/* 移动端适配样式 */
			@media (max-width: 768px) {
				/* 顶部导航 */
				.topview_1 {
					padding: 10px;
					flex-direction: column;
					align-items: center;
				}
				
				.logo {
					margin-bottom: 10px;
				}
				
				.loginview {
					width: 100%;
					justify-content: space-between;
				}
				
				.ssview {
					width: 60%;
				}
				
				/* 导航菜单 */
				.menu-toggle {
					display: block;
					position: absolute;
					top: 15px;
					right: 15px;
					background: none;
					border: none;
					font-size: 24px;
					color: #A65D57;
					cursor: pointer;
					z-index: 1000;
				}
				
				.topview_2 .itembox {
					flex-direction: column;
					max-height: 0;
					overflow: hidden;
					transition: max-height 0.3s ease;
				}
				
				.topview_2 .itembox.expanded {
					max-height: 300px;
				}
				
				.topview_2 .menuitem {
					width: 100%;
					text-align: left;
					padding: 10px 15px;
					border-bottom: 1px solid rgba(0,0,0,0.05);
				}
				
				/* 内容区域 */
				.content {
					padding: 10px;
				}
				
				.contenttitlebox {
					overflow-x: auto;
					white-space: nowrap;
					padding: 10px 0;
				}
				
				/* 课件页面布局 */
				.kjtop {
					flex-direction: column;
				}
				
				.jttopitem {
					width: 100%;
					margin-bottom: 10px;
				}
				
				.kjname {
					font-size: 16px;
				}
				
				.kjzz {
					flex-wrap: wrap;
				}
				
				.kjzz label {
					margin-bottom: 5px;
				}
				
				/* 课件窗口 */
				.kjbox {
					flex-direction: column;
				}
				
				.kjview {
					width: 100%;
					margin-bottom: 15px;
				}
				
				.kjdg {
					width: 100%;
					max-height: none;
				}
				
				/* 课程详情 */
				.kjjj {
					margin-top: 15px;
				}
				
				.topkjjjs {
					overflow-x: auto;
					white-space: nowrap;
					padding-bottom: 10px;
				}
				
				.topkjjjs div {
					padding: 8px 15px;
					font-size: 14px;
				}
				
				/* 评价区域 */
				.pinjiabox {
					padding: 0 10px;
				}
				
				.pjbbb {
					width: 90%;
					max-width: 90%;
				}
				
				.xxbox {
					padding: 10px;
				}
				
				/* 全屏预览 */
				#qp {
					padding: 10px;
				}
				
				#qp img {
					max-width: 100%;
					max-height: 80vh;
				}
				
				/* 知识点标签 */
				.zsdlabel {
					margin: 3px;
					font-size: 12px;
				}
				
				/* 底部 */
				footer .yqlj .box {
					flex-direction: column;
				}
				
				footer .yqlj .box a {
					margin: 5px 0;
				}
				
				/* 返回顶部按钮 */
				#backtop {
					position: fixed;
					bottom: 20px;
					right: 20px;
					z-index: 999;
					width: 40px;
					height: 40px;
					background: rgba(166, 93, 87, 0.8);
					border-radius: 50%;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					box-shadow: 0 2px 10px rgba(0,0,0,0.2);
				}
				
				#backtop div {
					font-size: 10px;
					color: white;
				}
				
				#backtop img {
					width: 15px;
					height: 15px;
				}
			}
			
			/* 超小屏幕优化 */
			@media (max-width: 480px) {
				/* PDF浏览器 */
				.tcbox {
					width: 95% !important;
					max-width: 95% !important;
				}
				
				.pdfbox {
					width: 100% !important;
					height: 80vh !important;
				}
				
				/* 课件详情 */
				.kjboxtitle {
					font-size: 14px;
				}
				
				.jj p {
					font-size: 13px;
				}
				
				/* 操作按钮 */
				.czbox div label {
					padding: 6px 12px;
					margin: 0 3px;
					font-size: 12px;
				}
				
				/* 课程标签 */
				.zsdlabel {
					padding: 4px 8px;
					font-size: 11px;
					margin: 2px;
				}
				
				.zsdlabel img {
					width: 14px;
					padding-right: 5px;
				}
				
				/* 评分区域 */
				.xx {
					width: 20px;
					height: 20px;
					background-size: contain;
				}
				
				.xxbox div span {
					font-size: 12px;
					width: 80px;
				}
				
				.xxbtnview div {
					padding: 6px 12px;
					font-size: 12px;
				}
			}
		</style>
	</head>
	<body class="index">
		<div class="tcbox" id="tcbox">

			<div class="topviewsss">
				<a onclick="closetc()">关闭</a>
				<label id="pdfname"></label>
			</div>
			<div class="panel-body pdfbox">
				<a id="pdf" class="media" href=""></a>
			</div>
		</div>
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<button class="menu-toggle" id="menuToggle" aria-label="菜单切换">
					<i class="fas fa-bars"></i>
				</button>
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="contenttitlebox">
			<div>
				<a id="zxxx" href="onlinelearning.html">在线学习</a>
				<img src="img/jtright.png" />
				<a href="onlinelearning3.html" id="hsname"></a>
				<img src="img/jtright.png" />
				<label class="aa" id="titlesss"></label>
			</div>
		</div>
		<div class="content">
			<div class="kjtop">
				<div class="jttopitem">
					<div class="kjname" id="kjname"></div>
					<div class="kjzz">
						<label><img src="img/kjzz.png" /><span id="zz"></span></label>
						<label><img src="img/kjyj.png" /><span id="yj"></span></label>
						<label><img src="img/kjpf.png" /><span id="pf"></span></label>
					</div>
				</div>
				<div class="jttopitem">
					<label class="kjpfbtn" onclick="showpfbox()"><img src="img/kjpf2.png" />课件评分</label>
				</div>
			</div>

			<div id="bbbb2" class="kjbox">
				<div class="kjview">
					<img id="img" src="" />
					<div class="topbag">
						这是一段课件名称
					</div>
					<div class="czbox" id="ppt">
						<img onclick="qpshow()" src="img/qp.png" />
						<div>
							<label onclick="syy()">上一页</label>
							<label onclick="xyy()">下一页</label>
						</div>
					</div>
					<div class="czbox2" id="video">
						<video autoplay controls id="videos" width="100%" controlsList="nodownload"></video>
					</div>
				</div>
				<div class="kjdg" id="pptbox">
					<div class="kjdgname">课件大纲</div>
					<!-- <div class="kjss">
						<div><img src="img/ss.png" />
							<input placeholder="请输入关键词" type="text" />
						</div>
					</div> -->
					<div class="kjlist" id="list">

					</div>
				</div>
				<div class="kjdg" id="videobox">
					<div class="kjdgname">视频列表</div>
					<div class="kjlist" id="list2">

					</div>
				</div>
			</div>
			<div class="kjjj">
				<div class="topkjjjs" id="selectbox">
					<div onclick="selects(this)" data-id="1" class="aaakj"><img id="xqimg" src="img/kcxq1.png" />课程详情
					</div>
					<div onclick="selects(this)" data-id="2"><img id="zsdimg" src="img/zsd2.png" />知识点</div>
					<div onclick="selects(this)" data-id="3"><img id="pjimg" src="img/kcpf2.png" />课程评价</div>
				</div>
				<div id="pf1" class="kjxqbox">
					<div class="kjboxtitle">课程简介:</div>
					<div class="jj">
						<p id="jj"></p>
					</div>
					<div id="kcgl">
						<div id="kj111">
							<div class="kjboxtitle">课件:</div>
							<div class="abq">
								<label onclick="showsss(this)" id="kj">查看详情</label>
							</div>
						</div>
						<div id="kj222">
							<div class="kjboxtitle">课后测试:</div>
							<div class="abq">
								<label id="stshow" onclick="showpdf(this)">查看详情</label>
							</div>
							<!-- <a href="" download="" id="stxz">立即下载</a> -->
						</div>
					</div>
				</div>
				<div id="pf2" style="display: none;" class="kjpfbox">
					<div class="zsdbox" style="padding: 0.78125rem;" id="zsdbox">

					</div>
				</div>

				<div id="pf3" style="display: none;" class="kjpfbox">
					<div class="pjtitle">课程评价详情</div>
					<div class="xxbox" id="xxbox2">


					</div>
				</div>

			</div>
		</div>
		<div class="pinjiabox">
			<div class="pjbbb">
				<div class="topbags">
					<img src="img/kjpf2.png" />
					课件评分
				</div>
				<div class="xxbox" id="xxbox">

				</div>
				<div class="str" id="pfwz" style="display: none;">您已评无法重复提交评分！</div>
				<div class="xxbtnview" id="wpf">
					<div onclick="pinfensubmit()">确定</div>
					<div onclick="hidepfbox()">取消</div>
				</div>
				<div class="xxbtnview" style="display: none;" id="ypf">
					<div class="gbgbh" onclick="hidepfbox()">关闭</div>
				</div>
			</div>
		</div>
		<div id="qp" onclick="hideqp()">
			<img id="img2" src="" />
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
			function closetc() {
				$("#tcbox").hide()
			}

			function showsss(item) {
				window.location.href = "onlinelearning4.html?id=" + $(item).attr("data-id")
			}

			function hidepfbox() {
				$(".pinjiabox").attr("style", "display: none")
			}

			function showpfbox() {
				$(".pinjiabox").attr("style", "display: flex")
			}

			function showpdf(item) {
				$.ajax({
					url: baseurl + "/course/view/" + $(item).attr("data-cid"),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {}
				})
				$.ajax({
					url: baseurl + "/course/meta/" + $(item).attr("data-id"),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							$("#pdfname").html(res.data.cmsResourcesCourseMetaList[0].attachName)
							$("#tcbox").show()
							$("#pdf").attr("href", baseurl + res.data.cmsResourcesCourseMetaList[0].attachPath)
							$('a.media').media()
						}
					}
				})
			}
			let imgindex = 0
			let imglist = null
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
					getclass('onlinelearning.html')
					getinfo()
					getfooterlink()
					getclassid()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
			})

			function xzxx(item) {
				let itemid = $(item).attr("data-id")
				//1 0星  2 半星  3  满星
				//当点击一个星星时  这个星包括前面的星全部满星 同时 data-id=3  后面的星星全部取消 data-id = 1
				if (itemid == 1) {
					$(item).attr('class', 'xx xx3').attr("data-id", "3")
					$(item).prevAll('label').attr("class", "xx xx3").attr("data-id", "3")
				} else if (itemid == 3) {
					//当点击的星星是满星时   这个星星后面的所有星星取消 并且 data-id =1
					$(item).attr('class', 'xx xx1').attr("data-id", "1")
					$(item).nextAll('label').attr("class", "xx xx1").attr("data-id", "1")
				}

				//黑星 0分   红星 1分   半星 0.5分
				let num = 0
				let allxx = $(item).prevAll('label')
				for (let i = 0; i < allxx.length; i++) {
					if ($(allxx[i]).attr("data-id") == '1') {
						num += 0
					} else if ($(allxx[i]).attr("data-id") == '3') {
						num += 1
					}
				}
				if ($(item).attr("data-id") == '1') {
					num += 0
				} else if ($(item).attr("data-id") == '3') {
					num += 1
				}
				// console.log(num)
				$(item).nextAll("div").html(num + '分')
			}

			function selects(item) {
				let allitem = $("#selectbox div")
				for (let i = 0; i < allitem.length; i++) {
					$(allitem[i]).attr("class", "")
				}
				$(item).attr("class", "aaakj")
				let thisid = $(item).attr("data-id")
				if (thisid == 1) {
					$("#xqimg").attr("src", "img/kcxq1.png")
					$("#pjimg").attr("src", "img/kcpf2.png")
					$("#zsdimg").attr("src", "img/zsd2.png")
					$("#pf1").show()
					$("#pf2").hide()
					$("#pf3").hide()
				} else if (thisid == 2) {
					$("#xqimg").attr("src", "img/kcxq2.png")
					$("#pjimg").attr("src", "img/kcpf2.png")
					$("#zsdimg").attr("src", "img/zsd1.png")
					$("#pf1").hide()
					$("#pf2").show()
					$("#pf3").hide()
				} else if (thisid == 3) {
					$("#xqimg").attr("src", "img/kcxq2.png")
					$("#pjimg").attr("src", "img/kcpf1.png")
					$("#zsdimg").attr("src", "img/zsd2.png")
					$("#pf1").hide()
					$("#pf2").hide()
					$("#pf3").show()
				}
			}

			function getclassid() {
				$.ajax({
					url: baseurl + "/category/teaching",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							classid = res.data[0].id
							pflid = res.data[0].parentId
							$("#hsname").html(res.data[0].name)
						}
					}
				})
			}
			let time1 = Date.now()
			let classid = null
			let pflid = null
			let xkid = null
			
			let videotime = null

			var vid = document.getElementById("videos");
			vid.onloadedmetadata = function() {
				// console.log('metadata loaded!');
				// console.log(vid.duration); //打印时长
				videotime = vid.duration
			};
		
			

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			function pinfensubmit() {
				let all = $("#xxbox div")
				let json = []
				for (let i = 0; i < all.length; i++) {
					json.push({
						courseId: getUrlParam('id'),
						evaluateId: $(all[i]).attr("data-id"),
						score: $(all[i]).find(".xx3").length
					})
				}
				// console.log(json)
				hidepfbox()
				$.ajax({
					url: baseurl + "/course/evaluate/add",
					type: 'POST',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: JSON.stringify(json),
					dataType: 'json',
					success: (res) => {
						getinfo(true)
					}
				})

			}
			let looklist = []//已看过的图片
			let allimglist = []//全部的图片
			let lookcount = 0 //已经看过的数量
			
			function getinfo(isimg) {
				$.ajax({
					url: baseurl + "/course/meta/" + getUrlParam('id'),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							$("#pf").html(parseFloat(res.data.listCourseEvaluate[3].zh).toFixed(1))
							addview(res.data.courceId)
							let hhhxx = ""
							res.data.listCourseEvaluate.map((item) => {
								if (!item.score) {
									item.score = 0
								}
								if (item.name) {
									hhhxx += '<div><span>' + item.name + ':</span>'
									for (let i = 0; i < 5; i++) {
										if (i < parseInt(item.score)) {
											hhhxx += '<label class="xx xx3"></label>'
										} else {
											if (parseFloat(item.score) > 0) {
												if (parseFloat(item.score) % parseInt(item.score) === 0) {
													hhhxx += '<label class="xx xx1"></label>'
												} else {
													hhhxx += '<label class="xx xx2"></label>'
												}
											} else {
												hhhxx += '<label class="xx xx1"></label>'
											}
										}
									}
									hhhxx += '<div class="fs">' + parseFloat(item.score).toFixed(1) +
										'分</div></div>'
								} else {
									hhhxx += '<div><span>资源综合评分:</span>'
									for (let i = 0; i < 5; i++) {
										if (i < parseInt(item.zh)) {
											hhhxx += '<label class="xx xx3"></label>'
										} else {
											if (i < parseInt(item.zh)) {
												hhhxx += '<label class="xx xx3"></label>'
											} else {
												if (parseFloat(item.zh) > 0) {
													if (parseFloat(item.zh) % parseInt(item.zh) === 0) {
														hhhxx += '<label class="xx xx1"></label>'
													} else {
														hhhxx += '<label class="xx xx2"></label>'
													}
												} else {
													hhhxx += '<label class="xx xx1"></label>'
												}
											}
										}
									}
									hhhxx += '<div class="fs">' + parseFloat(item.zh).toFixed(1) +
										'分</div></div>'
								}
							})
							$("#xxbox2").html(hhhxx)
							if (res.data.isCourseEvaluate == 0) {
								let xxhtml = ''
								res.data.listCurrentCourseEvaluate.map((item) => {
									xxhtml += '<div data-id="' + item.id + '"><span>'
									xxhtml += item.name
									xxhtml += ':</span>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label></div>'
								})
								$("#xxbox").html(xxhtml)
							} else {
								//已评分
								$("#ypf").show()
								$("#wpf").hide()
								$("#pfwz").show()
								let xxhtml = ''
								res.data.listCurrentCourseEvaluate.map((item) => {
									xxhtml += '<div><span>'
									xxhtml += item.name
									xxhtml += ':</span>'
									for (let i = 0; i < 5; i++) {
										if (i < item.score) {
											xxhtml += '<label class="xx xx3"></label>'
										} else {
											xxhtml += '<label class="xx xx1"></label>'
										}
									}
									xxhtml += '</div>'
								})
								$("#xxbox").html(xxhtml)
							}
							if (!isimg) {
								xkid = res.data.projectId
								$("#kjname").html(res.data.title)
								document.title = res.data.title
								$("#titlesss").html(res.data.title)
								$("#zz").html(res.data.author)
								$("#yj").html(res.data.view)
								$("#jj").html(res.data.introduction)
								let kjnum = 0
								let kj2num = 0
								if (res.data.cmsResourcesCourseMetaList[0].attachType == 'mp4') {
									res.data.cmsResourcesCourseMetaList.map((item) => {
										if (item.attachType == 'ppt' || item.attachType == 'pptx') {
											$("#kj").attr("data-id", item.id)
											kjnum += 1
										}
									})
									if (kjnum === 0) {
										$("#kj111").hide()
									}

									res.data.cmsResourcesCourseMetaList.map((item) => {
										if (item.attachType == 'pdf') {
											$("#stshow").attr("data-id", item.id)
											$("#stshow").attr("data-cid", item.courceId)
											kj2num += 1
											// $("#stxz").attr("href", baseurl + item.attachPath)
											// $("#stxz").attr("download", item.attachName+".pdf")
										}
									})
									if (kj2num === 0) {
										$("#kj222").hide()
									}
									$("#bbbb2").show()
									$("#video").show()
									$("#videobox").show()
									$("#ppt").hide()
									$("#pptbox").hide()
									$("#videos").attr("src", baseurl + res.data.cmsResourcesCourseMetaList[0]
										.attachPath)
									""

									let html = '<div class="kjitem"><img class="fm" src="' + baseurl + res.data
										.cmsResourcesCourseMetaList[0].coverPath + '" /><div class="kjbt">' +

										'1</div><img class="zt" src="img/yxx.png" /></div>'

									$("#list2").html(html)
									$("#bbbb2").css("display", "flex")
								} else {
									$("#video").hide()
									$("#videobox").hide()
									$("#ppt").show()
									$("#pptbox").show()
									$("#kcgl").hide()
									$("#img").attr("src", baseurl + res.data.cmsResourcesCourseMetaList[0]
										.attachListPath[0])
									$("#img2").attr("src", baseurl + res.data.cmsResourcesCourseMetaList[0]
										.attachListPath[0])
									let html = ""
									imglist = res.data.cmsResourcesCourseMetaList[0].attachListPath
									res.data.cmsResourcesCourseMetaList[0].attachListPath.map((item, index) => {
										html += '<div class="kjitem" onclick="showimg(this)" data-url2="'+item+'" data-url="' +
											baseurl + item + '" data-index="' + index +
											'"><img class="fm" src="' + baseurl + item +
											'" /><div class="kjbt">' +
											(index + 1) +
											'</div><img class="zt" src="img/yxx.png" /></div>'
									})
									$("#list").html(html)
									$("#bbbb2").css("display", "flex")
								}
							}
						
							if(res.data.cmsResourcesCourseMetaList[0].attachType == 'mp4'){
								// console.log('这是一个视频')
								window.onbeforeunload = function() {
									if (JSON.parse(userinfo).roleName == '学生') {
										let time2 = Date.now()
										let value = time2 - time1
										var days = parseInt(value / (1000 * 60 * 60 * 24))
										var hours = parseInt((value % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
										var minutes = parseInt((value % (1000 * 60 * 60)) / (1000 * 60))
										var seconds = Math.floor((value % (1000 * 60)) / 1000)
										let rwid = null
										if (getUrlParam('taskid')) {
											rwid = getUrlParam('taskid')
										}
									
										let json = {
											infoId: getUrlParam('id'), //信息id
											categoryId: pflid, //所属分类id
											totalInfo: videotime, //总时长，总页数
											positioning: days + "天" + hours + "时" + minutes + "分" + seconds + "秒", //学习了多久，多少页    
											progress: "", //进度 百分比
											type: '在线学习',
											learningTime: value, //学习时长
											sectionId: xkid, //学科ID
											taskId: rwid
										}
									
										fetch(baseurl + '/study/record/add', {
											method: 'POST',
											headers: {
												"Authorization": sessionStorage.getItem("header"),
												"Content-Type": "application/json"
											},
											body: JSON.stringify(json),
											keepalive: true
										})
									}
									return '请确认是否退出?'
								}
							}else if(res.data.cmsResourcesCourseMetaList[0].attachType == 'ppt' || res.data.cmsResourcesCourseMetaList[0].attachType == 'pptx'){
								// console.log('这是一个ppt')
								allimglist = res.data.cmsResourcesCourseMetaList[0].attachListPath
								
								looklist.push(allimglist[0])
								
								lookcount = 1
								
								// console.log('全部图片',allimglist)
								// console.log('已经看过的图片',looklist)
								// console.log('已看过的数量',lookcount)
								
								window.onbeforeunload = function() {
									if (JSON.parse(userinfo).roleName == '学生') {
										let time2 = Date.now()
										let value = time2 - time1
										var days = parseInt(value / (1000 * 60 * 60 * 24))
										var hours = parseInt((value % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
										var minutes = parseInt((value % (1000 * 60 * 60)) / (1000 * 60))
										var seconds = Math.floor((value % (1000 * 60)) / 1000)
										
										let rwid = null
										if (getUrlParam('taskid')) {
											rwid = getUrlParam('taskid')
										}
									
										let json = {
											infoId: getUrlParam('id'), //信息id
											categoryId: pflid, //所属分类id
											totalInfo: allimglist.length, //总时长，总页数
											positioning: days + "天" + hours + "时" + minutes + "分" + seconds + "秒", //学习了多久，多少页    
											progress: lookcount, //已看过
											type: '在线学习',
											learningTime: 0, //学习时长
											sectionId: xkid, //学科ID
											taskId: rwid
										}
									
										fetch(baseurl + '/study/record/add', {
											method: 'POST',
											headers: {
												"Authorization": sessionStorage.getItem("header"),
												"Content-Type": "application/json"
											},
											body: JSON.stringify(json),
											keepalive: true
										})
									}
									return '请确认是否退出?'
								}
							}
						
							
						}


						let zsdhtml = ''

						let knowledge = res.data.knowledge
						// 添加空值检查，确保knowledge存在且非空
						if (knowledge && typeof knowledge === 'string') {
							knowledge.split(',').forEach((item) => {
								if (item != null && item != '') {
									zsdhtml += `<div class="zsdlabel"><img src="img/zsd.png"/>${item}</div>`
								}
							})
						} else {
							// console.log('知识点信息不存在或格式不正确')
							// 如果knowledge为空，可以添加默认文本或者留空
							// zsdhtml = '<div class="zsdlabel"><img src="img/zsd.png"/>暂无知识点</div>'
						}
						$("#zsdbox").html(zsdhtml)
						//<div class="zsdlabel"><img src="img/zsd.png"/>知识点1</div>
					}
				})
			}

			function showimg(item) {
				$("#img").attr("src", $(item).attr("data-url"))
				$("#img2").attr("src", $(item).attr("data-url"))
				imgindex = $(item).attr("data-index")
				//判断已看列表中是否有当前点击的这个图片
				let ishave = 0
				looklist.forEach((myurl)=>{
					if(myurl == $(item).attr("data-url2")){
						ishave += 1
					}
				})
				
				if(ishave == 0){
					//没有看过 则将这个图片添加到已经看过的列表
					looklist.push($(item).attr("data-url2"))
					lookcount += 1 //更新已看过的数量
				}else{
					//已经看过了 则不做处理
				}
				
				// console.log('全部图片',allimglist)
				// console.log('已经看过的图片',looklist)
				// console.log('已看过的数量',lookcount)
			}

			function syy() {
				// console.log(imgindex)
				if (imgindex > 0) { //当前下标记 大于0则表示前面还有
					imgindex -= 1
					$("#img").attr("src", baseurl + imglist[imgindex])
					$("#img2").attr("src", baseurl + imglist[imgindex])
					
					let ishave = 0
					looklist.forEach((item)=>{
						if(item == allimglist[imgindex]){
							ishave += 1
						}
					})
					
					if(ishave == 0){
						//没有看过 则将这个图片添加到已经看过的列表
						looklist.push(allimglist[imgindex])
						lookcount += 1 //更新已看过的数量
					}else{
						//已经看过了 则不做处理
					}
				}
				// console.log('全部图片',allimglist)
				// console.log('已经看过的图片',looklist)
				// console.log('已看过的数量',lookcount)
			}

			function xyy() {
				// console.log(imgindex)
				if (imgindex < (imglist.length - 1)) { //当前下标记 小于 list长度-1  则表示后面还有
					imgindex += 1
					$("#img").attr("src", baseurl + imglist[imgindex])
					$("#img2").attr("src", baseurl + imglist[imgindex])
					
					let ishave = 0
					looklist.forEach((item)=>{
						if(item == allimglist[imgindex]){
							ishave += 1
						}
					})
					
					if(ishave == 0){
						//没有看过 则将这个图片添加到已经看过的列表
						looklist.push(allimglist[imgindex])
						lookcount += 1 //更新已看过的数量
					}else{
						//已经看过了 则不做处理
					}
				}
				// console.log('全部图片',allimglist)
				// console.log('已经看过的图片',looklist)
				// console.log('已看过的数量',lookcount)
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					if (res.data[i].id == getUrlParam('id') || res.data[i].redirectUrl ==
			// 						'onlinelearning.html') {
			// 						$("#zxxx").attr('href', "onlinelearning.html?id=" + res.data[i].id)
			// 						$("#zxxx").html(res.data[i].name)
			// 						pflid = res.data[i].id
			// 						html += '<a class="menuitem active" href="' + res.data[i].redirectUrl + '?id=' +
			// 							res
			// 							.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					} else {
			// 						html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 							.data[
			// 								i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					}
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function hideqp() {
				$("#qp").attr("style", "display: none;")
			}

			function qpshow() {
				$("#qp").attr("style", "display: flex;")
			}

			function addview(id) {
				$.ajax({
					url: baseurl + "/course/view/" + id,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {}
				})
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
		
		<!-- 移动端适配代码 -->
		<script>
			$(document).ready(function() {
				// 移动端菜单切换功能
				$("#menuToggle").on('click', function() {
					$("#menubox").toggleClass('expanded');
				});
				
				// 处理窗口大小变化，确保在切换到桌面视图时菜单正常显示
				$(window).resize(function() {
					if ($(window).width() > 768) {
						$("#menubox").removeClass('expanded');
					}
				});
				
				// 优化移动端PDF和视频播放体验
				if ($(window).width() <= 768) {
					// 视频播放优化
					$("#videos").on('play', function() {
						// 设置视频在移动端全屏播放
						if (this.requestFullscreen) {
							this.requestFullscreen();
						} else if (this.webkitRequestFullscreen) {
							this.webkitRequestFullscreen();
						} else if (this.msRequestFullscreen) {
							this.msRequestFullscreen();
						}
					});
					
					// PDF查看器调整
					window.showpdf = function(item) {
						if(!userinfo) {
							window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html';
							return;
						}
						
						$.ajax({
							url: baseurl + "/course/view/" + $(item).attr("data-cid"),
							type: 'GET',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							dataType: 'json',
							success: (res) => {}
						});
						
						$.ajax({
							url: baseurl + "/course/meta/" + $(item).attr("data-id"),
							type: 'GET',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							dataType: 'json',
							success: (res) => {
								if (res.code == '200') {
									$("#pdfname").html(res.data.cmsResourcesCourseMetaList[0].attachName);
									$("#tcbox").show();
									$("#pdf").attr("href", baseurl + res.data.cmsResourcesCourseMetaList[0].attachPath);
									
									// 在移动端使用更合适的尺寸
									$('a.media').media({
										width: $(window).width() < 768 ? '100%' : 800, 
										height: $(window).width() < 768 ? '80vh' : 850
									});
								}
							}
						});
					};
					
					// 优化全屏图片查看
					window.qpshow = function() {
						$("#qp").css({
							"display": "flex",
							"align-items": "center",
							"justify-content": "center"
						});
					};
				}
				
				// 优化返回顶部按钮
				$("#backtop").css({
					"position": "fixed",
					"bottom": "20px",
					"right": "20px",
					"z-index": "999"
				});
			});
		</script>
	</body>
</html>
