body {
	background: #fbfbfb;
}

.topview2 {
	background: url(../img/topbag2.png) no-repeat;
	background-size: 100%;
	height: 4.6875rem;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 1.5625rem;
	color: #ffffff;
	font-weight: bold;
	letter-spacing: 0.416666rem;
}

.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 2.083333rem;
	background: #56080a;
	font-size: 0.9375rem;
	color: #FFFFFF;
	line-height: 2.083333rem;
	text-align: center;
	z-index: 4;
	display: flex;
	align-items: center;
	justify-content: center;
}
.footer img{
	margin-left: 0.260417rem;
	padding-right: 0.260417rem;
}

.content {
	position: fixed;
	width: 66.666666rem;
	left: 0;
	right: 0;
	top: 5.8125rem;
	bottom: 3.25rem;
	margin: auto;
	display: flex;
}

.contentleft {
	width: 100%;
	margin-right: 0.78125rem;
	position: relative;
	background: #FFFFFF;
}

.contentright {
	width: 15.625rem;
	background: #FFFFFF;
	position: relative;
}

.sjtitle {
	position: relative;
}

.sjtitle img {
	width: 100%;
	display: block;
}

.sbag {
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	bottom: 0;
}

.stitle {
	font-size: 1.145833rem;
	color: #FFFFFF;
	font-weight: bold;
	text-align: center;
	line-height: 4.166666rem;
}

.sbottom {
	color: #ffffff;
	font-size: 0.729166rem;
	text-align: center;
}

.sbottom label {
	margin: 0px 0.78125rem;
}

.title2 {
	background: #c00714;
	font-size: 0.833333rem;
	color: #FFFFFF;
	line-height: 1.458333rem;
	padding-left: 0.78125rem;
	padding-right: 0.78125rem;
	margin-top: 0.052083rem;
}

.dttime {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	height: 2.1875rem;
	line-height: 2.1875rem;
	font-size: 1.145833rem;
	color: #e56c74;
	background: #c00714;
	text-align: center;
}

.dttime span {
	padding-left: 0.78125rem;
	color: #FFFFFF;
}

.scrollview1 {
	position: absolute;
	top: 7.5rem;
	left: 0;
	right: 0;
	bottom: 2.1875rem;
	overflow-y: auto;
}

.scrollview1::-webkit-scrollbar {
	/*滚动条整体样式*/

	width: 5px;
	/*高宽分别对应横竖滚动条的尺寸*/

	height: 1px;

}

.scrollview1::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/

	border-radius: 10px;

	-webkit-box-shadow: inset 0 0 5px #c00714;

	background: #535353;

}

.scrollview1::-webkit-scrollbar-track {
	/*滚动条里面轨道*/

	-webkit-box-shadow: inset 0 0 5px #FFFFFF;

	border-radius: 10px;

	background: #EDEDED;

}

textarea::-webkit-scrollbar {
	/*滚动条整体样式*/

	width: 5px;
	/*高宽分别对应横竖滚动条的尺寸*/

	height: 1px;

}

textarea::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/

	border-radius: 10px;

	-webkit-box-shadow: inset 0 0 5px #c00714;

	background: #535353;

}

textarea::-webkit-scrollbar-track {
	/*滚动条里面轨道*/

	-webkit-box-shadow: inset 0 0 5px #FFFFFF;

	border-radius: 10px;

	background: #EDEDED;

}

.dtktitle {
	position: relative;
}

.dtktitle img {
	display: block;
	width: 100%;
}

.dtktitle div {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	top: 0;
	padding-left: 0.78125rem;
	font-size: 0.833333rem;
	color: #ffffff;
	display: flex;
	align-items: center;
}

.dtkbox {
	padding: 0.78125rem;
	color: #666666;
	font-size: 0.729166rem;
	line-height: 1.197916rem;
}

.titletype {
	height: 1.5625rem;
	border-bottom: 1px solid #ececec;
	color: #666666;
	font-size: 0.729166rem;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding-left: 0.78125rem;
	padding-right: 0.78125rem;
	cursor: pointer;
}

.tmbox {
	border-bottom: 1px solid #ececec;
}

.titletype span {
	width: 0.3125rem;
	height: 0.572916rem;
	background: url(../img/jtyks.png) no-repeat;
}

.titletypeactive {
	background: #f39633 !important;
	color: #FFFFFF !important;
	border: none;
}

.titletypeactive span {
	width: 0.572916rem;
	height: 0.3125rem;
	background: url(../img/jtksx.png) no-repeat;
}

textarea {
	width: 100%;
	height: 6.25rem;
	border-color: #ececec;
	outline-style: none;
	box-sizing: border-box;
	padding: 0.520833rem;
	font-size: 0.833333rem;
	color: #999999;
	resize: none;
}

.tmbox {
	padding: 0.78125rem;
	display: none;
	flex-wrap: wrap;
	justify-content: flex-start;
}

.tmbox a label {
	width: 1.302083rem;
	height: 1.302083rem;
	box-sizing: border-box;
	border: 1px solid #c1c1c1;
	border-radius: 0.260416rem;
	color: #c1c1c1;
	font-size: 0.729166rem;
	cursor: pointer;
	margin: 0.208333rem;
	display: flex;
	justify-content: center;
	align-items: center;
}
.upspan{
	border-color: #c1c1c1;
	background: #ececec;
	color: #c1c1c1;
}

.tmbox a {
	width: calc(100% / 6);
	display: flex;
	justify-content: center;
}

.tmactive label {
	background: #f39633 !important;
	color: #FFFFFF !important;
	border: none !important;
}

.ksbtnbox {
	height: 2.1875rem;
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
	display: flex;
	color: #FFFFFF;
	font-size: 0.833333rem;
	line-height: 2.1875rem;
}

.tjda {
	width: 100%;
	background: #c00714;
	text-align: center;
	cursor: pointer;
}

.tckh {
	width: 8.333333rem;
	background: #56080a;
	text-align: center;
	cursor: pointer;
}

.item {
	display: flex;
	padding: 0.78125rem;
	border-bottom: 1px solid #ececec;
}

.item .itemleft {
	width: 5.208333rem;
	font-size: 0.833333rem;
	color: #333333;
}

.item .itemright {
	width: 100%;
}

.tm {
	font-size: 0.833333rem;
	color: #333333;
	border-bottom: 1px dashed #dedede;
	padding-bottom: 0.520833rem;
}

.da {
	color: #999999;
	font-size: 0.833333rem;
	line-height: 2.083333rem;
	display: flex;
	align-items: center;
}

.dabox {
	padding-top: 0.78125rem;
}

.da span {
	width: 1.041666rem;
	height: 1.041666rem;
	box-sizing: border-box;
	border-radius: 5px;
	border: 1px solid #dedede;
	margin-right: 0.520833rem;
	cursor: pointer;
}

.daactive span {
	border: none !important;
	background: #f39633;
	position: relative;
}

.daactive span::after {
	content: "";
	width: 0.677083rem;
	height: 0.46875rem;
	background: url(../img/check.png) no-repeat;
	position: absolute;
	margin: auto;
	left: 0;
	right: 0;
	bottom: 0;
	top: 0;
}

.xxtx {
	display: flex;
	justify-content: center;
	padding: 1.041666rem 0px;
}

.xxtx div {
	width: 10.416666rem;
	height: 1.666666rem;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #FFFFFF;
	font-size: 0.833333rem;
	background: #f39633;
	border-radius: 0.260416rem;
	cursor: pointer;
}

#mesbox1 {
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	z-index: 99999;
	background: rgba(0, 0, 0, 0.8);
}
#mesbox2{
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	z-index: 99999;
	background: rgba(0, 0, 0, 0.8);
	display: none;
}
.messdiv {
	width: 30rem;
	height: 20rem;
	background: #FFFFFF;
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	margin: auto;
	border-radius: 0.260416rem;
	overflow: hidden;
}

.mestitle {
	display: flex;
	width: 30rem;
	height: 3.220833rem;
	position: relative;
	align-items: center;
	justify-content: center;
}

.mestitle img {
	width: 30rem;
	display: block;
	position: absolute;
	top: 0;
	left: 0;
	right: 0;
	z-index: 1;
}

.mestitle label {
	font-size: 0.9375rem;
	position: absolute;
	z-index: 2;
	font-weight: bold;
	color: #FFFFFF;
}

.messdiv p {
	font-size: 0.9375rem;
	line-height: 1.5625rem;
}

.ppp {
	padding: 0.78125rem;
}

.btniwes {
	display: flex;
	justify-content: center;
	position: absolute;
	bottom: 1.5625rem;
	left: 0;
	right: 0;
}

.btniwes label:first-child {
	background: #c00714;
	text-align: center;
	cursor: pointer;
	font-size: 0.833333rem;
	color: #FFFFFF;
	width: 6.25rem;
	height: 1.822916rem;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-right: 0.78125rem;
}
.btniwes label:last-child {
	background: #c1c1c1;
	text-align: center;
	cursor: pointer;
	font-size: 0.833333rem;
	color: #FFFFFF;
	width: 6.25rem;
	height: 1.822916rem;
	display: flex;
	justify-content: center;
	align-items: center;
	margin-left: 0.78125rem;
}