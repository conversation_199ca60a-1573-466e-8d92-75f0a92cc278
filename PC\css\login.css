* {
	margin: 0;
	padding: 0;
	list-style: 0;
}

html {
	font-size: 1vw;
}

.content {
	position: fixed;
	z-index: 3;
	width: 66.666666rem;
	min-height: 100vh;
	margin: auto;
	left: 0;
	right: 0;
	top: 0;
}

.bagview {
	overflow: hidden;
	width: 100%;
	min-height: 100vh;
	position: fixed;
	z-index: 2;
}

.bag1 {
	position: absolute;
	width: 100%;
	height: auto;
	top: 0;
	left: 0;
	right: 0;
	display: block;
	z-index: 1;
}

.bag2 {
	position: absolute;
	width: 100%;
	height: auto;
	bottom: 0;
	left: 0;
	right: 0;
	display: block;
	z-index: 2;
}

.ctop1 {
	padding-top: 2.604166rem;
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 3.125rem;
	line-height: 3.125rem;
}

.ctop1 .logo {
	display: block;
	width: 9.375rem;
	height: auto;
}

.ctop1 div {
	display: flex;
	align-items: center;
}

.ctop1 .title {
	font-size: 1.666666rem;
	color: #FFFFFF;
	font-weight: bold;
	margin-left: 2.604166rem;
	position: relative;
}

.ctop1 .title::after {
	height: 3.125rem;
	width: 1px;
	content: "";
	position: absolute;
	left: -1.302083rem;
	top: 0;
	bottom: 0;
	background: #dedede;
}

.footer {
	position: fixed;
	bottom: 0;
	left: 0;
	right: 0;
	height: 2.083333rem;
	background: #56080a;
	font-size: 0.9375rem;
	color: #FFFFFF;
	line-height: 2.083333rem;
	text-align: center;
	z-index: 4;
	display: flex;
	align-items: center;
	justify-content: center;
}

.footer img{
	margin-left: 0.260417rem;
	margin-right: 0.260417rem;
}

.boxview {
	margin-top: 7.8125rem;
	position: relative;
}

.loginbox {
	width: 26.041666rem;
	height: auto;
	background: #FFFFFF;
	border-radius: 5px;
	box-shadow: 0px 0px 20px #666666;
	position: relative;
}

.logintitle {
	color: #c00714;
	font-size: 1.666666rem;
	font-weight: bold;
	text-align: center;
	line-height: 5.208333rem;
}
.inputbox {
	width: 20.833333rem;
	height: 2.083333rem;
	border: 1px solid #999999;
	margin: 0px auto;
	margin-bottom: 1.5625rem;
	display: flex;
	align-items: center;
	border-radius: 5px;
	position: relative;
	z-index: 4;
}
.inputbox2{
	width: 20.833333rem;
	margin: 0px auto;
	margin-bottom: 1.5625rem;
	position: relative;
	z-index: 4;
}
.inputbox2 .iiititle{
	font-size: 0.833333rem;
	color: #333333;
	margin-bottom: 0.260416rem;
}
.inputbox2 input{
	border: none;
	outline-style: none;
	line-height: 2.083333rem;
	height: 2.083333rem;
	width: calc(100% - 1.041666rem*2);
	border: 1px solid #999999;
	border-radius: 5px;
	font-size: 0.833333rem;
	padding-left: 1.041666rem;
	padding-right: 1.041666rem;
}
.inputbox2 select{
	border: none;
	outline-style: none;
	line-height: 2.083333rem;
	height: 2.083333rem;
	width: 100%;
	border: 1px solid #999999;
	border-radius: 5px;
	font-size: 0.833333rem;
	padding-left: 1.041666rem;
	padding-right: 1.041666rem;
}
.inputbox2 .input{
	font-size: 0.833333rem;
	color: #999999;
	line-height: 2.083333rem;
	height: 2.083333rem;
}
.inputbox img {
	width: 0.78125rem;
	height: auto;
	display: block;
	padding-left: 0.520833rem;
}

.inputbox input {
	border: none;
	outline-style: none;
	line-height: 2.083333rem;
	padding-left: 1.041666rem;
	height: 2.083333rem;
	font-size: 0.833333rem;
	width: 100%;
	padding-right: 1.041666rem;
}

.yzmbox {
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 2.083333rem;
	line-height: 2.083333rem;
	width: 20.833333rem;
	margin: 0px auto;
	position: relative;
}

.yzmbox input {
	outline-style: none;
	border: 1px solid #999999;
	height: 2.083333rem;
	line-height: 2.083333rem;
	border-radius: 5px;
	width: 100%;
	margin-right: 0.78125rem;
	padding-left: 1.041666rem;
	padding-right: 1.041666rem;
	font-size: 0.833333rem;
}

.yzmbox img {
	display: block;
	width: auto;
	height: 2.083333rem;
	cursor: pointer;
}

.wjpass {
	width: 20.833333rem;
	margin: 0px auto;
	text-align: right;
	font-size: 0.833333rem;
	color: #c00714;
	text-decoration: underline;
	margin-top: 0.78125rem;
}

.wjpass label {
	cursor: pointer;
}

.btnview {
	width: 20.833333rem;
	margin: 0px auto;
	text-align: center;
	margin-top: 2.604166rem;
	padding-bottom: 2.083333rem;
}

.btnview button {
	height: 2.083333rem;
	width: 20.833333rem;
	font-size: 0.833333rem;
	color: #FFFFFF;
	background: #c00714;
	border: none;
	border-radius: 5px;
	cursor: pointer;
}

.loginbag1,
.loginbag2 {
	position: absolute;
	z-index: 2;
}

.loginbag1 {
	width: 14.583333rem;
	height: auto;
	display: block;
	top: -5rem;
	right: -9rem;
}

.loginbag2 {
	width: 12.020833rem;
	height: auto;
	display: block;
	bottom: -4rem;
	left: -5rem;
}

.titlebox {
	width: 20.833333rem;
	height: 2.083333rem;
	margin: 0px auto;
	margin-bottom: 1.5625rem;
	display: flex;
	align-items: center;
	color: #dedede;
	font-size: 0.833333rem;
}
.aaaa{
	font-weight: bold;
	color: #333333;
	position: relative;
}
.aaaa::after{
	content: "";
	height: 4px;
	background: #c00714;
	position: absolute;
	bottom: -0.520833rem;
	width: 1.5625rem;
	border-radius: 100px;
	margin: auto;
	left: 0;
	right: 0;
}
.titlebox img{
	width: 0.78125rem;
	display: block;
	padding-left: 0.260416rem;
	padding-right: 0.260416rem;
}
.btnview2{
	width: 20.833333rem;
	margin: 0px auto;
	margin-top: 2.604166rem;
	padding-bottom: 2.083333rem;
	display: flex;
	justify-content: space-between;
}
.btnview2 button{
	border: none;
	outline-style: none;
	font-size: 0.833333rem;
	cursor: pointer;
	border-radius: 5px;
}
.xyb{
	width: 12.5rem;
	height: 2.083333rem;
	color: #FFFFFF;
	background: #c00714;
}
.backlogin{
	width: 6.25rem;
	height: 2.083333rem;
	background: #cecece;
	color: #FFFFFF;
}
#loadimg,#loadimg2,#loadimg3{
	position: absolute;
	margin: auto;
	top: 0;
	bottom: 0;
	right: -1.25rem;
	width: 0.9375rem;
	height: auto;
	display: none;
	animation:xz 1s linear infinite;
	-webkit-animation: xz 1s linear infinite;
}
@keyframes xz
{
    from {
			transform: rotateZ(0deg);
		}
    to {
			transform: rotateZ(360deg);
		}
}
 
@-webkit-keyframes xz /* Safari 与 Chrome */
{
    from {
    	transform: rotateZ(0deg);
    }
    to {
    	transform: rotateZ(360deg);
    }
}
.title2{
	font-size: 0.729166rem;
	text-align: center;
	color: #c79697;
	margin-top: -1.302083rem;
	padding-bottom: 1.302083rem;
}
.tiaoguo{
	text-align: center;
	font-size: 0.833333rem;
	color: #666666;
	padding-bottom: 0.78125rem;
	margin-top: 0.520833rem;
	text-decoration: underline;
}
.tiaoguo label{
	cursor: pointer;
}
#box1,#box2,#box3,#box4,#box5{
	position: absolute;
	margin: auto;
	left: 0;
	right: 0;
	transform: translateY(800px);
	-webkit-transform: translateY(800px);
	-ms-transform: translateY(800px);
	-moz-transform: translateY(800px);
	-o-transform: translateY(800px);
	transition: all .4s;
	-webkit-transition: all .4s;
	-moz-transition: all .4s;
	-o-transition: all .4s;
	-ms-transition: all .4s;
}
#box1 div,#box2 div,#box3 div,#box4 div,#box5 div{
	position: relative;
	z-index: 3;
}