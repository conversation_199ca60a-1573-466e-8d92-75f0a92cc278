body {
	background: #fbfbfb !important;
}

.box {
	width: 66.666666rem;
	margin: 0px auto;
}

.contentview {
	display: flex;
	margin-top: 3.125rem;
	margin-bottom: 3.125rem;
	justify-content: space-between;
	min-height: 28rem;
}

.boxleft {
	width: 11.458333rem;
}

.boxright {
	width: calc(100% - 12.239583rem);
	background: #FFFFFF;
}

.lefttopview {
	width: 100%;
	height: 3.125rem;
	background: url(../img/lefttopviewbag.png) no-repeat;
	background-size: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 1.145833rem;
	color: #FFFFFF;
	font-weight: bold;
}

.leftitembox {
	background: #a00611;
}

.leftitem,
.leftitem a {
	height: 5.989583rem;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 0.9375rem;
	color: #c79697;
	border-bottom: 0.052083rem dashed #b04047;
	cursor: pointer;
}

.activeleftitem,
.activeleftitem a {
	font-weight: bold;
	color: #FFFFFF !important;
}

.righttopview {
	width: 100%;
	height: 3.125rem;
	border-bottom: 0.052083rem solid #999999;
	box-sizing: border-box;
}

.righttopview label {
	height: 3.125rem;
	display: inline-block;
	line-height: 3.125rem;
	font-size: 0.9375rem;
	color: #c00714;
	font-weight: bold;
	position: relative;
	padding: 0px 0.78125rem;
}

.righttopview label::after {
	content: "";
	height: 0.104166rem;
	background: #c00714;
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
}

.box1 {
	padding: 1.5625rem 0.78125rem;
	position: relative;
	border-bottom: 0.052083rem solid #999999;
}

.box1:last-child {
	border: none;
}

#yz {
	position: absolute;
	top: 0.520833rem;
	right: 0.520833rem;
	width: 8.802083rem;
	display: block;
}

.box1_toptitle {
	font-size: 0.9375rem;
	color: #333333;
	font-weight: bold;
}

.xx {
	font-size: 0.9375rem;
	color: #666666;
	padding-left: 1.822916rem;
	display: flex;
	align-items: center;
	margin: 1.302083rem 0px;
}

.xx img {
	width: 1.041666rem;
	display: block;
	padding-left: 0.78125rem;
}

.xx1 {
	width: 65%;
	display: flex;
}

.xx2 {
	width: 20%;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.bdbtn {
	width: 5rem;
	height: 1.770833rem;
	display: block;
	background: #c00714;
	color: #fefefe;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 0.260416rem;
	font-size: 0.9375rem;
	cursor: pointer;
}

.wbd {
	color: #cecece;
	padding: 0px 1.041666rem;
}

.xxnr {
	padding-left: 1.822916rem;
}

.xxleft {
	width: 4.6875rem;
}

.borderbag {
	background: linear-gradient(to right, #c00714, #c00714, #ffc156);
	height: 2px;
}

.righttop {
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px dashed #dedede;
}

.righttop2 {
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px solid #dedede;
	height: 2.864583rem;
}

.righttop2 div {
	width: 50%;
	font-size: 0.9375rem;
	color: #333333;
	display: flex;
	align-items: center;
	justify-content: center;
	height: 2.864583rem;
	cursor: pointer;
}

.righttop2 .activetop2 {
	color: #c00714 !important;
	font-weight: bold !important;
	position: relative;
}

.righttop2 .activetop2::after {
	content: '';
	height: 0.104166rem;
	background: #c00714;
	position: absolute;
	left: 0;
	right: 0;
	bottom: -0.052083rem;
}

.righttop2 .activetop2::before {
	content: '';
	width: 0.572916rem;
	height: 0.3125rem;
	background: url(../img/sj.png) no-repeat;
	position: absolute;
	left: 0;
	right: 0;
	bottom: -0.052083rem;
	margin: auto;
}

.righttop div {
	height: 2.864583rem;
	display: flex;
	align-items: center;
}

.righttop div label {
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 0.729166rem;
	color: #e1e1e1;
	padding: 0px 0.78125rem;
}

.righttop div label img {
	width: 0.885416rem;
	display: block;
	padding-right: 0.260416rem;
}

.nrnr {
	padding: 2.28125rem;
}

.titlenr {
	font-size: 1.25rem;
	color: #333333;
	font-weight: bold;
	text-align: center;
	padding-bottom: 1.5625rem;
}

.nr p {
	font-size: 0.9375rem;
	color: #666666;
	text-indent: 2em;
	line-height: 1.5625rem;
	padding-top: 0.260416rem;
	padding-bottom: 0.260416rem;
}

.nrnr2 {
	padding: 2.083333rem;
	display: flex;
	flex-wrap: wrap;
}

.nr2left {
	width: 17.1875rem;
}

.nr2left img {
	display: block;
}

.nr2right {
	width: calc(100% - 17.1875rem);
	box-sizing: border-box;
	padding-left: 0.78125rem;
}

.item3 {
	border-bottom: 0.052083rem dashed #dedede;
	display: flex;
	padding-bottom: 0.260416rem;
	padding-top: 0.260416rem;
	justify-content: space-between;
	cursor: pointer;
}

.item3:last-child {
	border: none;
}
.item3l{
	width: calc(100% - 6rem);
}
.item3ltitle {
	font-size: 0.9375rem;
	color: #333333;
	text-overflow: -o-ellipsis-lastline;
	overflow: hidden;
	text-overflow: ellipsis;
	display: -webkit-box;
	-webkit-line-clamp: 2;
	line-clamp: 2;
	-webkit-box-orient: vertical;
}

.item3btnview {
	display: flex;
	justify-content: space-between;
	align-items: center;
	padding-top: 0.78125rem;
	padding-bottom: 0.78125rem;
}

.item3btnview div {
	display: flex;
}

.item3btnview img {
	width: 0.9375rem;
	display: block;
	padding-right: 0.260416rem;
}

.item3btnview label {
	display: flex;
	align-items: center;
	font-size: 0.729166rem;
	color: #cecece;
}

.item3btnview div label {
	padding-right: 0.78125rem;
}

.item3r img {
	display: block;
	width: 5.208333rem;
	padding-left: 0.520833rem;
}

.item4list {
	width: 100%;
	padding-top: 1.302083rem;
}

.item4 {
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 0.052083rem dashed #dedede;
	height: 3.385416rem;
	cursor: pointer;
}

.item4:last-child {
	border: none;
}

.item4 div {
	overflow: hidden;

	text-overflow: ellipsis;

	white-space: nowrap;
	font-size: 0.9375rem;
	color: #333333;
}

.item4 label {
	font-size: 0.729166rem;
	color: #cecece;
	display: flex;
	align-items: center;
	width: 11.458333rem;
	justify-content: flex-end;
}

.item4 label img {
	display: block;
	width: 0.9375rem;
}

.fybox {
	width: 100%;
}

.lefttopview img {
	width: 1.25rem;
	display: block;
	padding-right: 0.260416rem;
}

.lefttopview label {
	display: flex;
	align-items: center;
}

#swiper {
	width: 100%;
	overflow-x: hidden;
	position: relative;
	cursor: pointer;
}
#swiper .swiper-slide{
	min-height: 2.864583rem;
}
.imgiii {
	position: relative;
}

.imgiii img {
	display: block;
	width: 100%;
}

.imgiii .swpierbtn {
	display: flex;
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
	height: 2.864583rem;
	background: rgba(111, 7, 10, 0.9);
	z-index: 99;
	align-items: center;
	justify-content: space-between;
	padding-left: 0.9375rem;
	padding-right: 0.9375rem;
}

.imgiii .swpierbtn div {
	width: 8.854166rem;
	color: #ffffff;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	font-size: 0.9375rem;
}

.imgiii .swpierbtn label {
	display: flex;
	align-items: center;
	justify-content: center;
	color: #c79697;
	font-size: 0.729166rem;
}
.imgiii .swpierbtn label img{
	padding-right: 0.520833rem;
	width: 1.09375rem;
}
#fyq2{
	bottom: 3.020833rem;
}
.swiper-pagination-bullet-active{
	background: #FFFFFF;
	opacity: 1 !important;
}
.swiper-pagination-bullet{
	background: #FFFFFF;
	opacity: 0.3;
}