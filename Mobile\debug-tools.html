<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>开发调试工具 - 思政一体化平台</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            margin: 0;
            padding: 20px;
            line-height: 1.6;
        }
        
        .container {
            max-width: 600px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            overflow: hidden;
        }
        
        .header {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            padding: 30px 20px;
            text-align: center;
        }
        
        .header h1 {
            margin: 0;
            font-size: 24px;
            font-weight: 600;
        }
        
        .header p {
            margin: 10px 0 0;
            opacity: 0.9;
        }
        
        .content {
            padding: 30px 20px;
        }
        
        .section {
            margin-bottom: 30px;
        }
        
        .section h2 {
            color: #c00714;
            font-size: 18px;
            margin-bottom: 15px;
            border-bottom: 2px solid #f0f0f0;
            padding-bottom: 8px;
        }
        
        .current-user {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 20px;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .user-avatar {
            width: 60px;
            height: 60px;
            background: #c00714;
            border-radius: 50%;
            margin: 0 auto 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 24px;
            font-weight: bold;
        }
        
        .user-name {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }
        
        .user-role {
            color: #666;
            font-size: 14px;
        }
        
        .btn-group {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }
        
        .btn {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 15px 20px;
            background: #c00714;
            color: white;
            text-decoration: none;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 500;
            text-align: center;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            min-height: 50px;
        }
        
        .btn:hover {
            background: #a00610;
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(192, 7, 20, 0.3);
        }
        
        .btn.secondary {
            background: #6c757d;
        }
        
        .btn.secondary:hover {
            background: #5a6268;
            box-shadow: 0 4px 12px rgba(108, 117, 125, 0.3);
        }
        
        .btn.success {
            background: #28a745;
        }
        
        .btn.success:hover {
            background: #218838;
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }
        
        .btn.warning {
            background: #ffc107;
            color: #212529;
        }
        
        .btn.warning:hover {
            background: #e0a800;
            box-shadow: 0 4px 12px rgba(255, 193, 7, 0.3);
        }
        
        .btn-icon {
            margin-right: 8px;
            font-size: 18px;
        }
        
        .info-box {
            background: #e7f3ff;
            border: 1px solid #b3d9ff;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #0066cc;
        }
        
        .warning-box {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 6px;
            padding: 15px;
            margin-bottom: 20px;
            font-size: 14px;
            color: #856404;
        }
        
        .code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 4px;
            padding: 8px 12px;
            font-family: monospace;
            font-size: 12px;
            color: #495057;
            word-break: break-all;
            margin: 10px 0;
        }
        
        .status {
            display: inline-block;
            padding: 4px 8px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }
        
        .status.online {
            background: #d4edda;
            color: #155724;
        }
        
        .status.offline {
            background: #f8d7da;
            color: #721c24;
        }
        
        @media (max-width: 480px) {
            .btn-group {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🔧 开发调试工具</h1>
            <p>模拟登录和系统调试</p>
        </div>
        
        <div class="content">
            <!-- 当前用户状态 -->
            <div class="section">
                <h2>👤 当前用户状态</h2>
                <div class="current-user" id="currentUserInfo">
                    <div class="user-avatar" id="userAvatar">?</div>
                    <div class="user-name" id="userName">未登录</div>
                    <div class="user-role" id="userRole">无角色</div>
                    <div style="margin-top: 10px;">
                        <span class="status offline" id="loginStatus">离线</span>
                    </div>
                </div>
            </div>
            
            <!-- 模拟登录 -->
            <div class="section">
                <h2>🎭 模拟登录</h2>
                <div class="info-box">
                    <strong>说明：</strong>使用与PC端相同的模拟登录机制，无需真实的CAS认证，直接在本地存储中设置用户信息和token。
                </div>
                
                <div class="btn-group">
                    <button class="btn" onclick="switchToTeacher()">
                        <span class="btn-icon">👨‍🏫</span>
                        切换到教师账号
                    </button>
                    <button class="btn secondary" onclick="switchToStudent()">
                        <span class="btn-icon">👨‍🎓</span>
                        切换到学生账号
                    </button>
                </div>
            </div>
            
            <!-- 系统操作 -->
            <div class="section">
                <h2>⚙️ 系统操作</h2>
                <div class="btn-group">
                    <button class="btn warning" onclick="clearLoginData()">
                        <span class="btn-icon">🗑️</span>
                        清除登录数据
                    </button>
                    <button class="btn success" onclick="refreshPage()">
                        <span class="btn-icon">🔄</span>
                        刷新页面
                    </button>
                </div>
            </div>
            
            <!-- 页面导航 -->
            <div class="section">
                <h2>🧭 页面导航</h2>
                <div class="btn-group">
                    <a href="index.html" class="btn secondary">
                        <span class="btn-icon">🏠</span>
                        返回首页
                    </a>
                    <a href="login-test.html" class="btn secondary">
                        <span class="btn-icon">🧪</span>
                        登录测试
                    </a>
                    <a href="login-guide.html" class="btn secondary">
                        <span class="btn-icon">📖</span>
                        登录指南
                    </a>
                </div>
            </div>
            
            <!-- 技术信息 -->
            <div class="section">
                <h2>📊 技术信息</h2>
                <div class="warning-box">
                    <strong>注意：</strong>这是开发调试工具，仅在开发环境中使用。生产环境请使用真实的统一认证登录。
                </div>
                
                <div>
                    <strong>当前环境：</strong>
                    <div class="code" id="currentEnv">检测中...</div>
                </div>
                
                <div>
                    <strong>存储信息：</strong>
                    <div class="code" id="storageInfo">检测中...</div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 模拟登录函数（与PC端和移动端保持一致）
        function mockLogin(userType) {
            const userTypes = {
                teacher: {
                    token: 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiIyMTIxMDE4IiwiY3JlYXRlZCI6MTc0NDc2NzkyMzEwNiwiZXhwIjoxNzYwMzE5OTIzfQ.hJd3I-_acZAiSjcHLP7jbRlcDIZYHraB37bGVsFrWW72zvzkJ1fHwqoOG7hidvKs9w6qiFwoc_BjkE1tNy9Yvg',
                    userInfo: {
                        "id": "1299387870829744128",
                        "name": "吴永刚",
                        "roleName": "老师",
                        "phone": "18993689001",
                        "researchSection": "马克思主义学院",
                        "myCollegeName": "马克思主义学院"
                    }
                },
                student: {
                    token: 'Bearer eyJhbGciOiJIUzUxMiJ9.eyJzdWIiOiI1MjIwMTA4MDE1MzIiLCJjcmVhdGVkIjoxNzQ2NTEzMDkwMDkwLCJleHAiOjE3NjIwNjUwOTB9.KKBVPXxZFbR5JlUaoqiG_zHQ5Cya89BT9rpQRKj3OxJydEhOJIlHDUpaDsuxz0bT_uGYIWoQai8pu9grduTE7w',
                    userInfo: {
                        "id": "1388727398726543212",
                        "name": "测试学生",
                        "roleName": "学生",
                        "phone": "15991234567",
                        "collegeName": "中医学院",
                        "majorName": "中医学",
                        "className2": "中医2022-1班"
                    }
                }
            };
            
            const selectedUser = userTypes[userType] || userTypes.teacher;
            sessionStorage.setItem('header', selectedUser.token);
            sessionStorage.setItem('userinfo', JSON.stringify(selectedUser.userInfo));
            
            console.log(`模拟登录成功！用户：${selectedUser.userInfo.name}（${selectedUser.userInfo.roleName}）`);
            return selectedUser.userInfo;
        }

        // 切换到教师账号
        function switchToTeacher() {
            const userInfo = mockLogin('teacher');
            showToast(`已切换到教师账号：${userInfo.name}`, 'success');
            updateUserDisplay();
        }

        // 切换到学生账号
        function switchToStudent() {
            const userInfo = mockLogin('student');
            showToast(`已切换到学生账号：${userInfo.name}`, 'success');
            updateUserDisplay();
        }

        // 清除登录数据
        function clearLoginData() {
            sessionStorage.removeItem('userinfo');
            sessionStorage.removeItem('header');
            showToast('已清除所有登录数据', 'warning');
            updateUserDisplay();
        }

        // 刷新页面
        function refreshPage() {
            window.location.reload();
        }

        // 显示Toast提示
        function showToast(message, type = 'info') {
            const existingToast = document.getElementById('toast');
            if (existingToast) {
                existingToast.remove();
            }
            
            const toast = document.createElement('div');
            toast.id = 'toast';
            toast.style.cssText = `
                position: fixed;
                top: 20px;
                left: 50%;
                transform: translateX(-50%);
                background: ${type === 'success' ? '#28a745' : type === 'warning' ? '#ffc107' : '#007bff'};
                color: ${type === 'warning' ? '#212529' : 'white'};
                padding: 12px 20px;
                border-radius: 6px;
                font-size: 14px;
                z-index: 10000;
                box-shadow: 0 4px 12px rgba(0,0,0,0.3);
                max-width: 90%;
                text-align: center;
            `;
            
            toast.textContent = message;
            document.body.appendChild(toast);
            
            setTimeout(() => {
                if (toast.parentNode) {
                    toast.remove();
                }
            }, 3000);
        }

        // 更新用户显示
        function updateUserDisplay() {
            const userInfo = sessionStorage.getItem('userinfo');
            const header = sessionStorage.getItem('header');
            
            const userAvatar = document.getElementById('userAvatar');
            const userName = document.getElementById('userName');
            const userRole = document.getElementById('userRole');
            const loginStatus = document.getElementById('loginStatus');
            
            if (userInfo && header) {
                try {
                    const user = JSON.parse(userInfo);
                    userAvatar.textContent = user.name ? user.name.charAt(0) : '?';
                    userName.textContent = user.name || '未知用户';
                    userRole.textContent = user.roleName || '无角色';
                    loginStatus.textContent = '在线';
                    loginStatus.className = 'status online';
                } catch (e) {
                    userName.textContent = '数据错误';
                    userRole.textContent = '解析失败';
                    loginStatus.textContent = '错误';
                    loginStatus.className = 'status offline';
                }
            } else {
                userAvatar.textContent = '?';
                userName.textContent = '未登录';
                userRole.textContent = '无角色';
                loginStatus.textContent = '离线';
                loginStatus.className = 'status offline';
            }
            
            updateTechnicalInfo();
        }

        // 更新技术信息
        function updateTechnicalInfo() {
            const currentEnv = document.getElementById('currentEnv');
            const storageInfo = document.getElementById('storageInfo');
            
            // 环境信息
            currentEnv.textContent = `
                域名: ${window.location.hostname}
                端口: ${window.location.port || '默认'}
                协议: ${window.location.protocol}
                路径: ${window.location.pathname}
            `;
            
            // 存储信息
            const userInfo = sessionStorage.getItem('userinfo');
            const header = sessionStorage.getItem('header');
            
            storageInfo.textContent = `
                用户信息: ${userInfo ? '已存储' : '未存储'}
                认证Token: ${header ? '已存储' : '未存储'}
                Token长度: ${header ? header.length + '字符' : '0字符'}
            `;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            updateUserDisplay();
        });

        // 监听存储变化
        window.addEventListener('storage', function(e) {
            if (e.key === 'userinfo' || e.key === 'header') {
                updateUserDisplay();
            }
        });
    </script>
</body>
</html>
