<!DOCTYPE html>
<html>
	<head>
	<!--  -->
		<meta charset="utf-8" />
		<title>思政一体化平台-习主席足迹</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<meta name="viewport" content="width=device-width, initial-scale=1">
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/footprint.css" />
		<link rel="stylesheet" type="text/css" href="css/core.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jsMap-3.1.0.min.js" type="text/javascript" charset="utf-8"></script>
		<script>
			window.addEventListener('load', function() {
				if(window.jsMap) {
					// console.log('jsMap version:', jsMap.version);
					/* 将单行注释改为多行注释块
					console.log('jsMap methods:', {
						config: jsMap.config,
						refresh: jsMap.refresh,
						remove: jsMap.remove,
						multipleValue: jsMap.multipleValue
					});
					*/
				} else {
					console.error('jsMap not found');
				}
			});
		</script>
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
	</head>
	<body class="index">
		<!-- 添加样式覆盖移动端样式 -->
		<style>
		/* 覆盖外部CSS中的移动端样式，让页面使用全局字体缩放系统 */
		
		/* 确保导航栏正确固定在顶部，字体不受全局缩放影响 */
		.topview {
			position: fixed !important;
			top: 0 !important;
			left: 0 !important;
			z-index: 1000 !important;
			width: 100% !important;
			height: 6rem !important;
			margin-bottom: -1rem !important;
			display: flex !important;
			flex-wrap: wrap !important;
			justify-content: center !important;
			background: url(../img/indextopbag.png) !important;
			background-size: cover !important;
			transition: all 0.2s ease-in-out !important;
		}
		
		/* 导航栏内所有元素使用固定字体大小，不受全局缩放影响 */
		.topview,
		.topview *,
		.topview_1,
		.topview_1 *,
		.topview_2,
		.topview_2 *,
		.menuaaaa,
		.loginview,
		.loginview * {
			font-family: 'Source Han Sans CN', -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif !important;
			font-feature-settings: 'liga' 1, 'kern' 1 !important;
			text-rendering: optimizelegibility !important;
		}
		
		/* 导航栏第一行样式 */
		.topview_1 {
			width: 66.666666rem !important;
			height: 3.333333rem !important;
			display: flex !important;
			flex-wrap: nowrap !important;
			justify-content: space-between !important;
			align-items: center !important;
		}
		
		/* 导航栏第二行菜单样式 */
		.topview_2 {
			width: 66.666666rem !important;
			height: 2.75rem !important;
		}
		
		.topview_2 .itembox {
			display: flex !important;
			height: 100% !important;
		}
		
		/* 菜单项固定字体大小 */
		.menuaaaa {
			font-size: 0.833333rem !important;
			color: #FFFFFF !important;
			display: flex !important;
			height: 100% !important;
			width: 100% !important;
			align-items: center !important;
			justify-content: center !important;
			font-weight: 400 !important;
			transition: color 0.2s ease, font-weight 0.2s ease !important;
		}
		
		.menuaaaa:hover {
			color: #ffd05f !important;
			font-weight: bold !important;
		}
		
		.active {
			color: #ffd05f !important;
			font-weight: bold !important;
			position: relative !important;
		}
		
		.active::after {
			position: absolute !important;
			content: "" !important;
			height: 2px !important;
			background: #ffd05f !important;
			width: 100% !important;
			bottom: 0 !important;
		}
		
		/* 登录区域样式 */
		.topview_1 .loginview {
			display: flex !important;
			align-items: center !important;
			cursor: pointer !important;
		}
		
		.topview_1 .loginview img {
			padding-right: 0.260416rem !important;
			width: 0.78125rem !important;
			height: auto !important;
			display: block !important;
		}
		
		.topview_1 .loginview label,
		.topview_1 .loginview a {
			font-size: 0.729166rem !important;
			color: #FFFFFF !important;
			cursor: pointer !important;
		}
		
		/* 搜索框样式 */
		.topview_1 .ssview {
			width: 12.5rem !important;
			height: 1.666666rem !important;
			background: #FFFFFF !important;
			border-radius: 0.833333rem !important;
			display: flex !important;
			align-items: center !important;
			cursor: pointer !important;
		}
		
		.topview_1 input {
			width: calc(100% - 2rem) !important;
			height: 100% !important;
			border: none !important;
			outline: none !important;
			background: transparent !important;
			font-size: 0.729166rem !important;
		}
		
		/* 确保内容区域不被导航栏遮挡 */
		.content {
			padding-top: 6rem !important;
		}
		
		/* 下拉菜单样式 */
		.topview_2 .menuitemaaa {
			height: 100% !important;
			display: flex !important;
			align-items: center !important;
			justify-content: center !important;
			cursor: pointer !important;
			flex: 1 !important;
			position: relative !important;
			width: 6.666666666666rem !important;
		}
		
		.menuitemaaa:hover .itemcboxaaa {
			display: block !important;
		}
		
		.itemcboxaaa {
			position: absolute !important;
			top: 100% !important;
			background: rgb(126,8,11) !important;
			padding: 0.625rem !important;
			left: 0 !important;
			right: 0 !important;
			display: none !important;
			z-index: 99 !important;
		}
		
		.itemcboxaaa a {
			display: block !important;
			width: 100% !important;
			text-align: center !important;
			font-size: 0.833333rem !important;
			color: #FFFFFF !important;
		}
		
		.itemcboxaaa a:hover {
			color: #ffd05f !important;
			font-weight: bold !important;
		}
		
		.itema2aaa {
			line-height: 1.8rem !important;
		}
		
		/* 全面覆盖footprint.css中的固定字体大小，使用em单位继承全局字体缩放 */
		/* body * { */
			/* font-size: inherit !important; */
		/* } */
		
		/* 地图文字适配全局字体缩放 */
		#map svg text,
		.jsmap-svg text {
			font-size: 0.75rem; /* 相对于根字体大小 */
		}
		
		/* 主要内容区域字体适配 */
		.maptitlebox div {
			font-size: 0.85rem !important;
		}
		
		.mapbtntitle {
			font-size: 1.2rem !important;
		}
		
		.zjnr {
			font-size: 1rem !important;
		}
		
		.zjtime {
			font-size: 0.8rem !important;
		}
		
		.fybox span,
		.fybox label {
			font-size: 0.9rem !important;
		}
		
		#nodate {
			font-size: 1rem !important;
		}
		
		.resetBtn {
			font-size: 0.85rem !important;
		}
		
		.reset-icon {
			font-size: 1.1rem !important;
		}
		
		/* 细节文字元素 */
		.maptitle span,
		.maptitle label {
			font-size: 0.6rem !important;
		}
		
		.contenttitle {
			font-size: 1.3rem !important;
		}
		
		.contenttext {
			font-size: 1rem !important;
		}

		</style>
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="content">
			<div class="maptitle">
				<img src="img/xzxzj.png" />
			</div>
			<div id="map"></div>
			<div class="mapbtntitle">
				<img src="img/address.png" />
				<label>习近平总书记的足迹-<span id="cityname">全部</span></label>
				<button class="resetBtn" id="resetFilter" onclick="resetAllFilter()">
					<i class="reset-icon">↺</i>重置筛选
				</button>
			</div>
			<div class="maptitlebox" id="citylist">

			</div>

			<div class="borderdiv"></div>
			<div class="zjbox">
				<div class="zjboxmain" id="alist">


				</div>
				
			</div>
			<div id="nodate">没有更多数据</div>
			<div class="fybox" id="fyq">
				<span id="sy">首页</span>
				<span id="syy">上一页</span>
				<div class="num" id="num">
				</div>
				<span id="xyy">下一页</span>
				<span id="wy">尾页</span>
			</div>
			<div class="zwf"></div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/core.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
			let pageindex = 1
			let pagesize = 10
			let pages = 0
			let province = null
			let city = null
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				let userinfo = sessionStorage.getItem("userinfo")
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
					
					// 备份会话信息到localStorage，以便在页面跳转时恢复
					localStorage.setItem("userinfo_backup", userinfo);
					const header = sessionStorage.getItem("header");
					if (header) {
						localStorage.setItem("header_backup", header);
					}
					
					// 检查并提交学习记录
					if(window.localStorage.getItem("jilu")){
						$.ajax({
							url: baseurl + "/study/record/add",
							type: 'post',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: window.localStorage.getItem("jilu"),
							dataType: 'json',
							success: (res) => {
								console.log("学习记录提交成功");
								window.localStorage.removeItem("jilu"); // 只移除jilu，保留其他备份数据
							},
							error: (err) => {
								console.error("学习记录提交失败", err);
							}
						})
					}
					
					// 检查是否有临时保存的学习记录
					if(window.localStorage.getItem("temp_jilu")){
						$.ajax({
							url: baseurl + "/study/record/add",
							type: 'post',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: window.localStorage.getItem("temp_jilu"),
							dataType: 'json',
							success: (res) => {
								console.log("临时学习记录提交成功");
								window.localStorage.removeItem("temp_jilu"); // 移除临时记录
							},
							error: (err) => {
								console.error("临时学习记录提交失败", err);
							}
						})
					}
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				getclass()
				getcontent()
				getcitylist('')
				getfooterlink()
				
				// 初始化地图
				try {
					jsMap.config({
						el: 'map',
						style: {
							fill: '#DE2910',
							stroke: '#FFFFFF',
							'stroke-width': 1,
							label: {
								color: '#FFD700',
								size: 10
							}
						},
						click: function(data) {
							if(data && data.name) {
								getnewss(data.name);
							}
						}
					});
					
					// 添加自定义样式
					var style = document.createElement('style');
					style.textContent = `
						.jsmap-svg path {
							fill: #DE2910 !important;
							stroke: #FFFFFF !important;
							stroke-width: 1.5px !important;
							filter: drop-shadow(2px 4px 3px rgba(0, 0, 0, 0.3));
						}
						.jsmap-svg text {
							fill: #FFD700 !important;
							font-size: 10px !important;
							text-shadow: 1px 1px 2px rgba(0, 0, 0, 0.3);
						}
					`;
					document.head.appendChild(style);
					
					jsMap.refresh();
					
				} catch(e) {
					console.error('地图初始化失败:', e);
				}
			})
			function getfooterlink(){
				// 构建请求头，如果用户已登录则添加 Authorization 头
				const headers = sessionStorage.getItem("header") 
					? {"Authorization": sessionStorage.getItem("header")} 
					: {};
					
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: headers,
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item)=>{
								html+='<a href="'+item.url+'" target="_blank">'+item.name+'</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					if (res.data[i].id == getUrlParam('id') || res.data[i].redirectUrl ==
			// 						'footprint.html') {
			// 						classdate = res.data[i]
			// 						html += '<a class="menuitem active" href="' + res.data[i].redirectUrl + '?id=' +
			// 							res
			// 							.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					} else {
			// 						html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 							.data[
			// 								i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					}
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getnewss(name) {
				province = name
				city = null
				$("#cityname").html(name)
				//获取省下面全部市
				getcitylist(name)
				//下方列表
				pageindex = 1
				getcontent(province, city)
			}

			function selectcity(e) {
				// console.log($(e).attr("data-name"))
				let allcity = $("#citylist div")
				for (let i = 0; i < allcity.length; i++) {
					$(allcity[i]).attr('class', "")
				}
				
				// 添加选择动画效果
				$(e).addClass("selecting");
				setTimeout(function() {
					$(e).removeClass("selecting").attr("class", "actived");
				}, 300);
				
				city = $(e).attr("data-name")
				
				// 添加列表更新动画
				$("#alist").css({
					"opacity": "0.5",
					"transform": "translateY(10px)",
					"transition": "all 0.3s ease"
				});
				
				pageindex = 1
				getcontent(province, city)
				
				// 恢复列表显示
				setTimeout(function() {
					$("#alist").css({
						"opacity": "1",
						"transform": "translateY(0)"
					});
				}, 500);
			}

			function getcitylist(name) {
				if(name){
					$("#citylist").attr("style","display:flex")
					let json = {
						province: name
					}
					
					// 构建请求头，如果用户已登录则添加 Authorization 头
					const headers = sessionStorage.getItem("header") 
						? {"Authorization": sessionStorage.getItem("header")} 
						: {};
						
					$.ajax({
						url: baseurl + "/footprint/citys",
						type: 'POST',
						contentType: "application/json",
						headers: headers,
						data: JSON.stringify(json),
						dataType: 'json',
						success: (res) => {
							if (res.code == '200') {
								// console.log(res.data)
								let html = ""
								if(res.data.length>0){
									res.data.map((item, index) => {
										if ((index + 1) === res.data.length) {
											html += '<div onclick="selectcity(this)" data-name="' + item + '">' +
												item + '</div>'
										} else {
											html += '<div onclick="selectcity(this)" data-name="' + item + '">' +
												item + '</div>'
										}
									})
								}else{
									html+="<label>没有更多数据</label>"
								}
								
								$("#citylist").html(html)
							} else if (res.code == '401') {
								// 如果返回401未授权，显示友好提示
								$("#citylist").html('<div style="text-align:center;width:100%;"><p style="font-size:14px;color:#666;">登录后可查看详细城市</p></div>');
							}
						},
						error: function() {
							// 处理请求错误
							$("#citylist").html('<div style="text-align:center;width:100%;"><p style="font-size:14px;color:#666;">获取城市数据失败</p></div>');
						}
					})
				}
				
			}

			function getcontent(sname, cityname) { //获取主席足迹列表
				// 构建请求头，如果用户已登录则添加 Authorization 头
				const headers = sessionStorage.getItem("header") 
					? {"Authorization": sessionStorage.getItem("header")} 
					: {};
					
				$.ajax({
					url: baseurl + "/web/footprint",
					type: 'GET',
					contentType: "application/json",
					headers: headers,
					data: {
						pageNum: pageindex,
						pageSize: pagesize,
						province: sname,
						city: cityname
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data.list)
							let html = ""
							if(res.data.list.length>0){
								$("#nodate").hide()
								$("#alist").show()
								res.data.list.map((item) => {
									html += '<a href="footprintcontent.html?id=' + item.id +
										'" class="zjitem" target="_blank" onclick="backupSessionBeforeNavigation(event)">' +
										'<span class="zjyuan"></span>' +
										'<div class="zjnr">' + item.title + '</div>' +
										'<div class="zjtime"><img src="img/timeico.png" />' +
										'<span>' + setDate(item.dateTime) + '</span></div></a>'
								})
							}else{
								//显示没有更多数据
								$("#nodate").show()
								$("#alist").hide()
							}
							$("#alist").html(html)
							pages = res.data.pages
							if (pages > 1) {
								let numhtml = ""
								for (let a = 1; a <= pages; a++) {
									if (pageindex == a) {
										numhtml += '<label class="actinum" onclick="getnewlist(' + a + ')">' + a +
											'</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
						} else if (res.code == '401') {
							// 如果返回401未授权，显示友好提示而不是强制跳转
							$("#alist").html('<div style="text-align:center;padding:20px;"><p style="font-size:16px;color:#666;">登录后可查看更多内容</p></div>');
							$("#nodate").hide();
							$("#fyq").hide();
						}
					},
					error: function() {
						// 处理请求错误
						$("#alist").html('<div style="text-align:center;padding:20px;"><p style="font-size:16px;color:#666;">获取数据失败，请稍后再试</p></div>');
						$("#nodate").hide();
						$("#fyq").hide();
					}
				})
			}

			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是第最后一页了！")
					} else {
						cocoMessage.warning(1000, "经是第" + index + "页了！")
					}
				} else {
					pageindex = index
					getcontent(province, city)
				}
			}

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}
			function setDate(value){
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '年' + MM + '月' + d + '日';
			}
			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "退出登录！")
			}
			
			// 在导航到足迹详情页之前备份会话信息
			function backupSessionBeforeNavigation(event) {
				try {
					// 获取当前的会话数据
					const userInfo = sessionStorage.getItem("userinfo");
					const authHeader = sessionStorage.getItem("header");
					
					// 如果存在，则备份到localStorage
					if (userInfo) {
						localStorage.setItem("userinfo_backup", userInfo);
						
						// 增加会话备份时间戳，用于验证备份的有效期
						localStorage.setItem("userinfo_backup_timestamp", Date.now().toString());
						console.log("用户信息已备份到localStorage");
					}
					
					if (authHeader) {
						localStorage.setItem("header_backup", authHeader);
						console.log("授权头已备份到localStorage");
					}
					
					// 添加其他可能需要的会话数据
					// 获取当前足迹列表状态，便于返回时恢复
					if (province) {
						localStorage.setItem("footprint_province", province);
					}
					if (city) {
						localStorage.setItem("footprint_city", city);
					}
					localStorage.setItem("footprint_pageindex", pageindex.toString());
				} catch (error) {
					console.error("备份会话数据失败:", error);
				}
			}
			
			// 重置所有筛选条件的函数
			function resetAllFilter() {
				// 重置省份和城市选择
				province = null;
				city = null;
				
				// 更新城市名称显示
				$("#cityname").html("全部");
				
				// 隐藏城市列表
				$("#citylist").attr("style", "display:none");
				
				// 清空城市列表选择
				$("#citylist").html("");
				
				// 重置页码
				pageindex = 1;
				
				// 添加列表重置动画
				$("#alist").css({
					"opacity": "0.5",
					"transform": "translateY(10px)",
					"transition": "all 0.3s ease"
				});
				
				// 获取所有内容
				getcontent(null, null);
				
				// 恢复列表显示
				setTimeout(function() {
					$("#alist").css({
						"opacity": "1",
						"transform": "translateY(0)"
					});
					
					// 显示重置成功提示
					cocoMessage.success(1000, "已重置为全部足迹");
				}, 300);
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
				
				// 添加列表项的入场动画
				setTimeout(function() {
					$(".zjitem").each(function(index) {
						const item = $(this);
						setTimeout(function() {
							item.css({
								"opacity": "0",
								"transform": "translateX(-20px)",
								"transition": "all 0.5s ease"
							});
							
							setTimeout(function() {
								item.css({
									"opacity": "1",
									"transform": "translateX(0)"
								});
							}, 50);
						}, index * 100);
					});
				}, 500);
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
		<script>
			function checkMapLibrary() {
				/* 将单行注释改为多行注释块
				console.log('Available map objects:', {
					JSMap: window.JSMap,
					JsMap: window.JsMap,
					jsmap: window.jsmap,
					ChinaMap: window.ChinaMap
				});
				*/
			}
			window.addEventListener('load', checkMapLibrary);
		</script>
	</body>
</html>
