<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>移动端启动页 - 思政一体化平台</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: center;
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        .logo {
            width: 120px;
            height: 120px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 48px;
            font-weight: bold;
            margin-bottom: 24px;
            backdrop-filter: blur(10px);
            border: 3px solid rgba(255, 255, 255, 0.3);
        }
        
        .title {
            font-size: 28px;
            font-weight: 600;
            margin-bottom: 12px;
        }
        
        .subtitle {
            font-size: 16px;
            opacity: 0.9;
            margin-bottom: 40px;
            line-height: 1.5;
        }
        
        .features {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 16px;
            padding: 24px;
            margin-bottom: 40px;
            backdrop-filter: blur(10px);
            max-width: 400px;
            width: 100%;
        }
        
        .feature-item {
            display: flex;
            align-items: center;
            margin-bottom: 16px;
            font-size: 14px;
        }
        
        .feature-item:last-child {
            margin-bottom: 0;
        }
        
        .feature-icon {
            width: 24px;
            height: 24px;
            margin-right: 12px;
            opacity: 0.8;
        }
        
        .buttons {
            display: flex;
            flex-direction: column;
            gap: 16px;
            width: 100%;
            max-width: 300px;
        }
        
        .btn {
            padding: 16px 24px;
            border: none;
            border-radius: 12px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        
        .btn-primary {
            background: white;
            color: #667eea;
        }
        
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
        }
        
        .btn-secondary {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }
        
        .btn-secondary:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .device-info {
            position: fixed;
            bottom: 20px;
            left: 20px;
            right: 20px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 8px;
            padding: 12px;
            font-size: 12px;
            opacity: 0.7;
            backdrop-filter: blur(10px);
        }
        
        .loading {
            display: none;
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0, 0, 0, 0.8);
            z-index: 9999;
            justify-content: center;
            align-items: center;
            flex-direction: column;
        }
        
        .spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(255, 255, 255, 0.3);
            border-top: 3px solid white;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin-bottom: 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        @media (max-width: 375px) {
            .logo {
                width: 100px;
                height: 100px;
                font-size: 40px;
            }
            
            .title {
                font-size: 24px;
            }
            
            .subtitle {
                font-size: 14px;
            }
            
            .features {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="logo">思</div>
    
    <h1 class="title">思政一体化平台</h1>
    <p class="subtitle">移动端版本<br>传承红色基因，弘扬时代精神</p>
    
    <div class="features">
        <div class="feature-item">
            <svg class="feature-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            完整保留PC端所有功能
        </div>
        <div class="feature-item">
            <svg class="feature-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M17 1H7C5.9 1 5 1.9 5 3V21C5 22.1 5.9 23 7 23H17C18.1 23 19 22.1 19 21V3C19 1.9 18.1 1 17 1Z"/>
            </svg>
            专为移动设备优化设计
        </div>
        <div class="feature-item">
            <svg class="feature-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
            </svg>
            支持触摸手势和交互
        </div>
        <div class="feature-item">
            <svg class="feature-icon" viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            在线学习和社区功能
        </div>
    </div>
    
    <div class="buttons">
        <a href="index.html" class="btn btn-primary" onclick="showLoading()">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            进入移动端
        </a>
        
        <a href="../PC/index.html" class="btn btn-secondary">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 3H4C2.9 3 2 3.9 2 5V19C2 20.1 2.9 21 4 21H20C21.1 21 22 20.1 22 19V5C22 3.9 21.1 3 20 3Z"/>
            </svg>
            访问PC端
        </a>
    </div>
    
    <div class="device-info" id="deviceInfo">
        正在检测设备信息...
    </div>
    
    <div class="loading" id="loading">
        <div class="spinner"></div>
        <p>正在加载移动端...</p>
    </div>
    
    <script>
        // 检测设备信息
        function detectDevice() {
            const userAgent = navigator.userAgent;
            const deviceInfo = document.getElementById('deviceInfo');
            
            let deviceType = '未知设备';
            let browserType = '未知浏览器';
            
            // 检测设备类型
            if (/iPhone/i.test(userAgent)) {
                deviceType = 'iPhone';
            } else if (/iPad/i.test(userAgent)) {
                deviceType = 'iPad';
            } else if (/Android/i.test(userAgent)) {
                deviceType = 'Android设备';
            } else if (/Windows Phone/i.test(userAgent)) {
                deviceType = 'Windows Phone';
            } else {
                deviceType = '桌面设备';
            }
            
            // 检测浏览器类型
            if (/Safari/i.test(userAgent) && !/Chrome/i.test(userAgent)) {
                browserType = 'Safari';
            } else if (/Chrome/i.test(userAgent)) {
                browserType = 'Chrome';
            } else if (/Firefox/i.test(userAgent)) {
                browserType = 'Firefox';
            } else if (/Edge/i.test(userAgent)) {
                browserType = 'Edge';
            }
            
            const screenWidth = window.screen.width;
            const screenHeight = window.screen.height;
            
            deviceInfo.innerHTML = `
                设备：${deviceType} | 浏览器：${browserType} | 屏幕：${screenWidth}×${screenHeight}
            `;
            
            // 如果是移动设备，自动推荐移动端
            if (/iPhone|iPad|Android|Windows Phone/i.test(userAgent)) {
                setTimeout(() => {
                    if (confirm('检测到您正在使用移动设备，是否直接进入移动端？')) {
                        showLoading();
                        window.location.href = 'index.html';
                    }
                }, 2000);
            }
        }
        
        // 显示加载动画
        function showLoading() {
            document.getElementById('loading').style.display = 'flex';
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            detectDevice();
            
            // 检查是否支持触摸
            if ('ontouchstart' in window) {
                console.log('支持触摸交互');
            }
            
            // 检查是否支持Service Worker
            if ('serviceWorker' in navigator) {
                console.log('支持Service Worker');
            }
            
            // 预加载移动端资源
            const link = document.createElement('link');
            link.rel = 'prefetch';
            link.href = 'index.html';
            document.head.appendChild(link);
        });
        
        // 添加键盘快捷键
        document.addEventListener('keydown', function(e) {
            if (e.key === 'Enter' || e.key === ' ') {
                showLoading();
                window.location.href = 'index.html';
            } else if (e.key === 'Escape') {
                window.location.href = '../PC/index.html';
            }
        });
        
        // 添加触摸手势支持
        let startY = 0;
        document.addEventListener('touchstart', function(e) {
            startY = e.touches[0].clientY;
        }, { passive: true });
        
        document.addEventListener('touchend', function(e) {
            const endY = e.changedTouches[0].clientY;
            const deltaY = startY - endY;
            
            // 向上滑动进入移动端
            if (deltaY > 50) {
                showLoading();
                window.location.href = 'index.html';
            }
        }, { passive: true });
    </script>
</body>
</html>
