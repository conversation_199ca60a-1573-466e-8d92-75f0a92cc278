<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>在线学习 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        /* 学习页面专用样式 */
        .learning-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 16px;
            margin-top: 56px;
        }
        
        .learning-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .learning-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .progress-card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 16px;
            margin-top: 16px;
            backdrop-filter: blur(10px);
        }
        
        .progress-title {
            font-size: 14px;
            margin-bottom: 8px;
            opacity: 0.9;
        }
        
        .progress-bar {
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            height: 8px;
            overflow: hidden;
            margin-bottom: 8px;
        }
        
        .progress-fill {
            background: white;
            height: 100%;
            border-radius: 10px;
            transition: width 0.3s ease;
        }
        
        .progress-text {
            font-size: 12px;
            opacity: 0.8;
        }
        
        .category-tabs {
            background: white;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            position: sticky;
            top: 56px;
            z-index: 100;
        }
        
        .tab-scroll {
            display: flex;
            gap: 12px;
            overflow-x: auto;
            padding-bottom: 4px;
        }
        
        .tab-scroll::-webkit-scrollbar {
            display: none;
        }
        
        .category-tab {
            background: #f5f5f5;
            color: #666;
            border: none;
            padding: 8px 16px;
            border-radius: 20px;
            font-size: 14px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .category-tab.active {
            background: #c00714;
            color: white;
        }
        
        .learning-content {
            padding: 16px;
        }
        
        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 16px;
            display: flex;
            align-items: center;
            gap: 8px;
        }
        
        .course-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 12px;
            margin-bottom: 24px;
        }
        
        .course-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }
        
        .course-card:active {
            transform: scale(0.98);
        }
        
        .course-cover {
            height: 100px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 14px;
            font-weight: 500;
            text-align: center;
            padding: 8px;
            position: relative;
        }
        
        .course-badge {
            position: absolute;
            top: 8px;
            right: 8px;
            background: rgba(255, 255, 255, 0.9);
            color: #c00714;
            font-size: 10px;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 600;
        }
        
        .course-info {
            padding: 12px;
        }
        
        .course-title {
            font-size: 13px;
            font-weight: 500;
            line-height: 1.3;
            margin-bottom: 8px;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .course-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 11px;
            color: #999;
        }
        
        .course-progress {
            background: #f0f0f0;
            border-radius: 4px;
            height: 4px;
            overflow: hidden;
            margin-top: 8px;
        }
        
        .course-progress-fill {
            background: #c00714;
            height: 100%;
            border-radius: 4px;
            transition: width 0.3s ease;
        }
        
        .list-view {
            display: none;
        }
        
        .list-view.active {
            display: block;
        }
        
        .course-list-item {
            background: white;
            border-radius: 12px;
            padding: 16px;
            margin-bottom: 12px;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            display: flex;
            gap: 12px;
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
        }
        
        .course-list-item:active {
            transform: translateY(1px);
        }
        
        .course-list-cover {
            width: 80px;
            height: 60px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            text-align: center;
            flex-shrink: 0;
        }
        
        .course-list-info {
            flex: 1;
        }
        
        .course-list-title {
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 4px;
            line-height: 1.3;
        }
        
        .course-list-meta {
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
        }
        
        .view-toggle {
            position: fixed;
            bottom: 140px;
            right: 16px;
            width: 44px;
            height: 44px;
            background: white;
            border: 1px solid #e5e5e5;
            border-radius: 50%;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            cursor: pointer;
            display: flex;
            align-items: center;
            justify-content: center;
            z-index: 999;
            transition: all 0.3s ease;
        }
        
        .view-toggle:hover {
            background: #f8f9fa;
        }
        
        .view-toggle svg {
            width: 20px;
            height: 20px;
            color: #666;
        }
        
        @media (max-width: 375px) {
            .learning-header {
                padding: 16px 12px;
            }
            
            .learning-content {
                padding: 12px;
            }
            
            .course-grid {
                gap: 8px;
            }
            
            .course-cover {
                height: 80px;
                font-size: 12px;
            }
            
            .course-info {
                padding: 8px;
            }
        }
    </style>
</head>
<body class="mobile-learning">
    <!-- 顶部导航栏 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="menu-btn" id="menuBtn">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="logo">
                <h2 style="margin: 0; font-size: 16px; color: #333;">在线学习</h2>
            </div>
            <div class="header-actions">
                <button class="search-btn" id="searchBtn">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                </button>
                <div class="user-info" id="userInfo">
                    <button class="login-btn" id="loginBtn">登录</button>
                    <div class="user-avatar" id="userAvatar" style="display: none;">
                        <span id="userName"></span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 学习头部 -->
    <section class="learning-header">
        <div class="learning-title">
            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 28px; height: 28px;">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3ZM5 13.18V17.18L12 21L19 17.18V13.18L12 17L5 13.18Z"/>
            </svg>
            在线学习
        </div>
        <div class="learning-subtitle">知识改变命运，学习成就未来</div>
        
        <div class="progress-card">
            <div class="progress-title">本月学习进度</div>
            <div class="progress-bar">
                <div class="progress-fill" style="width: 68%"></div>
            </div>
            <div class="progress-text">已完成 17/25 门课程</div>
        </div>
    </section>

    <!-- 分类标签 -->
    <section class="category-tabs">
        <div class="tab-scroll">
            <button class="category-tab active" data-category="all">全部</button>
            <button class="category-tab" data-category="redbook">红色书籍</button>
            <button class="category-tab" data-category="course">课程学习</button>
            <button class="category-tab" data-category="theory">理论学习</button>
            <button class="category-tab" data-category="practice">实践活动</button>
            <button class="category-tab" data-category="exam">考试测验</button>
        </div>
    </section>

    <!-- 学习内容 -->
    <main class="learning-content">
        <!-- 网格视图 -->
        <div class="grid-view active" id="gridView">
            <div class="section-title">
                <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px; color: #c00714;">
                    <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19Z"/>
                </svg>
                推荐课程
            </div>
            <div class="course-grid" id="courseGrid">
                <!-- 课程卡片将通过JavaScript动态加载 -->
            </div>
        </div>

        <!-- 列表视图 -->
        <div class="list-view" id="listView">
            <div class="section-title">
                <svg viewBox="0 0 24 24" fill="currentColor" style="width: 20px; height: 20px; color: #c00714;">
                    <path d="M3 13H15V11H3V13ZM3 17H15V15H3V17ZM3 9H15V7H3V9ZM21 13V7H19V13H21ZM21 17V15H19V17H21Z"/>
                </svg>
                课程列表
            </div>
            <div id="courseList">
                <!-- 课程列表将通过JavaScript动态加载 -->
            </div>
        </div>
    </main>

    <!-- 视图切换按钮 -->
    <button class="view-toggle" id="viewToggle">
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M3 11H11V3H3V11ZM5 5H9V9H5V5ZM13 3V11H21V3H13ZM19 9H15V5H19V9ZM3 21H11V13H3V21ZM5 15H9V19H5V15ZM13 13H21V21H13V13ZM15 15H19V19H15V15Z"/>
        </svg>
    </button>

    <!-- 侧边菜单 -->
    <nav class="side-menu" id="sideMenu">
        <div class="menu-overlay" id="menuOverlay"></div>
        <div class="menu-content">
            <div class="menu-header">
                <div class="menu-logo">
                    <img src="../../PC/img/logo.png" alt="思政一体化平台">
                    <h3>思政一体化平台</h3>
                </div>
                <button class="menu-close" id="menuClose">×</button>
            </div>
            <div class="menu-items" id="menuItems">
                <!-- 菜单项将通过JavaScript动态加载 -->
            </div>
        </div>
    </nav>

    <!-- 搜索面板 -->
    <div class="search-panel" id="searchPanel">
        <div class="search-content">
            <div class="search-input-wrapper">
                <input type="text" placeholder="搜索课程..." id="searchInput">
                <button class="search-submit" id="searchSubmit">搜索</button>
            </div>
            <button class="search-close" id="searchClose">取消</button>
        </div>
    </div>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 4C16.55 4 17 4.45 17 5V9.5L20.5 13L19 14.5L15.5 11H15V5C15 4.45 15.45 4 16 4Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>我的</span>
        </a>
    </nav>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83L16.59 15.41L18 14L12 8L6 14Z"/>
        </svg>
    </button>

    <script>
        let currentCategory = 'all';
        let currentView = 'grid';
        let isLoading = false;

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            initSearch();
            
            // 检查登录状态
            updateLoginUI();
            
            // 初始化分类标签
            initCategoryTabs();
            
            // 初始化视图切换
            initViewToggle();
            
            // 加载学习内容
            loadLearningContent();
        });

        function initCategoryTabs() {
            const categoryTabs = document.querySelectorAll('.category-tab');
            
            categoryTabs.forEach(tab => {
                tab.addEventListener('click', function() {
                    const category = this.getAttribute('data-category');
                    
                    // 更新标签状态
                    categoryTabs.forEach(t => t.classList.remove('active'));
                    this.classList.add('active');
                    
                    // 切换内容
                    currentCategory = category;
                    loadLearningContent();
                });
            });
        }

        function initViewToggle() {
            const viewToggle = document.getElementById('viewToggle');
            const gridView = document.getElementById('gridView');
            const listView = document.getElementById('listView');
            
            viewToggle.addEventListener('click', function() {
                if (currentView === 'grid') {
                    currentView = 'list';
                    gridView.classList.remove('active');
                    listView.classList.add('active');
                    this.innerHTML = `
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 11H11V3H3V11ZM5 5H9V9H5V5ZM13 3V11H21V3H13ZM19 9H15V5H19V9ZM3 21H11V13H3V21ZM5 15H9V19H5V15ZM13 13H21V21H13V13ZM15 15H19V19H15V15Z"/>
                        </svg>
                    `;
                } else {
                    currentView = 'grid';
                    listView.classList.remove('active');
                    gridView.classList.add('active');
                    this.innerHTML = `
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M3 13H15V11H3V13ZM3 17H15V15H3V17ZM3 9H15V7H3V9ZM21 13V7H19V13H21ZM21 17V15H19V17H21Z"/>
                        </svg>
                    `;
                }
                
                // 重新渲染内容
                loadLearningContent();
            });
        }

        function loadLearningContent() {
            if (isLoading) return;

            console.log('开始加载学习内容，当前分类:', currentCategory);
            isLoading = true;

            // 显示加载状态
            const gridContainer = document.getElementById('courseGrid');
            const listContainer = document.getElementById('courseList');
            const loadingHtml = `
                <div class="empty-state" style="text-align: center; padding: 40px 20px;">
                    <div style="font-size: 24px; margin-bottom: 16px;">⏳</div>
                    <div style="font-size: 16px; color: #666;">正在加载...</div>
                </div>
            `;
            if (gridContainer) gridContainer.innerHTML = loadingHtml;
            if (listContainer) listContainer.innerHTML = loadingHtml;

            // 根据分类加载不同内容
            if (currentCategory === 'redbook') {
                loadRedBooks();
            } else if (currentCategory === 'course') {
                loadCourses();
            } else {
                loadAllContent();
            }
        }

        function loadRedBooks() {
            // 使用修复后的API，传入默认的红色书籍分类ID
            getRedBooksData(1, 20, "912354240784109568", "1369261422076366848").then(data => {
                console.log('红色书籍数据加载成功:', data);
                renderContent(data.list || [], 'redbook');
                isLoading = false;
            }).catch(error => {
                console.error('加载红色书籍失败:', error);
                renderErrorState('红色书籍加载失败，请检查网络连接');
                isLoading = false;
            });
        }

        function loadCourses() {
            // 使用修复后的API，可以传入分类ID
            getCoursesData(1, 20, null).then(data => {
                console.log('课程数据加载成功:', data);
                renderContent(data.list || [], 'course');
                isLoading = false;
            }).catch(error => {
                console.error('加载课程失败:', error);
                // 显示友好的错误信息
                renderErrorState('课程加载失败，请检查网络连接或稍后重试');
                isLoading = false;
            });
        }

        function loadAllContent() {
            // 加载混合内容 - 使用修复后的API
            Promise.all([
                getRedBooksData(1, 10, null, null).catch((error) => {
                    console.warn('红色书籍加载失败:', error);
                    return { list: [] };
                }),
                getCoursesData(1, 10, null).catch((error) => {
                    console.warn('课程加载失败:', error);
                    return { list: [] };
                })
            ]).then(([redbooks, courses]) => {
                console.log('混合内容加载结果:', { redbooks, courses });

                const allContent = [
                    ...(redbooks.list || []).map(item => ({ ...item, type: 'redbook' })),
                    ...(courses.list || []).map(item => ({ ...item, type: 'course' }))
                ];

                // 按时间排序
                allContent.sort((a, b) => new Date(b.createTime || b.publishedTime) - new Date(a.createTime || a.publishedTime));

                if (allContent.length > 0) {
                    renderContent(allContent, 'mixed');
                } else {
                    renderErrorState('暂无学习内容，请稍后再试');
                }
                isLoading = false;
            }).catch(error => {
                console.error('加载全部内容失败:', error);
                renderErrorState('内容加载失败，请检查网络连接');
                isLoading = false;
            });
        }

        function renderContent(data, type) {
            if (currentView === 'grid') {
                renderGridView(data, type);
            } else {
                renderListView(data, type);
            }
        }

        function renderGridView(data, type) {
            const container = document.getElementById('courseGrid');
            
            if (!data || data.length === 0) {
                container.innerHTML = '<div class="empty-state">暂无内容</div>';
                return;
            }
            
            let html = '';
            data.forEach(item => {
                const progress = Math.floor(Math.random() * 100);
                const itemType = item.type || type;
                const badge = itemType === 'redbook' ? '红色书籍' : '课程学习';
                
                html += `
                    <a href="${getItemUrl(item, itemType)}" class="course-card">
                        <div class="course-cover">
                            <span class="course-badge">${badge}</span>
                            ${item.title || item.name || '无标题'}
                        </div>
                        <div class="course-info">
                            <div class="course-title">${item.title || item.name || '无标题'}</div>
                            <div class="course-meta">
                                <span>${MobileUtils.formatTime(item.createTime || item.publishedTime)}</span>
                                <span>${progress}%</span>
                            </div>
                            <div class="course-progress">
                                <div class="course-progress-fill" style="width: ${progress}%"></div>
                            </div>
                        </div>
                    </a>
                `;
            });
            
            container.innerHTML = html;
        }

        function renderListView(data, type) {
            const container = document.getElementById('courseList');
            
            if (!data || data.length === 0) {
                container.innerHTML = '<div class="empty-state">暂无内容</div>';
                return;
            }
            
            let html = '';
            data.forEach(item => {
                const progress = Math.floor(Math.random() * 100);
                const itemType = item.type || type;
                const badge = itemType === 'redbook' ? '红色书籍' : '课程学习';
                
                html += `
                    <a href="${getItemUrl(item, itemType)}" class="course-list-item">
                        <div class="course-list-cover">${badge}</div>
                        <div class="course-list-info">
                            <div class="course-list-title">${item.title || item.name || '无标题'}</div>
                            <div class="course-list-meta">${MobileUtils.formatTime(item.createTime || item.publishedTime)} • 进度 ${progress}%</div>
                            <div class="course-progress">
                                <div class="course-progress-fill" style="width: ${progress}%"></div>
                            </div>
                        </div>
                    </a>
                `;
            });
            
            container.innerHTML = html;
        }

        function getItemUrl(item, type) {
            if (type === 'redbook') {
                return `redbook-detail.html?id=${item.id}`;
            } else {
                return `course-detail.html?id=${item.id}`;
            }
        }

        function renderEmptyState() {
            const gridContainer = document.getElementById('courseGrid');
            const listContainer = document.getElementById('courseList');

            const emptyHtml = '<div class="empty-state">暂无学习内容</div>';

            if (gridContainer) gridContainer.innerHTML = emptyHtml;
            if (listContainer) listContainer.innerHTML = emptyHtml;
        }

        function renderErrorState(message = '加载失败，请重试') {
            const gridContainer = document.getElementById('courseGrid');
            const listContainer = document.getElementById('courseList');

            const errorHtml = `
                <div class="empty-state" style="text-align: center; padding: 40px 20px;">
                    <div style="font-size: 48px; margin-bottom: 16px;">⚠️</div>
                    <div style="font-size: 16px; color: #333; margin-bottom: 8px;">加载失败</div>
                    <div style="font-size: 14px; color: #666; margin-bottom: 20px;">${message}</div>
                    <button onclick="loadLearningContent()" style="
                        padding: 10px 20px;
                        background: #c00714;
                        color: white;
                        border: none;
                        border-radius: 6px;
                        cursor: pointer;
                        font-size: 14px;
                    ">重试</button>
                </div>
            `;

            if (gridContainer) gridContainer.innerHTML = errorHtml;
            if (listContainer) listContainer.innerHTML = errorHtml;
        }
    </script>
</body>
</html>
