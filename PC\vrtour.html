<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-VR红色游学</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/vrtour.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<style>
			/* 添加新的样式 */
			.loading-overlay {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(255, 255, 255, 0.9);
				display: flex;
				justify-content: center;
				align-items: center;
				z-index: 9999;
				transition: opacity 0.3s;
			}
			
			.loading-spinner {
				width: 50px;
				height: 50px;
				border: 3px solid #f3f3f3;
				border-top: 3px solid #A65D57;
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}
			
			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}
			
			.ssinputbox {
				position: relative;
				transition: all 0.3s ease;
			}
			
			.ssinputbox input {
				transition: all 0.3s ease;
				border: 1px solid #ddd;
			}
			
			.ssinputbox input:focus {
				border-color: #A65D57;
				box-shadow: 0 0 5px rgba(166, 93, 87, 0.3);
			}
			
			.ssinputbox div {
				transition: all 0.3s ease;
			}
			
			.ssinputbox div:hover {
				background-color: #A65D57;
				transform: translateY(-2px);
			}
			
			.rcdiv span {
				transition: all 0.3s ease;
				position: relative;
				overflow: hidden;
			}
			
			.rcdiv span:hover {
				transform: translateY(-2px);
				box-shadow: 0 2px 8px rgba(0,0,0,0.1);
			}
			
			.rcdiv span::after {
				content: '';
				position: absolute;
				bottom: 0;
				left: 0;
				width: 0;
				height: 2px;
				background-color: #A65D57;
				transition: width 0.3s ease;
			}
			
			.rcdiv span:hover::after {
				width: 100%;
			}
			
			.imgbox .item {
				transition: all 0.3s ease;
			}
			
			.imgbox .item:hover {
				transform: translateY(-5px);
				box-shadow: 0 5px 15px rgba(0,0,0,0.1);
			}
			
			.imgbox .item img {
				transition: all 0.3s ease;
			}
			
			.imgbox .item:hover img {
				transform: scale(1.05);
			}
			
			.imgbox .item .itemname {
				transition: all 0.3s ease;
			}
			
			.imgbox .item:hover .itemname {
				color: #A65D57;
			}
			
			/* 添加平滑滚动 */
			html {
				scroll-behavior: smooth;
			}
			
			/* 返回顶部按钮动画 */
			#backtop {
				transition: all 0.3s ease;
				opacity: 0;
				visibility: hidden;
			}
			
			#backtop.visible {
				opacity: 1;
				visibility: visible;
			}
			
			#backtop:hover {
				transform: translateY(-5px);
			}
			
			/* 添加页面切换动画 */
			body {
				opacity: 0;
				animation: fadeIn 0.5s ease forwards;
			}
			
			@keyframes fadeIn {
				from { opacity: 0; }
				to { opacity: 1; }
			}
			
			/* iframe弹窗样式 */
			#iframe {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				z-index: 9998;
				border: none;
				display: none;
				background: #fff;
			}
			
			.iframe-container {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				z-index: 9997;
				background: rgba(0, 0, 0, 0.5);
				display: none;
			}
			
			/* 返回按钮样式 */
			#closes {
				position: fixed;
				top: 20px;
				right: 20px;
				z-index: 9999;
				background: #A65D57;
				color: white;
				padding: 10px 20px;
				border-radius: 5px;
				cursor: pointer;
				display: none;
				transition: all 0.3s ease;
				font-size: 14px;
				display: flex;
				align-items: center;
				gap: 5px;
			}
			
			#closes:before {
				content: "←";
				font-size: 16px;
			}
			
			#closes:hover {
				background: #8B4513;
				transform: translateY(-2px);
				box-shadow: 0 2px 8px rgba(0,0,0,0.2);
			}
		</style>
	</head>
	<body class="index">
		<!-- 添加加载动画 -->
		<div class="loading-overlay">
			<div class="loading-spinner"></div>
		</div>
		
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="content">
			<div class="maptitle">
				<img src="img/vrtitle.png" />
			</div>
			<div class="ssdiv">
				<div class="ssinputbox">
					<input id="ssname" onchange="ssssssss()" />
					<div onclick="ss()">搜索</div>
				</div>
			</div>
			<div class="rcdiv" id="rcdiv">

			</div>
			<div class="imgbox" id="vrlist">

			</div>
		</div>

		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<div class="iframe-container" id="iframe-container">
			<iframe src="" id="iframe"></iframe>
			<div id="closes">返回列表</div>
		</div>
		<script src="js/clipboard.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
			let rclist = null
			let classid = null
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html';
				});
				
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide();
					$("#user").show();
					$("#user").html(JSON.parse(userinfo).name);
					$("#edit").show();
					
					if (window.localStorage.getItem("jilu")) {
						$.ajax({
							url: baseurl + "/study/record/add",
							type: 'post',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: window.localStorage.getItem("jilu"),
							dataType: 'json',
							success: (res) => {
								window.localStorage.clear();
							}
						});
					}
				} else {
					//未登录 则显示登录按钮
					$("#login").show();
					$("#user").hide();
					$("#edit").hide();
				}
				
				// 检查是否从搜索页面跳转，如果是则直接加载指定ID
				const id = getUrlParam('id');
				const directLoad = getUrlParam('directLoad');
				
				if (id && directLoad === 'true') {
					// 直接加载指定ID的内容
					loadVirtualTourById(id);
				} else {
					// 正常加载列表
					getclass();
					getclassid();
					getfooterlink();
				}
			});
			
			// 根据ID直接加载虚拟游览
			function loadVirtualTourById(id) {
				$.ajax({
					url: baseurl + "/web/posts/" + id,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200' && res.data) {
							// 自动点击打开虚拟游览
							const item = {
								attr: function(name) {
									if (name === 'data-id') return id;
									if (name === 'data-url') return res.data.redirectUrl;
									return null;
								}
							};
							
							// 延迟执行，确保页面已加载
							setTimeout(function() {
								showurl(item);
							}, 500);
							
							// 同时加载列表，以便用户返回后可以看到
							getclass();
							getclassid();
							getfooterlink();
						} else {
							cocoMessage.error(2000, "获取内容失败，请刷新重试");
							// 加载列表
							getclass();
							getclassid();
							getfooterlink();
						}
					},
					error: () => {
						cocoMessage.error(2000, "获取内容失败，请刷新重试");
						// 加载列表
						getclass();
						getclassid();
						getfooterlink();
					}
				});
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = "";
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>';
							});
							$("#linkbox").html(html);
						}
					}
				});
			}

			function getclassid() {
				$.ajax({
					url: baseurl + "/web/category/vr",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							classid = res.data[0].id;
							getvrlist();
						}
					}
				});
			}

			function ss() {
				let name = $("#ssname").val();
				// console.log(name)
				getvrlist(name);
			}

			function getvrlist(name) {
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: 1,
						pageSize: 999999,
						categoryId: classid,
						title: name
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = "";
							let rc = [];
							res.data.list.map((item) => {
								rc.push(item.keyWords);
								html += '<div class="item"><img onclick="showurl(this)" data-id="' + item
									.id + '" data-url="' + item.redirectUrl + '" src="' + baseurl + item
									.thumbPath[0] + '" />' +
									'<div class="itembottom"><div class="ffff">' + item.title +
									'</div><div class="llll">' + item.keyWords +
									'</div></div></div>';
							});
							$("#vrlist").html(html);
							if (!rclist) {
								rclist = rc;
							}

							let rchtml = "<label>热词：</label>";
							rclist.map((item, index) => {
								if (index < 5) {
									rchtml += '<span onclick="sssss(this)">' + item + '</span>';
								}
							});
							$("#rcdiv").html(rchtml);
						}
					}
				});
			}
			
			let clipboard = new ClipboardJS('.copybtn');
			clipboard.on('success', function(e) {
				e.clearSelection();
				cocoMessage.success(1000, "复制成功！");
			});
			
			clipboard.on('error', function(e) {
				cocoMessage.error(1000, "复制失败！");
			});
			
			function ssssssss() {
				getvrlist($("#ssname").val());
			}

			function sssss(item) {
				$("#ssname").val($(item).html());
				getvrlist($(item).html());
			}

			function clicknum(id) {
				$.ajax({
					url: baseurl + "/posts/click/" + id,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {}
				});
			}

			function showurl(item) {
				clicknum($(item).attr("data-id"));
				if (userinfo) {
					let url = $(item).attr("data-url");
					// 直接在iframe中打开URL
					$("#iframe").css('opacity', 0)
						.attr("src", url);
					$("#iframe-container, #iframe, #closes").fadeIn(300);
					$("#iframe").animate({opacity: 1}, 300);
					
					// 添加错误处理
					$("#iframe").on('load', function() {
						$(this).animate({opacity: 1}, 300);
					}).on('error', function() {
						closeurl();
						cocoMessage.error(2000, "页面加载失败，请稍后重试");
					});
				} else {
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html';
				}
			}

			function closeurl() {
				$("#iframe-container, #iframe, #closes").fadeOut(300, function() {
					$("#iframe").attr("src", "");
					// 移除事件监听
					$("#iframe").off('load error');
				});
			}
			
			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear();
				$("#login").show();
				$("#user").hide();
				$("#edit").hide();
				cocoMessage.error(1000, "已退出登录！");
			}
		</script>
		<script>
			$("#backtop").hide();
			
			$(function() {
				// 页面加载优化
				setTimeout(function() {
					$('.loading-overlay').fadeOut();
				}, 500);
				
				// 图片懒加载
				$('.imgbox img').each(function() {
					$(this).attr('data-src', $(this).attr('src'));
					$(this).attr('src', '');
				});
				
				function lazyLoad() {
					$('.imgbox img').each(function() {
						if ($(this).offset().top < $(window).scrollTop() + $(window).height() + 100) {
							$(this).attr('src', $(this).attr('data-src'));
						}
					});
				}
				
				lazyLoad();
				$(window).on('scroll', lazyLoad);
				
				// 平滑滚动
				$('a[href*="#"]').on('click', function(e) {
					e.preventDefault();
					$('html, body').animate({
						scrollTop: $($(this).attr('href')).offset().top
					}, 500);
				});
				
				// 返回顶部按钮动画
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$('#backtop').addClass('visible');
					} else {
						$('#backtop').removeClass('visible');
					}
				});
				
				// 搜索框动画
				$('.ssinputbox input').focus(function() {
					$(this).parent().css('transform', 'scale(1.02)');
				}).blur(function() {
					$(this).parent().css('transform', 'scale(1)');
				});
				
				// 热词标签动画
				$('.rcdiv span').each(function(index) {
					$(this).css('animation-delay', (index * 0.1) + 's');
				});
			});
		</script>
		<script>
			// 绑定返回列表按钮点击事件
			$("#closes").on('click', function() {
				closeurl();
			});
		</script>
	</body>
</html>
