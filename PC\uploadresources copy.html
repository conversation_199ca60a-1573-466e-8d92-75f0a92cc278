<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-个人中心</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a href="studentachievement.html" class="leftitem">学生成绩统计</a>
						<a href="studentlearning.html" class="leftitem">学习记录统计</a>
						<a href="userexamination1.html" class="leftitem">考试管理</a>
						<a href="learningtasks.html" class="leftitem">学习任务管理</a>
						<a class="leftitem activeleftitem">成果展示</a>
						<a href="releaseyourvoice2.html" class="leftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="userinfotopdiv">
						<a href="achievements.html">成果列表</a>
						<a class="acccccg">发布资源</a>
					</div>
					
					<div id="loading" style="display: none;" class="updatebox">
						<div class="info"><img id="loadimg" src="img/loading.png"/>正在添加资源！请不要刷新网页！</div>
					</div>
					<div id="success" style="display: none;" class="updatebox">
						<div class="successbox">
							<div class="info"><img src="img/upsuccess.png"/>发布成功!</div>
							<div class="successbtn">
								<div onclick="showinfo()">点击查看</div>
								<div onclick="jixufabu()">继续发布</div>
							</div>
						</div>
					</div>
					<div id="update" style="display: block;" class="updatebox">
						<div class="updateitem">
							<div class="updateitemtitle">
								标题:
							</div>
							<input id="bt" placeholder="请输入标题" />
						</div>
						<div class="updateitem">
							<div class="updateitemtitle">
								作者:
							</div>
							<input id="zz" placeholder="请输入作者" />
						</div>
						<div class="updateitem">
							<div class="updateitemtitle">
								组织:
							</div>
							<input id="zuzhi" placeholder="请输入学校或出版社" />
						</div>
						<div class="updateitem">
							<div class="updateitemtitle">
								知识点:
							</div>
							<input id="zhishidian" placeholder="请输入知识点(多个知识点用逗号隔开)" />
						</div>
						<div class="updateitem">
							<div class="updateitemtitle">
								课程简介:
							</div>
							<input id="jj" placeholder="请输入简介" />
						</div>
						<div class="updateitem">
							<div class="updateitemtitle">
								资源类型:
							</div>
							<select style="width: 11.458333rem;" id="types">
								<option value="0">教学成果</option>
								<option value="1">外来资源</option>
							</select>
						</div>
						<div class="updateitem">
							<div class="updateitemtitle">
								属性:
							</div>
							<select style="width: 11.458333rem;" id="shuxing">
								
							</select>
						</div>
						<div class="updateitem">
							<div class="updateitemtitle">
								学科:
							</div>
							<select id="xueke" onchange="xuekechange()">
								
							</select>
						</div>
						<div class="updateitem">
							<div class="updateitemtitle">
								章:
							</div>
							<select style="width: 11.458333rem;" id="zhang" onchange="zhangchange()">
								<option value="0">请选择章</option>
							</select>
						</div>
						<div class="updateitem">
							<div class="updateitemtitle">
								节:
							</div>
							<select style="width: 11.458333rem;" id="jie" onchange="jiechange()">
								<option value="0">请选择节</option>
							</select>
						</div>
						<div class="updateitem">
							<div class="updateitemtitle">
								小节:
							</div>
							<select style="width: 11.458333rem;" id="xiaojie">
								<option value="0">请选择小节</option>
							</select>
						</div>
						
						<div class="updateitem2">
							<div class="updateitemtitle">
								附件上传:
							</div>
							<div class="updat4eboxsss">
								<div class="files">
									<div class="inputboxsss" id="fjlist">

									</div>
									<div class="tjbtn">
										<input accept=".mp4" id="filefj" onchange="selectfj()" type="file" />
										<img src="img/add.png"/>添加附件</div>
									<label>(*.mp4)</label>
								</div>
								<div class="files">
									<div class="inputboxsss" id="fjfmlist">
										
									</div>
									<div class="tjbtn">
										<input accept=".jpg,.png,.jpeg" id="filefjfm" onchange="selectfjfm()" type="file" />
										<img src="img/add.png"/>添加封面</div>
								</div>
							</div>
						</div>
						<div class="updateitem2">
							<div class="updateitemtitle">
								课件上传:
							</div>
							<div class="updat4eboxsss">
								<div class="files">
									<div class="inputboxsss" id="kjlist">
									</div>
									<div class="tjbtn">
										<input accept=".ppt,.pptx" id="filekj" onchange="selectkj()" type="file" />
										<img src="img/add.png"/>添加课件</div>
									<label>(*.ppt/pptx)</label>
								</div>
								<div class="files">
									<div class="inputboxsss" id="kjfmlist">
										
									</div>
									<div class="tjbtn">
										<input accept=".jpg,.png,.jpeg" id="filekjfm" onchange="selectkjfm()" type="file" />
										<img src="img/add.png"/>添加课件封面</div>
								</div>
							</div>
						</div>
						
						<div class="updateitem2">
							<div class="updateitemtitle">
								试题上传:
							</div>
							<div class="updat4eboxsss">
								<div class="files">
									<div class="inputboxsss" id="stlist">
										
									</div>
									<div class="tjbtn">
										<input accept=".pdf" id="filest" onchange="selectst()" type="file" />
										<img src="img/add.png"/>添加试题</div>
									<label>(*.pdf)</label>
								</div>
								<div class="files">
									<div class="inputboxsss" id="stfmlist">
									</div>
									<div class="tjbtn">
										<input accept=".jpg,.png,.jpeg" id="filestfm" onchange="selectstfm()" type="file" />
										<img src="img/add.png"/>添加试题封面</div>
								</div>
							</div>
						</div>
						<div class="updateitem">
							<div class="submitbtn" onclick="submits()">确认发布</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let shuxinglist = null
			let leixinglist = null
			let xuekelist = null
			let upclassid = null
			let type = 'teacher' //teacher/student
			var mySwiper = new Swiper('.museumboxitembottoml .swiper', {
				autoplay: false,
				loop: true,
				pagination: {
					el: '.museumboxitembottoml .swiper-pagination',
					clickable: true
				}
			})
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				let userinfo = sessionStorage.getItem("userinfo")
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getclassid()
				getxueke()
				getshuxin()
				getfenlei()
				getfooterlink()
			})
			function getfooterlink(){
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item)=>{
								html+='<a href="'+item.url+'" target="_blank">'+item.name+'</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
			function xuekechange(){//学科被选择时
				if($("#xueke").val()=='0'){
					$("#zhang").html('<option value=0>请选择章</option>')
				}else{
					xuekelist.map((item)=>{
						if(item.id==$("#xueke").val()){
							let zhtml = "<option value=0>请选择章</option>"
							item.children.map((item2)=>{
								zhtml+='<option value="'+item2.id+'">'+item2.name+'</option>'
							})
							$("#zhang").html(zhtml)
						}
					})
				}
			}
			function zhangchange(){//章被选择时
				if($("#zhang").val()=='0'){
					$("#jie").html('<option value=0>请选择节</option>')
				}else{
					xuekelist.map((item)=>{
						if(item.id==$("#xueke").val()){
							item.children.map((item2)=>{
								if(item2.id == $("#zhang").val()){
									let jiehtml = "<option value=0>请选择节</option>"
									item2.children.map((item3)=>{
										jiehtml+='<option value="'+item3.id+'">'+item3.name+'</option>'
									})
									$("#jie").html(jiehtml)
								}
							})
							$("#zhang").html(zhtml)
						}
					})
				}
			}
			
			function jiechange(){//当节被选择
				if($("#jie").val()=='0'){
					$("#xiaojie").html('<option value=0>请选择小节</option>')
				}else{
					xuekelist.map((item)=>{
						if(item.id==$("#xueke").val()){
							item.children.map((item2)=>{
								if(item2.id == $("#zhang").val()){
									item2.children.map((item3)=>{
										if(item3.id == $("#jie").val()){
											let xiaojiehtml = "<option value=0>请选择小节</option>"
											item3.children.map((item4)=>{
												xiaojiehtml+='<option value="'+item4.id+'">'+item4.name+'</option>' 
											})
											$("#xiaojie").html(xiaojiehtml)
										}
									})
								}
							})
							$("#zhang").html(zhtml)
						}
					})
				}
			}
			function getfenlei(){//获取类型
				$.ajax({
					url: baseurl + "/types",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log('类型',res.data)
							leixinglist = res.data
						}
					}
				})
			}
			function getshuxin(){//获取属性
				$.ajax({
					url: baseurl + "/attributes",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log('属性',res.data)
							shuxinglist = res.data
							let html = "<option value=0>请选择属性</option>"
							shuxinglist.map((item)=>{
								html += '<option value="'+item.id+'">'+item.name+'</option>'
							})
							$("#shuxing").html(html)
						}
					}
				})
			}
			function getxueke(){//获取学科树
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log('学科',res.data)
							xuekelist = res.data
							let xuekehtml = "<option value=0>请选择学科</option>"
							xuekelist.map((item)=>{
								xuekehtml+='<option value="'+item.id+'">'+item.name+'</option>'
							})
							$("#xueke").html(xuekehtml)
						}
					}
				})
			}
			function getclassid(){
				$.ajax({
					url: baseurl + "/category/teaching",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							upclassid = res.data[0].id
						}
					}
				})
			}
			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 						.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
			let addjson = {
				title: null,//标题
				author: null,//作者
				introduction: null,//简介
				projectId: null,//学科
				sectionId: null,//章
				nodeId: null,//节
				barId: null,//小节
				attributesId: null,//属性
				categoryId: null,//分类ID
				organization: null,//组织
				type: 0,
				cmsResourcesCourseMetaList:[],
				knowledge: null
			}
			let fjdata = {
				attachId: null,
				coverId: null,
				attachType: null,
				typeId: null,
				attachName: null
			}
			let kjdata = {
				attachId: null,
				coverId: null,
				attachType: null,
				typeId: null,
				attachName: null
			}
			let stdata = {
				attachId: null,
				coverId: null,
				attachType: null,
				typeId: null,
				attachName: null
			}
			let fjfile = null
			let kjfile = null
			let stfile = null
			let imgfj = null
			let imgkj = null
			let imgst = null
			function selectfj(){ //附件选择
				let file = document.getElementById('filefj').files[0]
				fjfile = file //赋值
				// console.log(file)
				if(file){
					$("#fjlist").html('<div class="filesitem"><div>'+file.name+'</div><img onclick="fjclose()" class="close" src="img/closered.png" /></div>')
				}
			}
			function fjclose(){ //附件清空
				$("#fjlist").html('')
				let fles = document.getElementById('filefj')
				fles.outerHTML=fles.outerHTML;
				fjfile = null //清空
			}
			function selectfjfm(){//附件封面选择
				let file = document.getElementById('filefjfm').files[0]
				imgfj = file //赋值
				// console.log(file)
				if(file){
					$("#fjfmlist").html('<div class="filesitem"><img src="'+window.URL.createObjectURL(file)+'" /><img onclick="fjfmclose()" class="close" src="img/closered.png" /></div>')
				}
			}
			function fjfmclose(){//清空附件封面
				$("#fjfmlist").html('')
				let fles = document.getElementById('filefjfm')
				fles.outerHTML=fles.outerHTML;
				imgfj = null //清空
			}
			function selectkj(){//选择课件
				let file = document.getElementById('filekj').files[0]
				kjfile = file //赋值
				// console.log(file)
				if(file){
					$("#kjlist").html('<div class="filesitem"><div>'+file.name+'</div><img onclick="kjclose()" class="close" src="img/closered.png" /></div>')
				}
			}
			function kjclose(){//清空课件
				$("#kjlist").html('')
				let fles = document.getElementById('filekj')
				fles.outerHTML=fles.outerHTML;
				kjfile = null //清空
			}
			function selectkjfm(){//选择课件封面
				let file = document.getElementById('filekjfm').files[0]
				imgkj = file //赋值
				// console.log(file)
				if(file){
					$("#kjfmlist").html('<div class="filesitem"><img src="'+window.URL.createObjectURL(file)+'" /><img onclick="kjfmclose()" class="close" src="img/closered.png" /></div>')
				}
			}
			function kjfmclose(){//清空课件封面
				$("#kjfmlist").html('')
				let fles = document.getElementById('filekjfm')
				fles.outerHTML=fles.outerHTML;
				imgkj = null //清空
			}
			function selectst(){//选择试题
				let file = document.getElementById('filest').files[0]
				stfile = file //赋值
				// console.log(file)
				if(file){
					$("#stlist").html('<div class="filesitem"><div>'+file.name+'</div><img onclick="stclose()" class="close" src="img/closered.png" /></div>')
				}
			}
			function stclose(){//清空试题
				$("#stlist").html('')
				let fles = document.getElementById('filest')
				fles.outerHTML=fles.outerHTML;
				stfile = null //清空
			}
			function selectstfm(){//选择试题封面
				let file = document.getElementById('filestfm').files[0]
				imgst = file //赋值
				// console.log(file)
				if(file){
					$("#stfmlist").html('<div class="filesitem"><img src="'+window.URL.createObjectURL(file)+'" /><img onclick="stfmclose()" class="close" src="img/closered.png" /></div>')
				}
			}
			function stfmclose(){//清空试题封面
				$("#stfmlist").html('')
				let fles = document.getElementById('filestfm')
				fles.outerHTML=fles.outerHTML;
				imgst = null //清空
			}
			function submits(){
				//属性 必选   标题 作者 简介  组织 必选
				if(!$("#bt").val()){
					cocoMessage.warning(1000, "请输入标题！")
				}else if(!$("#zz").val()){
					cocoMessage.warning(1000, "请输入作者！")
				}else if(!$("#zuzhi").val()){
					cocoMessage.warning(1000, "请输入组织！")
				}else if(!$("#jj").val()){
					cocoMessage.warning(1000, "请输入简介！")
				}else if($("#shuxing").val()=='0'){
					cocoMessage.warning(1000, "请请选择属性！")
				}else{
					$("#update").hide()
					$("#loading").show()
					$("#success").hide()
					addjson.categoryId = upclassid
					addjson.title = $("#bt").val()
					addjson.type = $("#types").val()
					addjson.author = $("#zz").val()
					addjson.organization = $("#zuzhi").val()
					addjson.introduction = $("#jj").val()
					addjson.attributesId = $("#shuxing").val()
					addjson.knowledge = $("#zhishidian").val()
					
					if($("#xueke").val()!='0'){
						addjson.projectId = $("#xueke").val()
						addjson.sectionId = $("#zhang").val()
						addjson.nodeId = $("#jie").val()
						addjson.barId = $("#xiaojie").val()
					}else{
						addjson.projectId = null
						addjson.sectionId = null
						addjson.nodeId = null
						addjson.barId = null
					}
				
					
					Promise.all([
						//上传附件
						new Promise((resolve, reject)=>{
							if(fjfile){//如果选择了附件 则上传附件
								let formData = new FormData()
								formData.append('files',fjfile)
								$.ajax({
									url: baseurl + "/attachments/uploads",
									type: 'post',
									headers: {
										"Authorization": sessionStorage.getItem("header")
									},
									data: formData,
									contentType: false,
									processData: false,
									success: (res) => {
										if(res.code == '200'){
											fjdata.attachId = res.data[0].id //附件ID
											fjdata.attachName = res.data[0].fileName //附件名称
											fjdata.attachType = res.data[0].type //附件类型
											let idid = null
											leixinglist.map((item)=>{
												if(item.name == "微课库"){
													idid = item.id
												}
											})
											fjdata.typeId = idid
											resolve()
										}else{
											reject('附件上传失败！')
										}
									}
								})
							}else{
								resolve()
							}
						}),
						//上传附件封面
						new Promise((resolve, reject)=>{
							if(imgfj){//如果选择了附件封面 则上传附件封面
								let formData = new FormData()
								formData.append('files',imgfj)
								$.ajax({
									url: baseurl + "/attachments/uploads",
									type: 'post',
									headers: {
										"Authorization": sessionStorage.getItem("header")
									},
									data: formData,
									contentType: false,
									processData: false,
									success: (res) => {
										if(res.code == '200'){
											fjdata.coverId = res.data[0].id //附件封面id
											resolve()
										}else{
											reject('附件封面上传失败！')
										}
									}
								})
							}else{
								resolve()
							}
						}),
						//上传课件
						new Promise((resolve, reject)=>{
							if(kjfile){//如果选择了课件 则上传课件
								let formData = new FormData()
								formData.append('files',kjfile)
								$.ajax({
									url: baseurl + "/attachments/uploads",
									type: 'post',
									headers: {
										"Authorization": sessionStorage.getItem("header")
									},
									data: formData,
									contentType: false,
									processData: false,
									success: (res) => {
										if(res.code == '200'){
											kjdata.attachId = res.data[0].id //附件ID
											kjdata.attachName = res.data[0].fileName //附件名称
											kjdata.attachType = res.data[0].type //附件类型
											let idid = null
											leixinglist.map((item)=>{
												if(item.name == "课件库"){
													idid = item.id
												}
											})
											kjdata.typeId = idid
											resolve()
										}else{
											reject('课件上传失败！')
										}
									}
								})
							}else{
								resolve()
							}
						}),
						//上传课件封面
						new Promise((resolve, reject)=>{
							if(imgkj){//如果选择了附件封面 则上传附件封面
								let formData = new FormData()
								formData.append('files',imgkj)
								$.ajax({
									url: baseurl + "/attachments/uploads",
									type: 'post',
									headers: {
										"Authorization": sessionStorage.getItem("header")
									},
									data: formData,
									contentType: false,
									processData: false,
									success: (res) => {
										if(res.code == '200'){
											kjdata.coverId = res.data[0].id //附件封面id
											resolve()
										}else{
											reject('课件封面上传失败！')
										}
									}
								})
							}else{
								resolve()
							}
						}),
						//上传试题
						new Promise((resolve, reject)=>{
							if(stfile){//如果选择了试题 则上传试题
								let formData = new FormData()
								formData.append('files',stfile)
								$.ajax({
									url: baseurl + "/attachments/uploads",
									type: 'post',
									headers: {
										"Authorization": sessionStorage.getItem("header")
									},
									data: formData,
									contentType: false,
									processData: false,
									success: (res) => {
										if(res.code == '200'){
											stdata.attachId = res.data[0].id //附件ID
											stdata.attachName = res.data[0].fileName //附件名称
											stdata.attachType = res.data[0].type //附件类型
											let idid = null
											leixinglist.map((item)=>{
												if(item.name == "试题库"){
													idid = item.id
												}
											})
											stdata.typeId = idid
											resolve()
										}else{
											reject('试题上传失败！')
										}
									}
								})
							}else{
								resolve()
							}
						}),
						//上传试题封面
						new Promise((resolve, reject)=>{
							if(imgst){//如果选择了附件封面 则上传附件封面
								let formData = new FormData()
								formData.append('files',imgst)
								$.ajax({
									url: baseurl + "/attachments/uploads",
									type: 'post',
									headers: {
										"Authorization": sessionStorage.getItem("header")
									},
									data: formData,
									contentType: false,
									processData: false,
									success: (res) => {
										if(res.code == '200'){
											stdata.coverId = res.data[0].id //附件封面id
											resolve()
										}else{
											reject('试题封面上传失败！')
										}
									}
								})
							}else{
								resolve()
							}
						}),
					]).then((res)=>{
						// console.log(fjdata)
						// console.log(kjdata)
						// console.log(stdata)
						if(fjdata.attachId){
							addjson.cmsResourcesCourseMetaList.push(fjdata)
						}
						if(kjdata.attachId){
							addjson.cmsResourcesCourseMetaList.push(kjdata)
						}
						if(stdata.attachId){
							addjson.cmsResourcesCourseMetaList.push(stdata)
						}
						$.ajax({
							url: baseurl + "/course/add",
							type: 'POST',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: JSON.stringify(addjson),
							dataType: 'json',
							success: (res) => {
								if (res.code == '200') {
									$("#update").hide()
									$("#loading").hide()
									$("#success").show()
									// console.log("添加成功")
								}
							}
						})
						
					}).catch((err)=>{
						// console.log(err)
						setTimeout(()=>{
							jixufabu()
						},1500)
					})
				}
			}
			
			function showinfo(){//查看发布的资源
				window.location.href = 'achievements.html'
			}
			function jixufabu(){
				window.location.reload()
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
