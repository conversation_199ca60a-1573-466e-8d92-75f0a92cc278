<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>红色书籍封面美化效果演示</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="css/mobile-base.css">
    <link rel="stylesheet" href="css/learning-styles.css">
    
    <style>
        body {
            background: #f5f5f5;
            padding: 20px;
        }
        
        .demo-section {
            margin-bottom: 30px;
        }
        
        .demo-title {
            font-size: 18px;
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            text-align: center;
        }
        
        .demo-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 15px;
        }
        
        .demo-item {
            background: white;
            border-radius: 8px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .demo-label {
            padding: 10px;
            text-align: center;
            font-size: 12px;
            color: #666;
            background: #f8f9fa;
        }
        
        @media (max-width: 375px) {
            .demo-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <h1 style="text-align: center; color: #c00714; margin-bottom: 30px;">红色书籍封面美化效果演示</h1>
    
    <!-- 基础效果 -->
    <div class="demo-section">
        <div class="demo-title">基础美化效果</div>
        <div class="demo-grid">
            <div class="demo-item">
                <div class="learning-cover redbook-cover" style="height: 120px;">
                    <img src="img/loginbagg.png" alt="默认效果">
                    <div class="learning-type">红色书籍</div>
                </div>
                <div class="demo-label">默认红色渐变背景</div>
            </div>
            
            <div class="demo-item">
                <div class="learning-cover redbook-cover textured" style="height: 120px;">
                    <img src="img/loginbagg.png" alt="纹理效果">
                    <div class="learning-type">红色书籍</div>
                </div>
                <div class="demo-label">纹理背景效果</div>
            </div>
        </div>
    </div>
    
    <!-- 3D和发光效果 -->
    <div class="demo-section">
        <div class="demo-title">3D和发光效果</div>
        <div class="demo-grid">
            <div class="demo-item">
                <div class="learning-cover redbook-cover book-3d" style="height: 120px;">
                    <img src="img/loginbagg.png" alt="3D效果">
                    <div class="learning-type">红色书籍</div>
                </div>
                <div class="demo-label">3D书籍效果</div>
            </div>
            
            <div class="demo-item">
                <div class="learning-cover redbook-cover glowing" style="height: 120px;">
                    <img src="img/loginbagg.png" alt="发光效果">
                    <div class="learning-type">红色书籍</div>
                </div>
                <div class="demo-label">发光效果</div>
            </div>
        </div>
    </div>
    
    <!-- 主题色彩效果 -->
    <div class="demo-section">
        <div class="demo-title">主题色彩效果</div>
        <div class="demo-grid">
            <div class="demo-item">
                <div class="learning-cover redbook-cover style-elegant" style="height: 120px;">
                    <img src="img/loginbagg.png" alt="优雅主题">
                    <div class="learning-type">红色书籍</div>
                </div>
                <div class="demo-label">优雅深色主题</div>
            </div>
            
            <div class="demo-item">
                <div class="learning-cover redbook-cover style-warm" style="height: 120px;">
                    <img src="img/loginbagg.png" alt="温暖主题">
                    <div class="learning-type">红色书籍</div>
                </div>
                <div class="demo-label">温暖金色主题</div>
            </div>
            
            <div class="demo-item">
                <div class="learning-cover redbook-cover style-modern" style="height: 120px;">
                    <img src="img/loginbagg.png" alt="现代主题">
                    <div class="learning-type">红色书籍</div>
                </div>
                <div class="demo-label">现代紫色主题</div>
            </div>
            
            <div class="demo-item">
                <div class="learning-cover redbook-cover style-classic" style="height: 120px;">
                    <img src="img/loginbagg.png" alt="经典主题">
                    <div class="learning-type">红色书籍</div>
                </div>
                <div class="demo-label">经典棕色主题</div>
            </div>
        </div>
    </div>
    
    <!-- 高级效果 -->
    <div class="demo-section">
        <div class="demo-title">高级美化效果</div>
        <div class="demo-grid">
            <div class="demo-item">
                <div class="learning-cover redbook-cover bookshelf" style="height: 120px;">
                    <img src="img/loginbagg.png" alt="书架效果">
                    <div class="learning-type">红色书籍</div>
                </div>
                <div class="demo-label">书架背景效果</div>
            </div>
            
            <div class="demo-item">
                <div class="learning-cover redbook-cover paper-texture" style="height: 120px;">
                    <img src="img/loginbagg.png" alt="纸张纹理">
                    <div class="learning-type">红色书籍</div>
                </div>
                <div class="demo-label">纸张纹理效果</div>
            </div>
        </div>
    </div>
    
    <!-- 组合效果 -->
    <div class="demo-section">
        <div class="demo-title">组合效果演示</div>
        <div class="demo-grid">
            <div class="demo-item">
                <div class="learning-cover redbook-cover book-3d glowing style-elegant" style="height: 120px;">
                    <img src="img/loginbagg.png" alt="组合效果1">
                    <div class="learning-type">红色书籍</div>
                </div>
                <div class="demo-label">3D + 发光 + 优雅主题</div>
            </div>
            
            <div class="demo-item">
                <div class="learning-cover redbook-cover textured paper-texture style-warm" style="height: 120px;">
                    <img src="img/loginbagg.png" alt="组合效果2">
                    <div class="learning-type">红色书籍</div>
                </div>
                <div class="demo-label">纹理 + 纸张 + 温暖主题</div>
            </div>
        </div>
    </div>
    
    <div style="text-align: center; margin-top: 30px; padding: 20px; background: white; border-radius: 8px;">
        <h3 style="color: #c00714; margin-bottom: 10px;">美化方案说明</h3>
        <p style="font-size: 14px; color: #666; line-height: 1.6;">
            针对9:16的红色书籍封面在16:9容器中的显示问题，我们采用了多种美化方案：<br>
            1. 保持图片比例，使用渐变背景填充空白区域<br>
            2. 添加3D效果、发光效果、纹理等视觉增强<br>
            3. 根据书籍类型自动应用不同的主题色彩<br>
            4. 支持多种效果的组合使用
        </p>
    </div>
    
    <div style="text-align: center; margin-top: 20px;">
        <a href="index.html" style="display: inline-block; padding: 12px 24px; background: #c00714; color: white; text-decoration: none; border-radius: 6px;">返回首页</a>
    </div>
</body>
</html>
