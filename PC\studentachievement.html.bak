<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-个人中心</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/wangEditor.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
		<!-- 添加outputLog函数以解决ReferenceError: outputLog is not defined错误 -->
		<script type="text/javascript">
			function outputLog(type, msg, isDoNotCheckOnce) {
				if (type === 'warn') {
					console.warn(msg);
				} else if (type === 'error') {
					console.error(msg);
				} else {
					console.log(msg);
				}
			}
		</script>
		<script src="./js/echarts.js" type="text/javascript" charset="utf-8"></script>
		<style>
			.num {
				color: #cecece !important;
			}

			.num .actinum {
				color: #FFFFFF !important;
			}

			.num label:hover {
				color: #FFFFFF !important;
			}

			/* 筛选列表样式 */
			.selectview {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				gap: 10px;
				padding: 15px;
				background: #f8f8f8;
				border-radius: 4px;
				margin: 0 0 20px 0;
			}

			.selectview select {
				flex: 1;
				min-width: 140px;
				max-width: 180px;
				height: 32px;
				padding: 0 8px;
				border: 1px solid #ddd;
				border-radius: 4px;
				background: #fff;
				color: #333;
				font-size: 14px;
				outline: none;
				cursor: pointer;
				transition: all 0.3s ease;
			}

			.selectview input {
				flex: 1.5;
				min-width: 160px;
				max-width: 200px;
				height: 32px;
				padding: 0 8px;
				border: 1px solid #ddd;
				border-radius: 4px;
				background: #fff;
				color: #333;
				font-size: 14px;
				outline: none;
				transition: all 0.3s ease;
			}

			/* 响应式布局 */
			@media screen and (max-width: 1200px) {
				.selectview {
					gap: 8px;
					padding: 12px;
				}
				
				.selectview select,
				.selectview input {
					min-width: 120px;
					max-width: 160px;
				}
			}

			@media screen and (max-width: 992px) {
				.selectview select,
				.selectview input {
					min-width: calc(33.33% - 8px);
					max-width: none;
				}
			}

			/* 保持其他样式不变 */
			.selectview select:hover,
			.selectview input:hover {
				border-color: #A65D57;
			}

			.selectview select:focus,
			.selectview input:focus {
				border-color: #A65D57;
				box-shadow: 0 0 0 2px rgba(166, 93, 87, 0.1);
			}

			.selectview input::placeholder {
				color: #999;
			}

			/* 标题样式 */
			.btbtbtbtbt {
				font-size: 20px;
				color: #333;
				font-weight: 500;
				margin: 20px 0 15px;
				padding-left: 0;
				border-left: none;
				height: 40px;
				line-height: 40px;
				text-align: center;
			}
		</style>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a class="leftitem activeleftitem">学生成绩统计</a>
						<a href="studentlearning.html" class="leftitem">学习记录统计</a>
						<a href="userexamination2.html" class="leftitem">考试管理</a>
						<a href="learningtasks.html" class="leftitem">学习任务管理</a>
						<a href="achievements.html" class="leftitem">成果展示</a>
						<a href="releaseyourvoice2.html" class="leftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="righttopview2">
						<label class="accccc">学生成绩统计分析</label>
					</div>
					<div class="btbtbtbtbt">学生成绩数据统计图</div>
					<div class="selectview">
						<select id="xuekeselect1" onchange="xuekechange()">
							<option value="0">请选择学科</option>
						</select>
						<input id="stinput1" oninput="getnumlist()" placeholder="请输入试题名称" />
						<select id="years" onchange="yearschange(this)">
						</select>
						<select id="xyselect" onchange="xychange(this)">
						</select>
						<select id="zyselect" onchange="zychange(this)">
						</select>
						<select id="bjselect" onchange="bjchange(this)">
						</select>
					</div>

					<div class="tjt">
						<div id="msg1"
							style="text-align: center;font-size: 0.8333rem;color: #999999;padding-top: 8rem;">请先选择学科
						</div>
						<div id="main" style="display: none;height: 400px;"></div>
					</div>
					<div class="btbtbtbtbt2">学生成绩信息列表<div class="sjjxbtn" onclick="daochu()">Excel导出</div>
					</div>
					<div class="selectbox">
						<div class="selectview nono" style="flex-wrap: wrap;justify-content: flex-start;">
							<select id="xuekeselect2" onchange="getlist()"></select>
							<select id="sjselect2" onchange="getssnewlist()">
							</select>
							<select id="xyselect2" onchange="xychange2(this)">
							</select>
							<select id="zyselect2" onchange="zychange2(this)">
							</select>
							<select style="margin-right: 0;" id="bjselect2" onchange="bjchange2(this)">
							</select>

							<!-- <select id="user1" onchange="user1change(this)">
							</select>
							<select id="user2" onchange="user2change(this)">
							</select> -->

							<div style="width: 100%;margin-top: 0.2604rem;">
								<input id="userxh" oninput="getlist()" placeholder="请输入学号" />
								<input id="userxm" oninput="getlist()" style="margin-left: 0.5208rem" placeholder="请输入姓名" />
							</div>
						</div>

					</div>
					<div class="tabview">
						<div class="tabletopview" id="showpapername">全部试卷</div>
						<div class="tabletoptt">
							<div>序号</div>
							<div>学院</div>
							<div>专业</div>
							<div>班级</div>
							<div>学号</div>
							<div>姓名</div>
							<div>主观题</div>
							<div>客观题</div>
							<div>总成绩</div>
							<div>权重</div>
						</div>
						<div id="paperlist">

						</div>
						<div class="tfyq">
							<div class="fybox" id="fyq">
								<span id="sy">首页</span>
								<span id="syy">上一页</span>
								<div class="num" id="num">
								</div>
								<span id="xyy">下一页</span>
								<span id="wy">尾页</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let userinfo = sessionStorage.getItem("userinfo")

			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getfooterlink()
				//getnumlist()
				getlist()
				getxueyuan()
				getpaperallyears()
				getyearspaper()
				getallxueke()
			})

			function getallxueke() {
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'get',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						let html = '<option value="0">请选择学科</option>'
						res.data.forEach((item) => {
							html += `<option value="${item.id}">${item.name}</option>`
						})
						$("#xuekeselect1").html(html)
						$("#xuekeselect2").html(html)
					}
				})
			}

			function daochu() {
				let dcxyid = ""
				if ($("#xyselect2").val() != '0') {
					dcxyid = $("#xyselect2").val()
				}
				let dczyid = ""
				if ($("#zyselect2").val() != "0") {
					dczyid = $("#zyselect2").val()
				}
				let dcbjid = ""
				if ($("#bjselect2").val() != "0") {
					dcbjid = $("#bjselect2").val()
				}

				let dcsjid = ""
				if ($("#sjselect2").val() != "0") {
					dcsjid = $("#sjselect2").val()
				}
				
				let xkidss = ""
				if($("#xuekeselect2").val() != '0'){
					xkidss = $("#xuekeselect2").val()
				}

				axios({
					method: 'GET',
					url: baseurl + "/paper/export",
					responseType: 'blob', //响应类型
					xsrfHeaderName: 'Authorization',
					params: {
						collegeId: dcxyid, //学院ID
						majorId: dczyid, //专业ID
						classId: dcbjid, //班级ID
						paperId: dcsjid, //试卷ID
						identifier: $("#userxh").val(), //学号
						subjectId: xkidss,
						name: $("#userxm").val()
					},
					headers: {
						'Content-Type': 'application/json',
						"Authorization": sessionStorage.getItem("header")
					},
				}).then(response => {
					let blob = new Blob([response.data], {
						type: 'application/vnd.ms-excel' //文件格式对应文件后缀xls（比如xlsx/dotx等）
					})
					let url = window.URL.createObjectURL(blob)
					let link = document.createElement('a')
					link.style.display = 'none'
					link.href = url
					link.setAttribute('download', "学生成绩信息.xlsx")
					document.body.appendChild(link)
					link.click()
				}).catch(error => {

				})
			}
			let collegeId = null //学院
			let majorId = null //专业
			let classId = null //班级

			let collegeId2 = null //学院2
			let majorId2 = null //专业2
			let classId2 = null //班级2
			let userNo = null //学号
			let ppid = null //试卷ID

			let time1 = null //开始时间
			let time2 = null //结束时间

			let xueyuanlist = null //学院专业班级数据
			function user1change(iii) {
				let xh = $(iii).val()
				$("#user2").val(xh)
				userNo = xh

				pageindex = 1
				getlist()
			}

			function user2change(iii) {
				let xh = $(iii).val()
				$("#user1").val(xh)
				userNo = xh

				pageindex = 1
				getlist()
			}

			function getstudentbyclassid(classid) {
				if (classid) {
					$.ajax({
						url: baseurl + "/student/ClaassStudentlist",
						type: 'GET',
						contentType: "application/json",
						data: {
							className: classid
						},
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						dataType: 'json',
						success: (res) => {
							if (res.code == '200') {
								// console.log(res.data)
								let html = '<option value="0">请选择学号</option>'
								let html2 = '<option value="0">请选择姓名</option>'
								res.data.forEach((item) => {
									html += '<option value="' + item.identifier + '">' + item.identifier +
										'</option>'
									html2 += '<option value="' + item.identifier + '">' + item.name +
										'</option>'
								})
								$("#user1").html(html)
								$("#user2").html(html2)
							}
						}
					})
				} else {
					$("#user1").html('<option value="0">请选择学号</option>')
					$("#user2").html('<option value="0">请选择姓名</option>')
				}

			}
			let allpapersssss = null

			function getyearspaper() {
				$.ajax({
					url: baseurl + "/paper/listNowYear",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data)
							allpapersssss = res.data
							let html = '<option value="0">全部试卷</option>'
							res.data.forEach((item) => {
								html += '<option value="' + item.id + '">' + item.name + '</option>'
							})
							$("#sjselect2").html(html)
						}
					}
				})
			}

			function getpaperallyears() { //获取所有年份
				let date = new Date()
				let nowyears = date.getFullYear()
				let smallYears = nowyears - 5
				let Years = nowyears - smallYears
				let array = []
				for (let i = 0; i <= Years; i++) {
					let yy = nowyears--

					array.push(yy + '-下学期')
					array.push(yy + '-上学期')
				}
				let yearstr = '<option value="0">全部年份</option>'
				array.map((item) => {
					yearstr += '<option value="' + item + '">' + item + '</option>'
				})
				$("#years").html(yearstr)
			}

			function yearschange(select) {
				let str = $(select).val()
				let strs = str.split('-')
				if (str == '0') {
					time1 = null
					time2 = null
				} else {
					if (strs[1] == '上学期') {
						//3-1  8-1
						time1 = strs[0] + '-3-2'
						time2 = strs[0] + '-8-1'
					} else {
						// 9- 1  2-29
						time1 = strs[0] + '-9-1'
						time2 = (parseInt(strs[0]) + 1) + '-3-1'
					}
				}

				getnumlist()
			}

			function getxueyuan() { //获取学院专业班级  学科树
				$.ajax({
					url: baseurl + "/college/major",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							xueyuanlist = res.data
							// console.log(xueyuanlist)
							let xyhtml = '<option value="0">请选择学院</option>'
							xueyuanlist.forEach((item) => {
								xyhtml += '<option value="' + item.id + '">' + item.name + '</option>'
							})
							$("#xyselect").html(xyhtml)
							$("#xyselect2").html(xyhtml)
							fleshxueyuan()
							fleshxueyuan2()
						}
					}
				})
			}

			function xychange(xy) { //当学院的list选择时
				if ($(xy).val() == '0') { //当学院选择了空 则 专业班级为空
					collegeId = null
					majorId = null
					classId = null
				} else { //当选择了有值学院则 刷新list  专业和班级为空
						collegeId = $(xy).val()
						majorId = null
						classId = null
				}
				getnumlist()
				fleshxueyuan()
			}

			function xychange2(xy) {
				if ($(xy).val() == '0') { //当学院选择了空 则 专业班级都为空
					collegeId2 = null
					majorId2 = null
					classId2 = null
				} else { //当选择了有值学院则 刷新list  专业和班级为空
					collegeId2 = $(xy).val()
					majorId2 = null
					classId2 = null
				}

				fleshxueyuan2()
			}

			function zychange(zy) { //当专业的list选择时
				if ($(zy).val() == '0') { //当专业选择了空 则 班级为空
					majorId = null
					classId = null
				} else { //当选择了有值专业则 刷新list  班级为空
					majorId = $(zy).val()
					classId = null
				}
				getnumlist()
				fleshxueyuan()
			}

			function zychange2(zy) { //当专业的list选择时
				if ($(zy).val() == '0') { //当专业选择了空 则 班级为空
					majorId2 = null
					classId2 = null
				} else { //选择了有值专业则 刷新list  班级为空
					majorId2 = $(zy).val()
					classId2 = null
				}
				fleshxueyuan2()
			}

			function bjchange(bj) { //当班级选择时
				if ($(bj).val() == '0') {
					classId = null
				} else {
					classId = $(bj).val()
				}
				getnumlist()
			}

			function bjchange2(bj) { //当班级选择时
				if ($(bj).val() == '0') {
					classId2 = null
				} else {
					classId2 = $(bj).val()
				}
				fleshxueyuan2()
				getstudentbyclassid(classId2)
			}

			function fleshxueyuan() { //刷新学院 专业班级数据
				if (collegeId) {
					//有学院id
					if (majorId) {
						//有学院ID  有专业ID 则 循环班级的list
						let bjhtml = '<option value="0">请选择班级</option>'
						xueyuanlist.forEach((item) => { //循环学院
							if (item.id == collegeId) { //判断学院ID
								item.children.forEach((item1) => { //循环专业
									if (item1.id == majorId) { //判断专业ID
										item1.children.forEach((item2) => {
											bjhtml += '<option value="' + item2.id + '">' + item2.name +
												'</option>'
										})
									}
								})
							}
						})
						$("#bjselect").html(bjhtml)
					} else {
						//有学院ID 没有专业ID  则班级全部是空  循环专业list
						classId = null //班级ID 都为空 select为空
						$("#bjselect").html('<option value="0">请选择班级</option>')
						//循环班级的list
						let zyhtml = '<option value="0">请选择专业</option>'
						xueyuanlist.forEach((item) => {
							if (item.id == collegeId) { //判断学院的ID和当前选择的学院ID是否相同
								//如果相同 则循环 当前item下的children
								item.children.forEach((item1) => {
									zyhtml += '<option value="' + item1.id + '">' + item1.name + '</option>'
								})
							}
						})
						$("#zyselect").html(zyhtml)
					}
				} else {
					//没有学院ID
					majorId = null //专业ID 都为空 select为空
					$("#zyselect").html('<option value="0">请选择专业</option>')
					classId = null //班级ID 都为空 select为空
					$("#bjselect").html('<option value="0">请选择班级</option>')
				}
			}


			function fleshxueyuan2() { //刷新学院 专业班级数据
				$("#user1").html('<option value="0">请选择学号</option>')
				$("#user2").html('<option value="0">请选择姓名</option>')
				userNo = null
				if (collegeId2) {
					//有学院id
					if (majorId2) {
						//有学院ID  有专业ID 则 循环班级的list
						if (!classId2) {
							let bjhtml = '<option value="0">请选择班级</option>'
							xueyuanlist.forEach((item) => { //循环学院
								if (item.id == collegeId2) { //判断学院ID
									item.children.forEach((item1) => { //循环专业
										if (item1.id == majorId2) { //判断专业ID
											item1.children.forEach((item2) => {
												bjhtml += '<option value="' + item2.id + '">' + item2
													.name +
													'</option>'
											})
										}
									})
								}
							})
							$("#bjselect2").html(bjhtml)
						}
					} else {
						//有学院ID 没有专业ID  则班级全部是空  循环专业list
						classId2 = null //班级ID 都为空 select为空
						$("#bjselect2").html('<option value="0">请选择班级</option>')
						//循环班级的list
						let zyhtml = '<option value="0">请选择专业</option>'
						xueyuanlist.forEach((item) => {
							if (item.id == collegeId2) { //判断学院的ID和当前选择的学院ID是否相同
								//如果相同 则循环 当前item下的children
								item.children.forEach((item1) => {
									zyhtml += '<option value="' + item1.id + '">' + item1.name + '</option>'
								})
							}
						})
						$("#zyselect2").html(zyhtml)
					}
				} else {
					//没有学院ID
					majorId2 = null //专业ID 都为空 select为空
					$("#zyselect2").html('<option value="0">请选择专业</option>')
					classId2 = null //班级ID 都为空 select为空
					$("#bjselect2").html('<option value="0">请选择班级</option>')
					userNo = null //学生的列表为空  学号也为空
					$("#user1").html('<option value="0">请选择学号</option>')
					$("#user2").html('<option value="0">请选择姓名</option>')
				}

				// console.log(collegeId2)
				pageindex = 1
				getlist()
			}
			//<select id="user1" onchange="user1change(this)">
			//</select>
			//<select id="user2" onchange="user2change(this)">
			//</select>

			function getssnewlist() {
				pageindex = 1
				getlist()
			}
			let pagesize = 10
			let pageindex = 1
			let pages = 1

			function getlist() {
				let ppids = $("#sjselect2").val()
				if (ppids == '0') {
					ppids = null
					$("#showpapername").html("全部试卷")
				}

				if (allpapersssss) {
					allpapersssss.forEach((item) => {
						if (item.id == ppids) {
							$("#showpapername").html(item.name)
						}
					})
				}
				let ssxuekeid = ""
				let xsname = $("#userxm").val()
				let xuehao = $("#userxh").val()
				
				if($("#xuekeselect2").val() != '0'){
					ssxuekeid = $("#xuekeselect2").val()
				}

				$.ajax({
					url: baseurl + "/paper/answered",
					type: 'GET',
					contentType: "application/json",
					data: {
						subjectId: ssxuekeid, //学科
						name: xsname, //学生姓名 模糊查询
						pageNum: pageindex,
						pageSize: pagesize,
						isMark: 1,
						collegeId: collegeId2, //学院2
						majorId: majorId2, //专业2
						classId: classId2, //班级2
						identifier: xuehao, //学号
						paperId: ppids //试卷ID
					},
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data)
							pages = res.data.pages
							let htmls = ''
							res.data.list.map((item, index) => {
								htmls += `<div class="tabletoptt nonos">
									<div>${index+1}</div>
									<div>${item.student != null ? item.student.collegeName : ""}</div>
									<div>${item.student != null ? item.student.majorName : ""}</div>
									<div>${item.student != null ? item.student.className1 : ""}</div>
									<div>${item.student != null ? item.student.userAuth.identifier : ""}</div>
									<div>${item.student != null ? item.student.name : ""}</div>
									<div>${parseInt(item.zhuguanScore)}分</div>
									<div>${parseInt(item.keguanTotalScore)}分</div>
									<div>${parseInt(item.score)+'分'}</div>
									<div>${item.cmsTestPaper.theWeight}</div>
								</div>`
							})
							$("#paperlist").html(htmls)
							if (pages > 1) {
								let numhtml = ""
								for (let a = 1; a <= pages; a++) {
									if (pageindex == a) {
										numhtml += '<label class="actinum" onclick="getnewlist(' + a + ')">' + a +
											'</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
						}
					}
				})
			}

			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是最后一页了！")
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！")
					}
				} else {
					pageindex = index
					getlist()
				}
			}

			function xuekechange() {
				if ($("#xuekeselect1").val() != '0') {
					getnumlist()
				} else {
					$("#msg1").show()
					$("#main").hide()
				}
			}

			function getnumlist() {
				$("#msg1").hide()
				$("#main").show()

				let xkid1 = ""
				let stname1 = ""
				if ($("#stinput1").val()) {
					stname1 = $("#stinput1").val()
				}
				if ($("#xuekeselect1").val() != '0') {
					xkid1 = $("#xuekeselect1").val()
				}

				// 构建请求参数对象
				const params = new URLSearchParams();
				
				// 只添加有值的参数
				if (xkid1) params.append('projectId', xkid1);
				if (stname1) params.append('paperName', stname1);
				if (collegeId) params.append('collegeId', collegeId);
				if (majorId) params.append('majorId', majorId);
				if (classId) params.append('classId', classId);
				if (time1) params.append('time1', time1);
				if (time2) params.append('time2', time2);

				// 并行请求两个接口获取数据
				Promise.all([
					// 获取统计数据
					$.ajax({
						url: baseurl + "/stat/studentsore?" + params.toString(),
						type: 'GET',
						headers: {
							"Authorization": sessionStorage.getItem("header"),
							"Content-Type": "application/json"
						}
					}).catch(error => {
						console.warn("统计数据获取失败:", error);
						return { code: 'error', data: [] };
					}),
					// 获取详细成绩数据
					$.ajax({
						url: baseurl + "/paper/answered",
						type: 'GET',
						data: {
							subjectId: xkid1 || undefined,
							name: "",
							pageNum: 1,
							pageSize: 1000,
							isMark: 1,
							collegeId: collegeId || undefined,
							majorId: majorId || undefined,
							classId: classId || undefined,
							identifier: "",
							paperId: ""
						},
						headers: {
							"Authorization": sessionStorage.getItem("header"),
							"Content-Type": "application/json"
						}
					}).catch(error => {
						console.warn("成绩数据获取失败:", error);
						return { code: 'error', data: { list: [] } };
					})
				]).then(([statRes, scoreRes]) => {
					if (scoreRes.code == '200') {
						const scoreStats = processScoreStats(scoreRes.data.list);
						showmain(scoreStats);
					} else {
						$("#msg1").show().text("数据获取失败，请稍后重试");
						$("#main").hide();
					}
				}).catch(error => {
					console.error("数据处理失败:", error);
					$("#msg1").show().text("数据处理失败，请稍后重试");
					$("#main").hide();
				});
			}

			// 修改错误提示的样式
			$("#msg1").css({
				"color": "#999999",
				"font-size": "0.8333rem",
				"text-align": "center",
				"padding-top": "8rem"
			});

			// 处理成绩统计数据
			function processScoreStats(scoreList) {
				if (!scoreList || scoreList.length === 0) {
					return {
						stats: [],
						validCounts: {
							subjective: { total: 0 },
							objective: { total: 0 },
							total: { total: 0 }
						},
						scoreList: [] // 保存原始数据用于获取班级信息
					};
				}

				// 过滤有效分数
				const validScores = {
					subjective: scoreList.filter(item => !isNaN(parseFloat(item.zhuguanScore))),
					objective: scoreList.filter(item => !isNaN(parseFloat(item.keguanTotalScore))),
					total: scoreList.filter(item => !isNaN(parseFloat(item.score)))
				};

				// 计算各项统计数据
				const stats = [
					{
						name: '主观题',
						type: 'bar',
						data: [
							{value: (validScores.subjective.reduce((sum, item) => sum + parseFloat(item.zhuguanScore), 0) / validScores.subjective.length).toFixed(1), name: '平均分'},
							{value: (validScores.subjective.filter(item => parseFloat(item.zhuguanScore) >= 60).length / validScores.subjective.length * 100).toFixed(1), name: '及格率'},
							{value: (Math.max(...validScores.subjective.map(item => parseFloat(item.zhuguanScore))) - Math.min(...validScores.subjective.map(item => parseFloat(item.zhuguanScore)))).toFixed(1), name: '分数跨度'},
							{value: Math.max(...validScores.subjective.map(item => parseFloat(item.zhuguanScore))).toFixed(1), name: '最高分'},
							{value: Math.min(...validScores.subjective.map(item => parseFloat(item.zhuguanScore))).toFixed(1), name: '最低分'}
						]
					},
					{
						name: '客观题',
						type: 'bar',
						data: [
							{value: (validScores.objective.reduce((sum, item) => sum + parseFloat(item.keguanTotalScore), 0) / validScores.objective.length).toFixed(1), name: '平均分'},
							{value: (validScores.objective.filter(item => parseFloat(item.keguanTotalScore) >= 60).length / validScores.objective.length * 100).toFixed(1), name: '及格率'},
							{value: (Math.max(...validScores.objective.map(item => parseFloat(item.keguanTotalScore))) - Math.min(...validScores.objective.map(item => parseFloat(item.keguanTotalScore)))).toFixed(1), name: '分数跨度'},
							{value: Math.max(...validScores.objective.map(item => parseFloat(item.keguanTotalScore))).toFixed(1), name: '最高分'},
							{value: Math.min(...validScores.objective.map(item => parseFloat(item.keguanTotalScore))).toFixed(1), name: '最低分'}
						]
					},
					{
						name: '总分',
						type: 'bar',
						data: [
							{value: (validScores.total.reduce((sum, item) => sum + parseFloat(item.score), 0) / validScores.total.length).toFixed(1), name: '平均分'},
							{value: (validScores.total.filter(item => parseFloat(item.score) >= 60).length / validScores.total.length * 100).toFixed(1), name: '及格率'},
							{value: (Math.max(...validScores.total.map(item => parseFloat(item.score))) - Math.min(...validScores.total.map(item => parseFloat(item.score)))).toFixed(1), name: '分数跨度'},
							{value: Math.max(...validScores.total.map(item => parseFloat(item.score))).toFixed(1), name: '最高分'},
							{value: Math.min(...validScores.total.map(item => parseFloat(item.score))).toFixed(1), name: '最低分'}
						]
					}
				];

				return {
					stats,
					validCounts: {
						subjective: { total: validScores.subjective.length },
						objective: { total: validScores.objective.length },
						total: { total: validScores.total.length }
					},
					scoreList: scoreList // 保存原始数据
				};
			}

			function showmain(statsData) {
				// 销毁已存在的实例
				let existingChart = echarts.getInstanceByDom(document.getElementById('main'));
				if (existingChart) {
					existingChart.dispose();
				}
				
				// 创建新实例
				var myChart = echarts.init(document.getElementById('main'));
				
				// 获取当前选中的班级名称
				let className = "全部班级";
				if (classId) {
					xueyuanlist.forEach(college => {
						if (college.id === collegeId) {
							college.children.forEach(major => {
								if (major.id === majorId) {
									major.children.forEach(cls => {
										if (cls.id === classId) {
											className = `${college.name}-${major.name}-${cls.name}`;
										}
									});
								}
							});
						}
					});
				} else if (majorId) {
					xueyuanlist.forEach(college => {
						if (college.id === collegeId) {
							college.children.forEach(major => {
								if (major.id === majorId) {
									className = `${college.name}-${major.name}-全部班级`;
								}
							});
						}
					});
				} else if (collegeId) {
					xueyuanlist.forEach(college => {
						if (college.id === collegeId) {
							className = `${college.name}-全部专业`;
						}
					});
				}
				
				// 定义棕红色系配色方案
				const colors = [
					'#A65D57', // 主观题：深棕红色
					'#D2691E', // 客观题：巧克力色
					'#CD5C5C'  // 总分：印度红色
				];
				
				// 获取班级信息
				function getClassInfo(scoreList) {
					if (!scoreList || scoreList.length === 0) return '';
					
					const classMap = new Map();
					scoreList.forEach(item => {
						if (item.student && item.student.className1) {
							classMap.set(item.student.className1, true);
						}
					});
					
					return Array.from(classMap.keys()).join('、');
				}

				let option = {
					title: {
						text: className + ' - 成绩统计',
						left: 'center',
						top: '10px',
						textStyle: {
							color: '#333',
							fontSize: 16,
							fontWeight: 'normal'
						}
					},
					color: colors,
					tooltip: {
						trigger: 'axis',
						axisPointer: {
							type: 'shadow'
						},
						formatter: function(params) {
							let result = params[0].name + '<br/>';
							params.forEach(param => {
								let value = param.value;
								let suffix = param.name === '及格率' ? '%' : 
										   (param.name !== '分数跨度' ? '分' : '');
								result += param.marker + param.seriesName + ': ' + value + suffix + '<br/>';
							});
							return result;
						}
					},
					legend: {
						data: ['主观题', '客观题', '总分'],
						textStyle: {
							color: '#666'
						},
						top: '40px'
					},
					grid: {
						left: '3%',
						right: '4%',
						bottom: '80px',
						top: '80px',
						containLabel: true
					},
					xAxis: {
						type: 'category',
						data: ['平均分', '及格率', '分数跨度', '最高分', '最低分'],
							axisLabel: {
								interval: 0,
								rotate: 0,
								color: '#666'
							}
					},
					yAxis: {
						type: 'value',
						axisLabel: {
							formatter: function(value) {
								return value;
							},
							color: '#666'
						}
					},
					series: statsData.stats.map((item, index) => ({
						...item,
						itemStyle: {
							color: colors[index % colors.length],
							emphasis: {
								shadowBlur: 10,
								shadowOffsetX: 0,
								shadowColor: 'rgba(0, 0, 0, 0.5)'
							}
						},
						barGap: '10%',
						barCategoryGap: '20%'
					}))
				};
				
				myChart.setOption(option);

				// 添加底部信息显示
				const container = document.getElementById('main');
				let infoDiv = document.getElementById('chart-info');
				if (!infoDiv) {
					infoDiv = document.createElement('div');
					infoDiv.id = 'chart-info';
					container.appendChild(infoDiv);
				}

				// 设置底部信息的样式
				infoDiv.style.cssText = `
					position: absolute;
					bottom: 10px;
					left: 0;
					width: 100%;
					text-align: center;
					display: flex;
					justify-content: center;
					align-items: center;
					gap: 20px;
					font-size: 14px;
					padding: 10px 0;
				`;

				// 更新底部信息内容
				infoDiv.innerHTML = `
					<span style="color: #A65D57;">有效人数: ${statsData.validCounts.total.total}人</span>
					<span style="color: #666;">|</span>
					<span style="color: #D2691E;">参与班级: ${getClassInfo(statsData.scoreList)}</span>
				`;
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 						.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
					sessionStorage.clear()
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					cocoMessage.error(1000, "已退出登录！")
			}

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '年' + MM + '月' + d + '日 ' + h + ':' + m
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
		<script>
			// 添加CSS样式
			const style = document.createElement('style');
			style.textContent = `
				#main {
					position: relative;
				}
				#chart-info span {
					display: inline-block;
					vertical-align: middle;
					line-height: 1.5;
				}
			`;
			document.head.appendChild(style);
		</script>
	</body>
</html>
