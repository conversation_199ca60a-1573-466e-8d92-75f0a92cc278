<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-心声社区</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/community.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
		<style>
			/* 美化列表样式 */
			.table .tr {
				transition: all 0.3s ease;
				border-radius: 4px;
				margin-bottom: 2px;
			}
			.table .tr:hover {
				background-color: #f5f5f5;
				transform: translateY(-2px);
				box-shadow: 0 2px 8px rgba(0,0,0,0.1);
			}
			.table .tr:first-child:hover {
				transform: none;
				box-shadow: none;
			}
			.bt {
				color: #333;
				font-weight: 500;
			}
			.tr:hover .bt {
				color: #c00714;
			}
			.fybox {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 15px 0;
			}
			.fybox span, .fybox label {
				margin: 0 5px;
				padding: 6px 12px;
				border-radius: 4px;
				cursor: pointer;
				transition: all 0.3s ease;
				border: 1px solid #e1e1e1;
			}
			.fybox span:hover, .fybox label:hover {
				background-color: #f5f5f5;
			}
			.actinum {
				background-color: #c00714 !important;
				color: white !important;
				border-color: #c00714 !important;
			}
			/* 当前页信息样式 */
			.page-info {
				color: #666;
				font-size: 0.8rem;
				margin-top: 10px;
				text-align: center;
			}
			/* 右侧热门话题样式美化 */
			.boxitem {
				transition: all 0.3s ease;
				padding: 5px 0;
			}
			.boxitem:hover {
				background-color: #f9f9f9;
			}
			.boxitem:hover .itemnr {
				color: #c00714;
			}
			/* 返回按钮样式 */
			.back-btn {
				position: absolute;
				top: 20px;
				right: 20px;
				background: #c00714;
				color: white;
				border: none;
				padding: 8px 16px;
				border-radius: 20px;
				cursor: pointer;
				font-size: 14px;
				transition: all 0.3s ease;
				display: flex;
				align-items: center;
				gap: 5px;
				box-shadow: 0 2px 4px rgba(192, 7, 20, 0.3);
			}
			.back-btn:hover {
				background: #a00610;
				transform: translateY(-1px);
				box-shadow: 0 4px 8px rgba(192, 7, 20, 0.4);
			}
			.back-btn::before {
				content: "←";
				font-weight: bold;
			}
			/* 确保标题区域有相对定位 */
			.sstitle {
				position: relative;
			}
		</style>
	</head>
	<body class="index" onbeforeunload="tiaozhuan()">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="content2">
			<div class="toptop">
				<label style="cursor: pointer;" onclick="window.history.back()">心声社区</label>
				<img src="img/jtright.png" />
				<label class="accc" id="flname"></label>
			</div>
			<div class="newsbox">
				<div class="newboxleft">
					<div class="sqtopsss">
						<div>
							<label>
								<img style="width: 0.9375rem;" src="img/ztico.png" />
								<span id="zt"></span>
							</label>
							<label>
								<img style="width: 1.041666rem;" src="img/flsq.png" />
								<span id="fl"></span>
							</label>
							<label>
								<img style="width: 0.9375rem;" src="img/zzsq.png" />
								<span id="zz"></span>
							</label>
						</div>
						<div>
							<label>
								<img style="width: 0.9375rem;" src="img/sjsq.png" />
								<span id="sj"></span>
							</label>
							<label>
								<img style="width: 1.145833rem;" src="img/yjsq.png" />
								<span id="ll"></span>
							</label>
							<label>
								<img style="width: 0.885416rem;" src="img/dz.png" />
								<span id="dz"></span>
							</label>
						</div>
					</div>
					<div class="sstitle" id="titles"></div>
					<button class="back-btn" onclick="window.history.back()">返回上页</button>
					<div class="nr" id="nr">
					</div>
					<div class="dzbox">
						<div id="dz1" class="mz mz1">
							<div onclick="dzsubmit()">
								<img src="img/dzno.png" />
							</div>
							<div>点个赞，鼓励一下</div>
						</div>
						<div id="dz2" class="mz mz2">
							<div>
								<img src="img/dzyes.png" />
							</div>
							<div id="dznum"></div>
						</div>
					</div>
				</div>
				<div class="newsright">
					<div class="border"></div>
					<div class="newtitles1">
						热门话题
					</div>
					<div class="boxxx" id="rmht">

					</div>
					<div class="bbbb"></div>
					<div class="border"></div>
					<div class="newtitles1">
						最新话题
					</div>
					<div class="boxxx" id="zxht">

					</div>
				</div>
			</div>
		</div>

		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
					getclass('community.html')
					getinfo()
					getfooterlink()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
			})
			let time1 = Date.now()
			let classid = null

			function tiaozhuan() {
				if (JSON.parse(userinfo).roleName == '学生') {
					let time2 = Date.now()
					let value = time2 - time1
					var days = parseInt(value / (1000 * 60 * 60 * 24))
					var hours = parseInt((value % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
					var minutes = parseInt((value % (1000 * 60 * 60)) / (1000 * 60))
					var seconds = Math.floor((value % (1000 * 60)) / 1000)
					let json = {
						infoId: getUrlParam('id'), //信息id
						categoryId: classid, //所属分类id
						totalInfo: "1", //总时长，总页数
						positioning: days + "天" + hours + "时" + minutes + "分" + seconds + "秒", //学习了多久，多少页    
						progress: "", //进度 百分比
						learningTime: value,
						type: '心声社区'
					}
					window.localStorage.setItem("jilu", JSON.stringify(json))
				}
			}

			function getlist2(id) { //最新话题
				let params = "?pageNum=1&pageSize=5&id=" + id + "&sort=dispaly_time&title="
				$.ajax({
					url: baseurl + "/web/xspostByCategoryId" + params,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.list.map((item) => {
								html += '<div onclick="ininfo(this)" data-id="' + item.id +
									'" class="boxitem"><div class="itemnr">' + item.title +
									'</div><div class="itemsj">' + setDate2(item.createdAt) + '</div></div>'
							})
							$("#zxht").html(html)
						}
					}
				})
			}

			function getlist3(id) { //最热话题
				let params = "?pageNum=1&pageSize=5&id=" + id + "&sort=click_count&title="
				$.ajax({
					url: baseurl + "/web/xspostByCategoryId" + params,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.list.map((item) => {
								html += '<div onclick="ininfo(this)" data-id="' + item.id +
									'" class="boxitem"><div class="itemnr">' + item.title +
									'</div><div class="itemsj">' + setDate2(item.createdAt) + '</div></div>'
							})
							$("#rmht").html(html)
						}
					}
				})
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
			function clicknum(id){
				$.ajax({
					url: baseurl + "/posts/click/"+id,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
					}
				})
			}
			function dzsubmit(item) {
				$("#dz1").hide()
				$("#dz2").show()
				let json = {
					postId: getUrlParam('id')
				}
				$.ajax({
					url: baseurl + "/cmsgive/add",
					type: 'POST',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: JSON.stringify(json),
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let num = parseInt($("#dz").html()) + 1
							$("#dz").html(num)
							$("#dznum").html(num)
						}
					}
				})
			}

			function getinfo() {
				$.ajax({
					url: baseurl + "/posts/" + getUrlParam('id'),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data.isGiveLike)
							if(res.data.isGiveLike==1){
								$("#dz1").hide()
								$("#dz2").show()
							}
							classid = res.data.cmsCategoryList[0].id
							$("#flname").html(res.data.themename)
							$("#fl").html(res.data.cmsCategoryList[0].name)
							$("#zt").html(res.data.themename)
							$("#zz").html(res.data.author)
							$("#sj").html(setDate(res.data.createdAt))
							$("#ll").html(res.data.clickCount)
							$("#dz").html(res.data.giveLike)
							$("#dznum").html(res.data.giveLike)
							$("#nr").html(res.data.content)
							$("#titles").html(res.data.title)
							$("video").parent().attr("style", "text-indent: 0;")
							getlist2(res.data.cmsCategoryList[0].id)
							getlist3(res.data.cmsCategoryList[0].id)
							clicknum(getUrlParam('id'))
						}
					}
				})
			}

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '年' + MM + '月' + d + '日 ' + h + ':' + m
			}

			function ininfo(item) {
				window.location.href = "communitydetail.html?id=" + $(item).attr("data-id")
			}

			function setDate2(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					if (res.data[i].id == getUrlParam('id') || res.data[i].redirectUrl ==
			// 						'community.html') {
			// 						classdate = res.data[i]
			// 						html += '<a class="menuitem active" href="' + res.data[i].redirectUrl + '?id=' +
			// 							res
			// 							.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					} else {
			// 						html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 							.data[
			// 								i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					}
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
