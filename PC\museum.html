<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-医史博物馆</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/museum.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="bodyview">
			<img class="topright" src="img/bwgrighttop.png" />
			<img class="leftbottom" src="img/bwgleftbottom.png" />
			<div class="museumbox" id="bwglist">


			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let classid = null
			var mySwiper = new Swiper('.museumboxitembottoml .swiper', {
				autoplay: false,
				loop: true,
				pagination: {
					el: '.museumboxitembottoml .swiper-pagination',
					clickable: true
				}
			})
let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
					if(window.localStorage.getItem("jilu")){
						$.ajax({
							url: baseurl + "/study/record/add",
							type: 'post',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: window.localStorage.getItem("jilu"),
							dataType: 'json',
							success: (res) => {
								window.localStorage.clear()
							}
						})
					}
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				getclass()
				getclassid()
				getfooterlink()
			})

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			function getclassid() {
				$.ajax({
					url: baseurl + "/web/museum",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							classid = res.data[0].id
							getlist()
						}
					}
				})
			}

			function getlist() {
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: 1,
						pageSize: 9999,
						categoryId: classid
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							res.data.list.map((item, index) => {
								let html = '<div class="museumboxitem">' +
									'<div class="museumboxitemtop">' +
									'<div class="museumboxitemtopl">' +
									'<img src="img/title.png" />' +
									'<label>' + item.title + '</label>' +
									'</div>' +
									'<div class="museumboxitemtopr">' +
									'<a onclick="inqj(this)" data-id="' + item.redirectUrl +
									'" class="inqj"><img src="img/qj.png" />进入学习</a>' +
									'<div class="ljxq" onclick="inxq(this)" data-id="' + item.id +
									'">了解详情</div>' +
									'</div>' +
									'</div>' +
									'<div class="borderbag"></div>' +
									'<div class="museumboxitembottom">' +
									'<div class="museumboxitembottoml">' +
									'<div class="swiper" id="swiper' + index + '">' +
									'<div class="swiper-wrapper">'
								item.thumbPath.map((item) => {
									html += '<div class="swiper-slide">' +
										'<img src="' + baseurl + item + '" />' +
										'</div>'
								})
								html += '</div>' +
									'</div>' +
									'<div class="swiper-pagination" id="swiper-pagination' + index +
									'"></div>' +
									'</div>' +
									'<div class="museumboxitembottomr">' + item.postName + '</div>' +
									'</div>' +
									'</div>'
								$("#bwglist").append(html)
								var mySwiper = new Swiper('#swiper' + index, {
									autoplay: true,
									loop: true,
									pagination: {
										el: '#swiper-pagination' + index,
										clickable: true
									}
								})
								mySwiper.el.onmouseover = function() {
									mySwiper.autoplay.stop();
								}
								mySwiper.el.onmouseout = function() {
									mySwiper.autoplay.start();
								}
							})
						}
					}
				})
			}
			function inqj(item){				if(userinfo){					window.open($(item).attr("data-id"), "_blank")				}else{					window.open('https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html', "_blank")				}			}
			function inxq(item) {
				window.location.href = 'museumcontent.html?id=' + $(item).attr("data-id")
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					if (res.data[i].id == getUrlParam('id') || res.data[i].redirectUrl ==
			// 						'museum.html') {
			// 						classdate = res.data[i]
			// 						html += '<a class="menuitem active" href="' + res.data[i].redirectUrl + '?id=' +
			// 							res
			// 							.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					} else {
			// 						html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 							.data[
			// 								i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					}
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
