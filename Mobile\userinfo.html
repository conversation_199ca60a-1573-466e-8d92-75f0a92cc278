<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>用户信息 - 思政一体化平台</title>

    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
        }

        .loading-overlay {
            text-align: center;
            padding: 40px 20px;
            background: white;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
            max-width: 300px;
            width: 100%;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #f3f3f3;
            border-top: 4px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 20px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .message {
            font-size: 16px;
            color: #333;
            margin-bottom: 10px;
        }

        .success {
            color: #4CAF50;
        }

        .error {
            color: #f44336;
        }

        .countdown {
            font-size: 14px;
            color: #666;
        }
    </style>

    <!-- JavaScript文件 -->
    <script src="../PC/js/jquery-3.2.1.min.js"></script>
</head>
<body class="mobile-userinfo">
    <!-- 加载动画 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <p class="message">正在处理登录信息...</p>
    </div>

    <script>
        // 配置baseurl
        const baseurl = 'https://szjx.sntcm.edu.cn';

        // 获取URL参数
        function getUrlParam(name) {
            try {
                const reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)");
                const r = window.location.search.substr(1).match(reg);
                if (r != null) return decodeURIComponent(r[2]);
                return null;
            } catch (e) {
                console.error('获取URL参数失败:', e);
                return null;
            }
        }

        // 更新显示内容
        function updateDisplay(html) {
            const overlay = document.getElementById('loadingOverlay');
            if (overlay) {
                overlay.innerHTML = html;
            }
        }

        // 显示错误信息
        function showError(message, countdown = 3) {
            updateDisplay(`
                <div style="text-align: center;">
                    <p class="message error">${message}</p>
                    <p class="countdown">${countdown}秒后自动跳转到首页...</p>
                </div>
            `);

            setTimeout(() => {
                window.location.href = 'index.html';
            }, countdown * 1000);
        }

        // 显示成功信息
        function showSuccess(message, countdown = 2) {
            updateDisplay(`
                <div style="text-align: center;">
                    <div class="loading-spinner"></div>
                    <p class="message success">${message}</p>
                </div>
            `);

            setTimeout(() => {
                window.location.href = 'index.html';
            }, countdown * 1000);
        }

        // 页面加载完成后处理登录
        document.addEventListener('DOMContentLoaded', function() {
            try {
                const ticket = getUrlParam('ticket');

                if (ticket) {
                    // 有ticket参数，进行CAS登录
                    const json = {
                        ticket: ticket,
                        service: window.location.href.split('?')[0]
                    };

                    // 检查jQuery是否加载
                    if (typeof $ === 'undefined') {
                        showError('系统加载失败，请稍后重试');
                        return;
                    }

                    $.ajax({
                        url: baseurl + "/student/caslogin",
                        type: 'POST',
                        data: JSON.stringify(json),
                        contentType: "application/json",
                        dataType: 'json',
                        timeout: 10000, // 10秒超时
                        success: (res) => {
                            try {
                                if (res && res.code == "200" && res.data) {
                                    // 储存token和用户信息
                                    sessionStorage.setItem('header', res.data.scheme + res.data.token);
                                    sessionStorage.setItem('userinfo', JSON.stringify(res.data.student));

                                    showSuccess('登录成功！正在跳转...', 2);
                                } else {
                                    showError('登录失败：' + (res.message || '未知错误'));
                                }
                            } catch (e) {
                                console.error('处理登录响应失败:', e);
                                showError('登录处理失败，请稍后重试');
                            }
                        },
                        error: (xhr, status, error) => {
                            console.error('CAS登录失败:', { xhr, status, error });
                            let errorMessage = '网络错误，登录失败';

                            if (status === 'timeout') {
                                errorMessage = '请求超时，请检查网络连接';
                            } else if (xhr.status === 404) {
                                errorMessage = '登录服务不可用';
                            } else if (xhr.status === 500) {
                                errorMessage = '服务器内部错误';
                            }

                            showError(errorMessage);
                        }
                    });
                } else {
                    // 没有ticket参数，检查是否已登录
                    const userinfo = sessionStorage.getItem("userinfo");
                    if (userinfo) {
                        // 已登录，跳转到个人中心
                        showSuccess('已登录，正在跳转...', 1);
                        setTimeout(() => {
                            window.location.href = 'pages/profile.html';
                        }, 1000);
                    } else {
                        // 未登录，跳转到首页
                        showSuccess('正在跳转到首页...', 1);
                        setTimeout(() => {
                            window.location.href = 'index.html';
                        }, 1000);
                    }
                }
            } catch (e) {
                console.error('页面初始化失败:', e);
                showError('页面初始化失败，请刷新重试');
            }
        });

        // 全局错误处理
        window.addEventListener('error', function(e) {
            console.error('全局错误:', e);
            showError('页面运行出错，请刷新重试');
        });
    </script>
</body>
</html>
