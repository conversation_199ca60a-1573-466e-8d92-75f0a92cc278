<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-在线学习-红色书籍</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/onlinelearning.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="contenttitlebox">
			<div>
				<a id="zxxx" href="onlinelearning.html">在线学习</a>
				<img src="img/jtright.png" />
				<label class="aa" id="hsname">教学资源</label>
			</div>
		</div>
		<div class="content">
			<div class="contenttopview">
				<div class="contentitem" id="classbox">
					<!-- <a class="titleactive">红色书籍</a>
					<a href="onlinelearning3.html">教学资源</a> -->
				</div>
				<!-- <div class="contentitem sss">
					<div class="ssdiv">
						<div class="select" onclick="showselect()">
							<label id="selecttxt">书籍</label>
							<img src="img/ssx.png" />
							<img src="img/ssb.png" />
						</div>
						<input />
						<div class="ss">搜索</div>
					</div>
					<div class="dztsg">电子图书馆</div>
					<div class="opbox" id="select">
						<div>书籍</div>
						<div>红色游学</div>
						<div>书籍</div>
						<div>红色游学</div>
					</div>
				</div> -->
			</div>
			<div class="tjbox" style="border: none;">
				<label>分类:</label>
				<span onclick="selects(this)" id="qbsj" class="tjactive" data-id="0">全部</span>
				<div id="flbox">
					
				</div>
			</div>
			
			<div class="tjbox" style="border: none; margin-top: 10px;">
				<label>排序:</label>
				<span onclick="changeSort(this)" id="sortDefault" class="tjactive" data-sort="">上传时间排序</span>
				<span onclick="changeSort(this)" id="sortPublishTime" data-sort="publishedTime,desc">发布时间</span>
			</div>
			
			<div id="htmlbox" class="sjbox">

			</div>
			
			<!-- 加载指示器 -->
			<div id="loading-indicator" style="display:none; text-align:center; padding:30px;">
				<div class="loading-container">
					<div class="loading-circle"></div>
					<div class="loading-text">正在加载数据，请稍候...</div>
				</div>
			</div>
			
			<div class="fybox" id="fyq">
				<span id="sy">首页</span>
				<span id="syy">上一页</span>
				<div class="num" id="num">
				</div>
				<span id="xyy">下一页</span>
				<span id="wy">尾页</span>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/clipboard.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				let userinfo = sessionStorage.getItem("userinfo")
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				getclass('onlinelearning.html')
				getfooterlink()
				getclassid()
				getallfllist()
				getclassch()
				
				// 默认选择发布时间排序
				$("#sortPublishTime").click();
			})
			let classid = null
			let selectid = 0
			
			let pageindex = 1
			let pageSize = 20
			let pages = 1
			let currentSort = ""; // 默认不指定排序
			let totalPages = 0; // 总页数
			let reversePageMode = true; // 是否启用页码倒序模式
			let highestValidPage = 1; // 已知的最高有效页码
			let lowestEmptyPage = -1; // 已知的最低空页码
			
			// 获取总页数并加载最后一页的数据
			function getInitialPageCount() {
				// 显示加载指示器
				$("#loading-indicator").show();
				$("#htmlbox").hide();
				
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						categoryId: classid,
						pageSize: 1, // 只获取一条数据，用于获取总条数
						redBookId: selectid != 0 ? selectid : null,
						pageNum: 1,
						_t: new Date().getTime(),
						sort: currentSort // 添加当前排序参数
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log("获取总页数:", res.data);
							totalPages = res.data.pages;
							// console.log("总页数:", totalPages);
							
							// 根据排序模式决定加载方式
							if(reversePageMode && totalPages > 0) {
								// 倒序模式：使用二分查找最高有效页码
								// 重置页码缓存
								highestValidPage = 1;
								lowestEmptyPage = -1;
								
								// 使用二分查找找到有效的最高页码
								binarySearchValidPage(1, totalPages);
							} else {
								// 正常排序模式：直接加载第一页
								getsjlist();
							}
						}
					}
				});
			}
			
			// 二分查找有效的最高页码
			function binarySearchValidPage(low, high) {
				if (low > high) {
					// 搜索完成，使用找到的最高有效页码
					// console.log("二分查找完成，最高有效页码:", highestValidPage);
					// 隐藏加载指示器
					$("#loading-indicator").hide();
					$("#htmlbox").show();
					
					if (highestValidPage > 0) {
						loadReversedPage(1, highestValidPage);
					} else {
						// 没有找到有效页码，显示第一页
						loadReversedPage(1, 1);
					}
					return;
				}
				
				// 计算中间值
				let mid = Math.floor((low + high) / 2);
				// console.log("二分查找中:", low, mid, high);
				
				// 检查此页是否有数据
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						categoryId: classid,
						pageSize: pageSize,
						redBookId: selectid != 0 ? selectid : null,
						pageNum: mid,
						_t: new Date().getTime()
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							if (res.data.list && res.data.list.length > 0) {
								// 此页有数据，记录并继续向上搜索
								// console.log("页码", mid, "有数据");
								if (mid > highestValidPage) {
									highestValidPage = mid;
								}
								// 继续向上搜索
								binarySearchValidPage(mid + 1, high);
							} else {
								// 此页无数据，记录并向下搜索
								// console.log("页码", mid, "无数据");
								if (lowestEmptyPage == -1 || mid < lowestEmptyPage) {
									lowestEmptyPage = mid;
								}
								// 继续向下搜索
								binarySearchValidPage(low, mid - 1);
							}
						}
					}
				});
			}
			
			// 加载倒序页面（将最后一页作为第一页显示）
			function loadReversedPage(displayPageNum, forcedActualPage) {
				// 显示加载指示器
				$("#loading-indicator").show();
				$("#htmlbox").hide();
				
				if(totalPages <= 0) return;
				
				// 计算实际需要加载的页码（倒序）
				let actualPageToLoad = forcedActualPage;
				
				// 如果没有指定页码，使用计算的倒序页码
				if (!actualPageToLoad) {
					// 如果知道最高有效页码，基于它来计算
					if (highestValidPage > 1) {
						actualPageToLoad = highestValidPage - displayPageNum + 1;
					} else {
						actualPageToLoad = totalPages - displayPageNum + 1;
					}
				}
				
				// 确保页码有效
				if(actualPageToLoad < 1) actualPageToLoad = 1;
				if(actualPageToLoad > totalPages) actualPageToLoad = totalPages;
				
				// console.log("显示页码:", displayPageNum, "实际加载页码:", actualPageToLoad);
				
				// 加载实际页码的数据
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						categoryId: classid,
						pageSize: pageSize,
						redBookId: selectid != 0 ? selectid : null,
						pageNum: actualPageToLoad,
						_t: new Date().getTime(),
						sort: currentSort
					},
					dataType: 'json',
					success: (res) => {
						// 隐藏加载指示器
						$("#loading-indicator").hide();
						$("#htmlbox").show();
						
						if (res.code == '200') {
							// console.log("加载的倒序页面数据:", res.data);
							
							// 处理空数据情况 - 可能加载的页是空的
							if (res.data.list && res.data.list.length === 0 && actualPageToLoad > 1) {
								// console.log("该页没有数据，尝试加载前一页:", actualPageToLoad - 1);
								
								// 更新最低空页码
								if (lowestEmptyPage == -1 || actualPageToLoad < lowestEmptyPage) {
									lowestEmptyPage = actualPageToLoad;
								}
								
								// 如果我们知道有效页码范围，直接跳转到最高有效页码
								if (highestValidPage > 0 && highestValidPage < actualPageToLoad) {
									loadReversedPage(displayPageNum, highestValidPage);
								} else {
									// 否则递减尝试
									loadReversedPage(displayPageNum, actualPageToLoad - 1);
								}
								return;
							}
							
							// 如果有数据，更新最高有效页码
							if (res.data.list && res.data.list.length > 0) {
								if (actualPageToLoad > highestValidPage) {
									highestValidPage = actualPageToLoad;
									// console.log("更新最高有效页码:", highestValidPage);
								}
							}
							
							// 更新页面数据
							updatePageDisplay(res.data, displayPageNum);
						}
					}
				});
			}
			
			// 更新页面显示（包括分页器）
			function updatePageDisplay(data, currentDisplayPage) {
				// 显示数据列表
				let newhtml = "";
				
				// 根据排序模式决定是否对页内数据进行倒序
				let displayList = data.list;
				if (reversePageMode) {
					// 默认排序时，对页内数据也进行倒序
					displayList = [...data.list].reverse();
				}
				
				// 使用适当排序后的数据生成HTML
				for (let k = 0; k < displayList.length; k++) {
					newhtml += '<div class="txtitem">' +
						'<div class="topitem" onclick="ininfo(this)" data-id="'+displayList[k].id+'">';
					if (displayList[k].thumbPath != null) {
						newhtml += '<img src="' + baseurl + displayList[k].thumbPath[0] + '"/>';
					}
					newhtml += '<div class="icobox">' +
						'<label><img src="img/gkl.png"/>' + displayList[k].clickCount +
						'</label></div></div>' +
						'<div class="bottomitem">' +
						'<div class="title">' + displayList[k].title + '</div>' +
						'<div class="zz">作者: ' + displayList[k].author + '</div><div class="zz">' + displayList[k]
						.postName + '</div></div></div>';
				}
				$("#htmlbox").html(newhtml);
				
				// 更新分页显示
				pages = data.pages;
				pageindex = currentDisplayPage; // 使用显示的页码
				
				if (pages > 1) {
					let numhtml = "";
					
					//当页数大于5
					if (pages > 5) {
						if (pageindex <= 5) { //当前页码小于5的时候右侧不变
							for (let a = 1; a <= 10; a++) {
								if (a > pages) break;
								if (pageindex == a) {
									numhtml += '<label class="actinum" onclick="getnewlist(' + a +
										')">' + a +
										'</label>';
								} else {
									numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
										'</label>';
								}
							}
							numhtml += '<label onclick="getnewlist(11)">...</label>';
						} else if (5 < pageindex && pageindex < pages - 5) {
							numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) - 5) +
								')">...</label>';
							for (let a = pageindex - 4; a <= pageindex + 4; a++) {
								if (pageindex == a) {
									numhtml += '<label class="actinum" onclick="getnewlist(' + a +
										')">' + a +
										'</label>';
								} else {
									numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
										'</label>';
								}
							}
							numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) + 5) +
								')">...</label>';
						} else {
							numhtml += '<label onclick="getnewlist(' + (pages - 11) +
								')">...</label>';
							for (let a = pages - 10; a <= pages; a++) {
								if (a < 1) continue;
								if (pageindex == a) {
									numhtml += '<label class="actinum" onclick="getnewlist(' + a +
										')">' + a +
										'</label>';
								} else {
									numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
										'</label>';
								}
							}
						}
					} else {
						for (let a = 1; a <= pages; a++) {
							if (pageindex == a) {
								numhtml += '<label class="actinum" onclick="getnewlist(' + a +
									')">' + a +
									'</label>';
							} else {
								numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>';
							}
						}
					}
					
					$("#sy").attr("onclick", "getnewlist(1)");
					$("#syy").attr("onclick", "getnewlist(1)");
					if (pageindex > 1) {
						$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")");
					}
					if (pageindex < pages) {
						$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")");
					} else {
						$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")");
					}
					$("#wy").attr("onclick", "getnewlist(" + pages + ")");
					$("#num").html(numhtml);
					$("#fyq").show();
				} else {
					$("#fyq").hide();
				}
			}
			
			// 修改获取新页面的函数
			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是最后一页了！");
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！");
					}
				} else {
					pageindex = index;
					
					if(reversePageMode) {
						// 如果已知有效的页码范围，优化页面加载
						if (highestValidPage > 1) {
							// 计算基于最高有效页码的实际页码
							let maxValidDisplayPage = highestValidPage;
							if (index > maxValidDisplayPage) {
								cocoMessage.warning(1000, "已经是第一页了，没有更多数据");
								pageindex = 1;
								index = 1;
							}
						}
						
						loadReversedPage(index);
					} else {
						getsjlist();
					}
				}
			}
			
			function changeSort(item) {
				// 清除所有排序按钮的激活状态
				$("#sortDefault, #sortPublishTime").removeClass("tjactive");
				
				// 设置当前点击的排序按钮为激活状态
				$(item).addClass("tjactive");
				
				// 保存排序方式
				currentSort = $(item).attr("data-sort");
				
				// 根据排序方式决定是否启用倒序模式
				if (currentSort === "publishedTime,desc") {
					// 发布时间排序时使用正常排序顺序
					reversePageMode = false;
				} else {
					// 默认排序时使用页码倒序显示
					reversePageMode = true;
				}
				
				// 重置到第一页
				pageindex = 1;
				
				// 重新获取总页数和数据
				getInitialPageCount();
			}
			
			function ininfo(item){
				window.location.href = "onlinelearning5.html?id="+$(item).attr("data-id")
			}
			function getallfllist(){
				$.ajax({
					url: baseurl + "/web/redbook/list ",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							// 过滤掉具有删除标记的分类
							let filteredList = res.data.list.filter(item => item.delFlag !== 1);
							// console.log("原始分类数量:", res.data.list.length, "过滤后分类数量:", filteredList.length);
							
							filteredList.map((item)=>{
								if(item.id!==selectid){
									html+='<span onclick="selects(this)" data-id="'+item.id+'">'+item.name+'</span>'
								}else{
									html+='<span class="tjactive">'+item.name+'</span>'
								}
							})
							$("#flbox").html(html)
						}
					}
				})
			}
			function selects(item){
				pageindex = 1
				let allfl = $("#flbox span")
				if($(item).attr("data-id")!=0){
					for(let i =0;i<allfl.length;i++){
						if($(allfl[i]).attr("data-id") == $(item).attr("data-id")){
							$(allfl[i]).attr("class","tjactive")
						}else{
							$(allfl[i]).attr("class","")
						}
					}
					$("#qbsj").attr("class","")
				}else{
					for(let i =0;i<allfl.length;i++){
						$(allfl[i]).attr("class","")
					}
					$("#qbsj").attr("class","tjactive")
				}
				
				selectid = $(item).attr("data-id")
				
				// 分类变更后，重新获取总页数
				getInitialPageCount();
			}
			function getclassid() {
				$.ajax({
					url: baseurl + "/web/category/book",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							classid = res.data[0].id;
							// 获取总页数并加载最后一页
							getInitialPageCount();
						}
					}
				})
			}
			let clipboard = new ClipboardJS('.copybtn');
			clipboard.on('success', function(e) {
				e.clearSelection();
				cocoMessage.success(1000, "复制成功！")
			});
			
			clipboard.on('error', function(e) {
				cocoMessage.error(1000, "复制失败！")
			});
			function getsjlist() {
				let ccid = null
				if(selectid!=0){
					ccid = selectid
				}
				
				// 显示加载指示器
				$("#loading-indicator").show();
				$("#htmlbox").hide();
				
				// 构建请求参数
				const requestParams = {
					categoryId: classid,
					pageSize: pageSize,
					redBookId: ccid,
					pageNum: pageindex,
					_t: new Date().getTime()
				};
				
				// 如果有排序参数，则添加到请求中
				if(currentSort) {
					requestParams.sort = currentSort;
				}
				
				// console.log("请求参数:", requestParams);
				
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: requestParams,
					dataType: 'json',
					success: (res) => {
						// 隐藏加载指示器
						$("#loading-indicator").hide();
						$("#htmlbox").show();
						
						// console.log("API返回数据:", res.data);
						if (res.code == '200') {
							// 打印返回的第一条记录，查看其所有字段
							if(res.data.list.length > 0) {
								// console.log("第一条记录:", res.data.list[0]);
							}
							
							// 更新页面数据
							updatePageDisplay(res.data, pageindex);
						}
					}
				});
			}
			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}


			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					// console.log(res.data[i])
			// 					if (res.data[i].id == getUrlParam('id') || res.data[i].redirectUrl ==
			// 						'onlinelearning.html') {
			// 						classdate = res.data[i]
			// 						$("#zxxx").attr('href', "onlinelearning.html?id=" + res.data[i].id)
			// 						html += '<a class="menuitem active" href="' + res.data[i].redirectUrl + '?id=' +
			// 							res
			// 							.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					} else {
			// 						html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 							.data[
			// 								i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					}
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 			getlist()
			// 		}
			// 	})
			// }
			
			function getclassch(){
				$.ajax({
					url: baseurl + "/web/category/teacher",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							classdate = res.data[0]
							getlist()
						}
					}
				})
			}

			function getlist(data) {
				let classData = data || classdate;
				let classhtml = ""
				for (let i = 0; i < classData.children.length; i++) {
					if (classData.children[i].name == '红色书籍') {
						classhtml += '<a class="titleactive" href="' + classData.children[i].redirectUrl + '?id=' + classData
							.children[i].id + '">' + classData.children[i].name + '</a>'
						$("#hsname").html(classData.children[i].name)
					} else {
						classhtml += '<a href="' + classData.children[i].redirectUrl + '?id=' + classData.children[i].id + '">' +
							classData.children[i].name + '</a>'
					}
				}
				$("#classbox").html(classhtml)
			}

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			$("#select div").on('click', function() {
				$("#selecttxt").html($(this).html())
				$("#select").hide()
			})

			function showselect() {
				$("#select").show()
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
		<style>
		/* 加载动画样式 */
		.loading-container {
			display: inline-flex;
			flex-direction: column;
			align-items: center;
			padding: 20px;
			border-radius: 8px;
			background-color: rgba(255, 255, 255, 0.95);
			box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
		}

		.loading-circle {
			width: 48px;
			height: 48px;
			border: 4px solid #f3f3f3;
			border-top: 4px solid #d82121;
			border-radius: 50%;
			margin-bottom: 15px;
			animation: spin 1s linear infinite;
		}

		.loading-text {
			font-size: 15px;
			color: #333;
			font-weight: 500;
		}

		@keyframes spin {
			0% { transform: rotate(0deg); }
			100% { transform: rotate(360deg); }
		}

		@keyframes fadeIn {
			from { opacity: 0; }
			to { opacity: 1; }
		}

		#loading-indicator {
			animation: fadeIn 0.3s ease-in-out;
		}
		
		/* 书籍封面标准化样式 - A4纸张比例 */
		.txtitem .topitem {
			position: relative;
			width: 100%;
			height: 0;
			padding-bottom: 141.4%; /* A4纸张比例 1:1.414 */
			overflow: hidden;
			background-color: #f5f5f5;
			box-shadow: 0 2px 6px rgba(0,0,0,0.1);
			border-radius: 4px;
		}
		
		.txtitem .topitem img {
			position: absolute;
			top: 0;
			left: 0;
			width: 100%;
			height: 100%;
			object-fit: cover; /* 保持比例但填满容器 */
			transition: transform 0.3s ease;
		}
		
		.txtitem .topitem:hover img {
			transform: scale(1.05);
		}
		
		.txtitem .topitem .icobox {
			position: absolute;
			bottom: 0;
			left: 0;
			width: 100%;
			background: linear-gradient(to top, rgba(0,0,0,0.7), transparent);
			padding: 10px 0 5px;
			z-index: 2;
		}
		
		.txtitem .topitem .icobox label {
			color: white;
			display: flex;
			align-items: center;
			justify-content: center;
			font-size: 12px;
		}
		
		.txtitem .topitem .icobox img {
			position: relative;
			width: 16px;
			height: 16px;
			margin-right: 5px;
			object-fit: contain;
		}
		
		/* 优化底部文字信息 */
		.txtitem .bottomitem {
			padding: 10px 5px;
			height: 80px;
			display: flex;
			flex-direction: column;
		}
		
		.txtitem .bottomitem .title {
			font-weight: bold;
			margin-bottom: 5px;
			max-height: 40px;
			overflow: hidden;
			text-overflow: ellipsis;
			display: -webkit-box;
			-webkit-line-clamp: 2;
			-webkit-box-orient: vertical;
		}
		
		.txtitem .bottomitem .zz {
			font-size: 12px;
			color: #666;
			line-height: 1.5;
		}
		</style>
	</body>
</html>
