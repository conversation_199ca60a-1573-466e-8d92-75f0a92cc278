/* 学习模块样式 */
.learning-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 16px 0;
}

.learning-item {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

.learning-item:active {
    transform: scale(0.98);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.learning-cover {
    position: relative;
    height: 120px;
    overflow: hidden;
    background: #f5f5f5;
}

.learning-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.learning-type {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(192, 7, 20, 0.9);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
}

.learning-info {
    padding: 12px;
}

.learning-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.learning-meta {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.learning-author {
    display: block;
    margin-bottom: 4px;
}

.learning-time {
    color: #999;
}

.learning-score {
    font-size: 12px;
    color: #ff6b35;
    font-weight: 600;
}

.learning-tabs {
    display: flex;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    margin-bottom: 16px;
}

.tab-btn {
    flex: 1;
    padding: 8px 16px;
    border: none;
    background: transparent;
    color: #666;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.tab-btn.active {
    background: white;
    color: #c00714;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.loading-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #666;
    font-size: 14px;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #c00714;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;
    font-size: 14px;
}

/* 响应式设计 */
@media (max-width: 375px) {
    .learning-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }
    
    .learning-cover {
        height: 100px;
    }
    
    .learning-info {
        padding: 10px;
    }
    
    .learning-title {
        font-size: 13px;
    }
    
    .tab-btn {
        padding: 6px 12px;
        font-size: 13px;
    }
}
