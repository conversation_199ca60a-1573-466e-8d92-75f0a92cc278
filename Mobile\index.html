<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>思政一体化平台 - 移动端</title>

    <!-- CSS文件 -->
    <link rel="stylesheet" href="css/mobile-base.css">
    <link rel="stylesheet" href="css/mobile-index.css">
    <link rel="stylesheet" href="css/mobile-components.css">

    <!-- JavaScript文件 -->
    <script src="../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../PC/js/coco-message.js"></script>
    <script src="mobile-config.js"></script>
    <script src="js/mobile-base.js"></script>
    <script src="js/mobile-touch.js"></script>
    <script src="js/mobile-nav.js"></script>
</head>
<body class="mobile-index">
    <!-- 加载动画 -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner"></div>
        <p>加载中...</p>
    </div>

    <!-- 顶部导航栏 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="menu-btn" id="menuBtn">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="logo">
                <img src="../PC/img/logo.png" alt="思政一体化平台">
            </div>
            <div class="header-actions">
                <button class="search-btn" id="searchBtn">
                    <svg viewBox="0 0 24 24" fill="currentColor">
                        <path d="M15.5 14h-.79l-.28-.27C15.41 12.59 16 11.11 16 9.5 16 5.91 13.09 3 9.5 3S3 5.91 3 9.5 5.91 16 9.5 16c1.61 0 3.09-.59 4.23-1.57l.27.28v.79l5 4.99L20.49 19l-4.99-5zm-6 0C7.01 14 5 11.99 5 9.5S7.01 5 9.5 5 14 7.01 14 9.5 11.99 14 9.5 14z"/>
                    </svg>
                </button>
                <div class="user-info" id="userInfo">
                    <button class="login-btn" id="loginBtn">登录</button>
                    <div class="user-avatar" id="userAvatar" style="display: none;">
                        <span id="userName"></span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 侧边菜单 -->
    <nav class="side-menu" id="sideMenu">
        <div class="menu-overlay" id="menuOverlay"></div>
        <div class="menu-content">
            <div class="menu-header">
                <div class="menu-logo">
                    <img src="../PC/img/logo.png" alt="思政一体化平台">
                    <h3>思政一体化平台</h3>
                </div>
                <button class="menu-close" id="menuClose">×</button>
            </div>
            <div class="menu-items" id="menuItems">
                <!-- 菜单项将通过JavaScript动态加载 -->
            </div>
        </div>
    </nav>

    <!-- 搜索面板 -->
    <div class="search-panel" id="searchPanel">
        <div class="search-content">
            <div class="search-input-wrapper">
                <input type="text" placeholder="搜索内容..." id="searchInput">
                <button class="search-submit" id="searchSubmit">搜索</button>
            </div>
            <button class="search-close" id="searchClose">取消</button>
        </div>
    </div>

    <!-- 主要内容区域 -->
    <main class="main-content">
        <!-- 轮播图区域 -->
        <section class="banner-section">
            <div class="swiper-container" id="bannerSwiper">
                <div class="swiper-wrapper" id="bannerWrapper">
                    <!-- 轮播图将通过JavaScript动态加载 -->
                </div>
                <div class="swiper-pagination"></div>
            </div>
        </section>

        <!-- 快捷功能区域 -->
        <section class="quick-actions">
            <div class="container">
                <div class="actions-grid">
                    <a href="pages/learning.html" class="action-item">
                        <div class="action-icon courses">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M19 3H5C3.9 3 3 3.9 3 5V19C3 20.1 3.9 21 5 21H19C20.1 21 21 20.1 21 19V5C21 3.9 20.1 3 19 3ZM19 19H5V5H19V19ZM17 12H7V10H17V12ZM17 16H7V14H17V16ZM17 8H7V6H17V8Z"/>
                            </svg>
                        </div>
                        <span>课程学习</span>
                    </a>
                    <a href="pages/redbooks.html" class="action-item">
                        <div class="action-icon redbooks">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M18 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V4C20 2.9 19.1 2 18 2ZM18 20H6V4H7V13L9.5 11.5L12 13V4H18V20Z"/>
                            </svg>
                        </div>
                        <span>红色书籍</span>
                    </a>
                    <a href="pages/vrtour.html" class="action-item">
                        <div class="action-icon vr">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M7 9C7 8.4 7.4 8 8 8H16C16.6 8 17 8.4 17 9V15C17 15.6 16.6 16 16 16H8C7.4 16 7 15.6 7 15V9Z"/>
                                <circle cx="10" cy="12" r="1.5"/>
                                <circle cx="14" cy="12" r="1.5"/>
                            </svg>
                        </div>
                        <span>VR红色游学</span>
                    </a>
                    <a href="pages/footprint.html" class="action-item">
                        <div class="action-icon footprint">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
                            </svg>
                        </div>
                        <span>总书记的足迹</span>
                    </a>
                    <a href="pages/experiment.html" class="action-item">
                        <div class="action-icon experiment">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M9 2V7H7.5C6.67 7 6 7.67 6 8.5V10H4.5C3.67 10 3 10.67 3 11.5V20.5C3 21.33 3.67 22 4.5 22H19.5C20.33 22 21 21.33 21 20.5V11.5C21 10.67 20.33 10 19.5 10H18V8.5C18 7.67 17.33 7 16.5 7H15V2H9ZM11 4H13V7H11V4ZM8 9H16V10H8V9ZM5 12H19V20H5V12ZM7 14V18H9V14H7ZM11 14V18H13V14H11ZM15 14V18H17V14H15Z"/>
                            </svg>
                        </div>
                        <span>虚仿实验空间</span>
                    </a>
                    <a href="pages/party-study.html" class="action-item">
                        <div class="action-icon party">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                            </svg>
                        </div>
                        <span>党建学习</span>
                    </a>
                    <a href="pages/museum.html" class="action-item">
                        <div class="action-icon museum">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M12 3L2 8V9H22V8L12 3ZM4 11V19H6V11H4ZM8 11V19H10V11H8ZM12 11V19H14V11H12ZM16 11V19H18V11H16ZM20 11V19H22V11H20ZM2 20V22H22V20H2Z"/>
                            </svg>
                        </div>
                        <span>医德博物馆</span>
                    </a>
                    <a href="pages/community.html" class="action-item">
                        <div class="action-icon medical">
                            <svg viewBox="0 0 24 24" fill="currentColor">
                                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
                            </svg>
                        </div>
                        <span>心声社区</span>
                    </a>
                </div>
            </div>
        </section>

        <!-- 心声社区区域 -->
        <section class="community-section">
            <div class="container">
                <div class="section-header">
                    <h2>
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
                        </svg>
                        心声社区
                    </h2>
                    <a href="pages/community.html" class="more-link">更多</a>
                </div>
                <div class="community-list" id="communityList">
                    <!-- 社区内容将通过JavaScript动态加载 -->
                </div>
            </div>
        </section>

        <!-- 在线学习区域 -->
        <section class="learning-section">
            <div class="container">
                <div class="section-header">
                    <h2>
                        <svg viewBox="0 0 24 24" fill="currentColor">
                            <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3ZM5 13.18V17.18L12 21L19 17.18V13.18L12 17L5 13.18Z"/>
                        </svg>
                        在线学习
                    </h2>
                    <a href="pages/onlinelearning.html" class="more-link">更多</a>
                </div>
                <div class="learning-tabs">
                    <button class="tab-btn active" data-tab="redbooks">红色书籍</button>
                    <button class="tab-btn" data-tab="courses">课程学习</button>
                </div>
                <div class="learning-content">
                    <div class="tab-content active" id="redbooksContent">
                        <!-- 红色书籍内容 -->
                    </div>
                    <div class="tab-content" id="coursesContent">
                        <!-- 课程学习内容 -->
                    </div>
                </div>
            </div>
        </section>
    </main>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="index.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="pages/learning-modules.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="pages/community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="pages/profile.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>我的</span>
        </a>
    </nav>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83L16.59 15.41L18 14L12 8L6 14Z"/>
        </svg>
    </button>

    <script>
        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initMobileFeatures();

            // 检查登录状态
            checkLoginStatus();

            // 加载页面数据
            loadPageData();

            // 隐藏加载动画
            setTimeout(() => {
                document.getElementById('loadingOverlay').style.display = 'none';
            }, 1000);
        });

        function initMobileFeatures() {
            // 初始化触摸交互
            initTouchInteractions();

            // 初始化导航
            initNavigation();

            // 初始化搜索
            initSearch();

            // 初始化返回顶部
            initBackToTop();
        }

        function checkLoginStatus() {
            const userinfo = sessionStorage.getItem("userinfo");
            const loginBtn = document.getElementById('loginBtn');
            const userAvatar = document.getElementById('userAvatar');
            const userName = document.getElementById('userName');

            if (userinfo) {
                const user = JSON.parse(userinfo);
                loginBtn.style.display = 'none';
                userAvatar.style.display = 'block';
                userName.textContent = user.name;
            } else {
                loginBtn.style.display = 'block';
                userAvatar.style.display = 'none';
            }
        }

        function loadPageData() {
            // 加载菜单
            loadMenu();

            // 加载轮播图
            loadBanner();

            // 加载心声社区
            loadCommunity();

            // 加载学习内容
            loadLearningContent();
        }

        function initBackToTop() {
            // 返回顶部功能已在mobile-touch.js中实现
        }

        function initLearningTabs() {
            const tabBtns = document.querySelectorAll('.tab-btn');
            const tabContents = document.querySelectorAll('.tab-content');

            tabBtns.forEach(btn => {
                btn.addEventListener('click', function() {
                    const targetTab = this.getAttribute('data-tab');

                    // 移除所有active类
                    tabBtns.forEach(b => b.classList.remove('active'));
                    tabContents.forEach(c => c.classList.remove('active'));

                    // 添加active类
                    this.classList.add('active');
                    document.getElementById(targetTab + 'Content').classList.add('active');

                    // 加载对应内容
                    if (targetTab === 'redbooks') {
                        loadRedBooks();
                    } else if (targetTab === 'courses') {
                        loadCourses();
                    }
                });
            });
        }

        function loadBanner() {
            getBannerData().then(bannerData => {
                renderBanner(bannerData);
            }).catch(error => {
                console.error('加载轮播图失败:', error);
                renderDefaultBanner();
            });
        }

        function renderBanner(bannerData) {
            const bannerWrapper = document.getElementById('bannerWrapper');
            if (!bannerWrapper) return;

            let html = '';
            if (bannerData && bannerData.length > 0) {
                bannerData.forEach(item => {
                    html += `
                        <div class="swiper-slide">
                            <img src="${item.imageUrl || '../PC/img/banner.png'}" alt="${item.title || '轮播图'}">
                            <div class="slide-content">
                                <div class="slide-title">${item.title || '思政一体化平台'}</div>
                                <div class="slide-description">${item.description || '传承红色基因，弘扬时代精神'}</div>
                            </div>
                        </div>
                    `;
                });
            } else {
                html = renderDefaultBanner();
            }

            bannerWrapper.innerHTML = html;
            initSwiper();
        }

        function renderDefaultBanner() {
            return `
                <div class="swiper-slide">
                    <img src="../PC/img/banner.png" alt="思政一体化平台">
                    <div class="slide-content">
                        <div class="slide-title">思政一体化平台</div>
                        <div class="slide-description">传承红色基因，弘扬时代精神</div>
                    </div>
                </div>
            `;
        }

        function initSwiper() {
            // 简单的轮播图实现
            const slides = document.querySelectorAll('.swiper-slide');
            if (slides.length <= 1) return;

            let currentSlide = 0;
            const totalSlides = slides.length;

            // 创建分页指示器
            const pagination = document.querySelector('.swiper-pagination');
            if (pagination) {
                let paginationHtml = '';
                for (let i = 0; i < totalSlides; i++) {
                    paginationHtml += `<span class="swiper-pagination-bullet ${i === 0 ? 'swiper-pagination-bullet-active' : ''}"></span>`;
                }
                pagination.innerHTML = paginationHtml;
            }

            // 自动轮播
            setInterval(() => {
                currentSlide = (currentSlide + 1) % totalSlides;
                updateSlide();
            }, 5000);

            function updateSlide() {
                slides.forEach((slide, index) => {
                    slide.style.display = index === currentSlide ? 'block' : 'none';
                });

                const bullets = document.querySelectorAll('.swiper-pagination-bullet');
                bullets.forEach((bullet, index) => {
                    bullet.classList.toggle('swiper-pagination-bullet-active', index === currentSlide);
                });
            }

            // 初始化显示
            updateSlide();
        }

        function loadCommunity() {
            const communityList = document.getElementById('communityList');
            if (!communityList) return;

            // 显示加载状态
            communityList.innerHTML = '<div class="loading-item"><div class="spinner"></div>加载中...</div>';

            // 获取心声社区数据
            $.ajax({
                url: baseurl + "/web/posts",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: {
                    pageNum: 1,
                    pageSize: 5
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        renderCommunity(res.data.list || []);
                    } else {
                        communityList.innerHTML = '<div class="empty-state">暂无社区内容</div>';
                    }
                },
                error: (err) => {
                    console.error('加载心声社区失败:', err);
                    communityList.innerHTML = '<div class="empty-state">暂无社区内容</div>';
                }
            });
        }

        function renderCommunity(communityData) {
            const communityList = document.getElementById('communityList');
            if (!communityList) return;

            if (!communityData || communityData.length === 0) {
                communityList.innerHTML = '<div class="empty-state">暂无社区内容</div>';
                return;
            }

            let html = '';
            communityData.forEach(item => {
                const isNew = MobileUtils.formatTime(item.publishedTime).includes('分钟前') ||
                             MobileUtils.formatTime(item.publishedTime).includes('小时前');

                html += `
                    <a href="pages/community-detail.html?id=${item.id}" class="community-item" data-id="${item.id}">
                        ${isNew ? '<span class="new-badge">NEW</span>' : ''}
                        <div class="community-content">
                            <div class="community-title">${item.title || '无标题'}</div>
                            <div class="community-time">${MobileUtils.formatTime(item.publishedTime)}</div>
                        </div>
                    </a>
                `;
            });

            communityList.innerHTML = html;

            // 绑定点击事件
            bindCommunityEvents();
        }

        function bindCommunityEvents() {
            const communityItems = document.querySelectorAll('.community-item');
            communityItems.forEach(item => {
                item.addEventListener('click', function(e) {
                    e.preventDefault();
                    const id = this.getAttribute('data-id');
                    if (id) {
                        clicknum(id);
                        setTimeout(() => {
                            window.location.href = this.getAttribute('href');
                        }, 100);
                    }
                });
            });
        }

        function loadLearningContent() {
            // 默认加载红色书籍
            loadRedBooks();
        }

        function loadRedBooks() {
            const redbooksContent = document.getElementById('redbooksContent');
            if (!redbooksContent) return;

            redbooksContent.innerHTML = '<div class="loading-item"><div class="spinner"></div>加载中...</div>';

            // 获取红色书籍数据
            $.ajax({
                url: baseurl + "/web/posts",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: {
                    pageNum: 1,
                    pageSize: 6,
                    redBookId: getRedBookClassId()
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        renderLearningGrid(res.data.list || [], redbooksContent, 'redbook');
                    } else {
                        redbooksContent.innerHTML = '<div class="empty-state">暂无红色书籍</div>';
                    }
                },
                error: (err) => {
                    console.error('加载红色书籍失败:', err);
                    redbooksContent.innerHTML = '<div class="empty-state">暂无红色书籍</div>';
                }
            });
        }

        function getRedBookClassId() {
            // 获取红色书籍分类ID
            return sessionStorage.getItem('redBookClassId') || null;
        }

        function loadCourses() {
            const coursesContent = document.getElementById('coursesContent');
            if (!coursesContent) return;

            coursesContent.innerHTML = '<div class="loading-item"><div class="spinner"></div>加载中...</div>';

            getCoursesData(1, 6).then(data => {
                renderLearningGrid(data.list || [], coursesContent, 'course');
            }).catch(error => {
                console.error('加载课程失败:', error);
                coursesContent.innerHTML = '<div class="empty-state">暂无课程内容</div>';
            });
        }

        function renderLearningGrid(data, container, type) {
            if (!data || data.length === 0) {
                container.innerHTML = '<div class="empty-state">暂无内容</div>';
                return;
            }

            let html = '<div class="learning-grid">';
            data.forEach(item => {
                const url = type === 'redbook' ?
                    `pages/redbook-detail.html?id=${item.id}` :
                    `pages/course-detail.html?id=${item.id}`;

                html += `
                    <a href="${url}" class="learning-item" data-id="${item.id}">
                        <div class="learning-cover">${item.title || item.name || '无标题'}</div>
                        <div class="learning-info">
                            <div class="learning-title">${item.title || item.name || '无标题'}</div>
                            <div class="learning-meta">
                                <span>${type === 'redbook' ? '红色书籍' : '课程学习'}</span>
                                <span>${MobileUtils.formatTime(item.createTime || item.publishedTime)}</span>
                            </div>
                        </div>
                    </a>
                `;
            });
            html += '</div>';

            container.innerHTML = html;
        }
    </script>
</body>
</html>
