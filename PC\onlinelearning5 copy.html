<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0">
		<title>思政一体化平台-在线学习-红色书籍</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/onlinelearning.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>
		<style>
			html, body {
				overflow-x: hidden;
				max-width: 100%;
			}
			
			/* 确保下拉菜单样式正确 */
			.menuitemaaa {
				position: relative;
			}
			
			.menuitemaaa .itemcboxaaa {
				display: none;
				position: absolute;
				top: 100%;
				left: 0;
				width: 100%;
				background: rgb(126,8,11);
				padding: 0.625rem;
				z-index: 999;
			}
			
			.menuitemaaa:hover .itemcboxaaa {
				display: block;
			}
			
			.menuaaaa {
				cursor: pointer;
				font-size: 0.833333rem;
				color: #FFFFFF;
				display: flex;
				height: 100%;
				width: 100%;
				align-items: center;
				justify-content: center;
			}
			
			.itema2aaa {
				display: block;
				width: 100%;
				text-align: center;
				font-size: 0.833333rem;
				color: #FFFFFF;
				line-height: 1.8rem;
			}
			
			.itema2aaa:hover {
				color: #ffd05f;
				font-weight: bold;
			}
			
			/* PDF阅读器美化样式 */
			.tcbox {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(0, 0, 0, 0.85);
				z-index: 999;
				display: none;
			}

			.topviewsss {
				position: relative;
				height: 60px;
				background-color: #d71c1c;
				color: white;
				display: flex;
				align-items: center;
				justify-content: space-between;
				padding: 0 24px;
				box-shadow: 0 2px 8px rgba(0, 0, 0, 0.2);
				box-sizing: border-box;
				width: 100%;
				overflow: hidden;
			}

			.topviewsss a {
				display: flex;
				align-items: center;
				justify-content: center;
				padding: 8px 16px;
				border-radius: 4px;
				background-color: rgba(255, 255, 255, 0.2);
				color: white;
				cursor: pointer;
				transition: all 0.3s ease;
				font-size: 16px;
				flex-shrink: 0;
				margin-left: 10px;
				z-index: 10;
			}

			.topviewsss a:hover {
				background-color: rgba(255, 255, 255, 0.3);
			}

			.topviewsss label {
				font-size: 18px;
				font-weight: bold;
				flex: 1;
				text-align: center;
				text-overflow: ellipsis;
				white-space: nowrap;
				overflow: hidden;
				padding: 0 20px;
			}

			.pdfbox {
				height: calc(100% - 60px);
				width: 100%;
				background-color: #f5f5f5;
				position: relative;
				overflow: hidden;
				display: flex;
			}

			.pdfbox iframe {
				width: 100%;
				height: 100%;
				border: none;
			}

			/* PDF 目录侧边栏 */
			.pdf-sidebar {
				width: 280px;
				height: 100%;
				background-color: #f8f8f8;
				border-right: 1px solid #ddd;
				overflow-y: auto;
				transition: all 0.3s ease;
				display: none;
				flex-shrink: 0;
			}

			.pdf-sidebar-visible {
				display: block;
			}

			.pdf-sidebar-header {
				padding: 15px;
				background-color: #d71c1c;
				color: white;
				font-weight: bold;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			.pdf-sidebar-title {
				font-size: 16px;
			}

			.pdf-sidebar-close {
				cursor: pointer;
				font-size: 20px;
			}

			.pdf-outline {
				padding: 10px 0;
			}

			.pdf-outline-item {
				padding: 8px 15px;
				cursor: pointer;
				transition: all 0.2s;
				border-left: 3px solid transparent;
			}

			.pdf-outline-item:hover {
				background-color: #eee;
				border-left-color: #d71c1c;
			}

			.pdf-outline-item.active {
				background-color: rgba(215, 28, 28, 0.1);
				border-left-color: #d71c1c;
				font-weight: bold;
			}

			.pdf-main-container {
				flex: 1;
				height: 100%;
				position: relative;
				overflow: hidden;
			}

			/* 导航控件 */
			.pdf-controls {
				position: absolute;
				bottom: 20px;
				left: 50%;
				transform: translateX(-50%);
				background-color: rgba(0, 0, 0, 0.6);
				border-radius: 30px;
				padding: 8px 15px;
				display: flex;
				align-items: center;
				z-index: 100;
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
			}

			.pdf-control-btn {
				background-color: transparent;
				border: none;
				color: white;
				font-size: 14px;
				padding: 5px 10px;
				margin: 0 5px;
				cursor: pointer;
				border-radius: 4px;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.2s;
			}

			.pdf-control-btn:hover {
				background-color: rgba(255, 255, 255, 0.2);
			}

			.pdf-page-info {
				color: white;
				margin: 0 10px;
				min-width: 60px;
				text-align: center;
			}

			.pdf-zoom-controls {
				margin: 0 5px;
			}

			.pdf-view-mode-btn {
				margin-left: 10px;
				position: relative;
			}

			.pdf-view-mode-btn:after {
				content: "";
				position: absolute;
				left: -5px;
				top: 5px;
				height: 20px;
				width: 1px;
				background-color: rgba(255, 255, 255, 0.3);
			}

			/* 页面翻转效果 */
			.pdf-page-container {
				perspective: 2000px;
				height: 100%;
				position: relative;
			}

			.pdf-book {
				width: 100%;
				height: 100%;
				position: relative;
				transform-style: preserve-3d;
				transform: translateZ(0);
				transition: transform 0.5s;
				backface-visibility: hidden;
			}

			.pdf-page {
				position: relative;
				width: 100%;
				height: 100%;
				background-color: white;
				overflow: hidden;
				box-sizing: border-box;
				display: flex;
				align-items: center;
				justify-content: center;
			}

			.pdf-page canvas {
				max-width: 100%;
				max-height: 100%;
				box-shadow: 0 0 10px rgba(0,0,0,0.1);
			}

			.pdf-page.turning {
				animation: page-turn 1.5s ease-in-out forwards;
				box-shadow: 0 0 15px rgba(0, 0, 0, 0.2);
			}

			@keyframes page-turn {
				0% { transform: rotateY(0deg); }
				100% { transform: rotateY(-180deg); }
			}

			/* 双页模式 */
			.pdf-book.double-page-mode {
				display: flex;
				width: 100%;
				height: 100%;
				justify-content: center;
			}

			.pdf-book.double-page-mode .pdf-page {
				position: relative;
				width: 50%;
				height: 100%;
				margin: 0;
				float: left;
				border-right: none;
				box-shadow: none;
			}

			.pdf-book.double-page-mode .pdf-page:nth-child(2n) {
				border-right: none;
				border-left: none;
			}
			
			.pdf-loading {
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				text-align: center;
				width: 200px;
				background-color: rgba(255, 255, 255, 0.9);
				border-radius: 10px;
				padding: 20px;
				box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
				z-index: 120;
			}

			.pdf-loading-spinner {
				width: 60px;
				height: 60px;
				border: 5px solid rgba(215, 28, 28, 0.2);
				border-top: 5px solid #d71c1c;
				border-radius: 50%;
				animation: spin 1.2s cubic-bezier(0.68, -0.55, 0.27, 1.55) infinite;
				margin: 0 auto 15px;
			}
			
			.pdf-loading-text {
				color: #333;
				font-size: 16px;
				font-weight: 500;
				margin-top: 10px;
			}
			
			.pdf-loading-progress {
				height: 6px;
				background-color: rgba(215, 28, 28, 0.2);
				border-radius: 3px;
				margin-top: 10px;
				overflow: hidden;
			}
			
			.pdf-loading-progress-bar {
				height: 100%;
				background-color: #d71c1c;
				border-radius: 3px;
				width: 0%;
				animation: progress-animation 2s ease-in-out infinite;
			}

			/* 移动设备适配 */
			@media screen and (max-width: 768px) {
				.pdf-sidebar {
					position: fixed;
					left: -280px;
					top: 0;
					bottom: 0;
					z-index: 150;
					box-shadow: 2px 0 10px rgba(0, 0, 0, 0.2);
					transition: left 0.3s ease;
					width: 80%;
					max-width: 320px;
				}

				.pdf-sidebar.pdf-sidebar-visible {
					left: 0;
				}

				.pdf-controls {
					flex-wrap: wrap;
					width: 90%;
					justify-content: center;
					background-color: rgba(0, 0, 0, 0.7);
					padding: 10px;
				}

				.pdf-book.double-page-mode {
					flex-direction: column;
				}

				.pdf-book.double-page-mode .pdf-page {
					width: 100%;
					height: 50%;
				}
				
				.pdf-view-mode-btn {
					display: none;
				}
				
				/* 移动端滑动区域 */
				.swipe-area {
					position: absolute;
					top: 0;
					bottom: 0;
					width: 100%;
					z-index: 20;
				}
				
				.pdf-control-btn {
					padding: 8px 15px; 
					margin: 5px;
				}
				
				/* 移动端目录按钮更明显 */
				#toggle-sidebar {
					background: rgba(215, 28, 28, 0.7) !important;
					border-radius: 5px !important;
					padding: 8px 15px !important;
				}
				
				.topviewsss label {
					font-size: 16px;
					padding: 0 10px;
				}
				
				/* 移动端章节目录优化 */
				.pdf-outline-item {
					padding: 12px 15px;
					border-bottom: 1px solid #eee;
				}
			}
			
			/* 添加滑动翻页区域 */
			.swipe-area {
				position: absolute;
				top: 60px;
				left: 0;
				right: 0;
				bottom: 80px;
				z-index: 10;
			}
			
			.swipe-left, .swipe-right {
				position: absolute;
				top: 0;
				bottom: 0;
				width: 50%;
				z-index: 10;
				cursor: pointer;
			}
			
			.swipe-left {
				left: 0;
			}
			
			.swipe-right {
				right: 0;
			}

			/* 阅读按钮美化 */
			#sjbtn {
				background-color: #d71c1c;
				border: none;
				color: white;
				padding: 12px 30px;
				border-radius: 4px;
				font-size: 16px;
				cursor: pointer;
				transition: all 0.3s;
				display: inline-flex;
				align-items: center;
				justify-content: center;
				box-shadow: 0 2px 5px rgba(215, 28, 28, 0.3);
			}

			#sjbtn:hover {
				background-color: #c51818;
				box-shadow: 0 4px 8px rgba(215, 28, 28, 0.4);
			}

			#sjbtn:before {
				content: "";
				display: inline-block;
				width: 20px;
				height: 20px;
				background: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="white"><path d="M6 2c-1.1 0-2 .9-2 2v16c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V8l-6-6H6zm0 2h7v5h5v11H6V4zm2 8v2h8v-2H8zm0 4v2h5v-2H8z"/></svg>') no-repeat;
				background-size: contain;
				margin-right: 8px;
			}
			
			/* 响应式布局优化 */
			@media screen and (max-width: 768px) {
				.sjinfoview {
					flex-direction: column;
				}
				
				.sjinfoleft, .sjinforight {
					width: 100%;
				}
				
				.sjinfoleft {
					text-align: center;
					margin-bottom: 20px;
				}
				
				.swiper {
					width: 100%;
				}
				
				.swiper-slide {
					width: 90%;
				}
			}
			
			.content {
				overflow: hidden;
				max-width: 100%;
				padding: 0 15px;
			}
			
			footer {
				width: 100%;
				overflow: hidden;
			}

			/* 添加提示消息样式 */
			.pdf-message {
				position: fixed;
				bottom: 80px;
				left: 50%;
				transform: translateX(-50%);
				padding: 10px 20px;
				background-color: rgba(0, 0, 0, 0.7);
				color: white;
				border-radius: 5px;
				z-index: 1000;
				font-size: 14px;
				box-shadow: 0 2px 10px rgba(0, 0, 0, 0.3);
				animation: fadeInOut 3s ease;
				text-align: center;
				min-width: 200px;
			}
			
			.pdf-message.error {
				background-color: rgba(215, 28, 28, 0.9);
			}
			
			@keyframes fadeInOut {
				0% { opacity: 0; transform: translate(-50%, 20px); }
				10% { opacity: 1; transform: translate(-50%, 0); }
				90% { opacity: 1; transform: translate(-50%, 0); }
				100% { opacity: 0; transform: translate(-50%, -20px); }
			}
			
			.pdf-outline-header {
				background-color: #d71c1c;
				color: white;
				padding: 12px 15px;
				font-weight: bold;
				margin-bottom: 10px;
			}
			
			/* 激活的目录项 */
			.pdf-outline-item.active {
				background-color: rgba(215, 28, 28, 0.1);
				border-left-color: #d71c1c !important;
				font-weight: bold;
			}
			
			/* 滚动条美化 */
			.pdf-sidebar::-webkit-scrollbar {
				width: 6px;
			}
			
			.pdf-sidebar::-webkit-scrollbar-track {
				background: #f1f1f1;
			}
			
			.pdf-sidebar::-webkit-scrollbar-thumb {
				background: #d71c1c;
				border-radius: 10px;
			}
		</style>
	</head>
	<body class="index">
		<div class="tcbox" id="tcbox">
			<div class="topviewsss">
				<button id="toggle-sidebar" style="background:none; border:none; color:white; cursor:pointer; padding:5px 10px; margin-right:10px; display:flex; align-items:center;">
					<svg width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
						<path d="M1 1H19M1 8H19M1 15H19" stroke="white" stroke-width="2" stroke-linecap="round"/>
					</svg>
					<span style="margin-left:5px;">目录</span>
				</button>
				<label id="pdfname"></label>
				<a onclick="closetc()">关闭 ×</a>
			</div>
			<div class="panel-body pdfbox">
				<div class="pdf-sidebar" id="pdf-sidebar">
					<div class="pdf-sidebar-header">
						<div class="pdf-sidebar-title">目录</div>
						<div class="pdf-sidebar-close" id="sidebar-close">×</div>
					</div>
					<div class="pdf-outline" id="pdf-outline">
						<!-- 目录内容将由JS动态生成 -->
						<div class="pdf-outline-loading" style="padding:15px; text-align:center;">
							加载目录中...
						</div>
					</div>
				</div>
				
				<div class="pdf-main-container">
					<div class="pdf-loading" id="pdf-loading">
						<div class="pdf-loading-spinner"></div>
						<div class="pdf-loading-text">加载中，请稍候...</div>
						<div class="pdf-loading-progress">
							<div class="pdf-loading-progress-bar"></div>
						</div>
					</div>
					
					<div class="pdf-page-container" id="pdf-container">
						<div class="pdf-book" id="pdf-book">
							<!-- PDF页面将由JS动态加载 -->
						</div>
					</div>
					
					<div class="pdf-controls">
						<button class="pdf-control-btn" id="prev-page">
							<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M10 12L6 8L10 4" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
							上一页
						</button>
						<div class="pdf-page-info" id="page-info">0 / 0</div>
						<button class="pdf-control-btn" id="next-page">
							下一页
							<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
								<path d="M6 4L10 8L6 12" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
							</svg>
						</button>
						<div class="pdf-zoom-controls">
							<button class="pdf-control-btn" id="zoom-out">
								<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M2 8H14" stroke="white" stroke-width="2" stroke-linecap="round"/>
								</svg>
							</button>
							<button class="pdf-control-btn" id="zoom-in">
								<svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
									<path d="M8 2V14" stroke="white" stroke-width="2" stroke-linecap="round"/>
									<path d="M2 8H14" stroke="white" stroke-width="2" stroke-linecap="round"/>
								</svg>
							</button>
						</div>
						<button class="pdf-control-btn pdf-view-mode-btn" id="toggle-view-mode">
							<svg width="20" height="16" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
								<rect x="1" y="1" width="8" height="14" rx="1" stroke="white" stroke-width="1.5"/>
								<rect x="11" y="1" width="8" height="14" rx="1" stroke="white" stroke-width="1.5"/>
							</svg>
						</button>
					</div>
				</div>
			</div>
		</div>
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
					<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>
					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
					<!-- 菜单项将由JS动态生成 -->
				</div>
			</div>
		</div>
		<div class="contenttitlebox">
			<div>
				<a id="zxxx" href="onlinelearning.html">在线学习</a>
				<img src="img/jtright.png" />
				<label class="aa" id="hsname"></label>
			</div>
		</div>
		<div class="content">
			<div class="sjinfoview">
				<div class="sjinfoleft">
					<img id="bookimg" src="" alt="书籍封面" />
				</div>
				<div class="sjinforight">
					<div class="sjname" id="sjname"></div>
					<div class="sjstr">
						<label>
							<img src="img/sjcbs.png" alt="出版社" />
							<span id="cbs"></span>
						</label>
						<label>
							<img src="img/sjfl.png" alt="分类" />
							<span id="fl"></span>
						</label>
						<label>
							<img src="img/sjyj.png" alt="阅读量" />
							<span id="yj"></span>
						</label>
					</div>
					<div class="sjjj" id="sjjj">

					</div>
					<label id="sjbtn" onclick="showpdf()">立即阅读</label>
				</div>
			</div>
			<div class="tjview" id="tjview" style="display: none;">
				<div class="tjtoptitle">
					<span>相关推荐</span>
				</div>
				<div class="pppp">
					<div id="swiperleft">
						<img src="img/sjleft.png" alt="上一页" />
					</div>
					<div class="swiper">
						<div class="swiper-wrapper" id="listbox">

						</div>
					</div>
					<div id="swiperright">
						<img src="img/sjright.png" alt="下一页" />
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" alt="备案图标" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" alt="回到顶部" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/clipboard.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/pdf.min.js"></script>
		<script src="./js/pdf_viewer.min.js"></script>
		<script>
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
					getfooterlink()
					getclass('onlinelearning.html')
					getclassid()
					getinfo()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href ='https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
			})
			let pdfname = null
			let pdfurl = null
			let pdfDoc = null
			let pageNum = 1
			let pageCount = 0
			let currentZoom = 1.0
			let doublePage = false
			let touchStartX = 0;
			let touchEndY = 0;
			let touchStartY = 0;
			let touchEndX = 0;
			let outlineDestinations = {};  // 存储目录项与页码的映射
			let startReadingTime = null; // 添加阅读开始时间变量
			
			// 当文档加载完成时初始化PDF.js
			pdfjsLib.GlobalWorkerOptions.workerSrc = './js/pdf.worker.min.js';
			
			// 检测是否为移动设备
			function isMobile() {
				return window.innerWidth <= 768 || 
					   /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
			}
			
			// 显示提示消息
			function showMessage(message, type = 'info') {
				const messageDiv = document.createElement('div');
				messageDiv.className = 'pdf-message ' + type;
				messageDiv.textContent = message;
				$('.pdf-main-container').append(messageDiv);
				
				setTimeout(function() {
					$(messageDiv).fadeOut(function() {
						$(this).remove();
					});
				}, 3000);
			}

			function closetc() {
				// 关闭前提交最终学习记录
				if(pdfDoc && getUrlParam('id') && startReadingTime) {
					// 计算实际学习时间（秒）
					const endReadingTime = Date.now();
					const actualLearningTime = Math.floor((endReadingTime - startReadingTime) / 1000);
					
					let json = {
						infoId: getUrlParam('id'), //信息id
						categoryId: flid, //所属分类id
						totalInfo: pdfDoc.numPages.toString(), //总页数
						positioning: pageNum.toString(), //当前页码   
						progress: Math.round((pageNum / pdfDoc.numPages) * 100), //进度百分比
						type: '红色书籍',
						learningTime: actualLearningTime, //实际学习时长（秒）
						sectionId: null,
						status: "1",
						taskId: getUrlParam('taskid') || null
					}
					
					$.ajax({
						url: baseurl + "/study/record/add",
						type: 'post',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						data: JSON.stringify(json),
						dataType: 'json',
						async: false // 确保在关闭前完成请求
					});
					
					// 重置开始时间
					startReadingTime = null;
				}

				// 原有关闭逻辑
				$("#tcbox").hide()
				// 清理PDF资源
				if (pdfDoc) {
					// 移除所有页面
					$("#pdf-book").empty();
					pageNum = 1;
				}
				
				// 清除进度更新计时器
				if (window.progressInterval) {
					clearInterval(window.progressInterval);
					window.progressInterval = null;
				}
			}

			// 切换侧边栏
			$(document).on('click', '#toggle-sidebar', function() {
				$("#pdf-sidebar").toggleClass("pdf-sidebar-visible");
			});
			
			$(document).on('click', '#sidebar-close', function() {
				$("#pdf-sidebar").removeClass("pdf-sidebar-visible");
			});
			
			// 切换视图模式（单页/双页）
			$(document).on('click', '#toggle-view-mode', function() {
				doublePage = !doublePage;
				if (doublePage) {
					$("#pdf-book").addClass("double-page-mode");
				} else {
					$("#pdf-book").removeClass("double-page-mode");
				}
				// 刷新当前页面视图
				renderCurrentPage();
			});
			
			// 加载PDF目录
			function loadOutline() {
				// 清空现有目录
				$("#pdf-outline").empty().html('<div class="pdf-outline-loading" style="padding:15px; text-align:center;">加载目录中...</div>');
				
				// 清空目录映射
				outlineDestinations = {};
				
				try {
					// 尝试获取文档目录
					pdfDoc.getOutline().then(function(outline) {
						if (outline && outline.length > 0) {
							// 创建解析目录项到页码的映射函数
							const processOutlineDestinations = function(items) {
								const promises = [];
								
								items.forEach(function(item) {
									if (item.dest) {
										// 处理目标类型为字符串的情况（引用）
										if (typeof item.dest === 'string') {
											const promise = pdfDoc.getDestination(item.dest).then(function(destArray) {
												return pdfDoc.getPageIndex(destArray[0]).then(function(pageIndex) {
													outlineDestinations[item.title] = pageIndex + 1;
													
													// 递归处理子项
													if (item.items && item.items.length > 0) {
														return processOutlineDestinations(item.items);
													}
												});
											});
											promises.push(promise);
										} 
										// 处理目标类型为数组的情况
										else if (Array.isArray(item.dest)) {
											const promise = pdfDoc.getPageIndex(item.dest[0]).then(function(pageIndex) {
												outlineDestinations[item.title] = pageIndex + 1;
												
												// 递归处理子项
												if (item.items && item.items.length > 0) {
													return processOutlineDestinations(item.items);
												}
											});
											promises.push(promise);
										}
									}
									
									// 如果有子项但没有目标，仍然需要处理子项
									if (item.items && item.items.length > 0 && !item.dest) {
										promises.push(processOutlineDestinations(item.items));
									}
								});
								
								return Promise.all(promises);
							};
							
							// 处理所有目录项目标
							processOutlineDestinations(outline).then(function() {
								// console.log("目录映射已创建:", outlineDestinations);
								
								// 创建目录HTML
								const outlineContent = createOutlineHTML(outline);
								$("#pdf-outline").html(outlineContent);
								
								// 绑定点击事件
								$("#pdf-outline .pdf-outline-item").on('click', function() {
									const titleText = $(this).data('title');
									const targetPage = outlineDestinations[titleText];
									
									if (targetPage && targetPage > 0 && targetPage <= pageCount) {
										pageNum = targetPage;
										renderCurrentPage();
										showMessage(`已跳转到第 ${targetPage} 页`);
										
										// 高亮当前项
										$("#pdf-outline .pdf-outline-item").removeClass('active');
										$(this).addClass('active');
										
										// 在移动设备上自动关闭侧边栏
										if (isMobile()) {
											$("#pdf-sidebar").removeClass("pdf-sidebar-visible");
										}
									} else {
										showMessage("无法跳转到指定位置", "error");
									}
								});
							}).catch(function(error) {
								console.error("处理目录目标时出错:", error);
								createPageListSimple();
							});
						} else {
							createPageListSimple();
						}
					}).catch(function(error) {
						console.error("获取目录时出错:", error);
						createPageListSimple();
					});
				} catch (error) {
					console.error("目录处理出错:", error);
					createPageListSimple();
				}
			}
			
			// 创建简单页码列表（当目录获取失败时使用）
			function createPageListSimple() {
				let html = '<div class="pdf-outline-header">页面列表</div>';
				
				for (let i = 1; i <= pageCount; i++) {
					html += `<div class="pdf-outline-item" data-page="${i}" style="padding:12px 15px;">
						第 ${i} 页
					</div>`;
				}
				
				$("#pdf-outline").html(html);
				
				// 绑定页码点击事件
				$("#pdf-outline .pdf-outline-item").on('click', function() {
					const targetPage = parseInt($(this).attr('data-page'));
					if (targetPage > 0 && targetPage <= pageCount) {
						pageNum = targetPage;
						renderCurrentPage();
						
						// 高亮当前项
						$("#pdf-outline .pdf-outline-item").removeClass('active');
						$(this).addClass('active');
						
						// 在移动设备上自动关闭侧边栏
						if (isMobile()) {
							$("#pdf-sidebar").removeClass("pdf-sidebar-visible");
						}
					}
				});
			}
			
			// 创建目录HTML
			function createOutlineHTML(outline, level = 0) {
				let html = level === 0 ? '<div class="pdf-outline-header">文档目录</div>' : '';
				
				outline.forEach(function(item) {
					if (item.title) {
						const paddingLeft = level * 15 + 'px';
						const itemTitle = item.title.replace(/"/g, '&quot;');
						
						html += `<div class="pdf-outline-item" data-title="${itemTitle}" style="padding-left: calc(15px + ${paddingLeft});">
							${item.title}
						</div>`;
						
						// 递归处理子项
						if (item.items && item.items.length > 0) {
							html += createOutlineHTML(item.items, level + 1);
						}
					}
				});
				
				return html;
			}
			
			// 添加鼠标滚轮缩放功能
			$("#pdf-container").on('wheel', function(e) {
				e.preventDefault();
				// 按住Ctrl键才缩放
				if (e.ctrlKey) {
					if (e.originalEvent.deltaY < 0) {
						// 向上滚动 - 放大
						if (currentZoom < 3.0) {
							currentZoom += 0.1;
							applyZoom();
						}
					} else {
						// 向下滚动 - 缩小
						if (currentZoom > 0.5) {
							currentZoom -= 0.1;
							applyZoom();
						}
					}
				}
			});
			
			// 添加触摸滑动翻页功能
			function initSwipeHandlers() {
				// 创建滑动区域
				const swipeArea = document.createElement('div');
				swipeArea.className = 'swipe-area';
				$("#pdf-container").append(swipeArea);
				
				// 触摸开始
				$(swipeArea).on('touchstart', function(e) {
					touchStartX = e.originalEvent.touches[0].clientX;
					touchStartY = e.originalEvent.touches[0].clientY;
				});
				
				// 触摸结束
				$(swipeArea).on('touchend', function(e) {
					touchEndX = e.originalEvent.changedTouches[0].clientX;
					touchEndY = e.originalEvent.changedTouches[0].clientY;
					handleSwipe();
				});
				
				// 添加左右区域点击翻页
				const swipeLeft = document.createElement('div');
				swipeLeft.className = 'swipe-left';
				
				const swipeRight = document.createElement('div');
				swipeRight.className = 'swipe-right';
				
				$(swipeArea).append(swipeLeft);
				$(swipeArea).append(swipeRight);
				
				// 点击左侧区域 - 上一页
				$(swipeLeft).on('click', function(e) {
					// 避免与双击缩放冲突
					if (!isMobile()) {
						prevPage();
					}
				});
				
				// 点击右侧区域 - 下一页
				$(swipeRight).on('click', function(e) {
					// 避免与双击缩放冲突
					if (!isMobile()) {
						nextPage();
					}
				});
				
				// 双击缩放
				$(swipeArea).on('dblclick', function(e) {
					if (currentZoom === 1.0) {
						currentZoom = 1.5;
					} else {
						currentZoom = 1.0;
					}
					applyZoom();
				});
			}
			
			// 处理滑动手势
			function handleSwipe() {
				const swipeThresholdX = 50;
				const swipeThresholdY = 50;
				const deltaX = touchEndX - touchStartX;
				const deltaY = touchEndY - touchStartY;
				
				// 确保水平滑动幅度大于垂直滑动，避免与页面滚动冲突
				if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > swipeThresholdX) {
					if (deltaX > 0) {
						// 右滑 - 上一页
						prevPage();
					} else {
						// 左滑 - 下一页
						nextPage();
					}
				}
			}
			
			// 上一页函数
			function prevPage() {
				if (pageNum <= 1) return;
				
				if (doublePage) {
					pageNum = Math.max(1, pageNum - 2);
				} else {
					pageNum--;
				}
				renderCurrentPage();
				updatePageInfo();
			}
			
			// 下一页函数
			function nextPage() {
				if (pageNum >= pageCount) return;
				
				if (doublePage) {
					pageNum = Math.min(pageCount, pageNum + 2);
				} else {
					pageNum++;
				}
				renderCurrentPage();
				updatePageInfo();
			}
			
			function applyZoom() {
				$(".pdf-page canvas").css({
					"transform": `scale(${currentZoom})`,
					"transform-origin": "center"
				});
			}
			
			// 页面导航
			$(document).on('click', '#prev-page', function() {
				prevPage();
			});
			
			$(document).on('click', '#next-page', function() {
				nextPage();
			});
			
			// 缩放控制
			$(document).on('click', '#zoom-in', function() {
				if (currentZoom < 3.0) {
					currentZoom += 0.1;
					applyZoom();
				}
			});
			
			$(document).on('click', '#zoom-out', function() {
				if (currentZoom > 0.5) {
					currentZoom -= 0.1;
					applyZoom();
				}
			});
			
			// 更新页码信息
			function updatePageInfo() {
				$("#page-info").text(`${pageNum} / ${pageCount}`);
				
				// 更新目录中的活动项（通过页码查找）
				$("#pdf-outline .pdf-outline-item").removeClass('active');
				
				// 遍历所有目录项，找到匹配当前页码的项
				for (const [title, page] of Object.entries(outlineDestinations)) {
					if (page === pageNum) {
						$(`#pdf-outline .pdf-outline-item[data-title="${title.replace(/"/g, '&quot;')}"]`).addClass('active');
						break;
					}
				}
				
				// 如果是简单页码列表，高亮当前页
				$(`#pdf-outline .pdf-outline-item[data-page="${pageNum}"]`).addClass('active');
			}
			
			// 渲染当前页面
			function renderCurrentPage() {
				$("#pdf-book").empty();
				
				if (doublePage) {
					// 双页模式，一次渲染两页
					renderDoublePage();
				} else {
					// 单页模式
					renderPage(pageNum);
				}
				
				updatePageInfo();
			}
			
			// 双页模式渲染
			function renderDoublePage() {
				// 如果是双页模式且当前页是偶数，则从前一页开始显示
				const startPage = pageNum % 2 === 0 ? pageNum - 1 : pageNum;
				
				// 渲染左侧页面（奇数页）
				if (startPage >= 1 && startPage <= pageCount) {
					renderPage(startPage);
				}
				
				// 渲染右侧页面（偶数页）
				if (startPage + 1 <= pageCount) {
					renderPage(startPage + 1);
				}
			}
			
			// 渲染单个页面
			function renderPage(num) {
				// 创建页面容器
				const pageDiv = document.createElement('div');
				pageDiv.className = 'pdf-page';
				pageDiv.dataset.pageNum = num;
				
				// 添加到书籍容器
				$("#pdf-book").append(pageDiv);
				
				// 使用PDF.js渲染页面
				pdfDoc.getPage(num).then(function(page) {
					// 计算适合显示的尺寸
					const containerWidth = doublePage ? 
						$(".pdf-main-container").width() / 2 : 
						$(".pdf-main-container").width();
					const containerHeight = doublePage && isMobile() ? 
						$(".pdf-main-container").height() / 2 : 
						$(".pdf-main-container").height();
					
					// 计算缩放比例以适应容器
					const viewport = page.getViewport({ scale: 1.0 });
					const scaleX = containerWidth / viewport.width * 0.95; // 增加比例，减少空隙
					const scaleY = containerHeight / viewport.height * 0.95;
					const scale = Math.min(scaleX, scaleY);
					
					// 使用计算出的缩放创建新视口
					const scaledViewport = page.getViewport({ scale: scale });
					
					// 创建Canvas
					const canvas = document.createElement('canvas');
					const context = canvas.getContext('2d');
					canvas.height = scaledViewport.height;
					canvas.width = scaledViewport.width;
					
					// 渲染PDF页面到Canvas
					const renderContext = {
						canvasContext: context,
						viewport: scaledViewport
					};
					
					// 将Canvas添加到页面容器
					$(pageDiv).append(canvas);
					
					// 渲染PDF内容
					page.render(renderContext);
				});
			}
			
			// 加载PDF文档
			function loadPDF(url) {
				$("#pdf-loading").show();
				$("#pdf-container").hide();
				
				const loadingTask = pdfjsLib.getDocument(url);
				
				// 添加进度显示
				loadingTask.onProgress = function(progressData) {
					if (progressData.total > 0) {
						const percent = Math.round(progressData.loaded / progressData.total * 100);
						$(".pdf-loading-progress-bar").css("width", percent + "%");
						$(".pdf-loading-text").text(`加载中...${percent}%`);
					}
				};
				
				// 加载PDF文档
				loadingTask.promise.then(function(pdf) {
					pdfDoc = pdf;
					pageCount = pdf.numPages;
					pageNum = 1;
					
					// 初始化页面导航
					$("#page-info").text(`${pageNum} / ${pageCount}`);
					
					// 加载文档大纲/目录
					setTimeout(function() {
						loadOutline();
					}, 300);
					
					// 渲染第一页
					renderCurrentPage();
					
					// 初始化滑动翻页
					initSwipeHandlers();
					
					// 隐藏加载提示，显示内容
					setTimeout(function() {
						$("#pdf-loading").hide();
						$("#pdf-container").show();
						$(".pdf-loading-progress-bar").css("width", "0%");
					}, 500);
					
				}).catch(function(error) {
					console.error("PDF加载错误:", error);
					$("#pdf-loading-text").text("PDF加载失败，请重试");
					
					// 5秒后自动关闭
					setTimeout(function() {
						closetc();
					}, 5000);
				});
			}

			function showpdf() {
				// 检查是否有PDF可阅读
				if (!pdfurl) {
					console.error("没有PDF可阅读");
					showMessage("没有可阅读的PDF文件", "error");
					return;
				}
				
				$("#pdfname").html(pdfname)
				$("#tcbox").show()
				$("#pdf-loading").show()
				$("#pdf-container").hide()
				$("#pdf-book").empty()
				
				// 重置缩放
				currentZoom = 1.0;
				
				// 默认设置为单页模式
				doublePage = false;
				$("#pdf-book").removeClass("double-page-mode");
				
				// 记录开始阅读时间
				startReadingTime = Date.now();
				
				// 加载PDF文档
				loadPDF(pdfurl);
				
				// 记录阅读状态
				if(getUrlParam('taskid')){
					let json = {
						infoId: getUrlParam('id'), //信息id
						categoryId: flid, //所属分类id
						totalInfo: "1", //总时长，总页数
						positioning: "", //学习了多久，多少页    
						progress: 0, //进度 百分比
						type: '红色书籍',
						learningTime: 0,
						sectionId: null,//学科ID
						status: "1",
						taskId: getUrlParam('taskid')
					}
					$.ajax({
						url: baseurl + "/study/record/add",
						type: 'post',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						data: JSON.stringify(json),
						dataType: 'json',
						success: function(res) {
							// console.log("学习记录已保存");
						},
						error: function(err) {
							console.error("学习记录添加失败", err);
						}
					})
				}
				
				// 定期更新学习进度
				if (window.progressInterval) {
					clearInterval(window.progressInterval);
				}
				
				// 每60秒更新一次学习进度
				if(getUrlParam('taskid')) {
					window.progressInterval = setInterval(function() {
						updateLearningProgress();
					}, 60000);
				}
			}

			// 更新学习进度
			function updateLearningProgress() {
				if(!getUrlParam('taskid') || !pdfDoc) return;
				
				let json = {
					infoId: getUrlParam('id'), //信息id
					categoryId: flid, //所属分类id
					totalInfo: pdfDoc.numPages.toString(), //总页数
					positioning: pageNum.toString(), //当前第几页   
					progress: Math.round((pageNum / pdfDoc.numPages) * 100), //进度 百分比
					type: '红色书籍',
					learningTime: 60, //每分钟更新一次，增加60秒
					sectionId: null,//学科ID
					status: "1",
					taskId: getUrlParam('taskid')
				}
				
				$.ajax({
					url: baseurl + "/study/record/add",
					type: 'post',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: JSON.stringify(json),
					dataType: 'json',
					success: function(res) {
						// console.log("学习进度已更新");
					},
					error: function(err) {
						console.error("学习进度更新失败", err);
					}
				})
			}

			function getlist(classid, flid) {
				$.ajax({
					url: baseurl + "/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						categoryId: classid,
						pageSize: 10,
						redBookId: flid,
						pageNum: 1
					},
					dataType: 'json',
					success: function(res) {
						if (res.code == '200') {
							if (res.data.list.length < 2) {
								$("#tjview").hide()
							} else {
								let newhtml = ""
								res.data.list.map((item) => {
									newhtml +=
										'<div class="swiper-slide"><a href="onlinelearning5.html?id=' + item
										.id + '"><img src="' + baseurl + item.thumbPath[0] +
										'" alt="' + item.title + '"/><div class="sm">' + item.title + '</div></a></div>'
								})
								$("#listbox").html(newhtml)
								var mySwiper = new Swiper('.swiper', {
									loop: res.data.list.length > 5,
									slidesPerView: 5,
									centeredSlides: true,
									spaceBetween: 24,
									navigation: {
										nextEl: '#swiperright',
										prevEl: '#swiperleft',
									},
									breakpoints: {
										320: {
											slidesPerView: 1,
											spaceBetween: 10
										},
										480: {
											slidesPerView: 2,
											spaceBetween: 20
										},
										768: {
											slidesPerView: 3,
											spaceBetween: 20
										},
										992: {
											slidesPerView: 4,
											spaceBetween: 20
										},
										1200: {
											slidesPerView: 5,
											spaceBetween: 24
										}
									},
									autoplay: {
										delay: 3000,
										disableOnInteraction: false
									}
								})
								mySwiper.el.onmouseover = function() {
									mySwiper.autoplay.stop();
								}
								mySwiper.el.onmouseout = function() {
									mySwiper.autoplay.start();
								}
								$("#tjview").show()
							}
						}
					},
					error: function(err) {
						console.error("获取推荐列表失败");
						$("#tjview").hide();
					}
				})
			}
			function getinfo() {
				$.ajax({
					url: baseurl + "/posts/" + getUrlParam('id'),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: function(res) {
						if (res.code == '200') {
							let items = res.data
							pflid = items.cmsCategoryList[0].parentId
							$("#sjname").html(items.title + '<span>' + items.author + '&ensp;&ensp;著</span>')
							$("#bookimg").attr("src", baseurl + items.thumbPath[0])
							$("#cbs").html(items.postName)
							$("#fl").html(items.redBookName)
							$("#yj").html(items.clickCount)
							$("#sjjj").html(items.excerpt)
							if (items.attachmentDtoList && items.attachmentDtoList.length > 0) {
								pdfname = items.attachmentDtoList[0].fileName
								pdfurl = baseurl + items.attachmentDtoList[0].attachmentPath
								// 启用阅读按钮
								$("#sjbtn").text("立即阅读").prop("disabled", false).css("opacity", 1);
							} else {
								// 没有PDF文件，禁用阅读按钮
								$("#sjbtn").text("暂无PDF").prop("disabled", true).css("opacity", 0.5);
							}
							clicknum(items.id)
							getlist(items.cmsCategoryList[0].id, items.redBookId)
						}
					},
					error: function(err) {
						console.error("获取详情失败");
						// 显示错误提示
						$("#sjjj").html("<p>获取内容失败，请刷新重试</p>");
						// 禁用阅读按钮
						$("#sjbtn").text("加载失败").prop("disabled", true).css("opacity", 0.5);
					}
				})
			}

			function clicknum(id) {
				$.ajax({
					url: baseurl + "/posts/click/" + id,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: function() {
						// 点击计数成功
					},
					error: function() {
						// 点击计数失败，不影响用户体验，静默处理
					}
				})
			}

			function getclassid() {
				$.ajax({
					url: baseurl + "/web/category/book",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: function(res) {
						if (res.code == '200') {
							$("#hsname").html(res.data[0].name)
							flid = res.data[0].id
						}
					},
					error: function() {
						$("#hsname").html("红色书籍")
					}
				})
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: function(res) {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					},
					error: function() {
						$("#linkbox").html("")
					}
				})
			}

			function getclass(redirect) {
				// 首先获取所有顶级菜单
				$.ajax({
					url: baseurl + "/web/category/all",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: function(res) {
						if (res.code == '200') {
							// 获取所有菜单数据
							let menuItems = res.data;
							// console.log("顶级菜单获取成功:", menuItems);
							
							// 生成菜单HTML
							let html = "";
							for (let i = 0; i < menuItems.length; i++) {
								const item = menuItems[i];
								const isActive = item.id == getUrlParam('id') || item.redirectUrl == redirect;
								
								if (isActive) {
									$("#zxxx").attr('href', redirect + "?id=" + item.id);
									$("#zxxx").html(item.name);
								}
								
								// 创建菜单项
								html += '<div class="menuitemaaa" id="menu-item-' + item.id + '">';
								
								// 菜单文本区域
								html += '<div class="menuaaaa' + (isActive ? ' active' : '') + '" data-href="' + item.redirectUrl + '?id=' + item.id + '">' + item.name + '</div>';
								
								// 只有当item.children存在且不为空时才添加子菜单
								if (item.children && item.children.length > 0) {
									html += '<div class="itemcboxaaa">';
									// 添加子菜单项
									item.children.forEach(function(subItem) {
										html += '<a href="' + subItem.redirectUrl + '?id=' + subItem.id + '" class="itema2aaa">' + subItem.name + '</a>';
									});
									html += '</div>'; // 关闭子菜单容器
								} else {
									// 没有子菜单则添加空的子菜单容器（隐藏）
									html += '<div class="itemcboxaaa" style="display:none;"></div>';
								}
								
								html += '</div>'; // 关闭菜单项
							}
							
							// 渲染菜单HTML
							$("#menubox").html(html);
							// console.log("渲染菜单HTML完成");
							
							// 添加主菜单项点击事件
							$(".menuaaaa").on("click", function() {
								const href = $(this).attr("data-href");
								if (href) {
									window.location.href = href;
								}
							});
							
							// 确保菜单容器样式正确
							$("#menubox").css({
								"display": "flex",
								"height": "100%"
							});
							
							// 确保CSS样式正确应用
							$(".menuitemaaa").css({
								"position": "relative",
								"height": "100%"
							});
							
							$(".itemcboxaaa").css({
								"position": "absolute", 
								"top": "100%",
								"left": "0",
								"width": "100%",
								"background": "rgb(126,8,11)",
								"padding": "0.625rem",
								"z-index": "999"
							});
							
							// 添加明确的事件处理，而不仅依赖CSS
							$(".menuitemaaa").hover(
								function() {
									$(this).find(".itemcboxaaa").show();
								}, 
								function() {
									$(this).find(".itemcboxaaa").hide();
								}
							);
						} else {
							console.error("顶级菜单获取返回错误码:", res.code);
						}
					},
					error: function(err) {
						console.error("获取菜单分类失败:", err);
					}
				});
			}

			// 只渲染主菜单，无子菜单
			function renderMainMenuOnly(menuItems, redirect) {
				// 此函数不再需要，但保留代码兼容性
			}

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
