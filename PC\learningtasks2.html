<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-学习任务管理</title>
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<!-- 导航增强脚本 -->
		<script src="./js/nav-enhancement.js" type="text/javascript" charset="utf-8"></script>
		<style>
			:root {
				--primary-color: #c41e3a;
				--primary-hover: #a01a31;
				--secondary-color: #666;
				--light-bg: #f8f9fa;
				--border-color: #e0e0e0;
				--success-color: #28a745;
				--warning-color: #ffc107;
				--box-shadow: 0 2px 8px rgba(0,0,0,0.1);
				--transition: all 0.3s ease;
				--border-radius: 4px;
				--font-family: "PingFang SC", "Microsoft YaHei", "Helvetica Neue", Arial, sans-serif;
				--text-color: #333;
			}
			
			body, html {
				font-family: var(--font-family);
				color: var(--text-color);
				line-height: 1.5;
			}
			
			/* 表单控件样式 */
			input, select, textarea {
				width: 100%;
				padding: 8px 12px;
				border: 1px solid var(--border-color);
				border-radius: var(--border-radius);
				transition: var(--transition);
				font-size: 14px;
				background-color: white;
				font-family: var(--font-family);
				color: var(--text-color);
				height: 36px;
				box-sizing: border-box;
			}
			
			input:focus, select:focus, textarea:focus {
				border-color: var(--primary-color);
				box-shadow: 0 0 0 3px rgba(196, 30, 58, 0.1);
				outline: none;
			}
			
			input[type="date"] {
				padding-right: 8px;
			}
			
			select {
				appearance: none;
				background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" width="12" height="6"><path d="M0 0L6 6L12 0Z" fill="%23666"/></svg>');
				background-repeat: no-repeat;
				background-position: right 10px center;
				padding-right: 28px;
				cursor: pointer;
			}
			
			/* 布局辅助类 */
			.form-col {
				margin-top: 5px;
			}
			
			.form-group:last-child {
				margin-bottom: 0;
			}
			
			.form-label {
				display: block;
				margin-bottom: 6px;
				font-weight: 500;
				color: var(--secondary-color);
				font-size: 14px;
			}
			
			/* 带星号的必填项标题加粗显示 - 兼容性更好的写法 */
			.form-label-required {
				font-weight: 700 !important;
				color: var(--text-color) !important;
			}
			
			.form-required {
				color: var(--primary-color);
				margin-left: 4px;
			}
			
			.form-row {
				display: flex;
				flex-wrap: wrap;
				margin: 0 -8px 10px -8px;
				align-items: center;
			}
			
			.form-row:last-child {
				margin-bottom: 0;
			}
			
			.form-col {
				padding: 0 8px;
				flex: 1;
				min-width: 0;
			}
			
			/* 表单卡片样式 */
			.form-card {
				background-color: white;
				border-radius: var(--border-radius);
				box-shadow: var(--box-shadow);
				padding: 20px;
				margin-bottom: 20px;
				transition: var(--transition);
			}
			
			.card-header {
				display: flex;
				align-items: center;
				margin-bottom: 16px;
				padding-bottom: 12px;
				border-bottom: 1px solid var(--border-color);
			}
			
			.card-header h3 {
				margin: 0;
				font-size: 16px;
				color: var(--primary-color);
				display: flex;
				align-items: center;
				font-weight: 600;
			}
			
			.card-header h3 i {
				margin-right: 8px;
				font-size: 16px;
			}
			
			/* 紧凑型权重设置区域 */
			.weight-section {
				display: flex;
				align-items: center;
				margin-bottom: 12px;
			}
			
			.weight-title {
				width: 30%;
				margin-right: 10px;
				font-weight: 500;
				font-size: 14px;
				color: var(--secondary-color);
			}
			
			.weight-input {
				width: 70%;
			}
			
			.weight-note {
				font-size: 13px;
				color: var(--secondary-color);
				margin-top: 8px;
				padding: 8px 10px;
				background-color: rgba(196, 30, 58, 0.05);
				border-radius: var(--border-radius);
				display: flex;
				align-items: flex-start;
			}
			
			.weight-note img {
				margin-right: 8px;
				margin-top: 2px;
				width: 14px;
			}
			
			/* 班级选择区域优化 */
			.class-select-container {
				margin-top: 12px;
			}
			
			.class-select-grid {
				display: flex;
				margin: 0 -8px;
			}
			
			.class-select-col {
				padding: 0 8px;
				width: 33.333333%;
			}
			
			.class-select-header {
				margin-bottom: 6px;
				font-weight: 500;
				font-size: 14px;
				color: var(--secondary-color);
			}
			
			.class-select-box {
				height: 200px;
				overflow-y: auto;
				border: 1px solid var(--border-color);
				border-radius: var(--border-radius);
				padding: 4px;
			}
			
			.class-item {
				padding: 6px 8px;
				margin-bottom: 4px;
				border-radius: var(--border-radius);
				background-color: var(--light-bg);
				cursor: pointer;
				transition: var(--transition);
				font-size: 13px;
			}
			
			.class-item:hover {
				background-color: rgba(196, 30, 58, 0.1);
			}
			
			.class-item.active {
				background-color: var(--primary-color);
				color: white;
			}
			
			/* 搜索输入框 */
			.search-wrapper {
				position: relative;
				margin-bottom: 12px;
				width: 100%;
				display: flex;
				align-items: center;
			}
			
			.search-input {
				height: 36px;
				padding: 0 32px 0 12px;
				border: 1px solid var(--border-color);
				border-radius: var(--border-radius);
				background-color: white;
				font-size: 14px;
				color: var(--text-color);
				transition: var(--transition);
				flex: 1;
			}
			
			.search-input:focus {
				border-color: var(--primary-color);
				box-shadow: 0 0 0 3px rgba(196, 30, 58, 0.1);
				outline: none;
			}
			
			.search-icon {
				position: absolute;
				right: 148px;
				top: 50%;
				transform: translateY(-50%);
				width: 16px;
				height: 16px;
				pointer-events: none;
				opacity: 0.6;
			}
			
			/* 按钮样式 */
			.all-class-btn {
				padding: 0 14px;
				height: 36px;
				background-color: var(--primary-color);
				color: white;
				border: none;
				border-radius: var(--border-radius);
				cursor: pointer;
				font-size: 14px;
				transition: var(--transition);
				white-space: nowrap;
				margin-left: 8px;
				font-family: var(--font-family);
			}
			
			.all-class-btn:hover {
				background-color: var(--primary-hover);
			}
			
			/* 清空选择按钮样式 */
			.clear-select-btn {
				padding: 4px 10px;
				margin-left: 8px;
				background-color: #f5f5f5;
				color: #666;
				border: 1px solid #ddd;
				border-radius: var(--border-radius);
				
				cursor: pointer;
				font-size: 13px;
				transition: var(--transition);
				font-family: var(--font-family);
			}
			
			.clear-select-btn:hover {
				background-color: #e0e0e0;
				color: var(--primary-color);
			}
			
			/* 已选择班级样式 */
			.yxzbj {
				margin-top: 12px;
			}
			
			.yxz {
				display: flex;
				align-items: center;
				margin-bottom: 8px;
				font-weight: 500;
				font-size: 14px;
				color: var(--secondary-color);
			}
			
			#yxz {
				display: flex;
				flex-wrap: wrap;
				gap: 8px;
				margin-bottom: 12px;
			}
			
			#yxz label {
				display: flex;
				align-items: center;
				padding: 4px 10px;
				background-color: rgba(196, 30, 58, 0.08);
				color: var(--primary-color);
				border-radius: 50px;
				font-size: 13px;
			}
			
			#yxz label span {
				display: inline-block;
				width: 16px;
				height: 16px;
				margin-left: 6px;
				background-image: url('img/closered.png');
				background-size: contain;
				cursor: pointer;
				opacity: 0.7;
				transition: var(--transition);
			}
			
			#yxz label span:hover {
				opacity: 1;
			}
			
			/* 紧凑型资源选择区域 */
			.resource-section {
				margin-bottom: 20px;
			}
			
			.resource-card {
				margin-bottom: 15px;
				width: 100%;
				background-color: #F7F8FA;
				border-radius: 4px;
				box-shadow: var(--box-shadow);
			}
			
			/* 资源卡片标题加粗 */
			.resource-header {
				padding: 10px 12px;
				font-weight: 700;
				font-size: 14px;
				color: var(--text-color);
				border-bottom: 1px solid var(--border-color);
			}
			
			.resource-body {
				display: flex;
				gap: 15px;
				padding: 12px;
			}
			
			.resource-left {
				flex: 2;
				position: relative;
			}
			
			.resource-right {
				flex: 1;
				border-left: 1px solid var(--border-color);
				padding-left: 15px;
			}
			
			.input-search {
				position: relative;
				margin-bottom: 0;
			}
			
			.input-search input {
				padding-right: 30px;
			}
			
			.input-search img {
				position: absolute;
				right: 10px;
				top: 50%;
				transform: translateY(-50%);
				opacity: 0.6;
				width: 16px;
				height: 16px;
			}
			
			.newiitembox {
				position: absolute;
				width: 100%;
				top: 100%;
				left: 0;
				border: 1px solid var(--border-color);
				border-radius: 0 0 var(--border-radius) var(--border-radius);
				background-color: white;
				box-shadow: var(--box-shadow);
				z-index: 20;
				display: none;
				margin-top: 0;
			}
			
			.search-results {
				position: relative;
				z-index: 10;
			}
			
			.resource-selected-header {
				display: flex;
				align-items: center;
				font-weight: 500;
				margin-bottom: 6px;
				color: var(--secondary-color);
				font-size: 13px;
			}
			
			.resource-selected-header::before {
				content: '';
				display: inline-block;
				width: 3px;
				height: 12px;
				background-color: var(--primary-color);
				margin-right: 6px;
				border-radius: 2px;
			}
			
			.resource-selected-list {
				border: 1px solid var(--border-color);
				border-radius: var(--border-radius);
				min-height: 150px;
				max-height: 210px;
				overflow-y: auto;
				padding: 4px;
				background-color: white;
			}
			
			/* 教学评价样式 */
			.checks-container {
				margin-top: 12px;
			}
			
			/* 提交按钮区域 */
			.rwbtnview {
				display: flex;
				justify-content: center;
				margin-top: 24px;
				margin-bottom: 16px;
			}
			
			.rwbtnview > div {
				padding: 10px 28px;
				background-color: var(--primary-color);
				color: white;
				border-radius: var(--border-radius);
				cursor: pointer;
				transition: var(--transition);
				font-size: 15px;
				font-weight: 500;
				font-family: var(--font-family);
			}
			
			.rwbtnview > div:hover {
				background-color: var(--primary-hover);
			}
			
			/* 多选相关样式 */
			.multi-select-header {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 8px 12px;
				background-color: var(--light-bg);
				border-bottom: 1px solid var(--border-color);
				font-size: 13px;
				position: sticky;
				top: 0;
				z-index: 10;
				box-shadow: 0 1px 2px rgba(0,0,0,0.05);
				border-radius: var(--border-radius) var(--border-radius) 0 0;
			}
			
			.multi-select-header label {
				display: flex;
				align-items: center;
				cursor: pointer;
			}
			
			.multi-select-header input[type="checkbox"] {
				margin-right: 6px;
				width: 16px;
				height: 16px;
			}
			
			.batch-add-btn {
				padding: 4px 10px;
				background-color: var(--primary-color);
				color: white;
				border: none;
				border-radius: var(--border-radius);
				cursor: pointer;
				font-size: 13px;
				transition: var(--transition);
				font-family: var(--font-family);
			}
			
			.batch-add-btn:hover {
				background-color: var(--primary-hover);
			}
			
			.multi-select-item {
				padding: 8px 10px;
				border-bottom: 1px solid rgba(0,0,0,0.05);
				transition: var(--transition);
			}
			
			.multi-select-item:hover {
				background-color: rgba(0,0,0,0.02);
			}
			
			.multi-select-item:last-child {
				border-bottom: none;
			}
			
			.multi-select-item label {
				display: flex;
				align-items: center;
				cursor: pointer;
				width: 100%;
				font-size: 13px;
			}
			
			.multi-select-item input[type="checkbox"] {
				margin-right: 8px;
				min-width: 16px;
				min-height: 16px;
				width: auto;
			}
			
			/* 单选钮样式 */
			.checks {
				display: flex;
				align-items: center;
			}
			
			.rwcc {
				display: flex;
				align-items: center;
				margin-right: 20px;
				font-size: 14px;
			}
			
			.rwcc div {
				width: 18px;
				height: 18px;
				border-radius: 50%;
				border: 1px solid var(--border-color);
				margin-right: 6px;
				position: relative;
				cursor: pointer;
				background-color: white;
			}
			
			.rwcc div label {
				width: 10px;
				height: 10px;
				border-radius: 50%;
				background-color: var(--primary-color);
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
			}
			
			/* 隐藏框 */
			.closediv {
				display: flex;
				justify-content: flex-end;
				padding: 4px 8px;
				background-color: var(--light-bg);
				border-bottom: 1px solid var(--border-color);
			}
			
			.closediv img {
				cursor: pointer;
				opacity: 0.6;
				transition: var(--transition);
				width: 16px;
				height: 16px;
			}
			
			.closediv img:hover {
				opacity: 1;
			}
			
			.scrollview {
				max-height: 240px;
				overflow-y: auto;
				padding: 0;
			}
			
			.errmsg {
				padding: 12px 16px;
				text-align: center;
				color: var(--secondary-color);
				font-size: 13px;
			}
			.newzyitem {
				padding: 8px 10px;
				border-bottom: 1px solid rgba(0,0,0,0.05);
				cursor: pointer;
			}
				
			.newzyitem:hover {
				background-color: rgba(0,0,0,0.02);
			}
			
			/* 美化的单选按钮样式 */
			.beautiful-radio {
				display: flex;
				align-items: center;
				margin-right: 20px;
				font-size: 14px;
			}
			
			.beautiful-radio input[type="radio"] {
				display: none;
			}
			
			.beautiful-radio label {
				position: relative;
				display: flex;
				align-items: center;
				cursor: pointer;
			}
			
			.beautiful-radio label::before {
				content: '';
				display: inline-block;
				width: 18px;
				height: 18px;
				margin-right: 8px;
				border: 2px solid var(--border-color);
				border-radius: 50%;
				transition: var(--transition);
			}
			
			.beautiful-radio input[type="radio"]:checked + label::before {
				border-color: var(--primary-color);
				background-color: white;
			}
			
			.beautiful-radio input[type="radio"]:checked + label::after {
				content: '';
				position: absolute;
				top: 7px;
				left: 7px;
				width: 8px;
				height: 8px;
				border-radius: 50%;
				background: var(--primary-color);
			}
			
			.beautiful-radio label:hover::before {
				border-color: var(--primary-color);
			}
			
			/* 导航菜单容器 */
			.leftitembox {
				background: transparent;
				padding: 20px 0;
				position: relative;
				z-index: 2;
				min-height: 450px; /* 确保导航栏最小高度一致 */
				box-sizing: border-box;
			}
			
			.leftitembox::before {
				content: '';
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 85%;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.15), transparent);
			}
			
			/* 导航菜单项 - 现代化设计与图标，移除slideIn动画 */
			.leftitem {
				height: auto !important;
				padding: 16px 24px !important;
				display: flex !important;
				justify-content: flex-start !important;
				align-items: center !important;
				font-size: 15px !important;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif !important;
				font-weight: 500 !important;
				color: #4a5568 !important;
				border-bottom: none !important;
				text-decoration: none !important;
				transition: color 0.2s ease, background-color 0.2s ease !important;
				position: relative !important;
				margin: 4px 16px !important;
				border-radius: 12px !important;
				background: transparent !important;
				overflow: hidden !important;
				min-height: 48px !important;
				box-sizing: border-box !important;
				/* 移除slideIn动画，导航项立即显示 */
				animation: none !important;
				opacity: 1 !important;
				transform: translateX(0) !important;
				animation-delay: 0s !important;
			}
			
			/* 为不同菜单项添加对应的SVG图标 */
			.leftitem::before {
				content: '';
				width: 20px;
				height: 20px;
				margin-right: 12px;
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				opacity: 0.7;
				transition: opacity 0.2s ease;
				flex-shrink: 0;
			}
			
			/* 个人信息图标 */
			.leftitem[href*="userinfo"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
			}
			
			/* 学生成绩统计图标 */
			.leftitem[href*="studentachievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z'/%3E%3C/svg%3E");
			}
			
			/* 学习记录统计图标 */
			.leftitem[href*="studentlearning"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z'/%3E%3C/svg%3E");
			}
			
			/* 考试管理图标 */
			.leftitem[href*="examination"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E");
			}
			
			/* 学习任务管理图标 - 激活状态 */
			.leftitem[href*="learning"]:not(.activeleftitem)::before,
			.leftitem[href*="tasks"]:not(.activeleftitem)::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
			}
			
			/* 成果展示图标 */
			.leftitem[href*="achievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E");
			}
			
			/* 发布心声图标 */
			.leftitem[href*="voice"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z'/%3E%3C/svg%3E");
			}
			
			/* 菜单项悬浮态 */
			.leftitem:hover {
				background: linear-gradient(135deg, rgba(220, 53, 69, 0.06), rgba(231, 76, 60, 0.08)) !important;
				color: #dc3545 !important;
			}
			
			.leftitem:hover::before {
				opacity: 1;
			}
			
			/* 激活状态的菜单项 - 统一标准双色渐变背景 */
			.leftitem.activeleftitem,
			a.leftitem.activeleftitem,
			.leftitembox .leftitem.activeleftitem,
			#teambox .leftitem.activeleftitem {
				font-weight: 700 !important;
				color: white !important;
				background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
				background-size: 200% 200% !important;
				animation: activeGradient 3s ease infinite !important;
				box-shadow: 0 4px 16px rgba(220, 53, 69, 0.25) !important;
				border-radius: 12px !important;
				opacity: 1 !important;
				transform: translateX(0) !important;
			}
			
			@keyframes activeGradient {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.leftitem.activeleftitem::before,
			a.leftitem.activeleftitem::before,
			.leftitembox .leftitem.activeleftitem::before,
			#teambox .leftitem.activeleftitem::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E") !important;
				opacity: 1 !important;
				filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
			}
			
			/* 移除所有导航项的动画延迟 */
			.leftitem:nth-child(1),
			.leftitem:nth-child(2), 
			.leftitem:nth-child(3), 
			.leftitem:nth-child(4), 
			.leftitem:nth-child(5), 
			.leftitem:nth-child(6), 
			.leftitem:nth-child(7) { 
				animation: none !important;
				animation-delay: 0s !important;
				opacity: 1 !important;
				transform: translateX(0) !important;
			}
		</style>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a href="studentachievement.html" class="leftitem">学生成绩统计</a>
						<a href="studentlearning.html" class="leftitem">学习记录统计</a>
						<a href="userexamination2.html" class="leftitem">考试管理</a>
						<a class="leftitem activeleftitem">学习任务管理</a>
						<a href="achievements.html" class="leftitem">成果展示</a>
						<a href="releaseyourvoice2.html" class="leftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="userinfotopdiv">
						<a href="learningtasks.html">任务列表</a>
						<a class="acccccg">发布任务</a>
					</div>
					<div class="paperscroll">
						<!-- 任务基本信息和权限设置卡片 -->
						<div class="form-card">
							<div class="card-header">
								<h3><i class="fa">📝</i> 任务基本设置</h3>
						</div>

							<!-- 任务标题和教学评价在同一行 -->
							<div class="form-row">
								<div class="form-col" style="width:50%;">
									<div class="form-group">
										<label class="form-label form-label-required">任务标题<span class="form-required">*</span></label>
										<div class="input-group">
											<input id="rwtitle" oninput="rwtitleinput()" maxlength="50" placeholder="请输入任务标题" />
											<small id="rwtitlestr" style="display:none;text-align:right;color:#999;margin-top:5px;">0/50</small>
										</div>
									</div>
								</div>
								<div class="form-col" style="width:50%;">
									<div class="form-group">
										<label class="form-label form-label-required" style="margin-bottom:10px;">教学评价<span class="form-required">*</span></label>
										<div class="checks-container" style="margin-top:4px;">
											<div class="checks" style="display:flex;align-items:center;">
												<div class="beautiful-radio">
													<input type="radio" id="radio-yes" name="evaluation" checked onchange="selectpj(1,this)">
													<label for="radio-yes">是</label>
												</div>
												<div class="beautiful-radio">
													<input type="radio" id="radio-no" name="evaluation" onchange="selectpj(0,this)">
													<label for="radio-no">否</label>
												</div>
											</div>
										</div>
									</div>
								</div>
							</div>

							<!-- 任务时间和学科 -->
							<div class="form-row">
								<div class="form-col" style="width:60%;">
									<div class="form-group">
										<label class="form-label form-label-required">任务时间<span class="form-required">*</span></label>
										<div style="display:flex;align-items:center;gap:10px;">
											<input id="startdate" type="date" placeholder="开始日期" />
											<span style="margin:0;flex:none;">~</span>
											<input id="enddate" type="date" placeholder="结束日期" />
								</div>
								</div>
							</div>
								<div class="form-col" style="width:40%;">
									<div class="form-group">
										<label class="form-label form-label-required">学科<span class="form-required">*</span></label>
										<select id="xueke"></select>
						</div>
								</div>
							</div>
							
							<!-- 任务总分 -->
							<div class="form-row">
								<div class="form-col" style="width:100%;">
									<div class="form-group">
										<label class="form-label form-label-required">学习任务总分<span class="form-required">*</span></label>
										<input placeholder="请输入权重分值" id="allqzscro" />
									</div>
								</div>
							</div>

							<!-- 权重设置 -->
							<div class="form-group">
								<label class="form-label form-label-required">资源权重设置<span class="form-required">*</span></label>
								<div class="form-row" style="margin-bottom:8px;">
									<div class="form-col">
										<div class="weight-section" style="align-items:center;">
											<div class="weight-title">教学资源权重</div>
											<div class="weight-input">
												<input oninput="scro1input(this)" id="scro1" type="number" placeholder="0%(权重分)" />
											</div>
										</div>
									</div>
									<div class="form-col">
										<div class="weight-section" style="align-items:center;">
											<div class="weight-title">试题资源权重</div>
											<div class="weight-input">
												<input oninput="scro4input(this)" id="scro4" type="number" placeholder="0%(权重分)" />
											</div>
										</div>
									</div>
								</div>
								<div class="weight-note">
									<img src="img/wh.png" />
									<div>两项权重总和必须为100%，请根据教学需求合理分配不同资源的权重比例。</div>
										</div>
									</div>
								</div>

						<!-- 任务权限卡片 -->
						<div class="form-card">
							<div class="card-header">
								<h3><i class="fa">👥</i> 班级权限设置</h3>
															</div>
							
							<div class="search-wrapper" style="width:100%;margin-bottom:10px;">
								<input type="text" class="search-input" id="bjss" placeholder="搜索班级..." oninput="searchClass(this.value)"/>
								<img src="img/ss.png" class="search-icon" alt="搜索"/>
								<button class="all-class-btn" onclick="selectAllClasses()">全部添加</button>
																</div>
							
							<div class="class-select-container">
								<div class="class-select-grid">
									<div class="class-select-col">
										<div class="class-select-header">学院<span class="form-required">*</span></div>
										<div class="class-select-box" id="xylist"></div>
																</div>
									<div class="class-select-col">
										<div class="class-select-header">专业<span class="form-required">*</span></div>
										<div class="class-select-box" id="zylist"></div>
															</div>
									<div class="class-select-col">
										<div class="class-select-header">班级<span class="form-required">*</span></div>
										<div class="class-select-box" id="bjlist"></div>
														</div>
													</div>
												</div>

							<div class="yxzbj">
								<div class="yxz">已选择班级： 
									<button id="clearSelectBtn" class="clear-select-btn" onclick="clearAllSelected()" style="display: none;">清空选择</button>
												</div>
								<div id="yxz"></div>
											</div>
										</div>
						
						<!-- 资源配置卡片 -->
						<div class="form-card">
							<div class="card-header">
								<h3><i class="fa">📚</i> 资源配置</h3>
									</div>

							<div class="resource-section">
								<!-- 教学资源 -->
								<div class="resource-card">
									<div class="resource-header">教学资源</div>
									<div class="resource-body">
										<div class="resource-left">
											<div class="input-search">
												<input id="kjss" placeholder="搜索教学资源..." 
													oninput="realTimeSearch(this, 'kjzy')"
													onfocus="showSearchBox(this, 'kjzy')" 
													onblur="hideSearchBox(this, 'kjzy')" />
												<img src="img/ss.png" />
															</div>
											<div class="search-results">
												<div class="newiitembox" id="kjzybox" style="display:none;">
																<div class="closediv">
														<img onclick="closessbox(this)" src="img/closeicos.png" />
																</div>
													<div id="zyssbox" class="scrollview">
																	<div class="errmsg">请输入关键词搜索</div>
																</div>
															</div>
														</div>
													</div>
										<div class="resource-right">
											<div class="resource-selected-header">已选资源</div>
											<div class="resource-selected-list" id="yxkjzybox"></div>
												</div>
									</div>
								</div>

								<!-- 红色书籍 -->
								<div class="resource-card">
									<div class="resource-header">红色书籍</div>
									<div class="resource-body">
										<div class="resource-left">
											<div class="input-search">
												<input id="hssjss" placeholder="搜索红色书籍..." 
													oninput="realTimeSearch(this, 'hssj')"
													onfocus="showSearchBox(this, 'hssj')" 
													onblur="hideSearchBox(this, 'hssj')" />
												<img src="img/ss.png" />
												</div>
											<div class="search-results">
												<div class="newiitembox" id="hssjbox" style="display:none;">
													<div class="closediv">
														<img onclick="closessbox(this)" src="img/closeicos.png" />
													</div>
													<div id="hssjssbox" class="scrollview">
														<div class="errmsg">请输入关键词搜索</div>
												</div>
												</div>
											</div>
										</div>
										<div class="resource-right">
											<div class="resource-selected-header">已选资源</div>
											<div class="resource-selected-list" id="yxhssjbox"></div>
											</div>
										</div>
									</div>

								<!-- VR红色游学 -->
								<div class="resource-card">
									<div class="resource-header">VR红色游学</div>
									<div class="resource-body">
										<div class="resource-left">
											<div class="input-search">
												<input id="vrss" placeholder="搜索VR资源..." 
													oninput="realTimeSearch(this, 'vr')"
													onfocus="showSearchBox(this, 'vr')" 
													onblur="hideSearchBox(this, 'vr')" />
												<img src="img/ss.png" />
															</div>
											<div class="search-results">
												<div class="newiitembox" id="vrbox" style="display:none;">
																<div class="closediv">
														<img onclick="closessbox(this)" src="img/closeicos.png" />
																</div>
																<div id="vrssbox" class="scrollview">
																	<div class="errmsg">请输入关键词搜索</div>
																</div>
															</div>
														</div>
													</div>
										<div class="resource-right">
											<div class="resource-selected-header">已选资源</div>
											<div class="resource-selected-list" id="yxvrbox"></div>
												</div>
										</div>
									</div>

								<!-- 试题资源 -->
								<div class="resource-card">
									<div class="resource-header">试题资源</div>
									<div class="resource-body">
										<div class="resource-left">
											<div class="input-search">
												<input id="stss" placeholder="搜索试题..." 
													oninput="realTimeSearch(this, 'st')"
													onfocus="showSearchBox(this, 'st')" 
													onblur="hideSearchBox(this, 'st')" />
												<img src="img/ss.png" />
															</div>
											<div class="search-results">
												<div class="newiitembox" id="stbox" style="display:none;">
																<div class="closediv">
														<img onclick="closessbox(this)" src="img/closeicos.png" />
																</div>
																<div id="stssbox" class="scrollview">
																	<div class="errmsg">请输入关键词搜索</div>
																</div>
															</div>
														</div>
													</div>
										<div class="resource-right">
											<div class="resource-selected-header">已选资源</div>
											<div class="resource-selected-list" id="yxstbox"></div>
												</div>
											</div>
										</div>
									</div>

						<!-- 提交按钮区域 -->
								<div class="rwbtnview">
									<div onclick="submit()">确认发布</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>


		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			//开始时间和结束时间分开  两个input    修改的时候  组卷界面重写  记录原有数据
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()

				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getfooterlink()
				getxuekelist()
				getxylist()
			})
			
			function scro1input(input){
				let val1 = $(input).val()
				let val4 = $('#scro4').val()
				
				$('#scro4').val(100 - parseInt(val1))
			}
			
			function scro4input(input){
				let val1 = $('#scro1').val()
				let val4 = $(input).val()
				
				$('#scro1').val(100 - parseInt(val4))
			}
		
			function closessbox(item) {
				$(item).parent().parent().hide()
			}

			// 添加全局防抖变量
			let searchDebounce = {
				kjzy: null,
				hssj: null,
				vr: null,
				st: null
			};
			
			/**
			 * 实时搜索函数，根据类型调用不同的搜索逻辑
			 * @param {Object} inputElement - 输入框元素
			 * @param {String} type - 搜索类型：kjzy(教学资源)、hssj(红色书籍)、vr(VR红色游学)、st(试题)
			 */
			function realTimeSearch(inputElement, type) {
				// 清除之前的防抖定时器
				clearTimeout(searchDebounce[type]);
				
				// 获取搜索框的下拉容器ID
				let boxId;
				switch(type) {
					case 'kjzy': boxId = 'kjzybox'; break;
					case 'hssj': boxId = 'hssjbox'; break;
					case 'vr': boxId = 'vrbox'; break;
					case 'st': boxId = 'stbox'; break;
				}
				
				// 如果输入框有焦点则显示搜索结果容器
				if(document.activeElement === inputElement) {
					$('#' + boxId).show();
				}
				
				// 如果输入为空，显示提示信息
				if (!inputElement.value.trim()) {
					// 根据类型设置不同的内容区域
					switch(type) {
						case 'kjzy':
							$("#zyssbox").html(`<div class="errmsg">请输入关键词搜索</div>`);
							break;
						case 'hssj':
							$("#hssjssbox").html(`<div class="errmsg">请输入关键词搜索</div>`);
							break;
						case 'vr':
							$("#vrssbox").html(`<div class="errmsg">请输入关键词搜索</div>`);
							break;
						case 'st':
							$("#stssbox").html(`<div class="errmsg">请输入关键词搜索</div>`);
							break;
					}
					return;
				}
				
				// 设置加载中提示
				switch(type) {
					case 'kjzy':
						$("#zyssbox").html(`<div class="errmsg">搜索中...</div>`);
						break;
					case 'hssj':
						$("#hssjssbox").html(`<div class="errmsg">搜索中...</div>`);
						break;
					case 'vr':
						$("#vrssbox").html(`<div class="errmsg">搜索中...</div>`);
						break;
					case 'st':
						$("#stssbox").html(`<div class="errmsg">搜索中...</div>`);
						break;
				}
				
				// 设置300ms的防抖，避免频繁请求
				searchDebounce[type] = setTimeout(() => {
					// 根据类型调用相应的搜索函数
					switch(type) {
						case 'kjzy':
							searchKjzy(inputElement.value);
							break;
						case 'hssj':
							searchHssj(inputElement.value);
							break;
						case 'vr':
							searchVr(inputElement.value);
							break;
						case 'st':
							searchSt(inputElement.value);
							break;
					}
				}, 300);
			}
			
			// 添加全局变量来存储各类资源的多选状态
			let selectedResources = {
				kjzy: [],
				hssj: [],
				vr: [],
				st: []
			};
			
			/**
			 * 搜索教学资源
			 */
			function searchKjzy(keyword) {
				$.ajax({
					url: baseurl + "/courseBytitle?title=" + keyword,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							if (res.data.length > 0) { //有数据
								let html = `
									<div class="multi-select-header">
										<label><input type="checkbox" onchange="toggleAllSelection(this, 'kjzy')"> 全选</label>
										<button class="batch-add-btn" onclick="batchAddResources('kjzy')">批量添加</button>
									</div>
								`;
								res.data.forEach((item) => {
									// 检查是否已经选中
									const isChecked = selectedResources.kjzy.some(r => r.id === item.id) ? 'checked' : '';
									html += `
										<div class="newzyitem multi-select-item">
											<label>
												<input type="checkbox" ${isChecked} onchange="toggleResourceSelection(this, 'kjzy', '${item.id}', '${item.title}', '${item.attachType}')">
												<span class="itemtype ${item.attachType}">${item.attachType}</span>
												${item.title}
											</label>
										</div>
									`;
								});
								$("#zyssbox").html(html);
							} else { //没有数据
								$("#zyssbox").html(`<div class="errmsg">没有找到数据哦~</div>`);
							}
						}
					}
				});
			}
			
			/**
			 * 搜索红色书籍
			 */
			function searchHssj(keyword) {
				$.ajax({
					url: baseurl + "/postsbycategoryandtitle?categoryId=912354240784109568&title=" + keyword,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							if (res.data.length > 0) { //有数据
								let html = `
									<div class="multi-select-header">
										<label><input type="checkbox" onchange="toggleAllSelection(this, 'hssj')"> 全选</label>
										<button class="batch-add-btn" onclick="batchAddResources('hssj')">批量添加</button>
									</div>
								`;
								res.data.forEach((item) => {
									// 检查是否已经选中
									const isChecked = selectedResources.hssj.some(r => r.id === item.id) ? 'checked' : '';
									html += `
										<div class="newzyitem multi-select-item">
											<label>
												<input type="checkbox" ${isChecked} onchange="toggleResourceSelection(this, 'hssj', '${item.id}', '${item.title}', '', '${item.attachmentDtoList[0].attachmentPath}')">
												${item.title}
											</label>
										</div>
									`;
								});
								$("#hssjssbox").html(html);
							} else { //没有数据
								$("#hssjssbox").html(`<div class="errmsg">没有找到数据哦~</div>`);
							}
						}
					}
				});
			}
			
			/**
			 * 搜索VR红色游学
			 */
			function searchVr(keyword) {
				$.ajax({
					url: baseurl + "/postsbycategoryandtitle?categoryId=912353959077875712&title=" + keyword,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							if (res.data.length > 0) { //有数据
								let html = `
									<div class="multi-select-header">
										<label><input type="checkbox" onchange="toggleAllSelection(this, 'vr')"> 全选</label>
										<button class="batch-add-btn" onclick="batchAddResources('vr')">批量添加</button>
									</div>
								`;
								res.data.forEach((item) => {
									// 检查是否已经选中
									const isChecked = selectedResources.vr.some(r => r.id === item.id) ? 'checked' : '';
									html += `
										<div class="newzyitem multi-select-item">
											<label>
												<input type="checkbox" ${isChecked} onchange="toggleResourceSelection(this, 'vr', '${item.id}', '${item.title}', '', '${item.redirectUrl}')">
												${item.title}
											</label>
										</div>
									`;
								});
								$("#vrssbox").html(html);
							} else { //没有数据
								$("#vrssbox").html(`<div class="errmsg">没有找到数据哦~</div>`);
							}
						}
					}
				});
			}
			
			/**
			 * 搜索试题
			 */
			function searchSt(keyword) {
				$.ajax({
					url: baseurl + "/paper/paperall?name=" + keyword,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							if (res.data.length > 0) { //有数据
								let html = `
									<div class="multi-select-header">
										<label><input type="checkbox" onchange="toggleAllSelection(this, 'st')"> 全选</label>
										<button class="batch-add-btn" onclick="batchAddResources('st')">批量添加</button>
									</div>
								`;
								res.data.forEach((item) => {
									// 检查是否已经选中
									const isChecked = selectedResources.st.some(r => r.id === item.id) ? 'checked' : '';
									html += `
										<div class="newzyitem multi-select-item">
											<label>
												<input type="checkbox" ${isChecked} onchange="toggleResourceSelection(this, 'st', '${item.id}', '${item.name}')">
												${item.name}
											</label>
										</div>
									`;
								});
								$("#stssbox").html(html);
							} else { //没有数据
								$("#stssbox").html(`<div class="errmsg">没有找到数据哦~</div>`);
							}
						}
					}
				});
			}
			
			/**
			 * 切换单个资源的选中状态
			 */
			function toggleResourceSelection(checkbox, type, id, title, attachType = '', url = '') {
				if (checkbox.checked) {
					// 添加到选中资源列表
					switch (type) {
						case 'kjzy':
							selectedResources.kjzy.push({
								id: id,
								title: title,
								attachType: attachType
							});
							break;
						case 'hssj':
							selectedResources.hssj.push({
								id: id,
								title: title,
								url: url
							});
							break;
						case 'vr':
							selectedResources.vr.push({
								id: id,
								title: title,
								url: url
							});
							break;
						case 'st':
							selectedResources.st.push({
								id: id,
								title: title
							});
							break;
					}
				} else {
					// 从选中资源列表中移除
					switch (type) {
						case 'kjzy':
							selectedResources.kjzy = selectedResources.kjzy.filter(item => item.id !== id);
							break;
						case 'hssj':
							selectedResources.hssj = selectedResources.hssj.filter(item => item.id !== id);
							break;
						case 'vr':
							selectedResources.vr = selectedResources.vr.filter(item => item.id !== id);
							break;
						case 'st':
							selectedResources.st = selectedResources.st.filter(item => item.id !== id);
							break;
					}
				}
			}
			
			/**
			 * 全选/取消全选
			 */
			function toggleAllSelection(checkbox, type) {
				const checkboxes = document.querySelectorAll(`#${type === 'kjzy' ? 'zyssbox' : type === 'hssj' ? 'hssjssbox' : type === 'vr' ? 'vrssbox' : 'stssbox'} input[type="checkbox"]:not(:first-child)`);
				
				checkboxes.forEach(box => {
					// 同步复选框状态
					box.checked = checkbox.checked;
					
					// 获取相关数据并更新选中状态
					const parentLabel = box.closest('label');
					const parentItem = parentLabel.closest('.newzyitem');
					
					if (checkbox.checked) {
						// 从标签属性中提取数据
						const id = box.getAttribute('onchange').match(/'([^']*)',\s*'([^']*)'/) ? 
							box.getAttribute('onchange').match(/'([^']*)',\s*'([^']*)'/) [2] : '';
						const title = parentLabel.textContent.trim();
						let attachType = '';
						let url = '';
						
						// 根据类型获取额外信息
						if (type === 'kjzy') {
							attachType = parentLabel.querySelector('.itemtype') ? 
								parentLabel.querySelector('.itemtype').className.replace('itemtype ', '') : '';
						} else if (type === 'hssj' || type === 'vr') {
							const onchangeAttr = box.getAttribute('onchange');
							url = onchangeAttr.match(/,'([^']*)'$/) ? onchangeAttr.match(/,'([^']*)'$/)[1] : '';
						}
						
						// 手动触发选中事件
						toggleResourceSelection({checked: true}, type, id, title, attachType, url);
					} else {
						// 清空该类型的所有选中资源
						selectedResources[type] = [];
					}
				});
			}
			
			/**
			 * 批量添加资源
			 */
			function batchAddResources(type) {
				if (selectedResources[type].length === 0) {
					cocoMessage.warning(1000, "请先选择要添加的资源！");
					return;
				}
				
				let added = 0;
				
				switch (type) {
					case 'kjzy':
						selectedResources.kjzy.forEach(item => {
							// 检查是否已存在
							let exists = showkjzylist.some(existing => existing.metaId === item.id);
							if (!exists) {
								showkjzylist.push({
									attachType: item.attachType,
									metaId: item.id,
									title: item.title
								});
								added++;
							}
						});
						// 清空已选列表
						selectedResources.kjzy = [];
						// 隐藏选择框
						$("#kjzybox").hide();
						// 更新已选展示
						showyxkjzy();
						break;
						
					case 'hssj':
						selectedResources.hssj.forEach(item => {
							// 检查是否已存在
							let exists = hssjyxlist.some(existing => existing.id === item.id);
							if (!exists) {
								hssjyxlist.push({
									url: item.url,
									id: item.id,
									title: item.title
								});
								added++;
							}
						});
						// 清空已选列表
						selectedResources.hssj = [];
						// 隐藏选择框
						$("#hssjbox").hide();
						// 更新已选展示
						showhssjxz();
						break;
						
					case 'vr':
						selectedResources.vr.forEach(item => {
							// 检查是否已存在
							let exists = vryxlist.some(existing => existing.id === item.id);
							if (!exists) {
								vryxlist.push({
									url: item.url,
									id: item.id,
									title: item.title
								});
								added++;
							}
						});
						// 清空已选列表
						selectedResources.vr = [];
						// 隐藏选择框
						$("#vrbox").hide();
						// 更新已选展示
						showvrxz();
						break;
						
					case 'st':
						selectedResources.st.forEach(item => {
							// 检查是否已存在
							let exists = styxlist.some(existing => existing.id === item.id);
							if (!exists) {
								styxlist.push({
									id: item.id,
									title: item.title
								});
								added++;
							}
						});
						// 清空已选列表
						selectedResources.st = [];
						// 隐藏选择框
						$("#stbox").hide();
						// 更新已选展示
						showstxz();
						break;
				}
				
				// 提示添加结果
				if (added > 0) {
					cocoMessage.success(1000, `成功添加 ${added} 个资源！`);
				} else {
					cocoMessage.info(1000, "所选资源已全部添加过，未添加新资源");
				}
			}

			let xuekelist = null

			function getxuekelist() {
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							xuekelist = res.data
							let xuekehtml = "<option value=0>全部学科</option>"
							xuekelist.map((item) => {
								xuekehtml += '<option value="' + item.id + '">' + item.name + '</option>'
							})
							$("#xueke").html(xuekehtml)
						}
					}
				})
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 						.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function rwtitleinput() {
				const titleLength = $("#rwtitle").val().length;
				$("#rwtitlestr").html(`${titleLength}/50`);
				
				// 只有当输入内容超过40个字符时才显示字数提示
				if (titleLength > 40) {
					$("#rwtitlestr").show();
				} else {
					$("#rwtitlestr").hide();
				}
			}
			
			let ispj = 1

			function selectpj(code, item) {
				ispj = code;
				// 不需要修改DOM元素，单选按钮自动处理选中状态
			}
			/**
			 * 统计选择的资源并组合成json
			 * */
			function jisuanallzy() {
				let submitalljson = []
				//课件
				
				showkjzylist.forEach((item) => {
					submitalljson.push({
						inforId: item.metaId,
						resourceType: 0,
						resourceName: item.title,
						resourceScore: 0
					})
				})
				//红色书籍
				hssjyxlist.forEach((item) => {
					submitalljson.push({
						inforId: item.id,
						resourceAddress: item.url,
						resourceType: 1,
						resourceName: item.title,
						resourceScore: 0
					})
				})
				//VR红色游学
				vryxlist.forEach((item) => {
					submitalljson.push({
						inforId: item.id,
						resourceAddress: item.url,
						resourceType: 2,
						resourceName: item.title,
						resourceScore: 0
					})
				})
				//试题
				styxlist.forEach((item) => {
					submitalljson.push({
						inforId: item.id,
						resourceType: 3,
						resourceName: item.title,
						resourceScore: 0
					})
				})
				return submitalljson
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}

			function submit() {
				let rwtitle = $("#rwtitle").val() //任务标题
				if (!rwtitle) {
					cocoMessage.warning(1000, "请输入标题！")
					return
				}
				let startdate = $("#startdate").val() //开始时间
				if (!startdate) {
					cocoMessage.warning(1000, "请输入开始时间！")
					return
				}
				let enddate = $("#enddate").val() //结束时间
				if (!enddate) {
					cocoMessage.warning(1000, "请输入结束时间！")
					return
				}
				let xkid = $("#xueke").val()
				if (xkid == '0') {
					cocoMessage.warning(1000, "请选择学科！")
					return
				}

				if (xzblist.length <= 0) {
					cocoMessage.warning(1000, "请选择任务权限！")
					return
				}

				if (!$('#allqzscro').val()) {
					cocoMessage.warning(1000, "请输入学习任务总分！")
					return
				}
				
				if (!$('#scro1').val()) {
					cocoMessage.warning(1000, "请输入教学资源权重分！")
					return
				}
				
				if (!$('#scro4').val()) {
					cocoMessage.warning(1000, "请输入试题资源权重分！")
					return
				}

				let allqzscro = $("#allqzscro").val() //学习任务总权重分

				let postJson = {
					tasksName: rwtitle,
					endTime: enddate,
					startTime: startdate,
					sectionId: xkid,
					isEvaluation: ispj,
					tasksAuthorityList: xzblist,
					tasksScore: allqzscro,
					learningTasksResourceRelationArrayList: jisuanallzy(),
					status: 0,
					resourcWeight: $('#scro1').val(),
					testWeight: $('#scro4').val(),
				}
				// console.log(JSON.stringify(postJson))
				$.ajax({
					url: baseurl + "/learning-tasks/add",
					type: 'POST',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: JSON.stringify(postJson),
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							cocoMessage.success(1000, "添加成功！")
							window.location.href = "learningtasks.html"
						} else {
							cocoMessage.error(1000, "添加失败！")
						}
					}
				})
			}
			let thisuserxydata = null
			/**
			 * 获取当前用户所带学院专业班级列表
			 * */
			function getxylist() {
				$.ajax({
					url: baseurl + "/binding/teacher-class",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							thisuserxydata = res.data
							let html = ''
							thisuserxydata.forEach((item) => {
								html +=
									`<div class="ljitem" onclick="selectxy(this)" data-xyid="${item.id}" data-id="0">${item.name}</div>`
							})
							$("#xylist").html(html)
						}
					}
				})
			}

			function selectxy(item) {
				let alllist = $("#xylist div")
				for (let i = 0; i < alllist.length; i++) {
					$(alllist[i]).attr('data-id', 0)
					$(alllist[i]).attr("class", "ljitem")
				}
				if ($(item).attr('data-id') == 0) {
					$(item).attr('data-id', 1)
					$(item).attr('class', 'ljitem gx')
				} else {
					$(item).attr('data-id', 0)
					$(item).attr('class', 'ljitem')
				}
				$("#bjlist").html('')
				showzy($(item).attr("data-xyid"))
			}

			function selectzy(item) {
				let alllist = $("#zylist div")
				for (let i = 0; i < alllist.length; i++) {
					$(alllist[i]).attr('data-id', 0)
					$(alllist[i]).attr("class", "ljitem")
				}
				if ($(item).attr('data-id') == 0) {
					$(item).attr('data-id', 1)
					$(item).attr('class', 'ljitem gx')
				} else {
					$(item).attr('data-id', 0)
					$(item).attr('class', 'ljitem')
				}
				$("#bjlist").html('')
				showbj($(item).attr("data-xyid"), $(item).attr("data-zyid"))
			}
			let xzblist = []

			function selectbj(item) {
				if ($(item).attr('data-id') == 0) {
					$(item).attr('data-id', 1)
					$(item).attr('class', 'ljitem gx1')
				} else {
					$(item).attr('data-id', 0)
					$(item).attr('class', 'ljitem')
				}

				thisuserxydata.forEach((itemn) => {
					if (itemn.id == $(item).attr("data-xyid")) {
						itemn.children.forEach((item2) => {
							if (item2.id == $(item).attr("data-zyid")) {
								item2.children.forEach((item3) => {
									if (item3.id == $(item).attr("data-bjid")) {
										if (item3.checkd) {
											item3.checkd = false
										} else {
											item3.checkd = true
										}
									}
								})
							}
						})
					}
				})

				jisuan()
			}

			function jisuan() { //计算已选
				let html = ''
				let newlist = []
				thisuserxydata.forEach((item) => {
					item.children.forEach((item2) => {
						item2.children.forEach((item3) => {
							if (item3.checkd) {
								html +=
									`<label>${item.name+'-'+item2.name+'-'+item3.name}<span onclick="deleteselect(${item.id},${item2.id},${item3.id})"></span></label>`
								newlist.push({
									collegeId: item.id,
									majorId: item2.id,
									classId: item3.id
								})
							}
						})
					})
				})
				xzblist = newlist
				$("#yxz").html(html)
				
				// 根据是否有选择的班级来显示或隐藏清空按钮
				if (xzblist.length > 0) {
					$("#clearSelectBtn").show();
				} else {
					$("#clearSelectBtn").hide();
				}
			}

			function deleteselect(xyid, zyid, bjid) {
				$("#zylist").html('')
				$("#bjlist").html('')
				thisuserxydata.forEach((itemn) => {
					if (itemn.id == xyid) {
						itemn.children.forEach((item2) => {
							if (item2.id == zyid) {
								item2.children.forEach((item3) => {
									if (item3.id == bjid) {
										item3.checkd = false
									}
								})
							}
						})
					}
				})

				jisuan()
			}

			function showzy(id) {
				thisuserxydata.forEach((item) => {
					if (item.id == id) {
						let html = ``
						item.children.forEach((item2) => {
							html +=
								`<div class="ljitem" onclick="selectzy(this)" data-xyid="${item.id}" data-zyid="${item2.id}" data-id="0">${item2.name}</div>`
						})
						$("#zylist").html(html)
					}
				})
			}

			function showbj(xyid, zyid) {
				if ($("#bjss").val()) {
					return;
				}
				
				thisuserxydata.forEach((item) => {
					if (item.id == xyid) {
						item.children.forEach((item2) => {
							if (item2.id == zyid) {
								let html = ``;
								item2.children.forEach((item3) => {
									if (item3.checkd) {
										html += `<div class="ljitem gx1" onclick="selectbj(this)" data-xyid="${xyid}" data-zyid="${zyid}" data-bjid="${item3.id}" data-id="1">${item3.name}</div>`;
									} else {
										html += `<div class="ljitem" onclick="selectbj(this)" data-xyid="${xyid}" data-zyid="${zyid}" data-bjid="${item3.id}" data-id="0">${item3.name}</div>`;
									}
								});
								$("#bjlist").html(html);
							}
						});
					}
				});
			}

			function additem(item) { //教学资源添加
				$(item).siblings('.itemslist').append(`<div class="iiiitem">
													<input class="input1" placeholder="请输入资源标题" /><input class="input2" placeholder="请输入资源信息" />
												</div>`)
			}

			function searchClass(value) {
				// 清空专业和班级列表
				$("#zylist").html('');
				$("#bjlist").html('');
				
				if (!value) {
					// 如果搜索框为空，恢复原始数据显示
					getxylist();
					return;
				}
				
				// 使用已存在的thisuserxydata数据进行本地过滤，无需重新请求API
				if (thisuserxydata) {
					// 过滤包含搜索关键字的班级
					let filteredData = thisuserxydata.map(college => {
						return {
							...college,
							children: college.children.map(major => {
								return {
									...major,
									children: major.children.filter(classItem => 
										classItem.name.toLowerCase().includes(value.toLowerCase())
									)
								};
							}).filter(major => major.children.length > 0)
						};
					}).filter(college => college.children.length > 0);
					
					// 更新显示
					let html = '';
					filteredData.forEach((college) => {
						college.children.forEach((major) => {
							major.children.forEach((classItem) => {
								// 检查是否已选择，设置相应的CSS类和data-id值
								const isSelected = classItem.checkd ? 'ljitem gx1' : 'ljitem';
								const dataId = classItem.checkd ? '1' : '0';
								
								html += `<div class="${isSelected}" onclick="selectbj(this)" 
											data-xyid="${college.id}" 
											data-zyid="${major.id}" 
											data-bjid="${classItem.id}" 
											data-id="${dataId}">
											${college.name} - ${major.name} - ${classItem.name}
										</div>`;
							});
						});
					});
					
					$("#bjlist").html(html || '<div class="errmsg" style="padding:10px;text-align:center;color:#999;">未找到匹配的班级</div>');
					return;
				}
				
				// 仅当本地数据不可用时请求远程数据
				$.ajax({
					url: baseurl + "/binding/teacher-class",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// 更新全局数据
							thisuserxydata = res.data;
							
							// 过滤包含搜索关键字的班级
							let filteredData = res.data.map(college => {
								return {
									...college,
									children: college.children.map(major => {
										return {
											...major,
											children: major.children.filter(classItem => 
												classItem.name.toLowerCase().includes(value.toLowerCase())
											)
										};
									}).filter(major => major.children.length > 0)
								};
							}).filter(college => college.children.length > 0);
							
							// 更新显示
							let html = '';
							filteredData.forEach((college) => {
								college.children.forEach((major) => {
									major.children.forEach((classItem) => {
										html += `<div class="ljitem" onclick="selectbj(this)" 
													data-xyid="${college.id}" 
													data-zyid="${major.id}" 
													data-bjid="${classItem.id}" 
													data-id="0">
													${college.name} - ${major.name} - ${classItem.name}
												</div>`;
									});
								});
							});
							
							$("#bjlist").html(html || '<div class="errmsg" style="padding:10px;text-align:center;color:#999;">未找到匹配的班级</div>');
						}
					}
				});
			}

			/**
			 * 选择当前老师所有班级
			 */
			function selectAllClasses() {
				// 清空之前的选择
				xzblist = [];
				
				// 遍历所有班级数据并全部选中
				thisuserxydata.forEach((college) => {
					college.children.forEach((major) => {
						major.children.forEach((classItem) => {
							// 标记为已选中
							classItem.checkd = true;
							
							// 添加到已选择列表
							xzblist.push({
								collegeId: college.id,
								majorId: major.id,
								classId: classItem.id
							});
						});
					});
				});
				
				// 更新已选班级显示
				updateSelectedClassesDisplay();
				
				// 提示用户
				cocoMessage.success(1000, "已选择所有班级！");
			}
			
			/**
			 * 更新已选班级的显示
			 */
			function updateSelectedClassesDisplay() {
				let html = '';
				
				thisuserxydata.forEach((college) => {
					college.children.forEach((major) => {
						major.children.forEach((classItem) => {
							if (classItem.checkd) {
								html += `<label>${college.name+'-'+major.name+'-'+classItem.name}<span onclick="deleteselect(${college.id},${major.id},${classItem.id})"></span></label>`;
							}
						});
					});
				});
				
				$("#yxz").html(html);
				
				// 根据是否有选择的班级来显示或隐藏清空按钮
				if (xzblist.length > 0) {
					$("#clearSelectBtn").show();
				} else {
					$("#clearSelectBtn").hide();
				}
			}

			/**
			 * 清空所有已选择的班级
			 */
			function clearAllSelected() {
				// 确认是否清空
				if (confirm("确定要清空所有已选择的班级吗？")) {
					// 重置所有班级的选中状态
					thisuserxydata.forEach((college) => {
						college.children.forEach((major) => {
							major.children.forEach((classItem) => {
								classItem.checkd = false;
							});
						});
					});
					
					// 清空已选择列表
					xzblist = [];
					
					// 更新显示
					$("#yxz").html("");
					
					// 隐藏清空按钮
					$("#clearSelectBtn").hide();
					
					// 提示用户
					cocoMessage.info(1000, "已清空所有选择");
				}
			}

			//将选择的课件资源储存到json
			let showkjzylist = []
			//已选好的红色书籍
			let hssjyxlist = []
			//已选好的VR红色游学
			let vryxlist = []
			//已选好的试题
			let styxlist = []

			function addkjzy(item) {
				let ishave = 0
				showkjzylist.forEach((item2) => {
					if (item2.metaId == $(item).attr("data-id")) {
						ishave += 1
					}
				})
				if (ishave == 0) {
					showkjzylist.push({
						attachType: $(item).attr("data-type"),
						metaId: $(item).attr("data-id"),
						title: $(item).attr("title")
					})
					$("#kjzybox").hide()
					showyxkjzy()
				} else {
					cocoMessage.error(1000, "已添加了该资源！")
				}
			}

			function showyxkjzy() {
				let html = ''
				showkjzylist.forEach((item) => {
					html +=
						`<div class="yxkjzyitem"><label class="itemtype ${item.attachType}">${item.attachType}</label>${item.title}<img onclick="deleteyxkc(this)" data-id="${item.metaId}" src="./img/closered.png"/></div>`
				})
				$("#yxkjzybox").html(html)
			}

			function deleteyxkc(item) {
				let id = $(item).attr("data-id")
				let newlist = []
				showkjzylist.forEach((item) => {
					if (item.metaId != id) {
						newlist.push(item)
					}
				})
				showkjzylist = newlist
				showyxkjzy()
			}
			
			//将搜索到的红色书籍 添加到 hssjyxlist
			function addhssjlist(item) {
				let ishave = 0
				hssjyxlist.forEach((item2) => {
					if (item2.id == $(item).attr("data-id")) {
						ishave += 1
					}
				})
				if (ishave == 0) {
					hssjyxlist.push({
						url: $(item).attr("data-url"),
						id: $(item).attr("data-id"),
						title: $(item).attr("title")
					})
					$("#hssjbox").hide()
					showhssjxz()
				} else {
					cocoMessage.error(1000, "已添加了该资源！")
				}
			}
			//将已添加的红色书籍  显示出来
			function showhssjxz() {
				let html = ''
				hssjyxlist.forEach((item) => {
					html +=
						`<div class="yxkjzyitem">${item.title}<img onclick="deleteyxsj(this)" data-id="${item.id}" src="./img/closered.png"/></div>`
				})
				$("#yxhssjbox").html(html)
			}
			//删除选中的红色书籍
			function deleteyxsj(item) {
				let id = $(item).attr("data-id")
				let newlist = []
				hssjyxlist.forEach((item) => {
					if (item.id != id) {
						newlist.push(item)
					}
				})
				hssjyxlist = newlist
				showhssjxz()
			}
			
			//将搜索到的VR红色游学 添加到 vryxlist
			function addvrlist(item) {
				let ishave = 0
				vryxlist.forEach((item2) => {
					if (item2.id == $(item).attr("data-id")) {
						ishave += 1
					}
				})
				if (ishave == 0) {
					vryxlist.push({
						url: $(item).attr("data-url"),
						id: $(item).attr("data-id"),
						title: $(item).attr("title")
					})
					$("#vrbox").hide()
					showvrxz()
				} else {
					cocoMessage.error(1000, "已添加了该资源！")
				}
			}
			//将已添加的VR  显示出来
			function showvrxz() {
				let html = ''
				vryxlist.forEach((item) => {
					html +=
						`<div class="yxkjzyitem">${item.title}<img onclick="deleteyxvr(this)" data-id="${item.id}" src="./img/closered.png"/></div>`
				})
				$("#yxvrbox").html(html)
			}
			//将选中的VR删除
			function deleteyxvr(item) {
				let id = $(item).attr("data-id")
				let newlist = []
				vryxlist.forEach((item) => {
					if (item.id != id) {
						newlist.push(item)
					}
				})
				vryxlist = newlist
				showvrxz()
			}
			
			//将搜索到的试题添加到  styxlist
			function addstlist(item) {
				let ishave = 0
				styxlist.forEach((item2) => {
					if (item2.id == $(item).attr("data-id")) {
						ishave += 1
					}
				})
				if (ishave == 0) {
					styxlist.push({
						id: $(item).attr("data-id"),
						title: $(item).attr("title")
					})
					$("#stbox").hide()
					showstxz()
				} else {
					cocoMessage.error(1000, "已添加了该资源！")
				}
			}
			//将已选的试题显示出来
			function showstxz() {
				let html = ''
				styxlist.forEach((item) => {
					html +=
						`<div class="yxkjzyitem">${item.title}<img onclick="deleteyxst(this)" data-id="${item.id}" src="./img/closered.png"/></div>`
				})
				$("#yxstbox").html(html)
			}
			//删除已选的试题
			function deleteyxst(item) {
				let id = $(item).attr("data-id")
				let newlist = []
				styxlist.forEach((item) => {
					if (item.id != id) {
						newlist.push(item)
					}
				})
				styxlist = newlist
				showstxz()
			}

			// 添加全局变量用于防止误触关闭
			let searchBoxStates = {
				kjzy: false,
				hssj: false,
				vr: false,
				st: false
			};
			
			/**
			 * 显示搜索框
			 * @param {Object} inputElement - 输入框元素
			 * @param {String} type - 搜索类型
			 */
			function showSearchBox(inputElement, type) {
				// 获取搜索框的下拉容器ID
				let boxId;
				switch(type) {
					case 'kjzy': boxId = 'kjzybox'; break;
					case 'hssj': boxId = 'hssjbox'; break;
					case 'vr': boxId = 'vrbox'; break;
					case 'st': boxId = 'stbox'; break;
				}
				
				// 显示搜索结果容器
				$('#' + boxId).show();
				searchBoxStates[type] = true;
				
				// 如果输入框有值，则立即搜索
				if(inputElement.value.trim()) {
					realTimeSearch(inputElement, type);
				}
			}
			
			/**
			 * 隐藏搜索框
			 * @param {Object} inputElement - 输入框元素
			 * @param {String} type - 搜索类型
			 */
			function hideSearchBox(inputElement, type) {
				// 使用延时来允许用户点击下拉框中的选项
				setTimeout(() => {
					// 如果用户在下拉框内操作，不关闭
					if(!$('#' + type + 'box:hover').length) {
						// 获取搜索框的下拉容器ID
						let boxId;
						switch(type) {
							case 'kjzy': boxId = 'kjzybox'; break;
							case 'hssj': boxId = 'hssjbox'; break;
							case 'vr': boxId = 'vrbox'; break;
							case 'st': boxId = 'stbox'; break;
						}
						
						// 隐藏搜索结果容器
						$('#' + boxId).hide();
						searchBoxStates[type] = false;
					}
				}, 200);
			}
			
			/**
			 * 关闭搜索结果框
			 */
			function closessbox(item) {
				$(item).parent().parent().hide();
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
		<script>
			// 页面加载完成后，将所有带星号的标签添加加粗样式
			$(document).ready(function() {
				// 为所有带星号的标签添加加粗样式
				$(".class-select-header:has(.form-required)").addClass("form-label-required");
			});
		</script>
	</body>
</html>
