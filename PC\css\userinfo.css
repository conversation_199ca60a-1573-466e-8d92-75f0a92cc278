body {
	background: #fbfbfb !important;
}

lefttopview, .leftitem {
    font-family: '微软雅黑', 'Microsoft YaHei', 'PingFang SC', 'Hiragino Sans GB', 'SimSun', '宋体', Arial, sans-serif !important;
}

.box {
	width: 66.666666rem;
	margin: 0px auto;
}

.contentview {
	display: flex;
	margin-top: 3.125rem;
	margin-bottom: 3.125rem;
	justify-content: space-between;
}

.boxleft {
	width: 11.458333rem;
}

.boxright {
	width: calc(100% - 12.239583rem);
	background: #FFFFFF;
}

.lefttopview {
	width: 100%;
	height: 3.125rem;
	background: url(../img/lefttopviewbag.png) no-repeat;
	background-size: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 1.145833rem;
	color: #FFFFFF;
	font-weight: bold;
}

.lefttopview img {
	width: 1.25rem;
	display: block;
	padding-right: 0.260416rem;
}

.lefttopview label {
	display: flex;
	align-items: center;
}

.leftitembox {
	background: #a00611;
}

.leftitem {
	height: 5.989583rem;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 0.9375rem;
	color: #c79697;
	border-bottom: 0.052083rem dashed #b04047;
}

.activeleftitem {
	font-weight: bold;
	color: #FFFFFF !important;
}

.righttopview,
.righttopview2 {
	width: 100%;
	height: 3.125rem;
	border-bottom: 0.052083rem solid #999999;
	box-sizing: border-box;
}

.righttopview2 label {
	height: 3.125rem;
	display: inline-block;
	line-height: 3.125rem;
	font-size: 0.9375rem;
	color: #666666;
	position: relative;
	padding: 0px 0.78125rem;
	cursor: pointer;
}

.righttopview2 .accccc {
	color: #c00714;
	font-weight: bold;
}

.accccc::after {
	content: "";
	height: 0.104166rem;
	background: #c00714;
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
}

.righttopview label {
	height: 3.125rem;
	display: inline-block;
	line-height: 3.125rem;
	font-size: 0.9375rem;
	color: #c00714;
	font-weight: bold;
	position: relative;
	padding: 0px 0.78125rem;
}

.righttopview label::after {
	content: "";
	height: 0.104166rem;
	background: #c00714;
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0;
}

.box1 {
	padding: 1.5625rem 0.78125rem;
	position: relative;
	border-bottom: 0.052083rem solid #999999;
}

.box1:last-child {
	border: none;
}

#yz {
	position: absolute;
	top: 0.520833rem;
	right: 0.520833rem;
	width: 8.802083rem;
	display: block;
}

.box1_toptitle {
	font-size: 0.9375rem;
	color: #333333;
	font-weight: bold;
}

.xx {
	font-size: 0.9375rem;
	color: #666666;
	padding-left: 1.822916rem;
	display: flex;
	align-items: center;
	margin: 1.302083rem 0px;
}

.xx img {
	width: 1.041666rem;
	display: block;
	padding-left: 0.78125rem;
}

.xx1 {
	width: 65%;
	display: flex;
}

.xx2 {
	width: 20%;
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.bdbtn {
	width: 5rem;
	height: 1.770833rem;
	display: block;
	background: #c00714;
	color: #fefefe;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 0.260416rem;
	font-size: 0.9375rem;
	cursor: pointer;
}

.wbd {
	color: #cecece;
	padding: 0px 1.041666rem;
}

.xxnr {
	padding-left: 1.822916rem;
}

.xxleft {
	width: 4.6875rem;
}

.userinfotopdiv {
	height: 3.020833rem;
	border-bottom: 0.052083rem solid #999999;
	display: flex;
	align-items: center;
}

.userinfotopdiv a {
	padding: 0px 1.302083rem;
	height: 3.020833rem;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 0.9375rem;
	color: #333333;
}

.acccccg {
	color: #c00714 !important;
	font-weight: bold;
	position: relative;
}

.acccccg::after {
	content: "";
	height: 0.104166rem;
	background: #c00714;
	position: absolute;
	bottom: -0.052083rem;
	left: 0;
	right: 0;
}

.cgbox {
	padding-left: 1.041666rem;
	padding-right: 1.041666rem;
}

.cgtop {
	height: 3.333333rem;
	display: flex;
	align-items: center;
	justify-content: space-between;
	border-bottom: 1px solid #999999;
}

.cgtop div select:first-child {
	width: 6.25rem;
	height: 1.25rem;
	border: none;
	outline-style: none;
	background: #f3f3f3;
	font-size: 0.729166rem;
	color: #999999;
}

.cgtop div select:last-child {
	width: 10.416666rem;
	height: 1.8rem;
	border: none;
	outline-style: none;
	background: #f3f3f3;
	font-size: 0.729166rem;
	color: #999999;
	margin-left: 0.520833rem;
}

.cgssbox {
	width: 8.333333rem;
	height: 1.25rem;
	display: flex;
	align-items: center;
	box-sizing: border-box;
	border: 0.052083rem solid #c00714;
	border-radius: 0.260416rem;
	overflow: hidden;
}

.cgssbox input {
	border: none;
	outline-style: none;
	width: calc(100% - 2.083333rem);
	padding-left: 0.260416rem;
	padding-right: 0.260416rem;
	box-sizing: border-box;
	height: 100%;
}

.cgssbox div {
	width: 2.083333rem;
	font-size: 0.729166rem;
	color: #ffffff;
	background: #c00714;
	height: 100%;
	text-align: center;
	cursor: pointer;
}

.table {
	height: 4.635416rem;
	display: flex;
	align-items: center;
	padding-left: 0.520833rem;
	padding-right: 0.520833rem;
	border-bottom: 0.052083rem dashed #c1c1c1;
}

.table:last-child {
	border: none;
}

.tabletop {
	height: 1.927083rem;
	background: #fbfbfb;
	color: #999999;
	font-size: 0.833333rem;
	display: flex;
	align-items: center;
	padding-left: 0.520833rem;
	padding-right: 0.520833rem;
	border: none !important;
}

.table div:nth-child(1) {
	width: 18.541666rem;
}

.table div:nth-child(2) {
	width: 8.333333rem;
}

.table div:nth-child(3) {
	width: 6.208333rem;
}

.table div:nth-child(4) {
	width: 8.604166rem;
}

.table div {
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
}

.fybox {
	padding-bottom: 1.5625rem;
}

.cgbt {
	font-size: 0.9375rem;
	color: #666666;
}

.cgtxt {
	font-size: 0.9375rem;
	color: #cecece;
}


.cgcz {
	display: flex;
	align-items: center;
	justify-content: center;
}

.cgcz label {
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.cgcz label:first-child {
	font-size: 0.833333rem;
	color: #ffffff;
	background: #c00714;
	width: 4.166666rem;
	height: 1.5625rem;
	border-top-left-radius: 0.260416rem;
	border-bottom-left-radius: 0.260416rem;
}

.cgcz label:last-child {
	font-size: 0.833333rem;
	color: #ffffff;
	background: #56080a;
	width: 4.166666rem;
	height: 1.5625rem;
	margin-left: 0.104166rem;
	border-top-right-radius: 0.260416rem;
	border-bottom-right-radius: 0.260416rem;
}

.updatebox {
	padding: 1.041666rem;
}

.updateitem {
	display: flex;
	align-items: center;
	padding-top: 0.520833rem;
	padding-bottom: 0.520833rem;
}

.updateitem2 {
	display: flex;
	padding-top: 0.520833rem;
	padding-bottom: 0.520833rem;
}

.updateitemtitle {
	width: 4.947916rem;
	font-size: 0.9375rem;
	color: #666666;
	text-align: right;
	padding-right: 1.041666rem;
}

.updateitem select,
.updateitem input {
	border: 0.052083rem solid #cecece;
	width: 23.958333rem;
	height: 1.979166rem;
	border-radius: 0.260416rem;
	color: #666666;
	font-size: 0.9375rem;
	outline-style: none;
	padding-left: 0.260416rem;
	padding-right: 0.260416rem;
	box-sizing: border-box;
}

.updateitem textarea {
	border: 0.052083rem solid #cecece;
	width: 23.958333rem;
	border-radius: 0.260416rem;
	color: #666666;
	line-height: 1.5625rem;
	padding-left: 0.260416rem;
	padding-right: 0.260416rem;
	box-sizing: border-box;
	outline-style: none;
	font-size: 0.9375rem;
	height: 4.6875rem;
	resize: none;
}

.updateitem input::-webkit-input-placeholder {
	color: #cecece;
}

.updateitem input:-moz-placeholder {
	color: #cecece;
}

.updateitem input::-moz-placeholder {
	color: #cecece;
}

.updateitem input:-ms-input-placeholder {
	color: #cecece;
}

.files {
	border: none !important;
	height: auto !important;
	font-size: 0.833333rem !important;
	display: flex;
	flex-wrap: wrap;
	width: 100%;
}

.updat4eboxsss {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}

.files {
	padding: 0.260416rem 0rem;
}

.files input {
	border: none !important;
	font-size: 0.833333rem !important;
	opacity: 0;
}

.filesitem {
	width: 7.8125rem !important;
	margin-right: 1.041666rem;
}

.filesitem {
	width: 100%;
	font-size: 0.729166rem;
	color: #333333;
	border: 0.052083rem dashed #e56c74;
	border-radius: 0.260416rem;
	line-height: 2.083333rem;
	padding: 0px 0.520833rem;
	line-height: 2.083333rem;
	box-sizing: border-box;
	position: relative;
}

.filesitem div {
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.close {
	width: 0.885416rem !important;
	display: block;
	position: absolute;
	top: -0.520833rem;
	right: -0.520833rem;
	padding: 0 !important;
	cursor: pointer;
}

.filesitem img {
	width: 100%;
	display: block;
	padding: 0.520833rem 0px;
}

.tjbtn {
	width: 7.8125rem;
	height: 2.083333rem;
	background: #c00714;
	font-size: 0.9375rem;
	color: #ffffff;
	border-radius: 0.260416rem;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
	position: relative;
}

.tjbtn input {
	position: absolute;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	cursor: pointer;
}

.tjbtn img {
	display: block;
	width: 1.041666rem;
	padding-right: 0.260416rem;
}

.submitbtn {
	border-radius: 0.260416rem;
	width: 8.333333rem;
	height: 2.083333rem;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 0.9375rem;
	color: #ffffff;
	background: #c00714;
	margin-top: 1.5625rem;
	cursor: pointer;
}

.successbox {
	display: flex;
	align-items: center;
	justify-content: center;
	flex-wrap: wrap;
}

.info {
	width: 100%;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 1.145833rem;
	color: #333333;
	padding-top: 2.083333rem;
}

.info img {
	display: block;
	width: 2.5rem;
	padding-right: 0.520833rem;
}

.successbtn {
	width: 100%;
	padding-top: 3.125rem;
	display: flex;
	justify-content: center;
	align-items: center;
}

.successbtn div {
	width: 8.333333rem;
	height: 2.083333rem;
	background: #c00714;
	font-size: 0.9375rem;
	color: #ffffff;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 0.260416rem;
	margin: 0px 1.041666rem;
	cursor: pointer;
}

#loadimg,
#loadimg2 {
	animation: xz 1s linear infinite;
	-webkit-animation: xz 1s linear infinite;
	padding: 0 !important;
}

@keyframes xz {
	from {
		transform: rotateZ(0deg);
	}

	to {
		transform: rotateZ(360deg);
	}
}

@-webkit-keyframes xz

/* Safari 与 Chrome */
	{
	from {
		transform: rotateZ(0deg);
	}

	to {
		transform: rotateZ(360deg);
	}
}

.editbox {
	position: fixed;
	left: 0;
	right: 0;
	top: 0;
	bottom: 0;
	background: rgba(0, 0, 0, 0.8);
	z-index: 999;
	display: flex;
	align-items: center;
	justify-content: center;
}

.editview {
	width: 26.041666rem;
	height: auto;
	background: #FFFFFF;
	position: relative;
}

.edittitle {
	height: 2.604166rem;
	font-size: 0.9375rem;
	display: flex;
	justify-content: center;
	align-items: center;
	background-image: linear-gradient(to right, #c00714, #c00714, #ffc156);
	color: #FFFFFF;
	font-weight: bold;
}

#closeedit {
	width: 1.041666rem;
	display: block;
	position: absolute;
	right: -1.5rem;
	top: -1.5rem;
	cursor: pointer;
}

.viewitem {
	padding: 0px 1.5625rem;
	display: flex;
	align-items: center;
	padding-top: 1.041666rem;
}

.viewitem label {
	width: 4.166666rem;
	font-size: 0.833333rem;
	color: #333333;
}

.viewitem input {
	outline-style: none;
	border: 0.052083rem solid #CECECE;
	font-size: 0.833333rem;
	height: 1.5625rem;
	width: 20.833333rem;
	padding: 0px 0.520833rem;
	box-sizing: border-box;
	color: #333333;
}

.viewitem select {
	outline-style: none;
	border: 0.052083rem solid #CECECE;
	font-size: 0.833333rem;
	height: 1.5625rem;
	width: 20.833333rem;
	padding: 0px 0.520833rem;
	color: #333333;
	overflow: hidden !important;
}

.btnboxview {
	display: flex;
	justify-content: center;
	align-items: center;
	padding-bottom: 1.041666rem;
}

.btnboxview label {
	border-radius: 0.260416rem;
	width: 8.333333rem;
	height: 1.822916rem;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 0.833333rem;
	color: #ffffff;
	background: #c00714;
	margin-top: 1.041666rem;
	cursor: pointer;
}

.fbwzbox {
	width: 100%;
	height: 7.8125rem;
	background: #f3f3f3;
	display: flex;
	align-items: center;
	justify-content: center;
}

.fbwzbox label {
	background: #c00714;
	color: #FFFFFF;
	width: 7.8125rem;
	height: 2.083333rem;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 0.260416rem;
	font-size: 0.9375rem;
	cursor: pointer;
}

#wzbt {
	width: 100%;
	height: 2.083333rem;
	outline-style: none;
	border: 0.052083rem solid #c1c1c1;
	box-sizing: border-box;
	padding: 0px 1.041666rem;
	margin-bottom: 0.416666rem;
	background: #FFFFFF;
	font-size: 0.833333rem;
}

#wzzt {
	width: 100%;
	height: 2.083333rem;
	outline-style: none;
	border: 0.052083rem solid #c1c1c1;
	box-sizing: border-box;
	padding: 0px 1.041666rem;
	margin-bottom: 0.416666rem;
	background: #FFFFFF;
	font-size: 0.833333rem;
}

#wzfl {
	width: 100%;
	height: 2.083333rem;
	outline-style: none;
	border: 0.052083rem solid #c1c1c1;
	box-sizing: border-box;
	padding: 0px 1.041666rem;
	margin-bottom: 0.416666rem;
	background: #FFFFFF;
	font-size: 0.833333rem;
}

.wbview {
	padding: 1.041666rem 1.041666rem 0 1.041666rem;
	background: #f3f3f3;
}

.fbbtnview {
	height: 1.666666rem;
	display: flex;
	justify-content: flex-end;
	align-items: center;
	padding-top: 1.041666rem;
	padding-bottom: 1.041666rem;
}

.fbbtnview label:first-child {
	background: #c00714;
	width: 6.770833rem;
	height: 1.666666rem;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 0.833333rem;
	color: #ffffff;
	border-radius: 0.260416rem;
	margin-right: 1.041666rem;
	cursor: pointer;
}

.fbbtnview label:last-child {
	background: #999999;
	width: 6.770833rem;
	height: 1.666666rem;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 0.833333rem;
	color: #ffffff;
	border-radius: 0.260416rem;
	cursor: pointer;
}

.yfbwztop {
	height: 1.354166rem;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 0.729166rem;
	color: #ffffff;
	background-image: linear-gradient(to right, #c00714, #c00714, #ffc156);
}

.bb {
	height: 1px;
	background-image: linear-gradient(to right, #c00714, #c00714, #ffc156);
	margin-top: 1px;
}

.w-e-text {
	font-size: 0.833333rem !important;
}

.w-e-text-container .placeholder {
	font-size: 0.833333rem !important;
}

.wzitem {
	display: flex;
	align-items: center;
	padding: 0.5rem 1.041666rem;
	border-bottom: 1px dashed #dedede;
	justify-content: space-between;
}

.wzitem:last-child {
	border: none;
}

.itemnr2 {
	width: 32.8125rem;
}

.itemnr2 div:first-child {
	display: flex;
	align-items: center;
}

.itemnr2 div:first-child label {
	font-size: 0.729166rem;
	color: #c1c1c1;
	margin-right: 2.604166rem;
	display: flex;
	align-items: center;
}

.itemnr2 div:first-child label img {
	display: block;
	padding-right: 0.260416rem;
}

.itemtitle {
	font-size: 0.9375rem;
	color: #333333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	padding-top: 0.78125rem;
}

.state {
	font-size: 0.9375rem;
	font-weight: bold;
	box-sizing: border-box;
}

.ing {
	color: #f8b967;
}

.success {
	color: #59b585;
}

.error {
	color: #a4080c;
}

.error span {
	font-size: 0.729166rem;
	font-weight: normal;
}

.btn {
	display: flex;
	justify-content: flex-end;
	align-items: center;
}

.btn label {
	width: 6.25rem !important;
	height: 1.666666rem !important;
	display: flex !important;
	justify-content: center !important;
	align-items: center !important;
	font-size: 0.833333rem !important;
	color: #ffffff !important;
	border-radius: 0.260416rem !important;
	font-weight: normal !important;
}

.no {
	background: #999999;
}

.is {
	background: #c00714;
}

#view1 {
	display: none;
}

#view2 {
	display: block;
}

.jlitem1 {
	display: flex;
	padding: 1.5625rem 1.041666rem;
	border-bottom: 1px dashed #dedede;
}

.jlitem1:last-child {
	border: none;
}

.jlitem1 img {
	width: 6.770833rem;
	display: block;
	border-radius: 5px;
}

.jlitem1 .cc {
	width: calc(100% - 6.25rem - 6.770833rem);
	display: flex;
	flex-wrap: wrap;
	box-sizing: border-box;
	padding: 0px 0.78125rem;
}

.cc2 {
	width: calc(100% - 6.25rem) !important;
	padding-left: 0 !important;
}

.jlitem2 {
	padding-top: 0.78125rem !important;
	padding-bottom: 0.78125rem !important;
}

.jlitem1 .cc div {
	width: 100%;
}

.jlitem1 .cc div:first-child {
	font-size: 0.9375rem;
	color: #333333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.jlitem1 .cc div:nth-child(2) {
	display: flex;
	align-items: center;
	justify-content: space-between;
}

.jlitem1 .cc div:nth-child(2) label:first-child {
	font-size: 0.729166rem;
	color: #f8b967;
}

.jlitem1 .cc div:nth-child(2) label:last-child {
	font-size: 0.729166rem;
	color: #cecece;
}

.jlitem1 .cc div:nth-child(3) {
	font-size: 0.625rem;
	color: #c1c1c1;
}

.jlitem1 .rr {
	width: 6.25rem;
	display: flex;
	flex-wrap: wrap;
	align-items: center;
	justify-content: center;
}

.jlitem1 .rr div,
.jlitem1 .rr a {
	width: 6.25rem;
	height: 1.666666rem;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 0.833333rem;
	color: #ffffff;
	border-radius: 0.260416rem;
	cursor: pointer;
}

.wc div,
.wc a {
	background: #f39633;
}

.jx div,
.jx a {
	background: #c00714;
}

.jlitem1 .rr label {
	font-size: 0.625rem;
	color: #cecece;
}

.paperscroll {
	padding: 1.041666rem;
}

.papertitle {
	font-weight: bold;
	font-size: 0.9375rem;
	color: #333333;
	padding-top: 1.041666rem;
	padding-bottom: 0.729166rem;
}

.paperitem {
	display: flex;
	align-items: center;
	justify-content: space-between;
	width: 100%;
	height: 4.427083rem;
	border-bottom: 1px dashed #dedede;
	padding: 0px 0.520833rem;
	box-sizing: border-box;
}

.paperitem:last-child {
	border: none;
}

.paperitem .ppn {
	display: flex;
	align-items: center;
}

.paperitem label {
	color: #c1c1c1;
	font-size: 0.9375rem;
}

.paperitem span {
	color: #666666;
	font-size: 0.9375rem;
	padding-left: 0.520833rem;
	padding-right: 0.520833rem;
}

.paperitem a {
	width: 5.208333rem;
	height: 1.666666rem;
	border-radius: 0.260416rem;
	background: #c00714;
	color: #FFFFFF;
	font-size: 0.833333rem;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	float: right;
}

.paperitem .ano {
	background: #b5b5b5;
}

#tbox {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	top: 0;
	background: rgba(0, 0, 0, 0.7);
	z-index: 999;
	display: flex;
	justify-content: center;
	align-items: center;
	display: none;
}

.yuepaperbox {
	width: 65.3125rem;
	height: 42.708333rem;
	background: rgba(255, 255, 255, 0.5);
	position: relative;
}

.yuepaperview {
	position: absolute;
	top: 0.520833rem;
	left: 0.520833rem;
	right: 0.520833rem;
	bottom: 0.520833rem;
	background: #FFFFFF;
}

.yuepapaertoptitle {
	height: 5.46875rem;
	background: url(../img/paperyuebag.png) no-repeat;
	background-size: cover;
}

.ypapertitle {
	height: 3.28125rem;
	display: flex;
	justify-content: center;
	align-items: center;
	color: #ffffff;
	font-size: 1.145833rem;
	font-weight: bold;
	position: relative;
}

.paperyueclose {
	position: absolute;
	right: 1.041666rem;
	width: 1.25rem;
	height: 1.25rem;
	background: url(../img/closeicos.png) no-repeat;
	background-size: 100% auto;
	cursor: pointer;
}

.paperyueclose2 {
	position: absolute;
	right: 1.041666rem;
	width: 1.25rem;
	height: 1.25rem;
	background: url(../img/paperclose.png) no-repeat;
	background-size: 100% auto;
	cursor: pointer;
}

.ypaperbottom {
	height: calc(100% - 3.28125rem);
	background: rgba(255, 255, 255, 0.2);
	display: flex;
	align-items: center;
	justify-content: center;
}

.ypaperbottom label {
	color: #f4bbbc;
	font-size: 0.833333rem;
	padding-left: 0.520833rem;
	padding-right: 0.520833rem;
}

.ypaperbottom label span {
	color: #ffffff;
	font-size: 0.833333rem;
}

.ypaperbox {
	height: calc(100% - 5.46875rem);
	padding: 1.041666rem;
	box-sizing: border-box;
	display: flex;
}

.ypaperboxleft {
	width: calc(100% - 11.354166rem);
	overflow-y: auto;
}

.ypaperboxright {
	width: 10.833333rem;
	margin-left: 0.520833rem;
	position: relative;
}

.xuekeselect {
	height: 2.864583rem;
	border-bottom: 0.052083rem dashed #e1e1e1;
	display: flex;
	align-items: center;
}

.xuekeselect select {
	height: 1.25rem;
	font-size: 0.729166rem;
	color: #999999;
	border: none;
	outline-style: none;
	background: #f3f3f3;
	min-width: 6.25rem;
	margin-left: 1.041666rem;
}

.mycjitem {
	padding: 0px 1.041666rem;
	display: flex;
	align-items: center;
	justify-content: space-between;
	height: 5.208333rem;
	border-bottom: 0.052083rem dashed #e1e1e1;
}

.mycjitem:last-child {
	border: none;
}

.mysjtime {
	width: 9.895833rem;
	height: 2.1875rem;
	display: flex;
	border-radius: 0.260416rem;
	overflow: hidden;
}

.sjtime1 {
	width: calc(100% / 3);
	background: #f39633;
	font-size: 1.25rem;
	color: #FFFFFF;
	display: flex;
	align-items: center;
	justify-content: center;
	font-weight: bold;
	background-image: url(../img/jiangbei.png);
	background-repeat: no-repeat;
	background-position: center;
	background-size: 1.354166rem;
}

.sjtime1 label {
	font-size: 0.729166rem;
	color: #FFFFFF;
	margin-right: 0.260416rem;
	font-weight: normal;
}

.sjtime2 {
	width: calc(100% / 3);
	background: #f3f3f3;
	justify-content: center;
	flex-wrap: wrap;
}

.sjtime2 div {
	width: 100%;
}

.sjtime2 div:first-child {
	font-size: 0.9375rem;
	font-weight: bold;
	color: #333333;
	text-align: center;
}

.sjtime2 div:last-child {
	font-size: 0.625rem;
	color: #999999;
	text-align: center;
}

.mysjname {
	font-size: 0.9375rem;
	color: #333333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	width: 13.020833rem;
}

.sjjxbtn {
	width: 6.25rem;
	height: 1.666666rem;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #c00714;
	color: #FFFFFF;
	font-size: 0.833333rem;
	border-radius: 0.260416rem;
	cursor: pointer;
}

.sjjxbtn2 {
	width: 6.25rem;
	height: 1.666666rem;
	display: flex;
	align-items: center;
	justify-content: center;
	background: #999999;
	color: #FFFFFF;
	font-size: 0.833333rem;
	border-radius: 0.260416rem;
	cursor: pointer;
}

.mysjstr {
	font-size: 0.729166rem;
	color: #cecece;
	display: flex;
	align-items: center;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.mysjstr img {
	display: block;
	padding-right: 0.260416rem;
}

.btbtbtbtbt {
	font-size: 0.9375rem;
	color: #333333;
	font-weight: bold;
	display: flex;
	justify-content: center;
	padding-top: 2.083333rem;
}

.btbtbtbtbt2 {
	font-size: 0.9375rem;
	color: #333333;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: space-between;
	padding-top: 2.083333rem;
	padding-left: 1.041666rem;
	padding-right: 1.041666rem;
}

.selectview {
	display: flex;
	justify-content: center;
	align-items: center;
	padding-top: 0.78125rem;
}

.selectview select {
	height: 1.25rem;
	font-size: 0.729166rem;
	color: #999999;
	border: none;
	outline-style: none;
	background: #f3f3f3;
	min-width: 6.25rem;
	margin-left: 0.364583rem;
	margin-right: 0.364583rem;
}

.nono select {
	height: 1.25rem;
	font-size: 0.729166rem;
	color: #999999;
	border: none;
	outline-style: none;
	background: #f3f3f3;
	min-width: 6.25rem;
	margin-right: 0.729166rem;
	margin-left: 0;
}

.tjt {
	height: 23.4375rem;
	border-bottom: 0.052083rem solid #999999;
	position: relative;
	margin-top: 2.083333rem;
	padding-bottom: 1.5625rem;
}

#main {
	position: absolute;
	width: 100%;
	height: 100%;
}

.selectbox {
	display: flex;
	justify-content: space-between;
	padding-left: 1.041666rem;
	padding-right: 1.041666rem;
	padding-top: 0.260416rem;
	padding-bottom: 0.416666rem;
}

.nono {
	padding: 0 !important;
}

.tabletopview {
	height: 1.875rem;
	display: flex;
	justify-content: center;
	align-items: center;
	font-size: 0.833333rem;
	font-weight: bold;
	color: #ffffff;
	background: #666666;
}

.tabview {
	padding: 0px 1.041666rem;
	padding-bottom: 1.041666rem;
}

.tabletoptt {
	display: flex;
	align-items: center;
	color: #FFFFFF;
	font-size: 0.729166rem;
	background: #999999;
	border-top: 0.052083rem solid #c1c1c1;
	height: 1.927083rem;
}

.tabletoptt div {
	padding-left: 0.260416rem;
	box-sizing: border-box;
	border-right: 0.052083rem solid #c1c1c1;
	height: 100%;
	display: flex;
	align-items: center;
}

.tabletoptt div:last-child {
	border: none;
}

.tabletoptt div:nth-of-type(1) {
	width: 2.34375rem;
}

.tabletoptt div:nth-of-type(2) {
	width: 7.8125rem;
}

.tabletoptt div:nth-of-type(3) {
	width: 8.854166rem;
}

.tabletoptt div:nth-of-type(4) {
	width: 7.291666rem;
}

.tabletoptt div:nth-of-type(5) {
	width: 6.25rem;
}

.tabletoptt div:nth-of-type(6) {
	width: 4.6875rem;
}

.tabletoptt div:nth-of-type(7) {
	width: 3.645833rem;
}

.tabletoptt div:nth-of-type(8) {
	width: 3.645833rem;
}

.tabletoptt div:nth-of-type(9) {
	width: 3.645833rem;
}

.tabletoptt div:nth-of-type(10) {
	width: 3.645833rem;
}

.nonos {
	border: none !important;
	color: #666666 !important;
}

.nonos:nth-child(even) {
	background: #fbfbfb !important;
}

.nonos:nth-child(odd) {
	background: #FFFFFF !important;
}

.tfyq {
	background: #666666;
}

.tfyq .fybox {
	padding-top: 1.041666rem !important;
	padding-bottom: 1.041666rem !important;
}

.tfyq .fybox span {
	color: #FFFFFF !important;
	border-color: #FFFFFF !important;
}

.tfyq .fybox span:hover {
	color: #FFFFFF;
	border-color: #FFFFFF;
}

.xxjlview {
	padding: 0.78125rem;
}

.xxjlview select {
	height: 1.25rem;
	font-size: 0.729166rem;
	color: #999999;
	border: none;
	outline-style: none;
	background: #f3f3f3;
	min-width: 6.25rem;
	margin-right: 0.729166rem;
}

.tjt4 {
	display: flex;
	flex-wrap: wrap;
	padding-bottom: 3.125rem;
	padding-top: 1.041666rem;
}

/* .tjt4 div {
	width: 100%;
	min-height: 23.4375rem;
	border-bottom: 0.052083rem solid #f3f3f3;
	box-sizing: border-box;
} */

#main1,
#main2 {
	width: 100%;
	height: 23.4375rem;
	border-bottom: 0.052083rem solid #f3f3f3;
	box-sizing: border-box;
}

#main2 {
	border: none;
	padding-top: 2.083333rem;
	height: 28.645833rem;
}

/* .tjt4 div:nth-child(odd) {
	border-right: 0.052083rem solid #f3f3f3;
	box-sizing: border-box;
} */

.tjt4 div:nth-child(3),
.tjt4 div:nth-child(4) {
	border-bottom: none;
}

.xxjlselectbox {
	height: 3.125rem;
	display: flex;
	align-items: center;
	border-bottom: 0.052083rem dashed #dedede;
	display: none;
}

.xxjlselectbox select {
	height: 1.25rem;
	font-size: 0.729166rem;
	color: #999999;
	border: none;
	outline-style: none;
	background: #f3f3f3;
	min-width: 6.25rem;
	margin-left: 1.041666rem;
}

.tmitem {
	padding: 1.5625rem 0;
	border-bottom: 0.052083rem dashed #dedede;
}

.tmitem:last-child {
	border: none;
}

.tmname {
	font-size: 0.9375rem;
	color: #333333;
	line-height: 1.302083rem;
	display: flex;
	align-items: flex-start;
}

.tmname label {
	width: 6rem;
}

.tmname div {
	width: calc(100% - 6rem);
}

.studentbox {
	background: #fbfbfb;
	padding: 0.78125rem;
	margin-top: 0.520833rem;
}

.studentbox2 {
	background: #fffbf6;
	padding: 0.78125rem;
	margin-top: 0.104166rem;
	margin-bottom: 0.78125rem;
}

.bbicott label {
	background: #666666;
	font-size: 0.833333rem;
	color: #FFFFFF;
	padding: 0.15625rem 0.520833rem;
	border-radius: 5px;
}

.bbicott2 label {
	background: #c00714;
	font-size: 0.833333rem;
	color: #FFFFFF;
	padding: 0.15625rem 0.520833rem;
	border-radius: 5px;
}

.studentstr {
	font-size: 0.833333rem;
	color: #666666;
	line-height: 1.302083rem;
	padding-top: 0.520833rem;
	padding-bottom: 1.041666rem;
}

.py {
	display: flex;
	align-items: flex-start;
	margin-bottom: 0.78125rem;
}

.py2 {
	align-items: center !important;
}

.py2 span {
	font-size: 0.833333rem;
	margin-left: 0.520833rem;
	color: #999999;
}

.py2 i {
	color: #c00714;
	padding: 0px 0.208333rem;
}

.py label {
	font-size: 0.9375rem;
	color: #666666;
	font-weight: bold;
	width: 2.604166rem;
}

.py textarea {
	width: calc(100% - 2.604166rem);
	outline-style: none;
	font-size: 0.9375rem;
	color: #666666;
	border: 0.052083rem solid #666666;
	padding: 0.520833rem;
	box-sizing: border-box;
	resize: none;
}

.py input {
	width: 7.291666rem;
	height: 1.666666rem;
	line-height: 1.666666rem;
	outline-style: none;
	padding-left: 0.520833rem;
	padding-right: 0.520833rem;
	box-sizing: border-box;
	font-size: 0.9375rem;
	color: #666666;
	border: 0.052083rem solid #666666;
}



.ypaperboxleft::-webkit-scrollbar {
	/*滚动条整体样式*/

	width: 5px;
	/*高宽分别对应横竖滚动条的尺寸*/

	height: 1px;

}

.ypaperboxleft::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/

	border-radius: 10px;

	-webkit-box-shadow: inset 0 0 5px #c00714;

	background: #535353;

}

.ypaperboxleft::-webkit-scrollbar-track {
	/*滚动条里面轨道*/

	-webkit-box-shadow: inset 0 0 5px #FFFFFF;

	border-radius: 10px;

	background: #EDEDED;

}

textarea::-webkit-scrollbar {
	/*滚动条整体样式*/

	width: 5px;
	/*高宽分别对应横竖滚动条的尺寸*/

	height: 1px;

}

textarea::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/

	border-radius: 10px;

	-webkit-box-shadow: inset 0 0 5px #c1c1c1;

	background: #c1c1c1;

}

textarea::-webkit-scrollbar-track {
	/*滚动条里面轨道*/

	-webkit-box-shadow: inset 0 0 5px #FFFFFF;

	border-radius: 10px;

	background: #eeecec;

}

.thlb {
	background: #f39633;
	color: #FFFFFF;
	font-size: 0.833333rem;
	display: flex;
	align-items: center;
	height: 1.458333rem;
	padding: 0px 0.78125rem;
	box-sizing: border-box;
}

.thbox {
	box-sizing: border-box;
	padding: 0.78125rem;
	display: flex;
	flex-wrap: wrap;
}

.thbox a {
	width: 1.5625rem;
	height: 1.5625rem;
	box-sizing: border-box;
	border: 0.052083rem solid #c1c1c1;
	border-radius: 0.260416rem;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #999999;
	font-size: 0.833333rem;
	margin-right: calc((100% - (1.5625rem * 5)) / 4);
	margin-bottom: 0.520833rem;
}

#thlist a:hover {
	background: #f39633 !important;
	color: #FFFFFF !important;
	border: none !important;
}

.aac {
	background: #f39633 !important;
	color: #FFFFFF !important;
	border: none !important;
}

.thbox a:nth-child(5n+5) {
	margin-right: 0;
}

.btnvvv {
	width: 100%;
	height: 1.666666rem;
	color: #FFFFFF;
	font-size: 0.833333rem;
	display: flex;
	position: absolute;
	bottom: 0;
	left: 0;
	right: 0;
}

.btnvvv div {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.btnvvv div:first-child {
	background: #c00714;
}

.btnvvv div:last-child {
	background: #56080a;
}

.mysj {
	font-size: 0.9375rem;
	text-align: center;
	color: #666666;
	padding-top: 1.041666rem;
}

.xjsjview {
	display: flex;
	justify-content: center;
	align-items: center;
	/* padding-top: 2.604166rem; */
	/* padding-bottom: 2.604166rem; */
}

.xjsjview div {
	width: 7.8125rem;
	height: 2.083333rem;
	background: #c00714;
	color: #FFFFFF;
	font-size: 0.833333rem;
	display: flex;
	justify-content: center;
	align-items: center;
	border-radius: 0.260416rem;
	cursor: pointer;
}

.papaerboxtopview {
	background-image: linear-gradient(to right, #c00714, #c00714, #c00714, #ffc156);
	height: 1.354166rem;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 0.729166rem;
	color: #FFFFFF;
}

.borderhhhh {
	background-image: linear-gradient(to right, #c00714, #c00714, #c00714, #ffc156);
	height: 0.104166rem;
	margin-top: 0.052083rem;
}

.nodata {
	font-size: 0.833333rem;
	text-align: center;
	color: #d1d1d1;
	margin-top: 20%;
}

.nodatabbbb {
	font-size: 0.9375rem;
	text-align: center;
	color: #999999;
	height: 23.4375rem;
	display: flex;
	align-items: center;
	justify-content: center;
	border-bottom: 0.052083rem solid #f3f3f3;
}

#msgbox {
	width: 100%;
}

.paperselectview {
	padding: 1.041666rem;
	border-bottom: 0.052083rem dashed #dedede;
}

.paperselectview select {
	height: 1.25rem;
	font-size: 0.729166rem;
	color: #999999;
	border: none;
	outline-style: none;
	background: #f3f3f3;
	min-width: 6.25rem;
	margin-right: 1.041666rem;
}

.papertop {
	display: flex;
	align-items: center;
}

.papername {
	display: flex;
	align-items: center;
	width: 28.125rem;
}

.paperbtn {
	width: calc(100% - 28.125rem);
	display: flex;
	align-items: center;
	justify-content: flex-end;
}

.paperbtn label {
	width: 4.166666rem;
	height: 1.458333rem;
	color: #FFFFFF;
	font-size: 0.833333rem;
	border-radius: 0.260416rem;
	display: flex;
	align-items: center;
	justify-content: center;
	margin-left: 0.520833rem;
	cursor: pointer;
}

.yl {
	background: #f8b967;
}

.xg {
	background: #769c94;
}

.fz {
	background: #76829c;
}

.sc {
	background: #c00714;
}

.papername label {
	width: 2.083333rem;
	height: 0.9375rem;
	font-size: 0.625rem;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 0.260416rem;
	margin-right: 0.260416rem;
}

.papername div {
	width: calc(100% - 2.083333rem - 0.260416rem);
	font-size: 0.9375rem;
	color: #333333;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
}

.open {
	background: #a9f4cd;
	color: #3ea972;
}

.closes {
	background: #f4a9a9;
	color: #a93e3e;
}

.defaule {
	background: #a9e3f4;
	color: #3e78a9;
}

.paperbottom {
	display: flex;
	align-items: center;
	padding-top: 0.520833rem;
}

.paperbottom div {
	margin-right: 1.041666rem;
	font-size: 0.729166rem;
	color: #c1c1c1;
	display: flex;
	align-items: center;
}

.paperbottom div img {
	margin-right: 0.260416rem;
}

.paper {
	padding: 1.041666rem;
	border-bottom: 0.052083rem dashed #dedede;
}

.paper:last-child {
	border: none;
}

.addpaperbox input,
.addpaperbox select {
	outline-style: none;
	border: 0.052083rem solid #cecece;
	font-size: 0.833333rem;
	line-height: 2.291666rem;
	height: 2.291666rem;
	box-sizing: border-box;
	padding: 0 0.520833rem;
	border-radius: 0.260416rem;
}

.addpaperbox div {
	margin-bottom: 0.3125rem;
}

.inputviewa input,
.inputviewa select {
	width: 100%;
}

.inputviewb {
	display: flex;
	align-items: center;
}

.inputviewb input {
	flex: 1;
	margin-right: 0.3125rem;
}

.inputviewb input:last-child {
	margin: 0;
}

.tmview {
	display: flex;
	align-items: center;
	background: #fafafa;
}

.zjbtn {
	display: flex;
	align-items: center;
	width: 6.25rem;
	height: 1.770833rem;
	background: #769c94;
	border-radius: 0.260416rem;
	justify-content: center;
	color: #FFFFFF;
	font-size: 0.833333rem;
	cursor: pointer;
	margin-bottom: 0 !important;
}

.zjbtn img {
	margin-right: 0.260416rem;
}

.zjstr {
	display: flex;
	align-items: center;
	margin-bottom: 0 !important;
}

.zjstr div {
	display: flex;
	align-items: center;
	margin: 0rem;
	font-size: 0.729166rem;
	color: #333333;
}

.zjstr span {
	color: #999999;
	font-size: 0.729166rem;
	padding: 0rem 0.520833rem;
}

.zjstr label {
	color: #333333;
	font-size: 0.729166rem;
}

.xt {
	padding-left: 0.520833rem;
}

.zjstr div label {
	padding: 0rem 0.260416rem;
}

.bottomview {
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 1.041666rem;
	margin-bottom: 1.041666rem !important;
}

.bbtn {
	display: flex;
	align-items: center;
}

.bbtn div {
	width: 7.8125rem;
	height: 2.083333rem;
	font-size: 0.833333rem;
	color: #FFFFFF;
	border-radius: 0.260416rem;
	display: flex;
	justify-content: center;
	align-items: center;
	cursor: pointer;
}

.bc {
	background: #c00714;
	margin-right: 2.604166rem;
}

.gb {
	background: #999999;
}

.bottomview div {
	margin-bottom: 0rem !important;
}

.bradio {
	display: flex;
	align-items: center;
}

.radioview {
	display: flex;
	align-items: center;
	font-size: 0.833333rem;
	color: #333333;
	margin-right: 1.5625rem;
	cursor: pointer;
}

.radioview label {
	width: 0.9375rem;
	height: 0.9375rem;
	border: 0.052083rem solid #cecece;
	border-radius: 0.260416rem;
	margin-right: 0.260416rem;
	cursor: pointer;
	box-sizing: border-box;
}

.radioacc {
	background: url(../img/checkboxse.png) no-repeat;
	background-size: cover;
	border: none !important;
}

#tmbox,
#tmbox2,
#tmbox3 {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	top: 0;
	background: rgba(0, 0, 0, 0.7);
	z-index: 999;
	display: flex;
	justify-content: center;
	align-items: center;
}

.tmborderview {
	width: 65.364583rem;
	height: 38rem;
	background: rgba(255, 255, 255, 0.5);
	position: relative;
	margin-top: -5rem;
}

.tmboxcontent {
	position: absolute;
	top: 0.520833rem;
	left: 0.520833rem;
	right: 0.520833rem;
	bottom: 0.520833rem;
	background: #FFFFFF;
}

.sjtitle {
	display: flex;
	height: 2rem;
	align-items: center;
	justify-content: center;
	background: #769c94;
	position: relative;
	font-size: 1.145833rem;
	color: #ffffff;
	font-weight: bold;
}

.sjtitle2 {
	display: flex;
	height: 3.28125rem;
	align-items: center;
	justify-content: center;
	background: #c00714;
	position: relative;
	font-size: 1.145833rem;
	color: #ffffff;
	font-weight: bold;
}

.sjtitle3 {
	display: flex;
	height: 3.28125rem;
	align-items: center;
	justify-content: center;
	background: #c00714;
	position: relative;
	font-size: 1.145833rem;
	color: #ffffff;
	font-weight: bold;
}

.delstr {
	font-size: 0.9375rem;
	color: #333333;
	text-align: center;
	padding: 1.5625rem;
}

.tmlxview {
	display: flex;
	align-items: center;
	background: #91b0a9;
	height: 2.1875rem;
	box-sizing: border-box;
	padding: 0rem 1.041666rem;
}

.tmlxview2 {
	justify-content: center !important;
}

.tmlxview div {
	font-size: 0.729166rem;
	color: #2d6459;
	display: flex;
	align-items: center;
	padding-right: 1.5625rem;
}

.tmlxview div label {
	font-size: 0.833333rem;
	color: #FFFFFF;
	background: #769c94;
	padding: 0.104166rem 0.520833rem;
	border-radius: 0.260416rem;
}

.tmsxview {
	padding: 1.041666rem;
	display: flex;
	align-items: center;
}

.tmsxview select {
	height: 1.25rem;
	border: none;
	outline-style: none;
	background: #f3f3f3;
	font-size: 0.729166rem;
	color: #999999;
	margin-right: 0.78125rem;
	padding: 0rem 0.520833rem;
}

.tmscrolltop {
	display: flex;
	align-items: center;
	height: 1.5625rem;
	background: #f3f3f3;
	font-size: 0.729166rem;
	color: #999999;
}

.checkboxview {
	width: 7.291666rem;
	display: flex;
	align-items: center;
}

.tm {
	width: 24.479166rem;
	line-height: 1.197916rem;
	height: 1.197916rem;
}

.tmlx {
	width: 5.208333rem;
}

.tmxk {
	width: 8.333333rem;
}

.tmzj {
	width: 13.541666rem;
}

.fnz {
	width: calc(100% - 7.291666rem - 24.479166rem - 5.208333rem - 8.333333rem - 13.541666rem);
}

.checkboxview .tmjt {
	width: 1.875rem;
	display: flex;
	justify-content: flex-end;
}

.checkboxview .tmjt img {
	width: 0.729166rem;
	display: block;
	cursor: pointer;
	transition: all .2s;
	-webkit-transition: all .2s;
	-moz-transition: all .2s;
	-o-transition: all .2s;
	-ms-transition: all .2s;
}

#tmbox {
	display: none;
}

.zhuan {
	transform: rotateZ(90deg);
	-webkit-transform: rotateZ(90deg);
	-moz-transform: rotateZ(90deg);
	-o-transform: rotateZ(90deg);
	-ms-transform: rotateZ(90deg);
}

.chenckbox,
.chenckboxall {
	width: 0.78125rem;
	height: 0.78125rem;
	border-radius: 0.260416rem;
	border: 0.052083rem solid #999999;
	margin-right: 0.520833rem;
	margin-left: 1.041666rem;
	cursor: pointer;
	background: #FFFFFF;
	box-sizing: border-box;
}

.ac {
	background: url(../img/checkboxse.png) no-repeat;
	background-size: cover;
	border: none !important;
}

.tmitems {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
	padding-top: 1.081666rem;
	padding-bottom: 1.081666rem;
	border-bottom: 0.052083rem solid #f3f3f3;
	cursor: pointer;
}

.dabox {
	width: 100%;
	box-sizing: border-box;
	padding: 1.041666rem 0.9375rem;
	margin: 0rem 0.9375rem;
	background: #f3f3f3;
	margin-top: 1.041666rem;
	display: none;
}

.ttt {
	display: flex;
	margin-bottom: 0.520833rem;
}

.ttt label {
	font-weight: bold;
	font-size: 0.833333rem;
	color: #333333;
	width: 3.125rem;
}

.ttt div {
	width: calc(100% - 3.125rem);
	font-size: 0.833333rem;
	color: #666666;
	line-height: 1.302083rem;
}

.tmitems .tm {
	color: #333333;
	font-size: 0.833333rem;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	box-sizing: border-box;
	padding-right: 0.520833rem;
}

.tmitems .tmlx,
.tmitems .tmxk,
.tmitems .tmzj,
.tmitems .fnz {
	font-size: 0.833333rem;
	color: #999999;
	box-sizing: border-box;
	padding-right: 0.520833rem;
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	box-sizing: border-box;
}

.tmscroll {
	height: 27.1875rem;
	overflow-y: auto;
}

.tmscroll2 {
	height: calc(100% - 3.28125rem - 2.1875rem) !important;
	overflow-y: auto;
}

.submitbox {
	height: 4.166666rem;
	display: flex;
	align-items: center;
	justify-content: center;
}

.submitbox div {
	width: 7.8125rem;
	height: 2.083333rem;
	color: #FFFFFF;
	border-radius: 0.260416rem;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 0.833333rem;
	cursor: pointer;
}

.tmscroll::-webkit-scrollbar {
	/*滚动条整体样式*/

	width: 5px;
	/*高宽分别对应横竖滚动条的尺寸*/

	height: 1px;

}

.tmscroll::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/

	border-radius: 10px;

	-webkit-box-shadow: inset 0 0 5px #E4E4E4;

	background: #999999;

}

.tmscroll::-webkit-scrollbar-track {
	/*滚动条里面轨道*/

	-webkit-box-shadow: inset 0 0 5px #FFFFFF;

	border-radius: 10px;

	background: #EDEDED;

}

#pbox2 {
	display: none;
}

#deletebox,
#deletebox2 {
	position: fixed;
	left: 0;
	right: 0;
	bottom: 0;
	top: 0;
	background: rgba(0, 0, 0, 0.7);
	z-index: 999;
	display: none;
	justify-content: center;
	align-items: center;
}

.deletesss {
	width: 26.041666rem;
	height: 15.625rem;
	background: #FFFFFF;
	border-radius: 0.260416rem;
	position: relative;
	margin-top: -200px;
}

.deldelpaper {
	position: absolute;
	left: 0;
	right: 0;
	bottom: 0.520833rem;
}

.yltm {
	padding: 1.041666rem;
	border-bottom: 0.052083rem dashed #dedede;
}

#tmbox2,
#tmbox3 {
	display: none;
}

.ylname,
.ylname * {
	font-size: 0.9375rem;
	color: #333333;
	line-height: 1.458333rem;
}

.ylname label {
	padding-right: 0.520833rem;
}

.tmlxlabel {
	padding-top: 0.520833rem;
	padding-bottom: 0.520833rem;
}

.tmlxlabel label {
	font-size: 0.833333rem;
	color: #FFFFFF;
	background: #769c94;
	padding: 0.15625rem 0.520833rem;
	border-radius: 0.260416rem;
}

.xuanxiang {
	font-size: 0.833333rem;
	color: #333333;
}

.xxbooo {
	font-size: 0.9375rem;
	padding-bottom: 0.260416rem;
}

.ylxx {
	line-height: 1.302083rem;
	color: #666666;
}

.dayl {
	color: #666666;
	font-size: 0.833333rem;
	padding-top: 0.520833rem;
	line-height: 1.354166rem;
}

.dayl label {
	font-size: 0.833333rem;
	color: #FFFFFF;
	background: #769c94;
	padding: 0.104166rem 0.520833rem;
	border-radius: 0.260416rem;
}

#ylbox1 {
	height: 100%;
	display: none;
}

#ylbox2 {
	text-align: center;
	font-size: 0.9375rem;
	color: #999999;
}

#ylbox2 div {
	font-size: 0.833333rem;
	color: #666666;
}

#loadimg {
	animation: xz 1s linear infinite;
	-webkit-animation: xz 1s linear infinite;
}

@keyframes xz {
	from {
		transform: rotateZ(0deg);
	}

	to {
		transform: rotateZ(360deg);
	}
}

@-webkit-keyframes xz

/* Safari 与 Chrome */
	{
	from {
		transform: rotateZ(0deg);
	}

	to {
		transform: rotateZ(360deg);
	}
}

#sssss,
#eeeee {
	margin-top: 13.020833rem;
}

#editbox2 {
	display: none;
}

#editbox1 div {
	text-align: center;
	font-size: 0.9375rem;
	color: #999999;
}

#tmbox3 input,
#tmbox3 select {
	outline-style: none;
	border: 0.052083rem solid #cecece;
	font-size: 0.833333rem;
	line-height: 2.291666rem;
	height: 2.291666rem;
	box-sizing: border-box;
	padding: 0 0.520833rem;
	border-radius: 0.260416rem;
}

.editboxsss {
	padding: 1.041666rem;
}

#tmbox3 {
	z-index: 998;
}

#tmbox3 .inputviewa {
	margin-bottom: 0.3125rem;
}

#tmbox3 .inputviewb {
	margin-bottom: 0.3125rem;
}

.bbbbbbbbb {
	display: flex;
	align-items: center;
	justify-content: center;
	margin-top: 26%;
}

#editbox1 {
	text-align: center;
}

.paperselect {
	outline-style: none;
	border: none;
	width: 15.625rem;
	height: 1.25rem;
	background: #f3f3f3;
	color: #999999;
	font-size: 0.729166rem;
}

.paperselectbtnview {
	margin-top: 0.520833rem;
}

.paperselectbtnview .paperselect {
	width: 6.25rem;
	margin-right: 0.520833rem;
}

.ppname {
	height: 1.979166rem;
	background: #fbfbfb;
	color: #333333;
	font-size: 0.833333rem;
	border-top: 0.052083rem solid #666666;
	margin-top: 0.520833rem;
	display: flex;
	align-items: center;
	padding-left: 0.520833rem;
}

.passwordbox {
	width: 26.041666rem;
	margin-left: 1.25rem;
}

.pwtitle {
	font-size: 1.145833rem;
	color: #333333;
	font-weight: bold;
	padding-top: 2.083333rem;
	padding-bottom: 1.041666rem;
	border-bottom: 0.052083rem dashed #c1c1c1;
	display: flex;
	align-items: center;
}

.pwwtbox select {
	outline-style: none;
	border: 0.052083rem solid #999999;
	width: 18.75rem;
	box-sizing: border-box;
	font-size: 0.833333rem;
	height: 2.5rem;
	border-radius: 0.260416rem;
	padding: 0px 0.78125rem;
	margin-top: 0.260416rem;
}

.editpasswordinputview .yzmbox {
	display: flex;
	align-items: center;
}

.editpasswordinputview .yzmbox input {
	width: 11.979166rem;
}

.editpasswordinputview .yzmbox img {
	display: block;
	height: 2.5rem;
	margin-left: 0.78125rem;
}

.pwtitle img {
	display: block;
	margin-right: 0.520833rem;
	width: 0.9375rem;
}

.aqwtstr {
	font-size: 0.9375rem;
	color: #333333;
	padding-top: 1.041666rem;
}

.anwts {
	font-size: 0.9375rem;
	color: #999999;
	padding-top: 0.520833rem;
}

.editpasswordinputview {
	margin-top: 1.302083rem;
}

.editpasswordinputview div {
	font-size: 0.9375rem;
	color: #333333;
	padding-bottom: 0.260416rem;
}

.editpasswordinputview input {
	width: 18.75rem;
	height: 2.5rem;
	outline-style: none;
	box-sizing: border-box;
	border-radius: 0.260416rem;
	border: 0.052083rem solid #999999;
	padding: 0px 1.041666rem;
	font-size: 0.833333rem;
}

.editbtnview {
	display: flex;
	align-items: center;
	margin-top: 2.083333rem;
}

.querenbtnbtn,
.quxiaoquxiaobtn {
	width: 7.8125rem;
	height: 2.083333rem;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 0.9375rem;
	color: #FFFFFF;
	border-radius: 0.260416rem;
	cursor: pointer;
}

.querenbtnbtn {
	background: #c00714;
}

.quxiaoquxiaobtn {
	background: #999999;
	margin-left: 0.78125rem;
}

.xxrwitem {
	border-bottom: 0.052083rem dashed #dedede;
	display: flex;
	align-items: center;
	padding-bottom: 0.78125rem;
	padding-top: 0.78125rem;
}

.xxrmitemr {
	display: flex;
	align-items: center;
}

.xxrwiteml {
	width: calc(100% - 4.166667rem*2 - 0.625rem);
	box-sizing: border-box;
	padding-right: 2.604167rem;
}

.xxrwyl,
.xxrwsc {
	width: 4.166667rem;
	height: 1.458333rem;
	display: flex;
	align-items: center;
	justify-content: center;
	color: #FFFFFF;
	font-size: 0.833333rem;
	border-radius: 0.260417rem;
	cursor: pointer;
}

.xxrwyl {
	background: #f8b967;
	margin-right: 0.625rem;
}

.xxrwsc {
	background: #c00714;
}

.xxrwtitle {
	display: flex;
	align-items: center;
}

.xxrwstr {
	box-sizing: border-box;
	font-size: 0.729167rem;
	color: #cecece;
	padding-top: 0.520833rem;
}

.xxrwtitle div {
	width: calc(100% - 2.8125rem);
	overflow: hidden;
	text-overflow: ellipsis;
	white-space: nowrap;
	box-sizing: border-box;
	font-size: 0.9375rem;
	color: #333333;
}

.xxrwjxz {
	width: 2.8125rem;
	height: 0.9375rem;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 0.625rem;
	background: #a9f4cd;
	color: #3ea972;
	border-radius: 0.260417rem;
}

.rwqx {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}

.rwqxtitle {
	width: 100%;
	font-size: 0.9375rem;
	color: #333333;
	font-weight: bold;
	padding-bottom: 0.78125rem;
}

.nodatamsg{
	font-size: 0.833333rem;
	color: #999999;
}

.rwtitle {
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}

.rwtitle div {
	width: 100%;
	font-size: 0.9375rem;
	color: #333333;
	font-weight: bold;
}

.rwtitle input {
	width: 23.4375rem;
	height: 2.083333rem;
	border: 0.052083rem solid #cecece;
	outline: none;
	border-radius: 0.260417rem;
	margin-top: 0.260417rem;
	box-sizing: border-box;
	padding: 0px 0.520833rem;
	font-size: 0.833333rem;
}

.rwtitle label {
	font-size: 0.833333rem;
	margin-left: 0.520833rem;
	color: #cecece;
}

.rwbt {
	color: #c00714 !important;
}

.rwtitle span {
	padding: 0px 0.9375rem;
}

.rwtitle select {
	width: 23.4375rem;
	height: 2.083333rem;
	border: 0.052083rem solid #cecece;
	outline: none;
	border-radius: 0.260417rem;
	margin-top: 0.260417rem;
	box-sizing: border-box;
	padding: 0px 0.520833rem;
	font-size: 0.833333rem;
}

.checks {
	display: flex;
	align-items: center;
	margin-top: 0.260417rem;
}

.checks div {
	width: auto !important;
	font-weight: normal !important;
	font-size: 0.833333rem !important;
}

.rwcc {
	display: flex;
	align-items: center;
	margin-right: 3.125rem;
}

.rwcc div {
	width: 1.458333rem !important;
	height: 1.458333rem;
	border-radius: 0.260417rem;
	border: 0.052083rem solid #cecece;
	margin-right: 0.520833rem;
	margin-left: 0;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
}

.rwcc div label {
	width: 100%;
	height: 100%;
	background: #c00714;
	border-radius: 0.260417rem;
	margin: 0 !important;
	cursor: pointer;
	box-sizing: border-box;
	border: 0.104167rem solid #FFFFFF;
}

.rwqxname{
	font-size: 0.833333rem;
	font-weight: bold;
	color: #333333;
	margin-top: 0.520833rem;
}
.rwqxview{
	display: flex;
	align-items: flex-start;
	margin-top: 0.260417rem;
}
.cbox{
	display: flex;
	align-items: center;
	cursor: pointer;
	font-size: 0.833333rem;
	color: #999999;
	line-height: 1.5625rem;
}
.cbox label{
	width: 0.729167rem;
	height: 0.729167rem;
	box-sizing: border-box;
	border-radius: 0.260417rem;
	border: 0.052083rem solid #cecece;
	display: flex;
	margin-right: 0.15625rem;
	cursor: pointer;
}
.rwaccc span{
	flex: 1;
	background: #c00714;
	border: 0.052083rem solid #FFFFFF;
	border-radius: 0.260417rem;
}
.rwqxviewleft{
	width: 4.166667rem;
}
.rwqxviewright{
	font-size: 0.833333rem;
	color: #999999;
	width: calc(100% - 4.166667rem);
	display: flex;
	align-items: center;
	flex-wrap: wrap;
}
.rwqxviewright .cbox{
	margin: 0px 1.041667rem;
}
.cboxname{
	font-size: 0.833333rem;
	color: #666666;
	margin-top: 0.260417rem;
}
.rwzfdiv{
	margin-top: 0.260417rem;
}
.rwzfdiv input{
	width: 23.958333rem;
	height: 2.083333rem;
	box-sizing: border-box;
	border: 0.052083rem solid #cecece;
	border-radius: 0.260417rem;
	padding: 0px 0.520833rem;
	font-size: 0.833333rem;
	outline: none;
}
.rwqzview{
	width: 100%;
	box-sizing: border-box;
	padding-left: 1.041667rem;
	display: flex;
	align-items: center;
	justify-content: space-between;
	margin-top: 1.5625rem;
}
.rwqzitem{
	display: flex;
	align-items: flex-start;
	flex-wrap: wrap;
	flex-direction: column;
}
.rwqzitem div{
	width: 6.666667rem;
	height: 1.145833rem;
	background: #cecece;
	font-size: 0.729167rem;
	color: #333333;
	box-sizing: border-box;
	padding: 0px 0.520833rem;
	border-radius: 0.260417rem;
	border-bottom-left-radius: 0;
	border-bottom-right-radius: 0;
}
.rwqzitem input{
	width: 11.458333rem;
	height: 1.458333rem;
	box-sizing: border-box;
	border: 0.052083rem solid #cecece;
	border-radius: 0.260417rem;
	padding: 0px 0.520833rem;
	font-size: 0.729167rem;
	outline: none;
	border-top-left-radius: 0;
}
.qzbox{
	width: 11.458333rem;
	height: 2.604167rem;
	background: #fff2f3;
}
.qzzzzzz{
	width: 100%;
	display: flex;
	justify-content: flex-end;
	margin-top: 0.78125rem;
}
.qzbox{
	border-radius: 0.260417rem;
	display: flex;
	align-items: center;
	box-sizing: border-box;
	padding: 0.520833rem;
}
.qxl{
	width: calc(100% - 2.083333rem);
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 1.25rem;
	color: #c00714;
	font-weight: bold;
}
.qzr{
	width: 2.083333rem;
	border-left: 0.052083rem solid #fbb5bb;
	box-sizing: border-box;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
}
.qzr img{
	display: block;
	width: 0.9375rem;
	cursor: pointer;
}
.qzr:hover .qzstr{
	display: block;
}
.qzstr{
	position: absolute;
	width: 18.229167rem;
	font-size: 0.729167rem;
	color: #999999;
	line-height: 1.302083rem;
	z-index: 99;
	background: #fcfcfc;
	box-sizing: border-box;
	padding: 0.520833rem;
	border: 0.052083rem solid #cecece;
	border-radius: 0.260417rem;
	top: 1.302083rem;
	display: none;
}
.kjzybox{
	box-sizing: border-box;
	margin-left: 1.041667rem;
	width: calc(100% - 1.041667rem);
	border-top: 0.052083rem dashed #dedede;
	border-bottom: 0.052083rem dashed #dedede;
	margin-top: 1.5625rem;
	padding: 1.041667rem 2.083333rem;
	background: #fcfcfc;
}
.kjzyitemname{
	font-size: 0.833333rem;
	color: #333333;
	font-weight: bold;
}
.kjzyitembox{
	display: flex;
	align-items: flex-start;
	margin-top: 0.520833rem;
}
.kjzyitemboxl{
	width: 2.916667rem;
	height: 1.666667rem;
	background: #ffe9bf;
	border-radius: 0.260417rem;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 0.833333rem;
	color: #f36933;
}
.kjzyitemboxr{
	width: calc(100% - 2.916667rem);
	padding-left: 0.520833rem;
	box-sizing: border-box;
}
.iiiitem .input0{
	width: calc(20rem - 1.979167rem);
	height: 1.5625rem;
	box-sizing: border-box;
	border: none;
	border-radius: 0.260417rem;
	padding: 0px 0.520833rem;
	font-size: 0.729167rem;
	outline: none;
	margin-right: 0.520833rem;
}
.iiiitem .input1,.iiiitem .input2{
	width: 0;
	display: none;
}
.addxxrw{
	width: 15.625rem;
	height: 1.5625rem;
	background: #fff2f3;
	box-sizing: border-box;
	border: 0.052083rem dashed #e56c74;
	border-radius: 0.260417rem;
	font-size: 0.729167rem;
	color: #e56c74;
	display: flex;
	align-items: center;
	justify-content: center;
	cursor: pointer;
	margin-top: 0.520833rem;
}
.kjzyitem{
	margin-bottom: 2.083333rem;
}
.rwbtnview{
	margin-top: 2.083333rem;
	padding-bottom: 2.083333rem;
}
.rwbtnview div{
	width: 8.333333rem;
	height: 2.083333rem;
	display: flex;
	align-items: center;
	justify-content: center;
	font-size: 0.833333rem;
	color: #FFFFFF;
	background: #c00714;
	border-radius: 0.260417rem;
	cursor: pointer;
}

.xxrwtc{
	position: fixed;
	top: 0;
	right: 0;
	bottom: 0;
	left: 0;
	background: rgba(0, 0, 0, 0.7);
	z-index: 999;
	display: none;
}
.tcview{
	position: absolute;
	width: 65.3125rem;
	height: 42.708333rem;
	background: transparent;
	margin: auto;
	left: 0;
	right: 0;
	top: 4.166667rem;
	box-sizing: border-box;
	padding: 0;
	border-radius: 8px;
	overflow: hidden;
}
.rwtcbox{
	background: #FFFFFF;
	width: 100%;
	height: 100%;
	border-radius: 8px;
	overflow: hidden;
	box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
}
.rwtitletc{
	height: 3.28125rem;
	background: #e88c42;
	font-size: 1.25rem;
	color: #FFFFFF;
	font-weight: bold;
	display: flex;
	align-items: center;
	justify-content: center;
	position: relative;
	border-radius: 8px 8px 0 0;
	border: none;
	margin: 0;
}
.rwtitletc img{
	position: absolute;
	margin: auto;
	top: 0;
	bottom: 0;
	right: 1.041667rem;
	display: block;
	width: 1.25rem;
	cursor: pointer;
}
.rwstr{
	width: 100%;
	height: 2.1875rem;
	background: #eda368;
	display: flex;
	align-items: center;
	border: none;
	margin: 0;
}
.rwstr .rwstritem{
	font-size: 0.729167rem;
	color: #a04d0b;
	display: flex;
	align-items: center;
	padding-left: 1.25rem;
	padding-right: 1.25rem;
}
.rwstr .rwstritem img{
	width: 1.5625rem;
	display: block;
}
.rwinfobox{
	box-sizing: border-box;
	padding: 1.041667rem;
	overflow-y: scroll;
	width: 100%;
	height: calc(100% - 3.28125rem - 2.1875rem);
	border: none;
	margin: 0;
	border-radius: 0 0 8px 8px;
	background: #FFFFFF;
}
.rwinfobox::-webkit-scrollbar {
	/*滚动条整体样式*/

	width: 5px;
	/*高宽分别对应横竖滚动条的尺寸*/

	height: 1px;

}

.rwinfobox::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/

	border-radius: 10px;

	-webkit-box-shadow: inset 0 0 5px #eeeeee;

	background: #cccccc;

}

.rwinfobox::-webkit-scrollbar-track {
	/*滚动条里面轨道*/

	-webkit-box-shadow: inset 0 0 5px #FFFFFF;

	border-radius: 10px;

	background: #EDEDED;

}
.rwinfotitle{
	font-size: 1.041667rem;
	color: #333333;
	font-weight: bold;
}
.yxz{
	font-size: 0.833333rem;
	color: #333333;
	font-weight: bold;
	margin-top: 0.520833rem;
}
.rwinfoitem{
	display: flex;
	align-items: center;
	margin-bottom: 0.520833rem;
}
.rwitemleft{
	width: 3.125rem;
	height: 1.041667rem;
	font-size: 0.729167rem;
	color: #f36933;
	background: #fff5e1;
	display: flex;
	align-items: center;
	justify-content: center;
	border-radius: 0.260417rem;
}
.rwitemright{
	width: calc(100% - 3.125rem);
	box-sizing: border-box;
	padding-left: 1.041667rem;
}
.rwitemright{
	font-size: 0.833333rem;
	color: #999999;
}
.rwitemright label{
	color: #666666;
}
.rwinfolist{
	padding-top: 0.520833rem;
	padding-bottom: 0.520833rem;
}
.btnnew{
	display: flex;
	padding-top: 0.260417rem;
}
.btnnew div{
	padding: 0.15625rem 0.78125rem;
	background: rgb(96, 150, 230);
	font-size: 0.833333rem;
	color: #FFFFFF;
	border-radius: 0.260417rem;
	cursor: pointer;
}


.zyitembox{
	box-sizing: border-box;
	padding-left: 40px;
}
.bjitembox{
	box-sizing: border-box;
	padding-left: 40px;
}
.lianjibox{
	width: 100%;
	background: #FFFFFF;
	display: flex;
	align-items: flex-start;
}
.rwqxlist{
	width: 100%;
}
.rbox{
	background: #fcfcfc;
}
.xybox,.zybox,.bjbox{
	width: 13.020833rem;
	border-right: 0.052083rem solid #eeeeee;
	margin: 0.520833rem 0;
	height: 7.8125rem;  
	overflow-y: auto;
}
.rname label{
	margin-left: 0.520833rem;
}
.rname{
	font-size: 0.833333rem;
	background: #eeeeee;
	margin: 0.260417rem;
	border-radius: 0.260417rem;
	padding: 0.260417rem;
	font-weight: bold;
}
.yxzbj{
	border: 0.052083rem dashed #cccccc;
	padding: 0 0.520833rem;
	border-radius: 0.260417rem;
}
.scrollview1::-webkit-scrollbar {
	/*滚动条整体样式*/

	width: 5px;
	/*高宽分别对应横竖滚动条的尺寸*/

	height: 1px;

}

.scrollview1::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/

	border-radius: 10px;

	-webkit-box-shadow: inset 0 0 5px #eeeeee;

	background: #999999;

}

.scrollview1::-webkit-scrollbar-track {
	/*滚动条里面轨道*/

	-webkit-box-shadow: inset 0 0 5px #FFFFFF;

	border-radius: 10px;

	background: #EDEDED;

}


.bjbox{
	border: none;
}
.ljitem{
	font-size: 0.833333rem;
	cursor: pointer;
	line-height: 1.5625rem;
	padding: 0 0.520833rem;
}
.ljitem:hover{
	background: rgba(0, 0, 0, 0.1);
}
.gx1{
	color: #c00714;
	position: relative;
}
.gx1::after{
	content: '';
	position: absolute;
	width: 1.5625rem;
	bottom: 0;
	top: 0;
	right: 0.520833rem;
	background: url(../img/gou.png) no-repeat;
	background-size: 60%;
	background-position: center;
}
.gx{
	color: #c00714;
}
#yxz{
	height: auto;
	padding-bottom: 0.520833rem;
	display: flex;
	flex-wrap: wrap;
}
#yxz label{
	font-size: 0.833333rem;
	margin-right: 0.78125rem;
	display: flex;
	align-items: center;
	margin-top: 0.520833rem;
	background: #f3f3f3;
	border-radius: 0.260417rem;
	padding: 0.260417rem 0.520833rem;
}

#yxz label span{
	background: url(../img/closered.png) no-repeat;
	width: 0.78125rem;
	height: 0.78125rem;
	background-size: 80%;
	cursor: pointer;
	background-position: center;
	margin-left: 0.260417rem;
}

.newiitem{
	position: relative;
}
.newiitembox{
	width: 20rem;
	position: absolute;
	z-index: 9;
	display: none;
	border: 0.052083rem solid #cecece;
	box-sizing: border-box;
	margin-top: 0.260417rem;
}

.scrollview{
	height: 15.625rem;
	overflow: auto;
	background: #FFFFFF;
	box-sizing: border-box;
}

.scrollview::-webkit-scrollbar {
	/*滚动条整体样式*/

	width: 5px;
	/*高宽分别对应横竖滚动条的尺寸*/

	height: 1px;

}

.scrollview::-webkit-scrollbar-thumb {
	/*滚动条里面小方块*/

	border-radius: 10px;

	-webkit-box-shadow: inset 0 0 5px #eeeeee;

	background: #999999;

}

.scrollview::-webkit-scrollbar-track {
	/*滚动条里面轨道*/

	-webkit-box-shadow: inset 0 0 5px #FFFFFF;

	border-radius: 10px;

	background: #EDEDED;

}

.itemtype {
	color: #FFFFFF;
	font-size: 0.520833rem;
	padding: 0.104166rem 0.3125rem;
	border-radius: 0.260416rem;
	margin-right: 0.260417rem;
}

.pdf {
	background: #f8b967;
}

.ppt,
.pptx {
	background: #f36933;
}

.video,
.mp4,
.rmvb {
	background: #7a92bb;
}
.newzyitem{
	font-size: 0.729167rem;
	box-sizing: border-box;
	line-height: 1.5625rem;
	padding: 0 0.520833rem;
	cursor: pointer;
	overflow: hidden;
	white-space: nowrap;
	text-overflow: ellipsis;
}
.newzyitem:hover{
	background: #eeeeee;
}

.errmsg{
	font-size: 0.833333rem;
	color: #999999;
	text-align: center;
	padding-top: 30%;
}

.inputboxrw{
	display: flex;
	align-items: center;
	border: 0.052083rem solid #cecece;
	width: 20rem;
	box-sizing: border-box;
	border-radius: 0.260417rem;
}
.inputboxrw img{
	display: block;
	width: 0.9375rem;
	height: auto;
	padding: 0px 0.520833rem;
	cursor: pointer;
}

.closediv{
	display: flex;
	align-items: center;
	justify-content: flex-end;
	box-sizing: border-box;
	width: 100%;
	background: #FFFFFF;
	padding: 0.260417rem 0;
}
.closediv img{
	width: 0.9375rem;
	cursor: pointer;
	margin-right: 0.260417rem;
}
.yxkjzyitem{
	font-size: 0.833333rem;
	display: flex;
	align-items: center;
	justify-content: flex-start;
	padding-top: 0.520833rem;
}
.yxkjzyitem img{
	width: 0.729167rem;
	display: block;
	margin-left: 0.260417rem;
	cursor: pointer;
}
.yxtitle{
	font-size: 0.833333rem;
	font-weight: bold;
}