<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-学院简介</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/collegeprofile.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
		<style>
			.boxright {
				width: calc(100% - 12.239583rem*2);
			}

			.border {
				background: linear-gradient(to right, #c00714, #c00714, #ffc156);
				height: 2px;
			}

			.itemsssname {
				height: 2.864583rem;
				display: flex;
				align-items: center;
				font-size: 0.9375rem;
				color: #c00714;
				font-weight: bold;
				border-bottom: 0.052083rem solid #e1e1e1;
				background: #FFFFFF;
				justify-content: center;
			}

			.itemnr {
				font-size: 0.833333rem;
				color: #333333;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				box-sizing: border-box;
				padding-left: 0.78125rem;
				position: relative;
			}
			.itemnr::after {
				position: absolute;
				content: "";
				width: 0.15625rem;
				height: 0.15625rem;
				background: #cecece;
				left: 0.260416rem;
				top: 0;
				bottom: 0;
				margin: auto;
				border-radius: 100rem;
			}
			.boxitem {
			    display: flex;
			    align-items: center;
			    height: 2.239583rem;
			    border-bottom: 0.052083rem dashed #e1e1e1;
			    margin-left: 0.260416rem;
			    margin-right: 0.260416rem;
			    cursor: pointer;
			}
			.boxxxxxxx{
				background: #FFFFFF;
			}
			.boxitem:last-child {
			    border: none;
			}
			.itemsss:last-child{
				margin-top: 0.78125rem;
			}
		</style>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />学院简介</label>
					</div>
					<div class="leftitembox" id="classview">

					</div>
				</div>
				<div class="boxright">
					<div class="borderbag"></div>
					<div class="righttop">
						<div class="dd1">
							<label><img src="img/zz.png" /><span id="zz"></span></label>
							<label><img src="img/ly.png" /><span id="ly"></span></label>
						</div>
						<div class="dd2">
							<label><img src="img/sj2.png" /><span id="sj"></span></label>
							<label><img src="img/yj.png" /><span id="views"></span></label>
						</div>
					</div>
					<div class="nrnr">
						<div class="titlenr" id="titles"></div>
						<div class="nr" id="nr">


						</div>
					</div>
				</div>
				<div class="boxleft">
					<div class="itemsss">
						<div class="border"></div>
						<div class="itemsssname">科研工作</div>
						<div class="boxxxxxxx" id="list1">
							
						</div>
					</div>
					<div class="itemsss">
						<div class="border"></div>
						<div class="itemsssname">教学实践</div>
						<div class="boxxxxxxx" id="list2">
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				let userinfo = sessionStorage.getItem("userinfo")
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
					
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				getclass('collegeprofile.html')
				getclassid()
				getfooterlink()
			})
			function clicknum(id){
				$.ajax({
					url: baseurl + "/web/posts/click/"+id,
					type: 'GET',
					contentType: "application/json",
					dataType: 'json',
					success: (res) => {
					}
				})
			}
			function getclassid() {
				//获取学院简介分类
				$.ajax({
					url: baseurl + "/web/about",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data[0].children.map((item) => {
								if (item.redirectUrl == 'collegeprofile2.html') {
									html += '<div class="leftitem activeleftitem"><a href="' + item.redirectUrl + '">' + item.name +
										'</a></div>'
								} else {
									html += '<div class="leftitem"><a href="' + item.redirectUrl + '">' +
										item.name + '</a></div>'
								}
							})
							$("#classview").html(html)
							getinfo()
							getrightlist(res.data[0].children[2].children)
						}
					}
				})
			}
			function inxq(item){
				window.location.href = "collegeprofile3.html?id=" + $(item).attr("data-id")
			}
			function getrightlist(data){
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: 1,
						pageSize: 5,
						categoryId: data[0].id
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data)
							let html = ""
							res.data.list.map((item)=>{
								html+='<div class="boxitem" onclick="inxq(this)" data-id="'+item.id+'"><div class="itemnr">'+item.title+'</div></div>'
							})
							$("#list1").html(html)
						}
					}
				})
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: 1,
						pageSize: 5,
						categoryId: data[1].id
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data)
							let html = ""
							res.data.list.map((item)=>{
								html+='<div class="boxitem" onclick="inxq(this)" data-id="'+item.id+'"><div class="itemnr">'+item.title+'</div></div>'
							})
							$("#list2").html(html)
						}
					}
				})
			}
			function getinfo() {
				$.ajax({
					url: baseurl + "/web/posts/"+getUrlParam('id'),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data)
							let items = res.data
							$("#titles").html(items.title)
							$("#nr").html(items.content)
							$("#zz").html(items.author)
							$("#ly").html(items.excerpt)
							$("#sj").html(setDate(items.createdAt))
							$("#views").html(items.clickCount)
							clicknum(items.id)
						}
					}
				})
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					if (res.data[i].id == getUrlParam('id') || res.data[i].redirectUrl ==
			// 						'collegeprofile.html') {
			// 						html += '<a class="menuitem active" href="' + res.data[i].redirectUrl + '?id=' +
			// 							res
			// 							.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					} else {
			// 						html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 							.data[
			// 								i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					}
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d
			}

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
