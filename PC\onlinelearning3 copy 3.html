<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">
		<title>思政一体化平台-在线学习-红色书籍</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/onlinelearning.css" />
		<link href="css/vendor/bootstrap.min.css" rel="stylesheet">
		<link href="css/vendor/animate.min.css" rel="stylesheet">
		<link rel="stylesheet" href="css/vendor/fontawesome.min.css">
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>
		<script src="js/vendor/bootstrap.bundle.min.js"></script>
		<style>
			/* 页面加载动画 */
			.loading-overlay {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(255, 255, 255, 0.95);
				display: flex;
				justify-content: center;
				align-items: center;
				z-index: 9999;
				transition: opacity 0.3s;
			}
			
			.loading-spinner {
				width: 50px;
				height: 50px;
				border: 3px solid #f3f3f3;
				border-top: 3px solid #A65D57;
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}
			
			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}

			/* 紧凑型筛选条件区域 - 红色风格 */
			.filter-container {
				background-color: #fff;
				border: 1px solid #eee;
				border-radius: 4px;
				margin: 10px 0;
				padding: 15px;
				box-shadow: 0 2px 4px rgba(0,0,0,0.05);
				display: none; /* 默认隐藏 */
			}
			
			/* 筛选器标题样式 */
			.filter-toggle {
				width: 100%;
				padding: 10px 15px;
				background-color: #d82121;
				color: white;
				border: none;
				border-radius: 4px;
				margin-bottom: 10px;
				text-align: left;
				font-size: 14px;
				font-weight: 500;
				cursor: pointer;
				display: flex;
				justify-content: space-between;
				align-items: center;
				transition: background-color 0.3s;
			}
			
			.filter-toggle:hover {
				background-color: #c01c1c;
			}
			
			.filter-toggle i {
				transition: transform 0.3s;
			}
			
			.filter-toggle.collapsed i {
				transform: rotate(180deg);
			}

			/* 筛选行样式 */
			.filter-row {
				display: flex;
				flex-wrap: wrap;
				gap: 20px;
				margin-bottom: 15px;
			}
			
			.filter-section {
				flex: 1;
				min-width: calc(50% - 10px);
			}
			
			/* 标签样式统一 */
			.tjbox {
				display: flex;
				flex-wrap: wrap;
				gap: 8px;
				margin-top: 5px;
			}
			
			.tjbox span {
				padding: 6px 12px;
				background-color: transparent; /* 去掉背景色 */
				border: 1px solid #e8e8e8;
				border-radius: 4px;
				color: #d82121; /* 红色文字 */
				cursor: pointer;
				font-size: 13px;
				transition: all 0.2s;
			}
			
			.tjbox span:hover {
				background-color: rgba(216, 33, 33, 0.05); /* 轻微浅红色背景 */
				color: #d82121;
				border-color: #d82121;
			}

			.tjbox span.tjactive {
				background-color: #d82121 !important;
				color: white !important;
				border-color: #d82121 !important;
				font-weight: 500; /* 稍微加粗 */
			}

			/* 明确定义非激活状态样式 */
			.tjbox span:not(.tjactive) {
				background-color: transparent !important;
				color: #d82121 !important;
				border: 1px solid #e8e8e8 !important;
			}

			/* 文件类型标签特殊样式 - 保留左边框颜色 */
			#filetypebox span:not(.tjactive) {
				background-color: #f5f5f5 !important;
				color: #666 !important;
				border-top: 1px solid #e8e8e8 !important;
				border-right: 1px solid #e8e8e8 !important;
				border-bottom: 1px solid #e8e8e8 !important;
			}

			/* 课程选择区域样式 */
			.course-section {
				margin: 15px 0;
				padding: 15px;
				background-color: #f8f9fa;
				border-radius: 4px;
				border: 1px solid #e9ecef;
			}
			
			.course-grid {
				display: grid;
				grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
				gap: 10px;
				margin-top: 10px;
			}
			
			.course-item {
				padding: 8px 12px;
				background-color: white;
				border: 1px solid #e8e8e8;
				border-radius: 4px;
				cursor: pointer;
				text-align: center;
				transition: all 0.2s;
				font-size: 13px;
				color: #666;
			}
			
			.course-item:hover {
				border-color: #d82121;
				color: #d82121;
				transform: translateY(-2px);
			}
			
			.course-item.active {
				background-color: #d82121;
				color: white;
				border-color: #d82121;
			}

			/* 筛选器标签样式 */
			.filter-label {
				color: #333;
				font-weight: 500;
				font-size: 14px;
				margin-bottom: 8px;
			}

			/* 文件类型标签特殊样式 */
			#filetypebox span {
				border-left-width: 3px;
				border-left-style: solid;
			}
			
			/* PDF已移除 */
			#filetypebox span[data-id="docx"], 
			#filetypebox span[data-id="doc"] { border-left-color: #3498db; }
			#filetypebox span[data-id="pptx"],
			#filetypebox span[data-id="ppt"] { border-left-color: #e67e22; }
			/* xlsx格式已移除 */
			#filetypebox span[data-id="mp4"] { border-left-color: #34495e; }
			#filetypebox span[data-id="mp3"] { border-left-color: #9b59b6; }

			/* 响应式优化 */
			@media (max-width: 768px) {
				.filter-section {
					min-width: 100%;
				}
				
				.filter-row {
					gap: 10px;
				}
				
				.course-grid {
					grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
				}
				
				.course-item {
					font-size: 12px;
					padding: 6px 10px;
				}
			}

			/* 移除原有的章节选择器样式 */
			.chapter-selects-group {
				display: none;
			}

			/* 课程选择区域始终显示 */
			#courseSelect {
				display: none; /* 隐藏原有的下拉选择框 */
			}

			/* 新增课程网格样式 */
			.course-grid {
				display: grid;
				grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
				gap: 10px;
				margin: 15px 0;
				padding: 15px;
				background-color: #f8f9fa;
				border-radius: 4px;
			}
			
			.course-item {
				padding: 8px 12px;
				background-color: white;
				border: 1px solid #e8e8e8;
				border-radius: 4px;
				cursor: pointer;
				text-align: center;
				transition: all 0.2s;
				font-size: 13px;
			}
			
			.course-item:hover {
				border-color: #d82121;
				color: #d82121;
				transform: translateY(-2px);
			}
			
			.course-item.active {
				background-color: #d82121;
				color: white;
				border-color: #d82121;
			}

			/* 搜索框样式 */
			.search-container {
				margin-bottom: 15px;
				display: flex;
				align-items: center;
			}
			
			.search-input {
				flex: 1;
				height: 36px;
				border: 1px solid #ddd;
				border-radius: 4px;
				padding: 0 15px;
				font-size: 14px;
			}
			
			.search-input:focus {
				outline: none;
				border-color: #d82121;
				box-shadow: 0 0 0 2px rgba(216, 33, 33, 0.1);
			}
			
			.search-button {
				height: 36px;
				background-color: #d82121;
				color: white;
				border: none;
				border-radius: 0 4px 4px 0;
				padding: 0 15px;
				margin-left: -1px;
				cursor: pointer;
				font-size: 14px;
				transition: background-color 0.2s;
			}
			
			.search-button:hover {
				background-color: #b81c1c;
			}

			/* 结果计数和重置按钮行 */
			.results-actions {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin: 10px 0;
				flex-wrap: wrap;
				gap: 10px;
			}
			
			.results-count {
				padding: 6px 12px;
				background-color: #f8f9fa;
				border-radius: 4px;
				font-size: 14px;
				color: #666;
				display: flex;
				align-items: center;
			}
			
			.results-count i {
				margin-right: 5px;
				color: #d82121;
			}
			
			.results-count strong {
				color: #d82121;
				font-weight: bold;
				margin: 0 3px;
			}
			
			.filter-actions {
				display: flex;
				gap: 10px;
			}
			
			.reset-button {
				padding: 8px 15px;
				border: 1px solid #ddd;
				border-radius: 4px;
				background-color: white;
				color: #666;
				font-size: 14px;
				cursor: pointer;
				transition: all 0.2s;
				display: flex;
				align-items: center;
			}
			
			.reset-button:hover {
				border-color: #d82121;
				color: #d82121;
			}
			
			.reset-button i {
				margin-right: 5px;
				font-size: 14px;
			}
			
			.sort-button {
				padding: 8px 15px;
				border: 1px solid #ddd;
				border-radius: 4px;
				background-color: white;
				color: #666;
				font-size: 14px;
				cursor: pointer;
				transition: all 0.2s;
				display: flex;
				align-items: center;
			}
			
			.sort-button:hover {
				border-color: #d82121;
				color: #d82121;
			}
			
			.sort-button i {
				margin-left: 5px;
				transition: transform 0.3s;
			}
			
			.sort-button i.desc {
				transform: rotate(180deg);
			}

			/* 文件类型标签颜色 */
			#filetypebox span[data-id="pdf"] {
				border-left: 3px solid rgba(231, 76, 60, 1);
			}
			
			#filetypebox span[data-id="docx"], 
			#filetypebox span[data-id="doc"] {
				border-left: 3px solid rgba(52, 152, 219, 1);
			}
			
			#filetypebox span[data-id="pptx"],
			#filetypebox span[data-id="ppt"] {
				border-left: 3px solid rgba(230, 126, 34, 1);
			}
			
			/* xlsx格式已移除 */
			#filetypebox span[data-id="xls"] {
				border-left: 3px solid rgba(46, 204, 113, 1);
			}
			
			#filetypebox span[data-id="mp4"],
			#filetypebox span[data-id="mov"],
			#filetypebox span[data-id="avi"] {
				border-left: 3px solid rgba(52, 73, 94, 1);
			}
			
			#filetypebox span[data-id="mp3"],
			#filetypebox span[data-id="wav"] {
				border-left: 3px solid rgba(155, 89, 182, 1);
			}
			
			#filetypebox span[data-id="jpg"],
			#filetypebox span[data-id="png"],
			#filetypebox span[data-id="gif"] {
				border-left: 3px solid rgba(41, 128, 185, 1);
			}
			
			/* 响应式布局调整 */
			@media (max-width: 768px) {
				.filter-section {
					width: 100%;
					min-width: 100%;
					margin-right: 0;
				}
				
				.results-actions {
					flex-direction: column;
					align-items: flex-start;
				}
				
				.filter-actions {
					width: 100%;
				}
				
				.reset-button, .sort-button {
					flex: 1;
					justify-content: center;
				}
				
				.chapter-selects-group {
					flex-direction: column;
				}
				
				.chapter-select {
					width: 100%;
				}
				
				.search-container {
					flex-direction: column;
				}
				
				.search-input {
					width: 100%;
					margin-bottom: 10px;
				}
				
				.search-button {
					width: 100%;
					margin-left: 0;
					border-radius: 4px;
				}
			}

			/* 内容卡片动画 */
			.txtitem {
				transition: all 0.3s ease;
				opacity: 0;
				transform: translateY(20px);
				animation: fadeInUp 0.6s ease forwards;
				animation-play-state: paused;
				width: calc(20% - 20px) !important; /* 统一卡片宽度 */
				margin: 10px !important; /* 统一外边距 */
			}
			
			@keyframes fadeInUp {
				from {
					opacity: 0;
					transform: translateY(20px);
				}
				to {
					opacity: 1;
					transform: translateY(0);
				}
			}

			.txtitem.show {
				animation-play-state: running;
			}
			
			.txtitem:hover {
				transform: translateY(-5px);
				box-shadow: 0 8px 16px rgba(0,0,0,0.1);
			}
			
			/* 统一图片容器尺寸 - 16:9比例 */
			.topitem {
				position: relative;
				overflow: hidden;
				width: 100% !important; /* 固定宽度 */
				padding-top: 56.25% !important; /* 16:9比例(9/16=0.5625) */
				height: 0 !important; /* 使用padding-top控制高度 */
			}
			
			/* 确保图片覆盖容器并保持位置 */
			.topitem img {
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
			
			/* 图片强制拉伸适应容器 */
			.topitem img {
				width: 100%;
				height: 100%;
				object-fit: cover; /* 保持宽高比并填充整个容器 */
				transition: all 0.5s ease;
			}

			/* PDF弹窗动画 */
			.tcbox {
				opacity: 0;
				visibility: hidden;
				transition: all 0.3s ease;
				transform: scale(0.95);
			}
			
			.tcbox.active {
				opacity: 1;
				visibility: visible;
				transform: scale(1);
			}
			.contentitem a:hover{
				color: #000!important;
			}

			/* 返回顶部按钮动画 */
			#backtop {
				transition: all 0.3s ease;
				opacity: 0;
				visibility: hidden;
				transform: translateY(20px);
			}
			
			#backtop.visible {
				opacity: 1;
				visibility: visible;
				transform: translateY(0);
			}
			
			#backtop:hover {
				transform: translateY(-5px);
			}

			/* 下拉选择框美化 */
			.bag select {
				transition: all 0.3s ease;
				border: 1px solid #ddd;
				border-radius: 4px;
				padding: 8px 12px;
				background: white;
				cursor: pointer;
			}
			
			.bag select:hover {
				border-color: #A65D57;
				box-shadow: 0 0 0 2px rgba(166, 93, 87, 0.1);
			}

			.bag select:focus {
				outline: none;
				border-color: #A65D57;
				box-shadow: 0 0 0 3px rgba(166, 93, 87, 0.2);
			}

			/* 分页按钮动画 */
			.fybox span, .fybox label {
				transition: all 0.3s ease;
				position: relative;
				overflow: hidden;
			}
			
			.fybox span::after, .fybox label::after {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(166, 93, 87, 0.2);
				transform: translateY(100%);
				transition: transform 0.3s ease;
				z-index: -1;
			}
			
			.fybox span:hover::after, .fybox label:hover::after {
				transform: translateY(0);
			}
			
			.fybox span:active, .fybox label:active {
				transform: translateY(0);
			}

			/* 面包屑导航动画 */
			.contenttitlebox a, .contenttitlebox label {
				transition: all 0.3s ease;
			}
			
			.contenttitlebox a:hover {
				color: #A65D57;
				text-decoration: underline;
			}

			/* 文件类型标签动画 */
			.itemtype {
				display: inline-block;
				padding: 4px 8px;
				border-radius: 4px;
				font-size: 12px;
				color: #fff;
				position: absolute;
				top: 10px;
				left: 10px;
				z-index: 2;
				transition: all 0.3s ease;
				font-weight: 500;
				text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2);
				box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
			}
			
			/* 为不同文件类型设置不同颜色 */
			/* PDF已移除 */

			.itemtype.ppt, .itemtype.pptx {
				background: rgba(230, 126, 34, 0.85);
			}

			.itemtype.doc, .itemtype.docx {
				background: rgba(52, 152, 219, 0.85);
			}

			.itemtype.xls {
				background: rgba(46, 204, 113, 0.85);
			}

			.itemtype.mp3, .itemtype.wav, .itemtype.ogg, .itemtype.m4a {
				background: rgba(155, 89, 182, 0.85);
			}

			.itemtype.mp4, .itemtype.avi, .itemtype.mov, .itemtype.wmv, .itemtype.mkv, .itemtype.flv {
				background: rgba(52, 73, 94, 0.85);
			}

			.itemtype.jpg, .itemtype.jpeg, .itemtype.png, .itemtype.gif, .itemtype.bmp, .itemtype.webp {
				background: rgba(41, 128, 185, 0.85);
			}
			
			.itemtype.txt, .itemtype.csv, .itemtype.xml, .itemtype.json {
				background: rgba(127, 140, 141, 0.85);
			}
			
			.itemtype.zip {
				background: rgba(192, 57, 43, 0.85);
			}
			.itemtype.rar {
				background: rgba(192, 57, 43, 0.85);
			}
			.itemtype[class*="7z"] {
				background: rgba(192, 57, 43, 0.85);
			}
			.itemtype.tar {
				background: rgba(192, 57, 43, 0.85);
			}
			.itemtype.gz {
				background: rgba(192, 57, 43, 0.85);
			}
			
			/* 筛选标签中的文件类型标记 */
			/* PDF已移除 */
			
			.filter-tag[data-id="docx"] {
				border-left: 3px solid rgba(52, 152, 219, 1);
			}
			
			.filter-tag[data-id="pptx"] {
				border-left: 3px solid rgba(230, 126, 34, 1);
			}
			
			/* xlsx格式已移除 */
			
			.filter-tag[data-id="mp4"] {
				border-left: 3px solid rgba(52, 73, 94, 1);
			}
			
			.filter-tag[data-id="mp3"] {
				border-left: 3px solid rgba(155, 89, 182, 1);
			}
			
			.filter-tag[data-id="jpg"] {
				border-left: 3px solid rgba(41, 128, 185, 1);
			}

			/* 图片容器相对定位 */
			.topitem {
				position: relative;
				overflow: hidden;
			}
			
			/* 图片加载动画 */
			.topitem img {
				transition: all 0.5s ease;
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
			
			.txtitem:hover .topitem img {
				transform: scale(1.05);
			}

			/* 优化底部文字信息 */
			.txtitem .bottomitem {
				padding: 10px 5px;
				height: 80px;
				display: flex;
				flex-direction: column;
				overflow: hidden; /* 确保不出现滚动条 */
				position: relative; /* 设置相对定位，让子元素可以绝对定位 */
			}
			
			/* 确保鼠标悬浮时书名容器可以溢出显示完整内容 */
			.txtitem:hover .bottomitem {
				overflow: visible; /* 允许内容溢出显示 */
				z-index: 10; /* 提高层级，防止被其他元素遮挡 */
			}

			.txtitem .bottomitem .title {
				font-weight: bold;
				margin-bottom: 5px;
				height: 24px; /* 增加单行高度 */
				line-height: 24px; /* 设置行高与高度一致 */
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap; /* 强制单行 */
				transition: all 0.3s ease;
				position: relative;
				padding-right: 5px;
				display: flex; /* 使用flex布局 */
				align-items: center; /* 垂直居中 */
				color: #333;
			}
			
			/* 鼠标悬浮时的滚动效果 */
			.txtitem:hover .bottomitem .title {
				animation: marquee 8s linear infinite; /* 放慢速度，更易阅读 */
				animation-play-state: running;
				text-overflow: clip; /* 移除省略号 */
				width: max-content; /* 确保宽度能完全容纳文本 */
				max-width: none; /* 移除最大宽度限制 */
				overflow: visible; /* 悬浮时允许内容溢出 */
				color: #A65D57;
			}
			
			/* 当标题文本宽度小于容器宽度时不需要滚动 */
			.txtitem .bottomitem .title.no-scroll {
				animation: none;
			}
			
			/* 滚动动画 - 从右向左滚动，确保完全显示书名 */
			@keyframes marquee {
				0%, 5% { transform: translateX(0); } /* 开始时停顿片刻 */
				95%, 100% { transform: translateX(calc(-100%)); } /* 完全滚动到末尾 */
			}
			
			/* 滚动时添加渐变遮罩 - 默认状态 */
			.txtitem .bottomitem .title:after {
				content: "";
				position: absolute;
				top: 0;
				right: 0;
				height: 100%;
				width: 20px;
				background: linear-gradient(to right, transparent, white);
				z-index: 1; /* 确保遮罩在文本上方 */
			}
			
			/* 鼠标悬浮时隐藏右侧遮罩，避免影响滚动文本 */
			.txtitem:hover .bottomitem .title:after {
				display: none; /* 完全移除遮罩，避免影响滚动 */
			}
			
			/* 添加阴影效果，提高文本在滚动时的可读性 */
			.txtitem:hover .bottomitem .title {
				text-shadow: 0px 0px 1px rgba(0,0,0,0.1); /* 轻微文本阴影 */
			}
			
			/* 优化卡片详情文字 */
			.txtitem .bottomitem .zz {
				font-size: 12px;
				color: #666;
				line-height: 1.5;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap; /* 强制单行显示 */
				margin-top: 4px;
			}

			/* 更新下拉选择框样式 */
			.bag {
				display: flex;
				gap: 15px;
				align-items: center;
				padding: 15px 0;
			}

			.bag select {
				min-width: 150px;
				height: 38px;
				padding: 0 15px;
				font-size: 14px;
				color: #333;
				background-color: #fff;
				border: 1px solid #ddd;
				border-radius: 4px;
				cursor: pointer;
				appearance: none;
				-webkit-appearance: none;
				-moz-appearance: none;
				background-image: url('data:image/svg+xml;utf8,<svg fill="%23333" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/><path d="M0 0h24v24H0z" fill="none"/></svg>');
				background-repeat: no-repeat;
				background-position: right 8px center;
				background-size: 20px;
				transition: all 0.3s ease;
			}

			.bag select:hover {
				border-color: #A65D57;
				box-shadow: 0 0 0 2px rgba(166, 93, 87, 0.1);
			}

			.bag select:focus {
				outline: none;
				border-color: #A65D57;
				box-shadow: 0 0 0 3px rgba(166, 93, 87, 0.2);
			}

			.bag select:disabled {
				background-color: #f5f5f5;
				cursor: not-allowed;
				opacity: 0.7;
			}

			/* 下拉选项样式 */
			.bag select option {
				padding: 10px;
				font-size: 14px;
				background-color: #fff;
				color: #333;
			}

			.bag select option:hover {
				background-color: #f5f5f5;
			}

			/* 占位符样式 */
			.bag select option[value="0"] {
				color: #999;
			}

			/* 添加响应式支持 */
			@media screen and (max-width: 768px) {
				.bag {
					flex-direction: column;
					gap: 10px;
				}

				.bag select {
					width: 100%;
					max-width: none;
				}
			}

			/* 优化筛选区域样式 */
			.filter-container {
				max-height: 800px;
				overflow: hidden;
				transition: max-height 0.5s ease-in-out, opacity 0.4s ease-in-out;
				opacity: 1;
				background-color: #f8f9fa;
				border-radius: 8px;
				margin-bottom: 20px;
				padding: 15px;
				box-shadow: 0 2px 5px rgba(0,0,0,0.1);
				animation: slideIn 0.5s ease-out;
			}
			
			.filter-container.collapsed {
				max-height: 0;
				opacity: 0;
				padding-top: 0;
				padding-bottom: 0;
				margin-bottom: 0;
				border: none;
			}
			
			.tjbox {
				margin-bottom: 12px;
				border-bottom: 1px solid #eee;
				padding-bottom: 12px;
			}
			
			.tjbox:last-child {
				border-bottom: none;
			}
			
			.tjbox label {
				font-weight: 500;
				margin-right: 10px;
				min-width: 60px;
				display: inline-block;
			}
			
			/* 搜索框样式 */
			.search-box input {
				transition: all 0.3s ease;
				border: 1px solid #ddd;
			}
			
			.search-box input:focus {
				border-color: #A65D57;
				box-shadow: 0 0 0 3px rgba(166, 93, 87, 0.1);
				outline: none;
			}
			
			.search-box button:hover {
				background: #8a4e49;
			}
			
			#resetBtn:hover {
				background: #eee;
				border-color: #ccc;
			}
			
			/* 文件类型筛选器样式 - 移除单独的样式定义，使用统一的filter-tag样式 */
			/* 注：这段样式被移除，以使用统一的filter-tag样式 */
			
			/* 结果计数样式 */
			.results-count {
				margin: 10px 0;
				font-size: 14px;
				color: #666;
			}
			
			.results-count strong {
				color: #A65D57;
				font-weight: 500;
			}
			
			/* 无结果提示 */
			.no-results {
				padding: 30px;
				text-align: center;
				background: #f9f9f9;
				border-radius: 8px;
				margin: 20px 0;
				color: #666;
			}

			/* 滑入动画效果 */
			@keyframes slideIn {
				from {
					transform: translateY(-20px);
					opacity: 0;
				}
				to {
					transform: translateY(0);
					opacity: 1;
				}
			}
			
			/* 筛选控件折叠功能 */
			.filter-toggle {
				display: block;
				width: 100%;
				padding: 10px 15px;
				background-color: #4a77d4;
				color: white;
				border: none;
				border-radius: 4px;
				margin-bottom: 10px;
				text-align: left;
				font-size: 16px;
				font-weight: 500;
				cursor: pointer;
				position: relative;
				transition: background-color 0.3s;
			}
			
			.filter-toggle:hover {
				background-color: #3a67c4;
			}

			.filter-toggle.active {
				background-color: #2c4e93;
			}

			/* 筛选数量标记 */
			.filter-count {
				display: none;
				position: absolute;
				right: 15px;
				top: 50%;
				transform: translateY(-50%);
				background-color: #ff6b6b;
				color: white;
				border-radius: 50%;
				width: 20px;
				height: 20px;
				text-align: center;
				line-height: 20px;
				font-size: 12px;
			}

			/* 响应式设计优化 */
			@media screen and (max-width: 768px) {
				.filter-container {
					padding: 10px;
				}
				
				.tjbox label {
					display: block;
					margin-bottom: 5px;
				}
				
				.search-reset-container {
					flex-direction: column;
					gap: 10px;
				}
				
				.search-box {
					width: 100%;
				}
				
				#searchInput {
					width: 100% !important;
				}
				
				#resetBtn {
					width: 100%;
				}
				
				.bag {
					flex-direction: column;
					gap: 10px;
				}
				
				.bag select {
					width: 100%;
				}
				
				#filetypebox {
					display: flex;
					flex-wrap: wrap;
				}
			}
			
			@media (max-width: 576px) {
				.resource-list .resource-item {
					width: 100%;
				}
				
				.filter-label {
					display: block;
					margin-bottom: 5px;
				}
				
				.type-filters, .file-type-filters {
					display: flex;
					flex-wrap: wrap;
				}
				
				.type-filters span, .file-type-filters span {
					margin-bottom: 5px;
				}
			}

			/* 加载资源提示样式 */
			.loading-resources {
				padding: 40px;
				text-align: center;
				background: #f9f9f9;
				border-radius: 8px;
				margin: 20px 0;
			}

			.loading-resources .loading-spinner {
				margin: 0 auto;
				width: 40px;
				height: 40px;
				border: 3px solid #f3f3f3;
				border-top: 3px solid #A65D57;
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}

			.loading-resources p {
				color: #666;
				margin-top: 10px;
				font-size: 14px;
			}

			/* 平滑滚动 */
			html {
				scroll-behavior: smooth;
			}

			/* 分页切换过渡效果 */
			.fybox span, .fybox label {
				position: relative;
				overflow: hidden;
			}

			.fybox span::after, .fybox label::after {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(166, 93, 87, 0.2);
				transform: translateY(100%);
				transition: transform 0.3s ease;
				z-index: -1;
			}

			.fybox span:hover::after, .fybox label:hover::after {
				transform: translateY(0);
			}
			
			/* === 新增筛选器现代样式 === */
			
			/* 筛选器标签样式 */
			.filter-tag {
				display: inline-block;
				background-color: #f8f9fa;
				border: 1px solid #dee2e6;
				border-radius: 4px;
				padding: 4px 10px;
				margin: 3px;
				cursor: pointer;
				transition: all 0.2s;
				font-size: 13px;
				color: #666;
				position: relative; /* 添加相对定位 */
			}
			
			.filter-tag:hover {
				background-color: #e9ecef;
				color: #A65D57;
				transform: translateY(-2px);
			}
			
			.filter-tag.active {
				background-color: #A65D57;
				color: white;
				border-color: #A65D57;
				box-shadow: 0 2px 0 #FFC107; /* 添加底部黄色阴影，实现线条效果 */
				border-bottom: 2px solid #FFC107; /* 添加底部边框 */
				padding-bottom: 2px; /* 调整内边距，保持整体高度不变 */
			}
			
			/* 添加标签选中时的底部线条样式 */
			.filter-tag.active::after {
				content: '';
				position: absolute;
				bottom: -3px;
				left: 0;
				width: 100%;
				height: 2px;
				background-color: #FFC107; /* 黄色线条 */
				display: block;
			}
			
			/* 确保类别选中样式也应用相同的效果 */
			.tjbox span.tjactive {
				color: #A65D57;
				font-weight: 500;
				box-shadow: 0 2px 0 #FFC107; /* 添加底部黄色阴影 */
				border-bottom: 2px solid #FFC107; /* 添加底部边框 */
			}
			
			/* 移除之前添加的伪元素样式 */
			/* .tjbox span.tjactive::after {
				content: '';
				position: absolute;
				bottom: -3px;
				left: 0;
				width: 100%;
				height: 2px;
				background-color: #FFC107;
				display: block;
			} */
			
			/* 筛选容器 */
			.modern-filter-container {
				border-radius: 5px;
				box-shadow: 0 2px 8px rgba(0,0,0,0.05);
				transition: all 0.3s;
				overflow: hidden;
				background-color: #fbfbfb;
			}
			
			.filter-section {
				border-bottom: 1px solid rgba(0,0,0,0.05);
				padding: 8px 12px;
				transition: all 0.25s;
			}
			
			.filter-section:last-child {
				border-bottom: none;
			}
			
			.filter-section:hover {
				background-color: rgba(0,0,0,0.01);
			}
			
			.filter-label {
				font-weight: 500;
				color: #495057;
				margin-bottom: 5px;
				display: flex;
				align-items: center;
				font-size: 13px;
			}
			
			.filter-label i {
				margin-right: 6px;
				color: #A65D57;
				font-size: 14px;
			}
			
			/* 筛选折叠按钮 */
			.filter-accordion-button {
				width: 100%;
				background-color: #A65D57;
				color: white;
				border: none;
				border-radius: 4px;
				padding: 8px 15px;
				text-align: left;
				font-weight: 500;
				position: relative;
				transition: all 0.3s;
				margin-bottom: 10px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				box-shadow: 0 2px 5px rgba(166, 93, 87, 0.2);
				font-size: 14px;
			}
			
			.filter-accordion-button:hover {
				background-color: #8a4e49;
			}
			
			.filter-accordion-button i {
				transition: transform 0.3s;
			}
			
			.filter-accordion-button.collapsed i {
				transform: rotate(180deg);
			}
			
			.filter-badge {
				background-color: #fff;
				color: #A65D57;
				border-radius: 20px;
				padding: 2px 6px;
				font-size: 11px;
				margin-left: 10px;
				border: 1px solid rgba(255,255,255,0.5);
			}
			
			/* 下拉选择框样式 */
			.chapter-select {
				border-radius: 4px;
				padding: 6px 12px;
				border: 1px solid #ced4da;
				width: 100%;
				transition: all 0.2s;
				background-color: white;
				font-size: 13px;
				margin-bottom: 5px;
				appearance: none;
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23A65D57' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14L2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
				background-repeat: no-repeat;
				background-position: right 8px center;
				background-size: 16px;
				padding-right: 30px;
				color: #555;
			}
			
			.chapter-select:focus {
				border-color: #A65D57;
				box-shadow: 0 0 0 2px rgba(166, 93, 87, 0.1);
				outline: none;
			}
			
			.chapter-select:disabled {
				background-color: #f5f5f5;
				cursor: not-allowed;
				opacity: 0.7;
			}

			.chapter-select option {
				padding: 8px;
				font-size: 14px;
			}

			/* 响应式调整 */
			@media (max-width: 768px) {
				.chapter-selects-group .col-md-3 {
					margin-bottom: 10px;
				}
				
				.chapter-select {
					height: 40px;
				}
			}
			
			/* 重置按钮 */
			.modern-reset-btn {
				border-radius: 4px;
				height: 36px;
				background-color: #f8f9fa;
				border: 1px solid #ced4da;
				padding: 0 15px;
				color: #495057;
				transition: all 0.2s;
				font-size: 13px;
				width: auto; /* 修改为自适应宽度 */
				white-space: nowrap; /* 防止文字换行 */
			}
			
			.modern-reset-btn:hover {
				background-color: #e9ecef;
				border-color: #A65D57;
				color: #A65D57;
			}
			
			/* 新增结果和重置按钮容器样式 */
			.results-reset-container {
				display: flex;
				align-items: center;
				justify-content: space-between;
				gap: 15px;
				margin: 10px 0;
			}
			
			/* 重置按钮在容器中的样式调整 */
			.results-reset-container .modern-reset-btn {
				margin: 0;
				box-shadow: 0 2px 4px rgba(0,0,0,0.05);
				flex-shrink: 0; /* 防止按钮被压缩 */
			}
			
			.results-reset-container .results-meta {
				margin: 0;
				flex-grow: 1; /* 占据剩余空间 */
			}
			
			@media (max-width: 768px) {
				.results-reset-container {
					flex-direction: column-reverse; /* 移动端上结果显示在上方 */
					gap: 10px;
				}
				
				.results-reset-container .modern-reset-btn {
					width: 100%; /* 移动端上按钮宽度100% */
				}
			}
			
			/* 结果统计 */
			.results-meta {
				background-color: #f8f9fa;
				border-radius: 4px;
				padding: 8px 12px;
				margin: 10px 0;
				display: flex;
				align-items: center;
				color: #495057;
				font-size: 13px;
			}
			
			.results-meta i {
				margin-right: 8px;
				color: #A65D57;
			}
			
			.results-meta strong {
				color: #A65D57;
				font-weight: 500;
			}
			
			/* 响应式调整 */
			@media (max-width: 768px) {
				.chapter-select {
					margin-bottom: 8px;
				}
				
				.filter-tags-container {
					display: flex;
					flex-wrap: wrap;
				}
			}

			/* 文件类型筛选器样式 */
			#filetypebox span {
				display: inline-block;
				background-color: #f8f9fa;
				border: 1px solid #dee2e6;
				border-radius: 4px;
				padding: 4px 10px;
				margin: 3px;
				cursor: pointer;
				transition: all 0.2s;
				font-size: 13px;
				color: #666;
			}

			#filetypebox span:hover {
				background-color: #e9ecef;
				color: #A65D57;
				transform: translateY(-2px);
			}

			#filetypebox span.tjactive, 
			#filetypebox span.active {
				background-color: #A65D57;
				color: white;
				border-color: #A65D57;
			}

			/* 筛选器标签样式 */
			.filter-tag {
				display: inline-block;
				background-color: #f8f9fa;
				border: 1px solid #dee2e6;
				border-radius: 4px;
				padding: 4px 10px;
				margin: 3px;
				cursor: pointer;
				transition: all 0.2s;
				font-size: 13px;
				color: #666;
				position: relative; /* 添加相对定位 */
			}

			.filter-tag:hover {
				background-color: #e9ecef;
				color: #A65D57;
				transform: translateY(-2px);
			}

			.filter-tag.active {
				background-color: #A65D57;
				color: white;
				border-color: #A65D57;
				box-shadow: 0 2px 0 #FFC107; /* 添加底部黄色阴影，实现线条效果 */
				border-bottom: 2px solid #FFC107; /* 添加底部边框 */
				padding-bottom: 2px; /* 调整内边距，保持整体高度不变 */
			}

			/* 添加标签选中时的底部线条样式 */
			.filter-tag.active::after {
				content: '';
				position: absolute;
				bottom: -3px;
				left: 0;
				width: 100%;
				height: 2px;
				background-color: #FFC107; /* 黄色线条 */
				display: block;
			}

			/* 筛选标签容器样式修改，确保标签在一行显示 */
			.filter-tags-container {
				display: flex;
				flex-wrap: nowrap; /* 改为nowrap，防止换行 */
				margin-top: 2px;
				overflow-x: auto; /* 添加水平滚动 */
				padding-bottom: 5px; /* 为滚动条留出空间 */
				-webkit-overflow-scrolling: touch; /* 平滑滚动 */
			}

			/* 隐藏滚动条但保留功能 */
			.filter-tags-container::-webkit-scrollbar {
				height: 4px;
			}

			.filter-tags-container::-webkit-scrollbar-thumb {
				background: rgba(166, 93, 87, 0.2);
				border-radius: 4px;
			}

			/* 课程筛选标签容器特殊样式 - 完全重写，使其与其他标签容器一致 */
			#xkbox, .course-grid {
	display: flex;
	flex-wrap: wrap; /* 改为允许换行 */
	padding-bottom: 5px;
	margin-top: 0;
	gap: 0.5rem; /* 添加间距 */
	max-height: none;
	overflow: visible;
}

			/* 隐藏滚动条但保留功能 */
			#xkbox::-webkit-scrollbar, .course-grid::-webkit-scrollbar {
				height: 4px;
			}

			#xkbox::-webkit-scrollbar-thumb, .course-grid::-webkit-scrollbar-thumb {
				background: rgba(166, 93, 87, 0.2);
				border-radius: 4px;
			}

			/* 课程项样式优化 */
			.course-item, #xkbox span {
	display: inline-block;
	flex-shrink: 0;
	background-color: #f8f9fa;
	border: 1px solid #dee2e6;
	border-radius: 4px;
	padding: 6px 12px;
	margin: 3px;
	cursor: pointer;
	transition: all 0.2s;
	font-size: 13px;
	color: #666;
	white-space: nowrap;
	overflow: hidden;
	text-overflow: ellipsis;
	max-width: 180px; /* 设置统一的最大宽度为180px */
	width: 180px; /* 强制所有项宽度相同 */
	text-align: center; /* 默认文本居中 */
	position: relative; /* 添加相对定位，用于滚动效果 */
}

			.course-item:hover, #xkbox span:hover {
	background-color: #e9ecef;
	color: #A65D57;
	transform: translateY(-2px);
	overflow: hidden; /* 保持内容不溢出 */
	z-index: 10; /* 提高层级，防止被其他元素遮挡 */
}

/* 悬停时的滚动效果 */
.course-item span, #xkbox span span {
	display: inline-block;
	white-space: nowrap;
	padding-right: 10px; /* 添加一些右侧空间 */
	transition: all 0.3s;
	text-align: center; /* 默认文本居中 */
}

.course-item[data-overflow="true"] span, #xkbox span[data-overflow="true"] span {
	text-align: left; /* 当溢出时左对齐 */
}

/* 仅当内容超出时应用滚动效果 */
.course-item:hover span.needs-scroll, #xkbox span:hover span.needs-scroll {
	animation: marqueeCourse 4s ease-in-out 1;
	animation-play-state: running;
	animation-fill-mode: forwards; /* 保持最终状态 */
}

/* 防止悬停时容器变形，文字居中 */
.course-item, #xkbox span {
	display: inline-flex !important;
	align-items: center !important;
	justify-content: center !important; /* 默认居中 */
	text-align: center !important;
}

/* 当内容溢出时左对齐 */
.course-item[data-overflow="true"], #xkbox span[data-overflow="true"] {
	justify-content: flex-start !important;
	text-align: left !important;
}

/* 添加改进后的滚动动画 - 只滚动到文本完全显示后停止 */
@keyframes marqueeCourse {
	0% { transform: translateX(0); } /* 起始位置 */
	100% { transform: translateX(calc(-100% + 160px)); } /* 滚动到文本完全显示 */
}

			.course-item.active, #xkbox span.active {
				background-color: #A65D57;
				color: white;
				border-color: #A65D57;
			}

			/* 移除底部黄色线条 - 使所有筛选选项样式一致 */
			.filter-tag.active, .tjbox span.tjactive, .course-item.active, #xkbox span.active {
				box-shadow: none;
				border-bottom: 1px solid #A65D57;
				padding-bottom: 6px;
			}

			.filter-tag.active::after, .tjbox span.tjactive::after, .course-item.active::after, #xkbox span.active::after {
				display: none;
			}

			/* 更多按钮样式 */
			.course-toggle-btn {
				display: inline-block;
				background-color: #f8f9fa;
				border: 1px solid #dee2e6;
				border-radius: 4px;
				padding: 4px 10px;
				margin: 3px;
				cursor: pointer;
				transition: all 0.2s;
				font-size: 13px;
				color: #666;
				line-height: 1.5;
			}

			.course-toggle-btn:hover {
				background-color: #e9ecef;
				color: #A65D57;
			}

			.course-toggle-btn.expanded {
				background-color: #e9ecef;
			}

			.course-toggle-btn i {
				margin-left: 3px;
				transition: transform 0.3s ease;
			}

			.course-toggle-btn.expanded i {
				transform: rotate(180deg);
			}

			/* 排序按钮样式 */
			.sort-toggle {
				background-color: #f8f9fa;
				border: 1px solid #dee2e6;
				border-radius: 4px;
				padding: 4px 12px;
				color: #666;
				font-size: 13px;
				cursor: pointer;
				transition: all 0.2s;
				margin-left: 10px;
				display: inline-flex;
				align-items: center;
				white-space: nowrap;
			}
			
			.sort-toggle:hover {
				background-color: #e9ecef;
				border-color: #A65D57;
				color: #A65D57;
			}
			
			.sort-toggle i {
				margin-left: 5px;
				transition: transform 0.3s;
			}
			
			/* 修正图标旋转方向 */
			.sort-toggle:not(.desc) i {
				transform: rotate(180deg);
			}
			
			.sort-info {
				color: #888;
				font-size: 12px;
				margin-left: 10px;
				font-style: italic;
			}

			/* 移动端优化样式 */
			@media (max-width: 768px) {
				/* 顶部导航 */
				.topview_1 {
					padding: 10px;
					flex-direction: column;
					align-items: center;
				}
				
				.logo {
					margin-bottom: 10px;
				}
				
				.loginview {
					width: 100%;
					justify-content: space-between;
				}
				
				/* 搜索栏样式移至统一定义 */
				
				/* 导航菜单 */
				.menu-toggle {
					display: block;
					position: absolute;
					top: 15px;
					right: 15px;
					background: none;
					border: none;
					font-size: 24px;
					color: #A65D57;
					cursor: pointer;
					z-index: 1000;
				}
				
				.topview_2 .itembox {
					flex-direction: column;
					max-height: 0;
					overflow: hidden;
					transition: max-height 0.3s ease;
				}
				
				.topview_2 .itembox.expanded {
					max-height: 300px;
				}
				
				.topview_2 .menuitem {
					width: 100%;
					text-align: left;
					padding: 10px 15px;
					border-bottom: 1px solid rgba(0,0,0,0.05);
				}
				
				/* 内容区域 */
				.content {
					padding: 10px;
				}
				
				.contenttitlebox {
					overflow-x: auto;
					white-space: nowrap;
					padding: 10px 0;
				}
				
				/* 资源卡片 */
				.txtitem {
					width: 100% !important;
					margin: 0 0 15px 0 !important;
				}
				
				.topitem {
					height: 150px !important;
				}
				
				/* 筛选区域 */
				.filter-accordion-button {
					padding: 10px;
					font-size: 14px;
				}
				
				.filter-section {
					padding: 10px;
				}
				
				.filter-tags-container {
					width: 100%;
					overflow-x: auto;
					padding-bottom: 5px;
				}
				
				/* 筛选标签 */
				.filter-tag {
					margin: 2px;
					padding: 3px 8px;
					font-size: 12px;
				}
				
				/* 章节选择区域 */
				.chapter-selects-group .row {
					margin: 0;
				}
				
				.col-md-3 {
					width: 100%;
					padding: 0;
					margin-bottom: 10px;
				}
				
				.chapter-select {
					width: 100%;
					margin-bottom: 10px;
				}
				
				/* 结果统计和排序区域 */
				.results-reset-container {
					flex-direction: column;
				}
				
				.results-reset-container > div {
					width: 100%;
					margin-bottom: 10px;
					flex-direction: column;
				}
				
				.sort-toggle, .modern-reset-btn {
					width: 100%;
					margin-top: 5px;
					margin-bottom: 5px;
				}
				
				/* 分页 */
				.fybox {
					flex-wrap: wrap;
					justify-content: center;
				}
				
				.fybox span, .fybox label {
					margin: 3px;
					padding: 8px 12px;
				}
				
				/* 底部 */
				footer .yqlj .box {
					flex-direction: column;
				}
				
				footer .yqlj .box a {
					margin: 5px 0;
				}
				
				/* 返回顶部按钮 */
				#backtop {
					position: fixed;
					bottom: 20px;
					right: 20px;
					z-index: 999;
					width: 40px;
					height: 40px;
					background: rgba(166, 93, 87, 0.8);
					border-radius: 50%;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					box-shadow: 0 2px 10px rgba(0,0,0,0.2);
				}
				
				#backtop div {
					font-size: 10px;
					color: white;
				}
				
				#backtop img {
					width: 15px;
					height: 15px;
				}
			}

			/* 超小屏幕优化 */
			@media (max-width: 480px) {
				/* 确保弹出层不会溢出屏幕 */
				.tcbox {
					width: 95% !important;
					max-width: 95% !important;
				}
				
				.pdfbox {
					width: 100% !important;
					height: 80vh !important;
				}
				
				/* 优化资源列表布局 */
				.sjbox {
					display: flex;
					flex-direction: column;
					padding: 0;
				}
				
				/* 改进文件类型标签显示 */
				.itemtype {
					font-size: 10px;
					padding: 2px 6px;
				}
				
				/* 优化筛选器按钮样式 */
				.filter-tag, .tjbox span {
					padding: 5px 8px;
					margin: 2px;
					font-size: 12px;
				}
				
				/* 优化底部间距 */
				footer {
					padding: 10px;
				}
				
				footer .title {
					width: 100% !important;
					text-align: center;
					margin-bottom: 10px;
				}
				
				footer .box {
					width: 100% !important;
					justify-content: center;
				}
			}

			/* 适配特殊手机尺寸 */
			@media (max-width: 375px) {
				/* 进一步优化UI元素尺寸 */
				.filter-tag, .tjbox span {
					padding: 4px 6px;
					font-size: 11px;
				}
				
				.chapter-select {
					height: 36px;
					font-size: 12px;
				}
				
				.results-meta {
					font-size: 12px;
				}
				
				/* 调整标题显示 */
				.contenttitlebox a, .contenttitlebox label {
					font-size: 12px;
				}
				
				/* 优化卡片信息显示 */
				.title {
					font-size: 14px;
				}
				
				.zz {
					font-size: 12px;
					-webkit-line-clamp: 2;
				}
			}

			/* 添加筛选数量标记样式 */
			.filter-count {
				display: inline-block;
				background-color: #ff6b6b;
				color: white;
				border-radius: 50%;
				width: 20px;
				height: 20px;
				text-align: center;
				line-height: 20px;
				font-size: 12px;
				margin-left: 8px;
				vertical-align: middle;
			}

			.filter-toggle {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				padding: 10px 15px;
				background-color: #d82121;
				color: white;
				border: none;
				border-radius: 4px;
				margin-bottom: 10px;
				text-align: left;
				font-size: 14px;
				font-weight: 500;
				cursor: pointer;
				transition: background-color 0.3s;
			}

			.filter-toggle:hover {
				background-color: #c01c1c;
			}

			.filter-toggle span {
				display: flex;
				align-items: center;
			}

			.filter-toggle i.fas.fa-filter {
				margin-right: 8px;
			}

			.filter-toggle i.fas.fa-chevron-up {
				transition: transform 0.3s;
			}

			.filter-toggle.collapsed i.fas.fa-chevron-up {
				transform: rotate(180deg);
			}

			/* 筛选区域整体样式 */
			.filter-section-wrapper {
				margin: 15px 0;
				background: #fff;
			}

			/* 筛选切换按钮样式 */
			.filter-toggle.light {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				padding: 12px 15px;
				background-color: transparent;
				color: #666;
				border: 1px solid #eee;
				border-radius: 4px;
				margin-bottom: 10px;
				font-size: 14px;
				cursor: pointer;
				transition: all 0.3s ease;
			}

			.filter-toggle.light:hover {
				background-color: #f9f9f9;
				border-color: #ddd;
			}

			.filter-toggle.light i {
				color: #999;
				transition: transform 0.3s;
			}

			.filter-toggle.light.collapsed i.fa-chevron-down {
				transform: rotate(-180deg);
			}

			/* 筛选容器样式 */
			.filter-container {
				background: #fff;
				border: 1px solid #eee;
				border-radius: 4px;
				padding: 15px;
				margin-bottom: 15px;
				display: none;
			}

			/* 筛选组样式 */
			.filter-group {
				margin-bottom: 1.25rem;
				padding-bottom: 1.25rem;
				border-bottom: 1px solid #edf2f7;
			}

			.filter-group:last-child {
				margin-bottom: 0;
				padding-bottom: 0;
				border-bottom: none;
			}
			
			/* 添加内部元素样式改进 */
			.filter-label-drawer {
				font-size: 0.875rem;
				font-weight: 500;
				color: #4b5563;
				margin-bottom: 0.75rem;
				display: flex;
				align-items: center;
			}
			
			.filter-options {
				display: flex;
				flex-wrap: wrap;
				gap: 0.5rem;
			}

			.filter-label {
				color: #666;
				font-size: 13px;
				margin-bottom: 10px;
			}

			/* 选项样式 */
			.filter-options {
				display: flex;
				flex-wrap: wrap;
				gap: 8px;
			}

			/* 选项样式修改 */
			.filter-option {
				display: inline-flex;
				align-items: center;
				justify-content: center;
				padding: 0.5rem 0.75rem;
				font-size: 0.875rem;
				color: #d82121; /* 红色文字 */
				background: transparent; /* 去掉背景色 */
				border: 1px solid #e5e7eb;
				border-radius: 0.375rem;
				cursor: pointer;
				transition: all 0.2s ease;
				white-space: nowrap;
			}

			.filter-option:hover {
				color: #d82121;
				border-color: #d82121;
				background: rgba(216, 33, 33, 0.05); /* 轻微浅红色背景 */
				transform: translateY(-1px);
				box-shadow: 0 1px 3px rgba(0,0,0,0.05);
			}

			.filter-option.active, .filter-option.tjactive {
				color: #fff !important; /* 确保文字为白色 */
				background-color: #d82121 !important; /* 红色背景 */
				border-color: #d82121 !important;
				font-weight: 500; /* 稍微加粗 */
				box-shadow: 0 2px 4px rgba(216, 33, 33, 0.2);
			}

			/* 筛选计数样式 */
			.filter-count {
				display: inline-flex;
				align-items: center;
				justify-content: center;
				min-width: 18px;
				height: 18px;
				padding: 0 6px;
				background: rgba(216, 33, 33, 0.1);
				color: #d82121;
				border-radius: 9px;
				font-size: 12px;
				margin-left: 6px;
			}

			/* 响应式调整 */
			@media (max-width: 768px) {
				.filter-option {
					padding: 4px 10px;
					font-size: 12px;
				}
				
				.filter-group {
					margin-bottom: 12px;
					padding-bottom: 12px;
				}
				
				.filter-label {
					font-size: 12px;
					margin-bottom: 8px;
				}
			}

			/* 页头搜索按钮样式 */
			.header-search-btn {
				background-color: #d82121;
				color: white;
				border: none;
				padding: 0 12px;
				height: 34px; /* 与输入框对齐 */
				border-radius: 0 4px 4px 0;
				cursor: pointer;
				margin-left: -1px; /* 紧贴输入框 */
				transition: background-color 0.2s;
			}

			.header-search-btn:hover {
				background-color: #c01c1c;
			}

			.ssview {
				display: flex; /* 让输入框和按钮在同一行 */
				align-items: center;
			}

			.ssview input[type="text"] {
				height: 34px;
				border-radius: 4px 0 0 4px;
				border-right: none;
			}

			/* 课程选择区域现代化样式 */
			.course-section {
				margin: 20px 0;
				padding: 15px;
				background-color: #fff; /* 更改背景为白色 */
				border-radius: 6px;
				/* box-shadow: 0 2px 8px rgba(0,0,0,0.03); /* 添加细微阴影 */
			}

			.course-section .filter-label { /* 复用之前的filter-label样式，或定义新的 */
				color: #333;
				font-weight: 500;
				font-size: 16px; /* 稍大字体 */
				margin-bottom: 15px;
			}

			.course-grid {
				display: grid;
				grid-template-columns: repeat(auto-fill, minmax(160px, 1fr)); /* 调整最小宽度 */
				gap: 12px; /* 调整间距 */
			}

			.course-item {
				padding: 10px 15px; /* 调整内边距 */
				background-color: #f8f9fa; /* 默认浅灰背景 */
				border: 1px solid #e9ecef; /* 浅边框 */
				border-radius: 4px;
				cursor: pointer;
				text-align: center;
				transition: all 0.25s ease;
				font-size: 13.5px; /* 调整字体大小 */
				color: #495057; /* 深灰色文字 */
				line-height: 1.4;
				white-space: nowrap; /* 单行显示 */
				overflow: hidden; /* 超出部分隐藏 */
				text-overflow: ellipsis; /* 显示省略号 */
				display: block; /* or inline-block, to respect width/truncation */
				max-width: 100%; /* Ensure it respects grid cell width */
			}

			.course-item:hover {
				border-color: #d82121;
				color: #d82121;
				background-color: #fff;
				transform: translateY(-2px);
				box-shadow: 0 4px 10px rgba(216, 33, 33, 0.1);
			}

			.course-item.active {
				background-color: #d82121;
				color: white;
				border-color: #d82121;
				font-weight: 500; /* 加粗选中项 */
				box-shadow: 0 2px 6px rgba(216, 33, 33, 0.2);
			}
			/* 移除黄色下划线 - 确保没有::after元素产生黄线 */
			.course-item.active::after {
				display: none;
			}

			/* 高级筛选抽屉样式 */
			.advanced-filter-drawer {
				margin: 20px 0;
				position: relative; /* 确保定位上下文 */
				z-index: 10; /* 提高层级 */
			}
			
			/* 确保高级筛选按钮可点击 */
			.filter-toggle.advanced {
				position: relative;
				z-index: 15; /* 确保按钮在上层 */
				cursor: pointer !important;
				pointer-events: auto !important;
			}

			.filter-toggle.advanced {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				padding: 12px 15px;
				background-color: transparent; /* 移除背景色 */
				color: #d82121; /* 红色文字 */
				border: 1px solid #e8e8e8;
				border-radius: 4px;
				margin-bottom: 0;
				font-size: 14px;
				font-weight: 500;
				cursor: pointer;
				position: relative;
				z-index: 2;
				transition: all 0.3s ease;
			}

			.filter-toggle.advanced:hover {
				/* 完全移除背景色变化，只保留边框变化 */
				background-color: #f5f5f5; /* 恢复为初始背景颜色 */
				border-color: #d0d0d0;
			}

			.filter-toggle.advanced:not(.collapsed) {
				background-color: #d82121; /* 展开时使用红色背景 */
				color: white; /* 展开时使用白色文字 */
				border-color: #d82121;
			}

			.filter-toggle.advanced .filter-count {
				background-color: rgba(216, 33, 33, 0.15); /* 计数器背景色调整 */
				color: #d82121; /* 计数器文字颜色 */
				padding: 2px 8px;
				font-size: 11.5px;
			}

			.filter-toggle.advanced i.fa-chevron-down {
				transition: transform 0.3s ease;
			}

			.filter-toggle.advanced.collapsed i.fa-chevron-down {
				transform: rotate(-180deg); /* 保持箭头向上 */
			}

			.filter-toggle.advanced:not(.collapsed) i.fa-chevron-down {
				transform: rotate(0deg); /* 明确未展开时的箭头方向 */
			}

			.advanced-filter-drawer .filter-container {
				background: #fff;
				border: 1px solid #e9ecef;
				border-top: none; /* 避免与按钮重复边框 */
				border-radius: 0 0 6px 6px;
				padding: 20px;
				margin-top: -1px; /* 与按钮紧贴 */
				/* display: none; 由JS控制 */
				box-shadow: 0 4px 12px rgba(0,0,0,0.05);
			}

			.filter-label-drawer { /* 抽屉内标签样式 */
				color: #333;
				font-size: 14px;
				font-weight: 500;
				margin-bottom: 10px;
			}

			.filter-options .filter-option {
				/* 沿用之前的 .filter-option 样式，可按需微调 */
				background-color: #f1f3f5;
				border-color: #dee2e6;
				color: #495057;
			}

			.filter-options .filter-option:hover {
				background-color: #e9ecef;
				border-color: #adb5bd;
				color: #d82121;
			}

			.filter-options .filter-option.active {
				background-color: #d82121;
				color: white;
				border-color: #d82121;
			}

			@media (max-width: 768px) {
				.course-grid {
					grid-template-columns: repeat(auto-fill, minmax(130px, 1fr));
					gap: 10px;
				}
				.course-item {
					padding: 8px 10px;
					font-size: 12.5px;
				}
				.filter-toggle.advanced {
					padding: 10px 15px;
					font-size: 13.5px;
				}
				.advanced-filter-drawer .filter-container {
					padding: 15px;
				}
			}

			/* 章节筛选区域样式 */
			.chapter-filter-section {
				margin: 15px 0;
				padding: 15px;
				background-color: #f8f9fa;
				border-radius: 0.5rem;
				border: 1px solid #e9ecef;
				box-shadow: 0 2px 5px rgba(0,0,0,0.05);
			}

			/* 现代化章节网格，使用Tailwind风格 */
			.chapter-grid {
				display: flex;
				flex-wrap: wrap; /* 改为自动换行 */
				gap: 0.5rem; /* 使用gap代替margin，更现代 */
				margin-top: 0.75rem;
				padding: 0.25rem;
				max-height: none; /* 移除固定高度，防止滚动条 */
				overflow: visible; /* 防止滚动条出现 */
			}

			/* 完全去除滚动条相关样式 */
			.chapter-grid::-webkit-scrollbar {
				display: none;
			}

			/* 现代化章节项目样式 */
.chapter-item {
	display: inline-flex;
	align-items: center;
	justify-content: center; /* 默认居中 */
	background-color: #ffffff;
	border: 1px solid #e5e7eb;
	border-radius: 0.375rem;
	padding: 0.5rem 0.75rem;
	cursor: pointer;
	transition: all 0.2s ease;
	font-size: 0.875rem;
	color: #4b5563;
	white-space: nowrap;
	max-width: 180px; /* 统一最大宽度 */
	width: 180px; /* 强制统一宽度 */
	overflow: hidden;
	text-overflow: ellipsis;
	box-shadow: 0 1px 2px rgba(0,0,0,0.05);
	position: relative; /* 添加相对定位，用于滚动效果 */
	text-align: center; /* 默认文本居中 */
}

			.chapter-item:hover {
	background-color: #f3f4f6;
	color: #A65D57;
	transform: translateY(-2px);
	box-shadow: 0 3px 6px rgba(0,0,0,0.1);
	border-color: #d1d5db;
	overflow: hidden; /* 保持内容不溢出 */
	z-index: 10; /* 提高层级，防止被其他元素遮挡 */
}

/* 悬停时的滚动效果 */
.chapter-item span {
	display: inline-block;
	white-space: nowrap;
	padding-right: 10px; /* 添加一些右侧空间 */
	transition: all 0.3s;
	text-align: center; /* 默认文本居中 */
}

.chapter-item[data-overflow="true"] span {
	text-align: left; /* 当溢出时左对齐 */
}

/* 仅当内容超出时应用滚动效果 */
.chapter-item:hover span.needs-scroll {
	animation: marqueeChapter 4s ease-in-out 1;
	animation-play-state: running;
	animation-fill-mode: forwards; /* 动画完成后保持最终状态 */
}

/* 防止悬停时容器变形，文字居中 */
.chapter-item {
	display: inline-flex !important;
	align-items: center !important;
	justify-content: center !important; /* 默认居中 */
	text-align: center !important;
}

/* 当内容溢出时左对齐 */
.chapter-item[data-overflow="true"] {
	justify-content: flex-start !important;
	text-align: left !important;
}

/* 添加改进的章节滚动动画 */
@keyframes marqueeChapter {
	0% { transform: translateX(0); } /* 起始位置 */
	100% { transform: translateX(calc(-100% + 160px)); } /* 滚动到文本完全显示 */
}

			.chapter-item.active {
				background-color: #A65D57;
				color: white;
				border-color: #A65D57;
				font-weight: 500;
				box-shadow: 0 2px 4px rgba(166, 93, 87, 0.3);
			}

			.filter-option.active::after { /* 确保高级筛选内的选项也没有黄线 */
				display: none !important; 
			}

			/* 完善筛选抽屉过渡效果 */
			.filter-container {
				overflow: hidden;
				transition: height 0.3s ease;
				will-change: height;
			}

			.filter-toggle.advanced {
				position: relative;
				z-index: 2;
				transition: all 0.3s ease;
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				padding: 0.75rem 1rem;
				background-color: #f9fafb;
				color: #4b5563;
				border: 1px solid #e5e7eb;
				border-radius: 0.5rem;
				font-size: 0.9375rem;
				font-weight: 500;
				cursor: pointer;
				box-shadow: 0 1px 2px rgba(0,0,0,0.05);
			}

			.filter-toggle.advanced:hover {
				background-color: #f3f4f6;
				border-color: #d1d5db;
			}

			.filter-toggle.advanced i.fa-chevron-down {
				transition: transform 0.3s ease;
				color: #6b7280;
				font-size: 0.8125rem;
			}

			.filter-toggle.advanced:not(.collapsed) {
				border-radius: 0.5rem 0.5rem 0 0;
				border-bottom-color: transparent;
				background-color: #f3f4f6;
			}

			.filter-toggle.advanced:not(.collapsed) i.fa-chevron-down {
				transform: rotate(180deg);
				color: #A65D57;
			}

			/* 确保筛选区域正常展开 */
			.advanced-filter-drawer {
				position: relative;
				z-index: 1;
			}

			/* 顶部搜索框样式优化 */
			/* 搜索框整体样式 - 与style.css完全一致 */
.topview_1 .ssview {
	background: #FFFFFF;
	display: flex;
	align-items: center;
	padding-left: 0.520833rem;
	padding-right: 0.520833rem;
	border: 1px solid #cc4347;
	border-radius: 5px;
	overflow: hidden;
	margin-right: 1.302083rem;
}

/* 搜索图标样式 - 与style.css完全一致 */
.topview_1 .ssview img {
	padding-right: 0.520833rem;
	width: 0.9375rem;
	height: auto;
	display: block;
}

/* 搜索输入框样式 - 与style.css一致 */
.topview_1 input {
	flex: 1;
	border: none;
	padding: 0;
	font-size: 0.833333rem;  /* 字体大小与style.css保持一致 */
	color: #333;
	background: transparent;
	outline: none;
}

			@media (max-width: 768px) {
				.ssview {
					width: 60%; /* 保持与顶部媒体查询中的一致 */
				}
				
				.loginview {
					flex-wrap: wrap;
					justify-content: center;
				}
			}

			/* 修复高级筛选抽屉样式 */
			.filter-section-wrapper.advanced-filter-drawer {
				position: relative;
				z-index: 1;
				margin: 15px 0;
			}

			.filter-container {
				display: none; /* 默认隐藏 */
				background-color: #fff;
				border: 1px solid #e5e7eb;
				border-radius: 0 0 0.5rem 0.5rem;
				padding: 1rem;
				margin-top: -1px;
				box-shadow: 0 4px 12px rgba(0,0,0,0.08);
				animation: none !important;
				transition: none !important;
				will-change: auto;
				overflow: visible;
				z-index: 20; /* 提高层级避免被遮挡 */
				position: relative; /* 确保定位上下文正确 */
			}

			.filter-toggle.advanced {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				padding: 12px 15px;
				background-color: transparent; /* 移除背景色 */
				color: #d82121; /* 红色文字 */
				border: 1px solid #e8e8e8;
				border-radius: 4px;
				margin-bottom: 0;
				font-size: 14px;
				font-weight: 500;
				cursor: pointer;
				position: relative;
				z-index: 2;
				transition: all 0.3s ease;
			}

			.filter-toggle.advanced.collapsed {
				border-radius: 4px;
			}

			.filter-toggle.advanced i.fa-chevron-down {
				transition: transform 0.3s ease;
			}

			.filter-toggle.advanced.collapsed i.fa-chevron-down {
				transform: rotate(180deg);
			}

			/* 确保点击时没有默认边框 */
			.filter-toggle:focus {
				outline: none !important;
				box-shadow: none !important;
			}

			/* 筛选项选中状态样式修复 - 确保文字为白色 */
			.filter-option.active, 
			.filter-tag.active,
			.course-item.active,
			.chapter-item.active,
			.tjactive {
				color: #fff !important; /* 确保文字为白色 */
				background-color: #d82121 !important; /* 红色背景 */
				border-color: #d82121 !important;
				font-weight: 500; /* 稍微加粗 */
			}

			/* 选项悬停效果 */
			.filter-option:hover, 
			.filter-tag:hover,
			.course-item:hover,
			.chapter-item:hover {
				border-color: #d82121;
				background-color: rgba(216, 33, 33, 0.1);
				color: #d82121;
			}

			/* 移除选项选中状态的黄色下划线，使用统一的红色背景白色文字 */
			#qblx .tjactive::after, 
			#qbsx .tjactive::after,
			#qblb .tjactive::after,
			#filetypebox .tjactive::after {
				display: none !important; 
			}

			/* 统一所有筛选标签的默认样式 */
			.filter-option, 
			.filter-tag, 
			.course-item, 
			.chapter-item {
				display: inline-block;
				background-color: #f8f9fa;
				border: 1px solid #dee2e6;
				border-radius: 4px;
				padding: 6px 12px;
				margin: 3px;
				cursor: pointer;
				transition: all 0.2s;
				font-size: 13px;
				color: #666;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}

			/* 明确定义非激活状态样式 */
			.course-item:not(.active) {
				background-color: white !important;
				color: #666 !important;
				border: 1px solid #e8e8e8 !important;
			}

			/* 课程项和章节项的样式统一 */
			.course-item, .chapter-item {
				padding: 6px 12px;
				background-color: transparent; /* 去掉背景色 */
				border: 1px solid #e8e8e8;
				border-radius: 4px;
				color: #d82121; /* 红色文字 */
				cursor: pointer;
				font-size: 13px;
				transition: all 0.2s;
				margin: 3px;
			}

			.course-item:hover, .chapter-item:hover {
				background-color: rgba(216, 33, 33, 0.05); /* 轻微浅红色背景 */
				color: #d82121;
				border-color: #d82121;
			}

			.course-item.active, .chapter-item.active, 
			.course-item.tjactive, .chapter-item.tjactive {
				background-color: #d82121 !important;
				color: white !important;
				border-color: #d82121 !important;
				font-weight: 500; /* 稍微加粗 */
			}

			/* 明确定义非激活状态样式 */
			.course-item:not(.active):not(.tjactive), .chapter-item:not(.active):not(.tjactive) {
				background-color: transparent !important;
				color: #d82121 !important;
				border: 1px solid #e8e8e8 !important;
			}

			/* 恢复课程项和章节项的原始样式 */
			.course-item, .chapter-item {
				padding: 8px 12px;
				background-color: white;
				border: 1px solid #e8e8e8;
				border-radius: 4px;
				cursor: pointer;
				text-align: center;
				transition: all 0.2s;
				font-size: 13px;
				color: #666;
				margin: 3px;
			}

			.course-item:hover, .chapter-item:hover {
				border-color: #d82121;
				color: #d82121;
				transform: translateY(-2px);
			}

			.course-item.active, .chapter-item.active, 
			.course-item.tjactive, .chapter-item.tjactive {
				background-color: #d82121 !important;
				color: white !important;
				border-color: #d82121 !important;
			}

			/* 明确定义非激活状态样式 */
			.course-item:not(.active):not(.tjactive), .chapter-item:not(.active):not(.tjactive) {
				background-color: white !important;
				color: #666 !important;
				border: 1px solid #e8e8e8 !important;
			}

			/* 课程选择标题样式 */
			.course-section .filter-label {
				color: #d82121;
				font-size: 16px;
				font-weight: 600;
				margin-bottom: 12px;
				position: relative;
				padding-left: 15px;
				display: inline-block;
			}

			.course-section .filter-label::before {
				content: "";
				position: absolute;
				left: 0;
				top: 0;
				height: 100%;
				width: 4px;
				background-color: #d82121;
				border-radius: 2px;
			}

			/* 高级筛选抽屉按钮样式 */
			.filter-toggle.advanced {
				display: flex;
				align-items: center;
				justify-content: space-between;
				width: 100%;
				padding: 10px 15px;
				background-color: #f5f5f5;
				color: #555;
				border: 1px solid #e0e0e0;
				border-radius: 4px;
				margin-bottom: 10px;
				font-size: 14px;
				font-weight: 500;
				cursor: pointer;
				position: relative;
				z-index: 2;
				transition: all 0.3s ease;
				box-shadow: 0 1px 3px rgba(0,0,0,0.05);
			}

			.filter-toggle.advanced:hover {
				background-color: #efefef;
				border-color: #d0d0d0;
			}

			.filter-toggle.advanced i.fa-filter {
				color: #d82121;
				margin-right: 6px;
			}

			/* 抽屉展开状态样式 */
			.filter-toggle.advanced:not(.collapsed) {
				background-color: #f0f0f0;
				border-color: #d0d0d0;
				border-radius: 4px 4px 0 0;
				border-bottom: none;
				margin-bottom: 0;
			}

			/* 确保抽屉收缩状态恢复为初始样式 */
			.filter-toggle.advanced.collapsed {
				background-color: #f5f5f5;
				color: #555;
				border: 1px solid #e0e0e0;
				border-radius: 4px;
				margin-bottom: 10px;
			}

			/* 筛选容器样式修改 */
			.filter-container {
				display: none;
				background-color: #fff;
				border: 1px solid #d0d0d0;
				border-radius: 0 0 4px 4px;
				padding: 20px;
				margin-top: 0;
				margin-bottom: 15px;
				position: relative;
				box-shadow: 0 2px 5px rgba(0,0,0,0.05);
			}

			/* 重置按钮位置调整 */
			.filter-reset-button {
				position: absolute;
				top: 15px;
				right: 15px;
				padding: 6px 12px;
				background-color: white;
				border: 1px solid #ddd;
				border-radius: 4px;
				color: #666;
				font-size: 13px;
				cursor: pointer;
				transition: all 0.2s;
				display: flex;
				align-items: center;
			}

			.filter-reset-button:hover {
				border-color: #d82121;
				color: #d82121;
			}

			.filter-reset-button i {
				margin-right: 5px;
				font-size: 12px;
			}

			/* 确保抽屉收缩状态恢复为初始样式 - 使用更高优先级 */
			.filter-section-wrapper .filter-toggle.advanced.collapsed {
				background-color: #f5f5f5 !important;
				color: #555 !important;
				border: 1px solid #e0e0e0 !important;
				border-radius: 4px !important;
				margin-bottom: 10px !important;
			}

			/* 展开状态样式 - 也使用更高优先级确保正确应用 */
			.filter-section-wrapper .filter-toggle.advanced:not(.collapsed) {
				background-color: #f0f0f0 !important;
				border-color: #d0d0d0 !important;
				border-radius: 4px 4px 0 0 !important;
				border-bottom: none !important;
				margin-bottom: 0 !important;
			}

			@media (max-width: 768px) {
				.chapter-select {
					margin-bottom: 8px;
				}
			}

			/* 搜索框样式已在CSS顶部统一定义，不再重复定义 */

			/* 课程选择区域现代化样式 */
		</style>
	</head>
	<body class="index">
		<!-- 添加加载动画 -->
		<div class="loading-overlay">
			<div class="loading-spinner"></div>
		</div>
		<!-- PDF查看器已移除 -->
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
					<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>
					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<!-- <div class="contenttitlebox">
			<div>
				<a id="zxxx" href="onlinelearning.html">在线学习</a>
				<img src="img/jtright.png" />
				<label class="aa" id="hsname">教学资源</label>
			</div>
		</div> -->
		<div class="content">
			<!-- <div class="contenttopview">
				<div class="contentitem" id="classbox">
					分类导航 -->
				<!-- </div> -->
				<!-- 原搜索框位置1，将被移除 -->
			<!-- </div>  -->
			
			<!-- 课程选择区域 -->
			<div class="course-section">
				<div class="filter-label">课程选择</div>
				<div class="course-grid" id="courseGrid">
					<div class="course-item active tjactive" data-id="0" onclick="selectxk(this)" title="全部课程">全部课程</div>
					<!-- 其他课程选项将通过JavaScript动态生成 -->
					</div>
				</div>
				
			<!-- 章节筛选区域 (新增，默认隐藏) -->
			<div class="chapter-filter-section" id="chapterFilterSection" style="display: none;">
				<div class="filter-label">章节选择</div>
				<div class="chapter-grid" id="chapterGrid">
					<!-- 章节选项将通过JavaScript动态生成 -->
					</div>
				</div>
				
			<!-- 高级筛选抽屉 -->
			<div class="filter-section-wrapper advanced-filter-drawer">
				<button class="filter-toggle advanced collapsed">
					<span><i class="fas fa-filter"></i> 高级筛选 <span class="filter-count" id="filterCount" style="display: none;">0</span></span>
					<i class="fas fa-chevron-down"></i>
						</button>
				<div class="filter-container" style="display: none;"> <!-- 默认隐藏 -->

					
					<!-- 属性筛选 -->
					<div class="filter-group">
						<div class="filter-label-drawer">属性</div>
						<div class="filter-options" id="qbsx">
							<span class="filter-option active" data-id="0" onclick="selectsx(this)">全部</span>
							<!-- 属性选项将通过API动态加载 -->
							</div>
							</div>
					<!-- 类型筛选 -->
					<div class="filter-group">
						<div class="filter-label-drawer">类型</div>
						<div class="filter-options" id="qblx">
							<span class="filter-option active" data-id="0" onclick="selectlx(this)">全部</span>
							<span class="filter-option" data-id="1" onclick="selectlx(this)">微课库</span>
							<span class="filter-option" data-id="2" onclick="selectlx(this)">精品课</span>
							<span class="filter-option" data-id="3" onclick="selectlx(this)">试题库</span>
							<span class="filter-option" data-id="4" onclick="selectlx(this)">课件库</span>
							</div>
							</div>
				<!-- 文件格式筛选 -->
					<div class="filter-group">
						<div class="filter-label-drawer">文件格式</div>
						<div class="filter-options" id="filetypebox">
							<span class="filter-option active tjactive" data-id="all" onclick="selectFileType(this)">全部</span>
							<!-- 文件类型选项将通过JavaScript动态生成 -->
					</div>
				</div>
			</div>
				</div>
				
			<!-- 结果统计/重置筛选区域 -->
			<div class="results-actions">
				<div class="results-count">
					<i class="fas fa-list"></i>
					共找到 <strong id="resourceCount">0</strong> 个资源
				</div>
				<div class="filter-actions">
					<button class="reset-all-button" onclick="resetAllFilters()">
						<i class="fas fa-redo"></i> 重置筛选
					</button>
				</div>
			</div>
			
			<div class="sjbox" id="list">
				<!-- 资源列表 -->
			</div>
			<div class="fybox" id="fyq">
				<span id="sy">首页</span>
				<span id="syy">上一页</span>
				<div class="num" id="num">
				</div>
				<span id="xyy">下一页</span>
				<span id="wy">尾页</span>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/clipboard.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/getpages.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			// 初始化页码，检查URL中是否存在页码参数
let pageindex = getUrlParam('currentPage') ? parseInt(getUrlParam('currentPage')) : 1
			let pagesize = 25
			let pages = 0
			let sxid = null
			let lxid = null
			let xkid = null
			let zid = null
			let jid = null
			let xjid = null
			let fileTypeId = null // 文件类型ID，默认为null表示"全部"
			let xkdata = null //学科章节树
			let zdata = null //章列表
			let jdata = null //节列表
			let xjdata = null //小节列表
			let classid = null
			let allFileTypes = [] // 所有文件类型列表
			let sortDesc = true // 默认降序排列，即最新的在前面
			
			let mtype = null //类型，默认null表示显示全部资源
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				// 确保所有资源都显示
				if (typeof ensureAllResourcesDisplayed === 'function') {
					ensureAllResourcesDisplayed();
				}
				
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				
				// 初始化文件类型筛选器，预先显示常见文件类型
				updateFileTypeFilters([]);
				
				getclassid()
				getclass('onlinelearning3.html') // 修改这里，确保传入当前页面URL
				getshuxin()
				getleixing()
				getxkzj()
				getfooterlink()
				getclassch()
				
				// 设置筛选器折叠/展开功能
				$("#filterToggle").on('click', function() {
					$("#filterContainer").slideToggle(200);
					$(this).toggleClass('collapsed');
				});
				
				// 初始化Bootstrap的工具提示
				const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
				tooltipTriggerList.map(function (tooltipTriggerEl) {
					return new bootstrap.Tooltip(tooltipTriggerEl);
				});
				
				// 检查URL参数，判断是否需要应用保存的筛选状态
				if (getUrlParam('applyFilter') === 'true') {
					// 尝试应用保存的筛选状态
					applyFilterState();
				} else {
					// 检查URL参数中是否有筛选条件
					applyUrlFilters();
				}
				
				// 添加CSS样式
				$('<style>').text(`
					.reset-all-button {
						background-color: #fff;
						border: 1px solid #e0e0e0;
						border-radius: 4px;
						padding: 8px 15px;
						color: #555;
						font-size: 14px;
						cursor: pointer;
						transition: all 0.2s ease;
						display: flex;
						align-items: center;
						gap: 8px;
					}
					.reset-all-button:hover {
						background-color: #f5f5f5;
						border-color: #d82121;
						color: #d82121;
					}
					.reset-all-button i {
						font-size: 14px;
					}
				`).appendTo('head');
			})
			
			// 应用保存的筛选状态
			function applyFilterState() {
				// 获取保存的筛选状态
				const savedFilterState = sessionStorage.getItem('resourceFilterState');
				
				if (savedFilterState) {
					try {
						const filterState = JSON.parse(savedFilterState);
						
						// 设置页码
						if (filterState.pageIndex) {
							pageindex = parseInt(filterState.pageIndex);
						}
						
						// 设置分类ID
						if (filterState.categoryId) {
							classid = filterState.categoryId;
						}
						
						// 设置课程ID
						if (filterState.courseId) {
							xkid = filterState.courseId;
						}
						
						// 设置章节ID
						if (filterState.chapterId) {
							zid = filterState.chapterId;
						}
						
						// 设置节ID
						if (filterState.nodeId) {
							jid = filterState.nodeId;
						}
						
						// 设置小节ID
						if (filterState.barId) {
							xjid = filterState.barId;
						}
						
						// 设置文件类型
						if (filterState.fileType) {
							fileTypeId = filterState.fileType;
						}
						
						// 设置属性ID
						if (filterState.attributeId) {
							sxid = filterState.attributeId;
						}
						
						// 设置类型ID
						if (filterState.typeId) {
							lxid = filterState.typeId;
						}
						
						// 设置排序方向
						if (filterState.sortDesc !== undefined) {
							sortDesc = filterState.sortDesc;
							// 更新排序按钮
							updateSortButtonState();
						}
						
						// 设置搜索关键词
						if (filterState.searchKeyword) {
							$("#searchInput").val(filterState.searchKeyword);
						}
						
						// 应用UI选中状态
						setTimeout(function() {
							// 应用课程选择并获取课程数据
							if (filterState.activeFilters && filterState.activeFilters.courseItem && filterState.courseId) {
								$(".course-item").removeClass("active tjactive");
								$(".course-item[data-id='" + filterState.activeFilters.courseItem + "']").addClass("active tjactive");
								
								// 重要：先更新课程数据，再恢复章节
								if (xkdata && xkdata.length > 0) {
									// 从xkdata中找到选中的课程，并获取其章节信息
									const selectedCourseData = xkdata.find(course => course.id == filterState.courseId);
									if (selectedCourseData && selectedCourseData.children && selectedCourseData.children.length > 0) {
										zdata = selectedCourseData.children;
										// 更新章节筛选器
										updateChapterFilter();
										
										// 修复：确保在章节数据加载后应用章节选择
										setTimeout(function() {
											if (filterState.activeFilters && filterState.activeFilters.chapterItem && filterState.chapterId) {
												$(".chapter-item").removeClass("active tjactive");
												$(".chapter-item[data-id='" + filterState.activeFilters.chapterItem + "']").addClass("active tjactive");
												// 确保章节选择区域显示
												if (filterState.activeFilters.chapterItem !== "0") {
													$("#chapterFilterSection").show();
												}
											}
										}, 300);
									}
								}
							} else {
								// 应用章节选择 - 如果没有课程数据
								setTimeout(function() {
									if (filterState.activeFilters && filterState.activeFilters.chapterItem && filterState.chapterId) {
										$(".chapter-item").removeClass("active tjactive");
										$(".chapter-item[data-id='" + filterState.activeFilters.chapterItem + "']").addClass("active tjactive");
										// 确保章节选择区域显示
										if (filterState.activeFilters.chapterItem !== "0") {
											$("#chapterFilterSection").show();
										}
									}
								}, 300);
							}
							
							// 应用类别选择
							if (filterState.activeFilters && filterState.activeFilters.categoryOption) {
								$("#qblbbox span").removeClass("tjactive");
								$("#qblbbox span[data-id='" + (filterState.activeFilters.categoryOption || '') + "']").addClass("tjactive");
								// 确保"全部"按钮状态正确
								if (filterState.activeFilters.categoryOption === "111") {
									$("#qblb").addClass("tjactive");
								} else {
									$("#qblb").removeClass("tjactive");
								}
							}
							
							// 应用属性选择
							if (filterState.activeFilters && filterState.activeFilters.attributeOption) {
								$("#sxbox span").removeClass("tjactive");
								$("#sxbox span[data-id='" + filterState.activeFilters.attributeOption + "']").addClass("tjactive");
							}
							
							// 应用类型选择
							if (filterState.activeFilters && filterState.activeFilters.typeOption) {
								$("#lxbox span").removeClass("tjactive");
								$("#lxbox span[data-id='" + filterState.activeFilters.typeOption + "']").addClass("tjactive");
							}
							
							// 应用文件类型选择
							if (filterState.activeFilters && filterState.activeFilters.fileTypeOption) {
								$("#filetypebox span").removeClass("tjactive");
								$("#filetypebox span[data-id='" + filterState.activeFilters.fileTypeOption + "']").addClass("tjactive");
							}
							
							// 更新筛选计数
							updateFilterCountBadge();
							
							// 章节筛选器已在课程选择处理中更新，这里无需再次更新
						}, 500);
						
						// 获取资源列表
						setTimeout(function() {
							getpages();
						}, 800);
						
						return true;
					} catch (error) {
						console.error('应用筛选状态失败:', error);
						return false;
					}
				}
				return false;
			}

			// 更新排序按钮状态
			function updateSortButtonState() {
				const $sortButton = $(".sort-button");
				if (sortDesc) {
					$sortButton.find("i").removeClass("fa-sort-amount-up").addClass("fa-sort-amount-down");
				} else {
					$sortButton.find("i").removeClass("fa-sort-amount-down").addClass("fa-sort-amount-up");
				}
			}

			// 从URL参数应用筛选条件
			function applyUrlFilters() {
				let filtersApplied = false;
				
				// 获取页码
				const urlPage = getUrlParam('currentPage');
				if (urlPage) {
					pageindex = parseInt(urlPage);
					filtersApplied = true;
				}
				
				// 获取课程ID
				const urlCourseId = getUrlParam('courseId');
				if (urlCourseId) {
					xkid = urlCourseId;
					filtersApplied = true;
					
					// 选中对应的课程并获取课程数据
					setTimeout(function() {
						$(".course-item").removeClass("active tjactive");
						$(".course-item[data-id='" + urlCourseId + "']").addClass("active tjactive");
						
						// 更新课程数据
						if (xkdata && xkdata.length > 0) {
							// 从xkdata中找到选中的课程，并获取其章节信息
							const selectedCourseData = xkdata.find(course => course.id == urlCourseId);
							if (selectedCourseData && selectedCourseData.children && selectedCourseData.children.length > 0) {
								zdata = selectedCourseData.children;
								// 更新章节筛选器
								updateChapterFilter();
								
								// 然后处理章节选择
								const urlChapterId = getUrlParam('chapterId');
								if (urlChapterId) {
									zid = urlChapterId;
									filtersApplied = true;
									
									// 延迟一点选中章节，确保章节列表已经渲染
									setTimeout(function() {
										$(".chapter-item").removeClass("active tjactive");
										$(".chapter-item[data-id='" + urlChapterId + "']").addClass("active tjactive");
										$("#chapterFilterSection").show();
									}, 300);
								}
							}
						}
					}, 500);
				} else {
					// 如果没有课程ID但有章节ID
					const urlChapterId = getUrlParam('chapterId');
					if (urlChapterId) {
						zid = urlChapterId;
						filtersApplied = true;
						
						// 确保章节筛选器初始化并选中对应章节
						setTimeout(function() {
							updateChapterFilter();
							$(".chapter-item").removeClass("active tjactive");
							$(".chapter-item[data-id='" + urlChapterId + "']").addClass("active tjactive");
							$("#chapterFilterSection").show();
						}, 600);
					}
				}
				
				// 获取节ID
				const urlNodeId = getUrlParam('nodeId');
				if (urlNodeId) {
					jid = urlNodeId;
					filtersApplied = true;
				}
				
				// 获取小节ID
				const urlBarId = getUrlParam('barId');
				if (urlBarId) {
					xjid = urlBarId;
					filtersApplied = true;
				}
				
				// 获取文件类型
				const urlFileType = getUrlParam('fileType');
				if (urlFileType) {
					fileTypeId = urlFileType;
					filtersApplied = true;
					
					// 选中对应的文件类型
					setTimeout(function() {
						$("#filetypebox span").removeClass("tjactive");
						$("#filetypebox span[data-id='" + urlFileType + "']").addClass("tjactive");
					}, 500);
				}
				
				// 获取属性ID
				const urlAttributeId = getUrlParam('attributeId');
				if (urlAttributeId) {
					sxid = urlAttributeId;
					filtersApplied = true;
					
					// 选中对应的属性
					setTimeout(function() {
						$("#sxbox span").removeClass("tjactive");
						$("#sxbox span[data-id='" + urlAttributeId + "']").addClass("tjactive");
					}, 500);
				}
				
				// 获取类型ID
				const urlTypeId = getUrlParam('typeId');
				if (urlTypeId) {
					lxid = urlTypeId;
					filtersApplied = true;
					
					// 选中对应的类型
					setTimeout(function() {
						$("#lxbox span").removeClass("tjactive");
						$("#lxbox span[data-id='" + urlTypeId + "']").addClass("tjactive");
					}, 500);
				}
				
				// 获取类别ID (mtype)
				const urlMtype = getUrlParam('mtype');
				if (urlMtype) {
					mtype = urlMtype;
					filtersApplied = true;
					
					// 选中对应的类别
					setTimeout(function() {
						$("#qblbbox span").removeClass("tjactive");
						if (urlMtype === "111") {
							$("#qblb").addClass("tjactive");
						} else {
							$("#qblb").removeClass("tjactive");
							$("#qblbbox span[data-id='" + urlMtype + "']").addClass("tjactive");
						}
					}, 500);
				}
				
				// 获取排序方向
				const urlSortDesc = getUrlParam('sortDesc');
				if (urlSortDesc !== null) {
					sortDesc = urlSortDesc === 'true';
					filtersApplied = true;
					
					// 更新排序按钮状态
					updateSortButtonState();
				}
				
				// 获取搜索关键词
				const urlKeyword = getUrlParam('keyword');
				if (urlKeyword) {
					$("#searchInput").val(decodeURIComponent(urlKeyword));
					filtersApplied = true;
				}
				
				if (filtersApplied) {
					// 更新筛选计数
					setTimeout(function() {
						updateFilterCountBadge();
						// 获取资源列表
						getpages();
					}, 800);
				} else {
					// 如果没有应用任何筛选，默认获取资源列表
					getpages();
				}
			}
			
			// 移除切换排序功能，此函数保留为空以避免错误
			function toggleSort() {
				// 排序按钮已移除，此函数仅保留为空以避免错误
			}
			
			// 移除排序信息更新功能，因为已不再使用排序按钮
			function updateSortInfo() {
				// 已移除排序按钮，此函数仅保留为空以避免错误
			}
			
			let time1 = null
			let pflid = null
			let xkidss = null
			let infoid = null
			function tiaozhuan() {
				if(JSON.parse(userinfo).roleName == '学生'){
					let time2 = Date.now()
					let value = time2 - time1
					var days = parseInt(value / (1000 * 60 * 60 * 24))
					var hours = parseInt((value % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
					var minutes = parseInt((value % (1000 * 60 * 60)) / (1000 * 60))
					var seconds = Math.floor((value % (1000 * 60)) / 1000)
					let json = {
						infoId: infoid, //信息id
						categoryId: pflid, //所属分类id
						totalInfo: "1", //总时长，总页数
						positioning: days+"天"+hours+"时"+minutes+"分"+seconds+"秒", //学习了多久，多少页    
						progress: "", //进度 百分比
						type: '在线学习',
						learningTime: value,
						sectionId: xkidss//学科ID
					}
					window.localStorage.setItem("jilu",JSON.stringify(json))
					if(window.localStorage.getItem("jilu")){
						$.ajax({
							url: baseurl + "/study/record/add",
							type: 'post',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: window.localStorage.getItem("jilu"),
							dataType: 'json',
							success: (res) => {
								window.localStorage.clear()
							}
						})
					}
				}
			}
			function getfooterlink(){
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item)=>{
								html+='<a href="'+item.url+'" target="_blank">'+item.name+'</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
			function getclassid() {
				$.ajax({
					url: baseurl + "/web/category/teaching",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							classid = res.data[0].id
							if(getUrlParam('type')){
								if(getUrlParam('type') == '00'){
									mtype = 0
								}
							}else{
								// 默认显示所有资源 - 相当于选中了"全部"
								mtype = null
							}
							getpages()
						}
					}
				})
			}
			function getclassch(){
				$.ajax({
					url: baseurl + "/web/category/teacher",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							classdate = res.data[0]
							getlist(res.data[0])
						}
					}
				})
			}
			function getlist(classdate) {
				let classhtml = ""
				for (let i = 0; i < classdate.children.length; i++) {
					if (classdate.children[i].name == '教学资源') {
						classhtml += '<a class="titleactive" href="' + classdate.children[i].redirectUrl + '?id=' + classdate
							.children[i].id + '">' + classdate.children[i].name + '</a>'
						$("#hsname").html(classdate.children[i].name)
					} else {
						classhtml += '<a href="' + classdate.children[i].redirectUrl + '?id=' + classdate.children[i].id + '">' +
							classdate.children[i].name + '</a>'
					}
				}
				$("#classbox").html(classhtml)
			}
			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function getshuxin() { //获取属性列表
				$.ajax({
					url: baseurl + "/web/attributes",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let sxhtml = ""
							for (let i = 0; i < res.data.length; i++) {
								if (res.data[i].id == sxid) {
									sxhtml += '<span onclick="selectsx(this)" class="filter-option active" data-id="' + res.data[
										i].id + '">' + res.data[
										i].name + '</span>'
								} else {
									sxhtml += '<span onclick="selectsx(this)" class="filter-option" data-id="' + res.data[i].id + '">' +
										res.data[i].name +
										'</span>'
								}
							}
							$("#qbsx").append(sxhtml)
						}
					}
				})
			}

			function getleixing() { //获取类型列表
				$.ajax({
					url: baseurl + "/web/types",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log("获取到类型列表数据:", res.data);
							let typeOptionsHtml = '<span class="filter-option active" data-id="0" onclick="selectlx(this)">全部</span>';
							
							// 遍历类型数据并生成选项
							res.data.forEach(item => {
								let isActive = item.id === lxid ? 'active' : '';
								typeOptionsHtml += `<span class="filter-option ${isActive}" 
													   data-id="${item.id}" 
													   title="${item.name}"
													   onclick="selectlx(this)">${item.name}</span>`;
							});
							
							// 更新类型选项到筛选面板
							$("#qblx").html(typeOptionsHtml);
							
							// console.log("类型选项已更新到筛选面板");
								} else {
							console.error("获取类型列表失败:", res.message);
								}
					},
					error: (err) => {
						console.error("获取类型列表请求出错:", err);
							}
				});
			}

			function getxkzj() { //获取学科章节列表
				$.ajax({
					url: baseurl + "/web/project/section/list/tree",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							xkdata = res.data;
							
							// 生成课程网格
							let courseHtml = `<div class="course-item active tjactive" data-id="0" onclick="selectxk(this)" title="全部课程"><span>全部课程</span></div>`;
							xkdata.forEach((item) => {
								// 添加 title 属性用于悬浮显示完整名称，使用span包裹文字以支持滚动效果
								courseHtml += `<div class="course-item" data-id="${item.id}" onclick="selectxk(this)" title="${item.name}"><span>${item.name}</span></div>`;
							});
							
							// 确保DOM更新后立即检测溢出
							setTimeout(checkItemOverflow, 100);
							$("#courseGrid").html(courseHtml);
							
							// 初始化章节筛选器（如果需要立即显示的话）
							// updateChapterFilterVisibility(); 
						}
					}
				});
			}

			function getpages() {
				// 构建请求参数对象，便于调试
				const requestParams = {
					categoryId: classid,
					pageSize: pagesize,
					pageNum: pageindex,
					projectId: xkid || '',
					sectionId: zid || '',
					nodeId: jid || '',
					barId: xjid || '',
					attributesId: sxid || '',
					typeId: lxid || '',
					type: mtype || '',
					attachType: fileTypeId || '',
					sortField: "createTime",
					sortOrder: sortDesc ? "desc" : "asc"
				};
				
				// 添加详细的调试输出
				// console.log("发起筛选请求，参数:", JSON.stringify(requestParams, null, 2));
				
				$.ajax({
					url: baseurl + "/web/course",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: requestParams,
					dataType: 'json',
					beforeSend: function() {
						// 显示轻量级加载提示
						$("#list").html('<div class="loading-resources"><div class="loading-spinner"></div><p>正在加载资源...</p></div>');
					},
					success: (res) => {
						if (res.code == '200') {
							// console.log("获取资源成功，总数:", res.data.total);
							if (res.data && res.data.list && res.data.list.length > 0) {
								let types = new Set();
								res.data.list.forEach(item => {
									if (item.attachType) {
										types.add(item.attachType.toLowerCase());
									}
								});
								updateFileTypeFilters(Array.from(types));
							}
							
							let html = ""
							res.data.list.map((item, index) => {
								// 添加延迟显示动画
								const delay = index % 10 * 50; // 每行5个，错开显示时间
								
																	html +=
										'<div class="txtitem" style="animation-delay: '+delay+'ms;"><div class="topitem" onclick="inxx(this)" data-id="'+item.metaId+'"><div class="itemtype '+item.attachType+'">' +item.attachType+
										'</div>'
										if(item.coverPath!=null){
											html+='<img data-src="'+baseurl+item.coverPath[0]+'" />'
										}
										html+='<div class="icobox">' +
										'<label><img class="zzico" src="img/fbr.png" /><span>'+item.author+'</span></label>' +
										'<label><img class="gklico" src="img/gkl.png" />'+item.view+'</label>' +
										'</div></div><div class="bottomitem">' +
										'<div class="title">'+item.title+'</div>' +
										'<div class="zz">'+item.introduction+'</div>'+
										''+
										'</div></div>'
								
							})
							$("#list").html(html)

							// 延迟显示卡片动画
							setTimeout(function() {
								$('.txtitem').addClass('show');
							}, 100);

							// 图片懒加载
							lazyLoad();

							// 显示资源数量
							$("#resourceCount").text(res.data.total || 0);

							pages = res.data.pages
							if (pages > 1) {
								let numhtml = ""
								for (let a = 1; a <= pages; a++) {
									if (pageindex == a) {
										numhtml += '<label class="actinum" onclick="getnewlist(' + a + ')">' + a +
											'</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
							
							// 没有结果时显示提示
							if (res.data.total === 0) {
								$("#list").html('<div class="alert alert-info p-4 text-center"><i class="fas fa-info-circle me-2"></i>未找到匹配的资源，请尝试其他筛选条件</div>');
							}
							
							// 更新筛选器计数
							updateFilterCountBadge();
						}
					}
				})
			}

			// 图片懒加载函数优化
			function lazyLoad() {
				$('.topitem img').each(function() {
					if ($(this).attr('data-src') && !$(this).attr('src')) {
						let img = $(this);
						if (img.offset().top < $(window).height() + $(window).scrollTop() + 200) {
							img.attr('src', img.attr('data-src'));
							// 添加加载状态
							img.css('opacity', '0');
							img.on('load', function() {
								img.css('opacity', '1');
							});
						}
					}
				});
				
				// 检测标题是否需要滚动效果
				checkTitleOverflow();
			}
			
			// 检测标题是否溢出并添加适当的滚动效果
			function checkTitleOverflow() {
				$('.txtitem .bottomitem .title').each(function() {
					const $title = $(this);
					const titleWidth = $title[0].scrollWidth;
					const containerWidth = $title.parent().width();
					
					// 如果标题宽度超过容器宽度，则需要滚动效果
					if (titleWidth > containerWidth) {
						// 计算基于文本长度的动画持续时间 - 较长的文本需要更长的滚动时间
						const textLength = $title.text().length;
						const duration = Math.max(5, Math.min(15, textLength * 0.3)); // 最小5秒，最大15秒
						
						// 设置自定义动画时间
						$title.css('animation-duration', duration + 's');
						
						// 移除no-scroll类（如果有）
						$title.removeClass('no-scroll');
					} else {
						// 标题宽度没有超过容器，不需要滚动效果
						$title.addClass('no-scroll');
					}
				});
			}
			
			// 检测课程项和章节项溢出
			function checkItemOverflow() {
				// 检查所有可能需要滚动效果的元素
				$('.course-item, .chapter-item').each(function() {
					const $item = $(this);
					const $text = $item.find('span');
					
					if ($text.length) {
						const textWidth = $text[0].scrollWidth;
						const containerWidth = $item.width() - 20; // 减去内边距
						
						if (textWidth > containerWidth) {
							// 标记为溢出
							$item.attr('data-overflow', 'true');
							$text.addClass('needs-scroll');
						} else {
							// 移除溢出标记
							$item.removeAttr('data-overflow');
							$text.removeClass('needs-scroll');
						}
					}
				});
			}
			
			let clipboard = new ClipboardJS('.copybtn');
			clipboard.on('success', function(e) {
				e.clearSelection();
				cocoMessage.success(1000, "复制成功！")
			});
			
			clipboard.on('error', function(e) {
				cocoMessage.error(1000, "复制失败！")
			});
			function closetc(){
				$("#tcbox").hide()
				this.tiaozhuan()
			}
			// PDF功能已移除
			function showpdf(pdf){
				// 提示用户PDF功能已移除
				cocoMessage.warning(2000, "PDF功能已移除，请使用其他格式查看内容");
			}
			// 保存完整的筛选条件到sessionStorage
function storeFilters() {
    const filterState = {
        pageIndex: pageindex,
        categoryId: classid || getUrlParam('categoryId'),
        courseId: xkid || null,
        chapterId: zid || null,
        nodeId: jid || null,
        barId: xjid || null,
        fileType: fileTypeId || null,
        attributeId: sxid || null,
        typeId: lxid || null,
        sortDesc: sortDesc,
        searchKeyword: $("#searchInput").val() ? $("#searchInput").val().trim() : null,
        // 保存选中的UI元素状态
        activeFilters: {
            // 添加类别选中状态
            categoryOption: mtype || "111", // 使用mtype变量保存的类别ID，如果为null则使用"111"(全部)
            courseItem: $(".course-item.active").attr("data-id") || "0",
            chapterItem: $(".chapter-item.active").attr("data-id") || "0",
            attributeOption: $("#sxbox span.tjactive").attr("data-id") || "0",
            typeOption: $("#lxbox span.tjactive").attr("data-id") || "0",
            fileTypeOption: $("#filetypebox span.tjactive").attr("data-id") || "all"
        }
    };
				
    // 将筛选状态保存到sessionStorage
    sessionStorage.setItem('resourceFilterState', JSON.stringify(filterState));
    return filterState;
}

// 优化inxx函数，添加防抖保护机制
function inxx(item) {
    // 防止重复点击
    if (window.isNavigating) {
        return;
    }
				
    // 设置标记，防止重复导航
    window.isNavigating = true;
				
    // 视觉反馈
    $(item).css('opacity', '0.7');
				
    try {
    // 保存筛选条件
    const filterState = storeFilters();
				
    // 构建URL并包含基本参数
    let url = "onlinelearning4.html?id=" + $(item).attr("data-id") + "&currentPage=" + pageindex;
				
    // 添加分类ID
    if (filterState.categoryId) url += "&categoryId=" + filterState.categoryId;
				
    // 添加类别ID (mtype)
    if (mtype !== null) url += "&mtype=" + mtype;
				
    // 添加课程ID
    if (filterState.courseId) url += "&courseId=" + filterState.courseId;
				
    // 添加章节ID
    if (filterState.chapterId) url += "&chapterId=" + filterState.chapterId;
				
    // 添加节ID
    if (filterState.nodeId) url += "&nodeId=" + filterState.nodeId;
				
    // 添加小节ID
    if (filterState.barId) url += "&barId=" + filterState.barId;
				
    // 添加文件类型
    if (filterState.fileType) url += "&fileType=" + filterState.fileType;
				
    // 添加属性ID
    if (filterState.attributeId) url += "&attributeId=" + filterState.attributeId;
				
    // 添加类型ID
    if (filterState.typeId) url += "&typeId=" + filterState.typeId;
				
    // 添加排序方向
    url += "&sortDesc=" + filterState.sortDesc;
				
    // 添加搜索关键词
    if (filterState.searchKeyword) url += "&keyword=" + encodeURIComponent(filterState.searchKeyword);
				
        // 使用timeout确保视觉反馈有时间显示
        setTimeout(function() {
    window.location.href = url;
        }, 100);
    } catch (e) {
        console.error("导航发生错误:", e);
        // 恢复视觉状态
        $(item).css('opacity', '');
        // 重置导航状态
        window.isNavigating = false;
    }
}

// 优化PDF查看器相关代码，添加防抖保护
function showpdf(pdf){
    // 防止重复点击
    if (window.isLoadingPdf) {
        return;
    }
				
    // 设置加载标记
    window.isLoadingPdf = true;
    
    // 添加视觉反馈
    $(pdf).css('opacity', '0.7');
    
    if(!userinfo) {
        window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html';
        // 重置状态
        window.isLoadingPdf = false;
        $(pdf).css('opacity', '');
        return;
    }
    
    $.ajax({
        url: baseurl + "/course/view/"+$(pdf).attr("data-cid"),
        type: 'GET',
        contentType: "application/json",
        headers: {
            "Authorization": sessionStorage.getItem("header")
        },
        dataType: 'json',
        success: (res) => {},
        error: () => {
            // 出错时重置状态
            window.isLoadingPdf = false;
            $(pdf).css('opacity', '');
        }
    });
    
    $.ajax({
        url: baseurl + "/course/meta/"+$(pdf).attr("data-id"),
        type: 'GET',
        contentType: "application/json",
        headers: {
            "Authorization": sessionStorage.getItem("header")
        },
        dataType: 'json',
        success: (res) => {
            if (res.code == '200') {
                time1 = Date.now();
                infoid = $(pdf).attr("data-id");
                xkidss = res.data.projectId;
                $("#pdfname").html(res.data.cmsResourcesCourseMetaList[0].attachName);
                $("#tcbox").addClass('active').show();
                $("#pdf").attr("href", baseurl + res.data.cmsResourcesCourseMetaList[0].attachPath);
                $('a.media').media({width: 800, height: 850});
            }
            // 重置加载标记和视觉状态
            window.isLoadingPdf = false;
            $(pdf).css('opacity', '');
        },
        error: () => {
            // 出错时重置状态
            window.isLoadingPdf = false;
            $(pdf).css('opacity', '');
            cocoMessage.error(1000, "加载PDF失败，请重试");
        }
    });
}

// 优化页面初始化和事件处理
$(function() {
    // 原有初始化代码保持不变
    
    // 优化图片懒加载，减少内存使用和提升性能
    const originalLazyLoad = window.lazyLoad;
    window.lazyLoad = function() {
        if (window.lazyLoadRunning) return; // 防止并发执行
        
        window.lazyLoadRunning = true;
        
        try {
            // 找到可视区域内的图片进行加载
            $('.topitem img').each(function() {
                if ($(this).attr('data-src') && !$(this).attr('src')) {
                    let img = $(this);
                    if (img.offset() && img.offset().top < $(window).height() + $(window).scrollTop() + 200) {
                        img.attr('src', img.attr('data-src'));
                        img.css('opacity', '0');
                        img.on('load', function() {
                            img.css('opacity', '1');
                        });
                    }
                }
            });
        } finally {
            window.lazyLoadRunning = false;
        }
    };
				
    // 优化滚动事件处理，添加节流
    let scrollTimer = null;
    $(window).off('scroll').on('scroll', function() {
        // 返回顶部按钮显示/隐藏 - 这个可以立即执行，开销小
        if ($(window).scrollTop() > 600) {
            $("#backtop").addClass('visible').show();
        } else {
            $("#backtop").removeClass('visible').hide();
        }
        
        // 图片懒加载 - 节流执行，避免频繁调用
        clearTimeout(scrollTimer);
        scrollTimer = setTimeout(function() {
            lazyLoad();
        }, 100);
    });
    
    // 优化窗口大小变化事件，添加节流
    let resizeTimer = null;
    $(window).off('resize').on('resize', function() {
        clearTimeout(resizeTimer);
        resizeTimer = setTimeout(function() {
            checkTitleOverflow();
            checkItemOverflow();
            adjustCardHeight();
        }, 200);
    });
});

// 确保卡片高度统一美观 - 全局函数
function adjustCardHeight() {
    // 重置所有卡片底部内容区高度
    $('.txtitem .bottomitem').css('height', 'auto');
    
    // 统一底部高度，确保一致性
    let maxHeight = 80; // 默认高度
    $('.txtitem .bottomitem').height(maxHeight);
}

			function getnewlist(index){
				if(index == pageindex){
					if(index == pages){
						cocoMessage.warning(1000, "已经是第最后一页了！")
					}else{
						cocoMessage.warning(1000, "已经是第"+index+"页了！")
					}
				}else{
					pageindex = index
					getpages()
				}
			}
			function selectlb(e) { //选择类别
				updateOptionState(e, '#qblb');
				const $activeOption = $(e);
				
				// 修复：直接从点击的元素获取data-id值
				if ($activeOption.attr('data-id') === '111') {
					mtype = null;
					// console.log("选择类别：全部 (type = null)");
						} else {
					mtype = $activeOption.attr('data-id');
					// console.log("选择类别：" + $activeOption.text() + " (type = " + mtype + ")");
						}
				
				// 保存筛选状态
				if (typeof storeFilters === 'function') {
					storeFilters();
				}
				
				pageindex = 1;
				getpages();
			}
			function selectsx(e) { //选择属性
				// 移除所有属性选项的active类
				$("#qbsx .filter-option").removeClass("active");
				// 为点击的元素添加active类
				$(e).addClass("active");
				
				// 获取data-id值
				if ($(e).attr('data-id') === '0') {
					sxid = null;
				} else {
					sxid = $(e).attr('data-id');
				}
				
				// 保存筛选状态
				if (typeof storeFilters === 'function') {
					storeFilters();
				}
				
				pageindex = 1;
				updateFilterCountBadge(); // 更新筛选计数
				getpages();
			}

			function selectlx(e) { //选择类型
				updateOptionState(e, '#qblx');
				const $activeOption = $(e);
				
				// 修复：直接从点击的元素获取data-id值
				if ($activeOption.attr('data-id') === '0') {
					lxid = null;
					// console.log("选择类型：全部 (typeId = null)");
				} else {
					lxid = $activeOption.attr('data-id');
					// console.log("选择类型：" + $activeOption.text() + " (typeId = " + lxid + ")");
				}
				
				// 保存筛选状态
				if (typeof storeFilters === 'function') {
					storeFilters();
				}
				
				pageindex = 1;
				getpages();
			}

			function selectxk(e) { //选择学科
	const courseId = $(e).attr("data-id");
	
	// 更新选中状态
	$(".course-item").removeClass("active tjactive");
	$(e).addClass("active tjactive");
	
	if (courseId === "0") {
		xkid = null;
		zdata = null; // 清空章节数据
		zid = null;   // 清空选中的章节ID
	} else {
		xkid = courseId;
		// 从xkdata中找到选中的课程，并获取其章节信息
		const selectedCourseData = xkdata.find(course => course.id == xkid);
		if (selectedCourseData && selectedCourseData.children && selectedCourseData.children.length > 0) {
			zdata = selectedCourseData.children;
			} else {
			zdata = null; // 该课程无章节
		}
		zid = null; // 新课程选中时，重置章节选择
	}
	
	updateChapterFilter(); // 更新章节筛选器的显示和内容
	
	// 检测文本溢出情况，确保滚动效果正常工作
	setTimeout(checkItemOverflow, 100);
	
	// 保存筛选状态
	if (typeof storeFilters === 'function') {
		storeFilters();
	}
	
	pageindex = 1;
	getpages();
	if (typeof updateFilterCountBadge === 'function') {
		updateFilterCountBadge();
	}
}

			function updateChapterFilter() {
				const $chapterSection = $("#chapterFilterSection");
				const $chapterGrid = $("#chapterGrid");

				if (xkid && zdata && zdata.length > 0) {
					let chapterHtml = `<div class="chapter-item active tjactive" data-id="0" onclick="selectChapter(this)" title="全部章节"><span>全部章节</span></div>`;
					zdata.forEach(chapter => {
						chapterHtml += `<div class="chapter-item" data-id="${chapter.id}" onclick="selectChapter(this)" title="${chapter.name}"><span>${chapter.name}</span></div>`;
					});
					$chapterGrid.html(chapterHtml);
					$chapterSection.slideDown(200);
					// 确保DOM更新后立即检测溢出
					setTimeout(checkItemOverflow, 100);
				} else {
					$chapterGrid.empty();
					$chapterSection.slideUp(200);
					zid = null; // 确保在隐藏时也清空章节ID
				}
			}

			function selectChapter(e) {
	const chapterId = $(e).attr("data-id");

	// 更新章节选中状态
	$(".chapter-item").removeClass("active tjactive");
	$(e).addClass("active tjactive");

	if (chapterId === "0") {
		zid = null; // "全部章节"被选中
	} else {
		zid = chapterId;
	}

	// 检测文本溢出情况，确保滚动效果正常工作
	setTimeout(checkItemOverflow, 100);
	
	// 保存筛选状态
	if (typeof storeFilters === 'function') {
		storeFilters();
	}

	pageindex = 1;
	getpages();
	if (typeof updateFilterCountBadge === 'function') {
		updateFilterCountBadge();
	}
}

			function zonchange(e) {
				$("#j").val('0')
				$("#xj").val('0')
				$("#xj").html("<option value='0'>请选择小节</option>")
				
				// 禁用小节选择框
				$("#xj").prop('disabled', true);
				
				zdata.map((item) => {
					if (item.id == $(e).val()) {
						jdata = item.children
					}
				})
				zid = $(e).val()
				
				if (jdata && jdata.length > 0) {
					let jhtml = "<option value='0'>请选择节</option>"
					jdata.map((item) => {
						jhtml += "<option value='" + item.id + "'>" + item.name + "</option>"
					})
					$("#j").html(jhtml)
					$("#j").prop('disabled', false);
				} else {
					$("#j").html("<option value='0'>请选择节</option>")
					$("#j").prop('disabled', true);
				}
				getnewpages()
			}

			function jonchange(e) {
				$("#xj").val('0')
				jdata.map((item) => {
					if (item.id == $(e).val()) {
						xjdata = item.children
					}
				})
				jid = $(e).val()
				
				if (xjdata && xjdata.length > 0) {
					let xjhtml = "<option value='0'>请选择小节</option>"
					xjdata.map((item) => {
						xjhtml += "<option value='" + item.id + "'>" + item.name + "</option>"
					})
					$("#xj").html(xjhtml)
					$("#xj").prop('disabled', false);
				} else {
					$("#xj").html("<option value='0'>请选择小节</option>")
					$("#xj").prop('disabled', true);
				}
				getnewpages()
			}

			function xjonchange(e) {
				xjid = $(e).val()
				getnewpages()
			}

			function getnewpages() {
				
				getpages()
			}

			$("#select div").on('click', function() {
				$("#selecttxt").html($(this).html())
				$("#select").hide()
			})

			function showselect() {
				$("#select").show()
			}

			// 更新文件类型筛选器的选项
			function updateFileTypeFilters(types) {
				const commonTypes = ['mp4','pptx', 'ppt', 'docx', 'doc', 'mp3']; // PDF已移除
				const currentSelectedFileTypeId = fileTypeId; // Preserve selection

				let newAllFileTypes = [...new Set([...allFileTypes, ...types, ...commonTypes])];
				newAllFileTypes = newAllFileTypes.filter(type => type && type.trim() !== '').sort();

				const newAllFileTypesString = JSON.stringify(newAllFileTypes);
				const currentAllFileTypesString = JSON.stringify(allFileTypes.slice().sort());

				if (newAllFileTypesString !== currentAllFileTypesString || $("#filetypebox").children().length <= 1) {
					 allFileTypes = newAllFileTypes;
				} else {
					if ($("#filetypebox").children().length > 1 || allFileTypes.length === 0) {
						// No actual change in available types, and we already have buttons (or no types at all)
						// Just ensure the active class is correctly set based on currentSelectedFileTypeId
						$("#filetypebox .filter-option").removeClass('active tjactive');
						if (currentSelectedFileTypeId === null) {
							$("#filetypebox .filter-option[data-id='all']").addClass('active tjactive');
						} else {
							$("#filetypebox .filter-option[data-id='" + currentSelectedFileTypeId + "']").addClass('active tjactive');
						}
						return; 
					}
				}
				
				// Always start with the "All" button
				// Ensure it gets both 'active' and 'tjactive' classes if no specific type is selected (fileTypeId is null)
				let typeHtml = `<span class="filter-option ${currentSelectedFileTypeId === null ? 'active tjactive' : ''}" data-id="all" onclick="selectFileType(this)">全部</span>`;

				allFileTypes.forEach(type => {
					if (type.toLowerCase() !== 'all') { 
							let displayType = type.toUpperCase();
						// Ensure correct active state for dynamically generated type buttons
						typeHtml += `<span class="filter-option ${currentSelectedFileTypeId === type ? 'active tjactive' : ''}" data-id="${type}" onclick="selectFileType(this)">${displayType}</span>`;
						}
					});
						$("#filetypebox").html(typeHtml);
			}
			
			// 选择文件类型
			function selectFileType(e) {
				updateOptionState(e, '#filetypebox');
				const $activeOption = $(e);
				
				// 修复：直接从点击的元素获取data-id值
				if ($activeOption.attr('data-id') === 'all') {
					fileTypeId = null;
					// console.log("选择文件类型：全部 (attachType = null)");
				} else {
					fileTypeId = $activeOption.attr('data-id');
					// console.log("选择文件类型：" + $activeOption.text() + " (attachType = " + fileTypeId + ")");
				}
				
				// 保存筛选状态
				if (typeof storeFilters === 'function') {
					storeFilters();
				}
				
				pageindex = 1;
				updateFilterCountBadge();
				getpages();
			}
			
			// 重置所有筛选条件
			function resetFilters() {
				// 重置各种筛选参数
				mtype = null; // 默认显示所有资源
				sxid = null;
				lxid = null;
				xkid = null;
				zid = null;
				jid = null;
				xjid = null;
				fileTypeId = null;
				sortDesc = true; // 重置为默认降序
				
				// 重置属性筛选
				$("#qbsx .filter-option").removeClass("active tjactive");
				$("#qbsx .filter-option[data-id='0']").addClass("active tjactive");
				
				// 重置类型筛选
				$("#qblx .filter-option").removeClass("active tjactive");
				$("#qblx .filter-option[data-id='0']").addClass("active tjactive");
				
				// 重置课程选择
				$(".course-item").removeClass("active tjactive");
				$(".course-item[data-id='0']").addClass("active tjactive");
				
				// 重置章节选择 - 隐藏章节筛选区域
				$("#chapterFilterSection").slideUp(200);
				$("#chapterGrid").empty();
				
				// 重置文件类型筛选
				$("#filetypebox .filter-option").removeClass("active tjactive");
				$("#filetypebox .filter-option[data-id='all']").addClass("active tjactive");
				
				// 重置排序按钮状态
				$(".sort-button i").removeClass("fa-sort-amount-up").addClass("fa-sort-amount-down");
				
				// 清空搜索框
				$("#searchInput").val("");
				
				pageindex = 1;
				getpages();
				
				// 更新筛选条件数量标记
				updateFilterCountBadge();
				
				// 显示重置成功提示
				cocoMessage.success(1000, "已重置所有筛选条件");
			}
			
			// 更新筛选条件数量标记
			function updateFilterCountBadge() {
				const activeFilters = getActiveFiltersCount();
				
				// 尝试获取可能的过滤器计数元素 (兼容不同版本的页面结构)
				const filterCount = document.getElementById('filterCount') || 
				                    document.querySelector('.filter-count');
				
				if (!filterCount) {
					// 如果元素不存在，则不进行任何操作
					// console.log('警告: 筛选条件计数元素不存在');
					return;
				}
				
				if (activeFilters > 0) {
					// 根据元素类型设置文本和显示状态
					filterCount.textContent = activeFilters;
					
					// 尝试使用classList (如果支持)
					if (filterCount.classList) {
					filterCount.classList.remove('d-none');
				} else {
						// 回退到style属性
						filterCount.style.display = 'inline-block';
					}
				} else {
					// 根据元素类型隐藏
					if (filterCount.classList) {
					filterCount.classList.add('d-none');
					} else {
						filterCount.style.display = 'none';
					}
				}
			}
			
			// 获取激活的筛选条件数量
			function getActiveFiltersCount() {
				let count = 0;
				
				// 检查类别筛选
				if (mtype !== null) count++;
				
				// 检查属性筛选
				if (sxid !== null) count++;
				
				// 检查类型筛选
				if (lxid !== null) count++;
				
				// 检查课程选择
				if (xkid !== null) count++;
				
				// 检查章节筛选
				if (zid && zid !== '0') count++;
				if (jid && jid !== '0') count++;
				if (xjid && xjid !== '0') count++;
				
				// 检查文件类型筛选
				if (fileTypeId !== null) count++;
				
				// 检查搜索框
				const searchInput = $("#searchInput");
				if (searchInput.length && searchInput.val().trim()) count++;
				
				return count;
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				// 页面加载完成后隐藏加载动画
				setTimeout(function() {
					$('.loading-overlay').fadeOut();
				}, 500);

				// 监听滚动事件
				$(window).scroll(function() {
					// 返回顶部按钮显示/隐藏
					if ($(window).scrollTop() > 600) {
						$("#backtop").addClass('visible').show();
					} else {
						$("#backtop").removeClass('visible').hide();
					}

					// 图片懒加载
					lazyLoad();
				});
				
				// 监听窗口大小变化，重新检测标题溢出
				$(window).resize(function() {
					checkTitleOverflow();
					adjustCardHeight();
				});
				
				// 页面加载完成后检测标题溢出和调整卡片高度
				setTimeout(function() {
					checkTitleOverflow();
					adjustCardHeight();
				}, 600);
				
				// 卡片高度调整函数已移至全局作用域

				// 平滑滚动到顶部
				$("#backtop").on('click', function() {
					$("body,html").animate({
						scrollTop: 0
					}, 300);
				});

				// 修改PDF显示函数
				window.showpdf = function(pdf) {
					if(!userinfo) {
						window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html';
						return;
					}
					
					$.ajax({
						url: baseurl + "/course/view/"+$(pdf).attr("data-cid"),
						type: 'GET',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						dataType: 'json',
						success: (res) => {}
					});
					
					$.ajax({
						url: baseurl + "/course/meta/"+$(pdf).attr("data-id"),
						type: 'GET',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						dataType: 'json',
						success: (res) => {
							if (res.code == '200') {
								time1 = Date.now();
								infoid = $(pdf).attr("data-id");
								xkidss = res.data.projectId;
								$("#pdfname").html(res.data.cmsResourcesCourseMetaList[0].attachName);
								$("#tcbox").addClass('active').show();
								$("#pdf").attr("href", baseurl + res.data.cmsResourcesCourseMetaList[0].attachPath);
								$('a.media').media({width: 800, height: 850});
							}
						}
					});
				};

				// 修改关闭PDF函数
				window.closetc = function() {
					$("#tcbox").removeClass('active').fadeOut(300);
					tiaozhuan();
				};
			});
		</script>
		<script>
			// 筛选器折叠/展开功能
			document.addEventListener('DOMContentLoaded', function() {
				// 使用querySelector来获取元素，因为元素没有相应的ID
				const filterToggle = document.querySelector('.filter-toggle.advanced');
				const filterContainer = document.querySelector('.filter-container');
				
				// 初始化筛选条件数量指示器
				updateFilterCountBadge();
				
				// 切换筛选器显示/隐藏
				if (filterToggle && filterContainer) {
				filterToggle.addEventListener('click', function() {
					filterContainer.classList.toggle('collapsed');
					filterToggle.classList.toggle('active');
				});
				} else {
					// console.log('警告: 筛选器元素不存在');
				}
				
				// 更新筛选条件数量标记
				function updateFilterCountBadge() {
					const activeFilters = getActiveFiltersCount();
					const filterCount = document.getElementById('filterCount');
					
					if (!filterCount) {
						// 如果元素不存在，则不进行任何操作
						// console.log('警告: filterCount 元素不存在');
						return;
					}
					
					if (activeFilters > 0) {
						filterCount.textContent = activeFilters;
						filterCount.classList.remove('d-none');
					} else {
						filterCount.classList.add('d-none');
					}
				}
				
				// 获取激活的筛选条件数量
				function getActiveFiltersCount() {
					let count = 0;
					
					// 类别筛选已移除，默认显示所有资源
					
					// 检查属性筛选
					if (sxid !== null) count++;
					
					// 检查类型筛选
					if (lxid !== null) count++;
					
					// 检查课程选择
					if (xkid !== null) count++;
					
					// 检查章节筛选
					if (zid && zid !== '0') count++;
					if (jid && jid !== '0') count++;
					if (xjid && xjid !== '0') count++;
					
					// 检查文件类型筛选
					if (fileTypeId !== null) count++;
					
					// 检查搜索框
					const searchInput = $("#searchInput");
					if (searchInput.length && searchInput.val().trim()) count++;
					
					return count;
				}
				
				// 监听筛选条件变化，实时更新指示器
				// 为所有筛选按钮添加点击后更新指示器的逻辑
				const originalSelectLb = window.selectlb;
				window.selectlb = function(e) {
					originalSelectLb(e);
					setTimeout(updateFilterCountBadge, 100);
				};
				
				const originalSelectSx = window.selectsx;
				window.selectsx = function(e) {
					originalSelectSx(e);
					setTimeout(updateFilterCountBadge, 100);
				};
				
				const originalSelectLx = window.selectlx;
				window.selectlx = function(e) {
					originalSelectLx(e);
					setTimeout(updateFilterCountBadge, 100);
				};
				
				const originalSelectXk = window.selectxk;
				window.selectxk = function(e) {
					originalSelectXk(e);
					setTimeout(updateFilterCountBadge, 100);
				};
				
				const originalSelectFileType = window.selectFileType;
				window.selectFileType = function(e) {
					originalSelectFileType(e);
					setTimeout(updateFilterCountBadge, 100);
				};
				
				const originalResetFilters = window.resetFilters;
				window.resetFilters = function() {
					originalResetFilters();
					setTimeout(updateFilterCountBadge, 100);
				};
				
				// 监听下拉选择框变化
				$("#z, #j, #xj").on('change', function() {
					setTimeout(updateFilterCountBadge, 100);
				});
				
				// 课程标签展开/收起功能
				const courseToggleBtn = document.getElementById('courseToggleBtn');
				const xkbox = document.getElementById('xkbox');
				
				// 添加安全检查，确保元素存在
				if (courseToggleBtn && xkbox) {
					courseToggleBtn.addEventListener('click', function() {
						// 切换xkbox的expanded类
						xkbox.classList.toggle('expanded');
						this.classList.toggle('expanded');
						
						// 更新按钮文本
						if (xkbox.classList.contains('expanded')) {
							this.innerHTML = '收起 <i class="fas fa-chevron-up"></i>';
						} else {
							this.innerHTML = '更多 <i class="fas fa-chevron-down"></i>';
						}
					});
				} else {
					// console.log('课程展开/收起功能所需的元素不存在，功能已禁用');
				}
			});
		</script>
		<script>
			// 修改课程选择相关函数
			function courseChange(e) {
				const courseId = $(e).val();
				if (courseId === '0') {
					xkid = null;
					// 禁用所有下级选择框
					$("#z, #j, #xj").prop('disabled', true).val('0');
					$("#z").html("<option value='0'>请选择章</option>");
					$("#j").html("<option value='0'>请选择节</option>");
					$("#xj").html("<option value='0'>请选择小节</option>");
				} else {
					xkid = courseId;
					// 启用章选择框
					$("#z").prop('disabled', false);
					
					// 获取章节数据
					let foundCourse = false;
					xkdata.forEach((item) => {
						if (item.id == xkid) {
							zdata = item.children;
							foundCourse = true;
						}
					});
					
					if (foundCourse && zdata && zdata.length > 0) {
						let zhtml = "<option value='0'>请选择章</option>";
						zdata.forEach((item) => {
							zhtml += `<option value="${item.id}">${item.name}</option>`;
						});
						$("#z").html(zhtml);
					} else {
						$("#z").html("<option value='0'>请选择章</option>");
						$("#z").prop('disabled', true);
					}
				}
				
				pageindex = 1;
				updateFilterCountBadge();
				getpages();
			}

			// 修改初始化函数，添加课程选择框的初始化
			$(function() {
				// ... existing code ...
				
				// 初始化课程选择框
				if (xkdata && xkdata.length > 0) {
					let courseHtml = "<option value='0'>请选择课程</option>";
					xkdata.map((item) => {
						courseHtml += "<option value='" + item.id + "'>" + item.name + "</option>";
					});
					$("#courseSelect").html(courseHtml);
				}
				
				// ... existing code ...
			});

			// 修改章节选择相关函数
			function zonchange(e) {
				$("#j").val('0');
				$("#xj").val('0');
				$("#xj").html("<option value='0'>请选择小节</option>");
				$("#xj").prop('disabled', true);
				
				zdata.map((item) => {
					if (item.id == $(e).val()) {
						jdata = item.children;
					}
				});
				zid = $(e).val();
				
				if (jdata && jdata.length > 0) {
					let jhtml = "<option value='0'>请选择节</option>";
					jdata.map((item) => {
						jhtml += "<option value='" + item.id + "'>" + item.name + "</option>";
					});
					$("#j").html(jhtml);
					$("#j").prop('disabled', false);
				} else {
					$("#j").html("<option value='0'>请选择节</option>");
					$("#j").prop('disabled', true);
				}
				getnewpages();
			}

			function jonchange(e) {
				$("#xj").val('0');
				jdata.map((item) => {
					if (item.id == $(e).val()) {
						xjdata = item.children;
					}
				});
				jid = $(e).val()
				
				if (xjdata && xjdata.length > 0) {
					let xjhtml = "<option value='0'>请选择小节</option>";
					xjdata.map((item) => {
						xjhtml += "<option value='" + item.id + "'>" + item.name + "</option>";
					});
					$("#xj").html(xjhtml);
					$("#xj").prop('disabled', false);
				} else {
					$("#xj").html("<option value='0'>请选择小节</option>");
					$("#xj").prop('disabled', true);
				}
				getnewpages();
			}

			function xjonchange(e) {
				xjid = $(e).val();
				getnewpages();
			}
		</script>
		<!-- 添加测试数据，用于验证筛选功能 -->
		<script>
			function testFileTypes() {
				// 模拟一些常见的文件类型数据
				const testTypes = ['mp4','pptx', 'ppt', 'docx', 'doc', 'mp3']; // PDF已移除
				
				// 调用更新函数以显示测试数据
				updateFileTypeFilters(testTypes);
				
				// 提示测试完成
				// console.log('文件类型筛选测试数据已加载');
			}
			
			// 页面加载后执行测试
			$(document).ready(function() {
				// 立即执行测试，确保显示所有文件类型
				testFileTypes();
			});
		</script>
		
		<!-- 移动端适配代码 -->
		<script>
			$(document).ready(function() {
				// 移动端菜单切换功能
				$("#menuToggle").on('click', function() {
					$("#menubox").toggleClass('expanded');
				});
				
				// 使用统一的updateFilterCountBadge函数，解决null引用错误
				if (typeof updateFilterCountBadge === 'function') {
					window.updateFilterCountBadge = updateFilterCountBadge;
						}
				
				// 处理窗口大小变化，确保在切换到桌面视图时菜单正常显示
				$(window).resize(function() {
					if ($(window).width() > 768) {
						$("#menubox").removeClass('expanded');
					}
				});
				
				// 在小屏幕上改进图片加载性能
				if ($(window).width() <= 768) {
					// 减少同时加载的图片数量，提高页面加载速度
					const lazyLoadThreshold = 100; // 减小可视区域外图片的预加载距离
					
					// 修改lazyLoad函数行为
					window.lazyLoad = function() {
						$('.topitem img').each(function() {
							if ($(this).attr('data-src') && !$(this).attr('src')) {
								let img = $(this);
								if (img.offset().top < $(window).height() + $(window).scrollTop() + lazyLoadThreshold) {
									img.attr('src', img.attr('data-src'));
									img.css('opacity', '0');
									img.on('load', function() {
										img.css('opacity', '1');
									});
								}
							}
						});
					};
				}
			});
		</script>
		<script>
			// 检测项目是否需要滚动效果
function checkItemOverflow() {
	// 统一检测函数，可用于任何选择器
	function checkElements(selector) {
		document.querySelectorAll(selector).forEach(item => {
			const span = item.querySelector('span');
			if (span) {
				// 计算实际需要滚动的程度
				const textWidth = span.scrollWidth;
				const containerWidth = item.clientWidth - 20; // 减去内边距
				
				if (textWidth > containerWidth) {
					span.classList.add('needs-scroll');
					// 设置自定义属性，用于样式控制和调试
					item.setAttribute('data-overflow', 'true');
					item.setAttribute('data-text-width', textWidth + 'px');
					item.setAttribute('data-container-width', containerWidth + 'px');
					// 当文本溢出时，强制设置左对齐以便更好地显示
					item.style.justifyContent = 'flex-start';
					span.style.textAlign = 'left';
				} else {
					span.classList.remove('needs-scroll');
					item.removeAttribute('data-overflow');
					item.removeAttribute('data-text-width');
					item.removeAttribute('data-container-width');
					// 当文本不溢出时，恢复居中对齐
					item.style.justifyContent = 'center';
					span.style.textAlign = 'center';
				}
			}
		});
	}
	
	// 检测章节项目
	checkElements('.chapter-grid .chapter-item');
	
	// 检测课程项目 - 包括course-grid中的和xkbox中的
	checkElements('.course-grid .course-item');
	checkElements('#xkbox span'); // 确保也检测xkbox中的元素
}
			
			// 当窗口大小变化时重新检测
			window.addEventListener('resize', function() {
				checkItemOverflow();
				console.log('窗口大小改变，重新检测溢出状态');
			});
			
			// 在DOM加载完成后检测
			document.addEventListener('DOMContentLoaded', function() {
	// 初始化时检测所有可能需要滚动的元素
	setTimeout(checkItemOverflow, 500);
	// 再次检测确保DOM完全渲染后能捕获所有元素
	setTimeout(checkItemOverflow, 1000);
	
	// 添加鼠标悬停事件监听
	document.addEventListener('mouseover', function(e) {
		// 检查是否悬停在章节或课程项上
		const chapterItem = e.target.closest('.chapter-item');
		const courseItem = e.target.closest('.course-item');
		const xkboxItem = e.target.closest('#xkbox span');
		
		// 如果悬停在任何这些项上，重新检测溢出状态
		if (chapterItem || courseItem || xkboxItem) {
			// 先运行检测
			checkItemOverflow();
			
			// 处理当前悬停的具体元素
			const currentItem = chapterItem || courseItem || xkboxItem;
			const span = currentItem.querySelector('span');
			
			// 确保span元素存在
			if (span) {
				// 检测文本是否需要滚动
				const textWidth = span.scrollWidth;
				const containerWidth = currentItem.clientWidth - 20; // 减去内边距
				
				if (textWidth > containerWidth) {
					// 添加滚动所需的类和样式
					span.classList.add('needs-scroll');
					currentItem.setAttribute('data-overflow', 'true');
					currentItem.style.justifyContent = 'flex-start';
					span.style.textAlign = 'left';
					
					// 应用立即显示的样式特性
					span.style.display = 'inline-block';
					span.style.whiteSpace = 'nowrap';
				}
			}
		}
	});
	
	// 监听窗口大小变化，重新检测溢出状态
	window.addEventListener('resize', function() {
		// 使用防抖动处理，避免频繁调用
		clearTimeout(window.resizeTimer);
		window.resizeTimer = setTimeout(checkItemOverflow, 200);
	});
});
			
			// 在课程和章节数据加载后重新检测
			const originalUpdateChapterFilter = window.updateChapterFilter;
			window.updateChapterFilter = function() {
				originalUpdateChapterFilter.apply(this, arguments);
				setTimeout(checkItemOverflow, 100);
			};
			
			const originalGetxkzj = window.getxkzj;
			window.getxkzj = function() {
				originalGetxkzj.apply(this, arguments);
				setTimeout(checkItemOverflow, 500);
			};

			// 添加搜索功能
			function searchResources() {
				const keyword = $("#searchInput").val().trim();
				if (!keyword) {
					cocoMessage.warning(1000, "请输入搜索关键词");
					return;
				}
				
				// 重置其他筛选条件
				resetFiltersExceptSearch();
				
				// 添加搜索中的提示
				$("#list").html('<div style="text-align:center; padding:30px;"><div class="loading-spinner" style="margin:0 auto;"></div><p style="margin-top:15px;">正在搜索，请稍候...</p></div>');
				
				// 发送搜索请求
				$.ajax({
					url: baseurl + "/web/course/search",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						categoryId: classid,
						pageSize: pagesize,
						pageNum: 1,
						keyword: keyword
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// 更新页面索引
							pageindex = 1;
							pages = res.data.pages || 0;
							
							// 显示搜索结果计数
							$("#resourceCount").text(res.data.total || 0);
							
							// 更新排序信息提示
							$("#sortInfo").text("（搜索结果：" + keyword + "）");
							
							// 渲染搜索结果
							let html = "";
							if (res.data.list && res.data.list.length > 0) {
								res.data.list.forEach((item, index) => {
									const delay = index % 10 * 50;
									
									// PDF已移除
									{
										html += '<div class="txtitem" style="animation-delay: '+delay+'ms;"><div class="topitem" onclick="inxx(this)" data-id="'+item.metaId+'"><div class="itemtype '+item.attachType+'">' +item.attachType+ '</div>'
										if(item.coverPath!=null){
											html+='<img data-src="'+baseurl+item.coverPath[0]+'" />'
										}
										html+='<div class="icobox">' +
										'<label><img class="zzico" src="img/fbr.png" /><span>'+item.author+'</span></label>' +
										'<label><img class="gklico" src="img/gkl.png" />'+item.view+'</label>' +
										'</div></div><div class="bottomitem">' +
										'<div class="title">'+item.title+'</div>' +
										'<div class="zz">'+item.introduction+'</div>'+
										''+
										'</div></div>'
									}
								});
								$("#list").html(html);
								
								// 应用动画效果
								setTimeout(function() {
									$('.txtitem').addClass('show');
								}, 100);
								
								// 启动图片懒加载
								lazyLoad();
								
								// 更新分页区域
								if (pages > 1) {
									updatePagination();
									$("#fyq").show();
								} else {
									$("#fyq").hide();
								}
							} else {
								// 没有找到结果
								$("#list").html('<div style="text-align:center; padding:50px;"><div style="font-size:60px; color:#ddd; margin-bottom:20px;"><i class="fas fa-search"></i></div><h3>未找到相关资源</h3><p>请尝试其他关键词</p></div>');
								$("#fyq").hide();
							}
						} else {
							// 搜索失败
							cocoMessage.error(2000, "搜索失败，请稍后重试");
							$("#list").html('<div style="text-align:center; padding:50px;"><div style="font-size:60px; color:#ddd; margin-bottom:20px;"><i class="fas fa-exclamation-triangle"></i></div><h3>搜索失败</h3><p>请稍后重试</p></div>');
						}
					},
					error: () => {
						cocoMessage.error(2000, "网络错误，请检查网络连接");
						$("#list").html('<div style="text-align:center; padding:50px;"><div style="font-size:60px; color:#ddd; margin-bottom:20px;"><i class="fas fa-wifi"></i></div><h3>网络错误</h3><p>请检查网络连接后重试</p></div>');
					}
				});
			}

			// 重置筛选条件但保留搜索
			function resetFiltersExceptSearch() {
				// 重置类别
				$("#qblb").addClass("tjactive");
				$("#qblbbox span").removeClass("tjactive");
				mtype = null;
				
				// 重置属性
				$("#qbsx").addClass("tjactive");
				$("#sxbox span").removeClass("tjactive");
				sxid = null;
				
				// 重置类型
				$("#qblx").addClass("tjactive");
				$("#lxbox span").removeClass("tjactive");
				lxid = null;
				
				// 重置文件类型
				$("#qbfiletype").addClass("tjactive");
				$("#filetypebox span").removeClass("tjactive");
				fileTypeId = null;
				
				// 重置课程和章节
				$("#courseSelect").val("0");
				$("#z").val("0").prop("disabled", true);
				$("#j").val("0").prop("disabled", true);
				$("#xj").val("0").prop("disabled", true);
				xkid = null;
				zid = null;
				jid = null;
				xjid = null;
				
				// 更新筛选计数
				updateFilterCountBadge();
			}
			
			// 添加一个新函数，用于完全重置所有筛选条件并刷新页面
			function resetAllFilters() {
				// 清空所有会话存储的筛选状态
				sessionStorage.removeItem('resourceFilterState');
				
				// 构建不带任何筛选参数的URL
				let resetUrl = window.location.pathname;
				
				// 只保留categoryId参数（如果有的话）
				const categoryId = classid || getUrlParam('categoryId');
				if (categoryId) {
					resetUrl += "?categoryId=" + categoryId;
				} else {
					resetUrl += "?";
				}
				
				// 强制将排序方向重置为默认的降序(最新在前)
				resetUrl += "&sortDesc=true";
				
				// 关闭高级筛选抽屉
				const advancedFilterBtn = document.querySelector('.filter-toggle.advanced');
				const filterContainer = document.querySelector('.filter-container');
				if (filterContainer && filterContainer.style.display === 'block') {
					filterContainer.style.display = 'none';
					if (advancedFilterBtn) advancedFilterBtn.classList.add('collapsed');
				}
				
				// 重置所有内存中的筛选变量
				mtype = null;      // 类别
				sxid = null;       // 属性
				lxid = null;       // 类型
				xkid = null;       // 课程
				zid = null;        // 章节
				jid = null;        // 节
				xjid = null;       // 小节
				fileTypeId = null; // 文件类型
				pageindex = 1;     // 页码重置为第一页
				sortDesc = true;   // 排序恢复默认
				
				// 清空搜索框
				$("#searchInput").val("");
				
				// 显示加载提示
				$("#list").html('<div style="text-align:center; padding:30px;"><div class="loading-spinner" style="margin:0 auto;"></div><p style="margin-top:15px;">正在重置筛选条件，请稍候...</p></div>');
				
				// 跳转到重置后的URL
				window.location.href = resetUrl;
				
				// 显示成功提示
				cocoMessage.success(1000, "已重置所有筛选条件");
			}

			// 为搜索框添加回车键搜索功能
			$(document).ready(function() {
				$("#searchInput").on("keypress", function(e) {
					if (e.which == 13) { // Enter键
						searchResources();
						return false; // 阻止表单提交
					}
				});
			});

			// 更新现有的resetFilters函数，使其调用新的resetAllFilters函数
			resetFilters = function() {
				// 直接调用完整的重置筛选函数
				resetAllFilters();
			}
			// ... existing code ...
		</script>
		<script>
			// 筛选条件展开/收起功能
			document.addEventListener('DOMContentLoaded', function() {
				// 初始化筛选器状态
				const filterContainer = document.querySelector('.filter-container');
				const filterToggle = document.querySelector('.filter-toggle');
				
				if (filterToggle && filterContainer) {
					filterToggle.addEventListener('click', function() {
						this.classList.toggle('collapsed');
						filterContainer.style.display = filterContainer.style.display === 'none' ? 'block' : 'none';
					});
				}

				// 初始化课程选择网格
				const courseSelect = document.getElementById('courseSelect');
				if (courseSelect) {
					const courseGrid = document.createElement('div');
					courseGrid.className = 'course-grid';
					
					// 从原有的select选项创建网格项
					Array.from(courseSelect.options).forEach(option => {
						if (option.value && option.value !== '0') {  // 跳过placeholder选项
							const courseItem = document.createElement('div');
							courseItem.className = 'course-item';
							courseItem.textContent = option.text;
							courseItem.dataset.value = option.value;
							courseItem.onclick = function() {
								// 移除其他项的active类
								document.querySelectorAll('.course-item').forEach(item => {
									item.classList.remove('active');
								});
								// 添加active类到当前项
								this.classList.add('active');
								// 更新原select的值
								courseSelect.value = this.dataset.value;
								// 触发change事件
								courseSelect.dispatchEvent(new Event('change'));
							};
							courseGrid.appendChild(courseItem);
						}
					});

					// 将网格插入到原select之前
					courseSelect.parentNode.insertBefore(courseGrid, courseSelect);
				}
			});
		</script>
		<script>
			// 获取激活的筛选条件数量
			function getActiveFiltersCount() {
				let count = 0;
				
				// 检查类别筛选
				if (mtype !== null) count++;
				
				// 检查属性筛选
				if (sxid !== null) count++;
				
				// 检查类型筛选
				if (lxid !== null) count++;
				
				// 检查课程选择
				if (xkid !== null) count++;
				
				// 检查章节筛选
				if (zid && zid !== '0') count++;
				if (jid && jid !== '0') count++;
				if (xjid && xjid !== '0') count++;
				
				// 检查文件类型筛选
				if (fileTypeId !== null) count++;
				
				// 检查搜索框
				const searchInput = $("#searchInput");
				if (searchInput.length && searchInput.val().trim()) count++;
				
				return count;
			}

			// 更新筛选条件数量指示器
			function updateFilterCountBadge() {
				const activeFilters = getActiveFiltersCount();
				const filterCount = $(".filter-count");
				
				if (filterCount.length) {
					if (activeFilters > 0) {
						filterCount.text(activeFilters);
						filterCount.show();
					} else {
						filterCount.hide();
					}
				}
			}

			// 添加事件监听器
			document.addEventListener('DOMContentLoaded', function() {
				// 初始化筛选条件数量指示器
				updateFilterCountBadge();
				
				// 监听课程选择变化
				document.querySelectorAll('.course-grid .course-item').forEach(item => {
					item.addEventListener('click', updateFilterCountBadge);
				});
				
				// 监听章节选择变化
				document.querySelectorAll('.chapter-grid .chapter-item').forEach(item => {
					item.addEventListener('click', updateFilterCountBadge);
				});
				
				// 监听文件类型筛选变化
				document.querySelectorAll('.file-type-filter .file-type').forEach(item => {
					item.addEventListener('click', updateFilterCountBadge);
				});
				
				// 监听搜索框输入
				const searchInput = document.querySelector('.search-input');
				if (searchInput) {
					searchInput.addEventListener('input', updateFilterCountBadge);
				}
				
				// 监听重置按钮点击
				const resetButton = document.querySelector('.reset-filters');
				if (resetButton) {
					resetButton.addEventListener('click', () => {
						setTimeout(updateFilterCountBadge, 0);
					});
				}
			});
		</script>
		<script>
			// 添加通用选项状态更新函数
			function updateOptionState(clickedOption, containerSelector) {
				// 获取点击的元素和容器
				const $option = $(clickedOption);
				const $container = $(containerSelector);
				
				// 获取当前元素的data-id值
				const optionId = $option.attr('data-id');
				
				// 检查当前元素是否是"全部"选项
				const isAllOption = optionId === '0' || optionId === '111' || optionId === 'all';
				
				// 记录操作类型，用于调试
				// console.log("选项操作：", isAllOption ? "切换到全部" : "选择具体选项", optionId);
				
				// 先完全重置所有选项的样式
				$container.find('.filter-option, span').removeClass('active tjactive').removeAttr('style');
				
				// 特殊处理文件类型标签，保持左边框
				if (containerSelector === '#filetypebox') {
					// 文件类型标签在恢复默认状态时保留左边框的颜色
					$container.find('span').each(function() {
						$(this).css('border-left-style', 'solid');
					});
				}
				
				// 给当前点击的选项添加active类
				$option.addClass('active tjactive');
			}
		</script>
		<script>
			// 移除这个初始化，使用fixAdvancedFilterButton代替
		</script>
		<script>
			// 移除这个重复的处理函数，只保留fixAdvancedFilterButton
		</script>
		<!-- 调试高级筛选抽屉功能 -->
		<script>
			// 直接强制修复高级筛选按钮
			function fixAdvancedFilterButton() {
				console.log('强制修复高级筛选按钮');
				
				// 设置内联点击处理函数代替事件绑定
				var advancedBtn = document.querySelector('.filter-toggle.advanced');
				if (advancedBtn) {
					// 清除所有现有的事件监听器
					var newBtn = advancedBtn.cloneNode(true);
					advancedBtn.parentNode.replaceChild(newBtn, advancedBtn);
					advancedBtn = newBtn;
					
					// 设置内联点击属性
					advancedBtn.setAttribute('onclick', 'toggleAdvancedFilter(event); return false;');
					
					// 确保样式可点击并设置初始为黑色
					advancedBtn.style.position = 'relative';
					advancedBtn.style.zIndex = '9999';
					advancedBtn.style.cursor = 'pointer';
					advancedBtn.style.pointerEvents = 'auto';
					advancedBtn.style.color = '#333333'; // 初始为黑色
					
					// 设置筛选按钮中的文本元素颜色
					var btnText = advancedBtn.querySelector('span');
					if (btnText) btnText.style.color = '#333333';
				}
				
				// 确保筛选容器初始状态为隐藏
				var filterContainers = document.querySelectorAll('.filter-container');
				filterContainers.forEach(function(container) {
					container.style.display = 'none';
				});
				
				// 添加内联点击方法到窗口上下文
				window.toggleAdvancedFilter = function(event) {
					event.preventDefault();
					event.stopPropagation();
					
					console.log('高级筛选按钮被点击 - 内联处理');
					
					var btn = document.querySelector('.filter-toggle.advanced');
					var container = btn.nextElementSibling;
					var btnText = btn.querySelector('span');
					
					if (container.style.display === 'none' || !container.style.display) {
						// 展开状态 - 显示筛选容器，设置为红色
						container.style.display = 'block';
						btn.classList.remove('collapsed');
						btn.style.color = '#d82121'; // 设置为红色
						if (btnText) btnText.style.color = '#d82121';
					} else {
						// 收起状态 - 隐藏筛选容器，设置为黑色
						container.style.display = 'none';
						btn.classList.add('collapsed');
						btn.style.color = '#333333'; // 设置为黑色
						if (btnText) btnText.style.color = '#333333';
					}
					
					return false;
				};
				
				// 防止筛选容器内点击事件冒泡
				var containers = document.querySelectorAll('.filter-container');
				containers.forEach(function(container) {
					container.addEventListener('click', function(e) {
						e.stopPropagation();
					});
				});
				
				// 添加全局点击处理，点击外部区域时关闭筛选
				document.addEventListener('click', function(e) {
					var container = document.querySelector('.filter-container');
					var btn = document.querySelector('.filter-toggle.advanced');
					
					if (container && container.style.display === 'block') {
						// 检查点击是否在高级筛选抽屉区域外
						var isOutsideFilterArea = !e.target.closest('.advanced-filter-drawer');
						
						if (isOutsideFilterArea) {
							container.style.display = 'none';
							btn.classList.add('collapsed');
							// 恢复黑色文字
							btn.style.color = '#333333';
							var btnText = btn.querySelector('span');
							if (btnText) btnText.style.color = '#333333';
						}
					}
				});
				
				console.log('高级筛选按钮修复完成 - 使用DOM API');
				
				// 初始化筛选计数标记
				if (typeof updateFilterCountBadge === 'function') {
					updateFilterCountBadge();
				}
			}
			
			// 页面加载和准备好后都尝试修复
			$(document).ready(fixAdvancedFilterButton);
			$(window).on('load', fixAdvancedFilterButton);
		</script>
		
		<!-- 修复搜索框样式的脚本 -->
		<script>
			$(document).ready(function() {
				// 确保搜索框和onlinelearning2.html完全一致
				// 首先移除所有现有的样式
				$('.ssview, .ssview input, .ssview img').removeAttr('style').removeClass('ssview-custom');
				$('style').each(function() {
					// 移除任何内联样式表中可能影响搜索栏的样式
					var styleContent = $(this).html();
					if(styleContent.includes('.ssview') || styleContent.includes('.topview_1 .ssview')) {
						// 不删除整个style标签，但可以标记这些规则为已废弃
						// 实际项目中可以考虑更精确的替换
					}
				});
				
				// 确保父级容器样式正确
				$('.topview_1').css({
					'display': 'flex',
					'align-items': 'center',
					'justify-content': 'space-between'
				});
				
				$('.loginview').css({
					'display': 'flex',
					'align-items': 'center'
				});
				
				// 精确匹配onlinelearning2.html的搜索框样式
				$('.ssview').css({
					'background': '#FFFFFF',
					'display': 'flex',
					'align-items': 'center',
					'padding-left': '0.520833rem',
					'padding-right': '0.520833rem',
					'border': '1px solid #cc4347',
					'border-radius': '5px',
					'overflow': 'hidden',
					'margin-right': '1.302083rem',
					'height': 'auto',
					'width': 'auto',
					'min-width': '180px',
					'max-width': '250px',
					'box-shadow': 'none',
					'position': 'relative'
				});
				
				$('.ssview img').css({
					'padding-right': '0.520833rem',
					'width': '0.9375rem',
					'height': 'auto',
					'display': 'block',
					'opacity': '1',
					'margin-left': '0'
				});
				
				$('.ssview input').css({
					'flex': '1',
					'border': 'none',
					'padding': '0',
					'margin': '0',
					'font-size': '0.833333rem',
					'color': '#333',
					'background': 'transparent',
					'outline': 'none',
					'height': '1.5rem',
					'line-height': '1.5rem',
					'width': 'calc(100% - 1.5rem)'
				});
				
				// 移除任何搜索按钮相关的样式
				$('.header-search-btn').remove();
				
				// 确保点击搜索框的事件监听器正常工作
				$(".ssview").off('click').on('click', function(){
					window.location.href = 'searchall.html';
				});
			});
		</script>
	</body>
</html>
