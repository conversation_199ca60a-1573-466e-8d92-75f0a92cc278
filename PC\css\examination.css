body{
	background: #fbfbfb;
}
.context{
	background: #FFFFFF;
	padding-top: 3.125rem;
	padding-bottom: 3.125rem;
	width: 66.666666rem;
	margin: 0px auto;
	margin-bottom: 3.125rem;
	min-height: 24.5rem;
}
.contexttopview{
	display: flex;
	justify-content: space-between;
	align-items: center;
	border-bottom: 1px solid #dedede;
	padding-bottom: 1.5625rem;
}
.ctitle{
	font-size: 0.9375rem;
	color: #c00714;
	font-weight: bold;
}

.inputview{
	border: 1px solid #c00714;
	border-radius: 5px;
	display: flex;
	overflow: hidden;
	height: 1.5625rem;
}
.inputview input{
	width: 6.770833rem;
	border: none;
	outline: none;
	padding-left: 0.520833rem;
	padding-right: 0.520833rem;
	font-size: 0.833333rem;
}
.inputview span{
	background: #c00714;
	color: #FFFFFF;
	font-size: 0.833333rem;
	line-height: 1.5625rem;
	width: 3.125rem;
	text-align: center;
	cursor: pointer;
}
.titlesbox{
	display: flex;
	align-items: flex-start;
	margin-top: 1.5625rem;
	margin-bottom: 2.604166rem;
}
.titlesl{
	font-size: 0.9375rem;
	color: #999999;
	margin-right: 1.5625rem;
	line-height: 1.5625rem;
}
.titlesr{
	color: #333333;
	font-size: 0.9375rem;
	padding-left: 0.520833rem;
	padding-right: 0.520833rem;
	cursor: pointer;
	line-height: 1.5625rem;
}
#xklist{
	width: calc(100% - 4rem);
	display: flex;
	flex-wrap: wrap;
}
.titlesr:hover{
	font-weight: bold;
	color: #c00714;
}
.tiactive{
	font-weight: bold;
	color: #c00714;
}
.topbarview{
	height: 1.5625rem;
	line-height: 1.5625rem;
	background-image: linear-gradient(to right, #c00714, #c00714, #ffc156);
	padding-left: 0.520833rem;
	padding-right: 0.520833rem;
	display: flex;
	align-items: center;
}
.topbarview span{
	font-size: 0.833333rem;
	color: #FFFFFF;
}
.topbarview img{
	width: 1.041666rem;
	padding-left: 0.520833rem;
	padding-right: 0.520833rem;
}
.zxkhbox .item{
	display: flex;
	align-items: center;
	height: 4.6875rem;
	border-bottom: 1px dashed #dedede;
	padding-left: 0.78125rem;
	padding-right: 0.78125rem;
}
.zxkhbox .item:last-child{
	border-bottom: none;
}
.leftitem{
	width: 14.0625rem;
	display: flex;
	align-items: center;
	font-size: 0.833333rem;
	color: #f8b967;
}
.leftitem img{
	width: 0.9375rem;
	margin-right: 0.260416rem;
}
.centeritem{
	width: calc(100% - 14.0625rem - 18.229166rem);
	font-size: 0.9375rem;
	color: #333333;
	overflow: hidden;
	            text-overflow:ellipsis;
	            white-space: nowrap;
}
.rightitem{
	display: flex;
	justify-content: space-between;
	align-items: center;
	width: 18.229166rem;
}
.ksnum{
	display: flex;
	align-items: center;
	font-size: 0.729166rem;
	color: #cecece;
	padding-left: 1.041666rem;
}
.ksnum img{
	width: 1.041666rem;
	margin-right: 0.260416rem;
}
.ksdtbtn{
	width: 6.25rem;
	height: 1.5625rem;
	background: #c00714;
	text-align: center;
	line-height: 1.5625rem;
	color: #FFFFFF;
	font-size: 0.833333rem;
	border-radius: 5px;
	cursor: pointer;
}
.ksdtbtn a{
	color: #FFFFFF;
}
.actidt{
	width: 6.25rem;
	height: 1.5625rem;
	background: #999999;
	text-align: center;
	line-height: 1.5625rem;
	color: #FFFFFF;
	font-size: 0.833333rem;
	border-radius: 5px;
	cursor: pointer;
}