<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-个人中心</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/nav-enhancement.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/wangEditor.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>
		<style>
			.mysj {
				text-align: center;
				padding: 40px 20px;
				color: #666;
				font-size: 16px;
				background-color: #f9f9f9;
				border-radius: 5px;
				margin: 20px 0;
				border: 1px dashed #ddd;
			}
			/* 导航菜单容器 */
			.leftitembox {
				background: transparent;
				padding: 20px 0;
				position: relative;
				z-index: 2;
				min-height: 450px; /* 确保导航栏最小高度一致 */
				box-sizing: border-box;
			}
			
			.leftitembox::before {
				content: '';
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 85%;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.15), transparent);
			}
			
			/* 导航菜单项 - 现代化设计与图标，移除slideIn动画 */
			.leftitem {
				height: auto !important;
				padding: 16px 24px !important;
				display: flex !important;
				justify-content: flex-start !important;
				align-items: center !important;
				font-size: 15px !important;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif !important;
				font-weight: 500 !important;
				color: #4a5568 !important;
				border-bottom: none !important;
				text-decoration: none !important;
				transition: color 0.2s ease, background-color 0.2s ease !important;
				position: relative !important;
				margin: 4px 16px !important;
				border-radius: 12px !important;
				background: transparent !important;
				overflow: hidden !important;
				min-height: 48px !important;
				box-sizing: border-box !important;
				/* 移除slideIn动画，导航项立即显示 */
				animation: none !important;
				opacity: 1 !important;
				transform: translateX(0) !important;
				animation-delay: 0s !important;
			}
			
			/* 为不同菜单项添加对应的SVG图标 */
			.leftitem::before {
				content: '';
				width: 20px;
				height: 20px;
				margin-right: 12px;
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				opacity: 0.7;
				transition: opacity 0.2s ease;
				flex-shrink: 0;
			}
			
			/* 个人信息图标 */
			.leftitem[href*="userinfo"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
			}
			
			/* 学习任务图标 */
			.leftitem[href*="studentasks"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm-1 16H9V7h9v14z'/%3E%3C/svg%3E");
			}
			
			/* 学习路径图标 - 激活状态 */
			.leftitem.activeleftitem::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M2 20h20v-4H2v4zm2-3h2v2H4v-2zM2 4v4h20V4H2zm4 3H4V5h2v2zm-4 7h20v-4H2v4zm2-3h2v2H4v-2z'/%3E%3C/svg%3E") !important;
			}
			
			/* 非激活状态的学习路径图标 */
			.leftitem:not(.activeleftitem)[href*="learningrecords"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M2 20h20v-4H2v4zm2-3h2v2H4v-2zM2 4v4h20V4H2zm4 3H4V5h2v2zm-4 7h20v-4H2v4zm2-3h2v2H4v-2z'/%3E%3C/svg%3E");
			}
			
			/* 考试成绩图标 */
			.leftitem[href*="achievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E");
			}
			
			/* 发布心声图标 */
			.leftitem[href*="voice"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z'/%3E%3C/svg%3E");
			}
			
			/* 菜单项悬浮态 */
			.leftitem:hover {
				background: linear-gradient(135deg, rgba(220, 53, 69, 0.06), rgba(231, 76, 60, 0.08)) !important;
				color: #dc3545 !important;
			}
			
			.leftitem:hover::before {
				opacity: 1;
			}
			
			/* 激活状态的菜单项 - 统一标准双色渐变背景 */
			.leftitem.activeleftitem,
			a.leftitem.activeleftitem,
			.leftitembox .leftitem.activeleftitem,
			#teambox .leftitem.activeleftitem {
				font-weight: 700 !important;
				color: white !important;
				background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
				background-size: 200% 200% !important;
				animation: activeGradient 3s ease infinite !important;
				box-shadow: 0 4px 16px rgba(220, 53, 69, 0.25) !important;
				border-radius: 12px !important;
				opacity: 1 !important;
				transform: translateX(0) !important;
			}
			
			@keyframes activeGradient {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.leftitem.activeleftitem::before,
			a.leftitem.activeleftitem::before,
			.leftitembox .leftitem.activeleftitem::before,
			#teambox .leftitem.activeleftitem::before {
				opacity: 1 !important;
				filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
			}
			
			/* 移除所有导航项的动画延迟 */
			.leftitem:nth-child(1),
			.leftitem:nth-child(2), 
			.leftitem:nth-child(3), 
			.leftitem:nth-child(4), 
			.leftitem:nth-child(5), 
			.leftitem:nth-child(6), 
			.leftitem:nth-child(7) { 
				animation: none !important;
				animation-delay: 0s !important;
				opacity: 1 !important;
				transform: translateX(0) !important;
			}
			
			/* 学科选择器美化 - 极简风格 */
			.xxjlselectbox {
				height: auto !important;
				padding: 16px 0 !important; /* 只保留上下内边距 */
				border-bottom: 1px solid rgba(0,0,0,0.06) !important; /* 极淡的分割线 */
				background: transparent !important; /* 透明背景 */
				border-radius: 0 !important; /* 移除圆角 */
				margin-bottom: 0 !important; /* 移除间距 */
				box-shadow: none !important; /* 移除阴影 */
			}
			
			.xxjlselectbox select {
				height: 40px !important;
				padding: 8px 12px !important;
				border: 1px solid rgba(0,0,0,0.08) !important; /* 极淡的边框 */
				border-radius: 6px !important;
				background: white !important;
				font-size: 13px !important;
				color: #333333 !important; /* 深色文字 */
				min-width: 180px !important;
				outline: none !important;
				transition: all 0.2s ease !important;
				cursor: pointer !important;
			}
			
			.xxjlselectbox select:hover {
				border-color: rgba(220, 53, 69, 0.3) !important; /* 悬浮时淡红色边框 */
				box-shadow: none !important; /* 移除阴影 */
			}
			
			.xxjlselectbox select:focus {
				border-color: rgba(220, 53, 69, 0.5) !important; /* 聚焦时稍深的红色边框 */
				box-shadow: none !important; /* 移除阴影 */
			}
			
			/* 学习记录列表美化 - 表格式设计 */
			.listbox {
				background: transparent !important;
				border-radius: 0 !important;
				padding: 0 !important;
				box-shadow: none !important;
				margin-bottom: 20px !important;
			}
			
			/* 表头样式 */
			.table-header {
				display: flex !important;
				align-items: center !important;
				padding: 16px 8px !important;
				background: linear-gradient(135deg, #f8f9fa, #ffffff) !important;
				border-bottom: 2px solid #e9ecef !important;
				font-weight: 600 !important;
				color: #495057 !important;
				font-size: 15px !important; /* 增大表头字体 */
				position: sticky !important;
				top: 0 !important;
				z-index: 10 !important;
			}
			
			.table-header .col-index {
				width: 60px !important;
				text-align: center !important;
				flex-shrink: 0 !important;
			}
			
			.table-header .col-content {
				flex: 1 !important;
				text-align: left !important;
				margin-left: 12px !important;
			}
			
			.table-header .col-category {
				width: 200px !important;
				text-align: center !important;
				flex-shrink: 0 !important;
			}
			
			.table-header .col-progress {
				width: 140px !important;
				text-align: center !important;
				flex-shrink: 0 !important;
			}
			
			.table-header .col-action {
				width: 90px !important; /* 增加宽度适应中文 */
				text-align: center !important;
				flex-shrink: 0 !important;
			}
			
			/* 学习记录项重新设计 - 表格行风格 */
			.jlitem1, .jlitem2 {
				background: transparent !important;
				border: none !important;
				border-radius: 0 !important;
				padding: 16px 8px !important; /* 增加内边距 */
				margin-bottom: 0 !important;
				box-shadow: none !important;
				transition: all 0.2s ease !important;
				display: flex !important;
				align-items: center !important;
				position: relative !important;
				overflow: hidden !important;
				min-height: 30px !important; /* 增加行高 */
				border-bottom: 1px solid rgba(0,0,0,0.04) !important;
			}
			
			/* 左侧彩色指示器 */
			/* .jlitem1::before, .jlitem2::before {
				content: '';
				position: absolute;
				left: 0;
				top: 0;
				bottom: 0;
				width: 3px;
				background: linear-gradient(135deg, #dc3545, #e74c3c);
				opacity: 0.8;
				transition: all 0.2s ease;
			} */
			
			/* 悬浮效果 */
			.jlitem1:hover, .jlitem2:hover {
				background: rgba(248, 249, 250, 0.8) !important;
				transform: none !important;
			}
			
			.jlitem1:hover::before, .jlitem2:hover::before {
				opacity: 1;
				width: 4px;
			}
			
			.jlitem1:last-child, .jlitem2:last-child {
				border-bottom: none !important;
			}
			
			/* 序号列 */
			.item-index {
				width: 60px !important;
				text-align: center !important;
				font-size: 16px !important; /* 增大序号字体 */
				font-weight: 600 !important;
				color: #6c757d !important;
				flex-shrink: 0 !important;
			}
			
			/* 内容区域重新布局 */
			.cc, .cc2 {
				flex: 1 !important;
				padding: 0 !important;
				margin-right: 0 !important;
				margin-left: 12px !important;
				display: block !important;
			}
			
			.cc > div:first-child, .cc2 > div:first-child {
				font-size: 16px !important; /* 增大标题字体 */
				font-weight: 600 !important;
				color: #1a1a1a !important;
				line-height: 1.4 !important;
				display: -webkit-box !important;
				-webkit-line-clamp: 1 !important;
				-webkit-box-orient: vertical !important;
				overflow: hidden !important;
				text-overflow: ellipsis !important;
				margin: 0 !important;
			}
			
			/* 隐藏其他div，只显示标题 */
			.cc > div:nth-child(2), .cc2 > div:nth-child(2),
			.cc > div:nth-child(3), .cc2 > div:nth-child(3) {
				display: none !important;
			}
			
			/* 分类信息列 */
			.item-category {
				width: 200px !important;
				text-align: center !important;
				font-size: 13px !important; /* 增大分类字体 */
				color: #666666 !important;
				flex-shrink: 0 !important;
				padding: 0 8px !important;
			}
			
			.item-category label {
				font-size: 13px !important; /* 增大标签字体 */
				color: #666666 !important;
				margin: 0 !important;
				padding: 0 !important;
				background: none !important;
				border: none !important;
				white-space: nowrap !important;
				overflow: hidden !important;
				text-overflow: ellipsis !important;
				display: block !important;
			}
			
			/* 进度信息列 */
			.item-progress {
				width: 140px !important;
				text-align: center !important;
				font-size: 13px !important; /* 增大进度字体 */
				color: #28a745 !important;
				font-weight: 500 !important;
				flex-shrink: 0 !important;
				padding: 0 8px !important;
				white-space: nowrap !important;
				overflow: hidden !important;
				text-overflow: ellipsis !important;
			}
			
			/* 操作按钮区域 - 中文按钮设计 */
			.rr {
				width: 90px !important; /* 增加宽度适应中文 */
				display: flex !important;
				justify-content: center !important;
				align-items: center !important;
				flex-shrink: 0 !important;
			}
			
			.rr a, .rr div {
				width: 80px !important; /* 增加按钮宽度 */
				height: 32px !important; /* 调整按钮高度 */
				border-radius: 16px !important; /* 改为圆角矩形 */
				background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
				color: white !important;
				text-decoration: none !important;
				font-size: 12px !important; /* 适合中文的字体大小 */
				font-weight: 500 !important;
				display: flex !important;
				align-items: center !important;
				justify-content: center !important;
				transition: all 0.3s ease !important;
				border: none !important;
				cursor: pointer !important;
				box-shadow: 0 2px 8px rgba(220, 53, 69, 0.3) !important;
			}
			
			.rr a:hover, .rr div:hover {
				transform: scale(1.05) !important; /* 减少缩放比例 */
				box-shadow: 0 4px 16px rgba(220, 53, 69, 0.4) !important;
				background: linear-gradient(135deg, #c82333, #dc3545) !important;
			}
			
			/* 移除播放图标，显示中文文字 */
			.rr a::before, .rr div::before {
				content: none !important; /* 移除图标 */
			}
			
			/* 显示中文文字 */
			.rr a::after, .rr div::after {
				content: '继续学习' !important;
				font-size: 12px !important;
				font-weight: 500 !important;
			}
			
			.rr a span, .rr div span {
				display: none !important;
			}
			
			/* 隐藏多余的label */
			.rr label {
				display: none !important;
			}
			
			/* 不同学习类型的颜色指示 */
			.jlitem1[data-type="在线学习"]::before {
				background: linear-gradient(135deg, #ff6b35, #f7931e) !important;
			}
			
			.jlitem1[data-type="心声社区"]::before {
				background: linear-gradient(135deg, #28a745, #20c997) !important;
			}
			
			.jlitem1[data-type="主席足迹"]::before {
				background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
			}
			
			.jlitem1[data-type="虚仿实验空间"]::before {
				background: linear-gradient(135deg, #17a2b8, #20c997) !important;
			}
			
			.jlitem1[data-type="红色书籍"]::before {
				background: linear-gradient(135deg, #6f42c1, #e83e8c) !important;
			}
			
			/* 按钮根据类型调整颜色 */
			.jlitem1[data-type="在线学习"] .rr a,
			.jlitem1[data-type="在线学习"] .rr div {
				background: linear-gradient(135deg, #ff6b35, #f7931e) !important;
				box-shadow: 0 2px 8px rgba(255, 107, 53, 0.3) !important;
			}
			
			.jlitem1[data-type="在线学习"] .rr a:hover,
			.jlitem1[data-type="在线学习"] .rr div:hover {
				background: linear-gradient(135deg, #e55a2b, #e8850f) !important;
				box-shadow: 0 4px 16px rgba(255, 107, 53, 0.4) !important;
			}
			
			.jlitem1[data-type="心声社区"] .rr a,
			.jlitem1[data-type="心声社区"] .rr div {
				background: linear-gradient(135deg, #28a745, #20c997) !important;
				box-shadow: 0 2px 8px rgba(40, 167, 69, 0.3) !important;
			}
			
			.jlitem1[data-type="心声社区"] .rr a:hover,
			.jlitem1[data-type="心声社区"] .rr div:hover {
				background: linear-gradient(135deg, #1e7e34, #17a2b8) !important;
				box-shadow: 0 4px 16px rgba(40, 167, 69, 0.4) !important;
			}
			
			.jlitem1[data-type="主席足迹"] .rr a,
			.jlitem1[data-type="主席足迹"] .rr div {
				background: linear-gradient(135deg, #ffc107, #fd7e14) !important;
				box-shadow: 0 2px 8px rgba(255, 193, 7, 0.3) !important;
			}
			
			.jlitem1[data-type="主席足迹"] .rr a:hover,
			.jlitem1[data-type="主席足迹"] .rr div:hover {
				background: linear-gradient(135deg, #e0a800, #dc3545) !important;
				box-shadow: 0 4px 16px rgba(255, 193, 7, 0.4) !important;
			}
			
			.jlitem1[data-type="虚仿实验空间"] .rr a,
			.jlitem1[data-type="虚仿实验空间"] .rr div {
				background: linear-gradient(135deg, #17a2b8, #20c997) !important;
				box-shadow: 0 2px 8px rgba(23, 162, 184, 0.3) !important;
			}
			
			.jlitem1[data-type="虚仿实验空间"] .rr a:hover,
			.jlitem1[data-type="虚仿实验空间"] .rr div:hover {
				background: linear-gradient(135deg, #117a8b, #28a745) !important;
				box-shadow: 0 4px 16px rgba(23, 162, 184, 0.4) !important;
			}
			
			.jlitem1[data-type="红色书籍"] .rr a,
			.jlitem1[data-type="红色书籍"] .rr div {
				background: linear-gradient(135deg, #6f42c1, #e83e8c) !important;
				box-shadow: 0 2px 8px rgba(111, 66, 193, 0.3) !important;
			}
			
			.jlitem1[data-type="红色书籍"] .rr a:hover,
			.jlitem1[data-type="红色书籍"] .rr div:hover {
				background: linear-gradient(135deg, #59359a, #dc3545) !important;
				box-shadow: 0 4px 16px rgba(111, 66, 193, 0.4) !important;
			}
			
			/* 空状态样式优化 */
			.mysj {
				background: linear-gradient(135deg, #f8f9fa, #ffffff) !important;
				border: 2px dashed #dee2e6 !important;
				border-radius: 15px !important;
				padding: 60px 40px !important;
				text-align: center !important;
				color: #6c757d !important;
				font-size: 16px !important;
				position: relative !important;
				overflow: hidden !important;
			}
			
			.mysj::before {
				content: '📚';
				font-size: 48px;
				display: block;
				margin-bottom: 15px;
				opacity: 0.6;
			}
			
			/* 移动端适配 - 保持表格结构 */
			@media (max-width: 768px) {
				.table-header {
					padding: 12px 4px !important;
					font-size: 13px !important; /* 增大移动端表头字体 */
				}
				
				.table-header .col-index {
					width: 40px !important;
				}
				
				.table-header .col-category {
					width: 120px !important;
				}
				
				.table-header .col-progress {
					width: 100px !important;
				}
				
				.table-header .col-action {
					width: 70px !important; /* 增加移动端按钮列宽度 */
				}
				
				.jlitem1, .jlitem2 {
					padding: 12px 4px !important; /* 增加移动端内边距 */
					min-height: 56px !important; /* 增加移动端行高 */
				}
				
				.item-index {
					width: 40px !important;
					font-size: 14px !important; /* 增大移动端序号字体 */
				}
				
				.cc, .cc2 {
					margin-left: 8px !important;
				}
				
				.cc > div:first-child, .cc2 > div:first-child {
					font-size: 14px !important; /* 增大移动端标题字体 */
					line-height: 1.3 !important;
				}
				
				.item-category {
					width: 120px !important;
					font-size: 11px !important; /* 增大移动端分类字体 */
					padding: 0 4px !important;
				}
				
				.item-category label {
					font-size: 11px !important; /* 增大移动端标签字体 */
				}
				
				.item-progress {
					width: 100px !important;
					font-size: 11px !important; /* 增大移动端进度字体 */
					padding: 0 4px !important;
				}
				
				.rr {
					width: 70px !important; /* 增加移动端按钮列宽度 */
				}
				
				.rr a, .rr div {
					width: 64px !important; /* 增加移动端按钮宽度适应中文 */
					height: 26px !important; /* 调整移动端按钮高度 */
					font-size: 10px !important; /* 适合移动端的字体大小 */
					border-radius: 13px !important; /* 保持圆角比例 */
				}
				
				.rr a::after, .rr div::after {
					content: '继续学习' !important;
					font-size: 10px !important; /* 移动端中文字体大小 */
					font-weight: 500 !important;
				}
				
				.xxjlselectbox {
					padding: 12px 0 !important;
				}
				
				.xxjlselectbox select {
					width: 100% !important;
					min-width: auto !important;
					height: 36px !important;
					font-size: 13px !important; /* 增大移动端选择器字体 */
				}
				
				.listbox {
					padding: 0 !important;
					margin-bottom: 12px !important;
				}
				
				/* 移动端隐藏点击功能，保持原有按钮操作 */
				.jlitem1, .jlitem2 {
					cursor: default !important;
				}
			}
		</style>
		
		<style>
			/* 分页组件美化 - 超级简洁设计 */
			.fybox {
				display: flex !important;
				justify-content: center !important;
				align-items: center !important;
				gap: 4px !important;
				padding: 24px 0 !important;
				background: transparent !important;
				border: none !important; /* 移除所有边框 */
				border-radius: 0 !important;
				box-shadow: none !important;
				margin-top: 16px !important;
				overflow-x: auto !important;
				white-space: nowrap !important;
			}
			
			.fybox span, .fybox label {
				display: flex !important;
				align-items: center !important;
				justify-content: center !important;
				min-width: 32px !important;
				height: 32px !important;
				padding: 6px 8px !important;
				border-radius: 16px !important;
				font-size: 12px !important;
				font-weight: 500 !important;
				cursor: pointer !important;
				transition: all 0.2s ease !important;
				border: none !important;
				background: transparent !important;
				color: #666666 !important;
				text-decoration: none !important;
				flex-shrink: 0 !important;
			}
			
			.fybox span:hover, .fybox label:hover {
				background: rgba(0,0,0,0.05) !important;
				color: #333333 !important;
				transform: none !important;
			}
			
			.fybox .actinum {
				background: #dc3545 !important;
				color: white !important;
				font-weight: 600 !important;
			}
			
			.fybox .actinum:hover {
				background: #c82333 !important;
				color: white !important;
			}
			
			.fybox .num {
				display: flex !important;
				gap: 2px !important;
				flex-wrap: nowrap !important;
				overflow-x: auto !important;
				max-width: 400px !important; /* 限制最大宽度 */
				scrollbar-width: none !important; /* Firefox隐藏滚动条 */
			}
			
			/* 完全隐藏滚动条 */
			.fybox .num::-webkit-scrollbar {
				display: none !important;
			}
			
			/* 首页尾页按钮 - 更淡的样式 */
			.fybox #sy, .fybox #wy {
				color: #999999 !important;
			}
			
			.fybox #sy:hover, .fybox #wy:hover {
				background: rgba(0,0,0,0.05) !important;
				color: #666666 !important;
			}
			
			/* 移动端优化 */
			@media (max-width: 768px) {
				.fybox {
					gap: 2px !important;
					padding: 20px 0 !important;
				}
				
				.fybox span, .fybox label {
					min-width: 28px !important;
					height: 28px !important;
					padding: 4px 6px !important;
					font-size: 11px !important;
				}
				
				.fybox .num {
					max-width: 80vw !important;
				}
			}
		</style>
	</head>
	<body class="index">
		<div class="tcbox" id="tcbox">
			
			<div class="topviewsss">
				<a onclick="closetc()">关闭</a>
				<label id="pdfname"></label>
			</div>
			<div class="panel-body pdfbox">
			    <a id="pdf" class="media" href=""></a>  
			</div>
		</div>
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a href="studentasks.html" class="leftitem">学习任务</a>
						<a class="leftitem activeleftitem">学习路径</a>
						<a href="achievement.html" class="leftitem">考试成绩</a>
						<a href="releaseyourvoice.html" class="leftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="righttopview2" id="flbox">
					</div>
					<div class="xxjlselectbox" id="xkselect">
						<select id="xueke" onchange="xuekechange()">
						</select>
					</div>
					<div class="listbox" id="listview">

					</div>
					<div class="fybox" id="fyq">
						<span id="sy">首页</span>
						<span id="syy">上一页</span>
						<div class="num" id="num">
						</div>
						<span id="xyy">下一页</span>
						<span id="wy">尾页</span>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let pageindex = 1
			let pagesize = 10
			let pages = 1

			let classid = null //分类ID
			let xkid = null //学科ID
			let userinfo = sessionStorage.getItem("userinfo")
			

			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getfooterlink()
				getfllist()
				getallxklist()
			})
			function xuekechange(){
				xkid = $("#xueke").val()
				getjllist()
			}
			function getallxklist(){
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let xuekehtml = "<option value=''>请选择学科</option>"
							res.data.map((item)=>{
								xuekehtml+='<option value="'+item.id+'">'+item.name+'</option>'
							})
							$("#xueke").html(xuekehtml)
						}
					}
				})
			}
			function getfllist() {
				$.ajax({
					url: baseurl + "/category/xinshengcategory",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data)
							let html = ''
							classid = res.data[0].id
							res.data.map((item,index) => {
								if(index == 0){
									html += '<label class="accccc" onclick="selectfl(this)" data-id="' + item.id + '">' + item
										.name + '</label>'
									if(item.name=='在线学习'){
										$("#xkselect").attr("style","display: flex")
									}else{
										$("#xkselect").hide()
									}
								}else{
									html += '<label onclick="selectfl(this)" data-id="' + item.id + '">' + item
										.name + '</label>'
								}
							})
							$("#flbox").html(html)
							getjllist()
						}
					}
				})
			}

			function selectfl(item) {
				let alll = $("#flbox label")
				for (let i = 0; i < alll.length; i++) {
					$(alll[i]).attr("class", "")
				}
				$(item).attr("class", 'accccc')
				classid = $(item).attr("data-id")
				pageindex = 1
				xkid = null
				getjllist()
				if($(item).html()=='在线学习'){
					$("#xkselect").attr("style","display: flex")
				}else{
					$("#xkselect").hide()
				}
			}

			function closetc(){
				$("#tcbox").hide()
				tiaozhuan()
			}

			function showpdf(pdf){
				$.ajax({
					url: baseurl + "/course/meta/"+$(pdf).attr("data-id"),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							$("#pdfname").html(res.data.cmsResourcesCourseMetaList[0].attachName)
							$("#tcbox").show()
							$("#pdf").attr("href",baseurl + res.data.cmsResourcesCourseMetaList[0].attachPath)
							$('a.media').media()
							time1 = Date.now()
							xkidsss = res.data.projectId
							infoid = $(pdf).attr("data-id")
							pflid = res.data.cmsCategory.parentId
						}
					}
				})
			}
			let time1 = null
			let pflid = null
			let xkidsss = null
			let infoid = null
			function tiaozhuan() {
				if(JSON.parse(userinfo).roleName == '学生'){
					let time2 = Date.now()
					let value = time2 - time1
					var days = parseInt(value / (1000 * 60 * 60 * 24))
					var hours = parseInt((value % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
					var minutes = parseInt((value % (1000 * 60 * 60)) / (1000 * 60))
					var seconds = Math.floor((value % (1000 * 60)) / 1000)
					let json = {
						infoId: infoid, //信息id
						categoryId: pflid, //所属分类id
						totalInfo: "1", //总时长，总页数
						positioning: days+"天"+hours+"时"+minutes+"分"+seconds+"秒", //学习了多久，多少页    
						progress: "", //进度 百分比
						type: '在线学习2',
						learningTime: value,
						sectionId: xkidsss//学科ID
					}
					window.localStorage.setItem("jilu",JSON.stringify(json))
					if(window.localStorage.getItem("jilu")){
						$.ajax({
							url: baseurl + "/study/record/add",
							type: 'post',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: window.localStorage.getItem("jilu"),
							dataType: 'json',
							success: (res) => {
								window.localStorage.clear()
								getjllist()
							}
						})
					}
				}
			}
			function getjllist() {
				$.ajax({
					url: baseurl + "/study/record/studenttecord",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: pageindex,
						pageSize: pagesize,
						categoryId: classid,
						sectionId: xkid
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							console.log("学习记录数据:", res.data);  // 添加调试日志
							let html = '';
							if (!res.data || !res.data.list || res.data.list.length === 0) {
								$("#listview").html("<div class='mysj'>没有查询到记录...</div>");
								$("#fyq").hide();
								return;
							}
							
							// 添加表头
							html += '<div class="table-header">' +
								'<div class="col-index">序号</div>' +
								'<div class="col-content">学习内容</div>' +
								'<div class="col-category">分类信息</div>' +
								'<div class="col-progress">学习进度</div>' +
								'<div class="col-action">操作</div>' +
								'</div>';
							
							res.data.list.map((item, index) => {
								try {
									let categoryInfo = '';
									let progressInfo = '';
									let currentIndex = (pageindex - 1) * pagesize + index + 1;
									
									if (item.type == "心声社区") {
										if (item.postInfo) {
											categoryInfo = (item.postInfo.cmsCategoryList && item.postInfo.cmsCategoryList.length > 0 ? 
												item.postInfo.cmsCategoryList[0].name : "未分类");
											if(item.postInfo.communitypropertyType){
												categoryInfo += " | " + item.postInfo.communitypropertyType;
											}
											if(item.postInfo.themename){
												categoryInfo += " | " + item.postInfo.themename;
											}
											progressInfo = item.positioning || "未记录时间";
											
											html += '<div class="jlitem1 jlitem2" data-type="心声社区" onclick="handleMobileClick(\'communitydetail.html?id='+(item.postInfo.id || "")+'\')">' +
												'<div class="item-index">' + currentIndex + '</div>' +
												'<div class="cc cc2">' +
												'<div>' + (item.postInfo.title || "无标题") + '</div>' +
												'</div>' +
												'<div class="item-category"><label>' + categoryInfo + '</label></div>' +
												'<div class="item-progress">' + progressInfo + '</div>' +
												'<div class="rr jx">' +
												'<a href="communitydetail.html?id='+(item.postInfo.id || "")+'"></a>' +
												'</div>' +
												'</div>';
										}
									} else if (item.type == "主席足迹") {
										if (item.cmsFootprint || item.infoId) {
											const footprint = item.cmsFootprint || {};
											const footprintId = footprint.id || item.infoId || "";
											const footprintTitle = footprint.title || "习近平总书记足迹";
											categoryInfo = "总书记足迹";
											
											if (footprint.cmsCategoryList && footprint.cmsCategoryList.length > 0) {
												categoryInfo = footprint.cmsCategoryList[0].name;
											} else if (item.category && item.category.name) {
												categoryInfo = item.category.name;
											}
											progressInfo = item.positioning || "未记录时间";
											
											html += '<div class="jlitem1 jlitem2" data-type="主席足迹" onclick="handleMobileClick(\'footprintcontent.html?id=' + footprintId + '\')">' +
												'<div class="item-index">' + currentIndex + '</div>' +
												'<div class="cc cc2">' +
												'<div>' + footprintTitle + '</div>' +
												'</div>' +
												'<div class="item-category"><label>' + categoryInfo + '</label></div>' +
												'<div class="item-progress">' + progressInfo + '</div>' +
												'<div class="rr jx">' +
												'<a href="footprintcontent.html?id=' + footprintId + '"></a>' +
												'</div>' +
												'</div>';
										}
									} else if (item.type == "虚仿实验空间") {
										if (item.cmsVirtualSimulation) {
											categoryInfo = "虚仿实验空间";
											progressInfo = item.positioning || "未记录时间";
											
											html += '<div class="jlitem1" data-type="虚仿实验空间" onclick="handleMobileClick(\''+ (item.cmsVirtualSimulation.linkAdress || "#") +'\')">' +
												'<div class="item-index">' + currentIndex + '</div>' +
												'<div class="cc">' +
												'<div>' + (item.cmsVirtualSimulation.titleName || "虚拟实验") + '</div>' +
												'</div>' +
												'<div class="item-category"><label>' + categoryInfo + '</label></div>' +
												'<div class="item-progress">' + progressInfo + '</div>' +
												'<div class="rr jx">' +
												'<a target="_blank" href="'+ (item.cmsVirtualSimulation.linkAdress || "#") +'"></a>' +
												'</div>' +
												'</div>';
										}
									} else if (item.type == "在线学习") {
										if (item.resourcesCourse) {
											categoryInfo = (item.category ? item.category.name : "未分类");
											if (item.resourcesCourse.cmsResourcesAttributes) {
												categoryInfo += ' | ' + item.resourcesCourse.cmsResourcesAttributes.name;
											}
											if (item.resourcesCourse.project) {
												categoryInfo += ' | ' + item.resourcesCourse.project.name;
											}
											progressInfo = item.positioning || "未记录时间";
											
											html += '<div class="jlitem1" data-type="在线学习" onclick="handleMobileClick(\'onlinelearning4.html?id='+ (item.infoId || "") +'\')">' +
												'<div class="item-index">' + currentIndex + '</div>' +
												'<div class="cc">' +
												'<div>' + (item.resourcesCourse.title || "在线学习") + '</div>' +
												'</div>' +
												'<div class="item-category"><label>' + categoryInfo + '</label></div>' +
												'<div class="item-progress">' + progressInfo + '</div>' +
												'<div class="rr jx">' +
												'<a href="onlinelearning4.html?id='+ (item.infoId || "") +'"></a>' +
												'</div>' +
												'</div>';
										}
									} else if(item.type == "在线学习2"){
										categoryInfo = (item.category ? item.category.name : "未分类");
										if (item.resourcesCourse && item.resourcesCourse.cmsResourcesAttributes) {
											categoryInfo += ' | ' + item.resourcesCourse.cmsResourcesAttributes.name;
										}
										if (item.resourcesCourse && item.resourcesCourse.project) {
											categoryInfo += ' | ' + item.resourcesCourse.project.name;
										}
										progressInfo = item.positioning || "未记录时间";
										
										html += '<div class="jlitem1" data-type="在线学习" onclick="handleMobileClick(null, \''+ (item.infoId || "") +'\')">' +
											'<div class="item-index">' + currentIndex + '</div>' +
											'<div class="cc">' +
											'<div>' + (item.resourcesCourse ? item.resourcesCourse.title : "在线学习资源") + '</div>' +
											'</div>' +
											'<div class="item-category"><label>' + categoryInfo + '</label></div>' +
											'<div class="item-progress">' + progressInfo + '</div>' +
											'<div class="rr jx">' +
											'<a onclick="showpdf(this)" data-id="'+ (item.infoId || "") +'"></a>' +
											'</div>' +
											'</div>';
									} else if(item.type == "红色书籍"){
										categoryInfo = (item.category ? item.category.name : '红色书籍');
										if (item.resourcesCourse && item.resourcesCourse.cmsResourcesAttributes) {
											categoryInfo += ' | ' + item.resourcesCourse.cmsResourcesAttributes.name;
										}
										if (item.resourcesCourse && item.resourcesCourse.project) {
											categoryInfo += ' | ' + item.resourcesCourse.project.name;
										}
										progressInfo = '已阅读到第' + (item.positioning || "0") + '页 / 共' + (item.totalInfo || "0") + '页';
										
										html += '<div class="jlitem1" data-type="红色书籍" onclick="handleMobileClick(\'onlinelearning5.html?id='+ (item.infoId || "") +'\')">' +
											'<div class="item-index">' + currentIndex + '</div>' +
											'<div class="cc">' +
											'<div>' + (item.resourcesCourse ? item.resourcesCourse.title : '红色书籍') + '</div>' +
											'</div>' +
											'<div class="item-category"><label>' + categoryInfo + '</label></div>' +
											'<div class="item-progress">' + progressInfo + '</div>' +
											'<div class="rr jx">' +
											'<a href="onlinelearning5.html?id='+ (item.infoId || "") +'"></a>' +
											'</div>' +
											'</div>';
									} else {
										console.log("未知的学习记录类型:", item.type, item);
									}
								} catch (error) {
									console.error("渲染学习记录项出错:", error, item);
								}
							});
							
							if(html === ''){
								$("#listview").html("<div class='mysj'>没有可显示的记录或解析数据时出错...</div>");
							} else {
								$("#listview").html(html);
							}
							
							// 生成分页
							pages = res.data.pages || 1;
							if (pages > 1) {
								let numhtml = "";
								for (let a = 1; a <= pages; a++) {
									if (pageindex == a) {
										numhtml += '<label class="actinum" onclick="getnewlist(' + a + ')">' + a +
											'</label>';
									} else {
										numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>';
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)");
								$("#syy").attr("onclick", "getnewlist(1)");
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")");
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")");
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")");
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")");
								$("#num").html(numhtml);
								$("#fyq").show();
							} else {
								$("#fyq").hide();
							}
						} else {
							console.error("获取学习记录失败:", res);
							$("#listview").html("<div class='mysj'>获取学习记录失败: " + (res.message || "未知错误") + "</div>");
							$("#fyq").hide();
						}
					},
					error: function(xhr, status, error) {
						console.error("请求学习记录出错:", status, error);
						$("#listview").html("<div class='mysj'>请求学习记录出错，请检查网络连接或重新登录</div>");
						$("#fyq").hide();
					}
				});
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是第最后一页了！")
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！")
					}
				} else {
					pageindex = index
					getjllist()
				}
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 						.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '年' + MM + '月' + d + '日 ' + h + ':' + m
			}

			// 移动端点击处理函数
			function handleMobileClick(url, pdfId) {
				// 检测是否为移动端（宽度小于768px时才响应整行点击）
				if (window.innerWidth <= 768) {
					if (pdfId) {
						// 处理PDF类型的学习记录
						showpdf({getAttribute: function(attr) { return pdfId; }});
					} else if (url && url !== "#") {
						// 处理普通链接
						if (url.includes('http') || url.startsWith('//')) {
							window.open(url, '_blank');
						} else {
							window.location.href = url;
						}
					}
				}
				// 桌面端不响应整行点击，保持原有按钮操作
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
