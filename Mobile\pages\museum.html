<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>医德博物馆 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        /* 医德博物馆页面专用样式 */
        .museum-header {
            background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
            color: white;
            padding: 20px 16px;
            margin-top: 56px;
        }
        
        .museum-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .museum-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .museum-content {
            padding: 16px;
        }
        
        .museum-item {
            background: white;
            border-radius: 16px;
            margin-bottom: 20px;
            overflow: hidden;
            box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
        }
        
        .museum-item:active {
            transform: translateY(2px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
        }
        
        .museum-item-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 16px;
            display: flex;
            align-items: center;
            justify-content: space-between;
        }
        
        .museum-item-title {
            display: flex;
            align-items: center;
            gap: 12px;
            font-size: 16px;
            font-weight: 600;
        }
        
        .museum-item-title svg {
            width: 20px;
            height: 20px;
        }
        
        .museum-item-actions {
            display: flex;
            gap: 8px;
        }
        
        .action-btn {
            background: rgba(255, 255, 255, 0.2);
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 16px;
            font-size: 12px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .action-btn:hover {
            background: rgba(255, 255, 255, 0.3);
        }
        
        .action-btn.primary {
            background: rgba(255, 255, 255, 0.9);
            color: #667eea;
        }
        
        .action-btn svg {
            width: 14px;
            height: 14px;
        }
        
        .museum-item-body {
            padding: 16px;
        }
        
        .museum-gallery {
            position: relative;
            margin-bottom: 16px;
        }
        
        .gallery-container {
            position: relative;
            border-radius: 12px;
            overflow: hidden;
            background: #f5f5f5;
        }
        
        .gallery-slide {
            width: 100%;
            height: 200px;
            display: none;
            position: relative;
        }
        
        .gallery-slide.active {
            display: block;
        }
        
        .gallery-slide img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .gallery-nav {
            position: absolute;
            top: 50%;
            transform: translateY(-50%);
            background: rgba(0, 0, 0, 0.5);
            color: white;
            border: none;
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .gallery-nav:hover {
            background: rgba(0, 0, 0, 0.7);
        }
        
        .gallery-nav.prev {
            left: 8px;
        }
        
        .gallery-nav.next {
            right: 8px;
        }
        
        .gallery-indicators {
            display: flex;
            justify-content: center;
            gap: 8px;
            margin-top: 12px;
        }
        
        .gallery-indicator {
            width: 8px;
            height: 8px;
            border-radius: 50%;
            background: #ddd;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .gallery-indicator.active {
            background: #667eea;
            transform: scale(1.2);
        }
        
        .museum-description {
            font-size: 14px;
            line-height: 1.6;
            color: #666;
            text-align: justify;
        }
        
        .loading-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #f093fb;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }
        
        .empty-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 16px;
            opacity: 0.5;
        }
        
        .empty-title {
            font-size: 16px;
            margin-bottom: 8px;
            color: #666;
        }
        
        .empty-description {
            font-size: 14px;
            line-height: 1.5;
        }
        
        @media (max-width: 375px) {
            .museum-header {
                padding: 16px 12px;
            }
            
            .museum-content {
                padding: 12px;
            }
            
            .museum-item {
                margin-bottom: 16px;
            }
            
            .museum-item-header {
                padding: 12px;
            }
            
            .museum-item-body {
                padding: 12px;
            }
            
            .gallery-slide {
                height: 160px;
            }
        }
    </style>
</head>
<body class="mobile-museum">
    <!-- 顶部导航栏 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="menu-btn" id="menuBtn">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="logo">
                <h2 style="margin: 0; font-size: 16px; color: #333;">医德博物馆</h2>
            </div>
            <div class="header-actions">
                <div class="user-info" id="userInfo">
                    <button class="login-btn" id="loginBtn">登录</button>
                    <div class="user-avatar" id="userAvatar" style="display: none;">
                        <span id="userName"></span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 博物馆头部 -->
    <section class="museum-header">
        <div class="museum-title">
            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 28px; height: 28px;">
                <path d="M12 3L2 8V9H22V8L12 3ZM4 11V19H6V11H4ZM8 11V19H10V11H8ZM12 11V19H14V11H12ZM16 11V19H18V11H16ZM20 11V19H22V11H20ZM2 20V22H22V20H2Z"/>
            </svg>
            医德博物馆
        </div>
        <div class="museum-subtitle">传承医者仁心，弘扬医德精神</div>
    </section>

    <!-- 博物馆内容 -->
    <main class="museum-content">
        <div id="museumContainer">
            <!-- 博物馆展品将通过JavaScript动态加载 -->
        </div>
    </main>

    <!-- 侧边菜单 -->
    <nav class="side-menu" id="sideMenu">
        <div class="menu-overlay" id="menuOverlay"></div>
        <div class="menu-content">
            <div class="menu-header">
                <div class="menu-logo">
                    <img src="../../PC/img/logo.png" alt="思政一体化平台">
                    <h3>思政一体化平台</h3>
                </div>
                <button class="menu-close" id="menuClose">×</button>
            </div>
            <div class="menu-items" id="menuItems">
                <!-- 菜单项将通过JavaScript动态加载 -->
            </div>
        </div>
    </nav>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 4C16.55 4 17 4.45 17 5V9.5L20.5 13L19 14.5L15.5 11H15V5C15 4.45 15.45 4 16 4Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>我的</span>
        </a>
    </nav>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83L16.59 15.41L18 14L12 8L6 14Z"/>
        </svg>
    </button>

    <script>
        let classid = null;
        let isLoading = false;

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 检查登录状态
            updateLoginUI();
            
            // 加载博物馆数据
            loadMuseumData();
        });

        function loadMuseumData() {
            if (isLoading) return;
            
            isLoading = true;
            const container = document.getElementById('museumContainer');
            
            container.innerHTML = `
                <div class="loading-state">
                    <div class="loading-spinner"></div>
                    <p>加载中...</p>
                </div>
            `;
            
            // 首先获取博物馆分类ID
            $.ajax({
                url: baseurl + "/web/museum",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200' && res.data && res.data.length > 0) {
                        classid = res.data[0].id;
                        loadMuseumItems();
                    } else {
                        renderEmptyState();
                    }
                },
                error: (err) => {
                    console.error('获取博物馆分类失败:', err);
                    renderEmptyState();
                }
            });
        }

        function loadMuseumItems() {
            $.ajax({
                url: baseurl + "/web/posts",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: {
                    pageNum: 1,
                    pageSize: 9999,
                    categoryId: classid
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        renderMuseumItems(res.data.list || []);
                    } else {
                        renderEmptyState();
                    }
                    isLoading = false;
                },
                error: (err) => {
                    console.error('加载博物馆展品失败:', err);
                    renderEmptyState();
                    isLoading = false;
                }
            });
        }

        function renderMuseumItems(items) {
            const container = document.getElementById('museumContainer');
            
            if (!items || items.length === 0) {
                renderEmptyState();
                return;
            }
            
            let html = '';
            items.forEach((item, index) => {
                html += `
                    <div class="museum-item">
                        <div class="museum-item-header">
                            <div class="museum-item-title">
                                <svg viewBox="0 0 24 24" fill="currentColor">
                                    <path d="M12 2L13.09 8.26L22 9L13.09 9.74L12 16L10.91 9.74L2 9L10.91 8.26L12 2Z"/>
                                </svg>
                                ${item.title}
                            </div>
                            <div class="museum-item-actions">
                                <button class="action-btn primary" onclick="enterLearning('${item.redirectUrl}')">
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M8 5v14l11-7z"/>
                                    </svg>
                                    进入学习
                                </button>
                                <button class="action-btn" onclick="viewDetails('${item.id}')">
                                    <svg viewBox="0 0 24 24" fill="currentColor">
                                        <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                                    </svg>
                                    了解详情
                                </button>
                            </div>
                        </div>
                        <div class="museum-item-body">
                            <div class="museum-gallery">
                                <div class="gallery-container" id="gallery-${index}">
                                    ${renderGallery(item.thumbPath, index)}
                                </div>
                                ${item.thumbPath && item.thumbPath.length > 1 ? renderGalleryIndicators(item.thumbPath, index) : ''}
                            </div>
                            <div class="museum-description">${item.postName || '暂无描述'}</div>
                        </div>
                    </div>
                `;
            });
            
            container.innerHTML = html;
            
            // 初始化图片轮播
            items.forEach((item, index) => {
                if (item.thumbPath && item.thumbPath.length > 1) {
                    initGallery(index, item.thumbPath.length);
                }
            });
        }

        function renderGallery(thumbPath, index) {
            if (!thumbPath || thumbPath.length === 0) {
                return `
                    <div class="gallery-slide active">
                        <div style="width: 100%; height: 100%; background: #f0f0f0; display: flex; align-items: center; justify-content: center; color: #999;">
                            暂无图片
                        </div>
                    </div>
                `;
            }
            
            let html = '';
            thumbPath.forEach((imagePath, imgIndex) => {
                html += `
                    <div class="gallery-slide ${imgIndex === 0 ? 'active' : ''}">
                        <img src="${baseurl}${imagePath}" alt="博物馆展品" onerror="this.style.display='none'">
                    </div>
                `;
            });
            
            if (thumbPath.length > 1) {
                html += `
                    <button class="gallery-nav prev" onclick="prevSlide(${index})">
                        <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px;">
                            <path d="M15.41 7.41L14 6l-6 6 6 6 1.41-1.41L10.83 12z"/>
                        </svg>
                    </button>
                    <button class="gallery-nav next" onclick="nextSlide(${index})">
                        <svg viewBox="0 0 24 24" fill="currentColor" style="width: 16px; height: 16px;">
                            <path d="M10 6L8.59 7.41 13.17 12l-4.58 4.59L10 18l6-6z"/>
                        </svg>
                    </button>
                `;
            }
            
            return html;
        }

        function renderGalleryIndicators(thumbPath, index) {
            if (!thumbPath || thumbPath.length <= 1) return '';
            
            let html = '<div class="gallery-indicators">';
            thumbPath.forEach((_, imgIndex) => {
                html += `<div class="gallery-indicator ${imgIndex === 0 ? 'active' : ''}" onclick="goToSlide(${index}, ${imgIndex})"></div>`;
            });
            html += '</div>';
            
            return html;
        }

        function initGallery(galleryIndex, slideCount) {
            // 自动轮播
            setInterval(() => {
                nextSlide(galleryIndex);
            }, 5000);
        }

        function nextSlide(galleryIndex) {
            const gallery = document.getElementById(`gallery-${galleryIndex}`);
            const slides = gallery.querySelectorAll('.gallery-slide');
            const indicators = gallery.parentElement.querySelectorAll('.gallery-indicator');
            
            let currentIndex = 0;
            slides.forEach((slide, index) => {
                if (slide.classList.contains('active')) {
                    currentIndex = index;
                }
            });
            
            const nextIndex = (currentIndex + 1) % slides.length;
            
            slides[currentIndex].classList.remove('active');
            slides[nextIndex].classList.add('active');
            
            if (indicators.length > 0) {
                indicators[currentIndex].classList.remove('active');
                indicators[nextIndex].classList.add('active');
            }
        }

        function prevSlide(galleryIndex) {
            const gallery = document.getElementById(`gallery-${galleryIndex}`);
            const slides = gallery.querySelectorAll('.gallery-slide');
            const indicators = gallery.parentElement.querySelectorAll('.gallery-indicator');
            
            let currentIndex = 0;
            slides.forEach((slide, index) => {
                if (slide.classList.contains('active')) {
                    currentIndex = index;
                }
            });
            
            const prevIndex = currentIndex === 0 ? slides.length - 1 : currentIndex - 1;
            
            slides[currentIndex].classList.remove('active');
            slides[prevIndex].classList.add('active');
            
            if (indicators.length > 0) {
                indicators[currentIndex].classList.remove('active');
                indicators[prevIndex].classList.add('active');
            }
        }

        function goToSlide(galleryIndex, slideIndex) {
            const gallery = document.getElementById(`gallery-${galleryIndex}`);
            const slides = gallery.querySelectorAll('.gallery-slide');
            const indicators = gallery.parentElement.querySelectorAll('.gallery-indicator');
            
            slides.forEach(slide => slide.classList.remove('active'));
            slides[slideIndex].classList.add('active');
            
            if (indicators.length > 0) {
                indicators.forEach(indicator => indicator.classList.remove('active'));
                indicators[slideIndex].classList.add('active');
            }
        }

        function enterLearning(redirectUrl) {
            const loginStatus = checkLoginStatus();
            if (!loginStatus.isLoggedIn) {
                MobileUtils.showToast('请先登录', 'warning');
                setTimeout(() => {
                    window.location.href = '../login.html';
                }, 1000);
                return;
            }
            
            if (redirectUrl) {
                window.open(redirectUrl, '_blank');
            } else {
                MobileUtils.showToast('学习链接不存在', 'error');
            }
        }

        function viewDetails(itemId) {
            if (itemId) {
                window.location.href = `museum-detail.html?id=${itemId}`;
            } else {
                MobileUtils.showToast('详情ID不存在', 'error');
            }
        }

        function renderEmptyState() {
            const container = document.getElementById('museumContainer');
            container.innerHTML = `
                <div class="empty-state">
                    <svg class="empty-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M12 2L2 8V9H22V8L12 3ZM4 11V19H6V11H4ZM8 11V19H10V11H8ZM12 11V19H14V11H12ZM16 11V19H18V11H16ZM20 11V19H22V11H20ZM2 20V22H22V20H2Z"/>
                    </svg>
                    <div class="empty-title">暂无博物馆展品</div>
                    <div class="empty-description">博物馆正在筹备中，敬请期待</div>
                </div>
            `;
            isLoading = false;
        }
    </script>
</body>
</html>
