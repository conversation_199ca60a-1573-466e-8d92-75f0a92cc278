<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-个人中心</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/wangEditor.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<!-- 导航增强脚本 -->
		<script src="./js/nav-enhancement.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
		<style>
			body {
				background: url(img/background.jpg) no-repeat;
				background-size: cover;
				background-attachment: fixed;
			}
			
			/* 现代化导航栏美化 - 与页面红色主题统一，红色偏橙色 */
			.boxleft {
				background: white;
				border-radius: 16px;
				box-shadow: 0 8px 32px rgba(220, 53, 69, 0.08);
				overflow: hidden;
				border: 1px solid rgba(231, 76, 60, 0.05);
				position: relative;
				backdrop-filter: blur(20px);
			}
			
			.boxleft::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8));
				pointer-events: none;
				border-radius: 16px;
			}
			
			/* 导航栏顶部标题区域 - 红色偏橙色渐变 */
			.lefttopview {
				width: 100%;
				height: 65px;
				background: linear-gradient(135deg, #dc3545, #e74c3c, #ff6b35);
				background-size: 300% 300%;
				animation: gradientShift 6s ease infinite;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 18px;
				color: #FFFFFF;
				font-weight: 700;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif;
				position: relative;
				box-shadow: 0 4px 20px rgba(220, 53, 69, 0.25);
				letter-spacing: 1.5px;
				border-radius: 16px 16px 0 0;
			}
			
			/* 渐变动画效果 */
			@keyframes gradientShift {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.lefttopview img {
				width: 24px;
				height: 24px;
				display: block;
				margin-right: 12px;
				filter: brightness(0) invert(1) drop-shadow(0 0 6px rgba(255, 255, 255, 0.4));
			}
			
			.lefttopview label {
				display: flex;
				align-items: center;
				text-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
			}
			
			/* 导航菜单容器 */
			.leftitembox {
				background: transparent;
				padding: 20px 0;
				position: relative;
				z-index: 2;
			}
			
			.leftitembox::before {
				content: '';
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 85%;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.15), transparent);
			}
			
			/* 导航菜单项 - 现代化设计与图标 */
			.leftitem {
				height: auto;
				padding: 16px 24px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				font-size: 15px;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif;
				font-weight: 500;
				color: #4a5568;
				border-bottom: none;
				text-decoration: none;
				transition: color 0.2s ease, background-color 0.2s ease;
				position: relative;
				margin: 4px 16px;
				border-radius: 12px;
				background: transparent;
				overflow: hidden;
				min-height: 48px;
			}
			
			/* 为不同菜单项添加对应的SVG图标 */
			.leftitem::before {
				content: '';
				width: 20px;
				height: 20px;
				margin-right: 12px;
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				opacity: 0.7;
				transition: opacity 0.2s ease;
				flex-shrink: 0;
			}
			
			/* 个人信息图标 */
			.leftitem[href*="userinfo"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
			}
			
			/* 学生成绩统计图标 */
			.leftitem[href*="studentachievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z'/%3E%3C/svg%3E");
			}
			
			/* 学习记录统计图标 */
			.leftitem[href*="studentlearning"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
			}
			
			/* 考试管理图标 */
			.leftitem[href*="examination"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E");
			}
			
			/* 学习任务管理图标 */
			.leftitem[href*="learning"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
			}
			
			/* 成果展示图标 */
			.leftitem[href*="achievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E");
			}
			
			/* 发布心声图标 */
			.leftitem[href*="voice"]::before,
			.leftitem.activeleftitem::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z'/%3E%3C/svg%3E");
			}
			
			/* 菜单项悬浮态 - 完全无抖动 */
			.leftitem:hover {
				background: linear-gradient(135deg, rgba(220, 53, 69, 0.06), rgba(231, 76, 60, 0.08));
				color: #dc3545;
			}
			
			.leftitem:hover::before {
				opacity: 1;
			}
			
			/* 激活状态的菜单项 - 红色偏橙色渐变与白色图标 */
			.activeleftitem {
				font-weight: 700 !important;
				color: white !important;
				background: linear-gradient(135deg, #dc3545, #e74c3c, #ff6b35) !important;
				background-size: 300% 300%;
				animation: activeGradient 4s ease infinite;
				box-shadow: 0 8px 32px rgba(220, 53, 69, 0.3);
				border-radius: 12px;
			}
			
			@keyframes activeGradient {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.activeleftitem::before {
				opacity: 1 !important;
				filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
			}
			
			.activeleftitem::after {
				content: '';
				position: absolute;
				right: 16px;
				top: 50%;
				transform: translateY(-50%);
				width: 8px;
				height: 8px;
				background: white;
				border-radius: 50%;
				box-shadow: 0 0 12px rgba(255, 255, 255, 0.8);
				animation: activePulse 2s ease infinite;
			}
			
			@keyframes activePulse {
				0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
				50% { opacity: 0.6; transform: translateY(-50%) scale(1.4); }
			}
			
			/* 添加精美的分隔线效果 */
			.leftitem:not(:last-child):not(.activeleftitem) {
				border-bottom: 1px solid rgba(231, 76, 60, 0.06);
				margin-bottom: 3px;
			}
			
			.leftitem:not(:last-child):not(.activeleftitem)::after {
				content: '';
				position: absolute;
				bottom: -1px;
				left: 40px;
				right: 24px;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.15), transparent);
				opacity: 0.5;
			}
			
			/* 整体容器增强效果 */
			.contentview .boxleft {
				background: rgba(255, 255, 255, 0.95);
				border-radius: 16px;
				box-shadow: 0 8px 40px rgba(220, 53, 69, 0.08);
				border: 1px solid rgba(231, 76, 60, 0.05);
				overflow: hidden;
				position: relative;
				backdrop-filter: blur(20px);
			}
			
			.contentview .boxleft::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				height: 3px;
				background: linear-gradient(90deg, #dc3545, #e74c3c, #ff6b35);
				opacity: 0;
				transition: opacity 0.4s ease;
			}
			
			.contentview .boxleft:hover::before {
				opacity: 1;
			}
			
			/* 响应式优化 */
			@media (max-width: 768px) {
				.boxleft {
					border-radius: 12px;
					margin-bottom: 20px;
				}
				
				.lefttopview {
					height: 55px;
					font-size: 16px;
					letter-spacing: 1px;
				}
				
				.lefttopview img {
					width: 20px;
					height: 20px;
					margin-right: 10px;
				}
				
				.leftitem {
					padding: 14px 20px;
					font-size: 14px;
					margin: 3px 12px;
					min-height: 44px;
				}
				
				.leftitem::before {
					width: 18px;
					height: 18px;
					margin-right: 10px;
				}
			}
			
			/* 字体优化 - 思源黑体 */
			.lefttopview,
			.leftitem {
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Source Han Sans', 'Noto Sans CJK SC', 'Microsoft YaHei UI', 'Microsoft YaHei', '微软雅黑', sans-serif;
			}
			
			/* 增强的交互反馈 */
			.leftitem {
				cursor: pointer;
			}
			
			.leftitem:active {
				transform: scale(0.98);
				transition: all 0.1s ease;
			}
			
			.activeleftitem:active {
				transform: scale(1.02);
			}
			
			/* 为菜单项添加微妙的进入动画 */
			.leftitem {
				animation: slideIn 0.5s ease-out forwards;
				opacity: 0;
				transform: translateX(-20px);
			}
			
			.leftitem:nth-child(1) { animation-delay: 0.1s; }
			.leftitem:nth-child(2) { animation-delay: 0.2s; }
			.leftitem:nth-child(3) { animation-delay: 0.3s; }
			.leftitem:nth-child(4) { animation-delay: 0.4s; }
			.leftitem:nth-child(5) { animation-delay: 0.5s; }
			.leftitem:nth-child(6) { animation-delay: 0.6s; }
			.leftitem:nth-child(7) { animation-delay: 0.7s; }
			
			@keyframes slideIn {
				to {
					opacity: 1;
					transform: translateX(0);
				}
			}
		</style>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a href="studentachievement.html" class="leftitem">学生成绩统计</a>
						<a href="studentlearning.html" class="leftitem">学习记录统计</a>
						<a href="userexamination2.html" class="leftitem">考试管理</a>
						<a href="learningtasks.html" class="leftitem">学习任务管理</a>
						<a href="achievements.html" class="leftitem">成果展示</a>
						<a class="leftitem activeleftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="righttopview">
						<label>文章发布</label>
					</div>
					<div id="view1">
						<div class="fbwzbox">
							<label onclick="showview()">发布文章</label>
						</div>
					</div>
					<div id="view2">
						<div class="wbview">
							<input id="wzbt" type="text" placeholder="请输入文章标题" />
							<input id="wzzt" type="text" placeholder="请输入文章主题" />
							<select id="wzfl">
							</select>
							<div id="E">

							</div>
							<div class="fbbtnview">
								<label onclick="submitwz()">发布文章</label>
								<label>取消</label>
							</div>
						</div>
						<div class="yfbwztop">
							已发布文章列表
						</div>
						<div class="bb"></div>
						<div class="wzlistbox" id="listbox">
							
							
						</div>
						<div class="fybox" id="fyq">
							<span id="sy">首页</span>
							<span id="syy">上一页</span>
							<div class="num" id="num">
							</div>
							<span id="xyy">下一页</span>
							<span id="wy">尾页</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let pageindex = 1
			let pagesize = 15
			let pages = 1
			let content = null //富文本内容
			const E = window.wangEditor
			const editor = new E("#E")
			editor.config.menus = [
				"head", "bold", "fontName", "italic", "underline", "strikeThrough", "indent", "lineHeight", "table",
				"image", "video", "splitLine", "undo", "redo"
			] //菜单栏目
			editor.config.uploadImgAccept = ['jpg', 'jpeg', 'png', 'gif'] //上传图片的格式
			editor.config.uploadVideoAccept = ['mp4'] //上传视屏的格式
			editor.config.showLinkImg = false //不上传网络图片
			editor.config.showLinkVideo = false //不上传网络视屏
			editor.config.uploadImgMaxLength = 1 //一次一张图片
			editor.config.zIndex = 1
			editor.config.customUploadImg = (resultFiles, insertImgFn) => {
				let formData = new FormData()
				formData.append('file', resultFiles[0])
				$.ajax({
					url: baseurl + "/attachments/upload",
					type: 'post',
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: formData,
					contentType: false,
					processData: false,
					success: (res) => {
						if (res.code == '200') {
							let imgurl = baseurl + res.data.fastFileId
							insertImgFn(imgurl)
						}
					}
				})
			}
			editor.config.customUploadVideo = (resultFiles, insertVideoFn) => {
				let formData = new FormData()
				formData.append('file', resultFiles[0])
				$.ajax({
					url: baseurl + "/attachments/upload",
					type: 'post',
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: formData,
					contentType: false,
					processData: false,
					success: (res) => {
						if (res.code == '200') {
							let videourl = baseurl + res.data.fastFileId
							insertVideoFn(videourl)
						}
					}
				})
			}
			editor.config.onchange = (html) => {
				content = html
			}
			editor.create()
			let type = 'student' //teacher/student
			var mySwiper = new Swiper('.museumboxitembottoml .swiper', {
				autoplay: false,
				loop: true,
				pagination: {
					el: '.museumboxitembottoml .swiper-pagination',
					clickable: true
				}
			})
			let classid = null
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getclassid()
				getfooterlink()
				getclasslist()
			})
			function getclasslist(){
				$.ajax({
					url: baseurl + "/web/communityproperty/list/tree",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = '<option value="">未选择分类</option>'
							res.data.map((item)=>{
								html+='<option value="'+item.name+'">'+item.name+'</option>'
							})
							$("#wzfl").html(html)
						}
					}
				})
			}
			function getfooterlink(){
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item)=>{
								html+='<a href="'+item.url+'" target="_blank">'+item.name+'</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
function ininfo(item){
				window.location.href = "communitydetail.html?id="+$(item).attr("data-id")
			}
			function getwzlist() {
				let params = "?pageNum="+pageindex+"&pageSize="+pagesize+"&creator="+JSON.parse(userinfo).userAuth.identifier+"&categoryId="+classid
				$.ajax({
					url: baseurl + "/posts"+params,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.list.map((item)=>{
								html+='<div class="wzitem">'+
								'<div class="itemnr2">'+
									'<div><label><img style="width: 1.041666rem;" src="img/flsq.png" />'
									if(item.communitypropertyType!=null){
										html+=item.communitypropertyType
									}else{
										html+=item.cmsCategoryList[0].name
									}
									html+='</label>'+
										'<label><img style="width: 0.9375rem;" src="img/sjsq.png" />'+setDate(item.createdAt)+'</label>'+
									'</div><div class="itemtitle">'+item.title+'</div></div>'
								if(item.approvalStatus=='1'){
									html+='<label class="ttt state ing">审核中</label>'
									html+='<label class="ttt btn">'+
										'<label class="no">查看文章</label></label></div>'
								}
								else if(item.approvalStatus=='2'){
									html+='<label class="ttt state error">审核未通过<span>（'+item.approvalOption+'）</span></label>'
									html+='<label class="ttt btn">'+
										'<label class="no">查看文章</label></label></div>'
								}
								else if(item.approvalStatus=='3'){
									html+='<label class="ttt state success">审核通过</label>'
									html+='<label class="ttt btn">'+
										'<label class="is" onclick="ininfo(this)" data-id="'+item.id+'">查看文章</label></label></div>'
								}
							})
							$("#listbox").html(html)
							pages = res.data.pages
							if (pages > 1) {
								let numhtml = ""
								//当页数大于5
								if (pages > 5) {
									if (pageindex <= 5) { //当前页码小于5的时候右侧不变
										for (let a = 1; a <= 10; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
										numhtml += '<label onclick="getnewlist(11)">...</label>'
									} else if (5 < pageindex & pageindex < pages - 5) {
										numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) - 5) +
											')">...</label>'
										for (let a = pageindex - 4; a <= pageindex + 4; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
										numhtml += '<label onclick="getnewlist(' + (parseInt(pageindex) + 5) +
											')">...</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + (pages - 11) +
											')">...</label>'
										for (let a = pages - 10; a <= pages; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a +
													'</label>'
											}
										}
									}
								} else {
									for (let a = 1; a <= pages; a++) {
										if (pageindex == a) {
											numhtml += '<label class="actinum" onclick="getnewlist(' + a +
												')">' + a +
												'</label>'
										} else {
											numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
										}
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
						}
					}
				})
			}
			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是第最后一页了！")
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！")
					}
				} else {
					pageindex = index
					getwzlist()
				}
			}
			function submitwz() {
				let titles = $("#wzbt").val()
				if (!titles) {
					cocoMessage.warning(1000, "请输入标题！")
				} else if (!content) {
					cocoMessage.warning(1000, "请输入内容！")
				} else {
					let jsons = {
						title: titles,
						content: content,
						approvalStatus: 1,
						author: JSON.parse(userinfo).name,
						cmsCategoryList: [{id: classid}],
						communitypropertyType: $("#wzfl").val(),
						themename: $("#wzzt").val()
					}
					$.ajax({
						url: baseurl + "/posts/add",
						type: 'POST',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						data: JSON.stringify(jsons),
						dataType: 'json',
						success: (res) => {
							if (res.code == '200') {
								cocoMessage.success(1000, "发布心声成功！")
								$("#wzbt").val('')
								editor.txt.html('')
								getwzlist()
							} else if(res.code == '400666'){
								cocoMessage.error(1000, "您输入的内容包含非法字符或者敏感词汇请检查！")
							} else {
								cocoMessage.error(1000, "发布心声失败！请稍后重试！")
							}
						}
					})
				}
			}

			function showview() {
				$("#view1").hide()
				$("#view2").show()
				getwzlist()
			}

			function getclassid() {
				$.ajax({
					url: baseurl + "/category/user",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							classid = res.data[0].id
							// 获取到classid后立即加载文章列表
							getwzlist()
						}
					}
				})
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 						.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
			
			function setDate(value){
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '年' + MM + '月' + d + '日 ' + h + ':' + m
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
