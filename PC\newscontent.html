<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-新闻资讯</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/news.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="content">
			<div class="newsbox">
				<div class="newboxleft">
					<div class="lefttop" id="tablist">
						<label style="cursor: pointer;" onclick="window.location.href='news.html'">新闻资讯></label>
						<label id="classname"></label>
					</div>
					<div class="sqtopsss">
						<div>
							<label>
								<img style="width: 1.041666rem;" src="img/flsq.png" />
								<span id="fl"></span>
							</label>
							<label>
								<img style="width: 0.9375rem;" src="img/zzsq.png" />
								<span id="zz"></span>
							</label>
						</div>
						<div>
							<label>
								<img style="width: 0.9375rem;" src="img/sjsq.png" />
								<span id="sj"></span>
							</label>
							<label>
								<img style="width: 1.145833rem;" src="img/yjsq.png" />
								<span id="ll"></span>
							</label>
							<!-- <label>
								<img style="width: 0.885416rem;" src="img/dz.png" />
								<span id="dz"></span>
							</label> -->
						</div>
					</div>
					<div class="padd">
						<div class="newtitle" id="titles"></div>
						<div class="newnr" id="nr">
						</div>
						<!-- <div class="dzbox">
							<div id="dz1" class="mz mz1">
								<div onclick="dzsubmit()">
									<img src="img/dzno.png" />
								</div>
								<div>点个赞，鼓励一下</div>
							</div>
							<div id="dz2" class="mz mz2">
								<div>
									<img src="img/dzyes.png" />
								</div>
								<div id="dznum"></div>
							</div>
						</div> -->
					</div>
					<!-- <div class="bbbbbbbbbbb">
						<a>上一篇：这是一短标题这是一短标题这是一短标题这是一短标题</a>
						<a>下一篇：这是一短标题这是一短标题这是一短标题这是一短标题</a>
					</div> -->
				</div>
				<div class="newsright">
					<div class="border"></div>
					<div class="newtitles1">
						<label id="classnn"></label>
						<div class="jtdiv">
							<span class="leftimg" onclick="syy2()">
							</span>
							<span class="rightimg" onclick="xyy2()">
							</span>
						</div>
					</div>
					<div class="boxxx" id="gglist">

					</div>
					<!-- <div class="bbbb"></div>
					<div class="border"></div>
					<div class="newtitles1">
						思政活动公告
					</div>
					<div class="boxxx">
						<div class="boxitem">
							<div class="itemnr">这是一个新闻标题这是一个新闻标题这是一个新闻标题</div>
							<div class="itemsj">2022年1月12日</div>
						</div>
						<div class="boxitem">
							<div class="itemnr">这是一个新闻标题这是一个新闻标题这是一个新闻标题</div>
							<div class="itemsj">2022年1月12日</div>
						</div>
						<div class="boxitem">
							<div class="itemnr">这是一个新闻标题这是一个新闻标题这是一个新闻标题</div>
							<div class="itemsj">2022年1月12日</div>
						</div>
						<div class="boxitem">
							<div class="itemnr">这是一个新闻标题这是一个新闻标题这是一个新闻标题</div>
							<div class="itemsj">2022年1月12日</div>
						</div>
						<div class="boxitem">
							<div class="itemnr">这是一个新闻标题这是一个新闻标题这是一个新闻标题</div>
							<div class="itemsj">2022年1月12日</div>
						</div>
					</div> -->
				</div>
			</div>
		</div>

		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let pageindex = 1
			let pageindex2 = 1
			let pages2 = 1
			let pagesize = 10
			let classid = getUrlParam('classid')

			
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				let userinfo = sessionStorage.getItem("userinfo")
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				getclass('news.html')
				getinfo()
				getclassid()
				getfooterlink()
			})
			function dzsubmit(item) {
				$("#dz1").hide()
				$("#dz2").show()
				let json = {
					postId: getUrlParam('id')
				}
				$.ajax({
					url: baseurl + "/cmsgive/add",
					type: 'POST',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: JSON.stringify(json),
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let num = parseInt($("#dz").html()) + 1
							$("#dz").html(num)
							$("#dznum").html(num)
						}
					}
				})
			}
			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			function getclassid() {
				$.ajax({
					url: baseurl + "/web/category/news",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data[0].children[2])
							$("#classnn").html(res.data[0].children[2].name)
							classid = res.data[0].children[2].id
							getzxgg()
						}
					}
				})
			}

			function syy2() {
				if (pageindex2 > 1) {
					pageindex2 -= 1
					getzxgg()
				}
			}

			function xyy2() {
				if (pageindex2 < pages2) {
					pageindex2 += 1
					getzxgg()
				}
			}

			function getzxgg() {
				$.ajax({
					url: baseurl + "/web/postByCategoryId",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: pageindex2,
						pageSize: 8,
						id: classid,
						sort: 'dispaly_time'
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data.list)
							pages2 = res.data.pages
							let html = ""
							res.data.list.map((item) => {
								html += '<div class="boxitem" onclick="incontent(this)" data-id="' + item
									.id + '"><div class="itemnr">' + item.title + '</div>' +
									'<div class="itemsj">' + setDate(item.eventTime) + '</div></div>'
							})
							$("#gglist").html(html)
						}
					}
				})
			}

			function selectid(item) {
				window.location.href = "news.html?&classid=" + $(item).attr("data-id")
			}

			function getinfo() {
				$.ajax({
					url: baseurl + "/web/posts/" + getUrlParam('id'),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							if(res.data.isGiveLike==1){
								$("#dz1").hide()
								$("#dz2").show()
							}
							$("#classname").html(res.data.cmsCategoryList[0].name)
							$("#fl").html(res.data.cmsCategoryList[0].name)
							$("#zz").html(res.data.author)
							$("#sj").html(setDate(res.data.eventTime))
							$("#ll").html(res.data.clickCount)
							$("#dz").html(res.data.giveLike)
							$("#dznum").html(res.data.giveLike)
							$("#nr").html(res.data.content)
							$("#titles").html(res.data.title)
							clicknum(res.data.id)
						}
					}
				})
			}
			function clicknum(id){
				$.ajax({
					url: baseurl + "/web/posts/click/"+id,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
					}
				})
			}
			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					if (res.data[i].id == getUrlParam('id') || res.data[i].redirectUrl == 'news.html') {
			// 						classdate = res.data[i]
			// 						html += '<a class="menuitem active" href="' + res.data[i].redirectUrl + '?id=' +
			// 							res
			// 							.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					} else {
			// 						html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 							.data[
			// 								i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					}
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d
			}

			function incontent(item) {
				window.location.href = "newscontent.html?id=" + $(item).attr("data-id") + "&classid=" + classid
			}

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
