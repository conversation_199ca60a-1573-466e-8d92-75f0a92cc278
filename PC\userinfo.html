<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-个人中心</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<!-- 导航增强脚本 -->
		<script src="./js/nav-enhancement.js" type="text/javascript" charset="utf-8"></script>
		<style>
			body {
				background: url(img/background.jpg) no-repeat;
				background-size: cover;
				background-attachment: fixed;
			}
			
			/* 现代化导航栏美化 - 与页面红色主题统一，红色偏橙色 */
			.boxleft {
				background: white;
				border-radius: 16px;
				box-shadow: 0 8px 32px rgba(220, 53, 69, 0.08);
				overflow: hidden;
				border: 1px solid rgba(231, 76, 60, 0.05);
				position: relative;
				backdrop-filter: blur(20px);
			}
			
			.boxleft::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8));
				pointer-events: none;
				border-radius: 16px;
			}
			
			/* 导航栏顶部标题区域 - 红色偏橙色渐变 */
			.lefttopview {
				width: 100%;
				height: 65px;
				background: linear-gradient(135deg, #dc3545, #e74c3c, #ff6b35);
				background-size: 300% 300%;
				animation: gradientShift 6s ease infinite;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 18px;
				color: #FFFFFF;
				font-weight: 700;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif;
				position: relative;
				box-shadow: 0 4px 20px rgba(220, 53, 69, 0.25);
				letter-spacing: 1.5px;
				border-radius: 16px 16px 0 0;
			}
			
			/* 渐变动画效果 */
			@keyframes gradientShift {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.lefttopview img {
				width: 24px;
				height: 24px;
				display: block;
				margin-right: 12px;
				filter: brightness(0) invert(1) drop-shadow(0 0 6px rgba(255, 255, 255, 0.4));
			}
			
			.lefttopview label {
				display: flex;
				align-items: center;
				text-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
			}
			
			/* 导航菜单容器 */
			.leftitembox {
				background: transparent;
				padding: 20px 0;
				position: relative;
				z-index: 2;
				min-height: 420px; /* 确保导航栏最小高度一致 */
			}
			
			.leftitembox::before {
				content: '';
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 85%;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.15), transparent);
			}
			
			/* 导航菜单项 - 现代化设计与图标 */
			.leftitem {
				height: auto;
				padding: 16px 24px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				font-size: 15px;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif;
				font-weight: 500;
				color: #4a5568;
				border-bottom: none;
				text-decoration: none;
				transition: color 0.2s ease, background-color 0.2s ease;
				position: relative;
				margin: 4px 16px;
				border-radius: 12px;
				background: transparent;
				overflow: hidden;
				min-height: 48px; /* 确保每个导航项高度一致 */
				box-sizing: border-box; /* 包含padding和border在高度计算中 */
			}
			
			/* 为不同菜单项添加对应的SVG图标 */
			.leftitem::before {
				content: '';
				width: 20px;
				height: 20px;
				margin-right: 12px;
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				opacity: 0.7;
				transition: opacity 0.2s ease;
				flex-shrink: 0;
			}
			
			/* 个人信息图标 */
			.leftitem[href*="userinfo"]::before,
			.leftitem.activeleftitem::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E") !important;
			}
			
			/* 激活状态的个人信息图标为白色 */
			.activeleftitem::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E") !important;
				opacity: 1 !important;
				filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
			}
			
			/* 学生成绩统计图标 */
			.leftitem[href*="studentachievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z'/%3E%3C/svg%3E");
			}
			
			/* 学习记录统计图标 */
			.leftitem[href*="studentlearning"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z'/%3E%3C/svg%3E");
			}
			
			/* 考试管理图标 */
			.leftitem[href*="examination"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E");
			}
			
			/* 学习任务管理图标 */
			.leftitem[href*="learning"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
			}
			
			/* 学生任务图标 */
			.leftitem[href*="studentasks"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm-1 16H9V7h9v14z'/%3E%3C/svg%3E");
			}
			
			/* 学习路径图标 */
			.leftitem[href*="learningrecords"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M2 20h20v-4H2v4zm2-3h2v2H4v-2zM2 4v4h20V4H2zm4 3H4V5h2v2zm-4 7h20v-4H2v4zm2-3h2v2H4v-2z'/%3E%3C/svg%3E");
			}
			
			/* 考试成绩图标（注意是achievement.html不是achievements.html）*/
			.leftitem[href*="achievement.html"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E");
			}
			
			/* 用户问题页面图标 */
			.leftitem[href*="userquestion"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z'/%3E%3C/svg%3E");
			}
			
			/* 修改密码图标 */
			.leftitem[href*="userpassword"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z'/%3E%3C/svg%3E");
			}
			
			/* 成果展示图标 */
			.leftitem[href*="achievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E");
			}
			
			/* 发布心声图标 */
			.leftitem[href*="voice"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z'/%3E%3C/svg%3E");
			}
			
			/* 菜单项悬浮态 - 完全无抖动 */
			.leftitem:hover {
				background: linear-gradient(135deg, rgba(220, 53, 69, 0.06), rgba(231, 76, 60, 0.08));
				color: #dc3545;
			}
			
			.leftitem:hover::before {
				opacity: 1;
			}
			
			/* 激活状态的菜单项 - 统一标准双色渐变背景 */
			.leftitem.activeleftitem,
			a.leftitem.activeleftitem,
			.leftitembox .leftitem.activeleftitem,
			#teambox .leftitem.activeleftitem {
				font-weight: 700 !important;
				color: white !important;
				background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
				background-size: 200% 200% !important;
				animation: activeGradient 3s ease infinite !important;
				box-shadow: 0 4px 16px rgba(220, 53, 69, 0.25) !important;
				border-radius: 12px !important;
				opacity: 1 !important;
				transform: translateX(0) !important;
			}
			
			@keyframes activeGradient {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.leftitem.activeleftitem::before,
			a.leftitem.activeleftitem::before,
			.leftitembox .leftitem.activeleftitem::before,
			#teambox .leftitem.activeleftitem::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E") !important;
				opacity: 1 !important;
				filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
			}
			
			.leftitem.activeleftitem::after,
			a.leftitem.activeleftitem::after,
			.leftitembox .leftitem.activeleftitem::after,
			#teambox .leftitem.activeleftitem::after {
				content: '' !important;
				position: absolute !important;
				right: 16px !important;
				top: 50% !important;
				transform: translateY(-50%) !important;
				width: 8px !important;
				height: 8px !important;
				background: white !important;
				border-radius: 50% !important;
				box-shadow: 0 0 12px rgba(255, 255, 255, 0.8) !important;
				animation: activePulse 2s ease infinite !important;
			}
			
			@keyframes activePulse {
				0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
				50% { opacity: 0.6; transform: translateY(-50%) scale(1.4); }
			}
			
			/* 添加精美的分隔线效果 */
			.leftitem:not(:last-child):not(.activeleftitem) {
				border-bottom: 1px solid rgba(231, 76, 60, 0.06);
				margin-bottom: 3px;
			}
			
			.leftitem:not(:last-child):not(.activeleftitem)::after {
				content: '';
				position: absolute;
				bottom: -1px;
				left: 40px;
				right: 24px;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.15), transparent);
				opacity: 0.5;
			}
			
			/* 整体容器增强效果 */
			.contentview .boxleft {
				background: rgba(255, 255, 255, 0.95);
				border-radius: 16px;
				box-shadow: 0 8px 40px rgba(220, 53, 69, 0.08);
				border: 1px solid rgba(231, 76, 60, 0.05);
				overflow: hidden;
				position: relative;
				backdrop-filter: blur(20px);
			}
			
			.contentview .boxleft::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				height: 3px;
				background: linear-gradient(90deg, #dc3545, #e74c3c, #ff6b35);
				opacity: 0;
				transition: opacity 0.4s ease;
			}
			
			.contentview .boxleft:hover::before {
				opacity: 1;
			}
			
			/* 响应式优化 */
			@media (max-width: 768px) {
				.boxleft {
					border-radius: 12px;
					margin-bottom: 20px;
				}
				
				.lefttopview {
					height: 55px;
					font-size: 16px;
					letter-spacing: 1px;
				}
				
				.lefttopview img {
					width: 20px;
					height: 20px;
					margin-right: 10px;
				}
				
				.leftitem {
					padding: 14px 20px;
					font-size: 14px;
					margin: 3px 12px;
					min-height: 44px;
				}
				
				.leftitem::before {
					width: 18px;
					height: 18px;
					margin-right: 10px;
				}
			}
			
			/* 字体优化 - 思源黑体 */
			.lefttopview,
			.leftitem {
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Source Han Sans', 'Noto Sans CJK SC', 'Microsoft YaHei UI', 'Microsoft YaHei', '微软雅黑', sans-serif;
			}
			
			/* 增强的交互反馈 */
			.leftitem {
				cursor: pointer;
			}
			
			.leftitem:active {
				transform: scale(0.98);
				transition: all 0.1s ease;
			}
			
			.activeleftitem:active {
				transform: scale(1.02);
			}
			
			/* 导航菜单项 - 移除加载动画，立即显示 */
			.leftitem:not(.activeleftitem) {
				/* 移除slideIn动画，导航项立即显示 */
				opacity: 1;
				transform: translateX(0);
			}
			
			/* 激活状态不使用动画，立即显示 */
			.leftitem.activeleftitem,
			a.leftitem.activeleftitem,
			.leftitembox .leftitem.activeleftitem,
			#teambox .leftitem.activeleftitem {
				animation: none !important;
				opacity: 1 !important;
				transform: translateX(0) !important;
			}
			
			/* 移除所有导航项的动画延迟 */
			.leftitem:nth-child(1),
			.leftitem:nth-child(2), 
			.leftitem:nth-child(3), 
			.leftitem:nth-child(4), 
			.leftitem:nth-child(5), 
			.leftitem:nth-child(6), 
			.leftitem:nth-child(7) { 
				animation-delay: 0s;
			}
		</style>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
					<a id="login"
						href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a> <a
						id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">

					</div>
				</div>
				<div class="boxright">
					<div class="righttopview">
						<label>个人信息</label>
					</div>
					<div class="box1">
						<div class="box1_toptitle">基本信息</div>
						<div id="info">

						</div>
						<img id="yz" src="" />
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">

					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>

		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script>
			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})

				if (getUrlParam('ticket')) {
					let json = {
						service: "http://szjx.sntcm.edu.cn/userinfo.html",
						ticket: getUrlParam('ticket')
					}
					// console.log(getUrlParam('ticket'))
					$.ajax({
						url: baseurl + "/student/caslogin",
						type: 'POST',
						data: JSON.stringify(json),
						contentType: "application/json",
						dataType: 'json',
						success: (res) => {
							if (res.code == "200") {
								cocoMessage.success(1000, "登录成功！")
								//储存token
								sessionStorage.setItem('header', res.data.scheme + "" + res.data.token)
								//储存用户信息
								sessionStorage.setItem('userinfo', JSON.stringify(res.data.student))

								let userinfonew = sessionStorage.getItem("userinfo")
								if (userinfonew) {
									//已登录 则显示用户信息
									$("#login").hide()
									$("#user").show()
									$("#user").html(JSON.parse(userinfonew).name)
									$("#edit").show()
								} else {
									//未登录 则显示登录按钮
									$("#login").show()
									$("#user").hide()
									$("#edit").hide()
								}
								
								getuserinfoallfun()
							} else {
								cocoMessage.error(1000, res.message)
							}
						}
					})
				} else {
					if (!sessionStorage.getItem("userinfo")) {
						//没有地址并且未登录
						$("#login").show()
						$("#user").hide()
						$("#edit").hide()
						window.location.href =
							'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
					}else{
						//没有地址 但是已登录
						getuserinfoallfun()
					}
				}

			})


			function getuserinfoallfun() {
				let userinfo = sessionStorage.getItem("userinfo")
				if (userinfo) {
					if (window.localStorage.getItem("jilu")) {
						$.ajax({
							url: baseurl + "/study/record/add",
							type: 'post',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: window.localStorage.getItem("jilu"),
							dataType: 'json',
							success: (res) => {
								window.localStorage.clear()
							}
						})
					}
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
					getclass()
					getfooterlink()
					if (JSON.parse(userinfo).roleName == '老师') {
						$("#yz").attr('src', 'img/ls.png')
						//左侧菜单
						let html = '<a class="leftitem activeleftitem">个人信息</a>' +
							'<a href="studentachievement.html" class="leftitem">学生成绩统计</a>' +
							'<a href="studentlearning.html" class="leftitem">学习记录统计</a>' +
							'<a href="userexamination2.html" class="leftitem">考试管理</a>' +
							'<a href="learningtasks.html" class="leftitem">学习任务管理</a>' +
							'<a href="achievements.html" class="leftitem">成果展示</a>' +
							'<a class="leftitem" href="releaseyourvoice2.html">发布心声</a>'
						$("#teambox").html(html)

						// 重新应用导航样式
						setTimeout(() => {
							if (window.initNavEnhancement) {
								window.initNavEnhancement();
							}
							// 强制应用激活状态样式
							const activeItem = document.querySelector('.activeleftitem');
							if (activeItem) {
								console.log('找到激活项:', activeItem);
								console.log('当前样式:', window.getComputedStyle(activeItem).background);
								activeItem.style.cssText = `
									background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
									color: white !important;
									font-weight: 700 !important;
									opacity: 1 !important;
									transform: translateX(0) !important;
									animation: activeGradient 3s ease infinite !important;
									background-size: 200% 200% !important;
									box-shadow: 0 4px 16px rgba(220, 53, 69, 0.25) !important;
									border-radius: 12px !important;
								`;
								console.log('强制样式后:', window.getComputedStyle(activeItem).background);
							} else {
								console.log('未找到激活项');
								console.log('所有leftitem:', document.querySelectorAll('.leftitem'));
							}
						}, 100);

						// let html2 = '<div class="xx">'+
						// 			'<div class="xxleft">教工号:</div>'+
						// 			'<label class="xxnr">'+JSON.parse(userinfo).userAuth.identifier+'</label></div>'+
						// 		'<div class="xx">'+
						// 			'<div class="xxleft">姓名:</div>'+
						// 			'<label class="xxnr">'+JSON.parse(userinfo).name+'</label><img src="img/jsico.png"/></div>'+
						// 		'<div class="xx">'+
						// 			'<div class="xxleft">院系:</div>'+
						// 			'<label class="xxnr">xxxxx院</label></div>'+
						// 		'<div class="xx">'+
						// 			'<div class="xxleft">职称:</div>'+
						// 			'<label class="xxnr">xxxxxx主任</label></div>'
						let html2 = '<div class="xx">' +
							'<div class="xxleft">教工号:</div>' +
							'<label class="xxnr">' + JSON.parse(userinfo).userAuth.identifier + '</label></div>' +
							'<div class="xx">' +
							'<div class="xxleft">姓名:</div>' +
							'<label class="xxnr">' + JSON.parse(userinfo).name + '</label><img src="img/jsico.png"/></div>' +
							'<div class="xx">' +
							'<div class="xxleft">院系:</div>' +
							'<label class="xxnr">' + JSON.parse(userinfo).myCollegeName + '</label></div>'

						$("#info").html(html2)
					} else {
						$("#yz").attr('src', 'img/xs.png')
						//左侧菜单
						let html = '<a class="leftitem activeleftitem">个人信息</a>' +
							'<a href="studentasks.html" class="leftitem">学习任务</a>' +
							'<a href="learningrecords.html" class="leftitem">学习路径</a>' +
							'<a href="achievement.html" class="leftitem">考试成绩</a>' +
							'<a href="releaseyourvoice.html" class="leftitem">发布心声</a>'
						$("#teambox").html(html)
						
						// 重新应用导航样式
						setTimeout(() => {
							if (window.initNavEnhancement) {
								window.initNavEnhancement();
							}
							// 强制应用激活状态样式
							const activeItem = document.querySelector('.activeleftitem');
							if (activeItem) {
								console.log('找到激活项:', activeItem);
								console.log('当前样式:', window.getComputedStyle(activeItem).background);
								activeItem.style.cssText = `
									background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
									color: white !important;
									font-weight: 700 !important;
									opacity: 1 !important;
									transform: translateX(0) !important;
									animation: activeGradient 3s ease infinite !important;
									background-size: 200% 200% !important;
									box-shadow: 0 4px 16px rgba(220, 53, 69, 0.25) !important;
									border-radius: 12px !important;
								`;
								console.log('强制样式后:', window.getComputedStyle(activeItem).background);
							} else {
								console.log('未找到激活项');
								console.log('所有leftitem:', document.querySelectorAll('.leftitem'));
							}
						}, 100);
						
						// console.log(JSON.parse(userinfo))
						let html2 = '<div class="xx">' +
							'<div class="xxleft">学号:</div>' +
							'<label class="xxnr">' + JSON.parse(userinfo).userAuth.identifier + '</label></div>' +
							'<div class="xx">' +
							'<div class="xxleft">姓名:</div>' +
							'<label class="xxnr">' + JSON.parse(userinfo).name + '</label><img src="img/xsico.png"/></div>' +
							'<div class="xx">' +
							'<div class="xxleft">院系:</div>' +
							'<label class="xxnr">' + JSON.parse(userinfo).collegeName + '</label></div>' +
							'<div class="xx">' +
							'<div class="xxleft">专业:</div>' +
							'<label class="xxnr">' + JSON.parse(userinfo).majorName + '</label></div>' +
							'<div class="xx">' +
							'<div class="xxleft">班级:</div>' +
							'<label class="xxnr">' + JSON.parse(userinfo).className2 + '</label></div>'
						$("#info").html(html2)
					}
					getuserinfo(JSON.parse(userinfo).id)
				}
			}

			function chackpassword() {
				window.location.href = 'userpassword.html'
			}

			function insetquestion() {
				window.location.href = 'userquestion.html'
			}

			function getuserinfo(id) {
				$.ajax({
					url: baseurl + "/student/" + id,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// // console.log(res.data)
							// /*安全问题*/
							// let htmlwt = ''
							// if (res.data.questionName) {
							// 	htmlwt = `<div class="xx1"><div class="xxleft">安全问题:</div><label class="xxnr">${res.data.questionName}</label></div>
							// 	<div class="xx2">
							// 		<label class="wbd">已设置</label>
							// 	</div>`
							// } else {
							// 	htmlwt = `<div class="xx1"><div class="xxleft">安全问题:</div></div>
							// 	<div class="xx2">
							// 		<label class="wbd">未设置</label>
							// 		<label class="bdbtn" onclick="insetquestion()">设置</label>
							// 	</div>`
							// }
							// $("#aqwtdiv").html(htmlwt)
						}
					}
				})
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 						.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
				window.location.href = "index.html"
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>