const express = require('express');
const { createProxyMiddleware } = require('http-proxy-middleware');
const cors = require('cors');

const app = express();

// 启用CORS
app.use(cors({
    origin: '*',
    methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
    allowedHeaders: ['Content-Type', 'Authorization']
}));

// 添加请求日志中间件
app.use((req, res, next) => {
  console.log(`[${new Date().toISOString()}] ${req.method} ${req.url}`);
  next();
});

// 解析JSON请求体
app.use(express.json());
app.use(express.urlencoded({ extended: true }));

// 配置代理
const proxy = createProxyMiddleware({
    target: 'https://szjx.sntcm.edu.cn',
    changeOrigin: true,
    secure: false, // 不验证SSL证书
    pathRewrite: {
        '^/api': '/api', // 保留/api路径
        '^/captcha': '/api/captcha' // 为验证码请求添加/api前缀
    },
    timeout: 60000, // 增加超时时间到60秒
    proxyTimeout: 60000, // 代理超时设置
    onProxyRes: function (proxyRes, req, res) {
        console.log(`[${new Date().toISOString()}] 代理响应状态: ${proxyRes.statusCode} - ${req.method} ${req.url}`);
        // 确保所有CORS头都正确设置
        proxyRes.headers['Access-Control-Allow-Origin'] = '*';
        proxyRes.headers['Access-Control-Allow-Methods'] = 'GET,POST,PUT,DELETE,OPTIONS';
        proxyRes.headers['Access-Control-Allow-Headers'] = 'Content-Type,Authorization';
        
        // 添加自定义响应处理
        const originalWrite = res.write;
        const originalEnd = res.end;
        
        let chunks = [];
        
        // 捕获响应体
        res.write = function(chunk) {
            chunks.push(chunk);
            return originalWrite.apply(res, arguments);
        };
        
        // 输出完整响应
        res.end = function(chunk) {
            if (chunk) {
                chunks.push(chunk);
            }
            
            if (proxyRes.headers['content-type'] && proxyRes.headers['content-type'].includes('json')) {
                try {
                    const responseBody = Buffer.concat(chunks).toString('utf8');
                    console.log(`[${new Date().toISOString()}] 响应体: ${responseBody}`);
                } catch (err) {
                    console.error(`无法解析响应体: ${err.message}`);
                }
            }
            
            return originalEnd.apply(res, arguments);
        };
    },
    onProxyReq: function(proxyReq, req, res) {
        console.log(`[${new Date().toISOString()}] 代理请求: ${req.method} ${req.url}`);
        
        // 记录请求体
        if (req.body && Object.keys(req.body).length > 0) {
            console.log(`请求体: ${JSON.stringify(req.body)}`);
            
            // 确保请求体正确传递到代理请求
            const bodyData = JSON.stringify(req.body);
            proxyReq.setHeader('Content-Type', 'application/json');
            proxyReq.setHeader('Content-Length', Buffer.byteLength(bodyData));
            proxyReq.write(bodyData);
        }
    },
    onError: function(err, req, res) {
        console.error(`[${new Date().toISOString()}] 代理错误: ${err.message}`);
        res.writeHead(500, {
            'Content-Type': 'application/json',
            'Access-Control-Allow-Origin': '*',
            'Access-Control-Allow-Methods': 'GET,POST,PUT,DELETE,OPTIONS',
            'Access-Control-Allow-Headers': 'Content-Type,Authorization'
        });
        res.end(JSON.stringify({ 
            code: '500', 
            message: '代理服务器错误: ' + err.message,
            timestamp: new Date().toISOString()
        }));
    }
});

// 将所有请求转发到目标服务器，不仅仅是/api开头的
app.use('/', proxy);

// OPTIONS 预检请求处理
app.options('*', cors());

// 添加错误处理中间件
app.use((err, req, res, next) => {
    console.error(`[${new Date().toISOString()}] 服务器错误: ${err.message}`);
    res.status(500).json({ 
        code: '500', 
        message: '服务器错误: ' + err.message,
        timestamp: new Date().toISOString()
    });
});

// 添加 favicon.ico 处理
app.get('/favicon.ico', (req, res) => {
    res.status(204).end(); // 返回204无内容
});

// 添加健康检查端点
app.get('/health', (req, res) => {
    res.status(200).json({ status: 'ok', timestamp: new Date().toISOString() });
});

// 启动服务器
const port = 5500;
app.listen(port, '0.0.0.0', () => {
    console.log(`代理服务器运行在 http://localhost:${port}`);
    console.log(`目标服务器: https://szjx.sntcm.edu.cn`);
    console.log(`时间: ${new Date().toISOString()}`);
}); 