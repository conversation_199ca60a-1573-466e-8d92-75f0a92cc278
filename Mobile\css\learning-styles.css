/* 学习模块样式 */
.learning-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
    padding: 16px 0;
}

.learning-item {
    background: white;
    border-radius: 8px;
    overflow: hidden;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    text-decoration: none;
    color: inherit;
    transition: all 0.3s ease;
}

.learning-item:active {
    transform: scale(0.98);
    box-shadow: 0 1px 2px rgba(0,0,0,0.1);
}

.learning-cover {
    position: relative;
    height: 120px;
    overflow: hidden;
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    display: flex;
    align-items: center;
    justify-content: center;
}

/* 红色书籍封面特殊处理 */
.learning-cover.redbook-cover {
    background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
    padding: 8px;
}

.learning-cover.redbook-cover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 20% 20%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 80% 80%, rgba(255,255,255,0.05) 0%, transparent 50%);
    pointer-events: none;
}

.learning-cover img {
    position: relative;
    z-index: 2;
}

/* 9:16图片在16:9容器中的适配 */
.learning-cover.redbook-cover img {
    height: 100%;
    width: auto;
    max-width: 60%;
    object-fit: contain;
    border-radius: 4px;
    box-shadow:
        0 4px 8px rgba(0,0,0,0.3),
        0 2px 4px rgba(0,0,0,0.2);
    transition: transform 0.3s ease;
}

.learning-item:active .learning-cover.redbook-cover img {
    transform: scale(0.95);
}

/* 课程封面样式优化 */
.learning-cover.course-cover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.learning-cover.course-cover::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background:
        radial-gradient(circle at 30% 30%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 70% 70%, rgba(255,255,255,0.05) 0%, transparent 50%);
    pointer-events: none;
    z-index: 1;
}

.learning-cover.course-cover img {
    width: 100%;
    height: 100%;
    object-fit: cover;
    position: relative;
    z-index: 2;
    border-radius: 4px;
    transition: transform 0.3s ease;
}

.learning-item:active .learning-cover.course-cover img {
    transform: scale(0.95);
}

/* 添加装饰元素 */
.learning-cover.redbook-cover::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 15%;
    right: 15%;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
    transform: translateY(-50%);
    z-index: 1;
}

/* 红色书籍封面的多种美化方案 */
.learning-cover.redbook-cover.style-elegant {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    border: 2px solid #ecf0f1;
}

.learning-cover.redbook-cover.style-warm {
    background: linear-gradient(135deg, #d4a574 0%, #c19a6b 100%);
}

.learning-cover.redbook-cover.style-modern {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.learning-cover.redbook-cover.style-classic {
    background: linear-gradient(135deg, #8b4513 0%, #a0522d 100%);
}

/* 添加纹理效果 */
.learning-cover.redbook-cover.textured::before {
    background-image:
        radial-gradient(circle at 25% 25%, rgba(255,255,255,0.1) 0%, transparent 50%),
        radial-gradient(circle at 75% 75%, rgba(255,255,255,0.05) 0%, transparent 50%),
        linear-gradient(45deg, rgba(255,255,255,0.02) 25%, transparent 25%),
        linear-gradient(-45deg, rgba(255,255,255,0.02) 25%, transparent 25%);
    background-size: 20px 20px, 30px 30px, 10px 10px, 10px 10px;
}

/* 书籍阴影效果增强 */
.learning-cover.redbook-cover img {
    filter: brightness(1.05) contrast(1.1);
}

.learning-cover.redbook-cover img:hover {
    filter: brightness(1.1) contrast(1.15);
}

/* 为不同类型的书籍添加不同的装饰 */
.learning-item[data-category="history"] .learning-cover.redbook-cover {
    background: linear-gradient(135deg, #c00714 0%, #8b0000 100%);
}

.learning-item[data-category="theory"] .learning-cover.redbook-cover {
    background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
}

.learning-item[data-category="culture"] .learning-cover.redbook-cover {
    background: linear-gradient(135deg, #134e5e 0%, #71b280 100%);
}

.learning-type {
    position: absolute;
    top: 8px;
    right: 8px;
    background: rgba(192, 7, 20, 0.9);
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 10px;
    font-weight: 600;
}

.learning-info {
    padding: 12px;
}

.learning-title {
    font-size: 14px;
    font-weight: 600;
    color: #333;
    margin-bottom: 8px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.learning-meta {
    font-size: 12px;
    color: #666;
    margin-bottom: 4px;
}

.learning-author {
    display: block;
    margin-bottom: 4px;
}

.learning-time {
    color: #999;
}

.learning-score {
    font-size: 12px;
    color: #ff6b35;
    font-weight: 600;
}

.learning-tabs {
    display: flex;
    background: #f8f9fa;
    border-radius: 8px;
    padding: 4px;
    margin-bottom: 16px;
}

.tab-btn {
    flex: 1;
    padding: 8px 16px;
    border: none;
    background: transparent;
    color: #666;
    border-radius: 6px;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    cursor: pointer;
}

.tab-btn.active {
    background: white;
    color: #c00714;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.loading-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #666;
    font-size: 14px;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #c00714;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 10px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;
    font-size: 14px;
}

/* 高级美化方案：书架效果 */
.learning-cover.redbook-cover.bookshelf {
    background:
        linear-gradient(90deg, #8b4513 0%, #a0522d 20%, #cd853f 40%, #daa520 60%, #b8860b 80%, #8b4513 100%),
        linear-gradient(0deg, rgba(0,0,0,0.1) 0%, transparent 100%);
    background-size: 100% 100%, 100% 20%;
    background-position: 0 0, 0 100%;
    border-top: 2px solid #654321;
    border-bottom: 3px solid #4a2c17;
}

.learning-cover.redbook-cover.bookshelf::before {
    background:
        repeating-linear-gradient(
            90deg,
            transparent 0px,
            transparent 8px,
            rgba(255,255,255,0.1) 8px,
            rgba(255,255,255,0.1) 9px
        ),
        radial-gradient(ellipse at center, rgba(255,255,255,0.1) 0%, transparent 70%);
}

/* 3D书籍效果 */
.learning-cover.redbook-cover.book-3d img {
    transform: perspective(200px) rotateY(-5deg);
    box-shadow:
        2px 0 4px rgba(0,0,0,0.3),
        4px 0 8px rgba(0,0,0,0.2),
        6px 0 12px rgba(0,0,0,0.1);
    border-left: 2px solid rgba(255,255,255,0.3);
}

.learning-item:active .learning-cover.redbook-cover.book-3d img {
    transform: perspective(200px) rotateY(-2deg) scale(0.95);
}

/* 发光效果 */
.learning-cover.redbook-cover.glowing {
    box-shadow:
        inset 0 0 20px rgba(255,255,255,0.1),
        0 0 20px rgba(192, 7, 20, 0.3);
    animation: gentle-glow 3s ease-in-out infinite alternate;
}

@keyframes gentle-glow {
    0% {
        box-shadow:
            inset 0 0 20px rgba(255,255,255,0.1),
            0 0 20px rgba(192, 7, 20, 0.3);
    }
    100% {
        box-shadow:
            inset 0 0 25px rgba(255,255,255,0.15),
            0 0 25px rgba(192, 7, 20, 0.4);
    }
}

/* 纸张纹理效果 */
.learning-cover.redbook-cover.paper-texture::before {
    background-image:
        radial-gradient(circle at 1px 1px, rgba(255,255,255,0.15) 1px, transparent 0),
        radial-gradient(circle at 3px 3px, rgba(255,255,255,0.1) 1px, transparent 0);
    background-size: 20px 20px, 40px 40px;
    opacity: 0.6;
}

/* 响应式设计 */
@media (max-width: 375px) {
    .learning-grid {
        grid-template-columns: 1fr;
        gap: 8px;
    }

    .learning-cover {
        height: 100px;
    }

    .learning-cover.redbook-cover img {
        max-width: 50%;
    }

    .learning-info {
        padding: 10px;
    }

    .learning-title {
        font-size: 13px;
    }

    .tab-btn {
        padding: 6px 12px;
        font-size: 13px;
    }
}
