<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<meta name="viewport" content="width=device-width, initial-scale=1.0">
		<title>思政一体化平台-在线学习-红色书籍</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/onlinelearning.css" />
		<link href="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/css/bootstrap.min.css" rel="stylesheet">
		<link href="https://cdn.jsdelivr.net/npm/animate.css@4.1.1/animate.min.css" rel="stylesheet">
		<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/@fortawesome/fontawesome-free@6.2.1/css/all.min.css">
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://cdn.jsdelivr.net/npm/bootstrap@5.2.3/dist/js/bootstrap.bundle.min.js"></script>
		<style>
			/* 页面加载动画 */
			.loading-overlay {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(255, 255, 255, 0.95);
				display: flex;
				justify-content: center;
				align-items: center;
				z-index: 9999;
				transition: opacity 0.3s;
			}
			
			.loading-spinner {
				width: 50px;
				height: 50px;
				border: 3px solid #f3f3f3;
				border-top: 3px solid #A65D57;
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}
			
			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}

			/* 筛选标签动画 */
			.tjbox span {
				transition: all 0.3s ease;
				position: relative;
				overflow: hidden;
				cursor: pointer;
				padding: 5px 15px;
				margin: 0 5px;
			}
			
			.tjbox span:hover {
				color: #A65D57;
			}

			/* 当前选中状态 */
			.tjbox span.tjactive {
				color: #A65D57;
				font-weight: 500;
			}

			.tjbox span.tjactive:hover {
				color: #A65D57;
			}

			/* 内容卡片动画 */
			.txtitem {
				transition: all 0.3s ease;
				opacity: 0;
				transform: translateY(20px);
				animation: fadeInUp 0.6s ease forwards;
				animation-play-state: paused;
			}
			
			@keyframes fadeInUp {
				from {
					opacity: 0;
					transform: translateY(20px);
				}
				to {
					opacity: 1;
					transform: translateY(0);
				}
			}

			.txtitem.show {
				animation-play-state: running;
			}
			
			.txtitem:hover {
				transform: translateY(-5px);
				box-shadow: 0 8px 16px rgba(0,0,0,0.1);
			}

			/* PDF弹窗动画 */
			.tcbox {
				opacity: 0;
				visibility: hidden;
				transition: all 0.3s ease;
				transform: scale(0.95);
			}
			
			.tcbox.active {
				opacity: 1;
				visibility: visible;
				transform: scale(1);
			}

			/* 返回顶部按钮动画 */
			#backtop {
				transition: all 0.3s ease;
				opacity: 0;
				visibility: hidden;
				transform: translateY(20px);
			}
			
			#backtop.visible {
				opacity: 1;
				visibility: visible;
				transform: translateY(0);
			}
			
			#backtop:hover {
				transform: translateY(-5px);
			}

			/* 下拉选择框美化 */
			.bag select {
				transition: all 0.3s ease;
				border: 1px solid #ddd;
				border-radius: 4px;
				padding: 8px 12px;
				background: white;
				cursor: pointer;
			}
			
			.bag select:hover {
				border-color: #A65D57;
				box-shadow: 0 0 0 2px rgba(166, 93, 87, 0.1);
			}

			.bag select:focus {
				outline: none;
				border-color: #A65D57;
				box-shadow: 0 0 0 3px rgba(166, 93, 87, 0.2);
			}

			/* 分页按钮动画 */
			.fybox span, .fybox label {
				transition: all 0.3s ease;
				position: relative;
				overflow: hidden;
			}
			
			.fybox span::after, .fybox label::after {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(166, 93, 87, 0.2);
				transform: translateY(100%);
				transition: transform 0.3s ease;
				z-index: -1;
			}
			
			.fybox span:hover::after, .fybox label:hover::after {
				transform: translateY(0);
			}
			
			.fybox span:active, .fybox label:active {
				transform: translateY(0);
			}

			/* 面包屑导航动画 */
			.contenttitlebox a, .contenttitlebox label {
				transition: all 0.3s ease;
			}
			
			.contenttitlebox a:hover {
				color: #A65D57;
				text-decoration: underline;
			}

			/* 文件类型标签动画 */
			.itemtype {
				display: inline-block;
				padding: 4px 8px;
				border-radius: 4px;
				font-size: 12px;
				color: #fff;
				position: absolute;
				top: 10px;
				left: 10px;
				z-index: 2;
				transition: all 0.3s ease;
			}
			
			/* 为不同文件类型设置不同颜色 */
			.itemtype.pdf {
				background: rgba(231, 76, 60, 0.8);
			}

			.itemtype.ppt, .itemtype.pptx {
				background: rgba(230, 126, 34, 0.8);
			}

			.itemtype.doc, .itemtype.docx {
				background: rgba(52, 152, 219, 0.8);
			}

			.itemtype.xls, .itemtype.xlsx {
				background: rgba(46, 204, 113, 0.8);
			}

			.itemtype.mp3, .itemtype.wav, .itemtype.ogg {
				background: rgba(155, 89, 182, 0.8);
			}

			.itemtype.mp4, .itemtype.avi, .itemtype.mov {
				background: rgba(52, 73, 94, 0.8);
			}

			.itemtype.jpg, .itemtype.jpeg, .itemtype.png, .itemtype.gif {
				background: rgba(41, 128, 185, 0.8);
			}

			/* 图片容器相对定位 */
			.topitem {
				position: relative;
				overflow: hidden;
			}
			
			/* 图片加载动画 */
			.topitem img {
				transition: all 0.5s ease;
				width: 100%;
				height: 100%;
				object-fit: cover;
			}
			
			.txtitem:hover .topitem img {
				transform: scale(1.05);
			}

			/* 标题悬停效果 */
			.title {
				transition: all 0.3s ease;
			}
			
			.txtitem:hover .title {
				color: #A65D57;
			}

			/* 更新下拉选择框样式 */
			.bag {
				display: flex;
				gap: 15px;
				align-items: center;
				padding: 15px 0;
			}

			.bag select {
				min-width: 150px;
				height: 38px;
				padding: 0 15px;
				font-size: 14px;
				color: #333;
				background-color: #fff;
				border: 1px solid #ddd;
				border-radius: 4px;
				cursor: pointer;
				appearance: none;
				-webkit-appearance: none;
				-moz-appearance: none;
				background-image: url('data:image/svg+xml;utf8,<svg fill="%23333" height="24" viewBox="0 0 24 24" width="24" xmlns="http://www.w3.org/2000/svg"><path d="M7 10l5 5 5-5z"/><path d="M0 0h24v24H0z" fill="none"/></svg>');
				background-repeat: no-repeat;
				background-position: right 8px center;
				background-size: 20px;
				transition: all 0.3s ease;
			}

			.bag select:hover {
				border-color: #A65D57;
				box-shadow: 0 0 0 2px rgba(166, 93, 87, 0.1);
			}

			.bag select:focus {
				outline: none;
				border-color: #A65D57;
				box-shadow: 0 0 0 3px rgba(166, 93, 87, 0.2);
			}

			.bag select:disabled {
				background-color: #f5f5f5;
				cursor: not-allowed;
				opacity: 0.7;
			}

			/* 下拉选项样式 */
			.bag select option {
				padding: 10px;
				font-size: 14px;
				background-color: #fff;
				color: #333;
			}

			.bag select option:hover {
				background-color: #f5f5f5;
			}

			/* 占位符样式 */
			.bag select option[value="0"] {
				color: #999;
			}

			/* 添加响应式支持 */
			@media screen and (max-width: 768px) {
				.bag {
					flex-direction: column;
					gap: 10px;
				}

				.bag select {
					width: 100%;
					max-width: none;
				}
			}

			/* 优化筛选区域样式 */
			.filter-container {
				max-height: 800px;
				overflow: hidden;
				transition: max-height 0.5s ease-in-out, opacity 0.4s ease-in-out;
				opacity: 1;
				background-color: #f8f9fa;
				border-radius: 8px;
				margin-bottom: 20px;
				padding: 15px;
				box-shadow: 0 2px 5px rgba(0,0,0,0.1);
				animation: slideIn 0.5s ease-out;
			}
			
			.filter-container.collapsed {
				max-height: 0;
				opacity: 0;
				padding-top: 0;
				padding-bottom: 0;
				margin-bottom: 0;
				border: none;
			}
			
			.tjbox {
				margin-bottom: 12px;
				border-bottom: 1px solid #eee;
				padding-bottom: 12px;
			}
			
			.tjbox:last-child {
				border-bottom: none;
			}
			
			.tjbox label {
				font-weight: 500;
				margin-right: 10px;
				min-width: 60px;
				display: inline-block;
			}
			
			/* 搜索框样式 */
			.search-box input {
				transition: all 0.3s ease;
				border: 1px solid #ddd;
			}
			
			.search-box input:focus {
				border-color: #A65D57;
				box-shadow: 0 0 0 3px rgba(166, 93, 87, 0.1);
				outline: none;
			}
			
			.search-box button:hover {
				background: #8a4e49;
			}
			
			#resetBtn:hover {
				background: #eee;
				border-color: #ccc;
			}
			
			/* 文件类型筛选器样式 - 移除单独的样式定义，使用统一的filter-tag样式 */
			/* 注：这段样式被移除，以使用统一的filter-tag样式 */
			
			/* 结果计数样式 */
			.results-count {
				margin: 10px 0;
				font-size: 14px;
				color: #666;
			}
			
			.results-count strong {
				color: #A65D57;
				font-weight: 500;
			}
			
			/* 无结果提示 */
			.no-results {
				padding: 30px;
				text-align: center;
				background: #f9f9f9;
				border-radius: 8px;
				margin: 20px 0;
				color: #666;
			}

			/* 滑入动画效果 */
			@keyframes slideIn {
				from {
					transform: translateY(-20px);
					opacity: 0;
				}
				to {
					transform: translateY(0);
					opacity: 1;
				}
			}
			
			/* 筛选控件折叠功能 */
			.filter-toggle {
				display: block;
				width: 100%;
				padding: 10px 15px;
				background-color: #4a77d4;
				color: white;
				border: none;
				border-radius: 4px;
				margin-bottom: 10px;
				text-align: left;
				font-size: 16px;
				font-weight: 500;
				cursor: pointer;
				position: relative;
				transition: background-color 0.3s;
			}
			
			.filter-toggle:hover {
				background-color: #3a67c4;
			}

			.filter-toggle.active {
				background-color: #2c4e93;
			}

			/* 筛选数量标记 */
			.filter-count {
				display: none;
				position: absolute;
				right: 15px;
				top: 50%;
				transform: translateY(-50%);
				background-color: #ff6b6b;
				color: white;
				border-radius: 50%;
				width: 20px;
				height: 20px;
				text-align: center;
				line-height: 20px;
				font-size: 12px;
			}

			/* 响应式设计优化 */
			@media screen and (max-width: 768px) {
				.filter-container {
					padding: 10px;
				}
				
				.tjbox label {
					display: block;
					margin-bottom: 5px;
				}
				
				.search-reset-container {
					flex-direction: column;
					gap: 10px;
				}
				
				.search-box {
					width: 100%;
				}
				
				#searchInput {
					width: 100% !important;
				}
				
				#resetBtn {
					width: 100%;
				}
				
				.bag {
					flex-direction: column;
					gap: 10px;
				}
				
				.bag select {
					width: 100%;
				}
				
				#filetypebox {
					display: flex;
					flex-wrap: wrap;
				}
			}
			
			@media (max-width: 576px) {
				.resource-list .resource-item {
					width: 100%;
				}
				
				.filter-label {
					display: block;
					margin-bottom: 5px;
				}
				
				.type-filters, .file-type-filters {
					display: flex;
					flex-wrap: wrap;
				}
				
				.type-filters span, .file-type-filters span {
					margin-bottom: 5px;
				}
			}

			/* 加载资源提示样式 */
			.loading-resources {
				padding: 40px;
				text-align: center;
				background: #f9f9f9;
				border-radius: 8px;
				margin: 20px 0;
			}

			.loading-resources .loading-spinner {
				margin: 0 auto;
				width: 40px;
				height: 40px;
				border: 3px solid #f3f3f3;
				border-top: 3px solid #A65D57;
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}

			.loading-resources p {
				color: #666;
				margin-top: 10px;
				font-size: 14px;
			}

			/* 平滑滚动 */
			html {
				scroll-behavior: smooth;
			}

			/* 分页切换过渡效果 */
			.fybox span, .fybox label {
				position: relative;
				overflow: hidden;
			}

			.fybox span::after, .fybox label::after {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background: rgba(166, 93, 87, 0.2);
				transform: translateY(100%);
				transition: transform 0.3s ease;
				z-index: -1;
			}

			.fybox span:hover::after, .fybox label:hover::after {
				transform: translateY(0);
			}
			
			/* === 新增筛选器现代样式 === */
			
			/* 筛选器标签样式 */
			.filter-tag {
				display: inline-block;
				background-color: #f8f9fa;
				border: 1px solid #dee2e6;
				border-radius: 4px;
				padding: 4px 10px;
				margin: 3px;
				cursor: pointer;
				transition: all 0.2s;
				font-size: 13px;
				color: #666;
				position: relative; /* 添加相对定位 */
			}
			
			.filter-tag:hover {
				background-color: #e9ecef;
				color: #A65D57;
				transform: translateY(-2px);
			}
			
			.filter-tag.active {
				background-color: #A65D57;
				color: white;
				border-color: #A65D57;
				box-shadow: 0 2px 0 #FFC107; /* 添加底部黄色阴影，实现线条效果 */
				border-bottom: 2px solid #FFC107; /* 添加底部边框 */
				padding-bottom: 2px; /* 调整内边距，保持整体高度不变 */
			}
			
			/* 添加标签选中时的底部线条样式 */
			.filter-tag.active::after {
				content: '';
				position: absolute;
				bottom: -3px;
				left: 0;
				width: 100%;
				height: 2px;
				background-color: #FFC107; /* 黄色线条 */
				display: block;
			}
			
			/* 确保类别选中样式也应用相同的效果 */
			.tjbox span.tjactive {
				color: #A65D57;
				font-weight: 500;
				box-shadow: 0 2px 0 #FFC107; /* 添加底部黄色阴影 */
				border-bottom: 2px solid #FFC107; /* 添加底部边框 */
			}
			
			/* 移除之前添加的伪元素样式 */
			/* .tjbox span.tjactive::after {
				content: '';
				position: absolute;
				bottom: -3px;
				left: 0;
				width: 100%;
				height: 2px;
				background-color: #FFC107;
				display: block;
			} */
			
			/* 筛选容器 */
			.modern-filter-container {
				border-radius: 5px;
				box-shadow: 0 2px 8px rgba(0,0,0,0.05);
				transition: all 0.3s;
				overflow: hidden;
				background-color: #fbfbfb;
			}
			
			.filter-section {
				border-bottom: 1px solid rgba(0,0,0,0.05);
				padding: 8px 12px;
				transition: all 0.25s;
			}
			
			.filter-section:last-child {
				border-bottom: none;
			}
			
			.filter-section:hover {
				background-color: rgba(0,0,0,0.01);
			}
			
			.filter-label {
				font-weight: 500;
				color: #495057;
				margin-bottom: 5px;
				display: flex;
				align-items: center;
				font-size: 13px;
			}
			
			.filter-label i {
				margin-right: 6px;
				color: #A65D57;
				font-size: 14px;
			}
			
			/* 筛选折叠按钮 */
			.filter-accordion-button {
				width: 100%;
				background-color: #A65D57;
				color: white;
				border: none;
				border-radius: 4px;
				padding: 8px 15px;
				text-align: left;
				font-weight: 500;
				position: relative;
				transition: all 0.3s;
				margin-bottom: 10px;
				display: flex;
				align-items: center;
				justify-content: space-between;
				box-shadow: 0 2px 5px rgba(166, 93, 87, 0.2);
				font-size: 14px;
			}
			
			.filter-accordion-button:hover {
				background-color: #8a4e49;
			}
			
			.filter-accordion-button i {
				transition: transform 0.3s;
			}
			
			.filter-accordion-button.collapsed i {
				transform: rotate(180deg);
			}
			
			.filter-badge {
				background-color: #fff;
				color: #A65D57;
				border-radius: 20px;
				padding: 2px 6px;
				font-size: 11px;
				margin-left: 10px;
				border: 1px solid rgba(255,255,255,0.5);
			}
			
			/* 下拉选择框样式 */
			.chapter-select {
				border-radius: 4px;
				padding: 6px 12px;
				border: 1px solid #ced4da;
				width: 100%;
				transition: all 0.2s;
				background-color: white;
				font-size: 13px;
				margin-bottom: 5px;
				appearance: none;
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='16' height='16' fill='%23A65D57' viewBox='0 0 16 16'%3E%3Cpath d='M7.247 11.14L2.451 5.658C1.885 5.013 2.345 4 3.204 4h9.592a1 1 0 0 1 .753 1.659l-4.796 5.48a1 1 0 0 1-1.506 0z'/%3E%3C/svg%3E");
				background-repeat: no-repeat;
				background-position: right 8px center;
				background-size: 16px;
				padding-right: 30px;
				color: #555;
			}
			
			.chapter-select:focus {
				border-color: #A65D57;
				box-shadow: 0 0 0 2px rgba(166, 93, 87, 0.1);
				outline: none;
			}
			
			.chapter-select:disabled {
				background-color: #f5f5f5;
				cursor: not-allowed;
				opacity: 0.7;
			}

			.chapter-select option {
				padding: 8px;
				font-size: 14px;
			}

			/* 响应式调整 */
			@media (max-width: 768px) {
				.chapter-selects-group .col-md-3 {
					margin-bottom: 10px;
				}
				
				.chapter-select {
					height: 40px;
				}
			}
			
			/* 重置按钮 */
			.modern-reset-btn {
				border-radius: 4px;
				height: 36px;
				background-color: #f8f9fa;
				border: 1px solid #ced4da;
				padding: 0 15px;
				color: #495057;
				transition: all 0.2s;
				font-size: 13px;
				width: 100%;
				margin-top: 5px;
			}
			
			.modern-reset-btn:hover {
				background-color: #e9ecef;
			}
			
			/* 结果统计 */
			.results-meta {
				background-color: #f8f9fa;
				border-radius: 4px;
				padding: 8px 12px;
				margin: 10px 0;
				display: flex;
				align-items: center;
				color: #495057;
				font-size: 13px;
			}
			
			.results-meta i {
				margin-right: 8px;
				color: #A65D57;
			}
			
			.results-meta strong {
				color: #A65D57;
				font-weight: 500;
			}
			
			/* 响应式调整 */
			@media (max-width: 768px) {
				.chapter-select {
					margin-bottom: 8px;
				}
				
				.filter-tags-container {
					display: flex;
					flex-wrap: wrap;
				}
			}

			/* 文件类型筛选器样式 */
			#filetypebox span {
				display: inline-block;
				background-color: #f8f9fa;
				border: 1px solid #dee2e6;
				border-radius: 4px;
				padding: 4px 10px;
				margin: 3px;
				cursor: pointer;
				transition: all 0.2s;
				font-size: 13px;
				color: #666;
			}

			#filetypebox span:hover {
				background-color: #e9ecef;
				color: #A65D57;
				transform: translateY(-2px);
			}

			#filetypebox span.tjactive, 
			#filetypebox span.active {
				background-color: #A65D57;
				color: white;
				border-color: #A65D57;
			}

			/* 筛选器标签样式 */
			.filter-tag {
				display: inline-block;
				background-color: #f8f9fa;
				border: 1px solid #dee2e6;
				border-radius: 4px;
				padding: 4px 10px;
				margin: 3px;
				cursor: pointer;
				transition: all 0.2s;
				font-size: 13px;
				color: #666;
				position: relative; /* 添加相对定位 */
			}

			.filter-tag:hover {
				background-color: #e9ecef;
				color: #A65D57;
				transform: translateY(-2px);
			}

			.filter-tag.active {
				background-color: #A65D57;
				color: white;
				border-color: #A65D57;
				box-shadow: 0 2px 0 #FFC107; /* 添加底部黄色阴影，实现线条效果 */
				border-bottom: 2px solid #FFC107; /* 添加底部边框 */
				padding-bottom: 2px; /* 调整内边距，保持整体高度不变 */
			}

			/* 添加标签选中时的底部线条样式 */
			.filter-tag.active::after {
				content: '';
				position: absolute;
				bottom: -3px;
				left: 0;
				width: 100%;
				height: 2px;
				background-color: #FFC107; /* 黄色线条 */
				display: block;
			}

			/* 筛选标签容器样式修改，确保标签在一行显示 */
			.filter-tags-container {
				display: flex;
				flex-wrap: nowrap; /* 改为nowrap，防止换行 */
				margin-top: 2px;
				overflow-x: auto; /* 添加水平滚动 */
				padding-bottom: 5px; /* 为滚动条留出空间 */
				-webkit-overflow-scrolling: touch; /* 平滑滚动 */
			}

			/* 隐藏滚动条但保留功能 */
			.filter-tags-container::-webkit-scrollbar {
				height: 4px;
			}

			.filter-tags-container::-webkit-scrollbar-thumb {
				background: rgba(166, 93, 87, 0.2);
				border-radius: 4px;
			}

			/* 课程筛选标签容器特殊样式 - 完全重写，使其与其他标签容器一致 */
			#xkbox {
				display: inline-block !important; /* 改为inline-block，与其他标签容器一致 */
				margin-top: 0; /* 移除顶部间距 */
				white-space: nowrap; /* 确保同一行显示 */
				overflow-x: auto; /* 允许水平滚动 */
				padding-bottom: 5px; /* 滚动条空间 */
				max-height: 40px; /* 默认高度 */
				transition: all 0.3s ease;
			}

			/* 课程标签展开状态 */
			#xkbox.expanded {
				max-height: 300px; /* 展开后的高度 */
				overflow-y: auto; /* 垂直滚动 */
				flex-wrap: wrap;
				width: 100%;
				display: flex !important;
			}

			/* 确保课程标签合适大小和样式，与其他标签保持一致 */
			#xkbox span {
				display: inline-block;
				background-color: #f8f9fa;
				border: 1px solid #dee2e6;
				border-radius: 4px;
				padding: 4px 10px;
				margin: 3px;
				cursor: pointer;
				transition: all 0.2s;
				font-size: 13px;
				color: #666;
			}

			#xkbox span:hover {
				background-color: #e9ecef;
				color: #A65D57;
				transform: translateY(-2px);
			}

			#xkbox span.active {
				background-color: #A65D57;
				color: white;
				border-color: #A65D57;
			}

			/* 更多按钮样式 */
			.course-toggle-btn {
				display: inline-block;
				background-color: #f8f9fa;
				border: 1px solid #dee2e6;
				border-radius: 4px;
				padding: 4px 10px;
				margin: 3px;
				cursor: pointer;
				transition: all 0.2s;
				font-size: 13px;
				color: #666;
				line-height: 1.5;
			}

			.course-toggle-btn:hover {
				background-color: #e9ecef;
				color: #A65D57;
			}

			.course-toggle-btn.expanded {
				background-color: #e9ecef;
			}

			.course-toggle-btn i {
				margin-left: 3px;
				transition: transform 0.3s ease;
			}

			.course-toggle-btn.expanded i {
				transform: rotate(180deg);
			}
		</style>
	</head>
	<body class="index">
		<!-- 添加加载动画 -->
		<div class="loading-overlay">
			<div class="loading-spinner"></div>
		</div>
		<div class="tcbox" id="tcbox">
			
			<div class="topviewsss">
				
				<a onclick="closetc()">关闭</a>
				<label id="pdfname"></label>
			</div>
			<div class="panel-body pdfbox">
			    <a id="pdf" class="media" href=""></a>  
			</div>
		</div>
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="contenttitlebox">
			<div>
				<a id="zxxx" href="onlinelearning.html">在线学习</a>
				<img src="img/jtright.png" />
				<label class="aa" id="hsname">教学资源</label>
			</div>
		</div>
		<div class="content">
			<div class="contenttopview">
				<div class="contentitem" id="classbox">

				</div>
			</div>
			
			<!-- 现代筛选控件 -->
			<button class="filter-accordion-button" type="button" id="filterToggle">
				<span><i class="fas fa-filter"></i> 筛选条件</span>
				<div>
					<span class="filter-badge d-none" id="filterCount">0</span>
					<i class="fas fa-chevron-down"></i>
				</div>
			</button>
			
			<!-- 新的筛选器容器 -->
			<div class="modern-filter-container p-3 bg-white mb-4" id="filterContainer">
				<!-- 类别筛选 -->
				<div class="filter-section">
					<div class="filter-label"><i class="fas fa-th-large"></i> 类别</div>
					<div class="filter-tags-container">
						<span onclick="selectlb(this)" id="qblb" class="filter-tag active" data-id="111">全部</span>
						<div id="qblbbox" class="d-inline">
							<span onclick="selectlb(this)" class="filter-tag" data-id="1">高校课程</span>
							<span onclick="selectlb(this)" class="filter-tag" data-id="0">教学成果</span>
						</div>
					</div>
				</div>
				
				<!-- 属性筛选 -->
				<div class="filter-section">
					<div class="filter-label"><i class="fas fa-tags"></i> 属性</div>
					<div class="filter-tags-container">
						<span onclick="selectsx(this)" id="qbsx" class="filter-tag active" data-id="0">全部</span>
						<div id="sxbox" class="d-inline"></div>
					</div>
				</div>
				
				<!-- 类型筛选 -->
				<div class="filter-section">
					<div class="filter-label"><i class="fas fa-bookmark"></i> 类型</div>
					<div class="filter-tags-container">
						<span onclick="selectlx(this)" id="qblx" class="filter-tag active" data-id="0">全部</span>
						<div id="lxbox" class="d-inline"></div>
					</div>
				</div>
				
				<!-- 课程筛选 -->
				<div class="filter-section">
					<div class="filter-label"><i class="fas fa-book"></i> 课程与章节</div>
					<div class="chapter-selects-group">
						<div class="row">
							<div class="col-md-3">
								<select id="courseSelect" class="chapter-select" onchange="courseChange(this)">
									<option value="0">请选择课程</option>
								</select>
							</div>
							<div class="col-md-3">
								<select id="z" class="chapter-select" onchange="zonchange(this)" disabled>
									<option value="0">请选择章</option>
								</select>
							</div>
							<div class="col-md-3">
								<select id="j" class="chapter-select" onchange="jonchange(this)" disabled>
									<option value="0">请选择节</option>
								</select>
							</div>
							<div class="col-md-3">
								<select id="xj" class="chapter-select" onchange="xjonchange(this)" disabled>
									<option value="0">请选择小节</option>
								</select>
							</div>
						</div>
					</div>
				</div>
				
				<!-- 文件格式筛选 -->
				<div class="filter-section">
					<div class="filter-label"><i class="fas fa-file"></i> 文件格式</div>
					<div class="filter-tags-container">
						<span onclick="selectFileType(this)" id="qbfiletype" class="filter-tag active" data-id="all">全部</span>
						<div id="filetypebox" class="d-inline"></div>
					</div>
				</div>
				
				<!-- 重置按钮 -->
				<div class="filter-section">
					<button id="resetBtn" class="btn modern-reset-btn" onclick="resetFilters()">
						<i class="fas fa-redo-alt me-1"></i> 重置筛选条件
					</button>
				</div>
			</div>
			
			<!-- 资源计数 -->
			<div class="results-meta">
				<i class="fas fa-info-circle"></i> 找到 <strong id="resourceCount">0</strong> 条资源
			</div>
			
			<div class="sjbox" id="list">

			</div>
			<div class="fybox" id="fyq">
				<span id="sy">首页</span>
				<span id="syy">上一页</span>
				<div class="num" id="num">
				</div>
				<span id="xyy">下一页</span>
				<span id="wy">尾页</span>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/clipboard.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let pageindex = 1
			let pagesize = 25
			let pages = 0
			let sxid = null
			let lxid = null
			let xkid = null
			let zid = null
			let jid = null
			let xjid = null
			let fileTypeId = null // 文件类型ID
			let xkdata = null //学科章节树
			let zdata = null //章列表
			let jdata = null //节列表
			let xjdata = null //小节列表
			let classid = null
			let allFileTypes = [] // 所有文件类型列表
			
			let mtype = null //类型
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				getclassid()
				getclass('onlinelearning.html')
				getshuxin()
				getleixing()
				getxkzj()
				getfooterlink()
				getclassch()
				
				// 设置筛选器折叠/展开功能
				$("#filterToggle").on('click', function() {
					$("#filterContainer").collapse('toggle');
					$(this).toggleClass('collapsed');
				});
				
				// 初始化Bootstrap的工具提示
				const tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
				tooltipTriggerList.map(function (tooltipTriggerEl) {
					return new bootstrap.Tooltip(tooltipTriggerEl);
				});
			})
			
			let time1 = null
			let pflid = null
			let xkidss = null
			let infoid = null
			function tiaozhuan() {
				if(JSON.parse(userinfo).roleName == '学生'){
					let time2 = Date.now()
					let value = time2 - time1
					var days = parseInt(value / (1000 * 60 * 60 * 24))
					var hours = parseInt((value % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
					var minutes = parseInt((value % (1000 * 60 * 60)) / (1000 * 60))
					var seconds = Math.floor((value % (1000 * 60)) / 1000)
					let json = {
						infoId: infoid, //信息id
						categoryId: pflid, //所属分类id
						totalInfo: "1", //总时长，总页数
						positioning: days+"天"+hours+"时"+minutes+"分"+seconds+"秒", //学习了多久，多少页    
						progress: "", //进度 百分比
						type: '在线学习',
						learningTime: value,
						sectionId: xkidss//学科ID
					}
					window.localStorage.setItem("jilu",JSON.stringify(json))
					if(window.localStorage.getItem("jilu")){
						$.ajax({
							url: baseurl + "/study/record/add",
							type: 'post',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: window.localStorage.getItem("jilu"),
							dataType: 'json',
							success: (res) => {
								window.localStorage.clear()
							}
						})
					}
				}
			}
			function getfooterlink(){
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item)=>{
								html+='<a href="'+item.url+'" target="_blank">'+item.name+'</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
			function getclassid() {
				$.ajax({
					url: baseurl + "/web/category/teaching",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							classid = res.data[0].id
							if(getUrlParam('type')){
								if(getUrlParam('type') == '00'){
									mtype = 0
									$("#qblb").attr("class", "")
									let allsx = $("#qblbbox span")
									for (let i = 0; i < allsx.length; i++) {
										if ($(allsx[i]).attr("data-id") == mtype) {
											$(allsx[i]).attr('class', "tjactive")
										} else {
											$(allsx[i]).attr('class', "")
										}
									}
								}
							}else{
								mtype = null
							}
							getpages()
						}
					}
				})
			}
			function getclassch(){
				$.ajax({
					url: baseurl + "/web/category/teacher",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							classdate = res.data[0]
							getlist(res.data[0])
						}
					}
				})
			}
			function getlist(classdate) {
				let classhtml = ""
				for (let i = 0; i < classdate.children.length; i++) {
					if (classdate.children[i].name == '教学资源') {
						classhtml += '<a class="titleactive" href="' + classdate.children[i].redirectUrl + '?id=' + classdate
							.children[i].id + '">' + classdate.children[i].name + '</a>'
						$("#hsname").html(classdate.children[i].name)
					} else {
						classhtml += '<a href="' + classdate.children[i].redirectUrl + '?id=' + classdate.children[i].id + '">' +
							classdate.children[i].name + '</a>'
					}
				}
				$("#classbox").html(classhtml)
			}
			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function getshuxin() { //获取属性列表
				$.ajax({
					url: baseurl + "/web/attributes",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let sxhtml = ""
							for (let i = 0; i < res.data.length; i++) {
								if (res.data[i].id == sxid) {
									sxhtml += '<span onclick="selectsx(this)" class="filter-tag active" data-id="' + res.data[
										i].id + '">' + res.data[
										i].name + '</span>'
								} else {
									sxhtml += '<span onclick="selectsx(this)" class="filter-tag" data-id="' + res.data[i].id + '">' +
										res.data[i].name +
										'</span>'
								}
							}
							$("#sxbox").html(sxhtml)
						}
					}
				})
			}

			function getleixing() { //获取类型列表
				$.ajax({
					url: baseurl + "/web/types",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let lxhtml = ""
							for (let i = 0; i < res.data.length; i++) {
								if (res.data[i].id == sxid) {
									lxhtml += '<span class="filter-tag active" onclick="selectlx(this)" data-id="' + res.data[
										i].id + '">' + res.data[
										i].name + '</span>'
								} else {
									lxhtml += '<span onclick="selectlx(this)" class="filter-tag" data-id="' + res.data[i].id + '">' +
										res.data[i].name +
										'</span>'
								}
							}
							$("#lxbox").html(lxhtml)
						}
					}
				})
			}

			function getxkzj() { //获取学科章节列表
				$.ajax({
					url: baseurl + "/web/project/section/list/tree",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							xkdata = res.data;
							
							// 初始化课程选择框
							let courseHtml = "<option value='0'>请选择课程</option>";
							xkdata.forEach((item) => {
								courseHtml += `<option value="${item.id}">${item.name}</option>`;
							});
							$("#courseSelect").html(courseHtml);
							
							// 初始化课程标签
							let xkhtml = "";
							xkdata.forEach((item) => {
								xkhtml += `<span onclick="selectxk(this)" class="filter-tag" data-id="${item.id}">${item.name}</span>`;
							});
							$("#xkbox").html(xkhtml);
						}
					}
				});
			}

			function getpages() {
				$.ajax({
					url: baseurl + "/web/course",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						categoryId: classid,
						pageSize: pagesize,
						pageNum: pageindex,
						projectId: xkid,
						sectionId: zid,
						nodeId: jid,
						barId: xjid,
						attributesId: sxid,
						typeId: lxid,
						type: mtype,
						attachType: fileTypeId
					},
					dataType: 'json',
					beforeSend: function() {
						// 显示轻量级加载提示
						$("#list").html('<div class="loading-resources"><div class="loading-spinner"></div><p>正在加载资源...</p></div>');
					},
					success: (res) => {
						if (res.code == '200') {
							if (res.data && res.data.list && res.data.list.length > 0) {
								let types = new Set();
								res.data.list.forEach(item => {
									if (item.attachType) {
										types.add(item.attachType.toLowerCase());
									}
								});
								updateFileTypeFilters(Array.from(types));
							}
							
							let html = ""
							res.data.list.map((item, index) => {
								// 添加延迟显示动画
								const delay = index % 10 * 50; // 每行5个，错开显示时间
								
								if(item.attachType === 'pdf'){
									html +=
										'<div class="txtitem" style="animation-delay: '+delay+'ms;" onclick="showpdf(this)" data-cid="'+item.courceId+'" data-id="'+item.metaId+'"><div class="topitem"><div class="itemtype '+item.attachType+'">' +item.attachType+
										'</div>'
										if(item.coverPath!=null){
											html+='<img data-src="'+baseurl+item.coverPath[0]+'" />'
										}
										html+='<div class="icobox">' +
										'<label><img class="zzico" src="img/fbr.png" /><span>'+item.author+'</span></label>' +
										'<label><img class="gklico" src="img/gkl.png" />'+item.view+'</label>' +
										'</div></div><div class="bottomitem">' +
										'<div class="title">'+item.title+'</div>' +
										'<div class="zz">'+item.introduction+'</div></div></div>'
								}else{
									html +=
										'<div class="txtitem" style="animation-delay: '+delay+'ms;"><div class="topitem" onclick="inxx(this)" data-id="'+item.metaId+'"><div class="itemtype '+item.attachType+'">' +item.attachType+
										'</div>'
										if(item.coverPath!=null){
											html+='<img data-src="'+baseurl+item.coverPath[0]+'" />'
										}
										html+='<div class="icobox">' +
										'<label><img class="zzico" src="img/fbr.png" /><span>'+item.author+'</span></label>' +
										'<label><img class="gklico" src="img/gkl.png" />'+item.view+'</label>' +
										'</div></div><div class="bottomitem">' +
										'<div class="title">'+item.title+'</div>' +
										'<div class="zz">'+item.introduction+'</div>'+
										''+
										'</div></div>'
								}
								
							})
							$("#list").html(html)

							// 延迟显示卡片动画
							setTimeout(function() {
								$('.txtitem').addClass('show');
							}, 100);

							// 图片懒加载
							lazyLoad();

							// 显示资源数量
							$("#resourceCount").text(res.data.total || 0);

							pages = res.data.pages
							if (pages > 1) {
								let numhtml = ""
								for (let a = 1; a <= pages; a++) {
									if (pageindex == a) {
										numhtml += '<label class="actinum" onclick="getnewlist(' + a + ')">' + a +
											'</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
							
							// 没有结果时显示提示
							if (res.data.total === 0) {
								$("#list").html('<div class="alert alert-info p-4 text-center"><i class="fas fa-info-circle me-2"></i>未找到匹配的资源，请尝试其他筛选条件</div>');
							}
							
							// 更新筛选器计数
							updateFilterCountBadge();
						}
					}
				})
			}

			// 图片懒加载函数优化
			function lazyLoad() {
				$('.topitem img').each(function() {
					if ($(this).attr('data-src') && !$(this).attr('src')) {
						let img = $(this);
						if (img.offset().top < $(window).height() + $(window).scrollTop() + 200) {
							img.attr('src', img.attr('data-src'));
							// 添加加载状态
							img.css('opacity', '0');
							img.on('load', function() {
								img.css('opacity', '1');
							});
						}
					}
				});
			}
			
			let clipboard = new ClipboardJS('.copybtn');
			clipboard.on('success', function(e) {
				e.clearSelection();
				cocoMessage.success(1000, "复制成功！")
			});
			
			clipboard.on('error', function(e) {
				cocoMessage.error(1000, "复制失败！")
			});
			function closetc(){
				$("#tcbox").hide()
				this.tiaozhuan()
			}
			function showpdf(pdf){
				if(!userinfo){
					window.location.href ='https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				
				$.ajax({
					url: baseurl + "/course/view/"+$(pdf).attr("data-cid"),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
					}
				})
				$.ajax({
					url: baseurl + "/course/meta/"+$(pdf).attr("data-id"),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							time1 = Date.now()
							infoid = $(pdf).attr("data-id")
							xkidss = res.data.projectId
							$("#pdfname").html(res.data.cmsResourcesCourseMetaList[0].attachName)
							$("#tcbox").show()
							$("#pdf").attr("href",baseurl + res.data.cmsResourcesCourseMetaList[0].attachPath)
							$('a.media').media({width: 800,height: 850})
						}
					}
				})
			}
			function inxx(item){
				window.location.href = "onlinelearning4.html?id="+$(item).attr("data-id")
			}
			function getnewlist(index){
				if(index == pageindex){
					if(index == pages){
						cocoMessage.warning(1000, "已经是第最后一页了！")
					}else{
						cocoMessage.warning(1000, "已经是第"+index+"页了！")
					}
				}else{
					pageindex = index
					getpages()
				}
			}
			function selectlb(e){ //选择类别
				if($(e).attr("data-id") == '111'){
					//全部
					mtype = null
					let alllb = $("#qblbbox span")
					for (let i = 0; i < alllb.length; i++) {
						$(alllb[i]).removeClass("active")
					}
					$("#qblb").addClass("active")
				}else{
					$("#qblb").removeClass("active")
					mtype = $(e).attr("data-id")
					let allsx = $("#qblbbox span")
					for (let i = 0; i < allsx.length; i++) {
						if ($(allsx[i]).attr("data-id") == mtype) {
							$(allsx[i]).addClass("active")
						} else {
							$(allsx[i]).removeClass("active")
						}
					}
				}
				pageindex = 1
				getnewpages()
			}
			function selectsx(e) { //选择属性
				if ($(e).attr("data-id") == '0') {
					sxid = null
					let allsx = $("#sxbox span")
					for (let i = 0; i < allsx.length; i++) {
						$(allsx[i]).removeClass("active")
					}
					$("#qbsx").addClass("active")
				} else {
					$("#qbsx").removeClass("active")
					sxid = $(e).attr("data-id")
					let allsx = $("#sxbox span")
					for (let i = 0; i < allsx.length; i++) {
						if ($(allsx[i]).attr("data-id") == sxid) {
							$(allsx[i]).addClass("active")
						} else {
							$(allsx[i]).removeClass("active")
						}
					}
				}
				pageindex = 1
				getnewpages()
			}

			function selectlx(e) { //选择类型
				if ($(e).attr("data-id") == '0') {
					lxid = null
					let alllx = $("#lxbox span")
					for (let i = 0; i < alllx.length; i++) {
						$(alllx[i]).removeClass("active")
					}
					$("#qblx").addClass("active")
				} else {
					$("#qblx").removeClass("active")
					lxid = $(e).attr("data-id")
					let alllx = $("#lxbox span")
					for (let i = 0; i < alllx.length; i++) {
						if ($(alllx[i]).attr("data-id") == lxid) {
							$(alllx[i]).addClass("active")
						} else {
							$(alllx[i]).removeClass("active")
						}
					}
				}
				pageindex = 1
				getnewpages()
			}

			function selectxk(e) { //选择学科
				const courseId = $(e).attr("data-id");
				$("#courseSelect").val(courseId);
				courseChange($("#courseSelect")[0]);
			}

			function zonchange(e) {
				$("#j").val('0')
				$("#xj").val('0')
				$("#xj").html("<option value='0'>请选择小节</option>")
				
				// 禁用小节选择框
				$("#xj").prop('disabled', true);
				
				zdata.map((item) => {
					if (item.id == $(e).val()) {
						jdata = item.children
					}
				})
				zid = $(e).val()
				
				if (jdata && jdata.length > 0) {
					let jhtml = "<option value='0'>请选择节</option>"
					jdata.map((item) => {
						jhtml += "<option value='" + item.id + "'>" + item.name + "</option>"
					})
					$("#j").html(jhtml)
					$("#j").prop('disabled', false);
				} else {
					$("#j").html("<option value='0'>请选择节</option>")
					$("#j").prop('disabled', true);
				}
				getnewpages()
			}

			function jonchange(e) {
				$("#xj").val('0')
				jdata.map((item) => {
					if (item.id == $(e).val()) {
						xjdata = item.children
					}
				})
				jid = $(e).val()
				
				if (xjdata && xjdata.length > 0) {
					let xjhtml = "<option value='0'>请选择小节</option>"
					xjdata.map((item) => {
						xjhtml += "<option value='" + item.id + "'>" + item.name + "</option>"
					})
					$("#xj").html(xjhtml)
					$("#xj").prop('disabled', false);
				} else {
					$("#xj").html("<option value='0'>请选择小节</option>")
					$("#xj").prop('disabled', true);
				}
				getnewpages()
			}

			function xjonchange(e) {
				xjid = $(e).val()
				getnewpages()
			}

			function getnewpages() {
				
				getpages()
			}

			$("#select div").on('click', function() {
				$("#selecttxt").html($(this).html())
				$("#select").hide()
			})

			function showselect() {
				$("#select").show()
			}

			// 更新文件类型筛选器的选项
			function updateFileTypeFilters(types) {
				// 只有在首次加载或有新文件类型时才更新筛选器
				if (!allFileTypes.length || types.some(type => !allFileTypes.includes(type))) {
					allFileTypes = [...new Set([...allFileTypes, ...types])];
					// 更新文件类型筛选器
					let typeHtml = '';
					allFileTypes.forEach(type => {
						if (type && type.trim() !== '') {
							let displayType = type.toUpperCase();
							typeHtml += `<span onclick="selectFileType(this)" class="filter-tag" data-id="${type}">${displayType}</span>`;
						}
					});
					// 只有在有新类型时才更新DOM
					if (typeHtml !== '') {
						$("#filetypebox").html(typeHtml);
					}
				}
			}
			
			// 选择文件类型
			function selectFileType(e) {
				if ($(e).attr("data-id") === 'all') {
					// 全部
					fileTypeId = null;
					// 移除所有文件类型标签的active类
					$("#filetypebox span").removeClass("active");
					$("#qbfiletype").addClass("active");
				} else {
					$("#qbfiletype").removeClass("active");
					fileTypeId = $(e).attr("data-id");
					// 移除所有文件类型标签的active类，然后添加到当前选中的标签
					$("#filetypebox span").removeClass("active");
					$(e).addClass("active");
				}
				
				pageindex = 1;
				updateFilterCountBadge();
				getpages();
			}
			
			// 重置所有筛选条件
			function resetFilters() {
				// 重置各种筛选参数
				mtype = null;
				sxid = null;
				lxid = null;
				xkid = null;
				zid = null;
				jid = null;
				xjid = null;
				fileTypeId = null;
				
				// 重置类别筛选
				$("#qblb").addClass("active");
				$("#qblbbox span").removeClass("active");
				
				// 重置属性筛选
				$("#qbsx").addClass("active");
				$("#sxbox span").removeClass("active");
				
				// 重置类型筛选
				$("#qblx").addClass("active");
				$("#lxbox span").removeClass("active");
				
				// 重置课程筛选
				$("#qbxk").addClass("active");
				$("#xkbox span").removeClass("active");
				
				// 重置文件类型筛选
				$("#qbfiletype").addClass("active");
				$("#filetypebox span").removeClass("active");
				
				// 重置章节选择
				$("#z").val('0');
				$("#j").val('0');
				$("#xj").val('0');
				$("#j").prop('disabled', true);
				$("#xj").prop('disabled', true);
				
				pageindex = 1;
				getpages();
				
				// 显示重置成功提示
				cocoMessage.success(1000, "已重置所有筛选条件");
			}
			
			// 更新筛选条件数量标记
			function updateFilterCountBadge() {
				const activeFilters = getActiveFiltersCount();
				const filterCount = document.getElementById('filterCount');
				
				if (activeFilters > 0) {
					filterCount.textContent = activeFilters;
					$(filterCount).removeClass('d-none');
				} else {
					$(filterCount).addClass('d-none');
				}
			}
			
			// 获取激活的筛选条件数量
			function getActiveFiltersCount() {
				let count = 0;
				
				// 类别筛选
				if (mtype !== null) count++;
				
				// 属性筛选
				if (sxid !== null) count++;
				
				// 类型筛选
				if (lxid !== null) count++;
				
				// 课程筛选
				if (xkid !== null) count++;
				
				// 章节筛选
				if (zid && zid !== '0') count++;
				if (jid && jid !== '0') count++;
				if (xjid && xjid !== '0') count++;
				
				// 文件类型筛选
				if (fileTypeId !== null) count++;
				
				return count;
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				// 页面加载完成后隐藏加载动画
				setTimeout(function() {
					$('.loading-overlay').fadeOut();
				}, 500);

				// 监听滚动事件
				$(window).scroll(function() {
					// 返回顶部按钮显示/隐藏
					if ($(window).scrollTop() > 600) {
						$("#backtop").addClass('visible').show();
					} else {
						$("#backtop").removeClass('visible').hide();
					}

					// 图片懒加载
					lazyLoad();
				});

				// 平滑滚动到顶部
				$("#backtop").on('click', function() {
					$("body,html").animate({
						scrollTop: 0
					}, 300);
				});

				// 修改PDF显示函数
				window.showpdf = function(pdf) {
					if(!userinfo) {
						window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html';
						return;
					}
					
					$.ajax({
						url: baseurl + "/course/view/"+$(pdf).attr("data-cid"),
						type: 'GET',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						dataType: 'json',
						success: (res) => {}
					});
					
					$.ajax({
						url: baseurl + "/course/meta/"+$(pdf).attr("data-id"),
						type: 'GET',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						dataType: 'json',
						success: (res) => {
							if (res.code == '200') {
								time1 = Date.now();
								infoid = $(pdf).attr("data-id");
								xkidss = res.data.projectId;
								$("#pdfname").html(res.data.cmsResourcesCourseMetaList[0].attachName);
								$("#tcbox").addClass('active').show();
								$("#pdf").attr("href", baseurl + res.data.cmsResourcesCourseMetaList[0].attachPath);
								$('a.media').media({width: 800, height: 850});
							}
						}
					});
				};

				// 修改关闭PDF函数
				window.closetc = function() {
					$("#tcbox").removeClass('active').fadeOut(300);
					tiaozhuan();
				};
			});
		</script>
		
			// 筛选器折叠/展开功能
			document.addEventListener('DOMContentLoaded', function() {
				const filterToggle = document.getElementById('filterToggle');
				const filterContainer = document.getElementById('filterContainer');
				
				// 初始化筛选条件数量指示器
				updateFilterCountBadge();
				
				// 切换筛选器显示/隐藏
				filterToggle.addEventListener('click', function() {
					filterContainer.classList.toggle('collapsed');
					filterToggle.classList.toggle('active');
				});
				
				// 更新筛选条件数量标记
				function updateFilterCountBadge() {
					const activeFilters = getActiveFiltersCount();
					const filterCount = document.querySelector('.filter-count');
					
					if (activeFilters > 0) {
						filterCount.textContent = activeFilters;
						filterCount.style.display = 'inline-block';
					} else {
						filterCount.style.display = 'none';
					}
				}
				
				// 获取激活的筛选条件数量
				function getActiveFiltersCount() {
					let count = 0;
					
					// 类别筛选
					if (mtype !== null) count++;
					
					// 属性筛选
					if (sxid !== null) count++;
					
					// 类型筛选
					if (lxid !== null) count++;
					
					// 课程筛选
					if (xkid !== null) count++;
					
					// 章节筛选
					if (zid && zid !== '0') count++;
					if (jid && jid !== '0') count++;
					if (xjid && xjid !== '0') count++;
					
					// 文件类型筛选
					if (fileTypeId !== null) count++;
					
					return count;
				}
				
				// 监听筛选条件变化，实时更新指示器
				// 为所有筛选按钮添加点击后更新指示器的逻辑
				const originalSelectLb = window.selectlb;
				window.selectlb = function(e) {
					originalSelectLb(e);
					setTimeout(updateFilterCountBadge, 100);
				};
				
				const originalSelectSx = window.selectsx;
				window.selectsx = function(e) {
					originalSelectSx(e);
					setTimeout(updateFilterCountBadge, 100);
				};
				
				const originalSelectLx = window.selectlx;
				window.selectlx = function(e) {
					originalSelectLx(e);
					setTimeout(updateFilterCountBadge, 100);
				};
				
				const originalSelectXk = window.selectxk;
				window.selectxk = function(e) {
					originalSelectXk(e);
					setTimeout(updateFilterCountBadge, 100);
				};
				
				const originalSelectFileType = window.selectFileType;
				window.selectFileType = function(e) {
					originalSelectFileType(e);
					setTimeout(updateFilterCountBadge, 100);
				};
				
				const originalResetFilters = window.resetFilters;
				window.resetFilters = function() {
					originalResetFilters();
					setTimeout(updateFilterCountBadge, 100);
				};
				
				// 监听下拉选择框变化
				$("#z, #j, #xj").on('change', function() {
					setTimeout(updateFilterCountBadge, 100);
				});
				
				// 课程标签展开/收起功能
				const courseToggleBtn = document.getElementById('courseToggleBtn');
				const xkbox = document.getElementById('xkbox');
				
				courseToggleBtn.addEventListener('click', function() {
					// 切换xkbox的expanded类
					xkbox.classList.toggle('expanded');
					this.classList.toggle('expanded');
					
					// 更新按钮文本
					if (xkbox.classList.contains('expanded')) {
						this.innerHTML = '收起 <i class="fas fa-chevron-up"></i>';
					} else {
						this.innerHTML = '更多 <i class="fas fa-chevron-down"></i>';
					}
				});
			});
		</script>
		
			// 修改课程选择相关函数
			function courseChange(e) {
				const courseId = $(e).val();
				if (courseId === '0') {
					xkid = null;
					// 禁用所有下级选择框
					$("#z, #j, #xj").prop('disabled', true).val('0');
					$("#z").html("<option value='0'>请选择章</option>");
					$("#j").html("<option value='0'>请选择节</option>");
					$("#xj").html("<option value='0'>请选择小节</option>");
				} else {
					xkid = courseId;
					// 启用章选择框
					$("#z").prop('disabled', false);
					
					// 获取章节数据
					let foundCourse = false;
					xkdata.forEach((item) => {
						if (item.id == xkid) {
							zdata = item.children;
							foundCourse = true;
						}
					});
					
					if (foundCourse && zdata && zdata.length > 0) {
						let zhtml = "<option value='0'>请选择章</option>";
						zdata.forEach((item) => {
							zhtml += `<option value="${item.id}">${item.name}</option>`;
						});
						$("#z").html(zhtml);
					} else {
						$("#z").html("<option value='0'>请选择章</option>");
						$("#z").prop('disabled', true);
					}
				}
				
				pageindex = 1;
				updateFilterCountBadge();
				getpages();
			}

			// 修改初始化函数，添加课程选择框的初始化
			$(function() {
				// ... existing code ...
				
				// 初始化课程选择框
				if (xkdata && xkdata.length > 0) {
					let courseHtml = "<option value='0'>请选择课程</option>";
					xkdata.map((item) => {
						courseHtml += "<option value='" + item.id + "'>" + item.name + "</option>";
					});
					$("#courseSelect").html(courseHtml);
				}
				
				// ... existing code ...
			});

			// 修改章节选择相关函数
			function zonchange(e) {
				$("#j").val('0');
				$("#xj").val('0');
				$("#xj").html("<option value='0'>请选择小节</option>");
				$("#xj").prop('disabled', true);
				
				zdata.map((item) => {
					if (item.id == $(e).val()) {
						jdata = item.children;
					}
				});
				zid = $(e).val();
				
				if (jdata && jdata.length > 0) {
					let jhtml = "<option value='0'>请选择节</option>";
					jdata.map((item) => {
						jhtml += "<option value='" + item.id + "'>" + item.name + "</option>";
					});
					$("#j").html(jhtml);
					$("#j").prop('disabled', false);
				} else {
					$("#j").html("<option value='0'>请选择节</option>");
					$("#j").prop('disabled', true);
				}
				getnewpages();
			}

			function jonchange(e) {
				$("#xj").val('0');
				jdata.map((item) => {
					if (item.id == $(e).val()) {
						xjdata = item.children;
					}
				});
				jid = $(e).val();
				
				if (xjdata && xjdata.length > 0) {
					let xjhtml = "<option value='0'>请选择小节</option>";
					xjdata.map((item) => {
						xjhtml += "<option value='" + item.id + "'>" + item.name + "</option>";
					});
					$("#xj").html(xjhtml);
					$("#xj").prop('disabled', false);
				} else {
					$("#xj").html("<option value='0'>请选择小节</option>");
					$("#xj").prop('disabled', true);
				}
				getnewpages();
			}

			function xjonchange(e) {
				xjid = $(e).val();
				getnewpages();
			}
		</script>
	</body>
</html>
