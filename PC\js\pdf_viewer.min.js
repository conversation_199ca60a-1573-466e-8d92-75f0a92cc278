!function(e,t){"object"==typeof exports&&"object"==typeof module?module.exports=t():"function"==typeof define&&define.amd?define("pdfjs-dist/web/pdf_viewer",[],t):"object"==typeof exports?exports["pdfjs-dist/web/pdf_viewer"]=t():e["pdfjs-dist/web/pdf_viewer"]=e.pdfjsViewer=t()}(globalThis,()=>(()=>{"use strict";var i=[,(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DefaultXfaLayerFactory=t.DefaultTextLayerFactory=t.DefaultStructTreeLayerFactory=t.DefaultAnnotationLayerFactory=t.DefaultAnnotationEditorLayerFactory=void 0;var s=i(2),u=i(5),g=i(4),p=i(6),n=i(8),o=i(9),a=i(10);t.<PERSON><PERSON>ult<PERSON>nnotationLayerFactory=class{createAnnotationLayerBuilder({pageDiv:e,pdfPage:t,annotationStorage:i=null,imageResourcesPath:n="",renderForms:a=!0,l10n:r=g.NullL10n,enableScripting:s=!1,hasJSActionsPromise:o=null,mouseState:l=null,fieldObjectsPromise:h=null,annotationCanvasMap:d=null,accessibilityManager:c=null}){return new u.AnnotationLayerBuilder({pageDiv:e,pdfPage:t,imageResourcesPath:n,renderForms:a,linkService:new p.SimpleLinkService,l10n:r,annotationStorage:i,enableScripting:s,hasJSActionsPromise:o,fieldObjectsPromise:h,mouseState:l,annotationCanvasMap:d,accessibilityManager:c})}};t.DefaultAnnotationEditorLayerFactory=class{createAnnotationEditorLayerBuilder({uiManager:e=null,pageDiv:t,pdfPage:i,accessibilityManager:n=null,l10n:a,annotationStorage:r=null}){return new s.AnnotationEditorLayerBuilder({uiManager:e,pageDiv:t,pdfPage:i,accessibilityManager:n,l10n:a,annotationStorage:r})}};t.DefaultStructTreeLayerFactory=class{createStructTreeLayerBuilder({pdfPage:e}){return new n.StructTreeLayerBuilder({pdfPage:e})}};t.DefaultTextLayerFactory=class{createTextLayerBuilder({textLayerDiv:e,pageIndex:t,viewport:i,enhanceTextSelection:n=!1,eventBus:a,highlighter:r,accessibilityManager:s=null}){return new o.TextLayerBuilder({textLayerDiv:e,pageIndex:t,viewport:i,enhanceTextSelection:n,eventBus:a,highlighter:r,accessibilityManager:s})}};t.DefaultXfaLayerFactory=class{createXfaLayerBuilder({pageDiv:e,pdfPage:t,annotationStorage:i=null}){return new a.XfaLayerBuilder({pageDiv:e,pdfPage:t,annotationStorage:i,linkService:new p.SimpleLinkService})}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AnnotationEditorLayerBuilder=void 0;var n=i(3),a=i(4);t.AnnotationEditorLayerBuilder=class{#uiManager;constructor(e){this.pageDiv=e.pageDiv,this.pdfPage=e.pdfPage,this.annotationStorage=e.annotationStorage||null,this.accessibilityManager=e.accessibilityManager,this.l10n=e.l10n||a.NullL10n,this.annotationEditorLayer=null,this.div=null,this._cancelled=!1,this.#uiManager=e.uiManager}async render(e,t="display"){if("display"===t&&!this._cancelled){e=e.clone({dontFlip:!0});if(this.div)return this.annotationEditorLayer.update({viewport:e}),void this.show();this.div=document.createElement("div"),this.div.className="annotationEditorLayer",this.div.tabIndex=0,this.pageDiv.append(this.div),this.annotationEditorLayer=new n.AnnotationEditorLayer({uiManager:this.#uiManager,div:this.div,annotationStorage:this.annotationStorage,accessibilityManager:this.accessibilityManager,pageIndex:this.pdfPage._pageIndex,l10n:this.l10n,viewport:e});e={viewport:e,div:this.div,annotations:null,intent:t};this.annotationEditorLayer.render(e)}}cancel(){this._cancelled=!0,this.destroy()}hide(){this.div&&(this.div.hidden=!0)}show(){this.div&&(this.div.hidden=!1)}destroy(){this.div&&(this.pageDiv=null,this.annotationEditorLayer.destroy(),this.div.remove())}}},e=>{let t;t="undefined"!=typeof window&&window["pdfjs-dist/build/pdf"]?window["pdfjs-dist/build/pdf"]:require("../build/pdf.js"),e.exports=t},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.NullL10n=void 0,t.fixupLangCode=function(e){return n[e?.toLowerCase()]||e},t.getL10nFallback=a;const i={of_pages:"of {{pagesCount}}",page_of_pages:"({{pageNumber}} of {{pagesCount}})",document_properties_kb:"{{size_kb}} KB ({{size_b}} bytes)",document_properties_mb:"{{size_mb}} MB ({{size_b}} bytes)",document_properties_date_string:"{{date}}, {{time}}",document_properties_page_size_unit_inches:"in",document_properties_page_size_unit_millimeters:"mm",document_properties_page_size_orientation_portrait:"portrait",document_properties_page_size_orientation_landscape:"landscape",document_properties_page_size_name_a3:"A3",document_properties_page_size_name_a4:"A4",document_properties_page_size_name_letter:"Letter",document_properties_page_size_name_legal:"Legal",document_properties_page_size_dimension_string:"{{width}} × {{height}} {{unit}} ({{orientation}})",document_properties_page_size_dimension_name_string:"{{width}} × {{height}} {{unit}} ({{name}}, {{orientation}})",document_properties_linearized_yes:"Yes",document_properties_linearized_no:"No",print_progress_percent:"{{progress}}%","toggle_sidebar.title":"Toggle Sidebar","toggle_sidebar_notification2.title":"Toggle Sidebar (document contains outline/attachments/layers)",additional_layers:"Additional Layers",page_landmark:"Page {{page}}",thumb_page_title:"Page {{page}}",thumb_page_canvas:"Thumbnail of Page {{page}}",find_reached_top:"Reached top of document, continued from bottom",find_reached_bottom:"Reached end of document, continued from top","find_match_count[one]":"{{current}} of {{total}} match","find_match_count[other]":"{{current}} of {{total}} matches","find_match_count_limit[one]":"More than {{limit}} match","find_match_count_limit[other]":"More than {{limit}} matches",find_not_found:"Phrase not found",error_version_info:"PDF.js v{{version}} (build: {{build}})",error_message:"Message: {{message}}",error_stack:"Stack: {{stack}}",error_file:"File: {{file}}",error_line:"Line: {{line}}",rendering_error:"An error occurred while rendering the page.",page_scale_width:"Page Width",page_scale_fit:"Page Fit",page_scale_auto:"Automatic Zoom",page_scale_actual:"Actual Size",page_scale_percent:"{{scale}}%",loading:"Loading…",loading_error:"An error occurred while loading the PDF.",invalid_file_error:"Invalid or corrupted PDF file.",missing_file_error:"Missing PDF file.",unexpected_response_error:"Unexpected server response.",printing_not_supported:"Warning: Printing is not fully supported by this browser.",printing_not_ready:"Warning: The PDF is not fully loaded for printing.",web_fonts_disabled:"Web fonts are disabled: unable to use embedded PDF fonts.",free_text_default_content:"Enter text…",editor_free_text_aria_label:"FreeText Editor",editor_ink_aria_label:"Ink Editor",editor_ink_canvas_aria_label:"User-created image"};function a(e,t){switch(e){case"find_match_count":e=`find_match_count[${1===t.total?"one":"other"}]`;break;case"find_match_count_limit":e=`find_match_count_limit[${1===t.limit?"one":"other"}]`}return i[e]||""}const n={en:"en-US",es:"es-ES",fy:"fy-NL",ga:"ga-IE",gu:"gu-IN",hi:"hi-IN",hy:"hy-AM",nb:"nb-NO",ne:"ne-NP",nn:"nn-NO",pa:"pa-IN",pt:"pt-PT",sv:"sv-SE",zh:"zh-CN"};t.NullL10n={async getLanguage(){return"en-us"},async getDirection(){return"ltr"},async get(e,t=null,i=a(e,t)){return i=i,(n=t)?i.replace(/\{\{\s*(\w+)\s*\}\}/g,(e,t)=>t in n?n[t]:"{{"+t+"}}"):i;var n},async translate(e){}}},(e,t,p)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.AnnotationLayerBuilder=void 0;var r=p(3),p=p(4);t.AnnotationLayerBuilder=class{constructor({pageDiv:e,pdfPage:t,linkService:i,downloadManager:n,annotationStorage:a=null,imageResourcesPath:r="",renderForms:s=!0,l10n:o=p.NullL10n,enableScripting:l=!1,hasJSActionsPromise:h=null,fieldObjectsPromise:d=null,mouseState:c=null,annotationCanvasMap:u=null,accessibilityManager:g=null}){this.pageDiv=e,this.pdfPage=t,this.linkService=i,this.downloadManager=n,this.imageResourcesPath=r,this.renderForms=s,this.l10n=o,this.annotationStorage=a,this.enableScripting=l,this._hasJSActionsPromise=h,this._fieldObjectsPromise=d,this._mouseState=c,this._annotationCanvasMap=u,this._accessibilityManager=g,this.div=null,this._cancelled=!1}async render(e,t="display"){var[t,i=!1,n=null]=await Promise.all([this.pdfPage.getAnnotations({intent:t}),this._hasJSActionsPromise,this._fieldObjectsPromise]);if(!this._cancelled&&0!==t.length){const a={viewport:e.clone({dontFlip:!0}),div:this.div,annotations:t,page:this.pdfPage,imageResourcesPath:this.imageResourcesPath,renderForms:this.renderForms,linkService:this.linkService,downloadManager:this.downloadManager,annotationStorage:this.annotationStorage,enableScripting:this.enableScripting,hasJSActions:i,fieldObjects:n,mouseState:this._mouseState,annotationCanvasMap:this._annotationCanvasMap,accessibilityManager:this._accessibilityManager};this.div?r.AnnotationLayer.update(a):(this.div=document.createElement("div"),this.div.className="annotationLayer",this.pageDiv.append(this.div),a.div=this.div,r.AnnotationLayer.render(a),this.l10n.translate(this.div))}}cancel(){this._cancelled=!0}hide(){this.div&&(this.div.hidden=!0)}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.SimpleLinkService=t.PDFLinkService=t.LinkTarget=void 0;var o=i(7);const s={NONE:0,SELF:1,BLANK:2,PARENT:3,TOP:4};function n(e,{url:t,target:i,rel:n,enabled:a=!0}={}){if(!t||"string"!=typeof t)throw new Error('A valid "url" parameter must provided.');t=(0,o.removeNullCharacters)(t);a?e.href=e.title=t:(e.href="",e.title="Disabled: "+t,e.onclick=()=>!1);let r="";switch(i){case s.NONE:break;case s.SELF:r="_self";break;case s.BLANK:r="_blank";break;case s.PARENT:r="_parent";break;case s.TOP:r="_top"}e.target=r,e.rel="string"==typeof n?n:"noopener noreferrer nofollow"}t.LinkTarget=s;class l{#pagesRefCache=new Map;constructor({eventBus:e,externalLinkTarget:t=null,externalLinkRel:i=null,ignoreDestinationZoom:n=!1}={}){this.eventBus=e,this.externalLinkTarget=t,this.externalLinkRel=i,this.externalLinkEnabled=!0,this._ignoreDestinationZoom=n,this.baseUrl=null,this.pdfDocument=null,this.pdfViewer=null,this.pdfHistory=null}setDocument(e,t=null){this.baseUrl=t,this.pdfDocument=e,this.#pagesRefCache.clear()}setViewer(e){this.pdfViewer=e}setHistory(e){this.pdfHistory=e}get pagesCount(){return this.pdfDocument?this.pdfDocument.numPages:0}get page(){return this.pdfViewer.currentPageNumber}set page(e){this.pdfViewer.currentPageNumber=e}get rotation(){return this.pdfViewer.pagesRotation}set rotation(e){this.pdfViewer.pagesRotation=e}#goToDestinationHelper(t,i=null,n){const a=n[0];let e;if("object"==typeof a&&null!==a){if(!(e=this._cachedPageNumber(a)))return void this.pdfDocument.getPageIndex(a).then(e=>{this.cachePageRef(e+1,a),this.#goToDestinationHelper(t,i,n)}).catch(()=>{console.error(`PDFLinkService.#goToDestinationHelper: "${a}" is not `+`a valid page reference, for dest="${t}".`)})}else{if(!Number.isInteger(a))return void console.error(`PDFLinkService.#goToDestinationHelper: "${a}" is not `+`a valid destination reference, for dest="${t}".`);e=a+1}!e||e<1||e>this.pagesCount?console.error(`PDFLinkService.#goToDestinationHelper: "${e}" is not `+`a valid page number, for dest="${t}".`):(this.pdfHistory&&(this.pdfHistory.pushCurrentPosition(),this.pdfHistory.push({namedDest:i,explicitDest:n,pageNumber:e})),this.pdfViewer.scrollPageIntoView({pageNumber:e,destArray:n,ignoreDestinationZoom:this._ignoreDestinationZoom}))}async goToDestination(i){if(this.pdfDocument){let e,t;t="string"==typeof i?(e=i,await this.pdfDocument.getDestination(i)):(e=null,await i),Array.isArray(t)?this.#goToDestinationHelper(i,e,t):console.error(`PDFLinkService.goToDestination: "${t}" is not `+`a valid destination array, for dest="${i}".`)}}goToPage(e){var t;this.pdfDocument&&(t="string"==typeof e&&this.pdfViewer.pageLabelToPageNumber(e)||0|e,Number.isInteger(t)&&0<t&&t<=this.pagesCount?(this.pdfHistory&&(this.pdfHistory.pushCurrentPosition(),this.pdfHistory.pushPage(t)),this.pdfViewer.scrollPageIntoView({pageNumber:t})):console.error(`PDFLinkService.goToPage: "${e}" is not a valid page.`))}addLinkAttributes(e,t,i=!1){n(e,{url:t,target:i?s.BLANK:this.externalLinkTarget,rel:this.externalLinkRel,enabled:this.externalLinkEnabled})}getDestinationHash(e){if("string"==typeof e){if(0<e.length)return this.getAnchorUrl("#"+escape(e))}else if(Array.isArray(e)){e=JSON.stringify(e);if(0<e.length)return this.getAnchorUrl("#"+escape(e))}return this.getAnchorUrl("")}getAnchorUrl(e){return(this.baseUrl||"")+e}setHash(i){if(this.pdfDocument){let e,t;if(i.includes("=")){const r=(0,o.parseQueryString)(i);if(r.has("search")&&this.eventBus.dispatch("findfromurlhash",{source:this,query:r.get("search").replace(/"/g,""),phraseSearch:"true"===r.get("phrase")}),r.has("page")&&(e=0|r.get("page")||1),r.has("zoom")){var n=r.get("zoom").split(",");const s=n[0];var a=parseFloat(s);s.includes("Fit")?"Fit"===s||"FitB"===s?t=[null,{name:s}]:"FitH"===s||"FitBH"===s||"FitV"===s||"FitBV"===s?t=[null,{name:s},1<n.length?0|n[1]:null]:"FitR"===s?5!==n.length?console.error('PDFLinkService.setHash: Not enough parameters for "FitR".'):t=[null,{name:s},0|n[1],0|n[2],0|n[3],0|n[4]]:console.error(`PDFLinkService.setHash: "${s}" is not a valid zoom value.`):t=[null,{name:"XYZ"},1<n.length?0|n[1]:null,2<n.length?0|n[2]:null,a?a/100:s]}t?this.pdfViewer.scrollPageIntoView({pageNumber:e||this.page,destArray:t,allowNegativeOffset:!0}):e&&(this.page=e),r.has("pagemode")&&this.eventBus.dispatch("pagemode",{source:this,mode:r.get("pagemode")}),r.has("nameddest")&&this.goToDestination(r.get("nameddest"))}else{t=unescape(i);try{t=JSON.parse(t),Array.isArray(t)||(t=t.toString())}catch(e){}"string"==typeof t||l.#isValidExplicitDestination(t)?this.goToDestination(t):console.error(`PDFLinkService.setHash: "${unescape(i)}" is not a valid destination.`)}}}executeNamedAction(e){switch(e){case"GoBack":this.pdfHistory?.back();break;case"GoForward":this.pdfHistory?.forward();break;case"NextPage":this.pdfViewer.nextPage();break;case"PrevPage":this.pdfViewer.previousPage();break;case"LastPage":this.page=this.pagesCount;break;case"FirstPage":this.page=1}this.eventBus.dispatch("namedaction",{source:this,action:e})}cachePageRef(e,t){t&&(t=0===t.gen?t.num+"R":t.num+"R"+t.gen,this.#pagesRefCache.set(t,e))}_cachedPageNumber(e){if(!e)return null;e=0===e.gen?e.num+"R":e.num+"R"+e.gen;return this.#pagesRefCache.get(e)||null}isPageVisible(e){return this.pdfViewer.isPageVisible(e)}isPageCached(e){return this.pdfViewer.isPageCached(e)}static#isValidExplicitDestination(t){if(!Array.isArray(t))return!1;var i=t.length;if(i<2)return!1;var e=t[0];if(!("object"==typeof e&&Number.isInteger(e.num)&&Number.isInteger(e.gen)||Number.isInteger(e)&&0<=e))return!1;e=t[1];if("object"!=typeof e||"string"!=typeof e.name)return!1;let n=!0;switch(e.name){case"XYZ":if(5!==i)return!1;break;case"Fit":case"FitB":return 2===i;case"FitH":case"FitBH":case"FitV":case"FitBV":if(3!==i)return!1;break;case"FitR":if(6!==i)return!1;n=!1;break;default:return!1}for(let e=2;e<i;e++){var a=t[e];if(!("number"==typeof a||n&&null===a))return!1}return!0}}t.PDFLinkService=l;t.SimpleLinkService=class{constructor(){this.externalLinkEnabled=!0}get pagesCount(){return 0}get page(){return 0}set page(e){}get rotation(){return 0}set rotation(e){}async goToDestination(e){}goToPage(e){}addLinkAttributes(e,t,i=0){n(e,{url:t,enabled:this.externalLinkEnabled})}getDestinationHash(e){return"#"}getAnchorUrl(e){return"#"}setHash(e){}executeNamedAction(e){}cachePageRef(e,t){}isPageVisible(e){return!0}isPageCached(e){return!0}}},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.animationStarted=t.VERTICAL_PADDING=t.UNKNOWN_SCALE=t.TextLayerMode=t.SpreadMode=t.SidebarView=t.ScrollMode=t.SCROLLBAR_PADDING=t.RenderingStates=t.RendererType=t.ProgressBar=t.PresentationModeState=t.OutputScale=t.MIN_SCALE=t.MAX_SCALE=t.MAX_AUTO_SCALE=t.DEFAULT_SCALE_VALUE=t.DEFAULT_SCALE_DELTA=t.DEFAULT_SCALE=t.AutoPrintRegExp=void 0,t.apiPageLayoutToViewerModes=function(e){let t=n.VERTICAL,i=a.NONE;switch(e){case"SinglePage":t=n.PAGE;break;case"OneColumn":break;case"TwoPageLeft":t=n.PAGE;case"TwoColumnLeft":i=a.ODD;break;case"TwoPageRight":t=n.PAGE;case"TwoColumnRight":i=a.EVEN}return{scrollMode:t,spreadMode:i}},t.apiPageModeToSidebarView=function(e){switch(e){case"UseNone":return i.NONE;case"UseThumbs":return i.THUMBS;case"UseOutlines":return i.OUTLINE;case"UseAttachments":return i.ATTACHMENTS;case"UseOC":return i.LAYERS}return i.NONE},t.approximateFraction=function(e){if(Math.floor(e)===e)return[e,1];var t=1/e;{if(8<t)return[1,8];if(Math.floor(t)===t)return[1,t]}var i=1<e?t:e;let n=0,a=1,r=1,s=1;for(;;){var o=n+r,l=a+s;if(8<l)break;i<=o/l?(r=o,s=l):(n=o,a=l)}let h;h=i-n/a<r/s-i?i===e?[n,a]:[a,n]:i===e?[r,s]:[s,r];return h},t.backtrackBeforeAllVisibleElements=w,t.binarySearchFirstItem=E,t.docStyle=void 0,t.getActiveOrFocusedElement=function(){let e=document,t=e.activeElement||e.querySelector(":focus");for(;t?.shadowRoot;)e=t.shadowRoot,t=e.activeElement||e.querySelector(":focus");return t},t.getPageSizeInches=function({view:e,userUnit:t,rotate:i}){var[e,n,a,r]=e,i=i%180!=0,a=(a-e)/72*t,e=(r-n)/72*t;return{width:i?e:a,height:i?a:e}},t.getVisibleElements=function({scrollEl:e,views:t,sortByVisibility:i=!1,horizontal:n=!1,rtl:a=!1}){const r=e.scrollTop,s=r+e.clientHeight,o=e.scrollLeft,l=o+e.clientWidth;const h=[],d=new Set,c=t.length;let u=E(t,n?function(e){var t=(e=e.div).offsetLeft+e.clientLeft,e=t+e.clientWidth;return a?t<l:e>o}:function(e){return(e=e.div).offsetTop+e.clientTop+e.clientHeight>r});0<u&&u<c&&!n&&(u=w(u,t,r));let g=n?l:-1;for(let e=u;e<c;e++){var p=t[e],f=p.div,_=f.offsetLeft+f.clientLeft,v=f.offsetTop+f.clientTop,m=f.clientWidth,f=f.clientHeight,y=_+m,P=v+f;if(-1===g)s<=P&&(g=P);else if((n?_:v)>g)break;P<=r||s<=v||y<=o||_>=l||(P=Math.max(0,r-v)+Math.max(0,P-s),y=Math.max(0,o-_)+Math.max(0,y-l),f=(f-P)/f*(P=(m-y)/m)*100|0,h.push({id:p.id,x:_,y:v,view:p,percent:f,widthPercent:100*P|0}),d.add(p.id))}var e=h[0],b=h.at(-1);i&&h.sort(function(e,t){var i=e.percent-t.percent;return.001<Math.abs(i)?-i:e.id-t.id});return{first:e,last:b,views:h,ids:d}},t.isPortraitOrientation=function(e){return e.width<=e.height},t.isValidRotation=function(e){return Number.isInteger(e)&&e%90==0},t.isValidScrollMode=function(e){return Number.isInteger(e)&&Object.values(n).includes(e)&&e!==n.UNKNOWN},t.isValidSpreadMode=function(e){return Number.isInteger(e)&&Object.values(a).includes(e)&&e!==a.UNKNOWN},t.noContextMenuHandler=function(e){e.preventDefault()},t.normalizeWheelEventDelta=function(e){let t=o(e);0===e.deltaMode?t/=900:1===e.deltaMode&&(t/=30);return t},t.normalizeWheelEventDirection=o,t.parseQueryString=function(e){const t=new Map;for(var[i,n]of new URLSearchParams(e))t.set(i.toLowerCase(),n);return t},t.removeNullCharacters=function(e,t=!1){if("string"!=typeof e)return console.error("The argument must be a string."),e;t&&(e=e.replace(s," "));return e.replace(r,"")},t.roundToDivide=function(e,t){var i=e%t;return 0==i?e:Math.round(e-i+t)},t.scrollIntoView=function(i,n,a=!1){let r=i.offsetParent;if(r){let e=i.offsetTop+i.clientTop,t=i.offsetLeft+i.clientLeft;for(;r.clientHeight===r.scrollHeight&&r.clientWidth===r.scrollWidth||a&&(r.classList.contains("markedContent")||"hidden"===getComputedStyle(r).overflow);)if(e+=r.offsetTop,t+=r.offsetLeft,!(r=r.offsetParent))return;n&&(void 0!==n.top&&(e+=n.top),void 0!==n.left&&(t+=n.left,r.scrollLeft=t)),r.scrollTop=e}else console.error("offsetParent is not set -- cannot scroll")},t.watchScroll=function(i,n){function e(e){r=r||window.requestAnimationFrame(function(){r=null;var e=i.scrollLeft,t=a.lastX,t=(e!==t&&(a.right=t<e),a.lastX=e,i.scrollTop),e=a.lastY;t!==e&&(a.down=e<t),a.lastY=t,n(a)})}const a={right:!0,down:!0,lastX:i.scrollLeft,lastY:i.scrollTop,_eventHandler:e};let r=null;return i.addEventListener("scroll",e,!0),a};t.DEFAULT_SCALE_VALUE="auto",t.DEFAULT_SCALE=1,t.DEFAULT_SCALE_DELTA=1.1,t.MIN_SCALE=.1,t.MAX_SCALE=10,t.UNKNOWN_SCALE=0,t.MAX_AUTO_SCALE=1.25,t.SCROLLBAR_PADDING=40,t.VERTICAL_PADDING=5,t.RenderingStates={INITIAL:0,RUNNING:1,PAUSED:2,FINISHED:3};t.PresentationModeState={UNKNOWN:0,NORMAL:1,CHANGING:2,FULLSCREEN:3};const i={UNKNOWN:-1,NONE:0,THUMBS:1,OUTLINE:2,ATTACHMENTS:3,LAYERS:4};t.SidebarView=i;t.RendererType={CANVAS:"canvas",SVG:"svg"};t.TextLayerMode={DISABLE:0,ENABLE:1,ENABLE_ENHANCE:2};const n={UNKNOWN:-1,VERTICAL:0,HORIZONTAL:1,WRAPPED:2,PAGE:3},a=(t.ScrollMode=n,{UNKNOWN:-1,NONE:0,ODD:1,EVEN:2});t.SpreadMode=a;t.AutoPrintRegExp=/\bprint\s*\(/;t.OutputScale=class{constructor(){var e=window.devicePixelRatio||1;this.sx=e,this.sy=e}get scaled(){return 1!==this.sx||1!==this.sy}};const r=/\x00/g,s=/[\x01-\x1F]/g;function E(e,t,i=0){let n=i,a=e.length-1;if(a<0||!t(e[a]))return e.length;if(t(e[n]))return n;for(;n<a;){var r=n+a>>1;t(e[r])?a=r:n=1+r}return n}function w(t,i,e){if(t<2)return t;let n=i[t].div,a=n.offsetTop+n.clientTop;a>=e&&(n=i[t-1].div,a=n.offsetTop+n.clientTop);for(let e=t-2;0<=e&&!((n=i[e].div).offsetTop+n.clientTop+n.clientHeight<=a);--e)t=e;return t}function o(e){let t=Math.hypot(e.deltaX,e.deltaY);e=Math.atan2(e.deltaY,e.deltaX);return t=-.25*Math.PI<e&&e<.75*Math.PI?-t:t}var l=new Promise(function(e){window.requestAnimationFrame(e)});t.animationStarted=l;const h=document.documentElement.style;t.docStyle=h;t.ProgressBar=class{#classList=null;#percent=0;#visible=!0;constructor(e){if(1<arguments.length)throw new Error("ProgressBar no longer accepts any additional options, please use CSS rules to modify its appearance instead.");e=document.getElementById(e);this.#classList=e.classList}get percent(){return this.#percent}set percent(e){var t,i;this.#percent=(t=0,i=100,Math.min(Math.max(e,t),i)),isNaN(e)?this.#classList.add("indeterminate"):(this.#classList.remove("indeterminate"),h.setProperty("--progressBar-percent",this.#percent+"%"))}setWidth(e){!e||0<(e=e.parentNode.offsetWidth-e.offsetWidth)&&h.setProperty("--progressBar-end-offset",e+"px")}hide(){this.#visible&&(this.#visible=!1,this.#classList.add("hidden"))}show(){this.#visible||(this.#visible=!0,this.#classList.remove("hidden"))}}},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.StructTreeLayerBuilder=void 0;const r={Document:null,DocumentFragment:null,Part:"group",Sect:"group",Div:"group",Aside:"note",NonStruct:"none",P:null,H:"heading",Title:null,FENote:"note",Sub:"group",Lbl:null,Span:null,Em:null,Strong:null,Link:"link",Annot:"note",Form:"form",Ruby:null,RB:null,RT:null,RP:null,Warichu:null,WT:null,WP:null,L:"list",LI:"listitem",LBody:null,Table:"table",TR:"row",TH:"columnheader",TD:"cell",THead:"columnheader",TBody:null,TFoot:null,Caption:null,Figure:"figure",Formula:null,Artifact:null},s=/^H(\d+)$/;t.StructTreeLayerBuilder=class{constructor({pdfPage:e}){this.pdfPage=e}render(e){return this._walk(e)}_setAttributes(e,t){void 0!==e.alt&&t.setAttribute("aria-label",e.alt),void 0!==e.id&&t.setAttribute("aria-owns",e.id),void 0!==e.lang&&t.setAttribute("lang",e.lang)}_walk(e){if(!e)return null;const t=document.createElement("span");if("role"in e){const n=e["role"];var i=n.match(s);i?(t.setAttribute("role","heading"),t.setAttribute("aria-level",i[1])):r[n]&&t.setAttribute("role",r[n])}if(this._setAttributes(e,t),e.children)if(1===e.children.length&&"id"in e.children[0])this._setAttributes(e.children[0],t);else for(const a of e.children)t.append(this._walk(a));return t}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TextLayerBuilder=void 0;var n=i(3);t.TextLayerBuilder=class{constructor({textLayerDiv:e,eventBus:t,pageIndex:i,viewport:n,highlighter:a=null,enhanceTextSelection:r=!1,accessibilityManager:s=null}){this.textLayerDiv=e,this.eventBus=t,this.textContent=null,this.textContentItemsStr=[],this.textContentStream=null,this.renderingDone=!1,this.pageNumber=i+1,this.viewport=n,this.textDivs=[],this.textLayerRenderTask=null,this.highlighter=a,this.enhanceTextSelection=r,this.accessibilityManager=s,this._bindMouse()}_finishRendering(){if(this.renderingDone=!0,!this.enhanceTextSelection){const e=document.createElement("div");e.className="endOfContent",this.textLayerDiv.append(e)}this.eventBus.dispatch("textlayerrendered",{source:this,pageNumber:this.pageNumber,numTextDivs:this.textDivs.length})}render(e=0){if((this.textContent||this.textContentStream)&&!this.renderingDone){this.cancel(),this.textDivs.length=0,this.highlighter?.setTextMapping(this.textDivs,this.textContentItemsStr),this.accessibilityManager?.setTextMapping(this.textDivs);const t=document.createDocumentFragment();this.textLayerRenderTask=(0,n.renderTextLayer)({textContent:this.textContent,textContentStream:this.textContentStream,container:t,viewport:this.viewport,textDivs:this.textDivs,textContentItemsStr:this.textContentItemsStr,timeout:e,enhanceTextSelection:this.enhanceTextSelection}),this.textLayerRenderTask.promise.then(()=>{this.textLayerDiv.append(t),this._finishRendering(),this.highlighter?.enable(),this.accessibilityManager?.enable()},function(e){})}}cancel(){this.textLayerRenderTask&&(this.textLayerRenderTask.cancel(),this.textLayerRenderTask=null),this.highlighter?.disable(),this.accessibilityManager?.disable()}setTextContentStream(e){this.cancel(),this.textContentStream=e}setTextContent(e){this.cancel(),this.textContent=e}_bindMouse(){const n=this.textLayerDiv;let a=null;n.addEventListener("mousedown",e=>{if(this.enhanceTextSelection&&this.textLayerRenderTask)return this.textLayerRenderTask.expandTextDivs(!0),void(a&&(clearTimeout(a),a=null));const t=n.querySelector(".endOfContent");var i;t&&(e.target!==n&&"none"!==window.getComputedStyle(t).getPropertyValue("-moz-user-select")&&(i=n.getBoundingClientRect(),e=Math.max(0,(e.pageY-i.top)/i.height),t.style.top=(100*e).toFixed(2)+"%"),t.classList.add("active"))}),n.addEventListener("mouseup",()=>{if(this.enhanceTextSelection&&this.textLayerRenderTask)a=setTimeout(()=>{this.textLayerRenderTask&&this.textLayerRenderTask.expandTextDivs(!1),a=null},300);else{const e=n.querySelector(".endOfContent");e&&(e.style.top="",e.classList.remove("active"))}})}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.XfaLayerBuilder=void 0;var a=i(3);t.XfaLayerBuilder=class{constructor({pageDiv:e,pdfPage:t,annotationStorage:i=null,linkService:n,xfaHtml:a=null}){this.pageDiv=e,this.pdfPage=t,this.annotationStorage=i,this.linkService=n,this.xfaHtml=a,this.div=null,this._cancelled=!1}render(i,n="display"){if("print"!==n)return this.pdfPage.getXfa().then(e=>{if(this._cancelled||!e)return{textDivs:[]};const t={viewport:i.clone({dontFlip:!0}),div:this.div,xfaHtml:e,annotationStorage:this.annotationStorage,linkService:this.linkService,intent:n};return this.div?a.XfaLayer.update(t):(this.div=document.createElement("div"),this.pageDiv.append(this.div),t.div=this.div,a.XfaLayer.render(t))}).catch(e=>{console.error(e)});{const t={viewport:i.clone({dontFlip:!0}),div:this.div,xfaHtml:this.xfaHtml,annotationStorage:this.annotationStorage,linkService:this.linkService,intent:n};var e=document.createElement("div"),e=(this.pageDiv.append(e),t.div=e,a.XfaLayer.render(t));return Promise.resolve(e)}}cancel(){this._cancelled=!0}hide(){this.div&&(this.div.hidden=!0)}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFViewer=t.PDFSinglePageViewer=void 0;var n=i(7),i=i(12);class a extends i.BaseViewer{}t.PDFViewer=a;class r extends i.BaseViewer{_resetView(){super._resetView(),this._scrollMode=n.ScrollMode.PAGE,this._spreadMode=n.SpreadMode.NONE}set scrollMode(e){}_updateScrollMode(){}set spreadMode(e){}_updateSpreadMode(){}}t.PDFSinglePageViewer=r},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PagesCountLimit=t.PDFPageViewBuffer=t.BaseViewer=void 0;var v=i(3),P=i(7),s=i(2),u=i(5),g=i(4),m=i(13),n=i(16),a=i(6),r=i(8),o=i(17),l=i(9),h=i(10);const d="enablePermissions",y={FORCE_SCROLL_MODE_PAGE:15e3,FORCE_LAZY_PAGE_INIT:7500,PAUSE_EAGER_PAGE_INIT:250};function b(e){return Object.values(v.AnnotationEditorType).includes(e)&&e!==v.AnnotationEditorType.DISABLE}t.PagesCountLimit=y;class c{#buf=new Set;#size=0;constructor(e){this.#size=e}push(e){const t=this.#buf;t.has(e)&&t.delete(e),t.add(e),t.size>this.#size&&this.#destroyFirstView()}resize(e,t=null){this.#size=e;const i=this.#buf;if(t){var n=i.size;let e=1;for(const a of i)if(t.has(a.id)&&(i.delete(a),i.add(a)),++e>n)break}for(;i.size>this.#size;)this.#destroyFirstView()}has(e){return this.#buf.has(e)}[Symbol.iterator](){return this.#buf.keys()}#destroyFirstView(){const e=this.#buf.keys().next().value;e?.destroy(),this.#buf.delete(e)}}t.PDFPageViewBuffer=c;t.BaseViewer=class p{#buffer=null;#annotationEditorMode=v.AnnotationEditorType.DISABLE;#annotationEditorUIManager=null;#annotationMode=v.AnnotationMode.ENABLE_FORMS;#enablePermissions=!1;#previousContainerHeight=0;#scrollModePageState=null;#onVisibilityChange=null;constructor(e){if(this.constructor===p)throw new Error("Cannot initialize BaseViewer.");var t="2.16.105";if(v.version!==t)throw new Error(`The API version "${v.version}" does not match the Viewer version "${t}".`);if(this.container=e.container,this.viewer=e.viewer||e.container.firstElementChild,"DIV"!==this.container?.tagName.toUpperCase()||"DIV"!==this.viewer?.tagName.toUpperCase())throw new Error("Invalid `container` and/or `viewer` option.");if(this.container.offsetParent&&"absolute"!==getComputedStyle(this.container).position)throw new Error("The `container` must be absolutely positioned.");this.eventBus=e.eventBus,this.linkService=e.linkService||new a.SimpleLinkService,this.downloadManager=e.downloadManager||null,this.findController=e.findController||null,this._scriptingManager=e.scriptingManager||null,this.removePageBorders=e.removePageBorders||!1,this.textLayerMode=e.textLayerMode??P.TextLayerMode.ENABLE,this.#annotationMode=e.annotationMode??v.AnnotationMode.ENABLE_FORMS,this.#annotationEditorMode=e.annotationEditorMode??v.AnnotationEditorType.DISABLE,this.imageResourcesPath=e.imageResourcesPath||"",this.enablePrintAutoRotate=e.enablePrintAutoRotate||!1,this.renderer=e.renderer||P.RendererType.CANVAS,this.useOnlyCssZoom=e.useOnlyCssZoom||!1,this.maxCanvasPixels=e.maxCanvasPixels,this.l10n=e.l10n||g.NullL10n,this.#enablePermissions=e.enablePermissions||!1,this.pageColors=e.pageColors||null,!this.pageColors||CSS.supports("color",this.pageColors.background)&&CSS.supports("color",this.pageColors.foreground)||((this.pageColors.background||this.pageColors.foreground)&&console.warn("BaseViewer: Ignoring `pageColors`-option, since the browser doesn't support the values used."),this.pageColors=null),this.defaultRenderingQueue=!e.renderingQueue,this.defaultRenderingQueue?(this.renderingQueue=new n.PDFRenderingQueue,this.renderingQueue.setViewer(this)):this.renderingQueue=e.renderingQueue,this.scroll=(0,P.watchScroll)(this.container,this._scrollUpdate.bind(this)),this.presentationModeState=P.PresentationModeState.UNKNOWN,this._onBeforeDraw=this._onAfterDraw=null,this._resetView(),this.removePageBorders&&this.viewer.classList.add("removePageBorders"),this.updateContainerHeightCss()}get pagesCount(){return this._pages.length}getPageView(e){return this._pages[e]}get pageViewsReady(){return!!this._pagesCapability.settled&&this._pages.every(function(e){return e?.pdfPage})}get renderForms(){return this.#annotationMode===v.AnnotationMode.ENABLE_FORMS}get enableScripting(){return!!this._scriptingManager}get currentPageNumber(){return this._currentPageNumber}set currentPageNumber(e){if(!Number.isInteger(e))throw new Error("Invalid page number.");this.pdfDocument&&!this._setCurrentPageNumber(e,!0)&&console.error(`currentPageNumber: "${e}" is not a valid page.`)}_setCurrentPageNumber(e,t=!1){if(this._currentPageNumber===e)return t&&this.#resetCurrentPageView(),!0;if(!(0<e&&e<=this.pagesCount))return!1;var i=this._currentPageNumber;return this._currentPageNumber=e,this.eventBus.dispatch("pagechanging",{source:this,pageNumber:e,pageLabel:this._pageLabels?.[e-1]??null,previous:i}),t&&this.#resetCurrentPageView(),!0}get currentPageLabel(){return this._pageLabels?.[this._currentPageNumber-1]??null}set currentPageLabel(t){if(this.pdfDocument){let e=0|t;var i;this._pageLabels&&0<=(i=this._pageLabels.indexOf(t))&&(e=i+1),this._setCurrentPageNumber(e,!0)||console.error(`currentPageLabel: "${t}" is not a valid page.`)}}get currentScale(){return this._currentScale!==P.UNKNOWN_SCALE?this._currentScale:P.DEFAULT_SCALE}set currentScale(e){if(isNaN(e))throw new Error("Invalid numeric scale.");this.pdfDocument&&this._setScale(e,!1)}get currentScaleValue(){return this._currentScaleValue}set currentScaleValue(e){this.pdfDocument&&this._setScale(e,!1)}get pagesRotation(){return this._pagesRotation}set pagesRotation(e){if(!(0,P.isValidRotation)(e))throw new Error("Invalid pages rotation angle.");if(this.pdfDocument&&((e%=360)<0&&(e+=360),this._pagesRotation!==e)){this._pagesRotation=e;var t=this._currentPageNumber,i={rotation:e};for(const n of this._pages)n.update(i);this._currentScaleValue&&this._setScale(this._currentScaleValue,!0),this.eventBus.dispatch("rotationchanging",{source:this,pagesRotation:e,pageNumber:t}),this.defaultRenderingQueue&&this.update()}}get firstPagePromise(){return this.pdfDocument?this._firstPageCapability.promise:null}get onePageRendered(){return this.pdfDocument?this._onePageRenderedCapability.promise:null}get pagesPromise(){return this.pdfDocument?this._pagesCapability.promise:null}#initializePermissions(e){const t={annotationEditorMode:this.#annotationEditorMode,annotationMode:this.#annotationMode,textLayerMode:this.textLayerMode};return e&&(e.includes(v.PermissionFlag.COPY)||this.viewer.classList.add(d),e.includes(v.PermissionFlag.MODIFY_CONTENTS)||(t.annotationEditorMode=v.AnnotationEditorType.DISABLE),e.includes(v.PermissionFlag.MODIFY_ANNOTATIONS)||e.includes(v.PermissionFlag.FILL_INTERACTIVE_FORMS)||this.#annotationMode!==v.AnnotationMode.ENABLE_FORMS||(t.annotationMode=v.AnnotationMode.ENABLE)),t}#onePageRenderedOrForceFetch(){if("hidden"===document.visibilityState||!this.container.offsetParent||0===this._getVisiblePages().views.length)return Promise.resolve();var e=new Promise(e=>{this.#onVisibilityChange=()=>{"hidden"===document.visibilityState&&(e(),document.removeEventListener("visibilitychange",this.#onVisibilityChange),this.#onVisibilityChange=null)},document.addEventListener("visibilitychange",this.#onVisibilityChange)});return Promise.race([this._onePageRenderedCapability.promise,e])}setDocument(g){if(this.pdfDocument&&(this.eventBus.dispatch("pagesdestroy",{source:this}),this._cancelRendering(),this._resetView(),this.findController&&this.findController.setDocument(null),this._scriptingManager&&this._scriptingManager.setDocument(null),this.#annotationEditorUIManager&&(this.#annotationEditorUIManager.destroy(),this.#annotationEditorUIManager=null)),this.pdfDocument=g){const p=g.isPureXfa,f=g.numPages;var e=g.getPage(1);const _=g.getOptionalContentConfig();var t,i=this.#enablePermissions?g.getPermissions():Promise.resolve();f>y.FORCE_SCROLL_MODE_PAGE&&(console.warn("Forcing PAGE-scrolling for performance reasons, given the length of the document."),t=this._scrollMode=P.ScrollMode.PAGE,this.eventBus.dispatch("scrollmodechanged",{source:this,mode:t})),this._pagesCapability.promise.then(()=>{this.eventBus.dispatch("pagesloaded",{source:this,pagesCount:f})},()=>{}),this._onBeforeDraw=e=>{(e=this._pages[e.pageNumber-1])&&this.#buffer.push(e)},this.eventBus._on("pagerender",this._onBeforeDraw),this._onAfterDraw=e=>{e.cssTransform||this._onePageRenderedCapability.settled||(this._onePageRenderedCapability.resolve({timestamp:e.timestamp}),this.eventBus._off("pagerendered",this._onAfterDraw),this._onAfterDraw=null,this.#onVisibilityChange&&(document.removeEventListener("visibilitychange",this.#onVisibilityChange),this.#onVisibilityChange=null))},this.eventBus._on("pagerendered",this._onAfterDraw),Promise.all([e,i]).then(([e,t])=>{if(g===this.pdfDocument){this._firstPageCapability.resolve(e),this._optionalContentConfigPromise=_;var{annotationEditorMode:t,annotationMode:i,textLayerMode:n}=this.#initializePermissions(t),a=(t!==v.AnnotationEditorType.DISABLE&&(p?console.warn("Warning: XFA-editing is not implemented."):b(t)?(this.#annotationEditorUIManager=new v.AnnotationEditorUIManager(this.container,this.eventBus),t!==v.AnnotationEditorType.NONE&&this.#annotationEditorUIManager.updateMode(t)):console.error("Invalid AnnotationEditor mode: "+t)),this._scrollMode===P.ScrollMode.PAGE?null:this.viewer),r=this.currentScale;const c=e.getViewport({scale:r*v.PixelsPerInch.PDF_TO_CSS_UNITS});var s=n===P.TextLayerMode.DISABLE||p?null:this,o=i!==v.AnnotationMode.DISABLE?this:null,l=p?this:null,h=this.#annotationEditorUIManager?this:null;for(let e=1;e<=f;++e){var d=new m.PDFPageView({container:a,eventBus:this.eventBus,id:e,scale:r,defaultViewport:c.clone(),optionalContentConfigPromise:_,renderingQueue:this.renderingQueue,textLayerFactory:s,textLayerMode:n,annotationLayerFactory:o,annotationMode:i,xfaLayerFactory:l,annotationEditorLayerFactory:h,textHighlighterFactory:this,structTreeLayerFactory:this,imageResourcesPath:this.imageResourcesPath,renderer:this.renderer,useOnlyCssZoom:this.useOnlyCssZoom,maxCanvasPixels:this.maxCanvasPixels,pageColors:this.pageColors,l10n:this.l10n});this._pages.push(d)}const u=this._pages[0];u&&(u.setPdfPage(e),this.linkService.cachePageRef(1,e.ref)),this._scrollMode===P.ScrollMode.PAGE?this.#ensurePageViewVisible():this._spreadMode!==P.SpreadMode.NONE&&this._updateSpreadMode(),this.#onePageRenderedOrForceFetch().then(async()=>{if(this.findController&&this.findController.setDocument(g),this._scriptingManager&&this._scriptingManager.setDocument(g),this.#annotationEditorUIManager&&this.eventBus.dispatch("annotationeditormodechanged",{source:this,mode:this.#annotationEditorMode}),g.loadingParams.disableAutoFetch||f>y.FORCE_LAZY_PAGE_INIT)this._pagesCapability.resolve();else{let n=f-1;if(n<=0)this._pagesCapability.resolve();else for(let i=2;i<=f;++i){var e=g.getPage(i).then(e=>{const t=this._pages[i-1];t.pdfPage||t.setPdfPage(e),this.linkService.cachePageRef(i,e.ref),0==--n&&this._pagesCapability.resolve()},e=>{console.error(`Unable to get page ${i} to initialize viewer`,e),0==--n&&this._pagesCapability.resolve()});i%y.PAUSE_EAGER_PAGE_INIT==0&&await e}}}),this.eventBus.dispatch("pagesinit",{source:this}),g.getMetadata().then(({info:e})=>{g===this.pdfDocument&&e.Language&&(this.viewer.lang=e.Language)}),this.defaultRenderingQueue&&this.update()}}).catch(e=>{console.error("Unable to initialize viewer",e),this._pagesCapability.reject(e)})}}setPageLabels(e){if(this.pdfDocument){e?Array.isArray(e)&&this.pdfDocument.numPages===e.length?this._pageLabels=e:(this._pageLabels=null,console.error("setPageLabels: Invalid page labels.")):this._pageLabels=null;for(let e=0,t=this._pages.length;e<t;e++)this._pages[e].setPageLabel(this._pageLabels?.[e]??null)}}_resetView(){this._pages=[],this._currentPageNumber=1,this._currentScale=P.UNKNOWN_SCALE,this._currentScaleValue=null,this._pageLabels=null,this.#buffer=new c(10),this._location=null,this._pagesRotation=0,this._optionalContentConfigPromise=null,this._firstPageCapability=(0,v.createPromiseCapability)(),this._onePageRenderedCapability=(0,v.createPromiseCapability)(),this._pagesCapability=(0,v.createPromiseCapability)(),this._scrollMode=P.ScrollMode.VERTICAL,this._previousScrollMode=P.ScrollMode.UNKNOWN,this._spreadMode=P.SpreadMode.NONE,this.#scrollModePageState={previousPageNumber:1,scrollDown:!0,pages:[]},this._onBeforeDraw&&(this.eventBus._off("pagerender",this._onBeforeDraw),this._onBeforeDraw=null),this._onAfterDraw&&(this.eventBus._off("pagerendered",this._onAfterDraw),this._onAfterDraw=null),this.#onVisibilityChange&&(document.removeEventListener("visibilitychange",this.#onVisibilityChange),this.#onVisibilityChange=null),this.viewer.textContent="",this._updateScrollMode(),this.viewer.removeAttribute("lang"),this.viewer.classList.remove(d)}#ensurePageViewVisible(){if(this._scrollMode!==P.ScrollMode.PAGE)throw new Error("#ensurePageViewVisible: Invalid scrollMode value.");const e=this._currentPageNumber,t=this.#scrollModePageState,i=this.viewer;if(i.textContent="",t.pages.length=0,this._spreadMode!==P.SpreadMode.NONE||this.isInPresentationMode){const r=new Set,s=this._spreadMode-1,o=(-1==s?r.add(e-1):e%2!=s?(r.add(e-1),r.add(e)):(r.add(e-2),r.add(e-1)),document.createElement("div"));if(o.className="spread",this.isInPresentationMode){const l=document.createElement("div");l.className="dummyPage",o.append(l)}for(const h of r){var n=this._pages[h];n&&(o.append(n.div),t.pages.push(n))}i.append(o)}else{var a=this._pages[e-1];i.append(a.div),t.pages.push(a)}t.scrollDown=e>=t.previousPageNumber,t.previousPageNumber=e}_scrollUpdate(){0!==this.pagesCount&&this.update()}#scrollIntoView(e,t=null){var i,n,a,{div:e,id:r}=e;this._scrollMode===P.ScrollMode.PAGE&&(this._setCurrentPageNumber(r),this.#ensurePageViewVisible(),this.update()),t||this.isInPresentationMode||(i=(r=e.offsetLeft+e.clientLeft)+e.clientWidth,{scrollLeft:n,clientWidth:a}=this.container,(this._scrollMode===P.ScrollMode.HORIZONTAL||r<n||n+a<i)&&(t={left:0,top:0})),(0,P.scrollIntoView)(e,t)}#isSameScale(e){return e===this._currentScale||Math.abs(e-this._currentScale)<1e-15}_setScaleUpdatePages(e,t,i=!1,n=!1){if(this._currentScaleValue=t.toString(),this.#isSameScale(e))n&&this.eventBus.dispatch("scalechanging",{source:this,scale:e,presetValue:t});else{P.docStyle.setProperty("--scale-factor",e*v.PixelsPerInch.PDF_TO_CSS_UNITS);var a={scale:e};for(const r of this._pages)r.update(a);if(this._currentScale=e,!i){let e=this._currentPageNumber,t;!this._location||this.isInPresentationMode||this.isChangingPresentationMode||(e=this._location.pageNumber,t=[null,{name:"XYZ"},this._location.left,this._location.top,null]),this.scrollPageIntoView({pageNumber:e,destArray:t,allowNegativeOffset:!0})}this.eventBus.dispatch("scalechanging",{source:this,scale:e,presetValue:n?t:void 0}),this.defaultRenderingQueue&&this.update(),this.updateContainerHeightCss()}}get _pageWidthScaleFactor(){return this._spreadMode!==P.SpreadMode.NONE&&this._scrollMode!==P.ScrollMode.HORIZONTAL?2:1}_setScale(i,n=!1){let a=parseFloat(i);if(0<a)this._setScaleUpdatePages(a,i,n,!1);else{var r=this._pages[this._currentPageNumber-1];if(r){let e=P.SCROLLBAR_PADDING,t=P.VERTICAL_PADDING;this.isInPresentationMode?e=t=4:this.removePageBorders?e=t=0:this._scrollMode===P.ScrollMode.HORIZONTAL&&([e,t]=[t,e]);var s=(this.container.clientWidth-e)/r.width*r.scale/this._pageWidthScaleFactor,o=(this.container.clientHeight-t)/r.height*r.scale;switch(i){case"page-actual":a=1;break;case"page-width":a=s;break;case"page-height":a=o;break;case"page-fit":a=Math.min(s,o);break;case"auto":var l=(0,P.isPortraitOrientation)(r)?s:Math.min(o,s);a=Math.min(P.MAX_AUTO_SCALE,l);break;default:return void console.error(`_setScale: "${i}" is an unknown zoom value.`)}this._setScaleUpdatePages(a,i,n,!0)}}}#resetCurrentPageView(){var e=this._pages[this._currentPageNumber-1];this.isInPresentationMode&&this._setScale(this._currentScaleValue,!0),this.#scrollIntoView(e)}pageLabelToPageNumber(e){return!this._pageLabels||(e=this._pageLabels.indexOf(e))<0?null:e+1}scrollPageIntoView({pageNumber:e,destArray:o=null,allowNegativeOffset:l=!1,ignoreDestinationZoom:h=!1}){if(this.pdfDocument){const f=Number.isInteger(e)&&this._pages[e-1];if(f)if(this.isInPresentationMode||!o)this._setCurrentPageNumber(e,!0);else{let i=0,n=0,a=0,r=0,e,t;var d=f.rotation%180!=0,c=(d?f.height:f.width)/f.scale/v.PixelsPerInch.PDF_TO_CSS_UNITS,u=(d?f.width:f.height)/f.scale/v.PixelsPerInch.PDF_TO_CSS_UNITS;let s=0;switch(o[1].name){case"XYZ":i=o[2],n=o[3],s=o[4],i=null!==i?i:0,n=null!==n?n:u;break;case"Fit":case"FitB":s="page-fit";break;case"FitH":case"FitBH":n=o[2],s="page-width",null===n&&this._location?(i=this._location.left,n=this._location.top):("number"!=typeof n||n<0)&&(n=u);break;case"FitV":case"FitBV":i=o[2],a=c,r=u,s="page-height";break;case"FitR":i=o[2],n=o[3],a=o[4]-i,r=o[5]-n;var g=this.removePageBorders?0:P.SCROLLBAR_PADDING,p=this.removePageBorders?0:P.VERTICAL_PADDING;e=(this.container.clientWidth-g)/a/v.PixelsPerInch.PDF_TO_CSS_UNITS,t=(this.container.clientHeight-p)/r/v.PixelsPerInch.PDF_TO_CSS_UNITS,s=Math.min(Math.abs(e),Math.abs(t));break;default:return void console.error(`scrollPageIntoView: "${o[1].name}" is not a valid destination type.`)}if(h||(s&&s!==this._currentScale?this.currentScaleValue=s:this._currentScale===P.UNKNOWN_SCALE&&(this.currentScaleValue=P.DEFAULT_SCALE_VALUE)),"page-fit"!==s||o[4]){d=[f.viewport.convertToViewportPoint(i,n),f.viewport.convertToViewportPoint(i+a,n+r)];let e=Math.min(d[0][0],d[1][0]),t=Math.min(d[0][1],d[1][1]);l||(e=Math.max(e,0),t=Math.max(t,0)),this.#scrollIntoView(f,{left:e,top:t})}else this.#scrollIntoView(f)}else console.error(`scrollPageIntoView: "${e}" is not a valid pageNumber parameter.`)}}_updateLocation(e){var t=this._currentScale,i=this._currentScaleValue,t=parseFloat(i)===t?Math.round(1e4*t)/100:i,i=e.id;const n=this._pages[i-1];var a=this.container,a=n.getPagePoint(a.scrollLeft-e.x,a.scrollTop-e.y),e=Math.round(a[0]),a=Math.round(a[1]);let r="#page="+i;this.isInPresentationMode||(r+=`&zoom=${t},${e},`+a),this._location={pageNumber:i,scale:t,top:a,left:e,rotation:this._pagesRotation,pdfOpenParams:r}}update(){var t=this._getVisiblePages(),i=t.views,n=i.length;if(0!==n){var n=Math.max(10,2*n+1),a=(this.#buffer.resize(n,t.ids),this.renderingQueue.renderHighestPriority(t),this._spreadMode===P.SpreadMode.NONE&&(this._scrollMode===P.ScrollMode.PAGE||this._scrollMode===P.ScrollMode.VERTICAL)),r=this._currentPageNumber;let e=!1;for(const s of i){if(s.percent<100)break;if(s.id===r&&a){e=!0;break}}this._setCurrentPageNumber(e?r:i[0].id),this._updateLocation(t.first),this.eventBus.dispatch("updateviewarea",{source:this,location:this._location})}}containsElement(e){return this.container.contains(e)}focus(){this.container.focus()}get _isContainerRtl(){return"rtl"===getComputedStyle(this.container).direction}get isInPresentationMode(){return this.presentationModeState===P.PresentationModeState.FULLSCREEN}get isChangingPresentationMode(){return this.presentationModeState===P.PresentationModeState.CHANGING}get isHorizontalScrollbarEnabled(){return!this.isInPresentationMode&&this.container.scrollWidth>this.container.clientWidth}get isVerticalScrollbarEnabled(){return!this.isInPresentationMode&&this.container.scrollHeight>this.container.clientHeight}_getVisiblePages(){var e=this._scrollMode===P.ScrollMode.PAGE?this.#scrollModePageState.pages:this._pages,t=this._scrollMode===P.ScrollMode.HORIZONTAL,i=t&&this._isContainerRtl;return(0,P.getVisibleElements)({scrollEl:this.container,views:e,sortByVisibility:!0,horizontal:t,rtl:i})}isPageVisible(e){return!!this.pdfDocument&&(Number.isInteger(e)&&0<e&&e<=this.pagesCount?this._getVisiblePages().ids.has(e):(console.error(`isPageVisible: "${e}" is not a valid page.`),!1))}isPageCached(e){return!!this.pdfDocument&&(Number.isInteger(e)&&0<e&&e<=this.pagesCount?(e=this._pages[e-1],this.#buffer.has(e)):(console.error(`isPageCached: "${e}" is not a valid page.`),!1))}cleanup(){for(const e of this._pages)e.renderingState!==P.RenderingStates.FINISHED&&e.reset()}_cancelRendering(){for(const e of this._pages)e.cancelRendering()}async#ensurePdfPageLoaded(e){if(e.pdfPage)return e.pdfPage;try{var t=await this.pdfDocument.getPage(e.id);return e.pdfPage||e.setPdfPage(t),this.linkService._cachedPageNumber?.(t.ref)||this.linkService.cachePageRef(e.id,t.ref),t}catch(e){return console.error("Unable to get page for page view",e),null}}#getScrollAhead(e){if(1===e.first?.id)return!0;if(e.last?.id===this.pagesCount)return!1;switch(this._scrollMode){case P.ScrollMode.PAGE:return this.#scrollModePageState.scrollDown;case P.ScrollMode.HORIZONTAL:return this.scroll.right}return this.scroll.down}#toggleLoadingIconSpinner(e){for(const t of e){const i=this._pages[t-1];i?.toggleLoadingIconSpinner(!0)}for(const n of this.#buffer)e.has(n.id)||n.toggleLoadingIconSpinner(!1)}forceRendering(e){var e=e||this._getVisiblePages(),t=this.#getScrollAhead(e),i=this._spreadMode!==P.SpreadMode.NONE&&this._scrollMode!==P.ScrollMode.HORIZONTAL;const n=this.renderingQueue.getHighestPriority(e,this._pages,t,i);return this.#toggleLoadingIconSpinner(e.ids),!!n&&(this.#ensurePdfPageLoaded(n).then(()=>{this.renderingQueue.renderView(n)}),!0)}createTextLayerBuilder({textLayerDiv:e,pageIndex:t,viewport:i,enhanceTextSelection:n=!1,eventBus:a,highlighter:r,accessibilityManager:s=null}){return new l.TextLayerBuilder({textLayerDiv:e,eventBus:a,pageIndex:t,viewport:i,enhanceTextSelection:!this.isInPresentationMode&&n,highlighter:r,accessibilityManager:s})}createTextHighlighter({pageIndex:e,eventBus:t}){return new o.TextHighlighter({eventBus:t,pageIndex:e,findController:this.isInPresentationMode?null:this.findController})}createAnnotationLayerBuilder({pageDiv:e,pdfPage:t,annotationStorage:i=this.pdfDocument?.annotationStorage,imageResourcesPath:n="",renderForms:a=!0,l10n:r=g.NullL10n,enableScripting:s=this.enableScripting,hasJSActionsPromise:o=this.pdfDocument?.hasJSActions(),mouseState:l=this._scriptingManager?.mouseState,fieldObjectsPromise:h=this.pdfDocument?.getFieldObjects(),annotationCanvasMap:d=null,accessibilityManager:c=null}){return new u.AnnotationLayerBuilder({pageDiv:e,pdfPage:t,annotationStorage:i,imageResourcesPath:n,renderForms:a,linkService:this.linkService,downloadManager:this.downloadManager,l10n:r,enableScripting:s,hasJSActionsPromise:o,mouseState:l,fieldObjectsPromise:h,annotationCanvasMap:d,accessibilityManager:c})}createAnnotationEditorLayerBuilder({uiManager:e=this.#annotationEditorUIManager,pageDiv:t,pdfPage:i,accessibilityManager:n=null,l10n:a,annotationStorage:r=this.pdfDocument?.annotationStorage}){return new s.AnnotationEditorLayerBuilder({uiManager:e,pageDiv:t,pdfPage:i,annotationStorage:r,accessibilityManager:n,l10n:a})}createXfaLayerBuilder({pageDiv:e,pdfPage:t,annotationStorage:i=this.pdfDocument?.annotationStorage}){return new h.XfaLayerBuilder({pageDiv:e,pdfPage:t,annotationStorage:i,linkService:this.linkService})}createStructTreeLayerBuilder({pdfPage:e}){return new r.StructTreeLayerBuilder({pdfPage:e})}get hasEqualPageSizes(){var i=this._pages[0];for(let e=1,t=this._pages.length;e<t;++e){var n=this._pages[e];if(n.width!==i.width||n.height!==i.height)return!1}return!0}getPagesOverview(){return this._pages.map(e=>(e=e.pdfPage.getViewport({scale:1}),!this.enablePrintAutoRotate||(0,P.isPortraitOrientation)(e)?{width:e.width,height:e.height,rotation:e.rotation}:{width:e.height,height:e.width,rotation:(e.rotation-90)%360}))}get optionalContentConfigPromise(){return this.pdfDocument?this._optionalContentConfigPromise||(console.error("optionalContentConfigPromise: Not initialized yet."),this.pdfDocument.getOptionalContentConfig()):Promise.resolve(null)}set optionalContentConfigPromise(e){if(!(e instanceof Promise))throw new Error("Invalid optionalContentConfigPromise: "+e);if(this.pdfDocument&&this._optionalContentConfigPromise){var t={optionalContentConfigPromise:this._optionalContentConfigPromise=e};for(const i of this._pages)i.update(t);this.update(),this.eventBus.dispatch("optionalcontentconfigchanged",{source:this,promise:e})}}get scrollMode(){return this._scrollMode}set scrollMode(e){if(this._scrollMode!==e){if(!(0,P.isValidScrollMode)(e))throw new Error("Invalid scroll mode: "+e);this.pagesCount>y.FORCE_SCROLL_MODE_PAGE||(this._previousScrollMode=this._scrollMode,this._scrollMode=e,this.eventBus.dispatch("scrollmodechanged",{source:this,mode:e}),this._updateScrollMode(this._currentPageNumber))}}_updateScrollMode(e=null){const t=this._scrollMode,i=this.viewer;i.classList.toggle("scrollHorizontal",t===P.ScrollMode.HORIZONTAL),i.classList.toggle("scrollWrapped",t===P.ScrollMode.WRAPPED),this.pdfDocument&&e&&(t===P.ScrollMode.PAGE?this.#ensurePageViewVisible():this._previousScrollMode===P.ScrollMode.PAGE&&this._updateSpreadMode(),this._currentScaleValue&&isNaN(this._currentScaleValue)&&this._setScale(this._currentScaleValue,!0),this._setCurrentPageNumber(e,!0),this.update())}get spreadMode(){return this._spreadMode}set spreadMode(e){if(this._spreadMode!==e){if(!(0,P.isValidSpreadMode)(e))throw new Error("Invalid spread mode: "+e);this._spreadMode=e,this.eventBus.dispatch("spreadmodechanged",{source:this,mode:e}),this._updateSpreadMode(this._currentPageNumber)}}_updateSpreadMode(e=null){if(this.pdfDocument){const a=this.viewer,r=this._pages;if(this._scrollMode===P.ScrollMode.PAGE)this.#ensurePageViewVisible();else if(a.textContent="",this._spreadMode===P.SpreadMode.NONE)for(const t of this._pages)a.append(t.div);else{var n=this._spreadMode-1;let i=null;for(let e=0,t=r.length;e<t;++e)null===i?((i=document.createElement("div")).className="spread",a.append(i)):e%2==n&&(i=i.cloneNode(!1),a.append(i)),i.append(r[e].div)}e&&(this._currentScaleValue&&isNaN(this._currentScaleValue)&&this._setScale(this._currentScaleValue,!0),this._setCurrentPageNumber(e,!0),this.update())}}_getPageAdvance(i,e=!1){switch(this._scrollMode){case P.ScrollMode.WRAPPED:{const v=this._getVisiblePages().views,m=new Map;for(var{id:t,y:n,percent:a,widthPercent:r}of v)if(!(0===a||r<100)){let e=m.get(n);e||m.set(n,e||=[]),e.push(t)}for(const y of m.values()){var s=y.indexOf(i);if(-1!==s){var o=y.length;if(1===o)break;if(e)for(let e=s-1;0<=e;e--){var l=y[e],h=y[e+1]-1;if(l<h)return i-h}else for(let e=s+1,t=o;e<t;e++){var d=y[e],c=y[e-1]+1;if(c<d)return c-i}if(e){if((s=y[0])<i)return i-s+1}else if(s=y[o-1],i<s)return s-i+1;break}}break}case P.ScrollMode.HORIZONTAL:break;case P.ScrollMode.PAGE:case P.ScrollMode.VERTICAL:if(this._spreadMode===P.SpreadMode.NONE)break;var u=this._spreadMode-1;if(e&&i%2!=u)break;if(!e&&i%2==u)break;var g,p,f,u=this._getVisiblePages().views,_=e?i-1:i+1;for({id:g,percent:p,widthPercent:f}of u)if(g===_){if(0<p&&100===f)return 2;break}}return 1}nextPage(){var e=this._currentPageNumber,t=this.pagesCount;if(t<=e)return!1;var i=this._getPageAdvance(e,!1)||1;return this.currentPageNumber=Math.min(e+i,t),!0}previousPage(){var e=this._currentPageNumber;if(e<=1)return!1;var t=this._getPageAdvance(e,!0)||1;return this.currentPageNumber=Math.max(e-t,1),!0}increaseScale(e=1){let t=this._currentScale;for(;t=(t*P.DEFAULT_SCALE_DELTA).toFixed(2),t=Math.ceil(10*t)/10,t=Math.min(P.MAX_SCALE,t),0<--e&&t<P.MAX_SCALE;);this.currentScaleValue=t}decreaseScale(e=1){let t=this._currentScale;for(;t=(t/P.DEFAULT_SCALE_DELTA).toFixed(2),t=Math.floor(10*t)/10,t=Math.max(P.MIN_SCALE,t),0<--e&&t>P.MIN_SCALE;);this.currentScaleValue=t}updateContainerHeightCss(){var e=this.container.clientHeight;e!==this.#previousContainerHeight&&(this.#previousContainerHeight=e,P.docStyle.setProperty("--viewer-container-height",e+"px"))}get annotationEditorMode(){return this.#annotationEditorUIManager?this.#annotationEditorMode:v.AnnotationEditorType.DISABLE}set annotationEditorMode(e){if(!this.#annotationEditorUIManager)throw new Error("The AnnotationEditor is not enabled.");if(this.#annotationEditorMode!==e){if(!b(e))throw new Error("Invalid AnnotationEditor mode: "+e);this.pdfDocument&&(this.#annotationEditorMode=e,this.eventBus.dispatch("annotationeditormodechanged",{source:this,mode:e}),this.#annotationEditorUIManager.updateMode(e))}}set annotationEditorParams({type:e,value:t}){if(!this.#annotationEditorUIManager)throw new Error("The AnnotationEditor is not enabled.");this.#annotationEditorUIManager.updateParams(e,t)}refresh(){if(this.pdfDocument){var e={};for(const t of this._pages)t.update(e);this.update()}}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFPageView=void 0;var c=i(3),u=i(7),n=i(14),r=i(4),d=i(15);const s=n.compatibilityParams.maxCanvasPixels||16777216;t.PDFPageView=class{#annotationMode=c.AnnotationMode.ENABLE_FORMS;#useThumbnailCanvas={initialOptionalContent:!0,regularAnnotations:!0};constructor(e){const t=e.container;var i=e.defaultViewport;this.id=e.id,this.renderingId="page"+this.id,this.pdfPage=null,this.pageLabel=null,this.rotation=0,this.scale=e.scale||u.DEFAULT_SCALE,this.viewport=i,this.pdfPageRotate=i.rotation,this._optionalContentConfigPromise=e.optionalContentConfigPromise||null,this.hasRestrictedScaling=!1,this.textLayerMode=e.textLayerMode??u.TextLayerMode.ENABLE,this.#annotationMode=e.annotationMode??c.AnnotationMode.ENABLE_FORMS,this.imageResourcesPath=e.imageResourcesPath||"",this.useOnlyCssZoom=e.useOnlyCssZoom||!1,this.maxCanvasPixels=e.maxCanvasPixels||s,this.pageColors=e.pageColors||null,this.eventBus=e.eventBus,this.renderingQueue=e.renderingQueue,this.textLayerFactory=e.textLayerFactory,this.annotationLayerFactory=e.annotationLayerFactory,this.annotationEditorLayerFactory=e.annotationEditorLayerFactory,this.xfaLayerFactory=e.xfaLayerFactory,this.textHighlighter=e.textHighlighterFactory?.createTextHighlighter({pageIndex:this.id-1,eventBus:this.eventBus}),this.structTreeLayerFactory=e.structTreeLayerFactory,this.renderer=e.renderer||u.RendererType.CANVAS,this.l10n=e.l10n||r.NullL10n,this.paintTask=null,this.paintedViewportMap=new WeakMap,this.renderingState=u.RenderingStates.INITIAL,this.resume=null,this._renderError=null,this._isStandalone=!this.renderingQueue?.hasViewer(),this._annotationCanvasMap=null,this.annotationLayer=null,this.annotationEditorLayer=null,this.textLayer=null,this.zoomLayer=null,this.xfaLayer=null,this.structTreeLayer=null;const n=document.createElement("div");if(n.className="page",n.style.width=Math.floor(this.viewport.width)+"px",n.style.height=Math.floor(this.viewport.height)+"px",n.setAttribute("data-page-number",this.id),n.setAttribute("role","region"),this.l10n.get("page_landmark",{page:this.id}).then(e=>{n.setAttribute("aria-label",e)}),this.div=n,t?.append(n),this._isStandalone){const a=e["optionalContentConfigPromise"];a&&a.then(e=>{a===this._optionalContentConfigPromise&&(this.#useThumbnailCanvas.initialOptionalContent=e.hasInitialVisibility)})}}setPdfPage(e){this.pdfPage=e,this.pdfPageRotate=e.rotate;var t=(this.rotation+this.pdfPageRotate)%360;this.viewport=e.getViewport({scale:this.scale*c.PixelsPerInch.PDF_TO_CSS_UNITS,rotation:t}),this.reset()}destroy(){this.reset(),this.pdfPage&&this.pdfPage.cleanup()}async _renderAnnotationLayer(){let t=null;try{await this.annotationLayer.render(this.viewport,"display")}catch(e){console.error(`_renderAnnotationLayer: "${e}".`),t=e}finally{this.eventBus.dispatch("annotationlayerrendered",{source:this,pageNumber:this.id,error:t})}}async _renderAnnotationEditorLayer(){let t=null;try{await this.annotationEditorLayer.render(this.viewport,"display")}catch(e){console.error(`_renderAnnotationEditorLayer: "${e}".`),t=e}finally{this.eventBus.dispatch("annotationeditorlayerrendered",{source:this,pageNumber:this.id,error:t})}}async _renderXfaLayer(){let t=null;try{var e=await this.xfaLayer.render(this.viewport,"display");this.textHighlighter&&this._buildXfaTextContentItems(e.textDivs)}catch(e){console.error(`_renderXfaLayer: "${e}".`),t=e}finally{this.eventBus.dispatch("xfalayerrendered",{source:this,pageNumber:this.id,error:t})}}async _buildXfaTextContentItems(e){const t=[];for(const i of(await this.pdfPage.getTextContent()).items)t.push(i.str);this.textHighlighter.setTextMapping(e,t),this.textHighlighter.enable()}_resetZoomLayer(e=!1){if(this.zoomLayer){const t=this.zoomLayer.firstChild;this.paintedViewportMap.delete(t),t.width=0,t.height=0,e&&this.zoomLayer.remove(),this.zoomLayer=null}}reset({keepZoomLayer:e=!1,keepAnnotationLayer:t=!1,keepAnnotationEditorLayer:i=!1,keepXfaLayer:n=!1}={}){this.cancelRendering({keepAnnotationLayer:t,keepAnnotationEditorLayer:i,keepXfaLayer:n}),this.renderingState=u.RenderingStates.INITIAL;const a=this.div;a.style.width=Math.floor(this.viewport.width)+"px",a.style.height=Math.floor(this.viewport.height)+"px";var r=a.childNodes,s=e&&this.zoomLayer||null,o=t&&this.annotationLayer?.div||null,l=i&&this.annotationEditorLayer?.div||null,h=n&&this.xfaLayer?.div||null;for(let e=r.length-1;0<=e;e--){const d=r[e];switch(d){case s:case o:case l:case h:continue}d.remove()}a.removeAttribute("data-loaded"),o&&this.annotationLayer.hide(),l?this.annotationEditorLayer.hide():this.annotationEditorLayer?.destroy(),h&&this.xfaLayer.hide(),s||(this.canvas&&(this.paintedViewportMap.delete(this.canvas),this.canvas.width=0,this.canvas.height=0,delete this.canvas),this._resetZoomLayer()),this.svg&&(this.paintedViewportMap.delete(this.svg),delete this.svg),this.loadingIconDiv=document.createElement("div"),this.loadingIconDiv.className="loadingIcon notVisible",this._isStandalone&&this.toggleLoadingIconSpinner(!0),this.loadingIconDiv.setAttribute("role","img"),this.l10n.get("loading").then(e=>{this.loadingIconDiv?.setAttribute("aria-label",e)}),a.append(this.loadingIconDiv)}update({scale:e=0,rotation:t=null,optionalContentConfigPromise:i=null}){this.scale=e||this.scale,"number"==typeof t&&(this.rotation=t),i instanceof Promise&&(this._optionalContentConfigPromise=i).then(e=>{i===this._optionalContentConfigPromise&&(this.#useThumbnailCanvas.initialOptionalContent=e.hasInitialVisibility)});e=(this.rotation+this.pdfPageRotate)%360;if(this.viewport=this.viewport.clone({scale:this.scale*c.PixelsPerInch.PDF_TO_CSS_UNITS,rotation:e}),this._isStandalone&&u.docStyle.setProperty("--scale-factor",this.viewport.scale),this.svg)return this.cssTransform({target:this.svg,redrawAnnotationLayer:!0,redrawAnnotationEditorLayer:!0,redrawXfaLayer:!0}),void this.eventBus.dispatch("pagerendered",{source:this,pageNumber:this.id,cssTransform:!0,timestamp:performance.now(),error:this._renderError});let n=!1;if(this.canvas&&0<this.maxCanvasPixels&&(t=this.outputScale,(Math.floor(this.viewport.width)*t.sx|0)*(Math.floor(this.viewport.height)*t.sy|0)>this.maxCanvasPixels&&(n=!0)),this.canvas){if(this.useOnlyCssZoom||this.hasRestrictedScaling&&n)return this.cssTransform({target:this.canvas,redrawAnnotationLayer:!0,redrawAnnotationEditorLayer:!0,redrawXfaLayer:!0}),void this.eventBus.dispatch("pagerendered",{source:this,pageNumber:this.id,cssTransform:!0,timestamp:performance.now(),error:this._renderError});this.zoomLayer||this.canvas.hidden||(this.zoomLayer=this.canvas.parentNode,this.zoomLayer.style.position="absolute")}this.zoomLayer&&this.cssTransform({target:this.zoomLayer.firstChild}),this.reset({keepZoomLayer:!0,keepAnnotationLayer:!0,keepAnnotationEditorLayer:!0,keepXfaLayer:!0})}cancelRendering({keepAnnotationLayer:e=!1,keepAnnotationEditorLayer:t=!1,keepXfaLayer:i=!1}={}){this.paintTask&&(this.paintTask.cancel(),this.paintTask=null),this.resume=null,this.textLayer&&(this.textLayer.cancel(),this.textLayer=null),!this.annotationLayer||e&&this.annotationLayer.div||(this.annotationLayer.cancel(),this.annotationLayer=null,this._annotationCanvasMap=null),!this.annotationEditorLayer||t&&this.annotationEditorLayer.div||(this.annotationEditorLayer.cancel(),this.annotationEditorLayer=null),!this.xfaLayer||i&&this.xfaLayer.div||(this.xfaLayer.cancel(),this.xfaLayer=null,this.textHighlighter?.disable()),this._onTextLayerRendered&&(this.eventBus._off("textlayerrendered",this._onTextLayerRendered),this._onTextLayerRendered=null)}cssTransform({target:n,redrawAnnotationLayer:e=!1,redrawAnnotationEditorLayer:t=!1,redrawXfaLayer:i=!1}){var a=this.viewport.width,r=this.viewport.height;const s=this.div;n.style.width=n.parentNode.style.width=s.style.width=Math.floor(a)+"px",n.style.height=n.parentNode.style.height=s.style.height=Math.floor(r)+"px";var o=this.viewport.rotation-this.paintedViewportMap.get(n).rotation,l=Math.abs(o);let h=1,d=1;if(90!==l&&270!==l||(h=r/a,d=a/r),n.style.transform=`rotate(${o}deg) scale(${h}, ${d})`,this.textLayer){l=this.textLayer.viewport,r=this.viewport.rotation-l.rotation,n=Math.abs(r);let e=a/l.width;90!==n&&270!==n||(e=a/l.height);const c=this.textLayer.textLayerDiv;let t,i;switch(n){case 0:t=i=0;break;case 90:t=0,i="-"+c.style.height;break;case 180:t="-"+c.style.width,i="-"+c.style.height;break;case 270:t="-"+c.style.width,i=0;break;default:console.error("Bad rotation value.")}c.style.transform=`rotate(${n}deg) `+`scale(${e}) `+`translate(${t}, ${i})`,c.style.transformOrigin="0% 0%"}e&&this.annotationLayer&&this._renderAnnotationLayer(),t&&this.annotationEditorLayer&&this._renderAnnotationEditorLayer(),i&&this.xfaLayer&&this._renderXfaLayer()}get width(){return this.viewport.width}get height(){return this.viewport.height}getPagePoint(e,t){return this.viewport.convertToPdfPoint(e,t)}toggleLoadingIconSpinner(e=!1){this.loadingIconDiv?.classList.toggle("notVisible",!e)}draw(){this.renderingState!==u.RenderingStates.INITIAL&&(console.error("Must be in new state before drawing"),this.reset());const{div:t,pdfPage:i}=this;if(!i)return this.renderingState=u.RenderingStates.FINISHED,this.loadingIconDiv&&(this.loadingIconDiv.remove(),delete this.loadingIconDiv),Promise.reject(new Error("pdfPage is not loaded"));this.renderingState=u.RenderingStates.RUNNING;const e=document.createElement("div"),n=(e.style.width=t.style.width,e.style.height=t.style.height,e.classList.add("canvasWrapper"),this.annotationLayer?.div||this.annotationEditorLayer?.div);n?n.before(e):t.append(e);let a=null;if(this.textLayerMode!==u.TextLayerMode.DISABLE&&this.textLayerFactory){this._accessibilityManager||=new d.TextAccessibilityManager;const h=document.createElement("div");h.className="textLayer",h.style.width=e.style.width,h.style.height=e.style.height,n?n.before(h):t.append(h),a=this.textLayerFactory.createTextLayerBuilder({textLayerDiv:h,pageIndex:this.id-1,viewport:this.viewport,enhanceTextSelection:this.textLayerMode===u.TextLayerMode.ENABLE_ENHANCE,eventBus:this.eventBus,highlighter:this.textHighlighter,accessibilityManager:this._accessibilityManager})}this.textLayer=a,this.#annotationMode!==c.AnnotationMode.DISABLE&&this.annotationLayerFactory&&(this._annotationCanvasMap||=new Map,this.annotationLayer||=this.annotationLayerFactory.createAnnotationLayerBuilder({pageDiv:t,pdfPage:i,imageResourcesPath:this.imageResourcesPath,renderForms:this.#annotationMode===c.AnnotationMode.ENABLE_FORMS,l10n:this.l10n,annotationCanvasMap:this._annotationCanvasMap,accessibilityManager:this._accessibilityManager})),this.xfaLayer?.div&&t.append(this.xfaLayer.div);let r=null;this.renderingQueue&&(r=e=>{if(!this.renderingQueue.isHighestPriority(this))return this.renderingState=u.RenderingStates.PAUSED,void(this.resume=()=>{this.renderingState=u.RenderingStates.RUNNING,e()});e()});const s=async(e=null)=>{if(o===this.paintTask&&(this.paintTask=null),e instanceof c.RenderingCancelledException)this._renderError=null;else if(this._renderError=e,this.renderingState=u.RenderingStates.FINISHED,this.loadingIconDiv&&(this.loadingIconDiv.remove(),delete this.loadingIconDiv),this._resetZoomLayer(!0),this.#useThumbnailCanvas.regularAnnotations=!o.separateAnnots,this.eventBus.dispatch("pagerendered",{source:this,pageNumber:this.id,cssTransform:!1,timestamp:performance.now(),error:this._renderError}),e)throw e},o=this.renderer===u.RendererType.SVG?this.paintOnSvg(e):this.paintOnCanvas(e);o.onRenderContinue=r;var l=(this.paintTask=o).promise.then(()=>s(null).then(()=>{var e;a&&(e=i.streamTextContent({includeMarkedContent:!0}),a.setTextContentStream(e),a.render()),this.annotationLayer&&this._renderAnnotationLayer().then(()=>{this.annotationEditorLayerFactory&&(this.annotationEditorLayer||=this.annotationEditorLayerFactory.createAnnotationEditorLayerBuilder({pageDiv:t,pdfPage:i,l10n:this.l10n,accessibilityManager:this._accessibilityManager}),this._renderAnnotationEditorLayer())})}),function(e){return s(e)});return this.xfaLayerFactory&&(this.xfaLayer||=this.xfaLayerFactory.createXfaLayerBuilder({pageDiv:t,pdfPage:i}),this._renderXfaLayer()),this.structTreeLayerFactory&&this.textLayer&&this.canvas&&(this._onTextLayerRendered=e=>{e.pageNumber===this.id&&(this.eventBus._off("textlayerrendered",this._onTextLayerRendered),this._onTextLayerRendered=null,this.canvas&&this.pdfPage.getStructTree().then(e=>{if(e&&this.canvas){const t=this.structTreeLayer.render(e);t.classList.add("structTree"),this.canvas.append(t)}}))},this.eventBus._on("textlayerrendered",this._onTextLayerRendered),this.structTreeLayer=this.structTreeLayerFactory.createStructTreeLayerBuilder({pdfPage:i})),t.setAttribute("data-loaded",!0),this.eventBus.dispatch("pagerender",{source:this,pageNumber:this.id}),l}paintOnCanvas(e){const t=(0,c.createPromiseCapability)(),i={promise:t.promise,onRenderContinue(e){e()},cancel(){d.cancel()},get separateAnnots(){return d.separateAnnots}},n=this.viewport,a=document.createElement("canvas");a.setAttribute("role","presentation");let r=a.hidden=!0;function s(){r&&(a.hidden=!1,r=!1)}e.append(a);e=(this.canvas=a).getContext("2d",{alpha:!1});const o=this.outputScale=new u.OutputScale;this.useOnlyCssZoom&&(l=n.clone({scale:c.PixelsPerInch.PDF_TO_CSS_UNITS}),o.sx*=l.width/n.width,o.sy*=l.height/n.height),0<this.maxCanvasPixels&&(l=n.width*n.height,l=Math.sqrt(this.maxCanvasPixels/l),o.sx>l||o.sy>l?(o.sx=l,o.sy=l,this.hasRestrictedScaling=!0):this.hasRestrictedScaling=!1);var l=(0,u.approximateFraction)(o.sx),h=(0,u.approximateFraction)(o.sy),l=(a.width=(0,u.roundToDivide)(n.width*o.sx,l[0]),a.height=(0,u.roundToDivide)(n.height*o.sy,h[0]),a.style.width=(0,u.roundToDivide)(n.width,l[1])+"px",a.style.height=(0,u.roundToDivide)(n.height,h[1])+"px",this.paintedViewportMap.set(a,n),o.scaled?[o.sx,0,0,o.sy,0,0]:null),h={canvasContext:e,transform:l,viewport:this.viewport,annotationMode:this.#annotationMode,optionalContentConfigPromise:this._optionalContentConfigPromise,annotationCanvasMap:this._annotationCanvasMap,pageColors:this.pageColors};const d=this.pdfPage.render(h);return d.onContinue=function(e){s(),i.onRenderContinue?i.onRenderContinue(e):e()},d.promise.then(function(){s(),t.resolve()},function(e){s(),t.reject(e)}),i}paintOnSvg(i){let e=!1;const n=()=>{if(e)throw new c.RenderingCancelledException("Rendering cancelled, page "+this.id,"svg")},a=this.pdfPage,r=this.viewport.clone({scale:c.PixelsPerInch.PDF_TO_CSS_UNITS});return{promise:a.getOperatorList({annotationMode:this.#annotationMode}).then(e=>{n();const t=new c.SVGGraphics(a.commonObjs,a.objs);return t.getSVG(e,r).then(e=>{n(),this.svg=e,this.paintedViewportMap.set(e,r),e.style.width=i.style.width,e.style.height=i.style.height,this.renderingState=u.RenderingStates.FINISHED,i.append(e)})}),onRenderContinue(e){e()},cancel(){e=!0},get separateAnnots(){return!1}}}setPageLabel(e){this.pageLabel="string"==typeof e?e:null,null!==this.pageLabel?this.div.setAttribute("data-page-label",this.pageLabel):this.div.removeAttribute("data-page-label")}get thumbnailCanvas(){var{initialOptionalContent:e,regularAnnotations:t}=this.#useThumbnailCanvas;return e&&t?this.canvas:null}}},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.compatibilityParams=t.OptionKind=t.AppOptions=void 0;const s=Object.create(null);t.compatibilityParams=s;{var i=navigator.userAgent||"",n=navigator.platform||"",a=navigator.maxTouchPoints||1;const r=/Android/.test(i),d=/\b(iPad|iPhone|iPod)(?=;)/.test(i)||"MacIntel"===n&&1<a;(d||r)&&(s.maxCanvasPixels=5242880)}const o={VIEWER:2,API:4,WORKER:8,PREFERENCE:128},l={annotationEditorMode:{value:-1,kind:(t.OptionKind=o).VIEWER+o.PREFERENCE},annotationMode:{value:2,kind:o.VIEWER+o.PREFERENCE},cursorToolOnLoad:{value:0,kind:o.VIEWER+o.PREFERENCE},defaultZoomValue:{value:"",kind:o.VIEWER+o.PREFERENCE},disableHistory:{value:!1,kind:o.VIEWER},disablePageLabels:{value:!1,kind:o.VIEWER+o.PREFERENCE},enablePermissions:{value:!1,kind:o.VIEWER+o.PREFERENCE},enablePrintAutoRotate:{value:!0,kind:o.VIEWER+o.PREFERENCE},enableScripting:{value:!0,kind:o.VIEWER+o.PREFERENCE},externalLinkRel:{value:"noopener noreferrer nofollow",kind:o.VIEWER},externalLinkTarget:{value:0,kind:o.VIEWER+o.PREFERENCE},historyUpdateUrl:{value:!1,kind:o.VIEWER+o.PREFERENCE},ignoreDestinationZoom:{value:!1,kind:o.VIEWER+o.PREFERENCE},imageResourcesPath:{value:"./images/",kind:o.VIEWER},maxCanvasPixels:{value:16777216,kind:o.VIEWER},forcePageColors:{value:!1,kind:o.VIEWER+o.PREFERENCE},pageColorsBackground:{value:"Canvas",kind:o.VIEWER+o.PREFERENCE},pageColorsForeground:{value:"CanvasText",kind:o.VIEWER+o.PREFERENCE},pdfBugEnabled:{value:!1,kind:o.VIEWER+o.PREFERENCE},printResolution:{value:150,kind:o.VIEWER},sidebarViewOnLoad:{value:-1,kind:o.VIEWER+o.PREFERENCE},scrollModeOnLoad:{value:-1,kind:o.VIEWER+o.PREFERENCE},spreadModeOnLoad:{value:-1,kind:o.VIEWER+o.PREFERENCE},textLayerMode:{value:1,kind:o.VIEWER+o.PREFERENCE},useOnlyCssZoom:{value:!1,kind:o.VIEWER+o.PREFERENCE},viewerCssTheme:{value:0,kind:o.VIEWER+o.PREFERENCE},viewOnLoad:{value:0,kind:o.VIEWER+o.PREFERENCE},cMapPacked:{value:!0,kind:o.API},cMapUrl:{value:"../web/cmaps/",kind:o.API},disableAutoFetch:{value:!1,kind:o.API+o.PREFERENCE},disableFontFace:{value:!1,kind:o.API+o.PREFERENCE},disableRange:{value:!1,kind:o.API+o.PREFERENCE},disableStream:{value:!1,kind:o.API+o.PREFERENCE},docBaseUrl:{value:"",kind:o.API},enableXfa:{value:!0,kind:o.API+o.PREFERENCE},fontExtraProperties:{value:!1,kind:o.API},isEvalSupported:{value:!0,kind:o.API},maxImageSize:{value:-1,kind:o.API},pdfBug:{value:!1,kind:o.API},standardFontDataUrl:{value:"../web/standard_fonts/",kind:o.API},verbosity:{value:1,kind:o.API},workerPort:{value:null,kind:o.WORKER},workerSrc:{value:"../build/pdf.worker.js",kind:o.WORKER}},h=(l.defaultUrl={value:"compressed.tracemonkey-pldi-09.pdf",kind:o.VIEWER},l.disablePreferences={value:!1,kind:o.VIEWER},l.locale={value:navigator.language||"en-US",kind:o.VIEWER},l.renderer={value:"canvas",kind:o.VIEWER+o.PREFERENCE},l.sandboxBundleSrc={value:"../build/pdf.sandbox.js",kind:o.VIEWER},Object.create(null));t.AppOptions=class{constructor(){throw new Error("Cannot initialize AppOptions.")}static get(e){var t=h[e];if(void 0!==t)return t;t=l[e];return void 0!==t?s[e]??t.value:void 0}static getAll(e=null){const t=Object.create(null);for(const r in l){var i=l[r];if(e){if(0==(e&i.kind))continue;if(e===o.PREFERENCE){var n=i.value,a=typeof n;if("boolean"==a||"string"==a||"number"==a&&Number.isInteger(n)){t[r]=n;continue}throw new Error("Invalid type for preference: "+r)}}a=h[r];t[r]=void 0!==a?a:s[r]??i.value}return t}static set(e,t){h[e]=t}static setAll(e){for(const t in e)h[t]=e[t]}static remove(e){delete h[e]}static _hasUserOptions(){return 0<Object.keys(h).length}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TextAccessibilityManager=void 0;var s=i(7);class o{#enabled=!1;#textChildren=null;#textNodes=new Map;#waitingElements=new Map;setTextMapping(e){this.#textChildren=e}static#compareElementPositions(e,t){e=e.getBoundingClientRect(),t=t.getBoundingClientRect();if(0===e.width&&0===e.height)return 1;if(0===t.width&&0===t.height)return-1;var i=e.y,n=e.y+e.height,a=e.y+e.height/2,r=t.y,s=t.y+t.height,o=t.y+t.height/2;return a<=r&&n<=o?-1:o<=i&&s<=a?1:e.x+e.width/2-(t.x+t.width/2)}enable(){if(this.#enabled)throw new Error("TextAccessibilityManager is already enabled.");if(!this.#textChildren)throw new Error("Text divs and strings have not been set.");if(this.#enabled=!0,this.#textChildren=this.#textChildren.slice(),this.#textChildren.sort(o.#compareElementPositions),0<this.#textNodes.size){var e,t,i=this.#textChildren;for([e,t]of this.#textNodes)document.getElementById(e)?this.#addIdToAriaOwns(e,i[t]):this.#textNodes.delete(e)}for(var[n,a]of this.#waitingElements)this.addPointerInTextLayer(n,a);this.#waitingElements.clear()}disable(){this.#enabled&&(this.#waitingElements.clear(),this.#textChildren=null,this.#enabled=!1)}removePointerInTextLayer(e){if(this.#enabled){var t=this.#textChildren;if(t&&0!==t.length){const n=e["id"];var i=this.#textNodes.get(n);if(void 0!==i){const a=t[i];this.#textNodes.delete(n);let e=a.getAttribute("aria-owns");e?.includes(n)&&((e=e.split(" ").filter(e=>e!==n).join(" "))?a.setAttribute("aria-owns",e):(a.removeAttribute("aria-owns"),a.setAttribute("role","presentation")))}}}else this.#waitingElements.delete(e)}#addIdToAriaOwns(e,t){const i=t.getAttribute("aria-owns");i?.includes(e)||t.setAttribute("aria-owns",i?i+" "+e:e),t.removeAttribute("role")}addPointerInTextLayer(t,e){var i,n,a=t["id"];a&&(this.#enabled?(e&&this.removePointerInTextLayer(t),(i=this.#textChildren)&&0!==i.length&&(n=(0,s.binarySearchFirstItem)(i,e=>o.#compareElementPositions(t,e)<0),n=Math.max(0,n-1),this.#addIdToAriaOwns(a,i[n]),this.#textNodes.set(a,n))):this.#waitingElements.set(t,e))}moveElementInDOM(e,t,i,n){if(this.addPointerInTextLayer(i,n),e.hasChildNodes()){const a=Array.from(e.childNodes).filter(e=>e!==t);if(0!==a.length){const r=i||t;n=(0,s.binarySearchFirstItem)(a,e=>o.#compareElementPositions(r,e)<0);0===n?a[0].before(t):a[n-1].after(t)}}else e.append(t)}}t.TextAccessibilityManager=o},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFRenderingQueue=void 0;var n=i(3),a=i(7);t.PDFRenderingQueue=class{constructor(){this.pdfViewer=null,this.pdfThumbnailViewer=null,this.onIdle=null,this.highestPriorityPage=null,this.idleTimeout=null,this.printing=!1,this.isThumbnailViewEnabled=!1}setViewer(e){this.pdfViewer=e}setThumbnailViewer(e){this.pdfThumbnailViewer=e}isHighestPriority(e){return this.highestPriorityPage===e.renderingId}hasViewer(){return!!this.pdfViewer}renderHighestPriority(e){this.idleTimeout&&(clearTimeout(this.idleTimeout),this.idleTimeout=null),this.pdfViewer.forceRendering(e)||this.isThumbnailViewEnabled&&this.pdfThumbnailViewer?.forceRendering()||this.printing||this.onIdle&&(this.idleTimeout=setTimeout(this.onIdle.bind(this),3e4))}getHighestPriority(e,i,n,t=!1){var a=e.views,r=a.length;if(0===r)return null;for(let e=0;e<r;e++){var s=a[e].view;if(!this.isViewFinished(s))return s}var o=e.first.id,l=e.last.id;if(r<l-o+1){const c=e.ids;for(let e=1,t=l-o;e<t;e++){var h=n?o+e:l-e;if(!c.has(h)){h=i[h-1];if(!this.isViewFinished(h))return h}}}e=n?l:o-2;let d=i[e];return d&&!this.isViewFinished(d)||t&&(e+=n?1:-1,(d=i[e])&&!this.isViewFinished(d))?d:null}isViewFinished(e){return e.renderingState===a.RenderingStates.FINISHED}renderView(e){switch(e.renderingState){case a.RenderingStates.FINISHED:return!1;case a.RenderingStates.PAUSED:this.highestPriorityPage=e.renderingId,e.resume();break;case a.RenderingStates.RUNNING:this.highestPriorityPage=e.renderingId;break;case a.RenderingStates.INITIAL:this.highestPriorityPage=e.renderingId,e.draw().finally(()=>{this.renderHighestPriority()}).catch(e=>{e instanceof n.RenderingCancelledException||console.error(`renderView: "${e}"`)})}return!0}}},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.TextHighlighter=void 0;t.TextHighlighter=class{constructor({findController:e,eventBus:t,pageIndex:i}){this.findController=e,this.matches=[],this.eventBus=t,this.pageIdx=i,this._onUpdateTextLayerMatches=null,this.textDivs=null,this.textContentItemsStr=null,this.enabled=!1}setTextMapping(e,t){this.textDivs=e,this.textContentItemsStr=t}enable(){if(!this.textDivs||!this.textContentItemsStr)throw new Error("Text divs and strings have not been set.");if(this.enabled)throw new Error("TextHighlighter is already enabled.");this.enabled=!0,this._onUpdateTextLayerMatches||(this._onUpdateTextLayerMatches=e=>{e.pageIndex!==this.pageIdx&&-1!==e.pageIndex||this._updateMatches()},this.eventBus._on("updatetextlayermatches",this._onUpdateTextLayerMatches)),this._updateMatches()}disable(){this.enabled&&(this.enabled=!1,this._onUpdateTextLayerMatches&&(this.eventBus._off("updatetextlayermatches",this._onUpdateTextLayerMatches),this._onUpdateTextLayerMatches=null))}_convertMatches(i,n){if(!i)return[];var a=this["textContentItemsStr"];let r=0,s=0;var o=a.length-1;const l=[];for(let t=0,e=i.length;t<e;t++){let e=i[t];for(;r!==o&&e>=s+a[r].length;)s+=a[r].length,r++;r===a.length&&console.error("Could not find a matching mapping");const h={begin:{divIdx:r,offset:e-s}};for(e+=n[t];r!==o&&e>s+a[r].length;)s+=a[r].length,r++;h.end={divIdx:r,offset:e-s},l.push(h)}return l}_renderMatches(a){if(0!==a.length){const{findController:p,pageIdx:f}=this,{textContentItemsStr:_,textDivs:v}=this;var r=f===p.selected.pageIdx,s=p.selected.matchIdx,t=p.state.highlightAll;let i=null;var o={divIdx:-1,offset:void 0};let e=s,n=e+1;if(t)e=0,n=a.length;else if(!r)return;for(let t=e;t<n;t++){var l=a[t],h=l.begin,l=l.end,d=r&&t===s,c=d?" selected":"";let e=0;if(i&&h.divIdx===i.divIdx?g(i.divIdx,i.offset,h.offset):(null!==i&&g(i.divIdx,i.offset,o.offset),u(h)),h.divIdx===l.divIdx)e=g(h.divIdx,h.offset,l.offset,"highlight"+c);else{e=g(h.divIdx,h.offset,o.offset,"highlight begin"+c);for(let e=h.divIdx+1,t=l.divIdx;e<t;e++)v[e].className="highlight middle"+c;u(l,"highlight end"+c)}i=l,d&&p.scrollMatchIntoView({element:v[h.divIdx],selectedLeft:e,pageIndex:f,matchIndex:s})}function u(e,t){var i=e.divIdx;v[i].textContent="",g(i,0,e.offset,t)}function g(e,t,i,n){let a=v[e];if(a.nodeType===Node.TEXT_NODE){const r=document.createElement("span");a.before(r),r.append(a),v[e]=r,a=r}e=_[e].substring(t,i),t=document.createTextNode(e);if(n){const s=document.createElement("span");return s.className=n+" appended",s.append(t),a.append(s),n.includes("selected")?s.offsetLeft:0}return a.append(t),0}i&&g(i.divIdx,i.offset,o.offset)}}_updateMatches(){if(this.enabled){var e,{findController:t,matches:n,pageIdx:a}=this,{textContentItemsStr:r,textDivs:s}=this;let i=-1;for(let e=0,t=n.length;e<t;e++){var o=n[e];for(let e=Math.max(i,o.begin.divIdx),t=o.end.divIdx;e<=t;e++){const l=s[e];l.textContent=r[e],l.className=""}i=o.end.divIdx+1}t?.highlightMatches&&(e=t.pageMatches[a]||null,t=t.pageMatchesLength[a]||null,this.matches=this._convertMatches(e,t),this._renderMatches(this.matches))}}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.DownloadManager=void 0;var s=i(3);function n(e,t){const i=document.createElement("a");if(!i.click)throw new Error('DownloadManager: "a.click()" is not supported.');i.href=e,i.target="_parent","download"in i&&(i.download=t),(document.body||document.documentElement).append(i),i.click(),i.remove()}t.DownloadManager=class{constructor(){this._openBlobUrls=new WeakMap}downloadUrl(e,t){(0,s.createValidAbsoluteUrl)(e,"http://example.com")?n(e+"#pdfjs.action=download",t):console.error("downloadUrl - not a valid URL: "+e)}downloadData(e,t,i){n(URL.createObjectURL(new Blob([e],{type:i})),t)}openOrDownloadData(i,e,n){var a=(0,s.isPdfFile)(n),r=a?"application/pdf":"";if(a){let t=this._openBlobUrls.get(i);t||(t=URL.createObjectURL(new Blob([e],{type:r})),this._openBlobUrls.set(i,t)),a="?file="+encodeURIComponent(t+"#"+n);try{return window.open(a),!0}catch(e){console.error("openOrDownloadData: "+e),URL.revokeObjectURL(t),this._openBlobUrls.delete(i)}}return this.downloadData(e,n,r),!1}download(e,t,i){n(URL.createObjectURL(e),i)}}},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.WaitOnType=t.EventBus=t.AutomationEventBus=void 0,t.waitOnEventOrTimeout=function({target:s,name:o,delay:l=0}){return new Promise(function(t,e){if("object"!=typeof s||!o||"string"!=typeof o||!(Number.isInteger(l)&&0<=l))throw new Error("waitOnEventOrTimeout - invalid parameters.");function i(e){s instanceof d?s._off(o,n):s.removeEventListener(o,n),r&&clearTimeout(r),t(e)}const n=i.bind(null,h.EVENT);s instanceof d?s._on(o,n):s.addEventListener(o,n);var a=i.bind(null,h.TIMEOUT);const r=setTimeout(a,l)})};const h={EVENT:"event",TIMEOUT:"timeout"};t.WaitOnType=h;class d{constructor(){this._listeners=Object.create(null)}on(e,t,i=null){this._on(e,t,{external:!0,once:i?.once})}off(e,t,i=null){this._off(e,t,{external:!0,once:i?.once})}dispatch(t,i){const n=this._listeners[t];if(n&&0!==n.length){let e;for(var{listener:a,external:r,once:s}of n.slice(0))s&&this._off(t,a),r?(e||=[]).push(a):a(i);if(e){for(const o of e)o(i);e=null}}}_on(e,t,i=null){const n=this._listeners[e]||=[];n.push({listener:t,external:!0===i?.external,once:!0===i?.once})}_off(e,i,t=0){const n=this._listeners[e];if(n)for(let e=0,t=n.length;e<t;e++)if(n[e].listener===i)return void n.splice(e,1)}}class i extends(t.EventBus=d){dispatch(e,t){throw new Error("Not implemented: AutomationEventBus.dispatch")}}t.AutomationEventBus=i},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GenericL10n=void 0,i(21);var a=i(4);const n=document.webL10n;t.GenericL10n=class{constructor(i){this._lang=i,this._ready=new Promise((e,t)=>{n.setLanguage((0,a.fixupLangCode)(i),()=>{e(n)})})}async getLanguage(){const e=await this._ready;return e.getLanguage()}async getDirection(){const e=await this._ready;return e.getDirection()}async get(e,t=null,i=(0,a.getL10nFallback)(e,t)){const n=await this._ready;return n.get(e,t,i)}async translate(e){const t=await this._ready;return t.translate(e)}}},()=>{function y(e,t,i){t=t||function(e){},i=i||function(){};var n=new XMLHttpRequest;n.open("GET",e,a),n.overrideMimeType&&n.overrideMimeType("text/plain; charset=utf-8"),n.onreadystatechange=function(){4==n.readyState&&(200==n.status||0===n.status?t(n.responseText):i())},n.onerror=i,n.ontimeout=i;try{n.send(null)}catch(e){i()}}function u(e,v,a,t){var m=e.replace(/[^\/]*$/,"")||"./";function i(e,t){var c={},i=/^\s*|\s*$/,u=/^\s*#|^\s*$/,g=/^\s*\[(.*)\]\s*$/,p=/^\s*@import\s+url\((.*)\)\s*$/i,f=/^([^=\s]*)\s*=\s*(.+)$/;function _(e,a,r){var s=e.replace(i,"").split(/[\r\n]+/),o="*",l=v.split("-",1)[0],h=!1,d="";!function e(){for(;;){if(!s.length)return void r();var t,i,n=s.shift();if(!u.test(n)){if(a){if(d=g.exec(n)){o=d[1].toLowerCase(),h="*"!==o&&o!==v&&o!==l;continue}if(h)continue;if(d=p.exec(n))return t=m+d[1],i=e,void y(t,function(e){_(e,!1,i)},function(){console.warn(t+" not found."),i()})}(n=n.match(f))&&3==n.length&&(c[n[1]]=(n=n[2]).lastIndexOf("\\")<0?n:n.replace(/\\\\/g,"\\").replace(/\\n/g,"\n").replace(/\\r/g,"\r").replace(/\\t/g,"\t").replace(/\\b/g,"\b").replace(/\\f/g,"\f").replace(/\\{/g,"{").replace(/\\}/g,"}").replace(/\\"/g,'"').replace(/\\'/g,"'"))}}}()}_(e,!0,function(){t(c)})}y(e,function(e){f+=e,i(e,function(e){for(var t in e){var i,n=t.lastIndexOf("."),n=0<n?(i=t.substring(0,n),t.substring(n+1)):(i=t,d);p[i]||(p[i]={}),p[i][n]=e[t]}a&&a()})},t)}function i(e,t){e=e&&e.toLowerCase(),t=t||function(){},p={},_=f="",_=e;var i=g.querySelectorAll('link[type="application/l10n"]'),n=i.length;if(0===n){var a=(o=g.querySelector('script[type="application/l10n"]'))?JSON.parse(o.innerHTML):null;if(a&&a.locales&&a.default_locale){if(console.log("using the embedded JSON directory, early way out"),!(p=a.locales[e])){var r,s=a.default_locale.toLowerCase();for(r in a.locales){if((r=r.toLowerCase())===e){p=a.locales[e];break}r===s&&(p=a.locales[s])}}t()}else console.log("no resource to load, early way out");v="complete"}else for(var o,l=0,h=function(){n<=++l&&(t(),v="complete")},d=0;d<n;d++)new c(i[d]).load(e,h);function c(e){var i=e.href;this.load=function(e,t){u(i,e,t,function(){console.warn(i+" not found."),console.warn('"'+e+'" resource not found'),_="",t()})}}}function h(e,t,i){var n=p[e];if(!n){if(console.warn("#"+e+" is undefined."),!i)return null;n=i}var a,r,s={};for(a in n)r=function(e,i,n){return e.replace(/\{\{\s*(.+?)\s*\}\}/g,function(e,t){return i&&t in i?i[t]:t in p?p[t]:(console.log("argument {{"+t+"}} for #"+n+" is undefined."),e)})}(function(e,t,i,n){var a=/\{\[\s*([a-zA-Z]+)\(([a-zA-Z]+)\)\s*\]\}/.exec(e);if(!a||!a.length)return e;var r,s=a[1],a=a[2];t&&a in t?r=t[a]:a in p&&(r=p[a]);s in c&&(t=c[s],e=t(e,r,i,n));return e}(n[a],t,e,a),t,e),s[a]=r;return s}function r(e){var t=function(e){if(!e)return{};var t=e.getAttribute("data-l10n-id"),e=e.getAttribute("data-l10n-args"),i={};if(e)try{i=JSON.parse(e)}catch(e){console.warn("could not parse arguments for #"+t)}return{id:t,args:i}}(e);if(t.id){var i=h(t.id,t.args);if(i){if(i[d]){if(0===function(e){if(e.children)return e.children.length;if(void 0!==e.childElementCount)return e.childElementCount;for(var t=0,i=0;i<e.childNodes.length;i++)t+=1===e.nodeType?1:0;return t}(e))e[d]=i[d];else{for(var n,a=e.childNodes,r=!1,s=0,o=a.length;s<o;s++)3===a[s].nodeType&&/\S/.test(a[s].nodeValue)&&(r?a[s].nodeValue="":(a[s].nodeValue=i[d],r=!0));r||(n=g.createTextNode(i[d]),e.prepend(n))}delete i[d]}for(var l in i)e[l]=i[l]}else console.warn("#"+t.id+" is undefined.")}}var e,g,p,f,d,_,c,v,a;document.webL10n=(e=window,g=document,p={},d="textContent",v="loading",a=!(_=f=""),(c={}).plural=function(e,t,i,n){var a,r,t=parseFloat(t);if(isNaN(t))return e;if(n!=d)return e;function s(e,t){return-1!==t.indexOf(e)}function o(e,t,i){return t<=e&&e<=i}c._pluralRules||(c._pluralRules=(l={0:function(e){return"other"},1:function(e){return o(e%100,3,10)?"few":0===e?"zero":o(e%100,11,99)?"many":2==e?"two":1==e?"one":"other"},2:function(e){return 0!==e&&e%10==0?"many":2==e?"two":1==e?"one":"other"},3:function(e){return 1==e?"one":"other"},4:function(e){return o(e,0,1)?"one":"other"},5:function(e){return o(e,0,2)&&2!=e?"one":"other"},6:function(e){return 0===e?"zero":e%10==1&&e%100!=11?"one":"other"},7:function(e){return 2==e?"two":1==e?"one":"other"},8:function(e){return o(e,3,6)?"few":o(e,7,10)?"many":2==e?"two":1==e?"one":"other"},9:function(e){return 0===e||1!=e&&o(e%100,1,19)?"few":1==e?"one":"other"},10:function(e){return o(e%10,2,9)&&!o(e%100,11,19)?"few":e%10!=1||o(e%100,11,19)?"other":"one"},11:function(e){return o(e%10,2,4)&&!o(e%100,12,14)?"few":e%10==0||o(e%10,5,9)||o(e%100,11,14)?"many":e%10==1&&e%100!=11?"one":"other"},12:function(e){return o(e,2,4)?"few":1==e?"one":"other"},13:function(e){return o(e%10,2,4)&&!o(e%100,12,14)?"few":1!=e&&o(e%10,0,1)||o(e%10,5,9)||o(e%100,12,14)?"many":1==e?"one":"other"},14:function(e){return o(e%100,3,4)?"few":e%100==2?"two":e%100==1?"one":"other"},15:function(e){return 0===e||o(e%100,2,10)?"few":o(e%100,11,19)?"many":1==e?"one":"other"},16:function(e){return e%10==1&&11!=e?"one":"other"},17:function(e){return 3==e?"few":0===e?"zero":6==e?"many":2==e?"two":1==e?"one":"other"},18:function(e){return 0===e?"zero":o(e,0,2)&&0!==e&&2!=e?"one":"other"},19:function(e){return o(e,2,10)?"few":o(e,0,1)?"one":"other"},20:function(e){return!o(e%10,3,4)&&e%10!=9||o(e%100,10,19)||o(e%100,70,79)||o(e%100,90,99)?e%1e6==0&&0!==e?"many":e%10!=2||s(e%100,[12,72,92])?e%10!=1||s(e%100,[11,71,91])?"other":"one":"two":"few"},21:function(e){return 0===e?"zero":1==e?"one":"other"},22:function(e){return o(e,0,1)||o(e,11,99)?"one":"other"},23:function(e){return o(e%10,1,2)||e%20==0?"one":"other"},24:function(e){return o(e,3,10)||o(e,13,19)?"few":s(e,[2,12])?"two":s(e,[1,11])?"one":"other"}},(r={af:3,ak:4,am:4,ar:1,asa:3,az:0,be:11,bem:3,bez:3,bg:3,bh:4,bm:0,bn:3,bo:0,br:20,brx:3,bs:11,ca:3,cgg:3,chr:3,cs:12,cy:17,da:3,de:3,dv:3,dz:0,ee:3,el:3,en:3,eo:3,es:3,et:3,eu:3,fa:0,ff:5,fi:3,fil:4,fo:3,fr:5,fur:3,fy:3,ga:8,gd:24,gl:3,gsw:3,gu:3,guw:4,gv:23,ha:3,haw:3,he:2,hi:4,hr:11,hu:0,id:0,ig:0,ii:0,is:3,it:3,iu:7,ja:0,jmc:3,jv:0,ka:0,kab:5,kaj:3,kcg:3,kde:0,kea:0,kk:3,kl:3,km:0,kn:0,ko:0,ksb:3,ksh:21,ku:3,kw:7,lag:18,lb:3,lg:3,ln:4,lo:0,lt:10,lv:6,mas:3,mg:4,mk:16,ml:3,mn:3,mo:9,mr:3,ms:0,mt:15,my:0,nah:3,naq:7,nb:3,nd:3,ne:3,nl:3,nn:3,no:3,nr:3,nso:4,ny:3,nyn:3,om:3,or:3,pa:3,pap:3,pl:13,ps:3,pt:3,rm:3,ro:9,rof:3,ru:11,rwk:3,sah:0,saq:3,se:7,seh:3,ses:0,sg:0,sh:11,shi:19,sk:12,sl:14,sma:7,smi:7,smj:7,smn:7,sms:7,sn:3,so:3,sq:3,sr:11,ss:3,ssy:3,st:3,sv:3,sw:3,syr:3,ta:3,te:3,teo:3,th:0,ti:4,tig:3,tk:3,tl:4,tn:3,to:0,tr:0,ts:3,tzm:22,uk:11,ur:3,ve:3,vi:0,vun:3,wa:4,wae:3,wo:0,xh:3,xog:3,yo:0,zh:0,zu:3}[(a=_).replace(/-.*$/,"")])in l?l[r]:(console.warn("plural form unknown for ["+a+"]"),function(){return"other"})));var l="["+c._pluralRules(t)+"]";return 0===t&&i+"[zero]"in p?e=p[i+"[zero]"][n]:1==t&&i+"[one]"in p?e=p[i+"[one]"][n]:2==t&&i+"[two]"in p?e=p[i+"[two]"][n]:i+l in p?e=p[i+l][n]:i+"[other]"in p&&(e=p[i+"[other]"][n]),e},{get:function(e,t,i){var n,a=e.lastIndexOf("."),r=d,a=(0<a&&(r=e.substring(a+1),e=e.substring(0,a)),i&&((n={})[r]=i),h(e,t,n));return a&&r in a?a[r]:"{{"+e+"}}"},getData:function(){return p},getText:function(){return f},getLanguage:function(){return _},setLanguage:function(e,t){i(e,function(){t&&t()})},getDirection:function(){var e=_.split("-",1)[0];return 0<=["ar","he","fa","ps","ur"].indexOf(e)?"rtl":"ltr"},translate:function(e){e=e||g.documentElement;for(var t,i=(t=e)?t.querySelectorAll("*[data-l10n-id]"):[],n=i.length,a=0;a<n;a++)r(i[a]);r(e)},getReadyState:function(){return v},ready:function(t){t&&("complete"==v||"interactive"==v?e.setTimeout(function(){t()}):g.addEventListener&&g.addEventListener("localized",function e(){g.removeEventListener("localized",e),t()}))}})},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFFindController=t.FindState=void 0;var h=i(7),r=i(3),s=i(23);const a={FOUND:0,NOT_FOUND:1,WRAPPED:2,PENDING:3},m=(t.FindState=a,{"‐":"-","‘":"'","’":"'","‚":"'","‛":"'","“":'"',"”":'"',"„":'"',"‟":'"',"¼":"1/4","½":"1/2","¾":"3/4"}),d=new Set([12441,12442,2381,2509,2637,2765,2893,3021,3149,3277,3387,3388,3405,3530,3642,3770,3972,4153,4154,5908,5940,6098,6752,6980,7082,7083,7154,7155,11647,43014,43052,43204,43347,43456,43766,44013,3158,3953,3954,3962,3963,3964,3965,3968,3956]),n=[...d.values()].map(e=>String.fromCharCode(e)).join(""),o=/\p{M}+/gu,c=/([.*+?^${}()|[\]\\])|(\p{P})|(\s+)|(\p{M})|(\p{L})/gu,l=/([^\p{M}])\p{M}*$/u,u=/^\p{M}*([^\p{M}])/u,y=/[\uAC00-\uD7AF\uFA6C\uFACF-\uFAD1\uFAD5-\uFAD7]+/g,P=new Map;let b=null,E=null;function g(e){const h=[];let i;for(;null!==(i=y.exec(e));){let t=i["index"];for(const r of i[0]){let e=P.get(r);e||(e=r.normalize("NFD").length,P.set(r,e)),h.push([e,t++])}}let t;var n;t=0===h.length&&b?b:0<h.length&&E?E:(n=`([${Object.keys(m).join("")}])|(\\p{M}+(?:-\\n)?)|(\\S-\\n)|(\\n)`,0===h.length?b=new RegExp(n+"|(\\u0000)","gum"):E=new RegExp(n+"|([\\u1100-\\u1112\\ud7a4-\\ud7af\\ud84a\\ud84c\\ud850\\ud854\\ud857\\ud85f])","gum"));const d=[];for(;null!==(i=o.exec(e));)d.push([i[0].length,i.index]);let a=e.normalize("NFD");const c=[[0,0]];let u=0,g=0,p=0,f=0,_=0,v=!1;return a=a.replace(t,(e,i,n,t,a,r,s)=>{if(s-=f,i){var i=m[e],o=i.length;for(let e=1;e<o;e++)c.push([s-p+e,p-e]);return p-=o-1,i}if(n){e=n.endsWith("\n"),i=e?n.length-2:n.length;v=!0;let t=i;s+_===d[u]?.[1]&&(t-=d[u][0],++u);for(let e=1;e<=t;e++)c.push([s-1-p+e,p-e]);return p-=t,f+=t,e?(s+=i-1,c.push([s-p+1,1+p]),p+=1,f+=1,_+=1,n.slice(0,i)):n}if(t)return c.push([s-p+1,1+p]),p+=1,f+=1,_+=1,t.charAt(0);if(a)return c.push([s-p+1,p-1]),--p,f+=1,_+=1," ";if(s+_===h[g]?.[1]){var l=h[g][0]-1;++g;for(let e=1;e<=l;e++)c.push([s-(p-e),p-e]);p-=l,f+=l}return r}),c.push([a.length,p]),[a,c,v]}t.PDFFindController=class{constructor({linkService:e,eventBus:t}){this._linkService=e,this._eventBus=t,this.#reset(),t._on("find",this.#onFind.bind(this)),t._on("findbarclose",this.#onFindBarClose.bind(this))}get highlightMatches(){return this._highlightMatches}get pageMatches(){return this._pageMatches}get pageMatchesLength(){return this._pageMatchesLength}get selected(){return this._selected}get state(){return this._state}setDocument(e){this._pdfDocument&&this.#reset(),e&&(this._pdfDocument=e,this._firstPageCapability.resolve())}#onFind(e){if(e){const i=this._pdfDocument,n=e["type"];null!==this._state&&!this.#shouldDirtyMatch(e)||(this._dirtyMatch=!0),this._state=e,"highlightallchange"!==n&&this.#updateUIState(a.PENDING),this._firstPageCapability.promise.then(()=>{var e,t;!this._pdfDocument||i&&this._pdfDocument!==i||(this.#extractText(),e=!this._highlightMatches,t=!!this._findTimeout,this._findTimeout&&(clearTimeout(this._findTimeout),this._findTimeout=null),n?this._dirtyMatch?this.#nextMatch():"again"===n?(this.#nextMatch(),e&&this._state.highlightAll&&this.#updateAllPages()):"highlightallchange"===n?(t?this.#nextMatch():this._highlightMatches=!0,this.#updateAllPages()):this.#nextMatch():this._findTimeout=setTimeout(()=>{this.#nextMatch(),this._findTimeout=null},250))})}}scrollMatchIntoView({element:e=null,selectedLeft:t=0,pageIndex:i=-1,matchIndex:n=-1}){this._scrollMatches&&e&&-1!==n&&n===this._selected.matchIdx&&-1!==i&&i===this._selected.pageIdx&&(this._scrollMatches=!1,h.scrollIntoView)(e,{top:-50,left:t+-400},!0)}#reset(){this._highlightMatches=!1,this._scrollMatches=!1,this._pdfDocument=null,this._pageMatches=[],this._pageMatchesLength=[],this._state=null,this._selected={pageIdx:-1,matchIdx:-1},this._offset={pageIdx:null,matchIdx:null,wrapped:!1},this._extractTextPromises=[],this._pageContents=[],this._pageDiffs=[],this._hasDiacritics=[],this._matchesCountTotal=0,this._pagesToSearch=null,this._pendingFindMatches=new Set,this._resumePageIdx=null,this._dirtyMatch=!1,clearTimeout(this._findTimeout),this._findTimeout=null,this._firstPageCapability=(0,r.createPromiseCapability)()}get#query(){return this._state.query!==this._rawQuery&&(this._rawQuery=this._state.query,[this._normalizedQuery]=g(this._state.query)),this._normalizedQuery}#shouldDirtyMatch(e){if(e.query!==this._state.query)return!0;switch(e.type){case"again":var t=this._selected.pageIdx+1;const i=this._linkService;return 1<=t&&t<=i.pagesCount&&t!==i.page&&!i.isPageVisible(t)?!0:!1;case"highlightallchange":return!1}return!0}#isEntireWord(e,t,i){let n=e.slice(0,t).match(l);if(n){var a=e.charCodeAt(t),r=n[1].charCodeAt(0);if((0,s.getCharacterType)(a)===(0,s.getCharacterType)(r))return!1}if(n=e.slice(t+i).match(u)){a=e.charCodeAt(t+i-1),r=n[1].charCodeAt(0);if((0,s.getCharacterType)(a)===(0,s.getCharacterType)(r))return!1}return!0}#calculateRegExpMatch(e,t,i,n){const a=[],r=[];for(var s,o,l=this._pageDiffs[i];null!==(s=e.exec(n));)t&&!this.#isEntireWord(n,s.index,s[0].length)||([s,o]=function(e,t,i){if(!e)return[t,i];const n=t,a=t+i;let r=(0,h.binarySearchFirstItem)(e,e=>e[0]>=n),s=(e[r][0]>n&&--r,(0,h.binarySearchFirstItem)(e,e=>e[0]>=a,r));return e[s][0]>a&&--s,[n+e[r][1],i+e[s][1]-e[r][1]]}(l,s.index,s[0].length),o&&(a.push(s),r.push(o)));this._pageMatches[i]=a,this._pageMatchesLength[i]=r}#convertToRegExpString(e,s){const o=this._state["matchDiacritics"];let l=!1;return(e=e.replace(c,(e,t,i,n,a,r)=>t?`[ ]*\\${t}[ ]*`:i?`[ ]*${i}[ ]*`:n?"[ ]+":o?a||r:a?d.has(a.charCodeAt(0))?a:"":s?(l=!0,r+"\\p{M}*"):r)).endsWith("[ ]*")&&(e=e.slice(0,e.length-"[ ]*".length)),o&&s&&(l=!0,e=`${e}(?=[${n}]|[^\\p{M}]|$)`),[l,e]}#calculateMatch(e){let t=this.#query;if(0!==t.length){var{caseSensitive:n,entireWord:a,phraseSearch:r}=this._state,s=this._pageContents[e];const o=this._hasDiacritics[e];let i=!1;if(r)[i,t]=this.#convertToRegExpString(t,o);else{const l=t.match(/\S+/g);l&&(t=l.sort().reverse().map(e=>{var[e,t]=this.#convertToRegExpString(e,o);return i||=e,`(${t})`}).join("|"))}r="g"+(i?"u":"")+(n?"":"i"),n=(t=new RegExp(t,r),this.#calculateRegExpMatch(t,a,e,s),this._state.highlightAll&&this.#updatePage(e),this._resumePageIdx===e&&(this._resumePageIdx=null,this.#nextPageMatch()),this._pageMatches[e].length);0<n&&(this._matchesCountTotal+=n,this.#updateUIResultsCount())}}#extractText(){if(!(0<this._extractTextPromises.length)){let t=Promise.resolve();for(let n=0,e=this._linkService.pagesCount;n<e;n++){const a=(0,r.createPromiseCapability)();this._extractTextPromises[n]=a.promise,t=t.then(()=>this._pdfDocument.getPage(n+1).then(e=>e.getTextContent()).then(e=>{const t=[];for(const i of e.items)t.push(i.str),i.hasEOL&&t.push("\n");[this._pageContents[n],this._pageDiffs[n],this._hasDiacritics[n]]=g(t.join("")),a.resolve()},e=>{console.error("Unable to get text content for page "+(n+1),e),this._pageContents[n]="",this._pageDiffs[n]=null,this._hasDiacritics[n]=!1,a.resolve()}))}}}#updatePage(e){this._scrollMatches&&this._selected.pageIdx===e&&(this._linkService.page=e+1),this._eventBus.dispatch("updatetextlayermatches",{source:this,pageIndex:e})}#updateAllPages(){this._eventBus.dispatch("updatetextlayermatches",{source:this,pageIndex:-1})}#nextMatch(){var e=this._state.findPrevious,t=this._linkService.page-1,i=this._linkService.pagesCount;if(this._highlightMatches=!0,this._dirtyMatch){this._dirtyMatch=!1,this._selected.pageIdx=this._selected.matchIdx=-1,this._offset.pageIdx=t,this._offset.matchIdx=null,this._offset.wrapped=!1,this._resumePageIdx=null,this._pageMatches.length=0,this._pageMatchesLength.length=0,this._matchesCountTotal=0,this.#updateAllPages();for(let e=0;e<i;e++)this._pendingFindMatches.has(e)||(this._pendingFindMatches.add(e),this._extractTextPromises[e].then(()=>{this._pendingFindMatches.delete(e),this.#calculateMatch(e)}))}if(""===this.#query)this.#updateUIState(a.FOUND);else if(!this._resumePageIdx){const n=this._offset;if(this._pagesToSearch=i,null!==n.matchIdx){t=this._pageMatches[n.pageIdx].length;if(!e&&n.matchIdx+1<t||e&&0<n.matchIdx)return n.matchIdx=e?n.matchIdx-1:n.matchIdx+1,void this.#updateMatch(!0);this.#advanceOffsetPage(e)}this.#nextPageMatch()}}#matchesReady(e){const t=this._offset;var e=e.length,i=this._state.findPrevious;return e?(t.matchIdx=i?e-1:0,this.#updateMatch(!0),!0):(this.#advanceOffsetPage(i),!!(t.wrapped&&(t.matchIdx=null,this._pagesToSearch<0))&&(this.#updateMatch(!1),!0))}#nextPageMatch(){null!==this._resumePageIdx&&console.error("There can only be one pending page.");let e=null;do{var t=this._offset.pageIdx;if(!(e=this._pageMatches[t])){this._resumePageIdx=t;break}}while(!this.#matchesReady(e))}#advanceOffsetPage(e){const t=this._offset;var i=this._linkService.pagesCount;t.pageIdx=e?t.pageIdx-1:t.pageIdx+1,t.matchIdx=null,this._pagesToSearch--,(t.pageIdx>=i||t.pageIdx<0)&&(t.pageIdx=e?i-1:0,t.wrapped=!0)}#updateMatch(e=!1){let t=a.NOT_FOUND;var i=this._offset.wrapped;this._offset.wrapped=!1,e&&(e=this._selected.pageIdx,this._selected.pageIdx=this._offset.pageIdx,this._selected.matchIdx=this._offset.matchIdx,t=i?a.WRAPPED:a.FOUND,-1!==e&&e!==this._selected.pageIdx&&this.#updatePage(e)),this.#updateUIState(t,this._state.findPrevious),-1!==this._selected.pageIdx&&(this._scrollMatches=!0,this.#updatePage(this._selected.pageIdx))}#onFindBarClose(e){const t=this._pdfDocument;this._firstPageCapability.promise.then(()=>{!this._pdfDocument||t&&this._pdfDocument!==t||(this._findTimeout&&(clearTimeout(this._findTimeout),this._findTimeout=null),this._resumePageIdx&&(this._resumePageIdx=null,this._dirtyMatch=!0),this.#updateUIState(a.FOUND),this._highlightMatches=!1,this.#updateAllPages())})}#requestMatchesCount(){var{pageIdx:t,matchIdx:e}=this._selected;let i=0,n=this._matchesCountTotal;if(-1!==e){for(let e=0;e<t;e++)i+=this._pageMatches[e]?.length||0;i+=e+1}return{current:i=i<1||i>n?n=0:i,total:n}}#updateUIResultsCount(){this._eventBus.dispatch("updatefindmatchescount",{source:this,matchesCount:this.#requestMatchesCount()})}#updateUIState(e,t=!1){this._eventBus.dispatch("updatefindcontrolstate",{source:this,state:e,previous:t,matchesCount:this.#requestMatchesCount(),rawQuery:this._state?.query??null})}}},(e,t)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.CharacterType=void 0,t.getCharacterType=function(e){if(e<11904)return 0==(65408&e)?function(e){return 32===e||9===e||13===e||10===e}(e)?i.SPACE:function(e){return 97<=e&&e<=122||65<=e&&e<=90}(e)||function(e){return 48<=e&&e<=57}(e)||95===e?i.ALPHA_LETTER:i.PUNCT:3584==(65408&e)?i.THAI_LETTER:160===e?i.SPACE:i.ALPHA_LETTER;{if(function(e){return 13312<=e&&e<=40959||63744<=e&&e<=64255}(e))return i.HAN_LETTER;if(function(e){return 12448<=e&&e<=12543}(e))return i.KATAKANA_LETTER;if(function(e){return 12352<=e&&e<=12447}(e))return i.HIRAGANA_LETTER;if(function(e){return 65376<=e&&e<=65439}(e))return i.HALFWIDTH_KATAKANA_LETTER}return i.ALPHA_LETTER};const i={SPACE:0,ALPHA_LETTER:1,PUNCT:2,HAN_LETTER:3,KATAKANA_LETTER:4,HIRAGANA_LETTER:5,HALFWIDTH_KATAKANA_LETTER:6,THAI_LETTER:7};t.CharacterType=i},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFHistory=void 0,t.isDestArraysEqual=l,t.isDestHashesEqual=a;var r=i(7),s=i(19);function o(){return document.location.hash}function a(e,t){return"string"==typeof e&&"string"==typeof t&&(e===t||(0,r.parseQueryString)(e).get("nameddest")===t)}function l(i,n){if(!Array.isArray(i)||!Array.isArray(n))return!1;if(i.length!==n.length)return!1;for(let e=0,t=i.length;e<t;e++)if(!function e(t,i){if(typeof t==typeof i&&!Array.isArray(t)&&!Array.isArray(i)){if(null===t||"object"!=typeof t||null===i)return t===i||Number.isNaN(t)&&Number.isNaN(i);if(Object.keys(t).length===Object.keys(i).length){for(const n in t)if(!e(t[n],i[n]))return;return 1}}}(i[e],n[e]))return!1;return!0}t.PDFHistory=class{constructor({linkService:e,eventBus:t}){this.linkService=e,this.eventBus=t,this._initialized=!1,this._fingerprint="",this.reset(),this._boundEvents=null,this.eventBus._on("pagesinit",()=>{this._isPagesLoaded=!1,this.eventBus._on("pagesloaded",e=>{this._isPagesLoaded=!!e.pagesCount},{once:!0})})}initialize({fingerprint:e,resetHistory:t=!1,updateUrl:i=!1}){if(e&&"string"==typeof e){this._initialized&&this.reset();var n,a,r=""!==this._fingerprint&&this._fingerprint!==e,e=(this._fingerprint=e,this._updateUrl=!0===i,this._initialized=!0,this._bindEvents(),window.history.state);if(this._popStateInProgress=!1,this._blockHashChange=0,this._currentHash=o(),this._numPositionUpdates=0,this._uid=this._maxUid=0,this._destination=null,this._position=null,!this._isValidState(e,!0)||t)return{hash:i,page:n,rotation:a}=this._parseCurrentHash(!0),!i||r||t?void this._pushOrReplaceState(null,!0):void this._pushOrReplaceState({hash:i,page:n,rotation:a},!0);r=e.destination;this._updateInternalState(r,e.uid,!0),void 0!==r.rotation&&(this._initialRotation=r.rotation),r.dest?(this._initialBookmark=JSON.stringify(r.dest),this._destination.page=null):r.hash?this._initialBookmark=r.hash:r.page&&(this._initialBookmark="page="+r.page)}else console.error('PDFHistory.initialize: The "fingerprint" must be a non-empty string.')}reset(){this._initialized&&(this._pageHide(),this._initialized=!1,this._unbindEvents()),this._updateViewareaTimeout&&(clearTimeout(this._updateViewareaTimeout),this._updateViewareaTimeout=null),this._initialBookmark=null,this._initialRotation=null}push({namedDest:t=null,explicitDest:i,pageNumber:n}){if(this._initialized)if(t&&"string"!=typeof t)console.error("PDFHistory.push: "+`"${t}" is not a valid namedDest parameter.`);else if(Array.isArray(i))if(this._isValidPage(n)||null===n&&!this._destination){t=t||JSON.stringify(i);if(t){let e=!1;if(this._destination&&(a(this._destination.hash,t)||l(this._destination.dest,i))){if(this._destination.page)return;e=!0}this._popStateInProgress&&!e||(this._pushOrReplaceState({dest:i,hash:t,page:n,rotation:this.linkService.rotation},e),this._popStateInProgress||(this._popStateInProgress=!0,Promise.resolve().then(()=>{this._popStateInProgress=!1})))}}else console.error("PDFHistory.push: "+`"${n}" is not a valid pageNumber parameter.`);else console.error("PDFHistory.push: "+`"${i}" is not a valid explicitDest parameter.`)}pushPage(e){this._initialized&&(this._isValidPage(e)?this._destination?.page===e||this._popStateInProgress||(this._pushOrReplaceState({dest:null,hash:"page="+e,page:e,rotation:this.linkService.rotation}),this._popStateInProgress||(this._popStateInProgress=!0,Promise.resolve().then(()=>{this._popStateInProgress=!1}))):console.error(`PDFHistory.pushPage: "${e}" is not a valid page number.`))}pushCurrentPosition(){this._initialized&&!this._popStateInProgress&&this._tryPushCurrentPosition()}back(){var e;this._initialized&&!this._popStateInProgress&&(e=window.history.state,this._isValidState(e)&&0<e.uid&&window.history.back())}forward(){var e;this._initialized&&!this._popStateInProgress&&(e=window.history.state,this._isValidState(e)&&e.uid<this._maxUid&&window.history.forward())}get popStateInProgress(){return this._initialized&&(this._popStateInProgress||0<this._blockHashChange)}get initialBookmark(){return this._initialized?this._initialBookmark:null}get initialRotation(){return this._initialized?this._initialRotation:null}_pushOrReplaceState(e,t=!1){var t=t||!this._destination,i={fingerprint:this._fingerprint,uid:t?this._uid:this._uid+1,destination:e};this._updateInternalState(e,i.uid);let n;if(this._updateUrl&&e?.hash){const a=document.location.href.split("#")[0];a.startsWith("file://")||(n=a+"#"+e.hash)}t?window.history.replaceState(i,"",n):window.history.pushState(i,"",n)}_tryPushCurrentPosition(e=!1){if(this._position){let t=this._position;if(e&&((t=Object.assign(Object.create(null),this._position)).temporary=!0),this._destination){if(this._destination.temporary)this._pushOrReplaceState(t,!0);else if(this._destination.hash!==t.hash&&(this._destination.page||!(this._numPositionUpdates<=50))){let e=!1;if(this._destination.page>=t.first&&this._destination.page<=t.page){if(void 0!==this._destination.dest||!this._destination.first)return;e=!0}this._pushOrReplaceState(t,e)}}else this._pushOrReplaceState(t)}}_isValidPage(e){return Number.isInteger(e)&&0<e&&e<=this.linkService.pagesCount}_isValidState(e,t=!1){if(!e)return!1;if(e.fingerprint!==this._fingerprint){if(!t)return!1;if("string"!=typeof e.fingerprint||e.fingerprint.length!==this._fingerprint.length)return!1;var[t]=performance.getEntriesByType("navigation");if("reload"!==t?.type)return!1}return!(!Number.isInteger(e.uid)||e.uid<0)&&(null!==e.destination&&"object"==typeof e.destination)}_updateInternalState(e,t,i=!1){this._updateViewareaTimeout&&(clearTimeout(this._updateViewareaTimeout),this._updateViewareaTimeout=null),i&&e?.temporary&&delete e.temporary,this._destination=e,this._uid=t,this._maxUid=Math.max(this._maxUid,t),this._numPositionUpdates=0}_parseCurrentHash(e=!1){var t=unescape(o()).substring(1);const i=(0,r.parseQueryString)(t);var n=i.get("nameddest")||"";let a=0|i.get("page");return{hash:t,page:a=!this._isValidPage(a)||e&&0<n.length?null:a,rotation:this.linkService.rotation}}_updateViewarea({location:e}){this._updateViewareaTimeout&&(clearTimeout(this._updateViewareaTimeout),this._updateViewareaTimeout=null),this._position={hash:e.pdfOpenParams.substring(1),page:this.linkService.page,first:e.pageNumber,rotation:e.rotation},this._popStateInProgress||(this._isPagesLoaded&&this._destination&&!this._destination.page&&this._numPositionUpdates++,this._updateViewareaTimeout=setTimeout(()=>{this._popStateInProgress||this._tryPushCurrentPosition(!0),this._updateViewareaTimeout=null},1e3))}_popState({state:e}){var t,i,n=o(),a=this._currentHash!==n;if(this._currentHash=n,!e)return this._uid++,{hash:n,page:t,rotation:i}=this._parseCurrentHash(),void this._pushOrReplaceState({hash:n,page:t,rotation:i},!0);this._isValidState(e)&&(this._popStateInProgress=!0,a&&(this._blockHashChange++,(0,s.waitOnEventOrTimeout)({target:window,name:"hashchange",delay:1e3}).then(()=>{this._blockHashChange--})),n=e.destination,this._updateInternalState(n,e.uid,!0),(0,r.isValidRotation)(n.rotation)&&(this.linkService.rotation=n.rotation),n.dest?this.linkService.goToDestination(n.dest):n.hash?this.linkService.setHash(n.hash):n.page&&(this.linkService.page=n.page),Promise.resolve().then(()=>{this._popStateInProgress=!1}))}_pageHide(){this._destination&&!this._destination.temporary||this._tryPushCurrentPosition()}_bindEvents(){this._boundEvents||(this._boundEvents={updateViewarea:this._updateViewarea.bind(this),popState:this._popState.bind(this),pageHide:this._pageHide.bind(this)},this.eventBus._on("updateviewarea",this._boundEvents.updateViewarea),window.addEventListener("popstate",this._boundEvents.popState),window.addEventListener("pagehide",this._boundEvents.pageHide))}_unbindEvents(){this._boundEvents&&(this.eventBus._off("updateviewarea",this._boundEvents.updateViewarea),window.removeEventListener("popstate",this._boundEvents.popState),window.removeEventListener("pagehide",this._boundEvents.pageHide),this._boundEvents=null)}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.PDFScriptingManager=void 0;var h=i(7),r=i(3);t.PDFScriptingManager=class{constructor({eventBus:e,sandboxBundleSrc:t=null,scriptingFactory:i=null,docPropertiesLookup:n=null}){this._pdfDocument=null,this._pdfViewer=null,this._closeCapability=null,this._destroyCapability=null,this._scripting=null,this._mouseState=Object.create(null),this._ready=!1,this._eventBus=e,this._sandboxBundleSrc=t,this._scriptingFactory=i,this._docPropertiesLookup=n,this._scriptingFactory||window.addEventListener("updatefromsandbox",e=>{this._eventBus.dispatch("updatefromsandbox",{source:window,detail:e.detail})})}setViewer(e){this._pdfViewer=e}async setDocument(e){if(this._pdfDocument&&await this._destroyScripting(),this._pdfDocument=e){var[t,i,n]=await Promise.all([e.getFieldObjects(),e.getCalculationOrderIds(),e.getJSActions()]);if(t||n){if(e===this._pdfDocument){try{this._scripting=this._createScripting()}catch(e){return console.error(`PDFScriptingManager.setDocument: "${e?.message}".`),void await this._destroyScripting()}this._internalEvents.set("updatefromsandbox",e=>{e?.source===window&&this._updateFromSandbox(e.detail)}),this._internalEvents.set("dispatcheventinsandbox",e=>{this._scripting?.dispatchEventInSandbox(e.detail)}),this._internalEvents.set("pagechanging",({pageNumber:e,previous:t})=>{e!==t&&(this._dispatchPageClose(t),this._dispatchPageOpen(e))}),this._internalEvents.set("pagerendered",({pageNumber:e})=>{this._pageOpenPending.has(e)&&e===this._pdfViewer.currentPageNumber&&this._dispatchPageOpen(e)}),this._internalEvents.set("pagesdestroy",async e=>{await this._dispatchPageClose(this._pdfViewer.currentPageNumber),await this._scripting?.dispatchEventInSandbox({id:"doc",name:"WillClose"}),this._closeCapability?.resolve()}),this._domEvents.set("mousedown",e=>{this._mouseState.isDown=!0}),this._domEvents.set("mouseup",e=>{this._mouseState.isDown=!1});for(var[a,r]of this._internalEvents)this._eventBus._on(a,r);for(var[s,o]of this._domEvents)window.addEventListener(s,o,!0);try{var l=await this._getDocProperties();if(e!==this._pdfDocument)return;await this._scripting.createSandbox({objects:t,calculationOrder:i,appInfo:{platform:navigator.platform,language:navigator.language},docInfo:{...l,actions:n}}),this._eventBus.dispatch("sandboxcreated",{source:this})}catch(e){return console.error(`PDFScriptingManager.setDocument: "${e?.message}".`),void await this._destroyScripting()}await this._scripting?.dispatchEventInSandbox({id:"doc",name:"Open"}),await this._dispatchPageOpen(this._pdfViewer.currentPageNumber,!0),Promise.resolve().then(()=>{e===this._pdfDocument&&(this._ready=!0)})}}else await this._destroyScripting()}}async dispatchWillSave(e){return this._scripting?.dispatchEventInSandbox({id:"doc",name:"WillSave"})}async dispatchDidSave(e){return this._scripting?.dispatchEventInSandbox({id:"doc",name:"DidSave"})}async dispatchWillPrint(e){return this._scripting?.dispatchEventInSandbox({id:"doc",name:"WillPrint"})}async dispatchDidPrint(e){return this._scripting?.dispatchEventInSandbox({id:"doc",name:"DidPrint"})}get mouseState(){return this._mouseState}get destroyPromise(){return this._destroyCapability?.promise||null}get ready(){return this._ready}get _internalEvents(){return(0,r.shadow)(this,"_internalEvents",new Map)}get _domEvents(){return(0,r.shadow)(this,"_domEvents",new Map)}get _pageOpenPending(){return(0,r.shadow)(this,"_pageOpenPending",new Set)}get _visitedPages(){return(0,r.shadow)(this,"_visitedPages",new Map)}async _updateFromSandbox(e){var t=this._pdfViewer.isInPresentationMode||this._pdfViewer.isChangingPresentationMode,{id:i,siblings:n,command:a,value:r}=e;if(i){if(!t||!e.focus){delete e.id,delete e.siblings;for(const o of n?[i,...n]:[i]){const l=document.querySelector(`[data-element-id="${o}"]`);l?l.dispatchEvent(new CustomEvent("updatefromsandbox",{detail:e})):this._pdfDocument?.annotationStorage.setValue(o,e)}}}else switch(a){case"clear":console.clear();break;case"error":console.error(r);break;case"layout":if(t)return;var s=(0,h.apiPageLayoutToViewerModes)(r);this._pdfViewer.spreadMode=s.spreadMode;break;case"page-num":this._pdfViewer.currentPageNumber=r+1;break;case"print":await this._pdfViewer.pagesPromise,this._eventBus.dispatch("print",{source:this});break;case"println":console.log(r);break;case"zoom":if(t)return;this._pdfViewer.currentScaleValue=r;break;case"SaveAs":this._eventBus.dispatch("download",{source:this});break;case"FirstPage":this._pdfViewer.currentPageNumber=1;break;case"LastPage":this._pdfViewer.currentPageNumber=this._pdfViewer.pagesCount;break;case"NextPage":this._pdfViewer.nextPage();break;case"PrevPage":this._pdfViewer.previousPage();break;case"ZoomViewIn":if(t)return;this._pdfViewer.increaseScale();break;case"ZoomViewOut":if(t)return;this._pdfViewer.decreaseScale()}}async _dispatchPageOpen(t,e=!1){const i=this._pdfDocument,n=this._visitedPages;if(e&&(this._closeCapability=(0,r.createPromiseCapability)()),this._closeCapability){const a=this._pdfViewer.getPageView(t-1);a?.renderingState!==h.RenderingStates.FINISHED?this._pageOpenPending.add(t):(this._pageOpenPending.delete(t),e=(async()=>{var e=await(n.has(t)?null:a.pdfPage?.getJSActions());i===this._pdfDocument&&await this._scripting?.dispatchEventInSandbox({id:"page",name:"PageOpen",pageNumber:t,actions:e})})(),n.set(t,e))}}async _dispatchPageClose(e){const t=this._pdfDocument,i=this._visitedPages;var n;this._closeCapability&&!this._pageOpenPending.has(e)&&(n=i.get(e))&&(i.set(e,null),await n,t===this._pdfDocument&&await this._scripting?.dispatchEventInSandbox({id:"page",name:"PageClose",pageNumber:e}))}async _getDocProperties(){if(this._docPropertiesLookup)return this._docPropertiesLookup(this._pdfDocument);const e=i(26)["docPropertiesLookup"];return e(this._pdfDocument)}_createScripting(){if(this._destroyCapability=(0,r.createPromiseCapability)(),this._scripting)throw new Error("_createScripting: Scripting already exists.");if(this._scriptingFactory)return this._scriptingFactory.createScripting({sandboxBundleSrc:this._sandboxBundleSrc});const e=i(26)["GenericScripting"];return new e(this._sandboxBundleSrc)}async _destroyScripting(){if(!this._scripting)return this._pdfDocument=null,void this._destroyCapability?.resolve();this._closeCapability&&(await Promise.race([this._closeCapability.promise,new Promise(e=>{setTimeout(e,1e3)})]).catch(e=>{}),this._closeCapability=null),this._pdfDocument=null;try{await this._scripting.destroySandbox()}catch(e){}for(var[e,t]of this._internalEvents)this._eventBus._off(e,t);this._internalEvents.clear();for(var[i,n]of this._domEvents)window.removeEventListener(i,n,!0);this._domEvents.clear(),this._pageOpenPending.clear(),this._visitedPages.clear(),this._scripting=null,delete this._mouseState.isDown,this._ready=!1,this._destroyCapability?.resolve()}}},(e,t,i)=>{Object.defineProperty(t,"__esModule",{value:!0}),t.GenericScripting=void 0,t.docPropertiesLookup=async function(e){var t="".split("#")[0];let{info:i,metadata:n,contentDispositionFilename:a,contentLength:r}=await e.getMetadata();{var s;r||(s=(await e.getDownloadInfo())["length"],r=s)}return{...i,baseURL:t,filesize:r,filename:a||(0,o.getPdfFilenameFromUrl)(""),metadata:n?.getRaw(),authors:n?.get("dc:creator"),numPages:e.numPages,URL:""}};var o=i(3);t.GenericScripting=class{constructor(e){this._ready=(0,o.loadScript)(e,!0).then(()=>window.pdfjsSandbox.QuickJSSandbox())}async createSandbox(e){const t=await this._ready;t.create(e)}async dispatchEventInSandbox(e){const t=await this._ready;setTimeout(()=>t.dispatchEvent(e),0)}async destroySandbox(){const e=await this._ready;e.nukeSandbox()}}}],n={};function a(e){var t=n[e];if(void 0!==t)return t.exports;t=n[e]={exports:{}};return i[e](t,t.exports,a),t.exports}var e,t,r,s,o,l,h,d,c,u,g,p,f,_,v,m,y,P={};return e=P,Object.defineProperty(e,"__esModule",{value:!0}),Object.defineProperty(e,"AnnotationLayerBuilder",{enumerable:!0,get:function(){return l.AnnotationLayerBuilder}}),Object.defineProperty(e,"DefaultAnnotationLayerFactory",{enumerable:!0,get:function(){return t.DefaultAnnotationLayerFactory}}),Object.defineProperty(e,"DefaultStructTreeLayerFactory",{enumerable:!0,get:function(){return t.DefaultStructTreeLayerFactory}}),Object.defineProperty(e,"DefaultTextLayerFactory",{enumerable:!0,get:function(){return t.DefaultTextLayerFactory}}),Object.defineProperty(e,"DefaultXfaLayerFactory",{enumerable:!0,get:function(){return t.DefaultXfaLayerFactory}}),Object.defineProperty(e,"DownloadManager",{enumerable:!0,get:function(){return h.DownloadManager}}),Object.defineProperty(e,"EventBus",{enumerable:!0,get:function(){return d.EventBus}}),Object.defineProperty(e,"GenericL10n",{enumerable:!0,get:function(){return c.GenericL10n}}),Object.defineProperty(e,"LinkTarget",{enumerable:!0,get:function(){return r.LinkTarget}}),Object.defineProperty(e,"NullL10n",{enumerable:!0,get:function(){return u.NullL10n}}),Object.defineProperty(e,"PDFFindController",{enumerable:!0,get:function(){return g.PDFFindController}}),Object.defineProperty(e,"PDFHistory",{enumerable:!0,get:function(){return p.PDFHistory}}),Object.defineProperty(e,"PDFLinkService",{enumerable:!0,get:function(){return r.PDFLinkService}}),Object.defineProperty(e,"PDFPageView",{enumerable:!0,get:function(){return f.PDFPageView}}),Object.defineProperty(e,"PDFScriptingManager",{enumerable:!0,get:function(){return _.PDFScriptingManager}}),Object.defineProperty(e,"PDFSinglePageViewer",{enumerable:!0,get:function(){return o.PDFSinglePageViewer}}),Object.defineProperty(e,"PDFViewer",{enumerable:!0,get:function(){return o.PDFViewer}}),Object.defineProperty(e,"ProgressBar",{enumerable:!0,get:function(){return s.ProgressBar}}),Object.defineProperty(e,"RenderingStates",{enumerable:!0,get:function(){return s.RenderingStates}}),Object.defineProperty(e,"ScrollMode",{enumerable:!0,get:function(){return s.ScrollMode}}),Object.defineProperty(e,"SimpleLinkService",{enumerable:!0,get:function(){return r.SimpleLinkService}}),Object.defineProperty(e,"SpreadMode",{enumerable:!0,get:function(){return s.SpreadMode}}),Object.defineProperty(e,"StructTreeLayerBuilder",{enumerable:!0,get:function(){return v.StructTreeLayerBuilder}}),Object.defineProperty(e,"TextLayerBuilder",{enumerable:!0,get:function(){return m.TextLayerBuilder}}),Object.defineProperty(e,"XfaLayerBuilder",{enumerable:!0,get:function(){return y.XfaLayerBuilder}}),Object.defineProperty(e,"parseQueryString",{enumerable:!0,get:function(){return s.parseQueryString}}),t=a(1),r=a(6),s=a(7),o=a(11),l=a(5),h=a(18),d=a(19),c=a(20),u=a(4),g=a(22),p=a(24),f=a(13),_=a(25),v=a(8),m=a(9),y=a(10),P})());