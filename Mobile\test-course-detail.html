<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>课程详情测试页面</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .test-title {
            text-align: center;
            color: #c00714;
            margin-bottom: 30px;
            font-size: 24px;
            font-weight: 600;
        }
        
        .test-item {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-item h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }
        
        .test-item p {
            margin: 0 0 10px 0;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        
        .test-btn {
            background: #c00714;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 14px;
            text-decoration: none;
            display: inline-block;
            transition: background 0.3s ease;
        }
        
        .test-btn:hover {
            background: #a00610;
        }
        
        .status {
            margin-top: 10px;
            padding: 8px 12px;
            border-radius: 4px;
            font-size: 12px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <div class="test-container">
        <h1 class="test-title">课程详情页面测试</h1>
        
        <div class="test-item">
            <h3>🎨 1. 界面优化测试</h3>
            <p>检查播放按钮是否更精致，红色卡片是否更清爽简洁。</p>
            <a href="pages/course-detail.html?id=test1" class="test-btn" target="_blank">测试界面优化</a>
            <div class="status success">
                ✓ 播放按钮更小巧精致，卡片设计更清爽
            </div>
        </div>
        
        <div class="test-item">
            <h3>⏱ 2. 动态计时功能测试</h3>
            <p>验证计时按钮持续走动显示，返回时有移动端友好的确认对话框。</p>
            <a href="pages/course-detail.html?id=test2" class="test-btn" target="_blank">测试动态计时</a>
            <div class="status info">
                ℹ 计时按钮会动态显示时间和动画效果，返回时显示移动端确认框
            </div>
        </div>
        
        <div class="test-item">
            <h3>🎥 3. 视频全屏功能测试</h3>
            <p>检查视频播放器是否有全屏按钮，以及播放/暂停按钮是否在视频中央。</p>
            <a href="pages/course-detail.html?id=test3" class="test-btn" target="_blank">测试视频功能</a>
            <div class="status info">
                ℹ 视频控制条右侧应有全屏按钮，视频中央有大型播放/暂停按钮
            </div>
        </div>
        
        <div class="test-item">
            <h3>📊 4. 学习记录提交测试</h3>
            <p>验证学习路径是否正确提交到后台服务器。</p>
            <button class="test-btn" onclick="checkLocalStorage()">检查本地记录</button>
            <div id="recordStatus" class="status info">
                ℹ 点击按钮查看本地存储的学习记录
            </div>
        </div>
    </div>

    <script>
        function checkLocalStorage() {
            const records = localStorage.getItem('jilu');
            const statusDiv = document.getElementById('recordStatus');
            
            if (records) {
                const parsedRecords = JSON.parse(records);
                statusDiv.className = 'status success';
                statusDiv.innerHTML = `✓ 找到 ${parsedRecords.length} 条学习记录<br>最新记录: ${parsedRecords[parsedRecords.length - 1]?.timestamp || '无'}`;
            } else {
                statusDiv.className = 'status info';
                statusDiv.innerHTML = 'ℹ 暂无本地学习记录，请先访问课程详情页面';
            }
        }
        
        // 页面加载时检查
        window.onload = function() {
            checkLocalStorage();
        };
    </script>
</body>
</html>
