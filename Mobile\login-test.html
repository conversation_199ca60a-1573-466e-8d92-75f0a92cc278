<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>登录测试 - 思政一体化平台</title>
    
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: #f5f5f5;
            padding: 20px;
            margin: 0;
        }
        
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 8px;
            padding: 20px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }
        
        .title {
            text-align: center;
            color: #c00714;
            margin-bottom: 30px;
            font-size: 20px;
            font-weight: 600;
        }
        
        .section {
            margin-bottom: 20px;
            padding: 15px;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
        }
        
        .section-title {
            font-weight: 600;
            margin-bottom: 10px;
            color: #333;
        }
        
        .btn {
            display: block;
            width: 100%;
            padding: 12px;
            background: #c00714;
            color: white;
            text-decoration: none;
            border-radius: 6px;
            text-align: center;
            margin-bottom: 10px;
            border: none;
            font-size: 14px;
            cursor: pointer;
        }
        
        .btn:hover {
            background: #a00610;
        }
        
        .btn.secondary {
            background: #6c757d;
        }
        
        .btn.secondary:hover {
            background: #5a6268;
        }
        
        .info {
            background: #e7f3ff;
            padding: 10px;
            border-radius: 4px;
            font-size: 12px;
            color: #0066cc;
            margin-bottom: 10px;
        }
        
        .status {
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 10px;
            font-size: 14px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .code {
            background: #f8f9fa;
            padding: 8px;
            border-radius: 4px;
            font-family: monospace;
            font-size: 12px;
            word-break: break-all;
            margin: 5px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="title">统一认证登录测试</div>
        
        <!-- 登录状态检查 -->
        <div class="section">
            <div class="section-title">当前登录状态</div>
            <div id="loginStatus" class="status info">检查中...</div>
            <button class="btn secondary" onclick="checkLoginStatus()">刷新状态</button>
        </div>
        
        <!-- 登录测试 -->
        <div class="section">
            <div class="section-title">登录测试</div>
            <div class="info">
                点击下方按钮测试统一认证登录流程
            </div>
            <button class="btn" onclick="testLogin()">测试统一认证登录</button>
            <button class="btn secondary" onclick="clearLoginData()">清除登录数据</button>
        </div>
        
        <!-- URL信息 -->
        <div class="section">
            <div class="section-title">URL信息</div>
            <div>当前页面：</div>
            <div class="code" id="currentUrl"></div>
            <div>登录回调URL：</div>
            <div class="code" id="callbackUrl"></div>
            <div>CAS登录URL：</div>
            <div class="code" id="casUrl"></div>
        </div>
        
        <!-- 调试信息 -->
        <div class="section">
            <div class="section-title">调试信息</div>
            <div id="debugInfo" class="code" style="max-height: 200px; overflow-y: auto;"></div>
            <button class="btn secondary" onclick="clearDebugInfo()">清除调试信息</button>
        </div>
        
        <!-- 快捷操作 -->
        <div class="section">
            <div class="section-title">快捷操作</div>
            <a href="index.html" class="btn secondary">返回首页</a>
            <a href="userinfo-simple.html" class="btn secondary">访问登录处理页面</a>
        </div>
    </div>

    <script>
        // 调试日志
        function log(message) {
            const debugInfo = document.getElementById('debugInfo');
            const timestamp = new Date().toLocaleTimeString();
            debugInfo.innerHTML += `[${timestamp}] ${message}\n`;
            debugInfo.scrollTop = debugInfo.scrollHeight;
            console.log(message);
        }

        // 清除调试信息
        function clearDebugInfo() {
            document.getElementById('debugInfo').innerHTML = '';
        }

        // 检查登录状态
        function checkLoginStatus() {
            const userinfo = sessionStorage.getItem('userinfo');
            const header = sessionStorage.getItem('header');
            const statusDiv = document.getElementById('loginStatus');
            
            if (userinfo && header) {
                try {
                    const user = JSON.parse(userinfo);
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `
                        已登录<br>
                        用户：${user.name || user.realName || '未知'}<br>
                        Token：${header.substring(0, 20)}...
                    `;
                    log('登录状态：已登录 - ' + (user.name || user.realName));
                } catch (e) {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = '登录数据格式错误';
                    log('登录状态：数据格式错误 - ' + e.message);
                }
            } else {
                statusDiv.className = 'status error';
                statusDiv.innerHTML = '未登录';
                log('登录状态：未登录');
            }
        }

        // 清除登录数据
        function clearLoginData() {
            sessionStorage.removeItem('userinfo');
            sessionStorage.removeItem('header');
            log('已清除登录数据');
            checkLoginStatus();
        }

        // 测试登录
        function testLogin() {
            const serviceUrl = getCallbackUrl();
            const casUrl = `https://cas.sntcm.edu.cn/lyuapServer/login?service=${encodeURIComponent(serviceUrl)}`;
            
            log('开始测试登录');
            log('回调URL: ' + serviceUrl);
            log('CAS URL: ' + casUrl);
            
            // 跳转到CAS登录页面
            window.location.href = casUrl;
        }

        // 获取回调URL
        function getCallbackUrl() {
            const origin = window.location.origin;
            const pathname = window.location.pathname;
            const basePath = pathname.substring(0, pathname.lastIndexOf('/') + 1);
            return origin + basePath + 'userinfo-simple.html';
        }

        // 更新URL信息
        function updateUrlInfo() {
            document.getElementById('currentUrl').textContent = window.location.href;
            
            const callbackUrl = getCallbackUrl();
            document.getElementById('callbackUrl').textContent = callbackUrl;
            
            const casUrl = `https://cas.sntcm.edu.cn/lyuapServer/login?service=${encodeURIComponent(callbackUrl)}`;
            document.getElementById('casUrl').textContent = casUrl;
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            log('页面加载完成');
            checkLoginStatus();
            updateUrlInfo();
            
            // 检查是否有ticket参数（从CAS返回）
            const urlParams = new URLSearchParams(window.location.search);
            const ticket = urlParams.get('ticket');
            if (ticket) {
                log('检测到ticket参数: ' + ticket);
                log('这表明从CAS登录页面返回，但应该跳转到userinfo-simple.html处理');
            }
        });

        // 监听存储变化
        window.addEventListener('storage', function(e) {
            if (e.key === 'userinfo' || e.key === 'header') {
                log('检测到登录状态变化');
                checkLoginStatus();
            }
        });
    </script>
</body>
</html>
