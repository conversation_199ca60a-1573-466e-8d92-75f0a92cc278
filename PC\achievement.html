<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-个人中心</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/wangEditor.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<!-- 导航增强脚本 -->
		<script src="./js/nav-enhancement.js" type="text/javascript" charset="utf-8"></script>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a href="studentasks.html" class="leftitem">学习任务</a>
						<a href="learningrecords.html" class="leftitem">学习路径</a>
						<a class="leftitem activeleftitem">考试成绩</a>
						<a href="releaseyourvoice.html" class="leftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="righttopview2">
						<label class="accccc">考试成绩</label>
					</div>
					<div class="xuekeselect">
						<select id="xueke" onchange="xuekechange()">
							
						</select>
					</div>
					<div class="cjlistview" id="paperlist">
						
						<!-- <div class="mycjitem">
							<div class="mysjtime">
								<div class="sjtime1">118</div>
								<div class="sjtime2">
									<div>58</div>
									<div>主观题</div>
								</div>
								<div class="sjtime2">
									<div>58</div>
									<div>客观题</div>
								</div>
							</div>
							<div class="mysjname">这是一段试卷名称这是一段试卷名称这是一段试卷名称这是一段试卷名称</div>
							<div class="mysjstr"><img src="./img/flsq.png"/>思想道德修养</div>
							<div class="mysjstr"><img src="./img/sj2.png"/>2022年3月5日 21:00:06</div>
							<div class="sjjxbtn">试卷解析</div>
						</div> -->
						
					</div>
					<div class="fybox" id="fyq">
						<span id="sy">首页</span>
						<span id="syy">上一页</span>
						<div class="num" id="num">
						</div>
						<span id="xyy">下一页</span>
						<span id="wy">尾页</span>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		
		<!-- 试卷解析弹窗 -->
		<div id="analysisModal" class="analysis-modal" style="display: none;">
			<div class="analysis-modal-overlay" onclick="closeAnalysisModal()"></div>
			<div class="analysis-modal-content">
				<div class="analysis-modal-header">
					<h2 id="paperTitleModal">试卷解析</h2>
					<button class="analysis-modal-close" onclick="closeAnalysisModal()">×</button>
				</div>
				<div class="analysis-modal-body">
					<!-- 试卷基本信息 -->
					<div class="paper-info-card">
						<div class="paper-info-title">
							<i class="paper-icon">📋</i>
							<span>试卷信息</span>
						</div>
						<div class="paper-info-grid">
							<div class="info-item">
								<label>学科：</label>
								<span id="modalSubjectName">-</span>
							</div>
							<div class="info-item">
								<label>考试时间：</label>
								<span id="modalExamTime">-</span>
							</div>
							<div class="info-item">
								<label>总分：</label>
								<span id="modalTotalScore">-</span>
							</div>
							<div class="info-item">
								<label>及格分：</label>
								<span id="modalPassScore">-</span>
							</div>
						</div>
					</div>
					
					<!-- 成绩概览 -->
					<div class="score-overview-card">
						<div class="score-overview-title">
							<i class="score-icon">🎯</i>
							<span>成绩概览</span>
						</div>
						<div class="score-stats">
							<div class="score-stat-item total-score">
								<div class="stat-value" id="modalMyTotalScore">0</div>
								<div class="stat-label">总得分</div>
							</div>
							<div class="score-stat-item objective-score">
								<div class="stat-value" id="modalObjectiveScore">0</div>
								<div class="stat-label">客观题得分</div>
							</div>
							<div class="score-stat-item subjective-score">
								<div class="stat-value" id="modalSubjectiveScore">0</div>
								<div class="stat-label">主观题得分</div>
							</div>
						</div>
					</div>
					
					<!-- 题目类型统计 -->
					<div class="question-types-card">
						<div class="question-types-title">
							<i class="types-icon">📊</i>
							<span>题型分析</span>
						</div>
						<div class="question-types-grid" id="questionTypesGrid">
							<!-- 动态生成题型统计 -->
						</div>
					</div>
					
					<!-- 详细题目解析 -->
					<div class="detailed-analysis-card">
						<div class="detailed-analysis-title">
							<i class="analysis-icon">🔍</i>
							<span>详细解析</span>
						</div>
						<div class="analysis-navigation">
							<select id="questionTypeSelect" onchange="switchQuestionType()">
								<option value="">选择题型</option>
								<option value="0">单选题</option>
								<option value="1">多选题</option>
								<option value="2">判断题</option>
								<option value="3">简答题</option>
								<option value="4">论述题</option>
								<option value="5">材料分析题</option>
							</select>
							<div class="question-nav-buttons">
								<button id="prevQuestionBtn" onclick="navigateQuestion(-1)" disabled>上一题</button>
								<span id="questionPosition">1/1</span>
								<button id="nextQuestionBtn" onclick="navigateQuestion(1)" disabled>下一题</button>
							</div>
						</div>
						<div id="questionDetailContainer">
							<div class="no-question-selected">
								<p>请选择题型查看详细解析</p>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let pageindex = 1
			let pagesize = 15
			let pages = 1
			
			let userinfo = sessionStorage.getItem("userinfo")
			
			
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getuserpaper()
				getfooterlink()
				getxueke()
			})
			function xuekechange(){
				pageindex = 1
				getuserpaper()
			}
			function getuserpaper(){
				let msubjectId = $("#xueke").val()
				if(msubjectId=='0'){
					msubjectId = null
				}
				$.ajax({
					url: baseurl + "/paper/answeredAll",
					type: 'GET',
					contentType: "application/json",
					data: {
						pageNum: pageindex,
						pageSize: pagesize,
						subjectId: msubjectId
					},
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// 数据预处理：按试卷ID分组，保留最高分记录
							let paperMap = new Map();
							
							res.data.list.forEach((item) => {
								if (item.cmsTestPaper) {
									const paperId = item.cmsTestPaper.id;
									const currentScore = parseFloat(item.score) || 0;
									
									// 如果该试卷已存在且当前分数更高，或者该试卷不存在，则更新记录
									if (!paperMap.has(paperId) || currentScore > parseFloat(paperMap.get(paperId).score || 0)) {
										paperMap.set(paperId, item);
									}
								}
							});
							
							// 将Map转换为数组并按时间倒序排列
							let uniquePapers = Array.from(paperMap.values()).sort((a, b) => {
								return new Date(b.createdAt) - new Date(a.createdAt);
							});
							
							let html = ''
							uniquePapers.map((item)=>{
								if(item.cmsTestPaper){
									if(item.isMark === 0){
										html+='<div class="mycjitem"><div class="mysjtime"><div class="sjtime1">---</div>'+
											'<div class="sjtime2"><div>---</div><div>主观题</div></div>'+
											'<div class="sjtime2"><div>---</div><div>客观题</div></div></div>'+
										'<div class="mysjname">'+item.cmsTestPaper.name+'</div>'+
										'<div class="mysjstr"><img src="./img/flsq.png"/>'+item.subjectName+'</div>'+
										'<div class="mysjstr"><img src="./img/sj2.png"/>'+setDate(item.createdAt)+'</div>'
										html+='<div class="sjjxbtn2">阅卷中...</div>'
									}else{
										html+='<div class="mycjitem"><div class="mysjtime"><div class="sjtime1">'+parseInt(item.score)+'</div>'+
											'<div class="sjtime2"><div>'+parseInt(item.zhuguanScore)+'</div><div>主观题</div></div>'+
											'<div class="sjtime2"><div>'+parseInt(item.keguanTotalScore)+'</div><div>客观题</div></div></div>'+
										'<div class="mysjname">'+item.cmsTestPaper.name+'</div>'+
										'<div class="mysjstr"><img src="./img/flsq.png"/>'+item.subjectName+'</div>'+
										'<div class="mysjstr"><img src="./img/sj2.png"/>'+setDate(item.createdAt)+'</div>'
										html+='<div class="sjjxbtn" onclick="injiexi(this)" data-id="'+item.id+'" data-taskid="'+item.taskId+'">试卷解析</div>'
									}
									html+='</div>'
								}
							})
							$("#paperlist").html(html)
							
							// 分页逻辑保持原有逻辑，但基于去重后的数据
							let totalItems = uniquePapers.length;
							pages = Math.ceil(totalItems / pagesize);
							
							if (pages > 1) {
								let numhtml = ""
								for (let a = 1; a <= pages; a++) {
									if (pageindex == a) {
										numhtml += '<label class="actinum" onclick="getnewlist(' + a + ')">' + a +
											'</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
						}
					}
				})
			}
			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是第最后一页了！")
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！")
					}
				} else {
					pageindex = index
					getuserpaper()
				}
			}
			function injiexi(item){
				let taskid = ""
				if($(item).attr("data-taskid")!="null"){
					taskid = $(item).attr("data-taskid")
				}
				// window.location.href = 'examination2info.html?id='+$(item).attr("data-id")+'&taskid=' + taskid
				
				// 打开试卷解析弹窗
				openAnalysisModal($(item).attr("data-id"), taskid)
			}
			
			// 试卷解析弹窗相关变量
			let analysisData = null
			let currentQuestionType = ''
			let currentQuestionIndex = 0
			let currentQuestions = []
			
			// 题型映射
			const typeMap = {
				'0': '单选题',
				'1': '多选题', 
				'2': '判断题',
				'3': '简答题',
				'4': '论述题',
				'5': '材料分析题'
			}
			
			// 打开试卷解析弹窗
			function openAnalysisModal(paperId, taskId) {
				// 临时移除body的padding-top，并记录原值
				const body = document.body
				const originalPadding = window.getComputedStyle(body).paddingTop
				body.setAttribute('data-original-padding', originalPadding)
				body.style.paddingTop = '0'
				body.style.overflow = 'hidden'
				
				$("#analysisModal").show()
				
				// 初始化拖动功能
				initDraggable();
				
				// 获取试卷解析数据
				getAnalysisData(paperId, taskId)
			}
			
			// 初始化拖动功能
			function initDraggable() {
				const modal = document.querySelector('.analysis-modal-content');
				const header = document.querySelector('.analysis-modal-header');
				
				let isDragging = false;
				let offsetX, offsetY;
				
				header.addEventListener('mousedown', (e) => {
					isDragging = true;
					offsetX = e.clientX - modal.getBoundingClientRect().left;
					offsetY = e.clientY - modal.getBoundingClientRect().top;
					modal.style.transition = 'none';
					modal.style.cursor = 'grabbing';
				});
				
				document.addEventListener('mousemove', (e) => {
					if (isDragging) {
						const x = e.clientX - offsetX;
						const y = e.clientY - offsetY;
						
						// 确保不会拖出视口
						const maxX = window.innerWidth - modal.offsetWidth;
						const maxY = window.innerHeight - modal.offsetHeight;
						
						const boundedX = Math.max(0, Math.min(x, maxX));
						const boundedY = Math.max(0, Math.min(y, maxY));
						
						modal.style.left = boundedX + 'px';
						modal.style.top = boundedY + 'px';
						modal.style.transform = 'none';
					}
				});
				
				document.addEventListener('mouseup', () => {
					if (isDragging) {
						isDragging = false;
						modal.style.cursor = 'default';
					}
				});
			}
			
			// 关闭试卷解析弹窗
			function closeAnalysisModal() {
				$("#analysisModal").hide()
				
				// 恢复body的原始样式
				const body = document.body
				const originalPadding = body.getAttribute('data-original-padding')
				if (originalPadding) {
					body.style.paddingTop = originalPadding
					body.removeAttribute('data-original-padding')
				}
				body.style.overflow = 'auto'
				
				// 移除拖动事件监听器
				removeDraggableListeners();
				
				// 重置数据
				analysisData = null
				currentQuestionType = ''
				currentQuestionIndex = 0
				currentQuestions = []
				$("#questionTypeSelect").val('')
				$("#questionDetailContainer").html('<div class="no-question-selected"><p>请选择题型查看详细解析</p></div>')
			}
			
			// 移除拖动事件监听器
			function removeDraggableListeners() {
				const modal = document.querySelector('.analysis-modal-content');
				const header = document.querySelector('.analysis-modal-header');
				
				// 重置样式
				modal.style.transform = 'translate(-50%, -50%)';
				modal.style.top = '50%';
				modal.style.left = '50%';
				modal.style.cursor = 'default';
			}
			
			// 获取试卷解析数据
			function getAnalysisData(paperId, taskId) {
				// 显示加载状态
				$("#paperTitleModal").text("正在加载试卷解析...")
				
				$.ajax({
					url: baseurl + "/paper/analysis",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						id: paperId,
						taskId: taskId
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							analysisData = res.data
							displayAnalysisData(res.data)
						} else {
							cocoMessage.error(1000, "获取试卷解析失败：" + (res.message || "未知错误"))
							closeAnalysisModal()
						}
					},
					error: (xhr, status, error) => {
						console.error("获取试卷解析失败:", error)
						cocoMessage.error(1000, "网络错误，请稍后重试")
						closeAnalysisModal()
					}
				})
			}
			
			// 显示试卷解析数据
			function displayAnalysisData(data) {
				const paper = data.cmsTestPaper
				
				// 设置标题
				$("#paperTitleModal").text(paper.name + " - 试卷解析")
				
				// 设置试卷基本信息 - 修复总分和及格分显示
				$("#modalSubjectName").text(data.subjectName || '-')
				$("#modalExamTime").text(paper.limitedTime ? paper.limitedTime + '分钟' : '-')
				
				// 计算试卷总分（题目分数总和）
				let totalScore = 0;
				if (paper.cmsSubjectList && paper.cmsSubjectList.length > 0) {
					paper.cmsSubjectList.forEach(subject => {
						totalScore += parseInt(subject.score) || 0;
					});
				} else {
					totalScore = data.totalScore || paper.score || 0;
				}
				
				// 计算及格分（总分的60%）
				const passScore = Math.round(totalScore * 0.6);
				
				$("#modalTotalScore").text(totalScore + '分');
				$("#modalPassScore").text(passScore + '分');
				
				// 设置成绩信息
				$("#modalMyTotalScore").text(parseInt(data.score) || 0)
				$("#modalObjectiveScore").text(parseInt(data.keguanTotalScore) || 0)
				$("#modalSubjectiveScore").text(parseInt(data.zhuguanScore) || 0)
				
				// 统计题型数据
				const questionStats = {
					'0': { total: 0, correct: 0, wrong: 0 },
					'1': { total: 0, correct: 0, wrong: 0 },
					'2': { total: 0, correct: 0, wrong: 0 },
					'3': { total: 0, correct: 0, wrong: 0, answered: 0, unanswered: 0 },
					'4': { total: 0, correct: 0, wrong: 0, answered: 0, unanswered: 0 },
					'5': { total: 0, correct: 0, wrong: 0, answered: 0, unanswered: 0 }
				}
				
				paper.cmsSubjectList.forEach(item => {
					const type = item.type
					if (questionStats[type]) {
						questionStats[type].total++
						
						// 判断是否有答题记录
						if (item.listRecord && item.listRecord[0]) {
							// 主观题（简答题、论述题、材料分析题）
							if (type === '3' || type === '4' || type === '5') {
								questionStats[type].answered++
								// 仍然保留正确/错误统计，但不在UI中显示
								if (item.listRecord[0].results === '正确') {
									questionStats[type].correct++
								} else {
									questionStats[type].wrong++
								}
							} 
							// 客观题（单选、多选、判断）
							else {
								if (item.listRecord[0].results === '正确') {
									questionStats[type].correct++
								} else {
									questionStats[type].wrong++
								}
							}
						} else {
							// 没有答题记录，视为未答
							if (type === '3' || type === '4' || type === '5') {
								questionStats[type].unanswered++
							}
						}
					}
				})
				
				// 显示题型统计
				displayQuestionTypesGrid(questionStats)
				
				// 初始化题型分析的展开/折叠功能
				initCollapsibleSections();
			}
			
			// 初始化题型分析的展开/折叠功能
			function initCollapsibleSections() {
				// 为题型分析标题添加点击事件
				$('.question-types-title').on('click', function() {
					// 切换题型分析网格的显示状态
					$('.question-types-grid').slideToggle(200);
					
					// 切换展开/折叠图标
					$(this).toggleClass('collapsed');
				});
			}
			
			// 显示题型统计网格
			function displayQuestionTypesGrid(stats) {
				let html = ''
				
				Object.keys(stats).forEach(type => {
					const stat = stats[type]
					if (stat.total > 0) {
						// 根据题型不同显示不同的统计信息
						let statsHtml = '';
						
						// 主观题（简答题、论述题、材料分析题）显示已答/未答
						if (type === '3' || type === '4' || type === '5') {
							statsHtml = `
								<div>共 ${stat.total} 题</div>
								<div>
									<span class="answered-count">已答 ${stat.answered}</span> / 
									<span class="unanswered-count">未答 ${stat.unanswered}</span>
								</div>
							`;
						} else {
							// 客观题（单选、多选、判断）显示对/错
							statsHtml = `
								<div>共 ${stat.total} 题</div>
								<div>
									<span class="correct-count">对 ${stat.correct}</span> / 
									<span class="wrong-count">错 ${stat.wrong}</span>
								</div>
							`;
						}
						
						html += `
							<div class="question-type-item" onclick="selectQuestionType('${type}')">
								<div class="type-name">${typeMap[type]}</div>
								<div class="type-stats">
									${statsHtml}
								</div>
							</div>
						`;
					}
				})
				
				$("#questionTypesGrid").html(html)
			}
			
			// 选择题型
			function selectQuestionType(type) {
				$("#questionTypeSelect").val(type)
				switchQuestionType()
			}
			
			// 切换题型
			function switchQuestionType() {
				const selectedType = $("#questionTypeSelect").val()
				
				if (!selectedType || !analysisData) {
					$("#questionDetailContainer").html('<div class="no-question-selected"><p>请选择题型查看详细解析</p></div>')
					return
				}
				
				// 筛选当前题型的题目
				currentQuestions = analysisData.cmsTestPaper.cmsSubjectList.filter(item => item.type === selectedType)
				currentQuestionType = selectedType
				currentQuestionIndex = 0
				
				if (currentQuestions.length > 0) {
					updateNavigationButtons()
					displayCurrentQuestion()
				} else {
					$("#questionDetailContainer").html('<div class="no-question-selected"><p>该题型暂无题目</p></div>')
				}
			}
			
			// 题目导航
			function navigateQuestion(direction) {
				const newIndex = currentQuestionIndex + direction
				
				if (newIndex >= 0 && newIndex < currentQuestions.length) {
					currentQuestionIndex = newIndex
					updateNavigationButtons()
					displayCurrentQuestion()
				}
			}
			
			// 更新导航按钮状态
			function updateNavigationButtons() {
				$("#prevQuestionBtn").prop("disabled", currentQuestionIndex <= 0)
				$("#nextQuestionBtn").prop("disabled", currentQuestionIndex >= currentQuestions.length - 1)
				$("#questionPosition").text(`${currentQuestionIndex + 1}/${currentQuestions.length}`)
			}
			
			// 显示当前题目详情
			function displayCurrentQuestion() {
				if (currentQuestions.length === 0) return
				
				const question = currentQuestions[currentQuestionIndex]
				const record = question.listRecord && question.listRecord[0]
				
				// 先清空内容，避免滚动位置保留
				$("#questionDetailContainer").scrollTop(0);
				
				let html = `
					<div class="question-detail">
						<div class="question-header">
							<div class="question-title">
								第 ${currentQuestionIndex + 1} 题 (${question.score}分)
								${record ? `<span class="result-indicator ${getResultClass(record.results, question.type)}">${getResultText(record.results, question.type)}</span>` : ''}
							</div>
							<div class="question-meta">
								<span>题型：${typeMap[question.type]}</span>
								<span>分值：${question.score}分</span>
								${record ? `<span>得分：${record.score || 0}分</span>` : ''}
							</div>
						</div>
						
						<div class="question-content">
							<div class="question-text">${question.name}</div>
				`
				
				// 根据题型显示不同内容
				if (question.type === '0' || question.type === '1') {
					// 单选题或多选题
					html += '<div class="question-options">'
					if (question.cmsSubjectOption) {
						question.cmsSubjectOption.forEach(option => {
							let optionClass = 'option-item'
							
							// 判断选项状态
							if (record && record.answered) {
								if (question.type === '0') {
									// 单选题
									if (option.keyword === record.answered) {
										optionClass += ' selected'
									}
								} else {
									// 多选题
									if (record.answered.includes(option.keyword)) {
										optionClass += ' selected'
									}
								}
							}
							
							// 显示正确答案
							if (question.answer && question.answer.includes(option.keyword)) {
								optionClass += ' correct'
							}
							
							html += `<div class="${optionClass}">${option.keyword}. ${option.name}</div>`
						})
					}
					html += '</div>'
					
					// 显示答案分析
					html += `
						<div class="answer-analysis">
							<div class="analysis-section">
								<div class="analysis-label">正确答案：</div>
								<div class="analysis-content">${question.answer || '未设置'}</div>
							</div>
							${record ? `
								<div class="analysis-section">
									<div class="analysis-label">您的答案：</div>
									<div class="analysis-content">${record.answered || '未作答'}</div>
								</div>
							` : ''}
					`
				} else if (question.type === '2') {
					// 判断题
					html += '<div class="question-options">'
					const userAnswer = record ? record.answered : null
					
					html += `
						<div class="option-item ${userAnswer === 'true' ? 'selected' : ''} ${question.answer === 'true' ? 'correct' : ''}">正确</div>
						<div class="option-item ${userAnswer === 'false' ? 'selected' : ''} ${question.answer === 'false' ? 'correct' : ''}">错误</div>
					`
					html += '</div>'
					
					// 显示答案分析
					html += `
						<div class="answer-analysis">
							<div class="analysis-section">
								<div class="analysis-label">正确答案：</div>
								<div class="analysis-content">${question.answer === 'true' ? '正确' : '错误'}</div>
							</div>
							${record ? `
								<div class="analysis-section">
									<div class="analysis-label">您的答案：</div>
									<div class="analysis-content">${userAnswer === 'true' ? '正确' : userAnswer === 'false' ? '错误' : '未作答'}</div>
								</div>
							` : ''}
					`
				} else {
					// 简答题、论述题、材料分析题
					html += `
						<div class="answer-analysis">
							${record && record.answered ? `
								<div class="analysis-section">
									<div class="analysis-label">您的答案：</div>
									<div class="analysis-content">${record.answered}</div>
								</div>
							` : `
								<div class="analysis-section">
									<div class="analysis-label">您的答案：</div>
									<div class="analysis-content">未作答</div>
								</div>
							`}
					`
				}
				
				// 添加题目解析和知识点
				if (question.answer && (question.type === '3' || question.type === '4' || question.type === '5')) {
					html += `
						<div class="analysis-section">
							<div class="analysis-label">参考答案：</div>
							<div class="analysis-content">${question.answer.replace(/\n/g, '<br>')}</div>
						</div>
					`
				}
				
				// 添加教师评语（如果有）
				if (record && record.comment) {
					html += `
						<div class="analysis-section">
							<div class="analysis-label">教师评语：</div>
							<div class="analysis-content teacher-comment">${record.comment.replace(/\n/g, '<br>')}</div>
						</div>
					`
				}
				
				if (question.knowledge) {
					html += `
						<div class="analysis-section">
							<div class="analysis-label">相关知识点：</div>
							<div class="analysis-content">${question.knowledge}</div>
						</div>
					`
				}
				
				html += `
						</div>
					</div>
				</div>
				`
				
				$("#questionDetailContainer").html(html)
			}
			
			// 获取结果样式类
			function getResultClass(result, questionType) {
				// 对主观题特殊处理
				if (questionType === '3' || questionType === '4' || questionType === '5') {
					return 'result-subjective';
				}
				
				// 客观题保持原有逻辑
				if (result === '正确') return 'result-correct';
				if (result === '错误') return 'result-wrong';
				return 'result-partial';
			}
			
			// 获取结果文本
			function getResultText(result, questionType) {
				// 对主观题特殊处理
				if (questionType === '3' || questionType === '4' || questionType === '5') {
					return '已批阅';
				}
				
				// 客观题保持原有文本
				return result || '未评分';
			}
			
			// 格式化日期
			function formatDate(dateString) {
				if (!dateString) return '未知'
				const date = new Date(dateString)
				return date.toLocaleString('zh-CN', {
					year: 'numeric',
					month: '2-digit',
					day: '2-digit',
					hour: '2-digit',
					minute: '2-digit'
				})
			}
			
			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
			function getxueke(){//获取学科树
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let xuekehtml = "<option value=0>请选择学科</option>"
							res.data.map((item)=>{
								xuekehtml+='<option value="'+item.id+'">'+item.name+'</option>'
							})
							$("#xueke").html(xuekehtml)
						}
					}
				})
			}
			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 						.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '年' + MM + '月' + d + '日 ' + h + ':' + m + ':' + s
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
		<style>
			/* 导航菜单容器 */
			.leftitembox {
				background: transparent;
				padding: 20px 0;
				position: relative;
				z-index: 2;
				min-height: 450px; /* 确保导航栏最小高度一致 */
				box-sizing: border-box;
			}
			
			.leftitembox::before {
				content: '';
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 85%;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.15), transparent);
			}
			
			/* 导航菜单项 - 现代化设计与图标，移除slideIn动画 */
			.leftitem {
				height: auto !important;
				padding: 16px 24px !important;
				display: flex !important;
				justify-content: flex-start !important;
				align-items: center !important;
				font-size: 15px !important;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif !important;
				font-weight: 500 !important;
				color: #4a5568 !important;
				border-bottom: none !important;
				text-decoration: none !important;
				transition: color 0.2s ease, background-color 0.2s ease !important;
				position: relative !important;
				margin: 4px 16px !important;
				border-radius: 12px !important;
				background: transparent !important;
				overflow: hidden !important;
				min-height: 48px !important;
				box-sizing: border-box !important;
				/* 移除slideIn动画，导航项立即显示 */
				animation: none !important;
				opacity: 1 !important;
				transform: translateX(0) !important;
				animation-delay: 0s !important;
			}
			
			/* 为不同菜单项添加对应的SVG图标 */
			.leftitem::before {
				content: '';
				width: 20px;
				height: 20px;
				margin-right: 12px;
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				opacity: 0.7;
				transition: opacity 0.2s ease;
				flex-shrink: 0;
			}
			
			/* 个人信息图标 */
			.leftitem[href*="userinfo"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
			}
			
			/* 学习任务图标 */
			.leftitem[href*="studentasks"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm-1 16H9V7h9v14z'/%3E%3C/svg%3E");
			}
			
			/* 学习路径图标 */
			.leftitem[href*="learningrecords"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M2 20h20v-4H2v4zm2-3h2v2H4v-2zM2 4v4h20V4H2zm4 3H4V5h2v2zm-4 7h20v-4H2v4zm2-3h2v2H4v-2z'/%3E%3C/svg%3E");
			}
			
			/* 考试成绩图标 - 激活状态 */
			.leftitem[href*="achievement"]:not(.activeleftitem)::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E");
			}
			
			/* 发布心声图标 */
			.leftitem[href*="voice"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z'/%3E%3C/svg%3E");
			}
			
			/* 菜单项悬浮态 */
			.leftitem:hover {
				background: linear-gradient(135deg, rgba(220, 53, 69, 0.06), rgba(231, 76, 60, 0.08)) !important;
				color: #dc3545 !important;
			}
			
			.leftitem:hover::before {
				opacity: 1;
			}
			
			/* 激活状态的菜单项 - 统一标准双色渐变背景 */
			.leftitem.activeleftitem,
			a.leftitem.activeleftitem,
			.leftitembox .leftitem.activeleftitem,
			#teambox .leftitem.activeleftitem {
				font-weight: 700 !important;
				color: white !important;
				background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
				background-size: 200% 200% !important;
				animation: activeGradient 3s ease infinite !important;
				box-shadow: 0 4px 16px rgba(220, 53, 69, 0.25) !important;
				border-radius: 12px !important;
				opacity: 1 !important;
				transform: translateX(0) !important;
			}
			
			@keyframes activeGradient {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.leftitem.activeleftitem::before,
			a.leftitem.activeleftitem::before,
			.leftitembox .leftitem.activeleftitem::before,
			#teambox .leftitem.activeleftitem::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E") !important;
				opacity: 1 !important;
				filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
			}
			
			/* 移除所有导航项的动画延迟 */
			.leftitem:nth-child(1),
			.leftitem:nth-child(2), 
			.leftitem:nth-child(3), 
			.leftitem:nth-child(4), 
			.leftitem:nth-child(5), 
			.leftitem:nth-child(6), 
			.leftitem:nth-child(7) { 
				animation: none !important;
				animation-delay: 0s !important;
				opacity: 1 !important;
				transform: translateX(0) !important;
			}
		</style>
		
		<style>
			/* 试卷解析弹窗样式 */
			.analysis-modal {
				position: fixed !important;
				top: 0 !important;
				left: 0 !important;
				right: 0 !important;
				bottom: 0 !important;
				width: 100vw !important;
				height: 100vh !important;
				z-index: 999999 !important;
				margin: 0 !important;
				padding: 0 !important;
				box-sizing: border-box !important;
			}
			
			.analysis-modal-overlay {
				position: absolute !important;
				top: 0 !important;
				left: 0 !important;
				right: 0 !important;
				bottom: 0 !important;
				width: 100% !important;
				height: 100% !important;
				background: rgba(0, 0, 0, 0.6) !important;
				backdrop-filter: blur(5px);
				margin: 0 !important;
				padding: 0 !important;
			}
			
			.analysis-modal-content {
				position: absolute !important;
				top: 50% !important;
				left: 50% !important;
				transform: translate(-50%, -50%) !important;
				width: calc(100vw - 40px) !important;
				max-width: 1000px !important;
				height: 80vh !important; /* 增加高度为视口高度的80% */
				max-height: 90vh !important; /* 限制最大高度为视口的90% */
				background: white !important;
				border-radius: 12px !important;
				box-shadow: 0 15px 35px rgba(220, 53, 69, 0.15) !important;
				overflow: hidden !important;
				animation: modalSlideIn 0.3s ease-out !important;
				display: flex !important;
				flex-direction: column !important;
				z-index: 1000000 !important;
				margin: 0 !important;
				padding: 0 !important;
				box-sizing: border-box !important;
				resize: both !important; /* 允许调整大小 */
				min-height: 500px !important; /* 设置最小高度 */
				min-width: 600px !important; /* 设置最小宽度 */
			}
			
			@keyframes modalSlideIn {
				from {
					opacity: 0;
					transform: translate(-50%, -50%) translateY(-20px) scale(0.97) !important;
				}
				to {
					opacity: 1;
					transform: translate(-50%, -50%) translateY(0) scale(1) !important;
				}
			}
			
			.analysis-modal-header {
				background: linear-gradient(135deg, #dc3545, #e74c3c);
				color: white;
				padding: 16px 20px;
				display: flex;
				justify-content: space-between;
				align-items: center;
				border-bottom: 1px solid rgba(255, 255, 255, 0.1);
				flex-shrink: 0;
				cursor: grab; /* 添加抓取光标 */
			}
			
			.analysis-modal-header h2 {
				margin: 0;
				font-size: 1.2rem;
				font-weight: 600;
				line-height: 1.3;
			}
			
			.analysis-modal-close {
				background: none;
				border: none;
				color: white;
				font-size: 24px;
				cursor: pointer;
				padding: 0;
				width: 28px;
				height: 28px;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: background-color 0.2s ease;
				flex-shrink: 0;
			}
			
			.analysis-modal-close:hover {
				background: rgba(255, 255, 255, 0.2);
			}
			
			.analysis-modal-body {
				padding: 18px;
				overflow-y: auto; /* 允许垂直滚动 */
				flex: 1;
				height: calc(100% - 60px); /* 减去header的高度 */
				max-height: none; /* 移除最大高度限制，使用父元素的固定高度 */
				display: block; /* 改为block布局，允许内容自然流动 */
			}
			
			/* 卡片通用样式 */
			.paper-info-card,
			.score-overview-card,
			.question-types-card,
			.detailed-analysis-card {
				background: #f8f9fa;
				border-radius: 8px;
				padding: 10px 12px; /* 减小内边距 */
				margin-bottom: 10px; /* 减小底部边距 */
				border-left: 3px solid #dc3545;
				flex-shrink: 0; /* 防止卡片被压缩 */
			}
			
			.paper-info-title,
			.score-overview-title,
			.question-types-title,
			.detailed-analysis-title {
				font-size: 0.9rem; /* 减小字体大小 */
				font-weight: 600;
				color: #dc3545;
				margin-bottom: 8px; /* 减小底部边距 */
				display: flex;
				align-items: center;
				gap: 5px; /* 减小间距 */
				cursor: pointer; /* 添加指针光标，表明可点击 */
			}
			
			/* 题型分析标题的展开/折叠图标 */
			.question-types-title::after {
				content: '▼';
				font-size: 0.7rem;
				margin-left: auto;
				transition: transform 0.2s ease;
			}
			
			.question-types-title.collapsed::after {
				transform: rotate(-90deg);
			}
			
			/* 试卷信息网格 */
			.paper-info-grid {
				display: grid;
				grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
				gap: 8px; /* 减小间距 */
			}
			
			.info-item {
				display: flex;
				align-items: center;
				gap: 5px; /* 减小间距 */
			}
			
			.info-item label {
				font-weight: 500;
				color: #6c757d;
				min-width: 60px;
				font-size: 0.85rem;
			}
			
			.info-item span {
				color: #495057;
				font-weight: 500;
				font-size: 0.85rem;
			}
			
			/* 成绩统计样式 */
			.score-stats {
				display: flex;
				gap: 8px; /* 减小间距 */
				justify-content: space-around;
			}
			
			.score-stat-item {
				text-align: center;
				padding: 10px 6px; /* 减小内边距 */
				border-radius: 8px;
				flex: 1;
				min-width: 0;
			}
			
			.score-stat-item.total-score {
				background: linear-gradient(135deg, #dc3545, #e74c3c);
				color: white;
			}
			
			.score-stat-item.objective-score {
				background: linear-gradient(135deg, #28a745, #20c997);
				color: white;
			}
			
			.score-stat-item.subjective-score {
				background: linear-gradient(135deg, #ffc107, #fd7e14);
				color: white;
			}
			
			.stat-value {
				font-size: 1.5rem;
				font-weight: bold;
				margin-bottom: 2px;
				line-height: 1.2;
			}
			
			.stat-label {
				font-size: 0.75rem;
				opacity: 0.9;
				line-height: 1.2;
			}
			
			/* 题型分析网格 */
			.question-types-grid {
				display: grid;
				grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
				gap: 8px; /* 减小间距 */
				max-height: none; /* 移除高度限制，允许完全展开 */
				overflow-y: visible; /* 移除滚动条 */
				padding-right: 5px; /* 为滚动条留出空间 */
			}
			
			.question-type-item {
				background: white;
				padding: 8px; /* 减小内边距 */
				border-radius: 6px;
				text-align: center;
				border: 1px solid #e9ecef;
				transition: all 0.2s ease;
				cursor: pointer;
			}
			
			.question-type-item:hover {
				border-color: #dc3545;
				box-shadow: 0 2px 8px rgba(220, 53, 69, 0.1);
			}
			
			.type-name {
				font-weight: 600;
				color: #495057;
				margin-bottom: 4px; /* 减小底部边距 */
				font-size: 0.85rem;
			}
			
			.type-stats {
				font-size: 0.75rem;
				color: #6c757d;
				line-height: 1.3;
			}
			
			.correct-count {
				color: #28a745;
				font-weight: 500;
			}
			
			.wrong-count {
				color: #dc3545;
				font-weight: 500;
			}
			
			/* 添加已答/未答的样式 */
			.answered-count {
				color: #17a2b8;
				font-weight: 500;
			}
			
			.unanswered-count {
				color: #6c757d;
				font-weight: 500;
			}
			
			/* 导航控制 */
			.analysis-navigation {
				display: flex;
				justify-content: space-between;
				align-items: center;
				margin-bottom: 10px; /* 减小底部边距 */
				gap: 10px; /* 减小间距 */
			}
			
			#questionTypeSelect {
				padding: 6px 10px;
				border: 1px solid #e9ecef;
				border-radius: 4px;
				font-size: 0.85rem;
				background: white;
				color: #495057;
				outline: none;
				transition: border-color 0.2s ease;
			}
			
			#questionTypeSelect:focus {
				border-color: #dc3545;
			}
			
			.question-nav-buttons {
				display: flex;
				align-items: center;
				gap: 8px;
			}
			
			.question-nav-buttons button {
				padding: 4px 10px;
				border: 1px solid #dc3545;
				background: white;
				color: #dc3545;
				border-radius: 4px;
				cursor: pointer;
				font-size: 0.8rem;
				transition: all 0.2s ease;
			}
			
			.question-nav-buttons button:not(:disabled):hover {
				background: #dc3545;
				color: white;
			}
			
			.question-nav-buttons button:disabled {
				opacity: 0.5;
				cursor: not-allowed;
			}
			
			#questionPosition {
				font-size: 0.8rem;
				color: #6c757d;
				font-weight: 500;
				min-width: 35px;
				text-align: center;
			}
			
			/* 题目详情容器 */
			.question-detail {
				background: white;
				border-radius: 6px;
				padding: 14px;
				border: 1px solid #e9ecef;
				max-height: 500px; /* 设置最大高度 */
				overflow-y: auto; /* 添加垂直滚动条 */
			}
			
			.question-header {
				border-bottom: 1px solid #f1f3f4;
				padding-bottom: 8px; /* 减小内边距 */
				margin-bottom: 10px; /* 减小底部边距 */
			}
			
			.question-title {
				font-size: 0.95rem;
				font-weight: 600;
				color: #495057;
				margin-bottom: 6px;
				line-height: 1.4;
			}
			
			.question-meta {
				display: flex;
				flex-wrap: wrap;
				gap: 12px;
				font-size: 0.8rem;
				color: #6c757d;
			}
			
			.question-content {
				margin-bottom: 10px; /* 减小底部边距 */
			}
			
			.question-text {
				font-size: 0.9rem;
				line-height: 1.5;
				color: #495057;
				margin-bottom: 12px;
			}
			
			.question-options {
				margin: 8px 0; /* 减小边距 */
			}
			
			.option-item {
				padding: 5px 8px; /* 减小内边距 */
				margin: 3px 0; /* 减小边距 */
				border-radius: 4px;
				background: #f8f9fa;
				border-left: 2px solid transparent;
				font-size: 0.85rem;
				line-height: 1.3; /* 减小行高 */
			}
			
			.option-item.selected {
				background: #e7f3ff;
				border-left-color: #0066cc;
			}
			
			.option-item.correct {
				background: #d4edda;
				border-left-color: #28a745;
			}
			
			.option-item.wrong {
				background: #f8d7da;
				border-left-color: #dc3545;
			}
			
			/* 答案分析区域 */
			.answer-analysis {
				background: #f8f9fa;
				padding: 8px; /* 减小内边距 */
				border-radius: 6px;
				border-left: 3px solid #17a2b8;
				max-height: 400px; /* 设置最大高度 */
				overflow-y: auto; /* 添加垂直滚动条 */
			}
			
			.analysis-section {
				margin-bottom: 8px; /* 减小底部边距 */
			}
			
			.analysis-section:last-child {
				margin-bottom: 0;
			}
			
			.analysis-label {
				font-weight: 600;
				color: #495057;
				margin-bottom: 4px; /* 减小底部边距 */
				display: flex;
				align-items: center;
				gap: 5px; /* 减小间距 */
				font-size: 0.85rem;
			}
			
			.analysis-content {
				color: #6c757d;
				line-height: 1.3; /* 减小行高 */
				white-space: pre-wrap;
				font-size: 0.85rem;
				max-height: 300px; /* 最大高度 */
				overflow-y: auto; /* 添加滚动条 */
				padding: 8px;
				background: rgba(255, 255, 255, 0.5);
				border-radius: 4px;
			}
			
			.result-indicator {
				display: inline-flex;
				align-items: center;
				padding: 2px 8px;
				border-radius: 12px;
				font-size: 0.75rem;
				font-weight: 500;
				margin-left: 8px;
			}
			
			.result-correct {
				background: #d4edda;
				color: #155724;
			}
			
			.result-wrong {
				background: #f8d7da;
				color: #721c24;
			}
			
			.result-partial {
				background: #fff3cd;
				color: #856404;
			}
			
			/* 主观题结果标签样式 */
			.result-subjective {
				background: #d1ecf1;
				color: #0c5460;
			}
			
			.no-question-selected {
				text-align: center;
				padding: 30px 20px;
				color: #6c757d;
				font-style: italic;
				font-size: 0.9rem;
			}
			
			/* 移动端适配 */
			@media (max-width: 768px) {
				.analysis-modal-content {
					width: calc(100vw - 20px) !important;
					max-height: calc(100vh - 20px) !important;
				}
			
				.analysis-modal-body {
					padding: 12px;
				}
			
				.score-stats {
					flex-direction: column;
					gap: 8px;
				}
			
				.analysis-navigation {
					flex-direction: column;
					align-items: stretch;
					gap: 8px;
				}
			
				.question-nav-buttons {
					justify-content: center;
				}
			
				.question-types-grid {
					grid-template-columns: repeat(2, 1fr);
				}
			
				.paper-info-grid {
					grid-template-columns: 1fr;
					gap: 8px;
				}
				
				.analysis-modal-header h2 {
					font-size: 1.1rem;
				}
				
				.stat-value {
					font-size: 1.3rem;
				}
			}
			
			/* 滚动条样式优化 */
			.analysis-modal-body::-webkit-scrollbar,
			#questionDetailContainer::-webkit-scrollbar,
			.question-types-grid::-webkit-scrollbar,
			.analysis-content::-webkit-scrollbar,
			.teacher-comment::-webkit-scrollbar,
			.question-detail::-webkit-scrollbar,
			.answer-analysis::-webkit-scrollbar {
				width: 6px;
			}
			
			.analysis-modal-body::-webkit-scrollbar-track,
			#questionDetailContainer::-webkit-scrollbar-track,
			.question-types-grid::-webkit-scrollbar-track,
			.analysis-content::-webkit-scrollbar-track,
			.teacher-comment::-webkit-scrollbar-track,
			.question-detail::-webkit-scrollbar-track,
			.answer-analysis::-webkit-scrollbar-track {
				background: #f1f1f1;
				border-radius: 3px;
			}
			
			.analysis-modal-body::-webkit-scrollbar-thumb,
			#questionDetailContainer::-webkit-scrollbar-thumb,
			.question-types-grid::-webkit-scrollbar-thumb,
			.analysis-content::-webkit-scrollbar-thumb,
			.teacher-comment::-webkit-scrollbar-thumb,
			.question-detail::-webkit-scrollbar-thumb,
			.answer-analysis::-webkit-scrollbar-thumb {
				background: #dc3545;
				border-radius: 3px;
			}
			
			.analysis-modal-body::-webkit-scrollbar-thumb:hover,
			#questionDetailContainer::-webkit-scrollbar-thumb:hover,
			.question-types-grid::-webkit-scrollbar-thumb:hover,
			.analysis-content::-webkit-scrollbar-thumb:hover,
			.teacher-comment::-webkit-scrollbar-thumb:hover,
			.question-detail::-webkit-scrollbar-thumb:hover,
			.answer-analysis::-webkit-scrollbar-thumb:hover {
				background: #c82333;
			}
			
			/* 详细题目解析卡片特殊样式 */
			.detailed-analysis-card {
				display: block; /* 改为块级元素 */
				margin-bottom: 0; /* 移除底部边距，因为它是最后一个元素 */
			}
			
			#questionDetailContainer {
				overflow-y: auto; /* 添加垂直滚动条 */
				height: auto; /* 自适应高度 */
				max-height: none; /* 移除最大高度限制 */
				padding-right: 5px; /* 为滚动条留出空间 */
				min-height: 300px; /* 设置最小高度 */
			}
			
			/* 题型分析卡片特殊样式 */
			.question-types-card {
				display: block; /* 改为块级元素 */
				max-height: none; /* 移除最大高度限制 */
			}
			
			/* 教师评语样式 */
			.teacher-comment {
				color: #0c5460;
				background-color: rgba(209, 236, 241, 0.3);
				padding: 5px 8px;
				border-radius: 4px;
				border-left: 2px solid #17a2b8;
				max-height: 300px; /* 增加最大高度 */
				overflow-y: auto; /* 添加滚动条 */
			}
		</style>
		
		<style>
			/* 优化试卷成绩列表样式 - 更紧凑的布局 */
			.mycjitem {
				padding: 12px 16px; /* 减少内边距 */
				display: flex;
				align-items: center;
				justify-content: space-between;
				height: auto; /* 自适应高度 */
				min-height: 65px; /* 设置最小高度，比原来的5.208333rem(83.33px)小 */
				border-bottom: 1px dashed #e1e1e1;
				transition: background-color 0.2s ease; /* 添加过渡效果 */
			}
			
			.mycjitem:hover {
				background-color: #f8f9fa; /* 悬浮效果 */
			}
			
			.mycjitem:last-child {
				border: none;
			}
			
			/* 优化分数显示区域 */
			.mysjtime {
				width: 160px; /* 略微减小宽度 */
				height: 45px; /* 减少高度 */
				display: flex;
				border-radius: 4px;
				overflow: hidden;
				box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* 添加阴影效果 */
			}
			
			.sjtime1 {
				width: calc(100% / 3);
				background: #f39633;
				font-size: 18px; /* 略微减小字体 */
				color: #FFFFFF;
				display: flex;
				align-items: center;
				justify-content: center;
				font-weight: bold;
				background-image: url(../img/jiangbei.png);
				background-repeat: no-repeat;
				background-position: center;
				background-size: 20px; /* 减小背景图标尺寸 */
			}
			
			.sjtime2 {
				width: calc(100% / 3);
				background: #f3f3f3;
				display: flex;
				flex-direction: column;
				justify-content: center;
				align-items: center;
				padding: 4px 2px; /* 减少内边距 */
			}
			
			.sjtime2 div:first-child {
				font-size: 14px; /* 减小字体 */
				font-weight: bold;
				color: #333333;
				line-height: 1.2;
			}
			
			.sjtime2 div:last-child {
				font-size: 10px; /* 减小字体 */
				color: #999999;
				line-height: 1.2;
				margin-top: 2px;
			}
			
			/* 优化试卷名称 */
			.mysjname {
				font-size: 15px; /* 略微减小字体 */
				color: #333333;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
				width: 220px; /* 略微增加宽度 */
				font-weight: 500; /* 增加字重 */
			}
			
			/* 优化试卷信息项 */
			.mysjstr {
				display: flex;
				align-items: center;
				font-size: 13px; /* 减小字体 */
				color: #666666;
				margin: 2px 0; /* 减少上下间距 */
				line-height: 1.3;
				width: 180px; /* 限制宽度 */
			}
			
			.mysjstr img {
				width: 14px; /* 减小图标尺寸 */
				height: 14px;
				margin-right: 6px;
				display: block;
			}
			
			/* 优化按钮样式 */
			.sjjxbtn, .sjjxbtn2 {
				width: 80px; /* 减小按钮宽度 */
				height: 32px; /* 减小按钮高度 */
				display: flex;
				align-items: center;
				justify-content: center;
				background: #c00714;
				color: #FFFFFF;
				font-size: 12px; /* 减小字体 */
				border-radius: 4px;
				cursor: pointer;
				transition: all 0.2s ease;
				font-weight: 500;
			}
			
			.sjjxbtn:hover {
				background: #a00610;
				transform: translateY(-1px);
				box-shadow: 0 2px 6px rgba(192, 7, 20, 0.3);
			}
			
			.sjjxbtn2 {
				background: #999999;
				cursor: not-allowed;
			}
			
			/* 移动端优化 */
			@media (max-width: 768px) {
				.mycjitem {
					flex-direction: column;
					align-items: flex-start;
					min-height: auto;
					padding: 12px;
				}
				
				.mysjtime {
					width: 100%;
					margin-bottom: 8px;
				}
				
				.mysjname {
					width: 100%;
					margin-bottom: 4px;
				}
				
				.mysjstr {
					width: 100%;
				}
				
				.sjjxbtn, .sjjxbtn2 {
					width: 100%;
					margin-top: 8px;
				}
			}
		</style>
	</body>
</html>
