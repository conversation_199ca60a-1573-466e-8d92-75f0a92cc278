<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-虚仿实验空间</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/experiment.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<style>
			/* <div class="xuekebox">
				<span class="xklabelss">学科:</span>
				<div id="xklist">
					
				</div> */
				.xuekebox{
					align-items: flex-start;
				}
				.xuekebox .xklabelss{
					width: 2.604167rem;
					line-height: 2.083333rem;
				}
				.xuekebox #xklist{
					display: flex;
					flex-wrap: wrap;
				}
				.xuekeitem{
					line-height: 2.083333rem;
				}
				.xuekebox{
					height: auto !important;
					min-height: 2.083333rem;
				}
		</style>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="content">
			<div class="contexttopview">
				<div class="ctitle">虚仿实验空间</div>
				<div class="inputview">
					<input id="ssinput" type="text">
					<span onclick="getvrlist()">搜索</span>
				</div>
			</div>
			<div class="xuekebox">
				<span class="xklabelss">学科:</span>
				<div id="xklist">
					
				</div>
			</div>
			<div class="itemboxss" id="list">

			</div>
			<div class="fybox" id="fyq">
				<span id="sy">首页</span>
				<span id="syy">上一页</span>
				<div class="num" id="num">
				</div>
				<span id="xyy">下一页</span>
				<span id="wy">尾页</span>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let pageindex = 1
			let pagesize = 10
			let pages = 0
			let classdate = null
			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				let userinfo = sessionStorage.getItem("userinfo")
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()

				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				getclass() // 从base.js调用此函数，加载导航栏
				getExperimentClass() // 调用本地函数获取classdate
				getfooterlink()
				getvrlist()
				getxuekelist()
			})
			function jiluxuexi(item){
				// 检查classdate是否存在
				if(!classdate) {
					cocoMessage.error(1000, "数据加载错误，请刷新页面重试");
					return;
				}
				
				// 检查用户是否登录
				if (!sessionStorage.getItem("header")) {
					cocoMessage.error(1000, "请先登录");
					return;
				}
				
				const infoId = $(item).attr('data-id');
				if (!infoId) {
					cocoMessage.error(1000, "资源ID不存在");
					return;
				}
				
				let json = {
					infoId: infoId, //信息id
					categoryId: classdate.id, //所属分类id
					totalInfo: "1", //总时长，总页数
					positioning: '1', //学习了多久，多少页    
					progress: "100%", //进度 百分比
					learningTime: 60, // 设置一个默认的学习时长（秒）
					type: classdate.name || "虚仿实验"
				}
				
				console.log("发送学习记录:", json);
				
				$.ajax({
					url: baseurl + "/study/record/add",
					type: 'post',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: JSON.stringify(json),
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							cocoMessage.success(1000, "学习记录已保存");
						} else {
							cocoMessage.error(1000, "保存学习记录失败: " + (res.msg || "未知错误"));
						}
					},
					error: (xhr, status, error) => {
						console.error("学习记录保存失败:", xhr.responseText);
						cocoMessage.error(1000, "保存学习记录失败，请稍后重试");
					}
				})
			}
			function getxuekelist(){
				$.ajax({
					url: baseurl + "/web/project/section/list/tree",
					type: 'GET',
					contentType: "application/json",
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = '<label class="xuekeitem" onclick="selectxk(this)" data-id="0">全部</label>'
							res.data.map((item)=>{
								html += '<label class="xuekeitem"  onclick="selectxk(this)" data-id="'+item.id+'">'+item.name+'</label>'
							})
							$("#xklist").html(html)
						}
					}
				})
			}
			let selectxkid = null
			function selectxk(item){
				let xkid = $(item).attr("data-id")
				if(xkid == '0'){
					xkid = null
				}
				selectxkid = xkid
				pageindex = 1
				getvrlist()
				let alls = $(".xuekeitem")
				for(let i =0;i<alls.length;i++){
					$(alls[i]).attr("class","xuekeitem")
				}
				$(item).attr("class","xuekeitem accccc")
			}
			function getvrlist() {
				let name = $('#ssinput').val()
				if(!name){
					name = null
				}
				$.ajax({
					url: baseurl + "/web/virtual/list",
					type: 'GET',
					data: {
						pageNum: pageindex,
						pageSize: pagesize,
						subjectId: selectxkid,
						titleName: name
					},
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							pages = res.data.pages
							let html = ""
							res.data.list.forEach((item) => {
								html += `<a target="_blank" href="${item.linkAdress}" onclick="jiluxuexi(this)" data-id="${item.id}" class="items">
													<div class="imgboxs">
														<img src="${baseurl+''+item.covertPath}" />
														<div class="jianjie">
															<div>${item.introduction}</div>
														</div>
													</div>
													<div class="iibt">${item.titleName}</div>
													<div class="ibbb">
														<img src="img/schoolico.png" />
														<label>${item.schoolName}</label>
														<div class="bbbsbs"></div>
														<img src="img/fzrico.png" />
														<label>${item.principal}</label>
													</div>
												</a>`
							})
							$("#list").html(html)
							if (pages > 1) {
								let numhtml = ""
								for (let a = 1; a <= pages; a++) {
									if (pageindex == a) {
										numhtml += '<label class="actinum" onclick="getnewlist(' + a + ')">' + a +
											'</label>'
									} else {
										numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
									}
								}
								$("#sy").attr("onclick", "getnewlist(1)")
								$("#syy").attr("onclick", "getnewlist(1)")
								if (pageindex > 1) {
									$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
								}
								if (pageindex < pages) {
									$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
								} else {
									$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
								}
								$("#wy").attr("onclick", "getnewlist(" + pages + ")")
								$("#num").html(numhtml)
								$("#fyq").show()
							} else {
								$("#fyq").hide()
							}
						}
					}
				})
			}
			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是第最后一页了！")
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！")
					}
				} else {
					pageindex = index
					getvrlist()
				}
			}
			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			// 使用base.js中的getclass函数，但需要获取classdate
			function getExperimentClass() {
				$.ajax({
					url: baseurl + "/web/category/all",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// 只设置classdate，不修改导航栏
							for (let i = 0; i < res.data.length; i++) {
								if (res.data[i].id == getUrlParam('id') || res.data[i].redirectUrl == 'experiment.html') {
									classdate = res.data[i]
									break;
								}
							}
						}
					}
				})
			}

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
