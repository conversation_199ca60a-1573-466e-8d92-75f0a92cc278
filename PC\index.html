<!DOCTYPE html>
<html>
	<head>
	<!-- 已将menu-modifier.js的功能整合到swiper.min.js中 -->
		
		<meta charset="utf-8" />
		<title>思政一体化平台</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/index.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/yuanbao.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/check.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/login-mock.js" type="text/javascript" charset="utf-8"></script>
		<style>
			/* 加载动画样式 */
			.loading-overlay {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(255, 255, 255, 0.8);
				display: flex;
				justify-content: center;
				align-items: center;
				z-index: 9999;
				transition: opacity 0.3s ease;
			}
			
			.loading-spinner {
				width: 60px;
				height: 60px;
				border: 4px solid rgba(168, 93, 87, 0.2);
				border-radius: 50%;
				border-top-color: #A65D57;
				animation: spin 1s ease-in-out infinite;
			}
			
			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}
			
			.loading-hide {
				opacity: 0;
				pointer-events: none;
			}

			/* 心声社区列表优化样式 */
			.itemtopbox {
				background: transparent;
				border-radius: 0;
				padding: 4px 0;
				border: none;
				box-shadow: none;
				margin-bottom: 0;
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: flex-start;
				overflow-y: auto; /* 允许垂直滚动 */
				max-height: 280px; /* 为标题留出空间 */
			}

			.index .itemtopboxitem {
				position: relative !important;
				background: transparent !important;
				border: none !important;
				border-radius: 0 !important;
				padding: 14px 0 !important;
				margin-bottom: 0 !important;
				transition: all 0.3s ease !important;
				cursor: pointer;
				display: flex !important;
				align-items: center !important;
				justify-content: space-between !important;
				overflow: hidden;
				height: auto !important;
				border-bottom: 1px solid rgba(230, 230, 230, 0.6) !important;
			}

			.index .itemtopboxitem:last-child {
				margin-bottom: 0 !important;
				border-bottom: 1px solid rgba(230, 230, 230, 0.6) !important;
			}

			.index .itemtopboxitem:hover {
				background: rgba(248, 249, 250, 0.5) !important;
				border-color: rgba(230, 230, 230, 0.8) !important;
				transform: none !important;
				box-shadow: none !important;
				padding-left: 0 !important;
			}

			.index .itemtopboxitem::before {
				display: none;
			}

			/* 简洁的NEW图标 */
			.index .itemtopboxitem .new {
				background: none !important;
				color: #c00714 !important;
				padding: 0 !important;
				border-radius: 0 !important;
				font-size: 12px !important;
				font-weight: 700 !important;
				margin-right: 12px !important;
				text-transform: uppercase;
				letter-spacing: 1px;
				flex-shrink: 0;
				width: auto !important;
				box-shadow: none;
				transition: all 0.3s ease !important;
			}

			.index .itemtopboxitem:hover .new {
				transform: none;
				box-shadow: none;
				color: #a00610 !important;
			}

			.index .itemtopboxitem .new::before,
			.index .itemtopboxitem .new::after {
				display: none;
			}

			.index .itemtopboxitem .txt {
				flex: 1 !important;
				color: #2c3e50 !important;
				font-size: 16px !important;
				font-weight: 500 !important;
				line-height: 1.5 !important;
				margin: 0 16px !important;
				overflow: hidden !important;
				text-overflow: ellipsis !important;
				white-space: nowrap !important;
				transition: color 0.3s ease !important;
				width: auto !important;
			}

			.index .itemtopboxitem:hover .txt {
				color: #c00714 !important;
				font-weight: 500 !important;
				font-size: 16px !important;
				transform: none !important;
			}

			.index .itemtopboxitem .time {
				color: #95a5a6 !important;
				font-size: 14px !important;
				font-weight: 400 !important;
				background: none !important;
				padding: 0 !important;
				border-radius: 0 !important;
				border: none !important;
				flex-shrink: 0;
				transition: color 0.3s ease !important;
				width: auto !important;
				text-align: right !important;
				min-width: 80px;
			}

			.index .itemtopboxitem:hover .time {
				background: none !important;
				color: #7f8c8d !important;
				border-color: transparent !important;
				font-weight: 400 !important;
				transform: none;
			}

			/* 心声社区容器样式 */
			.community-container {
				background: transparent;
				border-radius: 0;
				box-shadow: none;
				border: none;
				overflow: hidden;
				transition: all 0.3s ease;
				height: 320px; /* 与flexview高度保持一致 */
				display: flex;
				flex-direction: column;
			}

			.community-container:hover {
				box-shadow: none;
				transform: none;
			}

			/* 心声社区和VR红色游学标题区域优化 */
			.index .itemtop, .hsyxbox .itemtop {
				padding: 8px 0 6px 0;
				background: transparent;
				border-radius: 0;
				border-bottom: none;
				margin-bottom: 0;
				display: flex;
				justify-content: space-between;
				align-items: center;
			}

			.index .itemtop .title.titleactive, .hsyxbox .itemtop .title.titleactive {
				color: #2c3e50 !important;
				font-weight: 600 !important;
				position: relative;
				font-size: 18px !important;
				padding-left: 0;
				display: flex;
				align-items: center;
			}

			.index .itemtop .more, .hsyxbox .itemtop .more {
				color: #6c757d;
				font-size: 14px;
				text-decoration: none;
				transition: color 0.3s ease;
				padding: 0;
				border-radius: 0;
			}

			.index .itemtop .more:hover, .hsyxbox .itemtop .more:hover {
				color: #c00714;
				background: none;
			}

			/* 加载状态优化 */
			.loading-item {
				background: rgba(248, 249, 250, 0.5) !important;
				border: none !important;
				border-radius: 0 !important;
				padding: 24px 0 !important;
				text-align: center;
				margin: 16px 0;
			}

			.loading-item .loading-spinner {
				width: 20px !important;
				height: 20px !important;
				border: 2px solid #e9ecef !important;
				border-top: 2px solid #A65D57 !important;
				border-radius: 50% !important;
				animation: spin 1s linear infinite !important;
				margin: 0 auto 12px auto !important;
			}

			.loading-item p {
				color: #7f8c8d !important;
				font-size: 14px !important;
				margin: 0 !important;
				font-weight: 400 !important;
			}

			/* 空状态样式优化 */
			.empty-state {
				background: none !important;
				border: none !important;
				border-radius: 0 !important;
				padding: 32px 0 !important;
				text-align: center;
				color: #95a5a6 !important;
				font-size: 15px !important;
				font-weight: 400 !important;
			}

			/* 响应式优化 */
			@media (max-width: 768px) {
				.index .itemtopboxitem {
					padding: 16px 0 !important;
					flex-direction: row !important;
					align-items: center !important;
				}

				.index .itemtopboxitem .new {
					margin-bottom: 0 !important;
					margin-right: 12px !important;
				}

				.index .itemtopboxitem .txt {
					margin: 0 12px !important;
					font-size: 15px !important;
				}

				.index .itemtopboxitem:hover .txt {
					font-size: 15px !important;
				}

				.index .itemtopboxitem .time {
					font-size: 13px !important;
					min-width: 70px;
				}
			}
		</style>
	</head>
	<body class="index">
		<!-- 添加加载动画 -->
		<div class="loading-overlay">
			<div class="loading-spinner"></div>
		</div>
		<div class="tcbox" id="tcbox">

			<div class="topviewsss">

				<a onclick="closetc()">关闭</a>
				<label id="pdfname"></label>
			</div>
			<div class="panel-body pdfbox">
				<a id="pdf" class="media" href=""></a>
			</div>
		</div>
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
					<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>
					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
					
				</div>
			</div>
		</div>
		<div id="indexswiper" class="swiper">
			
			<div id="indexswiperpagination" class="swiper-pagination">

			</div>
		</div>
		<div class="contextview" style="margin-top: -1rem !important;padding: 0px !important;">
			<div class="flexview">
				<div class="item">
					<div class="community-container">
						<div class="itemtop" id="newsbarlist">
							<a class="title titleactive">
								<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 22px; height: 22px; margin-right: 10px;">
									<!-- 对话气泡背景 -->
									<path d="M20 2H4C2.9 2 2 2.9 2 4V16C2 17.1 2.9 18 4 18H6L10 22L14 18H20C21.1 18 22 17.1 22 16V4C22 2.9 21.1 2 20 2Z" fill="#FF6B6B"/>
									<!-- 对话内容线条 -->
									<path d="M7 6H17V8H7V6Z" fill="#FFFFFF"/>
									<path d="M7 9H17V11H7V9Z" fill="#FFFFFF"/>
									<path d="M7 12H15V14H7V12Z" fill="#FFFFFF"/>
									<!-- 小爱心装饰 -->
									<circle cx="18" cy="6" r="2" fill="#FFE066"/>
									<path d="M18.5 5.2C18.3 4.9 18 4.7 17.7 4.7C17.4 4.7 17.1 4.9 16.9 5.2C16.7 5.5 16.8 5.9 17.1 6.1L18 6.8L18.9 6.1C19.2 5.9 19.3 5.5 18.5 5.2Z" fill="#FF4757"/>
								</svg>
								心声社区
							</a>
							<a href="community.html" class="more">更多>></a>
						</div>
						<div class="itemtopbox" id="new1list">


						</div>
					</div>
				</div>
				<div class="item flex">
					<div class="middle-cards" style="position: relative;left: 10px;">
						<div class="card virtual-lab">
							<div class="card-header">
								<div class="card-icon experiment-icon">
									<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
										<path d="M9 2V7H7.5C6.67 7 6 7.67 6 8.5V10H4.5C3.67 10 3 10.67 3 11.5V20.5C3 21.33 3.67 22 4.5 22H19.5C20.33 22 21 21.33 21 20.5V11.5C21 10.67 20.33 10 19.5 10H18V8.5C18 7.67 17.33 7 16.5 7H15V2H9ZM11 4H13V7H11V4ZM8 9H16V10H8V9ZM5 12H19V20H5V12ZM7 14V18H9V14H7ZM11 14V18H13V14H11ZM15 14V18H17V14H15Z" fill="currentColor"/>
									</svg>
								</div>
								<h3>虚仿实验空间</h3>
							</div>
							<div class="card-content">
								<p>沉浸式虚拟实验环境，体验现代化教学设备，提升实践操作能力</p>
								<a href="experiment.html" class="card-btn">
									<span>点击进入</span>
									<i class="btn-arrow">→</i>
								</a>
							</div>
						</div>
						
						<div class="card museum">
							<div class="card-header">
								<div class="card-icon museum-icon">
									<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
										<path d="M12 3L2 8V9H22V8L12 3ZM4 11V19H6V11H4ZM8 11V19H10V11H8ZM12 11V19H14V11H12ZM16 11V19H18V11H16ZM20 11V19H22V11H20ZM2 20V22H22V20H2Z" fill="currentColor"/>
									</svg>
								</div>
								<h3>医德博物馆</h3>
							</div>
							<div class="card-content">
								<p>传承医德文化精神，了解传统医学发展脉络和重要历史事件</p>
								<a href="museum.html" class="card-btn">
									<span>点击探索</span>
									<i class="btn-arrow">→</i>
								</a>
							</div>
						</div>
					</div>
					<div class="itemright">
						<a href="footprint.html" class="map-container">
							<img src="img/map.jpg" class="map-image" />
							<div class="itemrightb">
								<span class="star-icon">⭐</span>
								进入总书记的足迹>>
							</div>
						</a>
					</div>
				</div>
			</div>
		</div>
		<div class="contextview hsyxbox" style="margin-top: 1rem !important; padding: 0px !important;">
			<div class="item hsyx">
				<div class="itemtop">
					<label class="title titleactive">
						<svg viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg" style="width: 22px; height: 22px; margin-right: 10px;">
							<!-- 背景圆环 -->
							<circle cx="12" cy="12" r="11" fill="url(#redGradient)"/>
							<!-- VR设备主体 -->
							<path d="M7 9C7 8.4 7.4 8 8 8H16C16.6 8 17 8.4 17 9V15C17 15.6 16.6 16 16 16H8C7.4 16 7 15.6 7 15V9Z" fill="#FFFFFF"/>
							<!-- 镜片边框 -->
							<circle cx="10" cy="12" r="1.5" fill="#FF4757" stroke="#FFFFFF" stroke-width="0.5"/>
							<circle cx="14" cy="12" r="1.5" fill="#FF4757" stroke="#FFFFFF" stroke-width="0.5"/>
							<!-- 镜片光效 -->
							<circle cx="10.3" cy="11.7" r="0.4" fill="#FFFFFF" opacity="0.8"/>
							<circle cx="14.3" cy="11.7" r="0.4" fill="#FFFFFF" opacity="0.8"/>
							<!-- 头带 -->
							<path d="M7 11H5C4.4 11 4 11.4 4 12S4.4 13 5 13H7" fill="#FFE066"/>
							<path d="M17 11H19C19.6 11 20 11.4 20 12S19.6 13 19 13H17" fill="#FFE066"/>
							<!-- 红旗标志 -->
							<path d="M12 4L10 6H14L12 4Z" fill="#FF4757"/>
							<rect x="11.7" y="6" width="0.6" height="3" fill="#FF4757"/>
							<!-- 星星点缀 -->
							<polygon points="8,6 8.3,6.7 9,6.7 8.5,7.1 8.7,7.8 8,7.4 7.3,7.8 7.5,7.1 7,6.7 7.7,6.7" fill="#FFE066"/>
							<polygon points="16,6 16.3,6.7 17,6.7 16.5,7.1 16.7,7.8 16,7.4 15.3,7.8 15.5,7.1 15,6.7 15.7,6.7" fill="#FFE066"/>
							<defs>
								<linearGradient id="redGradient" x1="0%" y1="0%" x2="100%" y2="100%">
									<stop offset="0%" style="stop-color:#FF6B6B;stop-opacity:1"/>
									<stop offset="100%" style="stop-color:#FF4757;stop-opacity:1"/>
								</linearGradient>
							</defs>
						</svg>
						VR红色游学
					</label>
					<a href="vrtour.html" class="more">更多>></a>
				</div>
				<div class="hsyxitembox" id="hsyxbox">

				</div>
			</div>
		</div>
		<div class="contextview zxxx" style="margin-top: 2rem !important;padding: 0px !important;">
			<div class="zxxxleft">
				<img class="topimghssj" src="img/hssjtopimg.png" />
				<div class="titlehssj">
					<img class="hssjico" src="img/hssjico.png" />
					<div>在</div>
					<div>线</div>
					<div>学</div>
					<div>习</div>
				</div>
				<img class="bottomimghssj" src="img/hssjbottomimg.png" />
			</div>
			<div class="zxxxright">
				<div class="zxxxrighttop">
					<div class="hssjboxs" id="hssjbox">

					</div>
					<a href="onlinelearning2.html" class="hssjmore">
						<div class="moress">
							<img src="img/morej.png" />
							<div>更</div>
							<div>多</div>
							<div>红</div>
							<div>色</div>
							<div>书</div>
							<div>籍</div>
						</div>
					</a>
				</div>
				<div class="jxzyboxs">
					<div class="jxzyboxsleft">
						<div class="scrollsss" id="zxxxbox">

						</div>
					</div>
					<a href="onlinelearning2.html" class="jxzyboxsmore">
						<div>
							<img src="img/moreb.png" />
							<div>更</div>
							<div>多</div>
							<div>教</div>
							<div>学</div>
							<div>资</div>
							<div>源</div>
						</div>
					</a>
				</div>
			</div>
		</div>
		<!-- <div class="contextview ysbwgview">
			<div class="item ysbwg">
				<div class="itemtop">
					<label class="title titleactive">教学成果</label>
					<a href="onlinelearning3.html?type=00" class="more">更多>></a>
				</div>
				<div class="ysbwgbox">
					<div class="sjbox" id="cgbox">


					</div>
				</div>
			</div>
		</div> -->
		<!-- <div class="contextview xssq">
			<div class="item hsyx">
				<div class="itemtop">
					<label class="title titleactive">特色空间</label>
					<a href="#" class="more">更多>></a>
				</div>
				<div class="xssq-container">
					<div class="xssq-card virtual-lab">
						<div class="card-icon">
							<img src="img/xf.png" alt="虚仿实验空间" />
						</div>
						<div class="card-content">
							<h3>虚仿实验空间</h3>
							<p>沉浸式体验现代化教学环境，通过虚拟技术参与实验教学活动</p>
							<a href="experiment.html" class="card-btn">
								<span>点击进入</span>
								<i class="btn-arrow"></i>
							</a>
						</div>
					</div>
					
					<div class="xssq-card museum">
						<div class="card-icon">
							<img src="img/jng.png" alt="医史博物馆" />
						</div>
						<div class="card-content">
							<h3>医史博物馆</h3>
							<p>探索医学历史长河，了解传统医学发展脉络和重要历史事件</p>
							<a href="museum.html" class="card-btn">
								<span>点击探索</span>
								<i class="btn-arrow"></i>
							</a>
						</div>
					</div>
				</div>
			</div>
		</div> -->

		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">

					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<a href="https://yuanbao.tencent.com/" target="_blank" class="yuanbao-float">
			<img src="img/yuanbao512.png" alt="腾讯元宝" title="点击访问腾讯元宝" />
			腾讯元宝
		</a>
		<div id="iframe-container" class="iframe-container">
			<iframe src="" id="iframe"></iframe>
			<div id="closes">返回列表</div>
		</div>
		<script>
			// 添加统一的AJAX错误处理函数
			function handleAjaxError(jqXHR, textStatus, errorThrown, functionName) {
				console.error(`${functionName} 请求失败:`, textStatus, errorThrown);
				console.error(`状态码: ${jqXHR.status}`);
				console.error(`响应内容:`, jqXHR.responseText);
			}
			
			// 添加一个小延时确保login-mock.js有足够时间初始化
			setTimeout(function() {
				// 重新获取登录状态
				let userinfo = sessionStorage.getItem("userinfo")
				// console.log("当前登录状态:", userinfo ? "已登录" : "未登录")
				// console.log("当前baseurl:", baseurl);
				
				$(function() {
					// 确保baseurl变量存在
					if (typeof baseurl === 'undefined') {
						// 如果baseurl未定义，定义一个默认值
						window.baseurl = '/api';
						console.warn("baseurl变量未定义，使用默认值: /api");
					}
					
					$(".ssview").on('click', function() {
						window.location.href = 'searchall.html'
					})

					// 调用所有数据加载函数并添加错误处理
					try {
						getclass(); // 不传递参数，让系统自动匹配当前页面
					} catch(e) {
						console.error("getclass 执行错误:", e);
					}
					
					try {
						getxuexilist();
					} catch(e) {
						console.error("getxuexilist 执行错误:", e);
					}
					
					try {
						getchengguolist();
					} catch(e) {
						console.error("getchengguolist 执行错误:", e);
					}
					
					try {
						getnews();
					} catch(e) {
						console.error("getnews 执行错误:", e);
					}
					
					try {
						getfooterlink();
					} catch(e) {
						console.error("getfooterlink 执行错误:", e);
					}
					
					try {
						getbanner();
					} catch(e) {
						console.error("getbanner 执行错误:", e);
					}
					
					try {
						// 获取心声社区数据放在新闻简讯位置
						getxssqclassid();
					} catch(e) {
						console.error("getxssqclassid 执行错误:", e);
					}
					
					try {
						// 获取新闻列表数据放在心声社区位置
						getnewslist();
					} catch(e) {
						console.error("getnewslist 执行错误:", e);
					}
					
					if (userinfo) {
						//已登录 则显示用户信息
						$("#login").hide()
						$("#user").show()
						$("#user").html(JSON.parse(userinfo).name)
						$("#edit").show()
						if (window.localStorage.getItem("jilu")) {
							$.ajax({
								url: baseurl + "/study/record/add",
								type: 'post',
								contentType: "application/json",
								headers: {
									"Authorization": sessionStorage.getItem("header")
								},
								data: window.localStorage.getItem("jilu"),
								dataType: 'json',
								success: (res) => {
									window.localStorage.clear()
								}
							})
						}
					} else {
						//未登录 则显示登录按钮
						$("#login").show()
						$("#user").hide()
						$("#edit").hide()
					}

					// 绑定返回列表按钮点击事件
					$("#closes").on('click', function() {
						closeurl();
					});
				})
			})

			function closeurl() {
				$("#iframe-container, #iframe, #closes").fadeOut(300, function() {
					$("#iframe").attr("src", "");
					// 移除事件监听
					$("#iframe").off('load error');
				});
			}
			let newsclassid = null
			let newsggclassid = null
			let newsclassdata = null
			let xsrmindex = 1
			let xszxindex = 1
			let xsclassid = null
			let xsrmpages = 0
			let xszxpages = 0

			function clicknum(id) {
				$.ajax({
					url: baseurl + "/posts/click/" + id,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						// console.log("点击计数成功:", id);
					},
					error: function(jqXHR, textStatus, errorThrown) {
						handleAjaxError(jqXHR, textStatus, errorThrown, "clicknum");
					}
				})
			}

			function showurl(item) {
				clicknum($(item).attr("data-id"));
				// 从sessionStorage中获取userinfo
				const userinfo = sessionStorage.getItem("userinfo");
				if (userinfo) {
					let url = $(item).attr("data-url");
					$("#iframe").css('opacity', 0)
						.attr("src", url);
					$("#iframe-container, #iframe, #closes").fadeIn(300);
					$("#iframe").animate({opacity: 1}, 300);
					
					// 添加错误处理
					$("#iframe").on('load', function() {
						$(this).animate({opacity: 1}, 300);
					}).on('error', function() {
						closeurl();
						cocoMessage.error(2000, "页面加载失败，请稍后重试");
					});
				} else {
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html';
				}
			}

			function xsrmxyy() { //心声热门下一页
				if (xsrmindex < xsrmpages) {
					xsrmindex += 1
					getlist3()
				}
			}

			function xsrmsyy() { //心声热门上一页
				if (1 < xsrmindex) {
					xsrmindex -= 1
					getlist3()
				}
			}

			function xszxxyy() { //心声最新下一页
				if (xszxindex < xszxpages) {
					xszxindex += 1
					getlist2()
				}
			}

			function xszxsyy() { //心声最新上一页
				if (1 < xszxindex) {
					xszxindex -= 1
					getlist2()
				}
			}

			function getxssqclassid() {
				// console.log("开始获取心声社区分类ID");
				
				// 预先检查news元素是否存在
				if (!document.querySelector('#news')) {
					console.warn("警告: #news元素不存在，可能会影响轮播初始化");
				}
				
				$.ajax({
					url: baseurl + "/web/category/user",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						// console.log("获取心声社区分类ID成功:", res);
						if (res.code == '200') {
							xsclassid = res.data[0].id
							getlist2()
							getlist3()
						} else {
							console.warn("获取心声社区分类ID接口返回非200状态:", res);
						}
					},
					error: function(jqXHR, textStatus, errorThrown) {
						handleAjaxError(jqXHR, textStatus, errorThrown, "getxssqclassid");
					}
				})
			}

			function getlist2() { //最新话题
				// console.log("开始获取最新话题");
				// 修改排序字段为created_at，并使用desc降序排序，确保最新的显示在最前面
				let params = "?pageNum=" + xszxindex + "&pageSize=5&id=" + xsclassid + "&sort=created_at&order=desc&title="
				
				// 添加加载状态指示
				$("#new1list").html('<div class="loading-item"><div class="loading-spinner" style="width:30px;height:30px;margin:15px auto;"></div><p style="text-align:center;color:#666;font-size:14px;">正在加载最新话题...</p></div>');
				
				$.ajax({
					url: baseurl + "/web/xspostByCategoryId" + params,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						// console.log("获取最新话题成功:", res);
						if (res.code == '200') {
							if (res.data.list && res.data.list.length > 0) {
								let html = ""
								res.data.list.map((item, index) => {
									// 修改为新闻简讯样式显示在上方区域，只给前三个加NEW图标
									html +=
										'<div class="itemtopboxitem" onclick="inxsinfo(this)" data-id="' +
										item.id + '">' + 
										// 只给前三个项目添加NEW标签
										(index < 3 ? '<i class="new">NEW</i>' : '') +
										'<div class="txt">' + item.title +
										'</div><div class="time">' + setDate2(item.createdAt) + '</div></div>'
								})
								$("#new1list").html(html)
								xszxpages = res.data.pages
							} else {
								// 没有数据时显示提示
								$("#new1list").html('<div style="padding:15px;text-align:center;color:#666;">暂无最新话题</div>');
							}
						} else {
							console.warn("获取最新话题接口返回非200状态:", res);
							$("#new1list").html('<div style="padding:15px;text-align:center;color:#666;">获取数据失败，请稍后再试</div>');
						}
					},
					error: function(jqXHR, textStatus, errorThrown) {
						handleAjaxError(jqXHR, textStatus, errorThrown, "getlist2");
						$("#new1list").html('<div style="padding:15px;text-align:center;color:#666;">获取数据失败，请稍后再试</div>');
					}
				})
			}

			function getlist3() { //最热话题
				// console.log("开始获取最热话题");
				let params = "?pageNum=" + xsrmindex + "&pageSize=5&id=" + xsclassid + "&sort=click_count&title="
				$.ajax({
					url: baseurl + "/web/xspostByCategoryId" + params,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						// console.log("获取最热话题成功:", res);
						if (res.code == '200') {
							let html = ""
							res.data.list.map((item) => {
								// 修改为公告轮播样式显示
								if (html === "") {
									html += '<div class="swiper-slide">'
								}
								
								html += '<div class="xwitem" onclick="inxsinfo(this)" data-id="' + item.id + 
									'"><div class="date"><label>' + getyear(item.createdAt) + 
									'</label><label>' + getmon(item.createdAt) + '</label></div>' +
									'<div class="datatxt">' + item.title + '</div></div>'
									
								// 每4个为一组
								if ((res.data.list.indexOf(item) + 1) % 4 === 0 && res.data.list.indexOf(item) < res.data.list.length - 1) {
									html += '</div><div class="swiper-slide">'
								}
							})
							
							// 关闭最后一个slide
							if (html !== "" && !html.endsWith('</div>')) {
								html += '</div>'
							}
							
							$("#newsgglist").html(html)
							xsrmpages = res.data.pages
							
							// 更新轮播插件
							try {
								// 检查内容是否为空
								if (html === "") {
									console.warn("获取的热门话题内容为空，无法初始化轮播");
									return;
								}
								
								// 确保DOM元素存在
								if (!document.querySelector('#news')) {
									console.warn("找不到#news元素，无法初始化轮播");
									return;
								}
								
								// 确保初始化之前有内容
								// console.log("初始化轮播前确认内容:", $("#newsgglist").html());
								
								// 延迟一小段时间确保DOM已更新
								setTimeout(function() {
									var news = new Swiper('#news', {
										autoplay: true,
										loop: true,
										pagination: {
											el: '#newspagination',
											clickable: true
										}
									});
									
									// 添加安全检查，确保news对象和news.el存在
									if (news && news.el) {
										news.el.onmouseover = function() {
											news.autoplay.stop();
										}
										
										news.el.onmouseout = function() {
											news.autoplay.start();
										}
										// console.log("轮播插件初始化成功");
									} else {
										console.warn("轮播插件初始化不完整，无法设置鼠标悬停事件");
									}
								}, 100); // 延迟100毫秒
							} catch(error) {
								console.error("初始化轮播插件失败:", error);
							}
						} else {
							console.warn("获取最热话题接口返回非200状态:", res);
						}
					},
					error: function(jqXHR, textStatus, errorThrown) {
						handleAjaxError(jqXHR, textStatus, errorThrown, "getlist3");
					}
				})
			}

			function inxsinfo(item) {
				clicknum($(item).attr("data-id"));
				window.location.href = "communitydetail.html?id=" + $(item).attr("data-id");
			}

			function setDate2(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d
			}

			function getnewgglist() {
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: {
						pageNum: 1,
						pageSize: 16,
						categoryId: newsggclassid
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data.list)
							let html = ""
							res.data.list.map((item) => {
								// 修改为心声社区样式显示在下方左侧区域
								html += '<div onclick="innewscontent(this)" data-id="' + item.id +
									'" class="riitem"><div class="huo"><img src="img/huo.png" />' + 
									item.clickCount + '</div><div class="txt">' + item.title +
									'</div><div class="date">' + setDate2(item.eventTime) + '</div></div>'
							})
							$("#rmht").html(html)
						}
					}
				})
			}

			function getnewslist() {
				// console.log("开始获取新闻列表分类");
				$.ajax({
					url: baseurl + "/web/category/news",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header") || ''
					},
					dataType: 'json',
					success: (res) => {
						// console.log("获取新闻列表分类成功:", res);
						if (res.code == '200') {
							newsclassdata = res.data[0].children
							newsclassid = res.data[0].children[0].id
							newsggclassid = res.data[0].children[2].id
							
							// 获取新闻列表内容显示在心声社区位置
							getnewslistinfo()
							getnewgglist()
						} else {
							console.warn("获取新闻列表分类接口返回非200状态:", res);
						}
					},
					error: function(jqXHR, textStatus, errorThrown) {
						handleAjaxError(jqXHR, textStatus, errorThrown, "getnewslist");
					}
				})
			}

			function getnewslistinfo() {
				// console.log("开始获取新闻列表内容");
				$.ajax({
					url: baseurl + "/web/posts",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header") || ''
					},
					data: {
						pageNum: 1,
						pageSize: 5,
						categoryId: newsclassid
					},
					dataType: 'json',
					success: (res) => {
						// console.log("获取新闻列表内容成功:", res);
						if (res.code == '200') {
							// console.log(res.data.list)
							let html = ""
							res.data.list.map((item) => {
								// 修改为心声社区样式显示在下方右侧区域
								html += '<div onclick="innewscontent(this)" data-id="' + item.id +
									'" class="riitem"><div class="txt">' + item.title +
									'</div><div class="date">' + setDate2(item.eventTime) + '</div></div>'
							})
							$("#zxht").html(html)
						} else {
							console.warn("获取新闻列表内容接口返回非200状态:", res);
						}
					},
					error: function(jqXHR, textStatus, errorThrown) {
						handleAjaxError(jqXHR, textStatus, errorThrown, "getnewslistinfo");
					}
				})
			}

			function innewscontent(item) {
				clicknum($(item).attr("data-id"));
				window.location.href = "newscontent.html?id=" + $(item).attr("data-id") + "&classid=" + newsggclassid;
			}

			function getbanner() {
				$.ajax({
					url: baseurl + "/sys/banners/Newitems/list",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ''
							res.data.list.map((item) => {
								html += '<div class="swiper-slide"><img src="' + baseurl + item.url +
									'" /></div>'
							})
							$("#banner").html(html)
							
							try {
								// 确保DOM元素存在并已更新
								if (!document.querySelector('#indexswiper')) {
									console.warn("找不到#indexswiper元素，无法初始化轮播");
									return;
								}
								
								// 延迟一小段时间确保DOM已更新
								setTimeout(function() {
									var mySwiper = new Swiper('#indexswiper', {
										autoplay: true,
										loop: true,
										pagination: {
											el: '#indexswiperpagination',
											clickable: true
										}
									});
									
									// 添加安全检查，确保mySwiper对象和mySwiper.el存在
									if (mySwiper && mySwiper.el) {
										mySwiper.el.onmouseover = function() {
											mySwiper.autoplay.stop();
										}
										mySwiper.el.onmouseout = function() {
											mySwiper.autoplay.start();
										}
										// console.log("Banner轮播插件初始化成功");
									} else {
										console.warn("轮播插件初始化不完整，无法设置鼠标悬停事件");
									}
								}, 100); // 延迟100毫秒
							} catch(error) {
								console.error("初始化轮播插件失败:", error);
							}
						}
					}
				})
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			function getnews() { //获取新闻
				$.ajax({
					url: baseurl + "/web/home",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data.homeContentList)
							let hsyxhtml = ""
							res.data.homeContentList.map((item) => {
								if (item.name == "VR红色游学") {
									item.pageList.list.map((item2) => {
										hsyxhtml += '<div class="item" onclick="showurl(this)" data-id="' + item2.id + 
											'" data-url="' + item2.redirectUrl + '">' +
											'<img src="' + baseurl + item2.thumbPath[0] + '" />' +
											'<div class="itemname">' + item2.title + '</div>' +
											'<div class="itemcode">' + item2.keyWords + '</div></div>'
									})
								}
							})
							$("#hsyxbox").html(hsyxhtml)
						}
					}
				})
			}

			function inconss(url) { //进入红色游学详情
				let urlstr = $(url).attr("data-url")
				checkLogin(urlstr)
			}

			function getchengguolist() { //教学成果
				$.ajax({
					url: baseurl + "/web/teachingResults",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let shtml = ''
							res.data.list.map((item) => {
								shtml += '<a onclick="showsss(this)" data-type="' + item.attachType +
									'" data-id="' + item.metaId + '" class="zyitem">' +
									'<div class="zyitemimgbox">' +
									'<label class="itemtype ' + item.attachType + '">' + item.attachType +
									'</label>'
								if (item.coverPath) {
									shtml += '<img class="fms" src="' + baseurl + item.coverPath[0] + '" />'
								} else {
									shtml += '<img class="fms" src="' + baseurl + '" />'
								}

								shtml += '<div class="zyicobox">' +
									'<label><img src="img/kjzz.png"/>' + item.author + '</label>' +
									'<label><img src="img/kjyj.png"/>' + item.view + '</label>' +
									'</div>' +
									'</div>' +
									'<div class="zytitle">' + item.title + '</div>' +
									'<div class="zyms">' + item.introduction + '</div></a>'
							})

							// 如果列表项少于10个，复制一份用于无缝滚动
							if (res.data.list.length < 10) {
								shtml = '<div class="scroll-wrapper scrolling">' + shtml + shtml + '</div>';
							} else {
								shtml = '<div class="scroll-wrapper">' + shtml + '</div>';
							}

							// 将内容包装在滚动容器中
							shtml = '<div class="scroll-container">' + shtml + '</div>';
							
							$("#cgbox").html(shtml);

							// 如果列表项少于10个，确保滚动动画平滑
							if (res.data.list.length < 10) {
								const wrapper = document.querySelector('.scroll-wrapper');
								// 添加检查确保wrapper元素存在
								if (wrapper) {
								wrapper.addEventListener('mouseenter', () => {
									wrapper.style.animationPlayState = 'paused';
								});
								wrapper.addEventListener('mouseleave', () => {
									wrapper.style.animationPlayState = 'running';
								});
								}
							}
						}
					}
				})
			}

			function showsss(cg) {
				if ($(cg).attr("data-type") == "pdf") {
					$.ajax({
						url: baseurl + "/course/meta/" + $(cg).attr("data-id"),
						type: 'GET',
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						dataType: 'json',
						success: (res) => {
							if (res.code == '200') {
								$("#pdfname").html(res.data.cmsResourcesCourseMetaList[0].attachName)
								$("#tcbox").show()
								$("#pdf").attr("href", baseurl + res.data.cmsResourcesCourseMetaList[0].attachPath)
								$('a.media').media()
							}
						}
					})
				} else {
					window.location.href = "onlinelearning4.html?id=" + $(cg).attr("data-id")
				}
			}

			function closetc() {
				$("#tcbox").hide()
			}

			function sjininfo(item) {
				window.location.href = "onlinelearning5.html?id=" + $(item).attr("data-id")
			}

			function getxuexilist() { //在线学习列表
				// 获取教学资源（除红色书籍外的内容）
				$.ajax({
					url: baseurl + "/web/learn",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let xxhtml = ""
							res.data.homeContentList[0].children.map((item) => {
								if (item.name != "红色书籍") {
									item.pageCourceList.list.map((item2) => {
										xxhtml += '<div onclick="showsss(this)" data-type="' + item2
											.attachType + '" data-id="' + item2.metaId +
											'" class="jxzyitem">' +
											'<label class="llllllll ' + item2.attachType + '">' +
											item2.attachType + '</label><div class="nrnrnr3">' +
											'<div>' + item2.title + '</div>' +
											'<div>' + item2.introduction + '</div>' +
											'</div><div class="ririri">' +
											'<label><img src="img/zz.png" />' + item2.author +
											'</label>' +
											'<label><img src="img/kjpf.png" />' + parseFloat(item2
												.score).toFixed(1) + '</label></div></div>'
									})
								}
							})
							$("#zxxxbox").html(xxhtml)
						}
					}
				})
				
				// 使用新的接口获取红色书籍
				$.ajax({
					url: baseurl + "/web/posts?categoryId=912354240784109568&pageSize=50&redBookId=1369261422076366848&pageNum=1&_t=" + new Date().getTime() + "&sort=publishedTime%2Cdesc",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200' && res.data && res.data.list) {
							let hshtml = '<div class="hssjbox-scroll">'
							
							// 获取所有书籍
							res.data.list.forEach((item) => {
								hshtml += '<div onclick="sjininfo(this)" data-id="' +
									item.id + '" class="hssjitem">' +
									'<img src="' + (item.thumbPath && item.thumbPath[0] ? baseurl + item.thumbPath[0] : 'img/book_default.jpg') + '" />' +
									'<div class="hssjtitle">' + item.title + '</div>' +
									'<div class="zz">作者：<label>' + (item.author || '未知') +
									'</label></div></div>'
							})
							
							// 为了实现无缝滚动，复制一份内容
							res.data.list.forEach((item) => {
								hshtml += '<div onclick="sjininfo(this)" data-id="' +
									item.id + '" class="hssjitem">' +
									'<img src="' + (item.thumbPath && item.thumbPath[0] ? baseurl + item.thumbPath[0] : 'img/book_default.jpg') + '" />' +
									'<div class="hssjtitle">' + item.title + '</div>' +
									'<div class="zz">作者：<label>' + (item.author || '未知') +
									'</label></div></div>'
							})
							
							hshtml += '</div>'
							$("#hssjbox").html(hshtml)
						} else {
							// 接口失败时显示提示，并使用默认内容
							console.error("获取红色书籍数据失败:", res)
							$("#hssjbox").html('<div class="empty-state">获取红色书籍数据失败，请稍后再试</div>')
						}
					},
					error: function(jqXHR, textStatus, errorThrown) {
						console.error("红色书籍接口请求失败:", textStatus, errorThrown)
						$("#hssjbox").html('<div class="empty-state">获取红色书籍数据失败，请稍后再试</div>')
					}
				})
			}
			
			
			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d
			}

			function getyear(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y
			}

			function getmon(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return MM + '-' + d
			}

			function edit() {
				// 清除sessionStorage
				sessionStorage.clear()
				
				// 清除localStorage
				localStorage.clear()
				
				// 清除所有相关cookies
				const cookies = document.cookie.split(";");
				for (let i = 0; i < cookies.length; i++) {
					const cookie = cookies[i];
					const eqPos = cookie.indexOf("=");
					const name = eqPos > -1 ? cookie.substr(0, eqPos).trim() : cookie.trim();
					document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
				}
				
				// 更新UI状态
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
				
				// 短暂延时后跳转到单点登录页面
				setTimeout(function() {
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/';
				}, 1200); // 延时1.2秒，确保消息显示完毕
			}

		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
			
			// 隐藏加载动画
			$(document).ready(function() {
				// 在所有资源加载完成后隐藏加载动画
				setTimeout(function() {
					$(".loading-overlay").addClass("loading-hide");
					// 2秒后完全移除加载动画元素
					setTimeout(function() {
						$(".loading-overlay").remove();
					}, 2000);
				}, 1000);
			});
		</script>
		<style>
			.hsyxitembox .item {
				transition: all 0.3s ease;
				cursor: pointer;
			}

			.hsyxitembox .item:hover {
				transform: translateY(-5px);
				box-shadow: 0 5px 15px rgba(0,0,0,0.1);
			}

			.hsyxitembox .item img {
				transition: all 0.3s ease;
			}

			.hsyxitembox .item:hover img {
				transform: scale(1.05);
			}

			.hsyxitembox .item .itemname {
				transition: all 0.3s ease;
			}

			.hsyxitembox .item:hover .itemname {
				color: #A65D57;
			}

			/* 教学成果列表悬浮效果 */
			.zyitem {
				transition: all 0.3s ease;
				cursor: pointer;
			}

			.zyitem:hover {
				transform: translateY(-5px);
				box-shadow: 0 5px 15px rgba(0,0,0,0.1);
			}

			.zyitem .fms {
				transition: all 0.3s ease;
			}

			.zyitem:hover .fms {
				transform: scale(1.05);
			}

			.zyitem .zytitle {
				transition: all 0.3s ease;
			}

			.zyitem:hover .zytitle {
				color: #A65D57;
			}

			/* iframe弹窗样式 */
			#iframe {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				z-index: 9998;
				border: none;
				display: none;
				background: #fff;
			}

			.iframe-container {
				position: fixed;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				z-index: 9997;
				background: rgba(0, 0, 0, 0.5);
				display: none;
			}

			/* 返回按钮样式 */
			#closes {
				position: fixed;
				top: 20px;
				right: 20px;
				z-index: 9999;
				background: #A65D57;
				color: white;
				padding: 10px 20px;
				border-radius: 5px;
				cursor: pointer;
				display: none;
				transition: all 0.3s ease;
				font-size: 14px;
				display: flex;
				align-items: center;
				gap: 5px;
			}

			#closes:before {
				content: "←";
				font-size: 16px;
			}

			#closes:hover {
				background: #8B4513;
				transform: translateY(-2px);
				box-shadow: 0 2px 8px rgba(0,0,0,0.2);
			}

			/* 添加滚动动画容器样式 */
			.scroll-container {
				width: 100%;
				overflow: hidden;
				position: relative;
			}

			.scroll-wrapper {
				display: flex;
				animation: none; /* 默认不开启动画 */
			}

			/* 当列表项少于10个时的滚动动画 */
			.scroll-wrapper.scrolling {
				animation: scrollLeft 20s linear infinite;
			}

			@keyframes scrollLeft {
				0% {
					transform: translateX(0);
				}
				100% {
					transform: translateX(-100%);
				}
			}

			/* 保持原有样式 */
			.zyitem {
				flex-shrink: 0;
				margin-right: 1.25rem;
				width: calc((100% - 3.75rem) / 4);
			}

			/* 确保动画流畅 */
			.scroll-wrapper.scrolling .zyitem:last-child {
				margin-right: 0;
			}

			/* 加载指示器样式 */
			.loading-item {
				padding: 20px;
				background-color: #f9f9f9;
				border-radius: 5px;
				margin: 10px 0;
			}
			
			.loading-spinner {
				width: 30px;
				height: 30px;
				border: 3px solid #f3f3f3;
				border-top: 3px solid #A65D57;
				border-radius: 50%;
				animation: spin 1s linear infinite;
				margin: 0 auto 10px auto;
			}
			
			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}
			
			/* 心声社区项目样式微调 */
			.itemtopboxitem {
				transition: all 0.3s ease;
				cursor: pointer;
			}
			
			.itemtopboxitem:hover {
				background-color: #f9f9f9;
				transform: translateY(-2px);
				box-shadow: 0 2px 5px rgba(0,0,0,0.1);
			}
			
			.itemtopboxitem .new {
				background-color: #A65D57;
				color: white;
				padding: 2px 5px;
				border-radius: 3px;
				font-size: 12px;
				margin-right: 5px;
			}
			
			.itemtopboxitem .time {
				color: #999;
				font-size: 12px;
			}

			/* 特色空间版块的现代化样式 */
			.modern-section {
				padding: 50px 0;
				background: linear-gradient(135deg, #f9f9f9 0%, #f2f2f2 100%);
				position: relative;
				overflow: hidden;
			}
			
			.modern-section::before {
				content: "";
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-image: linear-gradient(45deg, rgba(166, 93, 87, 0.05) 0%, rgba(166, 93, 87, 0.01) 100%);
				background-size: 200px;
				opacity: 0.1;
				pointer-events: none;
			}
			
			.section-title {
				text-align: center;
				margin-bottom: 40px;
				position: relative;
			}
			
			.section-title h2 {
				color: #333;
				font-size: 32px;
				margin: 0;
				padding: 0;
				position: relative;
				display: inline-block;
				font-weight: 600;
			}
			
			.section-title h2::after {
				content: "";
				position: absolute;
				bottom: -10px;
				left: 50%;
				transform: translateX(-50%);
				width: 80px;
				height: 3px;
				background: linear-gradient(to right, #A65D57, #c9817b);
				border-radius: 3px;
			}
			
			.title-decoration {
				width: 40px;
				height: 40px;
				background-color: rgba(166, 93, 87, 0.1);
				position: absolute;
				border-radius: 50%;
				top: -10px;
				left: 50%;
				transform: translateX(-50%);
				z-index: -1;
			}
			
			.xssq-container {
				display: flex;
				flex-wrap: wrap;
				justify-content: center;
				gap: 30px;
				max-width: 1200px;
				margin: 0 auto;
				padding: 0 20px;
			}
			
			.xssq-card {
				flex: 1;
				min-width: 300px;
				max-width: 500px;
				background: white;
				border-radius: 10px;
				box-shadow: 0 10px 25px rgba(0, 0, 0, 0.05);
				overflow: hidden;
				transition: all 0.3s ease;
				position: relative;
				display: flex;
				align-items: center;
				padding: 30px;
				border-top: 4px solid #A65D57;
				transform: scale(1);
			}
			
			.xssq-card:hover {
				transform: translateY(-10px);
				box-shadow: 0 15px 35px rgba(166, 93, 87, 0.2);
				transition: all 0.3s ease;
			}
			
			.card-icon {
				background: rgba(166, 93, 87, 0.1);
				width: 80px;
				height: 80px;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 20px;
				flex-shrink: 0;
				transition: all 0.3s ease;
				position: relative;
				z-index: 1;
			}
			
			.xssq-card:hover .card-icon {
				transform: scale(1.1) rotate(5deg);
				background: rgba(166, 93, 87, 0.2);
			}
			
			.card-icon img {
				width: 40px;
				height: 40px;
				object-fit: contain;
				transition: all 0.3s ease;
			}
			
			.xssq-card:hover .card-icon img {
				transform: scale(1.1);
			}
			
			.card-content {
				flex-grow: 1;
			}
			
			.card-content h3 {
				margin: 0 0 10px;
				color: #333;
				font-size: 22px;
				position: relative;
				padding-bottom: 10px;
			}
			
			.card-content h3::after {
				content: "";
				position: absolute;
				bottom: 0;
				left: 0;
				width: 40px;
				height: 2px;
				background-color: #A65D57;
				transition: width 0.3s ease;
			}
			
			.xssq-card:hover h3::after {
				width: 80px;
			}
			
			.card-content p {
				color: #666;
				margin: 0 0 20px;
				font-size: 14px;
				line-height: 1.5;
			}
			
			.card-btn {
				display: inline-flex;
				align-items: center;
				color: #A65D57;
				text-decoration: none;
				font-weight: 500;
				font-size: 16px;
				position: relative;
				transition: all 0.3s ease;
			}
			
			.card-btn:hover {
				color: #8B4513;
			}
			
			.btn-arrow {
				display: inline-block;
				margin-left: 8px;
				position: relative;
				width: 20px;
				height: 10px;
				transition: all 0.3s ease;
			}
			
			.btn-arrow::before {
				content: "";
				position: absolute;
				width: 12px;
				height: 2px;
				background-color: currentColor;
				top: 4px;
				right: 0;
			}
			
			.btn-arrow::after {
				content: "";
				position: absolute;
				width: 6px;
				height: 6px;
				border-top: 2px solid currentColor;
				border-right: 2px solid currentColor;
				top: 2px;
				right: 0;
				transform: rotate(45deg);
			}
			
			.xssq-card:hover .btn-arrow {
				transform: translateX(5px);
			}
			
			/* 响应式适配 */
			@media (max-width: 768px) {
				.xssq-container {
					flex-direction: column;
					align-items: center;
				}
				
				.xssq-card {
					width: 100%;
					max-width: 100%;
				}
			}
			
			/* 动画效果 - 重新实现 */
			@keyframes fadeInUp {
				from {
					opacity: 0;
					transform: translateY(30px);
				}
				to {
					opacity: 1;
					transform: translateY(0);
				}
			}
			
			@keyframes pulse {
				0% {
					box-shadow: 0 0 0 0 rgba(166, 93, 87, 0.4);
				}
				70% {
					box-shadow: 0 0 0 10px rgba(166, 93, 87, 0);
				}
				100% {
					box-shadow: 0 0 0 0 rgba(166, 93, 87, 0);
				}
			}
			
			@keyframes float {
				0% {
					transform: translateY(0px);
				}
				50% {
					transform: translateY(-10px);
				}
				100% {
					transform: translateY(0px);
				}
			}
			
			@keyframes rotate {
				from {
					transform: rotate(0deg);
				}
				to {
					transform: rotate(360deg);
				}
			}
			
			.animate-title {
				animation: fadeInUp 0.8s ease forwards;
			}
			
			.animate-card {
				animation: fadeInUp 0.8s ease forwards, float 6s ease-in-out infinite;
			}
			
			.delay-1 {
				animation-delay: 0.2s;
			}
			
			.pulse-animation {
				animation: pulse 2s infinite;
			}
			
			.pulse-animation::after {
				content: "";
				position: absolute;
				width: 100%;
				height: 100%;
				border-radius: 50%;
				background: rgba(166, 93, 87, 0.2);
				z-index: -1;
				animation: pulse 2s infinite;
			}
			
			/* 特殊效果 */
			.virtual-lab::before {
				content: "VR";
				position: absolute;
				top: 10px;
				right: 10px;
				font-size: 40px;
				color: rgba(166, 93, 87, 0.1);
				font-weight: bold;
				pointer-events: none;
				transition: all 0.5s ease;
			}
			
			.virtual-lab:hover::before {
				transform: rotate(10deg) scale(1.2);
				color: rgba(166, 93, 87, 0.2);
			}
			
			.museum::before {
				content: "医德";
				position: absolute;
				top: 10px;
				right: 10px;
				font-size: 30px;
				color: rgba(166, 93, 87, 0.1);
				font-weight: bold;
				pointer-events: none;
				transition: all 0.5s ease;
			}
			
			.museum:hover::before {
				transform: rotate(-5deg) scale(1.2);
				color: rgba(166, 93, 87, 0.2);
			}

			/* 特色空间版块样式 */
			.contextview.xssq {
				background: #fff;
				padding: 20px 0;
			}
			
			.xssq .itemtop {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 0 20px;
				margin-bottom: 20px;
			}
			
			.xssq .title {
				font-size: 1.25rem;
				color: #333;
				font-weight: bold;
				position: relative;
				padding-left: 15px;
			}
			
			.xssq .title::before {
				content: '';
				position: absolute;
				left: 0;
				top: 50%;
				transform: translateY(-50%);
				width: 5px;
				height: 20px;
				background-color: #A65D57;
				border-radius: 2px;
			}
			
			.xssq-container {
				display: flex;
				flex-wrap: wrap;
				justify-content: space-between;
				gap: 20px;
				padding: 0 20px;
			}
			
			.xssq-card {
				flex: 1;
				min-width: 300px;
				background: #fff;
				border-radius: 10px;
				box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
				overflow: hidden;
				transition: all 0.3s ease;
				position: relative;
				display: flex;
				align-items: center;
				padding: 20px;
				border: 1px solid #eee;
			}
			
			.xssq-card:hover {
				transform: translateY(-5px);
				box-shadow: 0 10px 25px rgba(166, 93, 87, 0.2);
			}
			
			.card-icon {
				background: rgba(166, 93, 87, 0.1);
				width: 60px;
				height: 60px;
				border-radius: 50%;
				display: flex;
				align-items: center;
				justify-content: center;
				margin-right: 15px;
				flex-shrink: 0;
				transition: all 0.3s ease;
				position: relative;
			}
			
			.xssq-card:hover .card-icon {
				transform: scale(1.1);
				background: rgba(166, 93, 87, 0.2);
			}
			
			.card-icon img {
				width: 30px;
				height: 30px;
				object-fit: contain;
				transition: all 0.3s ease;
			}
			
			.card-content {
				flex-grow: 1;
			}
			
			.card-content h3 {
				margin: 0 0 10px;
				color: #333;
				font-size: 18px;
				position: relative;
				padding-bottom: 8px;
			}
			
			.card-content h3::after {
				content: "";
				position: absolute;
				bottom: 0;
				left: 0;
				width: 30px;
				height: 2px;
				background-color: #A65D57;
				transition: width 0.3s ease;
			}
			
			.xssq-card:hover h3::after {
				width: 60px;
			}
			
			.card-content p {
				color: #666;
				margin: 0 0 15px;
				font-size: 14px;
				line-height: 1.5;
			}
			
			.card-btn {
				display: inline-flex;
				align-items: center;
				color: #A65D57;
				text-decoration: none;
				font-weight: 500;
				font-size: 14px;
				position: relative;
				transition: all 0.3s ease;
				background-color: rgba(166, 93, 87, 0.1);
				padding: 5px 12px;
				border-radius: 20px;
			}
			
			.card-btn:hover {
				color: white;
				background-color: #A65D57;
			}
			
			.xssq-card .btn-arrow {
				display: inline-block;
				margin-left: 5px;
				transition: all 0.3s ease;
			}
			
			.xssq-card:hover .btn-arrow {
				transform: translateX(3px);
			}

			/* 为按钮添加箭头图标 */
			.card-btn .btn-arrow {
				position: relative;
				width: 16px;
				height: 16px;
				margin-left: 5px;
			}
			
			.card-btn .btn-arrow::before {
				content: '';
				position: absolute;
				top: 50%;
				right: 0;
				width: 8px;
				height: 8px;
				border-top: 2px solid #A65D57;
				border-right: 2px solid #A65D57;
				transform: translateY(-50%) rotate(45deg);
				transition: all 0.3s ease;
			}
			
			.card-btn:hover .btn-arrow::before {
				border-color: white;
				right: -2px;
			}
			
			/* 特色空间版块响应式优化 */
			@media (max-width: 768px) {
				.xssq-container {
					flex-direction: column;
				}
				
				.xssq-card {
					width: 100%;
					margin-bottom: 15px;
				}
				
				.card-icon {
					width: 50px;
					height: 50px;
				}
				
				.card-icon img {
					width: 25px;
					height: 25px;
				}
				
				.card-content h3 {
					font-size: 16px;
				}
				
				.card-content p {
					font-size: 13px;
				}
			}
			
			/* 设置按钮悬停效果与主题色一致 */
			.card-btn:hover {
				background-color: #A65D57;
				color: white;
				box-shadow: 0 2px 8px rgba(166, 93, 87, 0.3);
			}

			/* 地图容器样式美化 */
			.map-container {
				position: relative;
				display: block;
				border-radius: 12px;
				overflow: hidden;
				transition: all 0.3s ease;
				box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
				text-decoration: none;
				margin-top: 0;
				vertical-align: top;
			}



			/* 右侧地图容器 - 保持实际宽度不变 */
			.index .itemright {
				width: 50%;
				right: 10px;
				position: relative;
				overflow: hidden;
				border-radius: 12px;
				transition: all 0.3s ease;
				box-shadow: 0 4px 16px rgba(0, 0, 0, 0.1);
				height: 100%;
				flex-shrink: 0;
			}

			/* 调整flexview布局 - 三栏精确对齐 */
			.index .flexview {
				display: flex;
				flex-wrap: nowrap;
				justify-content: space-between;
				align-items: stretch;
				gap: 15px;
				padding: 0 !important;
				height: 320px; /* 设置固定高度以精确对齐 */
			}

			/* 三个大版块统一对齐设置 - 强制覆盖所有contextview的padding */
			.index .contextview {
				padding: 0 20px !important;
				box-sizing: border-box !important;
			}

			/* 确保所有子容器也对齐 */
			.hsyxbox .item.hsyx {
				padding: 0 !important;
			}

			.contextview.zxxx .zxxxleft,
			.contextview.zxxx .zxxxright {
				margin: 0 !important;
				padding: 0 !important;
			}

			/* 心声社区版块 - 调整宽度使右边框对齐 */
			.index .flexview .item:first-child {
				width: 47%;
				min-width: 300px;
				flex-shrink: 0;
				display: flex;
				flex-direction: column;
				height: 100%;
			}

			/* 中间+右侧版块容器 */
			.index .flexview .item.flex {
				width: 53%;
				display: flex;
				justify-content: space-between;
				align-items: stretch;
				gap: 10px;
				flex-shrink: 0;
				height: 100%;
			}

			/* 中间卡片区域 - 减少宽度保持地图宽度不变 */
			.index .flexview .item.flex .middle-cards {
				width: 45%;
				display: flex;
				flex-direction: column;
				gap: 15px;
				height: 100%;
			}

			/* 虚仿实验空间和医德博物馆卡片 */
			.index .flexview .item.flex .middle-cards .card {
				flex: 1;
				background: linear-gradient(135deg, #fff 0%, #fafafa 100%);
				border-radius: 16px;
				transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
				padding: 14px;
				position: relative;
				overflow: hidden;
				border: none;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
				height: 147px;
				box-shadow: 0 2px 12px rgba(0, 0, 0, 0.08);
			}

			/* 为不同卡片设置红色主题渐变背景 */
			.index .flexview .item.flex .middle-cards .card.virtual-lab {
				border-left: 5px solid #c00714;
			}

			.index .flexview .item.flex .middle-cards .card.museum {
				border-left: 5px solid #a00610;
			}

			.index .flexview .item.flex .middle-cards .card:hover {
				box-shadow: 0 6px 20px rgba(0, 0, 0, 0.12);
			}

			.index .flexview .item.flex .middle-cards .card.virtual-lab:hover {
				background: linear-gradient(135deg, #fef8f8 0%, #fdf2f2 100%);
				border-left-color: #a00610;
			}

			.index .flexview .item.flex .middle-cards .card.museum:hover {
				background: linear-gradient(135deg, #fef7f7 0%, #fcf1f1 100%);
				border-left-color: #8a0508;
			}

			.index .flexview .item.flex .middle-cards .card .card-header {
				display: flex;
				align-items: center;
				margin-bottom: 10px;
			}

			.index .flexview .item.flex .middle-cards .card .card-icon {
				margin-right: 12px;
				width: 44px;
				height: 44px;
				border-radius: 12px;
				display: flex;
				align-items: center;
				justify-content: center;
				transition: all 0.3s ease;
				flex-shrink: 0;
			}

			/* 实验室图标样式 - 红色主题 */
			.index .flexview .item.flex .middle-cards .card .card-icon.experiment-icon {
				background: linear-gradient(135deg, #fde8e8 0%, #f8d7da 100%);
				color: #c00714;
			}

			/* 博物馆图标样式 - 红色主题 */
			.index .flexview .item.flex .middle-cards .card .card-icon.museum-icon {
				background: linear-gradient(135deg, #fce4e4 0%, #f7d0d0 100%);
				color: #a00610;
			}

			.index .flexview .item.flex .middle-cards .card .card-icon svg {
				width: 24px;
				height: 24px;
				transition: transform 0.3s ease;
			}

			.index .flexview .item.flex .middle-cards .card:hover .card-icon {
				transform: scale(1.05);
			}

			.index .flexview .item.flex .middle-cards .card:hover .card-icon svg {
				transform: rotate(3deg);
			}

			.index .flexview .item.flex .middle-cards .card h3 {
				font-size: 17px;
				font-weight: 600;
				color: #2c3e50;
				margin: 0;
				letter-spacing: -0.2px;
			}

			.index .flexview .item.flex .middle-cards .card .card-content {
				flex: 1;
				display: flex;
				flex-direction: column;
				justify-content: space-between;
			}

			.index .flexview .item.flex .middle-cards .card .card-content p {
				font-size: 13px;
				color: #5a6c7d;
				line-height: 1.4;
				margin: 0 0 8px 0;
				overflow: hidden;
				display: -webkit-box;
				-webkit-line-clamp: 2;
				-webkit-box-orient: vertical;
			}

			.index .flexview .item.flex .middle-cards .card .card-btn {
				background: linear-gradient(135deg, #34495e 0%, #2c3e50 100%);
				color: white;
				border: none;
				padding: 7px 14px;
				border-radius: 20px;
				cursor: pointer;
				transition: all 0.3s ease;
				font-size: 13px;
				font-weight: 500;
				align-self: flex-end;
				text-decoration: none;
				display: flex;
				align-items: center;
				gap: 6px;
				box-shadow: 0 2px 8px rgba(52, 73, 94, 0.2);
			}

			/* 为不同卡片设置红色主题按钮颜色 */
			.index .flexview .item.flex .middle-cards .card.virtual-lab .card-btn {
				background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
				box-shadow: 0 2px 8px rgba(192, 7, 20, 0.3);
			}

			.index .flexview .item.flex .middle-cards .card.museum .card-btn {
				background: linear-gradient(135deg, #a00610 0%, #8a0508 100%);
				box-shadow: 0 2px 8px rgba(160, 6, 16, 0.3);
			}

			.index .flexview .item.flex .middle-cards .card .card-btn:hover {
				box-shadow: 0 4px 15px rgba(52, 73, 94, 0.4);
			}

			.index .flexview .item.flex .middle-cards .card.virtual-lab .card-btn:hover {
				box-shadow: 0 4px 15px rgba(192, 7, 20, 0.4);
			}

			.index .flexview .item.flex .middle-cards .card.museum .card-btn:hover {
				box-shadow: 0 4px 15px rgba(160, 6, 16, 0.4);
			}

			.index .flexview .item.flex .middle-cards .card .card-btn .btn-arrow {
				font-size: 14px;
				font-weight: 600;
				transition: transform 0.3s ease;
			}

			.index .flexview .item.flex .middle-cards .card .card-btn:hover .btn-arrow {
				transform: translateX(2px);
			}

			.map-container {
				display: block;
				width: 100%;
				height: 320px; /* 与flexview高度保持一致 */
				position: relative;
				text-decoration: none;
				border-radius: 12px;
				overflow: hidden;
			}

			.map-container:hover {
				box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
			}

			.map-image {
				width: 100%;
				height: 100%;
				object-fit: cover;
				display: block;
				border-radius: 12px;
				transition: all 0.3s ease;
			}

			.map-container:hover .map-image {
				transform: scale(1.02);
			}

			.map-container .itemrightb {
				position: absolute;
				bottom: 0;
				left: 0;
				right: 0;
				background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
				color: white;
				text-align: center;
				padding: 12px 20px;
				font-size: 16px;
				font-weight: 600;
				letter-spacing: 0.5px;
				transition: all 0.3s ease;
				border-radius: 0 0 12px 12px;
				cursor: pointer;
			}

			.map-container:hover .itemrightb {
				background: linear-gradient(135deg, #a00610 0%, #8a0508 100%);
				padding: 14px 20px;
				font-size: 17px;
			}

			.map-container .itemrightb .star-icon {
				margin-right: 8px;
				font-size: 16px;
				color: #ffd700;
				opacity: 0.9;
			}

			.map-container:hover .itemrightb .star-icon {
				animation: footstep 0.6s ease-in-out;
			}

			@keyframes footstep {
				0%, 100% { transform: translateX(0); }
				25% { transform: translateX(-3px); }
				75% { transform: translateX(3px); }
			}



			/* 响应式设计 */
			@media (max-width: 1200px) {
				.index .flexview {
					gap: 15px;
					padding: 0 15px;
					height: 300px;
				}

				.index .flexview .item:first-child {
					width: 40%;
					min-width: 280px;
				}

				.index .flexview .item.flex {
					width: 60%;
					gap: 15px;
				}

				.index .flexview .item.flex .middle-cards {
					width: 52%;
				}

				.index .itemright {
					width: 48%;
				}

				.community-container {
					height: 300px;
				}

				.map-container {
					height: 300px;
				}

				.index .flexview .item.flex .middle-cards .card {
					height: 135px;
					padding: 14px;
				}

				.index .flexview .item.flex .middle-cards .card h3 {
					font-size: 16px;
				}

				.index .flexview .item.flex .middle-cards .card .card-content p {
					font-size: 12px;
					margin-bottom: 10px;
				}

				.index .flexview .item.flex .middle-cards .card .card-icon {
					width: 40px;
					height: 40px;
					margin-right: 10px;
				}

				.index .flexview .item.flex .middle-cards .card .card-icon svg {
					width: 20px;
					height: 20px;
				}

				.itemtopbox {
					max-height: 260px;
				}
			}

			@media (max-width: 992px) {
				.index .flexview {
					flex-direction: column;
					gap: 20px;
					height: auto;
				}

				.index .flexview .item:first-child,
				.index .flexview .item.flex {
					width: 100%;
					height: auto;
				}

				.index .flexview .item.flex .middle-cards {
					width: 60%;
				}

				.index .itemright {
					width: 40%;
				}

				.community-container {
					height: 280px;
				}

				.map-container {
					height: 250px;
				}

				.itemtopbox {
					max-height: 240px;
				}
			}

			@media (max-width: 768px) {
				.index .flexview .item.flex {
					flex-direction: column;
					gap: 15px;
				}

				.index .flexview .item.flex .middle-cards,
				.index .itemright {
					width: 100%;
				}

				.index .flexview .item.flex .middle-cards .card {
					height: 120px;
					padding: 12px;
				}

				.index .flexview .item.flex .middle-cards .card h3 {
					font-size: 15px;
				}

				.index .flexview .item.flex .middle-cards .card .card-content p {
					font-size: 12px;
					margin-bottom: 8px;
					-webkit-line-clamp: 1;
				}

				.index .flexview .item.flex .middle-cards .card .card-icon {
					width: 36px;
					height: 36px;
					margin-right: 8px;
				}

				.index .flexview .item.flex .middle-cards .card .card-icon svg {
					width: 18px;
					height: 18px;
				}

				.index .flexview .item.flex .middle-cards .card .card-btn {
					padding: 6px 12px;
					font-size: 12px;
				}

				.community-container {
					height: 240px;
				}

				.map-container {
					height: 200px;
				}

				.itemtopbox {
					max-height: 200px;
				}
			}





			/* 添加红色书籍轮播样式 */
			/* 红色书籍轮播样式 */
			.hssjboxs {
				position: relative;
				overflow: hidden;
				width: 100%;
				height: 100%;
			}
			
			.hssjbox-scroll {
				display: flex;
				width: max-content;
				animation: scroll-left 120s linear infinite;
			}
			
			.hssjbox-scroll:hover {
				animation-play-state: paused;
			}
			
			@keyframes scroll-left {
				0% { transform: translateX(0); }
				100% { transform: translateX(-50%); }
			}
			
			.hssjitem {
				flex: 0 0 auto;
				width: 150px;
				margin-right: 15px;
				cursor: pointer;
				transition: all 0.3s ease;
			}
			
			.hssjitem:hover {
				transform: translateY(-5px);
				box-shadow: 0 5px 15px rgba(166, 93, 87, 0.2);
			}
			
			.hssjitem img {
				width: 100%;
				height: 200px;
				object-fit: cover;
				border-radius: 5px;
				box-shadow: 0 3px 10px rgba(0, 0, 0, 0.1);
			}
			
			.hssjtitle {
				font-size: 14px;
				font-weight: bold;
				margin-top: 8px;
				overflow: hidden;
				text-overflow: ellipsis;
				white-space: nowrap;
			}
			
			.hssjitem .zz {
				font-size: 12px;
				color: #666;
				margin-top: 5px;
			}
			
			.hssjitem .zz label {
				color: #A65D57;
				font-weight: 500;
			}
			
			/* 空状态样式 */
			.empty-state {
				padding: 20px;
				text-align: center;
				color: #666;
				font-size: 14px;
				background-color: #f9f9f9;
				border-radius: 5px;
				border: 1px dashed #ddd;
			}
		</style>
	</body>
</html>
