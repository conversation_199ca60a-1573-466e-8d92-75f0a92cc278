// 导航统一样式脚本 - 完全按照userinfo.html标准
// 确保所有页面导航与个人中心页面完全一致，图标固定不变

function applyUserInfoNavStyles() {
	try {
		// 检查document.head是否存在
		if (!document.head) {
			console.warn('⚠️ document.head不存在，无法添加样式');
			return;
		}
		
	const style = document.createElement('style');
	style.id = 'userinfo-nav-styles';
	style.innerHTML = `
		/* 现代化导航栏美化 - 与userinfo.html完全一致 */
		.boxleft {
			background: white !important;
			border-radius: 16px !important;
			box-shadow: 0 8px 32px rgba(220, 53, 69, 0.08) !important;
			overflow: hidden !important;
			border: 1px solid rgba(231, 76, 60, 0.05) !important;
			position: relative !important;
			backdrop-filter: blur(20px) !important;
		}
		
		.boxleft::before {
			content: '' !important;
			position: absolute !important;
			top: 0 !important;
			left: 0 !important;
			right: 0 !important;
			bottom: 0 !important;
			background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8)) !important;
			pointer-events: none !important;
			border-radius: 16px !important;
		}
		
		/* 导航栏顶部标题区域 - 完全按照userinfo.html */
		.lefttopview {
			width: 100% !important;
			height: 65px !important;
			background: linear-gradient(135deg, #dc3545, #e74c3c, #ff6b35) !important;
			background-size: 300% 300% !important;
			animation: gradientShift 6s ease infinite !important;
			display: flex !important;
			justify-content: center !important;
			align-items: center !important;
			font-size: 18px !important;
			color: #FFFFFF !important;
			font-weight: 700 !important;
			font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif !important;
			position: relative !important;
			box-shadow: 0 4px 20px rgba(220, 53, 69, 0.25) !important;
			letter-spacing: 1.5px !important;
			border-radius: 16px 16px 0 0 !important;
		}
		
		/* 渐变动画效果 */
		@keyframes gradientShift {
			0% { background-position: 0% 50%; }
			33% { background-position: 100% 50%; }
			66% { background-position: 50% 100%; }
			100% { background-position: 0% 50%; }
		}
		
		.lefttopview img {
			width: 24px !important;
			height: 24px !important;
			display: block !important;
			margin-right: 12px !important;
			filter: brightness(0) invert(1) drop-shadow(0 0 6px rgba(255, 255, 255, 0.4)) !important;
		}
		
		.lefttopview label {
			display: flex !important;
			align-items: center !important;
			text-shadow: 0 2px 6px rgba(0, 0, 0, 0.2) !important;
		}
		
		/* 导航菜单容器 - 按照userinfo.html */
		.leftitembox, #teambox {
			background: transparent !important;
			padding: 20px 0 !important;
			position: relative !important;
			z-index: 2 !important;
			min-height: 420px !important;
		}
		
		.leftitembox::before, #teambox::before {
			content: '' !important;
			position: absolute !important;
			top: 0 !important;
			left: 50% !important;
			transform: translateX(-50%) !important;
			width: 85% !important;
			height: 1px !important;
			background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.15), transparent) !important;
		}
		
		/* 导航菜单项 - 完全按照userinfo.html样式 */
		.leftitem, #teambox .leftitem, .leftitembox .leftitem {
			height: auto !important;
			padding: 16px 24px !important;
			display: flex !important;
			justify-content: flex-start !important;
			align-items: center !important;
			font-size: 15px !important;
			font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif !important;
			font-weight: 500 !important;
			color: #4a5568 !important;
			border-bottom: none !important;
			text-decoration: none !important;
			transition: color 0.2s ease, background-color 0.2s ease !important;
			position: relative !important;
			margin: 4px 16px !important;
			border-radius: 12px !important;
			background: transparent !important;
			overflow: hidden !important;
			min-height: 48px !important;
			box-sizing: border-box !important;
			cursor: pointer !important;
			/* 移除动画，立即显示 */
			opacity: 1 !important;
			transform: translateX(0) !important;
			animation: none !important;
		}
		
		/* 图标样式 - 按照userinfo.html */
		.leftitem::before, #teambox .leftitem::before, .leftitembox .leftitem::before {
			content: '' !important;
			width: 20px !important;
			height: 20px !important;
			margin-right: 12px !important;
			background-size: contain !important;
			background-repeat: no-repeat !important;
			background-position: center !important;
			opacity: 0.7 !important;
			transition: opacity 0.2s ease !important;
			flex-shrink: 0 !important;
		}
		
		/* 固定图标定义 - 每个导航项都有固定的图标 */
		/* 个人信息图标 */
		.leftitem[data-icon="user"]::before,
		#teambox .leftitem[data-icon="user"]::before,
		.leftitembox .leftitem[data-icon="user"]::before {
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E") !important;
		}
		
		/* 学习任务图标 */
		.leftitem[data-icon="task"]::before,
		#teambox .leftitem[data-icon="task"]::before,
		.leftitembox .leftitem[data-icon="task"]::before {
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E") !important;
		}
		
		/* 学习路径/学习记录图标 */
		.leftitem[data-icon="record"]::before,
		#teambox .leftitem[data-icon="record"]::before,
		.leftitembox .leftitem[data-icon="record"]::before {
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z'/%3E%3C/svg%3E") !important;
		}
		
		/* 考试成绩图标 */
		.leftitem[data-icon="exam"]::before,
		#teambox .leftitem[data-icon="exam"]::before,
		.leftitembox .leftitem[data-icon="exam"]::before {
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z'/%3E%3C/svg%3E") !important;
		}
		
		/* 发布心声图标 */
		.leftitem[data-icon="voice"]::before,
		#teambox .leftitem[data-icon="voice"]::before,
		.leftitembox .leftitem[data-icon="voice"]::before {
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z'/%3E%3C/svg%3E") !important;
		}
		
		/* 成果展示/发布资源图标 */
		.leftitem[data-icon="achievement"]::before,
		#teambox .leftitem[data-icon="achievement"]::before,
		.leftitembox .leftitem[data-icon="achievement"]::before {
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E") !important;
		}
		
		/* 考试管理图标 */
		.leftitem[data-icon="manage"]::before,
		#teambox .leftitem[data-icon="manage"]::before,
		.leftitembox .leftitem[data-icon="manage"]::before {
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E") !important;
		}
		
		/* 学生成绩统计图标 */
		.leftitem[data-icon="chart"]::before,
		#teambox .leftitem[data-icon="chart"]::before,
		.leftitembox .leftitem[data-icon="chart"]::before {
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M5,2V13H16V2M5,15V22H12V15M18,9V22H25V9M14,20V22H16V20M18,7V9H25V7M5,13V15H16V13Z'/%3E%3C/svg%3E") !important;
		}
		
		/* 悬浮态 - 按照userinfo.html */
		.leftitem:hover, #teambox .leftitem:hover, .leftitembox .leftitem:hover {
			background: linear-gradient(135deg, rgba(220, 53, 69, 0.06), rgba(231, 76, 60, 0.08)) !important;
			color: #dc3545 !important;
		}
		
		.leftitem:hover::before, #teambox .leftitem:hover::before, .leftitembox .leftitem:hover::before {
			opacity: 1 !important;
		}
		
		/* 激活状态 - 完全按照userinfo.html */
		.leftitem.activeleftitem, a.leftitem.activeleftitem,
		.leftitembox .leftitem.activeleftitem, #teambox .leftitem.activeleftitem,
		#teambox a.leftitem.activeleftitem, .leftitembox a.leftitem.activeleftitem {
			font-weight: 700 !important;
			color: white !important;
			background: linear-gradient(135deg, #dc3545, #e74c3c) !important;
			background-size: 200% 200% !important;
			animation: activeGradient 3s ease infinite !important;
			box-shadow: 0 4px 16px rgba(220, 53, 69, 0.25) !important;
			border-radius: 12px !important;
			opacity: 1 !important;
			transform: translateX(0) !important;
		}
		
		@keyframes activeGradient {
			0% { background-position: 0% 50%; }
			33% { background-position: 100% 50%; }
			66% { background-position: 50% 100%; }
			100% { background-position: 0% 50%; }
		}
		
		/* 激活状态图标 - 只改变颜色，不改变图标形状 */
		.leftitem.activeleftitem[data-icon="user"]::before,
		#teambox .leftitem.activeleftitem[data-icon="user"]::before,
		.leftitembox .leftitem.activeleftitem[data-icon="user"]::before {
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E") !important;
			opacity: 1 !important;
			filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
		}
		
		.leftitem.activeleftitem[data-icon="task"]::before,
		#teambox .leftitem.activeleftitem[data-icon="task"]::before,
		.leftitembox .leftitem.activeleftitem[data-icon="task"]::before {
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E") !important;
			opacity: 1 !important;
			filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
		}
		
		.leftitem.activeleftitem[data-icon="record"]::before,
		#teambox .leftitem.activeleftitem[data-icon="record"]::before,
		.leftitembox .leftitem.activeleftitem[data-icon="record"]::before {
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z'/%3E%3C/svg%3E") !important;
			opacity: 1 !important;
			filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
		}
		
		.leftitem.activeleftitem[data-icon="exam"]::before,
		#teambox .leftitem.activeleftitem[data-icon="exam"]::before,
		.leftitembox .leftitem.activeleftitem[data-icon="exam"]::before {
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z'/%3E%3C/svg%3E") !important;
			opacity: 1 !important;
			filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
		}
		
		.leftitem.activeleftitem[data-icon="voice"]::before,
		#teambox .leftitem.activeleftitem[data-icon="voice"]::before,
		.leftitembox .leftitem.activeleftitem[data-icon="voice"]::before {
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z'/%3E%3C/svg%3E") !important;
			opacity: 1 !important;
			filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
		}
		
		.leftitem.activeleftitem[data-icon="achievement"]::before,
		#teambox .leftitem.activeleftitem[data-icon="achievement"]::before,
		.leftitembox .leftitem.activeleftitem[data-icon="achievement"]::before {
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E") !important;
			opacity: 1 !important;
			filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
		}
		
		.leftitem.activeleftitem[data-icon="manage"]::before,
		#teambox .leftitem.activeleftitem[data-icon="manage"]::before,
		.leftitembox .leftitem.activeleftitem[data-icon="manage"]::before {
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E") !important;
			opacity: 1 !important;
			filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
		}
		
		.leftitem.activeleftitem[data-icon="chart"]::before,
		#teambox .leftitem.activeleftitem[data-icon="chart"]::before,
		.leftitembox .leftitem.activeleftitem[data-icon="chart"]::before {
			background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M5,2V13H16V2M5,15V22H12V15M18,9V22H25V9M14,20V22H16V20M18,7V9H25V7M5,13V15H16V13Z'/%3E%3C/svg%3E") !important;
			opacity: 1 !important;
			filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
		}
		
		/* 激活状态右侧指示器 */
		.leftitem.activeleftitem::after, a.leftitem.activeleftitem::after,
		.leftitembox .leftitem.activeleftitem::after, #teambox .leftitem.activeleftitem::after,
		#teambox a.leftitem.activeleftitem::after, .leftitembox a.leftitem.activeleftitem::after {
			content: '' !important;
			position: absolute !important;
			right: 16px !important;
			top: 50% !important;
			transform: translateY(-50%) !important;
			width: 8px !important;
			height: 8px !important;
			background: white !important;
			border-radius: 50% !important;
			box-shadow: 0 0 12px rgba(255, 255, 255, 0.8) !important;
			animation: activePulse 2s ease infinite !important;
		}
		
		@keyframes activePulse {
			0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
			50% { opacity: 0.6; transform: translateY(-50%) scale(1.4); }
		}
		
		/* 分隔线效果 - 按照userinfo.html */
		.leftitem:not(:last-child):not(.activeleftitem), 
		#teambox .leftitem:not(:last-child):not(.activeleftitem),
		.leftitembox .leftitem:not(:last-child):not(.activeleftitem) {
			border-bottom: 1px solid rgba(231, 76, 60, 0.06) !important;
			margin-bottom: 3px !important;
		}
		
		.leftitem:not(:last-child):not(.activeleftitem)::after,
		#teambox .leftitem:not(:last-child):not(.activeleftitem)::after,
		.leftitembox .leftitem:not(:last-child):not(.activeleftitem)::after {
			content: '' !important;
			position: absolute !important;
			bottom: -1px !important;
			left: 40px !important;
			right: 24px !important;
			height: 1px !important;
			background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.15), transparent) !important;
			opacity: 0.5 !important;
		}
		
		/* 交互反馈 - 按照userinfo.html */
		.leftitem:active, #teambox .leftitem:active, .leftitembox .leftitem:active {
			transform: scale(0.98) !important;
			transition: all 0.1s ease !important;
		}
		
		.activeleftitem:active {
			transform: scale(1.02) !important;
		}
		
		/* 移除所有动画延迟 */
		.leftitem:nth-child(1), .leftitem:nth-child(2), .leftitem:nth-child(3), 
		.leftitem:nth-child(4), .leftitem:nth-child(5), .leftitem:nth-child(6), 
		.leftitem:nth-child(7), #teambox .leftitem:nth-child(1), 
		#teambox .leftitem:nth-child(2), #teambox .leftitem:nth-child(3), 
		#teambox .leftitem:nth-child(4), #teambox .leftitem:nth-child(5), 
		#teambox .leftitem:nth-child(6), #teambox .leftitem:nth-child(7) { 
			animation-delay: 0s !important;
			opacity: 1 !important;
			transform: translateX(0) !important;
		}
		
		/* 响应式优化 - 按照userinfo.html */
		@media (max-width: 768px) {
			.boxleft {
				border-radius: 12px !important;
				margin-bottom: 20px !important;
			}
			
			.lefttopview {
				height: 55px !important;
				font-size: 16px !important;
				letter-spacing: 1px !important;
			}
			
			.lefttopview img {
				width: 20px !important;
				height: 20px !important;
				margin-right: 10px !important;
			}
			
			.leftitem, #teambox .leftitem, .leftitembox .leftitem {
				padding: 14px 20px !important;
				font-size: 14px !important;
				margin: 3px 12px !important;
				min-height: 44px !important;
			}
			
			.leftitem::before, #teambox .leftitem::before, .leftitembox .leftitem::before {
				width: 18px !important;
				height: 18px !important;
				margin-right: 10px !important;
			}
		}
	`;
	
	// 移除已存在的样式
	const existingStyle = document.getElementById('userinfo-nav-styles');
	if (existingStyle) {
		existingStyle.remove();
	}
	
	document.head.appendChild(style);
	console.log('✅ userinfo.html样式应用成功');
	
	} catch (error) {
		console.error('❌ 应用userinfo.html样式失败:', error);
	}
}

// 图标映射表 - 根据文本内容确定图标类型
const iconMapping = {
	'个人信息': 'user',
	'学习任务': 'task',
	'学习任务管理': 'task',
	'学习路径': 'record',
	'学习记录': 'record',
	'学习记录统计': 'record',
	'考试成绩': 'exam',
	'考试管理': 'manage',
	'发布心声': 'voice',
	'成果展示': 'achievement',
	'发布资源': 'achievement',
	'学生成绩统计': 'chart'
};

// 应用固定图标
function applyFixedIcons() {
	try {
	const leftItems = document.querySelectorAll('.leftitem, #teambox .leftitem');
		
		if (leftItems.length === 0) {
			console.log('📝 未找到导航项，跳过图标设置');
			return;
		}
	
	leftItems.forEach(item => {
			try {
		const text = item.textContent.trim();
		
		// 根据文本内容确定图标类型
		let iconType = null;
		for (const [keyword, type] of Object.entries(iconMapping)) {
			if (text === keyword || text.includes(keyword)) {
				iconType = type;
				break;
			}
		}
		
		// 如果找到匹配的图标类型，设置data-icon属性
		if (iconType) {
			item.setAttribute('data-icon', iconType);
			console.log(`设置图标: ${text} -> ${iconType}`);
		} else {
			// 默认使用用户图标
			item.setAttribute('data-icon', 'user');
			console.log(`默认图标: ${text} -> user`);
				}
			} catch (itemError) {
				console.warn('⚠️ 设置单个导航项图标失败:', itemError);
		}
	});
	} catch (error) {
		console.error('❌ 应用固定图标失败:', error);
	}
}

// 移除动画效果
function removeAllAnimations() {
	try {
	const leftItems = document.querySelectorAll('.leftitem, #teambox .leftitem');
		
		if (leftItems.length === 0) {
			console.log('📝 未找到导航项，跳过动画移除');
			return;
		}
	
	leftItems.forEach(item => {
			try {
		// 移除动画类
		item.classList.remove('slideInLeft', 'animated');
		
		// 强制设置样式
		item.style.animation = 'none';
		item.style.animationDelay = '0s';
		item.style.opacity = '1';
		item.style.transform = 'translateX(0)';
		item.style.visibility = 'visible';
			} catch (itemError) {
				console.warn('⚠️ 移除单个导航项动画失败:', itemError);
			}
	});
	} catch (error) {
		console.error('❌ 移除动画效果失败:', error);
	}
}

// 主初始化函数
function initUserInfoNavigation() {
	console.log('🚀 正在按照userinfo.html标准统一导航样式和固定图标...');
	
	// 1. 应用userinfo.html样式
	applyUserInfoNavStyles();
	
	// 2. 移除动画
	removeAllAnimations();
	
	// 3. 应用固定图标
	setTimeout(() => {
		applyFixedIcons();
		
		// 监听DOM变化 - 修复MutationObserver错误
		if (document.body) {
		const observer = new MutationObserver((mutations) => {
			let shouldUpdate = false;
			
			mutations.forEach((mutation) => {
				if (mutation.addedNodes.length > 0) {
					mutation.addedNodes.forEach((node) => {
						if (node.nodeType === 1 && 
							(node.classList && node.classList.contains('leftitem') || 
							 (node.querySelector && node.querySelector('.leftitem')))) {
							shouldUpdate = true;
						}
					});
				}
			});
			
			if (shouldUpdate) {
				console.log('🔄 检测到导航变化，重新应用固定图标...');
				setTimeout(() => {
					applyFixedIcons();
					removeAllAnimations();
				}, 50);
			}
		});
		
			// 安全地观察document.body
			try {
		observer.observe(document.body, {
			childList: true,
			subtree: true,
			attributes: true,
			attributeFilter: ['class']
		});
		
		console.log('✅ 导航样式和固定图标系统统一完成！');
			} catch (error) {
				console.warn('⚠️ MutationObserver初始化失败:', error.message);
				console.log('✅ 导航样式和固定图标系统统一完成（无监听器）！');
			}
		} else {
			console.warn('⚠️ document.body未准备好，跳过MutationObserver初始化');
			console.log('✅ 导航样式和固定图标系统统一完成（无监听器）！');
		}
		
	}, 100);
}

// 确保在DOM加载完成后执行
function initNavEnhancement() {
	// 检查DOM是否准备好
	function isDocumentReady() {
		return document.readyState === 'complete' || 
			   (document.readyState === 'interactive' && document.body);
	}
	
	// 安全的初始化函数
	function safeInit() {
		try {
			if (document.body) {
				initUserInfoNavigation();
			} else {
				console.warn('⚠️ document.body不存在，延迟初始化...');
				setTimeout(safeInit, 100);
			}
		} catch (error) {
			console.error('❌ 导航增强初始化失败:', error);
		}
	}
	
	if (isDocumentReady()) {
		safeInit();
	} else if (document.readyState === 'loading') {
		document.addEventListener('DOMContentLoaded', safeInit);
		// 备用方案
		setTimeout(safeInit, 500);
	} else {
		// 立即执行
		safeInit();
	}
	
	// 额外保险 - 确保在窗口加载完成后再次尝试
	window.addEventListener('load', function() {
		setTimeout(safeInit, 100);
	});
}

// 导出全局函数
window.initNavEnhancement = initNavEnhancement;
window.applyUserInfoNavStyles = applyUserInfoNavStyles;

// 自动初始化
initNavEnhancement(); 