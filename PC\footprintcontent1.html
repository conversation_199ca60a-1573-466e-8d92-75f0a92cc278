<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>思政一体化平台-习主席足迹详情</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/footprint.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		
		<!-- 外部链接检测与重定向脚本 -->
		<script type="text/javascript">
			// 检查内容是否为外部链接，如果是则重定向
			function checkAndRedirectExternalLink(content) {
				if (!content) return false;
				
				// 匹配标记之间的外部链接 - 使用字符串而不是正则表达式
				const startTag = "<!-- EXTERNAL_LINK_START -->";
				const endTag = "<!-- EXTERNAL_LINK_END -->";
				
				if (content.includes(startTag) && content.includes(endTag)) {
					const startPos = content.indexOf(startTag) + startTag.length;
					const endPos = content.indexOf(endTag);
					
					if (startPos < endPos) {
						const externalUrl = content.substring(startPos, endPos).trim();
						
						// 添加加载提示
						document.getElementById('content').innerHTML = '<div style="text-align:center;padding:20px;">正在重定向到外部链接，请稍候...</div>';
						
						// 延迟一点重定向，让用户有时间看到提示
						setTimeout(function() {
							// 重定向到外部链接
							window.location.href = externalUrl;
						}, 800);
						return true;
					}
				}
				return false;
			}
		</script>
	</head>
	<body class="index" onbeforeunload="tiaozhuan()">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
		
				</div>
			</div>
		</div>
		<div class="content">
			<div class="contenttitlebox">
				<div>
					<a id="zxxx" href="footprint.html">总书记的足迹</a>
					<img src="img/jtright.png" />
					<label id="city" class="aa"></label>
				</div>
			</div>
			<div class="contentbox">
				<div class="boxtop">
					<div>
						<div><img src="img/zz.png" />
							<span id="zz"></span></div>
						<!-- <div><img src="img/ly.png" />
							社会科学部</div> -->
					</div>
					<div>
						<div><img src="img/sj2.png" /><span id="date"></span></div>
						<div><img src="img/yj.png" /><span id="view"></span></div>
					</div>
				</div>
				<div class="xqnr">
					<div class="xqnrtitle" id="title"></div>
					<div id="content">
						
					</div>
				</div>
			</div>
		</div>

		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
			let userinfo = sessionStorage.getItem("userinfo")
			let pflid = null
			let time1 = null // 定义时间变量
			
			$(function() {
				$(".ssview").on('click',function(){
					window.location.href = 'searchall.html'
				})
				
				// 判断是否已登录
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				
				// 不管是否登录都尝试获取内容
				fetchFootprintContent();
				getclass('footprint.html');
				getfooterlink();
			})
			
			// 菜单加载函数
			function getclass(redirectUrl) {
				// 构建请求头，如果用户已登录则添加 Authorization 头
				const headers = sessionStorage.getItem("header") 
					? {"Authorization": sessionStorage.getItem("header")} 
					: {};
					
				$.ajax({
					url: baseurl + "/web/category/all",
					type: 'GET',
					contentType: "application/json",
					headers: headers,
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							for (let i = 0; i < res.data.length; i++) {
								if (res.data[i].id == getUrlParam('id') || res.data[i].redirectUrl == redirectUrl) {
									// classdate = res.data[i]
									$("#zxxx").html(res.data[i].name)
									$("#zxxx").attr('href', "footprint.html")
									html += '<a class="menuitem active" href="' + res.data[i].redirectUrl + '?id=' +
										res.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
								} else {
									html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res.data[i].id + 
										'"><label>' + res.data[i].name + '</label></a>'
								}
							}
							$("#menubox").html(html)
						}
					}
				})
			}
			
			// 提取获取足迹内容的函数
			function fetchFootprintContent() {
				// 构建请求头，如果用户已登录则添加 Authorization 头
				const headers = sessionStorage.getItem("header") 
					? {"Authorization": sessionStorage.getItem("header")} 
					: {};
					
				$.ajax({
					url: baseurl + "/footprint/"+getUrlParam('id'),
					type: 'GET',
					contentType: "application/json",
					headers: headers,
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							pflid = res.data.cmsCategoryList[0].id;
							$("#city").html(res.data.city);
							$("#zz").html(res.data.author);
							$("#date").html(res.data.dateTime);
							$("#view").html(res.data.views);
							$("#title").html(res.data.title);
							
							// 检查是否为外部链接，如果是则重定向
							if (!checkAndRedirectExternalLink(res.data.content)) {
								// 不是外部链接，正常显示内容
								$("#content").html(res.data.content);
							}
							
							// 记录浏览量
							clicknum(getUrlParam('id'));
							
							// 如果是学生角色，开始记录学习时间
							if (userinfo && JSON.parse(userinfo).roleName == '学生') {
								time1 = Date.now();
							}
						} else if (res.code == '401') {
							// 未授权，显示友好提示
							$("#title").html("需要登录才能查看详细内容");
							$("#content").html('<div style="text-align:center;padding:20px;"><p style="font-size:16px;color:#666;">您尚未登录，请点击右上角的"用户登录"按钮进行登录后查看内容</p></div>');
						} else {
							// 其他错误
							$("#title").html("获取内容失败");
							$("#content").html('<div style="text-align:center;padding:20px;"><p style="font-size:16px;color:#666;">获取内容失败，请稍后再试</p></div>');
						}
					},
					error: function() {
						// 请求失败
						$("#title").html("网络错误");
						$("#content").html('<div style="text-align:center;padding:20px;"><p style="font-size:16px;color:#666;">网络连接错误，请检查网络连接后重试</p></div>');
					}
				});
			}
			
			function clicknum(id){
				// 构建请求头，如果用户已登录则添加 Authorization 头
				const headers = sessionStorage.getItem("header") 
					? {"Authorization": sessionStorage.getItem("header")} 
					: {};
					
				$.ajax({
					url: baseurl + "/footprint/views/"+id,
					type: 'GET',
					contentType: "application/json",
					headers: headers,
					dataType: 'json',
					success: (res) => {
						// 浏览量记录成功，无需处理
					}
				})
			}
			
			function tiaozhuan() {
				// 只有登录且为学生角色且开始了计时才记录学习时间
				if(userinfo && JSON.parse(userinfo).roleName == '学生' && time1){
					let time2 = Date.now()
					let value = time2 - time1
					var days = parseInt(value / (1000 * 60 * 60 * 24))
					var hours = parseInt((value % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60))
					var minutes = parseInt((value % (1000 * 60 * 60)) / (1000 * 60))
					var seconds = Math.floor((value % (1000 * 60)) / 1000)
					let json = {
						infoId: getUrlParam('id'), //信息id
						categoryId: pflid, //所属分类id
						totalInfo: "1", //总时长，总页数
						positioning: days+"天"+hours+"时"+minutes+"分"+seconds+"秒", //学习了多久，多少页    
						type: "主席足迹",
						learningTime: value,
						progress: "", //进度 百分比
					}
					window.localStorage.setItem("jilu",JSON.stringify(json))
				}
			}
			
			function getfooterlink(){
				// 构建请求头，如果用户已登录则添加 Authorization 头
				const headers = sessionStorage.getItem("header") 
					? {"Authorization": sessionStorage.getItem("header")} 
					: {};
					
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: headers,
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item)=>{
								html+='<a href="'+item.url+'" target="_blank">'+item.name+'</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
			
			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}
			
			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
