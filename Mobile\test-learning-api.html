<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>在线学习API测试</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            margin: 0;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 12px;
            padding: 20px;
            box-shadow: 0 2px 12px rgba(0,0,0,0.1);
        }
        
        h1 {
            color: #c00714;
            text-align: center;
            margin-bottom: 30px;
        }
        
        .test-item {
            margin-bottom: 20px;
            padding: 20px;
            border: 1px solid #e9ecef;
            border-radius: 8px;
            background: #f8f9fa;
        }
        
        .test-btn {
            background: #c00714;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            margin-right: 10px;
            margin-bottom: 10px;
        }
        
        .test-btn:hover {
            background: #a00610;
        }
        
        .status {
            margin-top: 10px;
            padding: 10px;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        
        .result-box {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 6px;
            padding: 15px;
            margin-top: 10px;
            max-height: 200px;
            overflow-y: auto;
            font-family: monospace;
            font-size: 12px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 在线学习API修复测试</h1>
        
        <div class="test-item">
            <h3>📚 1. 课程数据API测试</h3>
            <p>测试修复后的课程数据获取API</p>
            <button class="test-btn" onclick="testCoursesAPI()">测试课程API</button>
            <div id="coursesStatus" class="status info">
                ℹ 点击按钮测试课程API
            </div>
            <div id="coursesResult" class="result-box" style="display: none;"></div>
        </div>
        
        <div class="test-item">
            <h3>📖 2. 红色书籍API测试</h3>
            <p>测试修复后的红色书籍数据获取API</p>
            <button class="test-btn" onclick="testRedBooksAPI()">测试红色书籍API</button>
            <div id="redbooksStatus" class="status info">
                ℹ 点击按钮测试红色书籍API
            </div>
            <div id="redbooksResult" class="result-box" style="display: none;"></div>
        </div>
        
        <div class="test-item">
            <h3>🏷️ 3. 分类数据API测试</h3>
            <p>测试分类数据获取API</p>
            <button class="test-btn" onclick="testCategoryAPI()">测试分类API</button>
            <div id="categoryStatus" class="status info">
                ℹ 点击按钮测试分类API
            </div>
            <div id="categoryResult" class="result-box" style="display: none;"></div>
        </div>
        
        <div class="test-item">
            <h3>🔗 4. 在线学习页面测试</h3>
            <p>测试修复后的在线学习页面</p>
            <a href="pages/learning.html" class="test-btn" target="_blank">打开在线学习页面</a>
            <div class="status success">
                ✓ API端点已修复：/web/course 和 /web/posts<br>
                ✓ 添加了错误处理和重试机制<br>
                ✓ 使用PC端相同的参数格式
            </div>
        </div>
    </div>

    <script src="js/jquery-3.2.1.min.js"></script>
    <script src="js/mobile-base.js"></script>
    <script>
        function testCoursesAPI() {
            const statusDiv = document.getElementById('coursesStatus');
            const resultDiv = document.getElementById('coursesResult');
            
            statusDiv.className = 'status info';
            statusDiv.innerHTML = '⏳ 正在测试课程API...';
            resultDiv.style.display = 'none';
            
            getCoursesData(1, 5)
                .then(data => {
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `✓ 课程API测试成功！获取到 ${data.list ? data.list.length : 0} 条数据`;
                    
                    resultDiv.innerHTML = JSON.stringify(data, null, 2);
                    resultDiv.style.display = 'block';
                })
                .catch(error => {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = `✗ 课程API测试失败：${error}`;
                    
                    resultDiv.innerHTML = `错误信息：${error}`;
                    resultDiv.style.display = 'block';
                });
        }
        
        function testRedBooksAPI() {
            const statusDiv = document.getElementById('redbooksStatus');
            const resultDiv = document.getElementById('redbooksResult');
            
            statusDiv.className = 'status info';
            statusDiv.innerHTML = '⏳ 正在测试红色书籍API...';
            resultDiv.style.display = 'none';
            
            getRedBooksData(1, 5, "912354240784109568", "1369261422076366848")
                .then(data => {
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `✓ 红色书籍API测试成功！获取到 ${data.list ? data.list.length : 0} 条数据`;
                    
                    resultDiv.innerHTML = JSON.stringify(data, null, 2);
                    resultDiv.style.display = 'block';
                })
                .catch(error => {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = `✗ 红色书籍API测试失败：${error}`;
                    
                    resultDiv.innerHTML = `错误信息：${error}`;
                    resultDiv.style.display = 'block';
                });
        }
        
        function testCategoryAPI() {
            const statusDiv = document.getElementById('categoryStatus');
            const resultDiv = document.getElementById('categoryResult');
            
            statusDiv.className = 'status info';
            statusDiv.innerHTML = '⏳ 正在测试分类API...';
            resultDiv.style.display = 'none';
            
            getCategoryData()
                .then(data => {
                    statusDiv.className = 'status success';
                    statusDiv.innerHTML = `✓ 分类API测试成功！`;
                    
                    resultDiv.innerHTML = JSON.stringify(data, null, 2);
                    resultDiv.style.display = 'block';
                })
                .catch(error => {
                    statusDiv.className = 'status error';
                    statusDiv.innerHTML = `✗ 分类API测试失败：${error}`;
                    
                    resultDiv.innerHTML = `错误信息：${error}`;
                    resultDiv.style.display = 'block';
                });
        }
        
        // 页面加载时显示当前API配置
        window.onload = function() {
            console.log('当前API基础URL:', baseurl);
            console.log('修复的API端点:');
            console.log('- 课程: /web/course');
            console.log('- 红色书籍: /web/posts');
            console.log('- 分类: /web/category/teacher');
        };
    </script>
</body>
</html>
