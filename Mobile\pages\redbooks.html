<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0, user-scalable=no">
    <title>红色书籍 - 思政一体化平台</title>
    
    <!-- CSS文件 -->
    <link rel="stylesheet" href="../css/mobile-base.css">
    <link rel="stylesheet" href="../css/mobile-components.css">
    
    <!-- JavaScript文件 -->
    <script src="../../PC/js/jquery-3.2.1.min.js"></script>
    <script src="../../PC/js/coco-message.js"></script>
    <script src="../mobile-config.js"></script>
    <script src="../js/mobile-base.js"></script>
    <script src="../js/mobile-touch.js"></script>
    <script src="../js/mobile-nav.js"></script>
    
    <style>
        /* 红色书籍页面专用样式 */
        .redbooks-header {
            background: linear-gradient(135deg, #c00714 0%, #a00610 100%);
            color: white;
            padding: 20px 16px;
            margin-top: 56px;
        }
        
        .redbooks-title {
            font-size: 24px;
            font-weight: 600;
            margin-bottom: 8px;
            display: flex;
            align-items: center;
            gap: 12px;
        }
        
        .redbooks-subtitle {
            font-size: 14px;
            opacity: 0.9;
        }
        
        .search-section {
            background: white;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
            position: sticky;
            top: 56px;
            z-index: 100;
        }
        
        .search-input-group {
            display: flex;
            gap: 12px;
            align-items: center;
        }
        
        .search-input {
            flex: 1;
            padding: 12px 16px;
            border: 2px solid #f0f0f0;
            border-radius: 8px;
            font-size: 16px;
            background: #f8f9fa;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            border-color: #c00714;
            background: white;
            box-shadow: 0 0 0 3px rgba(192, 7, 20, 0.1);
        }
        
        .search-btn {
            background: #c00714;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: background 0.3s ease;
        }
        
        .search-btn:hover {
            background: #a00610;
        }
        
        .category-section {
            background: white;
            padding: 16px;
            border-bottom: 1px solid #f0f0f0;
        }
        
        .category-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 12px;
        }
        
        .category-scroll {
            display: flex;
            gap: 8px;
            overflow-x: auto;
            padding-bottom: 4px;
        }
        
        .category-scroll::-webkit-scrollbar {
            display: none;
        }
        
        .category-item {
            padding: 8px 16px;
            background: #f8f9fa;
            color: #666;
            border: 1px solid #e5e5e5;
            border-radius: 20px;
            font-size: 14px;
            white-space: nowrap;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .category-item.active {
            background: #c00714;
            color: white;
            border-color: #c00714;
        }
        
        .redbooks-content {
            padding: 16px;
        }
        
        .books-grid {
            display: grid;
            grid-template-columns: repeat(2, 1fr);
            gap: 16px;
        }
        
        .book-card {
            background: white;
            border-radius: 12px;
            overflow: hidden;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            transition: all 0.3s ease;
            text-decoration: none;
            color: inherit;
        }
        
        .book-card:active {
            transform: scale(0.98);
        }
        
        .book-cover {
            width: 100%;
            height: 160px;
            position: relative;
            overflow: hidden;
        }
        
        .book-cover img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        
        .book-overlay {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
            color: white;
            padding: 12px;
            transform: translateY(100%);
            transition: transform 0.3s ease;
        }
        
        .book-card:hover .book-overlay {
            transform: translateY(0);
        }
        
        .book-info {
            padding: 12px;
        }
        
        .book-title {
            font-size: 14px;
            font-weight: 600;
            color: #333;
            margin-bottom: 6px;
            line-height: 1.4;
            display: -webkit-box;
            -webkit-line-clamp: 2;
            -webkit-box-orient: vertical;
            overflow: hidden;
        }
        
        .book-author {
            font-size: 12px;
            color: #999;
            margin-bottom: 8px;
        }
        
        .book-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 11px;
            color: #999;
        }
        
        .book-category {
            background: rgba(192, 7, 20, 0.1);
            color: #c00714;
            padding: 2px 6px;
            border-radius: 8px;
            font-weight: 500;
        }
        
        .book-views {
            display: flex;
            align-items: center;
            gap: 4px;
        }
        
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            padding: 20px;
            gap: 8px;
        }
        
        .page-btn {
            min-width: 36px;
            height: 36px;
            border: 1px solid #e5e5e5;
            background: white;
            color: #666;
            border-radius: 6px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .page-btn:hover {
            border-color: #c00714;
            color: #c00714;
        }
        
        .page-btn.active {
            background: #c00714;
            color: white;
            border-color: #c00714;
        }
        
        .page-btn:disabled {
            opacity: 0.5;
            cursor: not-allowed;
        }
        
        .loading-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }
        
        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid #f3f3f3;
            border-top: 3px solid #c00714;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 16px;
        }
        
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: #999;
        }
        
        .empty-icon {
            width: 80px;
            height: 80px;
            margin: 0 auto 16px;
            opacity: 0.5;
        }
        
        .empty-title {
            font-size: 16px;
            margin-bottom: 8px;
            color: #666;
        }
        
        .empty-description {
            font-size: 14px;
            line-height: 1.5;
        }
        
        @media (max-width: 375px) {
            .redbooks-header {
                padding: 16px 12px;
            }
            
            .redbooks-content {
                padding: 12px;
            }
            
            .books-grid {
                grid-template-columns: 1fr;
                gap: 12px;
            }
            
            .book-cover {
                height: 200px;
            }
        }
    </style>
</head>
<body class="mobile-redbooks">
    <!-- 顶部导航栏 -->
    <header class="mobile-header">
        <div class="header-content">
            <button class="menu-btn" id="menuBtn">
                <span></span>
                <span></span>
                <span></span>
            </button>
            <div class="logo">
                <h2 style="margin: 0; font-size: 16px; color: #333;">红色书籍</h2>
            </div>
            <div class="header-actions">
                <div class="user-info" id="userInfo">
                    <button class="login-btn" id="loginBtn">登录</button>
                    <div class="user-avatar" id="userAvatar" style="display: none;">
                        <span id="userName"></span>
                    </div>
                </div>
            </div>
        </div>
    </header>

    <!-- 红色书籍头部 -->
    <section class="redbooks-header">
        <div class="redbooks-title">
            <svg viewBox="0 0 24 24" fill="currentColor" style="width: 28px; height: 28px;">
                <path d="M18 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V4C20 2.9 19.1 2 18 2ZM18 20H6V4H7V13L9.5 11.5L12 13V4H18V20Z"/>
            </svg>
            红色书籍
        </div>
        <div class="redbooks-subtitle">传承红色基因，汲取精神力量</div>
    </section>

    <!-- 搜索区域 -->
    <section class="search-section">
        <div class="search-input-group">
            <input type="text" class="search-input" id="searchInput" placeholder="搜索书籍标题或作者...">
            <button class="search-btn" onclick="searchBooks()">搜索</button>
        </div>
    </section>

    <!-- 分类区域 -->
    <section class="category-section">
        <div class="category-title">书籍分类</div>
        <div class="category-scroll" id="categoryScroll">
            <div class="category-item active" data-id="">全部</div>
            <!-- 分类将通过JavaScript动态加载 -->
        </div>
    </section>

    <!-- 书籍内容 -->
    <main class="redbooks-content">
        <div class="books-grid" id="booksGrid">
            <!-- 书籍列表将通过JavaScript动态加载 -->
        </div>
        
        <!-- 分页 -->
        <div class="pagination" id="pagination" style="display: none;">
            <!-- 分页按钮将通过JavaScript动态生成 -->
        </div>
    </main>

    <!-- 侧边菜单 -->
    <nav class="side-menu" id="sideMenu">
        <div class="menu-overlay" id="menuOverlay"></div>
        <div class="menu-content">
            <div class="menu-header">
                <div class="menu-logo">
                    <img src="../../PC/img/logo.png" alt="思政一体化平台">
                    <h3>思政一体化平台</h3>
                </div>
                <button class="menu-close" id="menuClose">×</button>
            </div>
            <div class="menu-items" id="menuItems">
                <!-- 菜单项将通过JavaScript动态加载 -->
            </div>
        </div>
    </nav>

    <!-- 底部导航 -->
    <nav class="bottom-nav">
        <a href="../index.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M10 20V14H14V20H19V12H22L12 3L2 12H5V20H10Z"/>
            </svg>
            <span>首页</span>
        </a>
        <a href="learning.html" class="nav-item active">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 3L1 9L12 15L21 10.09V17H23V9L12 3Z"/>
            </svg>
            <span>学习</span>
        </a>
        <a href="community.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M16 4C16.55 4 17 4.45 17 5V9.5L20.5 13L19 14.5L15.5 11H15V5C15 4.45 15.45 4 16 4Z"/>
            </svg>
            <span>社区</span>
        </a>
        <a href="profile.html" class="nav-item">
            <svg viewBox="0 0 24 24" fill="currentColor">
                <path d="M12 2C13.1 2 14 2.9 14 4C14 5.1 13.1 6 12 6C10.9 6 10 5.1 10 4C10 2.9 10.9 2 12 2Z"/>
            </svg>
            <span>我的</span>
        </a>
    </nav>

    <!-- 返回顶部按钮 -->
    <button class="back-to-top" id="backToTop">
        <svg viewBox="0 0 24 24" fill="currentColor">
            <path d="M7.41 15.41L12 10.83L16.59 15.41L18 14L12 8L6 14Z"/>
        </svg>
    </button>

    <script>
        let currentPage = 1;
        let totalPages = 0;
        let pageSize = 20;
        let selectedCategoryId = null;
        let isLoading = false;
        let searchKeyword = '';

        document.addEventListener('DOMContentLoaded', function() {
            // 初始化移动端功能
            initTouchInteractions();
            initNavigation();
            
            // 检查登录状态
            updateLoginUI();
            
            // 加载数据
            loadCategories();
            loadBooks();
        });

        function loadCategories() {
            $.ajax({
                url: baseurl + "/web/redbook/list",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: {
                    pageNum: 1,
                    pageSize: 999
                },
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        renderCategories(res.data.list || []);
                    }
                },
                error: (err) => {
                    console.error('加载分类失败:', err);
                }
            });
        }

        function renderCategories(categories) {
            const categoryScroll = document.getElementById('categoryScroll');
            let html = '<div class="category-item active" data-id="" onclick="selectCategory(this, null)">全部</div>';
            
            categories.forEach(category => {
                html += `<div class="category-item" data-id="${category.id}" onclick="selectCategory(this, '${category.id}')">${category.name}</div>`;
            });
            
            categoryScroll.innerHTML = html;
        }

        function selectCategory(element, categoryId) {
            // 更新选中状态
            document.querySelectorAll('.category-item').forEach(item => {
                item.classList.remove('active');
            });
            element.classList.add('active');
            
            // 更新筛选条件
            selectedCategoryId = categoryId;
            currentPage = 1;
            
            // 重新加载数据
            loadBooks();
        }

        function loadBooks() {
            if (isLoading) return;
            
            isLoading = true;
            const container = document.getElementById('booksGrid');
            
            if (currentPage === 1) {
                container.innerHTML = `
                    <div class="loading-state" style="grid-column: 1 / -1;">
                        <div class="loading-spinner"></div>
                        <p>加载中...</p>
                    </div>
                `;
            }
            
            const requestData = {
                pageNum: currentPage,
                pageSize: pageSize
            };
            
            if (selectedCategoryId) {
                requestData.redBookId = selectedCategoryId;
            }
            
            if (searchKeyword) {
                requestData.title = searchKeyword;
            }
            
            $.ajax({
                url: baseurl + "/web/posts",
                type: 'GET',
                contentType: "application/json",
                headers: {
                    "Authorization": sessionStorage.getItem("header")
                },
                data: requestData,
                dataType: 'json',
                success: (res) => {
                    if (res.code == '200') {
                        totalPages = res.data.pages;
                        renderBooks(res.data.list || []);
                        renderPagination();
                    } else {
                        renderEmptyState();
                    }
                    isLoading = false;
                },
                error: (err) => {
                    console.error('加载书籍失败:', err);
                    renderEmptyState();
                    isLoading = false;
                }
            });
        }

        function renderBooks(books) {
            const container = document.getElementById('booksGrid');
            
            if (!books || books.length === 0) {
                renderEmptyState();
                return;
            }
            
            let html = '';
            books.forEach(book => {
                const coverImage = book.thumbPath && book.thumbPath[0] ? 
                    baseurl + book.thumbPath[0] : 
                    '../../PC/img/book_default.jpg';
                
                html += `
                    <a href="redbook-detail.html?id=${book.id}" class="book-card" onclick="recordBookClick('${book.id}')">
                        <div class="book-cover">
                            <img src="${coverImage}" alt="${book.title}" onerror="this.src='../../PC/img/book_default.jpg'">
                            <div class="book-overlay">
                                <div style="font-size: 12px; opacity: 0.9;">${book.excerpt || '点击查看详情'}</div>
                            </div>
                        </div>
                        <div class="book-info">
                            <div class="book-title">${book.title}</div>
                            <div class="book-author">作者：${book.author || '未知'}</div>
                            <div class="book-meta">
                                <span class="book-category">${book.redBookName || '红色书籍'}</span>
                                <div class="book-views">
                                    <svg viewBox="0 0 24 24" fill="currentColor" style="width: 10px; height: 10px;">
                                        <path d="M12 4.5C7 4.5 2.73 7.61 1 12c1.73 4.39 6 7.5 11 7.5s9.27-3.11 11-7.5c-1.73-4.39-6-7.5-11-7.5zM12 17c-2.76 0-5-2.24-5-5s2.24-5 5-5 5 2.24 5 5-2.24 5-5 5zm0-8c-1.66 0-3 1.34-3 3s1.34 3 3 3 3-1.34 3-3-1.34-3-3-3z"/>
                                    </svg>
                                    <span>${book.clickCount || 0}</span>
                                </div>
                            </div>
                        </div>
                    </a>
                `;
            });
            
            container.innerHTML = html;
        }

        function renderPagination() {
            const pagination = document.getElementById('pagination');
            
            if (totalPages <= 1) {
                pagination.style.display = 'none';
                return;
            }
            
            pagination.style.display = 'flex';
            
            let html = '';
            
            // 首页按钮
            html += `<button class="page-btn" onclick="goToPage(1)" ${currentPage === 1 ? 'disabled' : ''}>首页</button>`;
            
            // 上一页按钮
            html += `<button class="page-btn" onclick="goToPage(${currentPage - 1})" ${currentPage === 1 ? 'disabled' : ''}>上一页</button>`;
            
            // 页码按钮
            const startPage = Math.max(1, currentPage - 2);
            const endPage = Math.min(totalPages, currentPage + 2);
            
            for (let i = startPage; i <= endPage; i++) {
                html += `<button class="page-btn ${i === currentPage ? 'active' : ''}" onclick="goToPage(${i})">${i}</button>`;
            }
            
            // 下一页按钮
            html += `<button class="page-btn" onclick="goToPage(${currentPage + 1})" ${currentPage === totalPages ? 'disabled' : ''}>下一页</button>`;
            
            // 尾页按钮
            html += `<button class="page-btn" onclick="goToPage(${totalPages})" ${currentPage === totalPages ? 'disabled' : ''}>尾页</button>`;
            
            pagination.innerHTML = html;
        }

        function goToPage(page) {
            if (page < 1 || page > totalPages || page === currentPage) return;
            
            currentPage = page;
            loadBooks();
        }

        function searchBooks() {
            searchKeyword = document.getElementById('searchInput').value.trim();
            currentPage = 1;
            loadBooks();
        }

        function recordBookClick(bookId) {
            // 记录点击数
            clicknum(bookId);
        }

        function renderEmptyState() {
            const container = document.getElementById('booksGrid');
            container.innerHTML = `
                <div class="empty-state" style="grid-column: 1 / -1;">
                    <svg class="empty-icon" viewBox="0 0 24 24" fill="currentColor">
                        <path d="M18 2H6C4.9 2 4 2.9 4 4V20C4 21.1 4.9 22 6 22H18C19.1 22 20 21.1 20 20V4C20 2.9 19.1 2 18 2ZM18 20H6V4H7V13L9.5 11.5L12 13V4H18V20Z"/>
                    </svg>
                    <div class="empty-title">暂无书籍</div>
                    <div class="empty-description">请尝试调整搜索条件或分类筛选</div>
                </div>
            `;
            isLoading = false;
        }

        // 搜索框回车事件
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                searchBooks();
            }
        });
    </script>
</body>
</html>
