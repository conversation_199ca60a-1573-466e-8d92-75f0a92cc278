import os
import re
import glob

def add_script_to_html_files():
    """
    在所有HTML文件的head标签中添加menu-modifier.js脚本引用
    """
    # 查找当前目录下所有的HTML文件
    html_files = glob.glob("*.html")
    
    # 统计修改的文件数量
    modified_count = 0
    
    for file_path in html_files:
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                content = file.read()
            
            # 检查是否已经包含了menu-modifier.js引用
            if 'menu-modifier.js' in content:
                print(f"跳过 {file_path} - 已经包含了menu-modifier.js引用")
                continue
            
            # 查找head标签，并在其后添加script标签
            head_pattern = re.compile(r'(<head[^>]*>)', re.IGNORECASE)
            if head_pattern.search(content):
                modified_content = head_pattern.sub(
                    r'\1\n\t',
                    content
                )
                
                # 写入修改后的内容
                with open(file_path, 'w', encoding='utf-8') as file:
                    file.write(modified_content)
                
                modified_count += 1
                print(f"修改成功: {file_path}")
            else:
                print(f"跳过 {file_path} - 未找到head标签")
        
        except Exception as e:
            print(f"处理 {file_path} 时出错: {str(e)}")
    
    print(f"\n总计修改了 {modified_count} 个HTML文件")

if __name__ == "__main__":
    add_script_to_html_files() 