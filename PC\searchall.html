<!DOCTYPE html>
<html>
	<head>
		<meta charset="utf-8" />
		<title>思政一体化平台-VR红色游学</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/searchall.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<style>
			/* 热词标签样式 */
			.gjcdiv label {
				display: inline-block;
				padding: 6px 12px;
				margin: 5px;
				border-radius: 15px;
				background: linear-gradient(145deg, #ffffff, #f0f0f0);
				box-shadow: 3px 3px 6px #d9d9d9, -3px -3px 6px #ffffff;
				cursor: pointer;
				transition: all 0.3s ease;
				position: relative;
				overflow: hidden;
				color: #666;
				border: 1px solid #eee;
			}

			.gjcdiv label:hover {
				transform: translateY(-2px);
				background: linear-gradient(145deg, #f0f0f0, #ffffff);
				color: #A65D57;
				border-color: #A65D57;
			}

			.gjcdiv label:active {
				transform: scale(0.95);
				box-shadow: 1px 1px 3px #d9d9d9, -1px -1px 3px #ffffff;
			}

			/* 标签出现动画 */
			@keyframes tagAppear {
				from {
					opacity: 0;
					transform: translateY(10px);
				}
				to {
					opacity: 1;
					transform: translateY(0);
				}
			}

			.gjcdiv label {
				animation: tagAppear 0.5s ease forwards;
				animation-delay: calc(var(--tag-index) * 0.1s);
				opacity: 0;
			}

			/* 波纹效果 */
			.gjcdiv label::after {
				content: '';
				position: absolute;
				top: 50%;
				left: 50%;
				width: 5px;
				height: 5px;
				background: rgba(166, 93, 87, 0.3);
				opacity: 0;
				border-radius: 100%;
				transform: scale(1, 1) translate(-50%);
				transform-origin: 50% 50%;
			}

			.gjcdiv label:focus:not(:active)::after {
				animation: ripple 1s ease-out;
			}

			@keyframes ripple {
				0% {
					transform: scale(0, 0);
					opacity: 0.5;
				}
				100% {
					transform: scale(20, 20);
					opacity: 0;
				}
			}
		</style>
	</head>
	<body class="index">
		<div class="tcbox" id="tcbox">
			
			<div class="topviewsss">
				<a onclick="closetc()">关闭</a>
				<label id="pdfname"></label>
			</div>
			<div class="panel-body pdfbox">
			    <a id="pdf" class="media" href=""></a>  
			</div>
		</div>
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="content">
			<div class="ssdiv">
				<div>
					<input id="searchall" placeholder="搜索内容" />
					<label onclick="ss()">搜索</label>
				</div>
			</div>
			<div class="gjcdiv">
				<span>热门搜索:</span>
				<div id="codelist">
				</div>
			</div>
			<!-- <div class="classbox" id="classlist">

			</div> -->
			<!-- <div class="bb">
				<div class="nrtoptile">
					<label>排序方式:</label>
					<span class="acac">默认排序</span>
					<span>发布时间</span>
					<span>观看量</span>
				</div>
			</div> -->
			<div class="strlist" id="sslist">

				<!-- <div class="item1">
					<div class="item1nr">这是一段新闻标题这是一段新闻标题这是一段新闻标题这是一段新闻标题这是一段新闻标题这是一段新闻标题</div>
					<div class="item1btn">
						<label>所属板块</label>
						<label><img id="uu" src="img/userno.png" />发布人员</label>
						<label><img id="ss" src="img/sjsq.png" />2022年3月9日 10:29:16</label>
						<label><img id="yy" src="img/yjsq.png" />999+</label>
					</div>
				</div>

				<div class="item2">
					<img class="item2img" src="img/zy.png" />
					<div class="item2right">
						<div class="item2righttop">
							<div class="item1nr">这是一段标题这是一段标题这是一段标题这是一段标题这是一段标题这是段标题这是一段标题这是一段标题这是一段标题</div>
							<div class="zjxk">
								XXXXXXXXXX学科 | 第一章 | 第三节
							</div>
						</div>
						<div class="item2rightbottom">
							<div class="item1btn">
								<label><img id="uu" src="img/userno.png" />发布人员</label>
								<label><img id="ss" src="img/sjsq.png" />2022年3月9日 10:29:16</label>
								<label><img id="yy" src="img/yjsq.png" />999+</label>
							</div>
						</div>
					</div>
				</div>


				<div class="item3">
					<img class="item3img" src="./img/txt.png" />
					<div class="item3right">
						<div class="item3righttop">
							<div class="item1nr">这是一段标题这是一段标题这是一段标题这是一段标题这是一段标题这是一段标题这是一段标题这是一段标题这是一段标题</div>
							<div class="zjxk">
								这是一段介绍文字这是一段介绍文字这是一段介绍文字这是一段介绍文字这是一段介绍文字这是一段介绍文字这是一段介绍文字这是一段介绍文字
							</div>
						</div>
						<div class="item3rightbottom">
							<div class="item1btn">
								<label><img id="uu" src="img/userno.png" />在线学习-红色书籍</label>
								<label><img id="ss" src="img/sjsq.png" />2022年3月9日 10:29:16</label>
								<label><img id="yy" src="img/yjsq.png" />999+</label>
							</div>
						</div>
					</div>
				</div> -->
			</div>
			<div class="fybox" id="fyq" style="display: none;">
				<span id="sy">首页</span>
				<span id="syy">上一页</span>
				<div class="num" id="num">
				</div>
				<span id="xyy">下一页</span>
				<span id="wy">尾页</span>
			</div>
		</div>

		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
					if (window.localStorage.getItem("jilu")) {
						$.ajax({
							url: baseurl + "/study/record/add",
							type: 'post',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							data: window.localStorage.getItem("jilu"),
							dataType: 'json',
							success: (res) => {
								window.localStorage.clear()
							}
						})
					}
				} else {
					//未登录 则显示登录按���
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
				}
				getcodelist() //获取关键词列表
				getclasslist()
				getclass()
				getfooterlink()
			})
			let classid = null

			function getclasslist() {
				$.ajax({
					url: baseurl + "/web/category/searchcategory",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ''
							res.data.map((item) => {
								html += '<div onclick="selectclass(this)" data-id="' + item.id + '">' + item
									.name + '</div>'
							})
							$("#classlist").html(html)
						}
					}
				})
			}

			function selectclass(item) { //选择分类
				classid = $(item).attr("data-id")
				let all = $("#classlist div")
				for (let i = 0; i < all.length; i++) {
					$(all[i]).attr('class', '')
				}
				$(item).attr('class', 'acccs')
				searchall($("#searchall").val())
			}

			function clears() {
				classid = null
				let all = $("#classlist div")
				for (let i = 0; i < all.length; i++) {
					$(all[i]).attr('class', '')
				}
			}

			function selectcode(e) {
				clears()
				$("#searchall").val($(e).html())
				searchall($(e).html())
			}

			function ss() {
				clears()
				searchall($("#searchall").val())
			}
			let pageindex = 1
			let pagesize = 10
			let pages = 0

			function searchall(code) {
				let json = {
					title: code,
					pageNum: pageindex,
					pageSize: pagesize,
					categoryid: classid
				}
				if (code) {
					$.ajax({
						url: baseurl + "/web/searchlist",
						type: 'GET',
						data: json,
						contentType: "application/json",
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						dataType: 'json',
						success: (res) => {
							if (res.code == '200') {
								console.log(res.data)
								if (res.data.list) {
									let html = ''
									res.data.list.map((item) => {
										if (item.type == '0') { //新闻
											html += '<div class="item1" onclick="incontent(this)" data-id="'+item.id+'" data-type="'+item.type+'">' +
												'<div class="item1nr">' + item.name + '</div>' +
												'<div class="item1btn">' +
												'<label>' + item.categoryName + '</label>' +
												'<label><img id="uu" src="img/userno.png" />' + item
												.creator + '</label>' +
												'<label><img id="ss" src="img/sjsq.png" />' + setDate(item
													.createdAt) + '</label>' +
												'<label><img id="yy" src="img/yjsq.png" />' + item.cilcked +
												'</label></div></div>'
										} else if (item.type == '1') { //在线学习
											html += '<div class="item2" onclick="incontent(this)" data-str="'+item.cmsResourcesCourseMeta.attachType+'" data-id="'+item.cmsResourcesCourseMeta.id+'" data-type="'+item.type+'">'
											
												html+='<img class="item2img" src="' + baseurl + item.cmsResourcesCourseMeta.coverPath +
												'" />' +
												'<div class="item2right">' +
												'<div class="item2righttop">' +
												'<div class="item1nr">' + item.name + '</div>' +
												'<div class="zjxk">'+item.project.name
												if(item.section){
													html+=' | '+item.section.name
												}
												html+='</div></div><div class="item2rightbottom">' +
												'<div class="item1btn"><label><img id="uu" src="img/userno.png" />' +
												item.creator + '</label>' +
												'<label><img id="ss" src="img/sjsq.png" />' + setDate(item
													.createdAt) + '</label>' +
												'<label><img id="yy" src="img/yjsq.png" />' + item.cilcked +
												'</label></div></div></div></div>'
										} else if (item.type == '2') { //红色书籍
											html += '<div class="item3" onclick="incontent(this)" data-id="'+item.id+'" data-type="'+item.type+'">' +
												'<img class="item3img" src="' + baseurl + item.attachment +
												'" />' +
												'<div class="item3right"><div class="item3righttop">' +
												'<div class="item1nr">' + item.name + '</div>' +
												'<div class="zjxk">' + item.description +
												'</div></div><div class="item3rightbottom"><div class="item1btn">' +
												'<label><img id="uu" src="img/userno.png" />' + item
												.categoryName + '</label>' +
												'<label><img id="ss" src="img/sjsq.png" />' + setDate(item
													.createdAt) + '</label>' +
												'<label><img id="yy" src="img/yjsq.png" />' + item.cilcked +
												'</label></div></div></div></div>'
										} else if (item.type == '3') { //主席足迹
											html += '<div class="item1" onclick="incontent(this)" data-id="'+item.id+'" data-type="'+item.type+'">' +
												'<div class="item1nr">' + item.name + '</div>' +
												'<div class="item1btn">' +
												'<label>' + item.categoryName + '</label>' +
												'<label><img id="uu" src="img/userno.png" />' + item
												.creator + '</label>' +
												'<label><img id="ss" src="img/sjsq.png" />' + setDate(item
													.createdAt) + '</label>' +
												'<label><img id="yy" src="img/yjsq.png" />' + item.cilcked +
												'</label></div></div>'
										}else if(item.type == '4'){//医德博物馆
											html += '<div class="item1" onclick="incontent(this)" data-id="'+item.id+'" data-type="'+item.type+'">' +
												'<div class="item1nr">' + item.name + '</div>' +
												'<div class="item1btn">' +
												'<label>' + item.categoryName + '</label>' +
												'<label><img id="uu" src="img/userno.png" />' + item
												.creator + '</label>' +
												'<label><img id="ss" src="img/sjsq.png" />' + setDate(item
													.createdAt) + '</label>' +
												'<label><img id="yy" src="img/yjsq.png" />' + item.cilcked +
												'</label></div></div>'
										}else if(item.type == '5'){//虚仿实验空间
											html += '<div class="item1" onclick="incontent(this)" data-id="'+item.id+'" data-type="'+item.type+'">' +
												'<div class="item1nr">' + item.name + '</div>' +
												'<div class="item1btn">' +
												'<label>' + item.categoryName + '</label>' +
												'<label><img id="uu" src="img/userno.png" />' + item
												.creator + '</label>' +
												'<label><img id="ss" src="img/sjsq.png" />' + setDate(item
													.createdAt) + '</label>' +
												'<label><img id="yy" src="img/yjsq.png" />' + item.cilcked +
												'</label></div></div>'
										}
									})
									$("#sslist").html(html)

									pages = res.data.pages
									if (pages > 1) {
										let numhtml = ""
										for (let a = 1; a <= pages; a++) {
											if (pageindex == a) {
												numhtml += '<label class="actinum" onclick="getnewlist(' + a +
													')">' + a +
													'</label>'
											} else {
												numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>'
											}
										}
										$("#sy").attr("onclick", "getnewlist(1)")
										$("#syy").attr("onclick", "getnewlist(1)")
										if (pageindex > 1) {
											$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")")
										}
										if (pageindex < pages) {
											$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")")
										} else {
											$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")")
										}
										$("#wy").attr("onclick", "getnewlist(" + pages + ")")
										$("#num").html(numhtml)
										$("#fyq").show()
									} else {
										$("#fyq").hide()
									}

								} else {
									$("#fyq").hide()
									$("#sslist").html('<div class="nodata">没有找到结果</div>')
								}
							}
						}
					})
				} else {
					cocoMessage.warning(1000, "请输入关键词！")
				}
			}
			function showpdf(pdf){
				$.ajax({
					url: baseurl + "/course/meta/"+$(pdf).attr("data-id"),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							console.log(res.data)
							$("#pdfname").html(res.data.cmsResourcesCourseMetaList[0].attachName)
							$("#tcbox").show()
							$("#pdf").attr("href",baseurl + res.data.cmsResourcesCourseMetaList[0].attachPath)
							$('a.media').media()
							time1 = Date.now()
							xkid = res.data.projectId
							infoid = $(pdf).attr("data-id")
						}
					}
				})
			}
			function closetc(){
				$("#tcbox").hide()
				this.tiaozhuan()
			}
			function incontent(item){
				let type = $(item).attr("data-type")
				let id = $(item).attr("data-id")
				if(type == '0'){//新闻
					window.location.href = 'newscontent.html?id='+id
				}else if(type == '1'){//在线学习
					let str = $(item).attr("data-str")
					if(str != 'pdf'){
						window.location.href = 'onlinelearning4.html?id='+id
					}else{
						console.log('显示pdf')
						showpdf(item)
					}
					//window.location.href = 'onlinelearning5.html?id='+id
				}else if(type == '2'){//红色书籍
					window.location.href = 'onlinelearning5.html?id='+id
				}else if(type == '3'){//主席足迹
					window.location.href = 'footprintcontent.html?id='+id
				}else if(type == '4'){//医德博物馆
					window.location.href = 'museumcontent.html?id='+id
				}else if(type == '5'){//虚拟仿真
					window.location.href = 'experiment.html?id='+id
				}
			}
			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是第最后一页了！")
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！")
					}
				} else {
					pageindex = index
					searchall($("#searchall").val())
				}
			}
			function getcodelist() {
				$.ajax({
					url: baseurl + "/web/category/searchcategory",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ''
							res.data.map((item, index) => {
								html += '<label style="--tag-index: ' + index + '" onclick="selectcode(this)">' + item.name + '</label>'
							})
							$("#codelist").html(html)
						}
					}
				})
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}



			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res.data[i]
			// 						.id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '-' + MM + '-' + d + ' ' + h + ':' + m + ':' + s
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
