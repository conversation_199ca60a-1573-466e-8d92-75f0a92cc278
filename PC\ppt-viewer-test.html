<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PPT查看器测试</title>
    
    <script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="./js/viewers/howler.min.js" type="text/javascript" charset="utf-8"></script>
    <script src="./js/viewers/file-viewer.js" type="text/javascript" charset="utf-8"></script>
    
    <style>
        body {
            font-family: 'Microsoft YaHei', Arial, sans-serif;
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #333;
            margin-bottom: 10px;
        }
        
        .header p {
            color: #666;
            font-size: 16px;
        }
        
        .preview-container {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            padding: 20px;
            margin-bottom: 30px;
            min-height: 500px;
        }
        
        .test-buttons {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-bottom: 20px;
        }
        
        .btn {
            padding: 10px 20px;
            background-color: #A65D57;
            color: white;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
            transition: background-color 0.3s;
        }
        
        .btn:hover {
            background-color: #8A4D47;
        }
        
        .debug-panel {
            background: #f0f0f0;
            border-radius: 8px;
            padding: 15px;
            font-family: monospace;
            margin-top: 20px;
            white-space: pre-wrap;
            max-height: 200px;
            overflow: auto;
            font-size: 13px;
        }
        
        @media (max-width: 768px) {
            .container {
                padding: 10px;
            }
            
            .preview-container {
                padding: 10px;
                min-height: 400px;
            }
            
            .btn {
                font-size: 13px;
                padding: 8px 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>PPT查看器测试</h1>
            <p>用于测试PPT查看器的全屏显示、触摸控制和图片加载功能</p>
        </div>
        
        <div class="test-buttons">
            <button class="btn" id="testPPT">测试PPT查看器</button>
            <button class="btn" id="testMockPPT">测试本地模拟PPT</button>
            <button class="btn" id="testFullscreen">测试全屏功能</button>
            <button class="btn" id="toggleDebug">开启调试模式</button>
        </div>
        
        <div class="preview-container" id="previewContainer">
            <div style="display:flex;align-items:center;justify-content:center;height:100%;color:#666;font-size:16px;">
                点击上方按钮开始测试
            </div>
        </div>
        
        <div class="debug-panel" id="debugPanel">
            === 调试信息 ===
        </div>
    </div>
    
    <script>
        // 记录调试信息
        function log(message) {
            const debugPanel = document.getElementById('debugPanel');
            const timestamp = new Date().toLocaleTimeString();
            debugPanel.textContent += `\n[${timestamp}] ${message}`;
            debugPanel.scrollTop = debugPanel.scrollHeight;
        }
        
        // 初始化文件查看器
        window.baseurl = ''; // 模拟baseurl
        
        // 初始化文件查看器
        window.addEventListener('DOMContentLoaded', function() {
            try {
                // 初始化文件查看器
                if (window.FileViewer) {
                    window.FileViewer.init({
                        containerSelector: '#previewContainer',
                        previewContainerSelector: '#previewContainer',
                        baseUrl: window.baseurl || ''
                    });
                    log('文件查看器初始化成功');
                } else {
                    log('错误: 未找到FileViewer对象');
                }
                
                // 测试PPT查看器按钮
                document.getElementById('testPPT').addEventListener('click', function() {
                    testPPTViewer();
                });
                
                // 测试模拟PPT按钮
                document.getElementById('testMockPPT').addEventListener('click', function() {
                    testMockPPTViewer();
                });
                
                // 测试全屏功能按钮
                document.getElementById('testFullscreen').addEventListener('click', function() {
                    testFullscreenFunction();
                });
                
                // 切换调试模式
                document.getElementById('toggleDebug').addEventListener('click', function() {
                    const url = new URL(window.location.href);
                    
                    if (url.searchParams.has('debug')) {
                        url.searchParams.delete('debug');
                        log('关闭调试模式');
                    } else {
                        url.searchParams.set('debug', '1');
                        log('开启调试模式');
                    }
                    
                    window.location.href = url.toString();
                });
            } catch (error) {
                log('初始化错误: ' + error.message);
            }
        });
        
        // 测试PPT查看器
        function testPPTViewer() {
            try {
                log('测试PPT查看器...');
                
                // 模拟PPT元数据
                const metadata = {
                    attachListPath: [
                        'https://picsum.photos/800/600?random=1',
                        'https://picsum.photos/800/600?random=2',
                        'https://picsum.photos/800/600?random=3',
                        'https://picsum.photos/800/600?random=4',
                        'https://picsum.photos/800/600?random=5'
                    ]
                };
                
                // 显示PPT
                window.FileViewer.display(
                    'https://example.com/test.pptx',
                    'pptx',
                    '测试演示文稿.pptx',
                    metadata
                );
                
                log('PPT查看器启动成功，加载5页随机图片');
            } catch (error) {
                log('测试PPT查看器失败: ' + error.message);
            }
        }
        
        // 测试模拟PPT查看器（使用Base64图像）
        function testMockPPTViewer() {
            try {
                log('测试模拟PPT查看器...');
                
                // 创建演示用的彩色图像（各种颜色的方块）
                const generateColorSlide = (color, text) => {
                    const canvas = document.createElement('canvas');
                    canvas.width = 800;
                    canvas.height = 600;
                    const ctx = canvas.getContext('2d');
                    
                    // 填充背景
                    ctx.fillStyle = color;
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    
                    // 添加文字
                    ctx.fillStyle = 'white';
                    ctx.font = 'bold 48px Arial';
                    ctx.textAlign = 'center';
                    ctx.textBaseline = 'middle';
                    ctx.fillText(text, canvas.width / 2, canvas.height / 2);
                    
                    // 添加页码
                    ctx.font = '24px Arial';
                    ctx.fillText('测试PPT查看器', canvas.width / 2, 50);
                    
                    return canvas.toDataURL('image/png');
                };
                
                // 创建模拟的PPT幻灯片
                const slides = [
                    generateColorSlide('#A65D57', '第1页 - 红色'),
                    generateColorSlide('#3D85C6', '第2页 - 蓝色'),
                    generateColorSlide('#6AA84F', '第3页 - 绿色'),
                    generateColorSlide('#E69138', '第4页 - 橙色'),
                    generateColorSlide('#8E7CC3', '第5页 - 紫色')
                ];
                
                // 模拟PPT元数据
                const metadata = {
                    attachListPath: slides
                };
                
                // 显示PPT
                window.FileViewer.display(
                    'data:,mockPPT',
                    'pptx',
                    '模拟演示文稿.pptx',
                    metadata
                );
                
                log('模拟PPT查看器启动成功，加载5页彩色幻灯片');
            } catch (error) {
                log('测试模拟PPT查看器失败: ' + error.message);
            }
        }
        
        // 测试全屏功能
        function testFullscreenFunction() {
            try {
                log('测试全屏功能...');
                
                // 首先加载一个测试PPT
                testMockPPTViewer();
                
                // 然后执行全屏功能
                setTimeout(() => {
                    const fullscreenBtn = document.getElementById('pptFullscreenBtn');
                    if (fullscreenBtn) {
                        log('找到全屏按钮，触发点击事件');
                        fullscreenBtn.click();
                    } else {
                        log('错误: 未找到全屏按钮(#pptFullscreenBtn)');
                        
                        // 详细检查页面中的按钮
                        const allButtons = document.querySelectorAll('button');
                        log(`页面中共有 ${allButtons.length} 个按钮`);
                        
                        // 尝试直接使用JS进入全屏
                        const pptContainer = document.getElementById('pptMainContainer');
                        if (pptContainer) {
                            log('尝试用JS直接进入全屏模式');
                            if (pptContainer.requestFullscreen) {
                                pptContainer.requestFullscreen();
                            } else if (pptContainer.mozRequestFullScreen) {
                                pptContainer.mozRequestFullScreen();
                            } else if (pptContainer.webkitRequestFullscreen) {
                                pptContainer.webkitRequestFullscreen();
                            } else if (pptContainer.msRequestFullscreen) {
                                pptContainer.msRequestFullscreen();
                            } else {
                                log('错误: 浏览器不支持全屏API');
                            }
                        } else {
                            log('错误: 未找到PPT容器(#pptMainContainer)');
                        }
                    }
                }, 1000);
            } catch (error) {
                log('测试全屏功能失败: ' + error.message);
            }
        }
        
        // 记录当前URL参数
        log('页面加载完成，URL: ' + window.location.href);
        
        // 检查是否有调试参数
        if (window.location.search.includes('debug')) {
            log('调试模式已开启');
        }
    </script>
</body>
</html> 