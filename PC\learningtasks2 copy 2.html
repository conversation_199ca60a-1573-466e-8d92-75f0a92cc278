<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-学习任务管理</title>
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<style>
			.iiiitem {
				margin-bottom: 0.520833rem;
			}
			.search-wrapper {
				position: relative;
				margin-bottom: 15px;
				padding: 0 10px;
				width: 20%; /* 设置为1/3宽度 */
				/* margin-left: auto;  */
				margin-right: 30px; /* 右边留出一些间距 */
			}
			.search-wrapper .search-input {
				width: 100%;
				height: 25px;
				padding: 0 25px 0 15px;
				border: 1px solid #e0e0e0;
				border-radius: 4px;
				background-color: #f5f5f5;
				font-size: 14px;
				color: #333;
				transition: all 0.3s ease;
			}
			.search-wrapper .search-input:focus {
				background-color: #fff;
				border-color: #c41e3a;
				box-shadow: 0 0 0 2px rgba(196, 30, 58, 0.1);
				outline: none;
			}
			.search-wrapper .search-icon {
				position: absolute;
				right: 20px;
				top: 50%;
				transform: translateY(-50%);
				width: 16px;
				height: 16px;
				pointer-events: none;
			}
			.search-wrapper .search-input::placeholder {
				color: #999;
			}
		</style>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a href="studentachievement.html" class="leftitem">学生成绩统计</a>
						<a href="studentlearning.html" class="leftitem">学习记录统计</a>
						<a href="userexamination2.html" class="leftitem">考试管理</a>
						<a class="leftitem activeleftitem">学习任务管理</a>
						<a href="achievements.html" class="leftitem">成果展示</a>
						<a href="releaseyourvoice2.html" class="leftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="userinfotopdiv">
						<a href="learningtasks.html">任务列表</a>
						<a class="acccccg">发布任务</a>
					</div>
					<div class="paperscroll">
						<div class="rwtitle">
							<div>任务标题<label class="rwbt">*</label></div>
							<input id="rwtitle" oninput="rwtitleinput()" maxlength="50" placeholder="请输入标题" />
							<label id="rwtitlestr">0/50</label>
						</div>

						<div class="rwtitle" style="margin-top: 2.083333rem;">
							<div>任务时间<label class="rwbt">*</label></div>
							<input id="startdate" style="width: 10.416667rem;" type="date" placeholder="请输入标题" />
							<span>~</span>
							<input id="enddate" style="width: 10.416667rem;" type="date" placeholder="请输入标题" />
						</div>

						<div class="rwtitle" style="margin-top: 2.083333rem;">
							<div>学科<label class="rwbt">*</label></div>
							<select id="xueke">
							</select>
						</div>

						<div class="rwtitle" style="margin-top: 2.083333rem;">
							<div>教学评价<label class="rwbt">*</label></div>
							<div class="checks">
								<div class="rwcc">
									<div onclick="selectpj(1,this)" id="shi"><label></label></div>是
								</div>
								<div class="rwcc">
									<div onclick="selectpj(0,this)" id="fou"></div>否
								</div>
							</div>
						</div>

						<div class="rwqx" style="margin-top: 2.083333rem;">
							<div class="rwqxtitle">任务权限<label class="rwbt" style="font-weight: normal;margin-left: 0.520833rem;">*</label></div>
							<div class="rwqxlist" style="position: relative;">
								<div class="search-wrapper">
									<input type="text" class="search-input" id="bjss" placeholder="搜索班级..." oninput="searchClass(this.value)"/>
									<img src="img/ss.png" class="search-icon" alt="搜索"/>
								</div>
								<!-- 学院列表 -->
								<div class="lianjibox">
									<div class="rbox">
										<div class="rname">学院<label class="rwbt">*</label></div>
										<div class="xybox scrollview1" id="xylist">

										</div>
									</div>
									<div class="rbox">
										<div class="rname">专业<label class="rwbt">*</label></div>
										<div class="zybox scrollview1" id="zylist">
										</div>
									</div>
									<div class="rbox">
										<div class="rname">班级<label class="rwbt">*</label></div>
										<div class="bjbox scrollview1" id="bjlist">
										</div>
									</div>
								</div>

								<div class="yxzbj" style="margin-top: 1.041667rem;">
									<div class="yxz">已选择班级： </div>
									<div id="yxz"></div>
								</div>
							</div>


							<div class="rwqx" style="margin-top: 2.083333rem;">
								<div class="rwqxtitle">学习任务总分<label class="rwbt"
										style="font-weight: normal;margin-left: 0.520833rem;">*</label></div>
								<div class="rwzfdiv">
									<input placeholder="请输入权重分值" id="allqzscro" />
								</div>
								<div class="rwqzview" style="justify-content: flex-start;">
									<div class="rwqzitem">
										<div>教学资源</div>
										<input oninput="scro1input(this)" id="scro1" type="number" placeholder="0%(权重分)" />
									</div>
									<!-- <div class="rwqzitem">
										<div>红色书籍</div>
										<input id="scro2" type="number" placeholder="0%(总分权重)" />
									</div>
									<div class="rwqzitem">
										<div>VR红色游学</div>
										<input id="scro3" type="number" placeholder="0%(总分权重)" />
									</div> -->
									<div class="rwqzitem" style="margin-left: 1.5625rem;">
										<div>试题资源</div>
										<input oninput="scro4input(this)" id="scro4" type="number" placeholder="0%(权重分)" />
									</div>
								</div>
								<div class="qzzzzzz">
									<div class="qzbox">
										<div class="qxl">100%</div>
										<div class="qzr"><img src="img/wh.png" />
											<div class="qzstr">
												这是一段文字文字这是一段文字文字这是一段文字文字这是一段文字文字这是一段文字文字这是一段文字文字这是一段文字文字这是一段文字文字</div>
										</div>
									</div>
								</div>

								<div class="kjzybox">
									<div class="kjzyitem">
										<div class="kjzyitemname">教学资源</div>
										<div class="kjzyitembox">
											<div class="kjzyitemboxl">资源</div>
											<div class="kjzyitemboxr">
												<div class="itemslist" id="additem1">
													<div class="iiiitem">
														<div class="newiitem">
															<div class="inputboxrw">
																<input id="kjss" class="input0" placeholder="选择资源" />
																<img onclick="kjsstap()" src="img/ss.png" />
															</div>
															<div class="newiitembox" id="kjzybox">
																<div class="closediv">
																	<img onclick="closessbox(this)"
																		src="img/closeicos.png" />
																</div>
																<div id="zyssbox" class="scrollview">
																	<div class="errmsg">请输入关键词搜索</div>
																</div>
															</div>
														</div>
														<!-- <input placeholder="请输入资源标题" class="input1" /><input
															class="input2" placeholder="请输入资源信息" /> -->
													</div>
												</div>
												<div class="yxtitle">已选资源:</div>
												<div class="yxkjzy" id="yxkjzybox">

												</div>
											</div>
										</div>
									</div>

									<div class="kjzyitem">
										<div class="kjzyitemname">红色书籍</div>
										<div class="kjzyitembox">
											<div class="kjzyitemboxl">资源</div>
											<div class="kjzyitemboxr">
												<div class="itemslist" id="additem2">
													<div class="iiiitem">
														<div class="newiitem">
															<div class="inputboxrw">
																<input id="hssjss" class="input0" placeholder="选择书籍" />
																<img onclick="hssjtap()" src="img/ss.png" />
															</div>
															<div class="newiitembox" id="hssjbox">
																<div class="closediv">
																	<img onclick="closessbox(this)"
																		src="img/closeicos.png" />
																</div>
																<div id="hssjssbox" class="scrollview">
																	<div class="errmsg">请输入关键词搜索</div>
																</div>
															</div>
														</div>
														<!-- <input placeholder="请输入资源标题" class="input1" /><input
															class="input2" placeholder="请输入资源信息" /> -->
													</div>
												</div>
												<div class="yxtitle">已选资源:</div>
												<div class="yxkjzy" id="yxhssjbox">

												</div>
												<!-- <div class="itemslist" id="additem2">
													<div class="iiiitem">
														<input class="input0" placeholder="选择资源" />
														<input placeholder="请输入资源标题" class="input1" /><input
															class="input2" placeholder="请输入资源信息" />
													</div>
												</div>
												<div class="addxxrw" onclick="additem(this)">
													+ 再添加一个资源
												</div> -->
											</div>
										</div>
									</div>

									<div class="kjzyitem">
										<div class="kjzyitemname">VR红色游学</div>
										<div class="kjzyitembox">
											<div class="kjzyitemboxl">资源</div>
											<div class="kjzyitemboxr">
												<div class="itemslist" id="additem3">
													<div class="iiiitem">
														<div class="newiitem">
															<div class="inputboxrw">
																<input id="vrss" class="input0" placeholder="选择VR" />
																<img onclick="vrtap()" src="img/ss.png" />
															</div>
															<div class="newiitembox" id="vrbox">
																<div class="closediv">
																	<img onclick="closessbox(this)"
																		src="img/closeicos.png" />
																</div>
																<div id="vrssbox" class="scrollview">
																	<div class="errmsg">请输入关键词搜索</div>
																</div>
															</div>
														</div>
														<!-- <input placeholder="请输入资源标题" class="input1" /><input
															class="input2" placeholder="请输入资源信息" /> -->
													</div>
												</div>
												<div class="yxtitle">已选资源:</div>
												<div class="yxkjzy" id="yxvrbox">

												</div>
												<!-- <div class="itemslist" id="additem3">
													<div class="iiiitem">
														<input class="input0" placeholder="选择资源" />
														<input placeholder="请输入资源标题" class="input1" /><input
															class="input2" placeholder="请输入资源信息" />
													</div>
												</div>
												<div class="addxxrw" onclick="additem(this)">
													+ 再添加一个资源
												</div> -->
											</div>
										</div>
									</div>

									<div class="kjzyitem">
										<div class="kjzyitemname">试题资源</div>
										<div class="kjzyitembox">
											<div class="kjzyitemboxl">资源</div>
											<div class="kjzyitemboxr">
												<div class="itemslist" id="additem4">
													<div class="iiiitem">
														<div class="newiitem">
															<div class="inputboxrw">
																<input id="stss" class="input0" placeholder="选择试题" />
																<img onclick="sttap()" src="img/ss.png" />
															</div>
															<div class="newiitembox" id="stbox">
																<div class="closediv">
																	<img onclick="closessbox(this)"
																		src="img/closeicos.png" />
																</div>
																<div id="stssbox" class="scrollview">
																	<div class="errmsg">请输入关键词搜索</div>
																</div>
															</div>
														</div>
														<!-- <input placeholder="请输入资源标题" class="input1" /><input
															class="input2" placeholder="请输入资源信息" /> -->
													</div>
												</div>
												<div class="yxtitle">已选资源:</div>
												<div class="yxkjzy" id="yxstbox">

												</div>
												<!-- <div class="itemslist" id="additem4">
													<div class="iiiitem">
														<input class="input0" placeholder="选择资源" />
														<input placeholder="请输入资源标题" class="input1" /><input
															class="input2" placeholder="请输入资源信息" />
													</div>
												</div>
												<div class="addxxrw" onclick="additem(this)">
													+ 再添加一个资源
												</div> -->
											</div>
										</div>
									</div>


								</div>

								<div class="rwbtnview">
									<div onclick="submit()">确认发布</div>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>


		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			//开始时间和结束时间分开  两个input    修改的时候  组卷界面重写  记录原有数据
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()

				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getfooterlink()
				getxuekelist()
				getxylist()
			})
			
			function scro1input(input){
				let val1 = $(input).val()
				let val4 = $('#scro4').val()
				
				$('#scro4').val(100 - parseInt(val1))
			}
			
			function scro4input(input){
				let val1 = $('#scro1').val()
				let val4 = $(input).val()
				
				$('#scro1').val(100 - parseInt(val4))
			}
		
			function closessbox(item) {
				$(item).parent().parent().hide()
			}

			function kjsstap() {
				if (!$("#kjss").val()) {
					$("#kjss").parent().siblings('.newiitembox').show()
					$("#zyssbox").html(`<div class="errmsg">请输入关键词搜索</div>`)
					return
				}
				$.ajax({
					url: baseurl + "/courseBytitle?title=" + $("#kjss").val(),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							if (res.data.length > 0) { //有数据
								let html = ''
								res.data.forEach((item) => {
									html +=
										`<div class="newzyitem" onclick="addkjzy(this)" title="${item.title}" data-type="${item.attachType}" data-id="${item.id}"><label class="itemtype ${item.attachType}">${item.attachType}</label>${item.title}</div>`
								})
								$("#zyssbox").html(html)
								$("#kjss").parent().siblings('.newiitembox').show()
							} else { //没有数据
								$("#kjss").parent().siblings('.newiitembox').show()
								$("#zyssbox").html(`<div class="errmsg">没有找到数据哦~</div>`)
							}
						}
					}
				})
			}
			//将选择的课件资源储存到json
			let showkjzylist = []
			//已选好的红色书籍
			let hssjyxlist = []
			//已选好的VR红色游学
			let vryxlist = []
			//已选好的试题
			let styxlist = []

			function addkjzy(item) {
				let ishave = 0
				showkjzylist.forEach((item2) => {
					if (item2.metaId == $(item).attr("data-id")) {
						ishave += 1
					}
				})
				if (ishave == 0) {
					showkjzylist.push({
						attachType: $(item).attr("data-type"),
						metaId: $(item).attr("data-id"),
						title: $(item).attr("title")
					})
					$("#kjzybox").hide()
					showyxkjzy()
				} else {
					cocoMessage.error(1000, "已添加了该资源！")
				}
			}

			function showyxkjzy() {
				let html = ''
				showkjzylist.forEach((item) => {
					html +=
						`<div class="yxkjzyitem"><label class="itemtype ${item.attachType}">${item.attachType}</label>${item.title}<img onclick="deleteyxkc(this)" data-id="${item.metaId}" src="./img/closered.png"/></div>`
				})
				$("#yxkjzybox").html(html)
			}

			function deleteyxkc(item) {
				let id = $(item).attr("data-id")
				let newlist = []
				showkjzylist.forEach((item) => {
					if (item.metaId != id) {
						newlist.push(item)
					}
				})
				showkjzylist = newlist
				showyxkjzy()
			}


			//红色书籍部分
			//搜索红色书籍
			function hssjtap() {
				if (!$("#hssjss").val()) {
					$("#hssjss").parent().siblings('.newiitembox').show()
					$("#hssjssbox").html(`<div class="errmsg">请输入关键词搜索</div>`)
					return
				}
				$.ajax({
					url: baseurl + "/postsbycategoryandtitle?categoryId=912354240784109568&title=" + $("#hssjss").val(),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							if (res.data.length > 0) { //有数据
								let html = ''
								res.data.forEach((item) => {
									html +=
										`<div class="newzyitem" onclick="addhssjlist(this)" title="${item.title}" data-url="${item.attachmentDtoList[0].attachmentPath}" data-id="${item.id}">${item.title}</div>`
								})
								$("#hssjssbox").html(html)
								$("#hssjss").parent().siblings('.newiitembox').show()
							} else { //没有数据
								$("#hssjss").parent().siblings('.newiitembox').show()
								$("#hssjssbox").html(`<div class="errmsg">没有找到数据哦~</div>`)
							}
						}
					}
				})
			}
			//将搜索到的红色书籍 添加到 hssjyxlist
			function addhssjlist(item) {
				let ishave = 0
				hssjyxlist.forEach((item2) => {
					if (item2.id == $(item).attr("data-id")) {
						ishave += 1
					}
				})
				if (ishave == 0) {
					hssjyxlist.push({
						url: $(item).attr("data-url"),
						id: $(item).attr("data-id"),
						title: $(item).attr("title")
					})
					$("#hssjbox").hide()
					showhssjxz()
				} else {
					cocoMessage.error(1000, "已添加了该资源！")
				}
			}
			//将已添加的红色书籍  显示出来
			function showhssjxz() {
				let html = ''
				hssjyxlist.forEach((item) => {
					html +=
						`<div class="yxkjzyitem">${item.title}<img onclick="deleteyxsj(this)" data-id="${item.id}" src="./img/closered.png"/></div>`
				})
				$("#yxhssjbox").html(html)
			}
			//删除选中的红色书籍
			function deleteyxsj(item) {
				let id = $(item).attr("data-id")
				let newlist = []
				hssjyxlist.forEach((item) => {
					if (item.id != id) {
						newlist.push(item)
					}
				})
				hssjyxlist = newlist
				showhssjxz()
			}


			//VR红色游学部分
			//搜索VR红色游学
			function vrtap() {
				if (!$("#vrss").val()) {
					$("#vrss").parent().siblings('.newiitembox').show()
					$("#vrssbox").html(`<div class="errmsg">请输入关键词搜索</div>`)
					return
				}
				$.ajax({
					url: baseurl + "/postsbycategoryandtitle?categoryId=912353959077875712&title=" + $("#vrss").val(),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							if (res.data.length > 0) { //有数据
								let html = ''
								res.data.forEach((item) => {
									html +=
										`<div class="newzyitem" onclick="addvrlist(this)" title="${item.title}" data-url="${item.redirectUrl}" data-id="${item.id}">${item.title}</div>`
								})
								$("#vrssbox").html(html)
								$("#vrss").parent().siblings('.newiitembox').show()
							} else { //没有数据
								$("#vrss").parent().siblings('.newiitembox').show()
								$("#vrssbox").html(`<div class="errmsg">没有找到数据哦~</div>`)
							}
						}
					}
				})
			}
			//将搜索到的VR红色游学 添加到 vryxlist
			function addvrlist(item) {
				let ishave = 0
				vryxlist.forEach((item2) => {
					if (item2.id == $(item).attr("data-id")) {
						ishave += 1
					}
				})
				if (ishave == 0) {
					vryxlist.push({
						url: $(item).attr("data-url"),
						id: $(item).attr("data-id"),
						title: $(item).attr("title")
					})
					$("#vrbox").hide()
					showvrxz()
				} else {
					cocoMessage.error(1000, "已添加了该资源！")
				}
			}
			//将已添加的VR  显示出来
			function showvrxz() {
				let html = ''
				vryxlist.forEach((item) => {
					html +=
						`<div class="yxkjzyitem">${item.title}<img onclick="deleteyxvr(this)" data-id="${item.id}" src="./img/closered.png"/></div>`
				})
				$("#yxvrbox").html(html)
			}
			//将选中的VR删除
			function deleteyxvr(item) {
				let id = $(item).attr("data-id")
				let newlist = []
				vryxlist.forEach((item) => {
					if (item.id != id) {
						newlist.push(item)
					}
				})
				vryxlist = newlist
				showvrxz()
			}

			//试题部分
			//根据模糊查询试题列表
			function sttap() {
				if (!$("#stss").val()) {
					$("#stss").parent().siblings('.newiitembox').show()
					$("#stssbox").html(`<div class="errmsg">请输入关键词搜索</div>`)
					return
				}
				$.ajax({
					url: baseurl + "/paper/paperall?name=" + $("#stss").val(),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							if (res.data.length > 0) { //有数据
								let html = ''
								res.data.forEach((item) => {
									html +=
										`<div class="newzyitem" onclick="addstlist(this)" title="${item.name}" data-id="${item.id}">${item.name}</div>`
								})
								$("#stssbox").html(html)
								$("#stss").parent().siblings('.newiitembox').show()
							} else { //没有数据
								$("#stss").parent().siblings('.newiitembox').show()
								$("#stssbox").html(`<div class="errmsg">没有找到数据哦~</div>`)
							}
						}
					}
				})
			}
			//将搜索到的试题添加到  styxlist
			function addstlist(item) {
				let ishave = 0
				styxlist.forEach((item2) => {
					if (item2.id == $(item).attr("data-id")) {
						ishave += 1
					}
				})
				if (ishave == 0) {
					styxlist.push({
						id: $(item).attr("data-id"),
						title: $(item).attr("title")
					})
					$("#stbox").hide()
					showstxz()
				} else {
					cocoMessage.error(1000, "已添加了该资源！")
				}
			}
			//将已选的试题显示出来
			function showstxz() {
				let html = ''
				styxlist.forEach((item) => {
					html +=
						`<div class="yxkjzyitem">${item.title}<img onclick="deleteyxst(this)" data-id="${item.id}" src="./img/closered.png"/></div>`
				})
				$("#yxstbox").html(html)
			}
			//删除已选的试题
			function deleteyxst(item) {
				let id = $(item).attr("data-id")
				let newlist = []
				styxlist.forEach((item) => {
					if (item.id != id) {
						newlist.push(item)
					}
				})
				styxlist = newlist
				showstxz()
			}






			let xuekelist = null

			function getxuekelist() {
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							xuekelist = res.data
							let xuekehtml = "<option value=0>全部学科</option>"
							xuekelist.map((item) => {
								xuekehtml += '<option value="' + item.id + '">' + item.name + '</option>'
							})
							$("#xueke").html(xuekehtml)
						}
					}
				})
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 						.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function rwtitleinput() {
				$("#rwtitlestr").html(`${$("#rwtitle").val().length}/50`)
			}
			let ispj = 1

			function selectpj(code, item) {
				if (code == 1) {
					ispj = 1
					$("#shi").html("<label></label>")
					$("#fou").html("")
				} else {
					ispj = 0
					$("#shi").html("")
					$("#fou").html("<label></label>")
				}
			}
			/**
			 * 统计选择的资源并组合成json
			 * */
			function jisuanallzy() {
				let submitalljson = []
				//课件
				showkjzylist.forEach((item) => {
					submitalljson.push({
						inforId: item.metaId,
						resourceType: 0,
						resourceName: item.title,
						resourceScore: 0
					})
				})
				//红色书籍
				hssjyxlist.forEach((item) => {
					submitalljson.push({
						inforId: item.id,
						resourceAddress: item.url,
						resourceType: 1,
						resourceName: item.title,
						resourceScore: 0
					})
				})
				//VR红色游学
				vryxlist.forEach((item) => {
					submitalljson.push({
						inforId: item.id,
						resourceAddress: item.url,
						resourceType: 2,
						resourceName: item.title,
						resourceScore: 0
					})
				})
				//试题
				styxlist.forEach((item) => {
					submitalljson.push({
						inforId: item.id,
						resourceType: 3,
						resourceName: item.title,
						resourceScore: 0
					})
				})
				return submitalljson
			}

			function edit() {
				sessionStorage.clear()
				$("#login").show()
				$("#user").hide()
				$("#edit").hide()
				cocoMessage.error(1000, "已退出登录！")
			}

			function submit() {
				let rwtitle = $("#rwtitle").val() //任务标题
				if (!rwtitle) {
					cocoMessage.warning(1000, "请输入标题！")
					return
				}
				let startdate = $("#startdate").val() //开始时间
				if (!startdate) {
					cocoMessage.warning(1000, "请输入开始时间！")
					return
				}
				let enddate = $("#enddate").val() //结束时间
				if (!enddate) {
					cocoMessage.warning(1000, "请输入结束时间！")
					return
				}
				let xkid = $("#xueke").val()
				if (xkid == '0') {
					cocoMessage.warning(1000, "请选择学科！")
					return
				}

				if (xzblist.length <= 0) {
					cocoMessage.warning(1000, "请选择任务权限！")
					return
				}

				if (!$('#allqzscro').val()) {
					cocoMessage.warning(1000, "请输入学习任务总分！")
					return
				}
				
				if (!$('#scro1').val()) {
					cocoMessage.warning(1000, "请输入教学资源权重分！")
					return
				}
				
				if (!$('#scro4').val()) {
					cocoMessage.warning(1000, "请输入试题资源权重分！")
					return
				}

				let allqzscro = $("#allqzscro").val() //学习任务总权重分

				let postJson = {
					tasksName: rwtitle,
					endTime: enddate,
					startTime: startdate,
					sectionId: xkid,
					isEvaluation: ispj,
					tasksAuthorityList: xzblist,
					tasksScore: allqzscro,
					learningTasksResourceRelationArrayList: jisuanallzy(),
					status: 0,
					resourcWeight: $('#scro1').val(),
					testWeight: $('#scro4').val(),
				}
				// console.log(JSON.stringify(postJson))
				$.ajax({
					url: baseurl + "/learning-tasks/add",
					type: 'POST',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: JSON.stringify(postJson),
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							cocoMessage.success(1000, "添加成功！")
							window.location.href = "learningtasks.html"
						} else {
							cocoMessage.error(1000, "添加失败！")
						}
					}
				})
			}
			let thisuserxydata = null
			/**
			 * 获取当前用户所带学院专业班级列表
			 * */
			function getxylist() {
				$.ajax({
					url: baseurl + "/binding/teacher-class",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							thisuserxydata = res.data
							let html = ''
							thisuserxydata.forEach((item) => {
								html +=
									`<div class="ljitem" onclick="selectxy(this)" data-xyid="${item.id}" data-id="0">${item.name}</div>`
							})
							$("#xylist").html(html)
						}
					}
				})
			}

			function selectxy(item) {
				let alllist = $("#xylist div")
				for (let i = 0; i < alllist.length; i++) {
					$(alllist[i]).attr('data-id', 0)
					$(alllist[i]).attr("class", "ljitem")
				}
				if ($(item).attr('data-id') == 0) {
					$(item).attr('data-id', 1)
					$(item).attr('class', 'ljitem gx')
				} else {
					$(item).attr('data-id', 0)
					$(item).attr('class', 'ljitem')
				}
				$("#bjlist").html('')
				showzy($(item).attr("data-xyid"))
			}

			function selectzy(item) {
				let alllist = $("#zylist div")
				for (let i = 0; i < alllist.length; i++) {
					$(alllist[i]).attr('data-id', 0)
					$(alllist[i]).attr("class", "ljitem")
				}
				if ($(item).attr('data-id') == 0) {
					$(item).attr('data-id', 1)
					$(item).attr('class', 'ljitem gx')
				} else {
					$(item).attr('data-id', 0)
					$(item).attr('class', 'ljitem')
				}
				$("#bjlist").html('')
				showbj($(item).attr("data-xyid"), $(item).attr("data-zyid"))
			}
			let xzblist = []

			function selectbj(item) {
				if ($(item).attr('data-id') == 0) {
					$(item).attr('data-id', 1)
					$(item).attr('class', 'ljitem gx1')
				} else {
					$(item).attr('data-id', 0)
					$(item).attr('class', 'ljitem')
				}

				thisuserxydata.forEach((itemn) => {
					if (itemn.id == $(item).attr("data-xyid")) {
						itemn.children.forEach((item2) => {
							if (item2.id == $(item).attr("data-zyid")) {
								item2.children.forEach((item3) => {
									if (item3.id == $(item).attr("data-bjid")) {
										if (item3.checkd) {
											item3.checkd = false
										} else {
											item3.checkd = true
										}
									}
								})
							}
						})
					}
				})

				jisuan()
			}

			function jisuan() { //计算已选
				let html = ''
				let newlist = []
				thisuserxydata.forEach((item) => {
					item.children.forEach((item2) => {
						item2.children.forEach((item3) => {
							if (item3.checkd) {
								html +=
									`<label>${item.name+'-'+item2.name+'-'+item3.name}<span onclick="deleteselect(${item.id},${item2.id},${item3.id})"></span></label>`
								newlist.push({
									collegeId: item.id,
									majorId: item2.id,
									classId: item3.id
								})
							}
						})
					})
				})
				xzblist = newlist
				$("#yxz").html(html)
			}

			function deleteselect(xyid, zyid, bjid) {
				$("#zylist").html('')
				$("#bjlist").html('')
				thisuserxydata.forEach((itemn) => {
					if (itemn.id == xyid) {
						itemn.children.forEach((item2) => {
							if (item2.id == zyid) {
								item2.children.forEach((item3) => {
									if (item3.id == bjid) {
										item3.checkd = false
									}
								})
							}
						})
					}
				})

				jisuan()
			}

			function showzy(id) {
				thisuserxydata.forEach((item) => {
					if (item.id == id) {
						let html = ``
						item.children.forEach((item2) => {
							html +=
								`<div class="ljitem" onclick="selectzy(this)" data-xyid="${item.id}" data-zyid="${item2.id}" data-id="0">${item2.name}</div>`
						})
						$("#zylist").html(html)
					}
				})
			}

			function showbj(xyid, zyid) {
				if ($("#bjss").val()) {
					return;
				}
				
				thisuserxydata.forEach((item) => {
					if (item.id == xyid) {
						item.children.forEach((item2) => {
							if (item2.id == zyid) {
								let html = ``;
								item2.children.forEach((item3) => {
									if (item3.checkd) {
										html += `<div class="ljitem gx1" onclick="selectbj(this)" data-xyid="${xyid}" data-zyid="${zyid}" data-bjid="${item3.id}" data-id="1">${item3.name}</div>`;
									} else {
										html += `<div class="ljitem" onclick="selectbj(this)" data-xyid="${xyid}" data-zyid="${zyid}" data-bjid="${item3.id}" data-id="0">${item3.name}</div>`;
									}
								});
								$("#bjlist").html(html);
							}
						});
					}
				});
			}

			function additem(item) { //教学资源添加
				$(item).siblings('.itemslist').append(`<div class="iiiitem">
													<input class="input1" placeholder="请输入资源标题" /><input class="input2" placeholder="请输入资源信息" />
												</div>`)
			}

			function searchClass(value) {
				// 清空专业和班级列表
				$("#zylist").html('');
				$("#bjlist").html('');
				
				if (!value) {
					// 如果搜索框为空，恢复原始数据显示
					getxylist();
					return;
				}
				
				// 重新请求数据并进行搜索
				$.ajax({
					url: 'http://192.168.100.31:5500/api/binding/teacher-class',
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// 过滤包含搜索关键字的班级
							let filteredData = res.data.map(college => {
								return {
									...college,
									children: college.children.map(major => {
										return {
											...major,
											children: major.children.filter(classItem => 
												classItem.name.toLowerCase().includes(value.toLowerCase())
											)
										};
									}).filter(major => major.children.length > 0)
								};
							}).filter(college => college.children.length > 0);
							
							// 更新显示
							let html = '';
							filteredData.forEach((college) => {
								college.children.forEach((major) => {
									major.children.forEach((classItem) => {
										html += `<div class="ljitem" onclick="selectbj(this)" 
													data-xyid="${college.id}" 
													data-zyid="${major.id}" 
													data-bjid="${classItem.id}" 
													data-id="0">
													${college.name} - ${major.name} - ${classItem.name}
												</div>`;
									});
								});
							});
							$("#bjlist").html(html);
						}
					}
				});
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
