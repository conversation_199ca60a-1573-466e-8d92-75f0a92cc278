<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-个人中心</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/userinfo.css" />
		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/wangEditor.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<!-- 导航增强脚本 -->
		<script src="./js/nav-enhancement.js" type="text/javascript" charset="utf-8"></script>
		<script src="https://unpkg.com/axios/dist/axios.min.js"></script>
		<!-- 添加outputLog函数以解决ReferenceError: outputLog is not defined错误 -->
		<script type="text/javascript">
			function outputLog(type, msg, isDoNotCheckOnce) {
				if (type === 'warn') {
					console.warn(msg);
				} else if (type === 'error') {
					console.error(msg);
				} else {
					console.log(msg);
				}
			}
		</script>
		<script src="./js/echarts.js" type="text/javascript" charset="utf-8"></script>
		<style>
			.num {
				color: #cecece !important;
			}

			.num .actinum {
				color: #FFFFFF !important;
			}

			.num label:hover {
				color: #FFFFFF !important;
			}

			/* 筛选列表样式 */
			.selectview {
				display: flex;
				align-items: center;
				flex-wrap: wrap;
				gap: 10px;
				padding: 15px;
				background: #f8f8f8;
				border-radius: 4px;
				margin: 0 0 20px 0;
			}

			.selectview select {
				flex: 1;
				min-width: 140px;
				max-width: 180px;
				height: 32px;
				padding: 0 8px;
				border: 1px solid #ddd;
				border-radius: 4px;
				background: #fff;
				color: #333;
				font-size: 14px;
				outline: none;
				cursor: pointer;
				transition: all 0.3s ease;
			}

			.selectview input {
				flex: 1.5;
				min-width: 160px;
				max-width: 200px;
				height: 32px;
				padding: 0 8px;
				border: 1px solid #ddd;
				border-radius: 4px;
				background: #fff;
				color: #333;
				font-size: 14px;
				outline: none;
				transition: all 0.3s ease;
			}

			/* 响应式布局 */
			@media screen and (max-width: 1200px) {
				.selectview {
					gap: 8px;
					padding: 12px;
				}
				
				.selectview select,
				.selectview input {
					min-width: 120px;
					max-width: 160px;
				}
			}

			@media screen and (max-width: 992px) {
				.selectview select,
				.selectview input {
					min-width: calc(33.33% - 8px);
					max-width: none;
				}
			}

			/* 保持其他样式不变 */
			.selectview select:hover,
			.selectview input:hover {
				border-color: #A65D57;
			}

			.selectview select:focus,
			.selectview input:focus {
				border-color: #A65D57;
				box-shadow: 0 0 0 2px rgba(166, 93, 87, 0.1);
			}

			.selectview input::placeholder {
				color: #999;
			}

			/* 标题样式 */
			.btbtbtbtbtbt {
				font-size: 20px;
				color: #333;
				font-weight: 500;
				margin: 20px 0 15px;
				padding-left: 0;
				border-left: none;
				height: 40px;
				line-height: 40px;
				text-align: center;
			}

			body {
				background: url(img/background.jpg) no-repeat;
				background-size: cover;
				background-attachment: fixed;
			}
			
			/* 现代化导航栏美化 - 与页面红色主题统一，红色偏橙色 */
			.boxleft {
				background: white;
				border-radius: 16px;
				box-shadow: 0 8px 32px rgba(220, 53, 69, 0.08);
				overflow: hidden;
				border: 1px solid rgba(231, 76, 60, 0.05);
				position: relative;
				backdrop-filter: blur(20px);
			}
			
			.boxleft::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				bottom: 0;
				background: linear-gradient(135deg, rgba(255, 255, 255, 0.9), rgba(248, 249, 250, 0.8));
				pointer-events: none;
				border-radius: 16px;
			}
			
			/* 导航栏顶部标题区域 - 红色偏橙色渐变 */
			.lefttopview {
				width: 100%;
				height: 65px;
				background: linear-gradient(135deg, #dc3545, #e74c3c, #ff6b35);
				background-size: 300% 300%;
				animation: gradientShift 6s ease infinite;
				display: flex;
				justify-content: center;
				align-items: center;
				font-size: 18px;
				color: #FFFFFF;
				font-weight: 700;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif;
				position: relative;
				box-shadow: 0 4px 20px rgba(220, 53, 69, 0.25);
				letter-spacing: 1.5px;
				border-radius: 16px 16px 0 0;
			}
			
			/* 渐变动画效果 */
			@keyframes gradientShift {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.lefttopview img {
				width: 24px;
				height: 24px;
				display: block;
				margin-right: 12px;
				filter: brightness(0) invert(1) drop-shadow(0 0 6px rgba(255, 255, 255, 0.4));
			}
			
			.lefttopview label {
				display: flex;
				align-items: center;
				text-shadow: 0 2px 6px rgba(0, 0, 0, 0.2);
			}
			
			/* 导航菜单容器 */
			.leftitembox {
				background: transparent;
				padding: 20px 0;
				position: relative;
				z-index: 2;
				min-height: 420px; /* 确保导航栏最小高度一致 */
			}
			
			.leftitembox::before {
				content: '';
				position: absolute;
				top: 0;
				left: 50%;
				transform: translateX(-50%);
				width: 85%;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(231, 76, 60, 0.15), transparent);
			}
			
			/* 导航菜单项 - 现代化设计与图标 */
			.leftitem {
				height: auto;
				padding: 16px 24px;
				display: flex;
				justify-content: flex-start;
				align-items: center;
				font-size: 15px;
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Microsoft YaHei', sans-serif;
				font-weight: 500;
				color: #4a5568;
				border-bottom: none;
				text-decoration: none;
				transition: color 0.2s ease, background-color 0.2s ease;
				position: relative;
				margin: 4px 16px;
				border-radius: 12px;
				background: transparent;
				overflow: hidden;
				min-height: 48px; /* 确保每个导航项高度一致 */
				box-sizing: border-box; /* 包含padding和border在高度计算中 */
			}
			
			/* 为不同菜单项添加对应的SVG图标 */
			.leftitem::before {
				content: '';
				width: 20px;
				height: 20px;
				margin-right: 12px;
				background-size: contain;
				background-repeat: no-repeat;
				background-position: center;
				opacity: 0.7;
				transition: opacity 0.2s ease;
				flex-shrink: 0;
			}
			
			/* 个人信息图标 */
			.leftitem[href*="userinfo"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 12c2.21 0 4-1.79 4-4s-1.79-4-4-4-4 1.79-4 4 1.79 4 4 4zm0 2c-2.67 0-8 1.34-8 4v2h16v-2c0-2.66-5.33-4-8-4z'/%3E%3C/svg%3E");
			}
			
			/* 学生成绩统计图标 */
			.leftitem[href*="studentachievement"]::before,
			.leftitem.activeleftitem::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%23ffffff' viewBox='0 0 24 24'%3E%3Cpath d='M16 6l2.29 2.29-4.88 4.88-4-4L2 16.59 3.41 18l6-6 4 4 6.3-6.29L22 12V6h-6z'/%3E%3C/svg%3E");
			}
			
			/* 学习记录统计图标 */
			.leftitem[href*="studentlearning"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
			}
			
			/* 考试管理图标 */
			.leftitem[href*="examination"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M14 2H6c-1.1 0-1.99.9-1.99 2L4 20c0 1.1.89 2 2 2h8c1.1 0 2-.9 2-2V8l-6-6zm2 16H8v-2h8v2zm0-4H8v-2h8v2zm-3-5V3.5L18.5 9H13z'/%3E%3C/svg%3E");
			}
			
			/* 学习任务管理图标 */
			.leftitem[href*="learning"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M19 3h-4.18C14.4 1.84 13.3 1 12 1c-1.3 0-2.4.84-2.82 2H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zm-7 0c.55 0 1 .45 1 1s-.45 1-1 1-1-.45-1-1 .45-1 1-1zm2 14H7v-2h7v2zm3-4H7v-2h10v2zm0-4H7V7h10v2z'/%3E%3C/svg%3E");
			}
			
			/* 学生任务图标 */
			.leftitem[href*="studentasks"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M16 1H4c-1.1 0-2 .9-2 2v14h2V3h12V1zm3 4H8c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h11c1.1 0 2-.9 2-2V7c0-1.1-.9-2-2-2zm-1 16H9V7h9v14z'/%3E%3C/svg%3E");
			}
			
			/* 学习路径图标 */
			.leftitem[href*="learningrecords"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M2 20h20v-4H2v4zm2-3h2v2H4v-2zM2 4v4h20V4H2zm4 3H4V5h2v2zm-4 7h20v-4H2v4zm2-3h2v2H4v-2z'/%3E%3C/svg%3E");
			}
			
			/* 考试成绩图标（注意是achievement.html不是achievements.html）*/
			.leftitem[href*="achievement.html"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm-2 15l-5-5 1.41-1.41L10 14.17l7.59-7.59L19 8l-9 9z'/%3E%3C/svg%3E");
			}
			
			/* 用户问题页面图标 */
			.leftitem[href*="userquestion"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 2C6.48 2 2 6.48 2 12s4.48 10 10 10 10-4.48 10-10S17.52 2 12 2zm1 17h-2v-2h2v2zm2.07-7.75l-.9.92C13.45 12.9 13 13.5 13 15h-2v-.5c0-1.1.45-2.1 1.17-2.83l1.24-1.26c.37-.36.59-.86.59-1.41 0-1.1-.9-2-2-2s-2 .9-2 2H8c0-2.21 1.79-4 4-4s4 1.79 4 4c0 .88-.36 1.68-.93 2.25z'/%3E%3C/svg%3E");
			}
			
			/* 修改密码图标 */
			.leftitem[href*="userpassword"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M18 8h-1V6c0-2.76-2.24-5-5-5S7 3.24 7 6v2H6c-1.1 0-2 .9-2 2v10c0 1.1.9 2 2 2h12c1.1 0 2-.9 2-2V10c0-1.1-.9-2-2-2zm-6 9c-1.1 0-2-.9-2-2s.9-2 2-2 2 .9 2 2-.9 2-2 2zm3.1-9H8.9V6c0-1.71 1.39-3.1 3.1-3.1 1.71 0 3.1 1.39 3.1 3.1v2z'/%3E%3C/svg%3E");
			}
			
			/* 成果展示图标 */
			.leftitem[href*="achievement"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z'/%3E%3C/svg%3E");
			}
			
			/* 发布心声图标 */
			.leftitem[href*="voice"]::before {
				background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' fill='%234a5568' viewBox='0 0 24 24'%3E%3Cpath d='M12 14c1.66 0 2.99-1.34 2.99-3L15 5c0-1.66-1.34-3-3-3S9 3.34 9 5v6c0 1.66 1.34 3 3 3zm5.3-3c0 3-2.54 5.1-5.3 5.1S6.7 14 6.7 11H5c0 3.41 2.72 6.23 6 6.72V21h2v-3.28c3.28-.48 6-3.3 6-6.72h-1.7z'/%3E%3C/svg%3E");
			}
			
			/* 菜单项悬浮态 - 完全无抖动 */
			.leftitem:hover {
				background: linear-gradient(135deg, rgba(220, 53, 69, 0.06), rgba(231, 76, 60, 0.08));
				color: #dc3545;
			}
			
			.leftitem:hover::before {
				opacity: 1;
			}
			
			/* 激活状态的菜单项 - 红色偏橙色渐变与白色图标 */
			.activeleftitem {
				font-weight: 700 !important;
				color: white !important;
				background: linear-gradient(135deg, #dc3545, #e74c3c, #ff6b35) !important;
				background-size: 300% 300%;
				animation: activeGradient 4s ease infinite;
				box-shadow: 0 8px 32px rgba(220, 53, 69, 0.3);
				border-radius: 12px;
			}
			
			@keyframes activeGradient {
				0% { background-position: 0% 50%; }
				33% { background-position: 100% 50%; }
				66% { background-position: 50% 100%; }
				100% { background-position: 0% 50%; }
			}
			
			.activeleftitem::before {
				opacity: 1 !important;
				filter: brightness(0) invert(1) drop-shadow(0 0 8px rgba(255, 255, 255, 0.5)) !important;
			}
			
			.activeleftitem::after {
				content: '';
				position: absolute;
				right: 16px;
				top: 50%;
				transform: translateY(-50%);
				width: 8px;
				height: 8px;
				background: white;
				border-radius: 50%;
				box-shadow: 0 0 12px rgba(255, 255, 255, 0.8);
				animation: activePulse 2s ease infinite;
			}
			
			@keyframes activePulse {
				0%, 100% { opacity: 1; transform: translateY(-50%) scale(1); }
				50% { opacity: 0.6; transform: translateY(-50%) scale(1.4); }
			}
			
			/* 添加精美的分隔线效果 */
			.leftitem:not(:last-child):not(.activeleftitem) {
				border-bottom: 1px solid rgba(231, 76, 60, 0.06);
				margin-bottom: 3px;
			}
			
			.leftitem:not(:last-child):not(.activeleftitem)::after {
				content: '';
				position: absolute;
				bottom: -1px;
				left: 40px;
				right: 24px;
				height: 1px;
				background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.15), transparent);
				opacity: 0.5;
			}
			
			/* 整体容器增强效果 */
			.contentview .boxleft {
				background: rgba(255, 255, 255, 0.95);
				border-radius: 16px;
				box-shadow: 0 8px 40px rgba(220, 53, 69, 0.08);
				border: 1px solid rgba(231, 76, 60, 0.05);
				overflow: hidden;
				position: relative;
				backdrop-filter: blur(20px);
			}
			
			.contentview .boxleft::before {
				content: '';
				position: absolute;
				top: 0;
				left: 0;
				right: 0;
				height: 3px;
				background: linear-gradient(90deg, #dc3545, #e74c3c, #ff6b35);
				opacity: 0;
				transition: opacity 0.4s ease;
			}
			
			.contentview .boxleft:hover::before {
				opacity: 1;
			}
			
			/* 响应式优化 */
			@media (max-width: 768px) {
				.boxleft {
					border-radius: 12px;
					margin-bottom: 20px;
				}
				
				.lefttopview {
					height: 55px;
					font-size: 16px;
					letter-spacing: 1px;
				}
				
				.lefttopview img {
					width: 20px;
					height: 20px;
					margin-right: 10px;
				}
				
				.leftitem {
					padding: 14px 20px;
					font-size: 14px;
					margin: 3px 12px;
					min-height: 44px;
				}
				
				.leftitem::before {
					width: 18px;
					height: 18px;
					margin-right: 10px;
				}
			}
			
			/* 字体优化 - 思源黑体 */
			.lefttopview,
			.leftitem {
				font-family: 'Source Han Sans CN', 'Source Han Sans SC', 'Source Han Sans', 'Noto Sans CJK SC', 'Microsoft YaHei UI', 'Microsoft YaHei', '微软雅黑', sans-serif;
			}
			
			/* 增强的交互反馈 */
			.leftitem {
				cursor: pointer;
			}
			
			.leftitem:active {
				transform: scale(0.98);
				transition: all 0.1s ease;
			}
			
			.activeleftitem:active {
				transform: scale(1.02);
			}
			
			/* 为菜单项添加微妙的进入动画 */
			.leftitem {
				animation: slideIn 0.5s ease-out forwards;
				opacity: 0;
				transform: translateX(-20px);
			}
			
			.leftitem:nth-child(1) { animation-delay: 0.1s; }
			.leftitem:nth-child(2) { animation-delay: 0.2s; }
			.leftitem:nth-child(3) { animation-delay: 0.3s; }
			.leftitem:nth-child(4) { animation-delay: 0.4s; }
			.leftitem:nth-child(5) { animation-delay: 0.5s; }
			.leftitem:nth-child(6) { animation-delay: 0.6s; }
			.leftitem:nth-child(7) { animation-delay: 0.7s; }
			
			@keyframes slideIn {
				to {
					opacity: 1;
					transform: translateX(0);
				}
			}
		</style>
	</head>
	<body class="index">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="box">
			<div class="contentview">
				<div class="boxleft">
					<div class="lefttopview">
						<label><img src="img/bbbb.png" />个人中心</label>
					</div>
					<div class="leftitembox" id="teambox">
						<a href="userinfo.html" class="leftitem">个人信息</a>
						<a class="leftitem activeleftitem">学生成绩统计</a>
						<a href="studentlearning.html" class="leftitem">学习记录统计</a>
						<a href="userexamination2.html" class="leftitem">考试管理</a>
						<a href="learningtasks.html" class="leftitem">学习任务管理</a>
						<a href="achievements.html" class="leftitem">成果展示</a>
						<a href="releaseyourvoice2.html" class="leftitem">发布心声</a>
					</div>
				</div>
				<div class="boxright">
					<div class="righttopview2">
						<label class="accccc">学生成绩统计分析</label>
					</div>
					<div class="btbtbtbtbt">学生成绩数据统计图</div>
					<div class="selectview">
						<select id="xuekeselect1" onchange="xuekechange()">
							<option value="0">请选择学科</option>
						</select>
						<input id="stinput1" oninput="getnumlist()" placeholder="请输入试题名称" />
						<select id="years" onchange="yearschange(this)">
						</select>
						<select id="xyselect" onchange="xychange(this)">
						</select>
						<select id="zyselect" onchange="zychange(this)">
						</select>
						<select id="bjselect" onchange="bjchange(this)">
						</select>
					</div>

					<div class="tjt">
						<div id="msg1"
							style="text-align: center;font-size: 0.8333rem;color: #999999;padding-top: 8rem;">请先选择学科
						</div>
						<div id="main" style="display: none;height: 400px;"></div>
					</div>
					<div class="btbtbtbtbt2">学生成绩信息列表<div class="sjjxbtn" onclick="daochu()">Excel导出</div>
					</div>
					<div class="data-filter-note" style="text-align: center; color: #666; font-size: 14px; margin: 5px 0 15px; background: #f8f8f8; padding: 8px; border-radius: 4px;">
						注：列表中仅显示每个学生的最高成绩记录，Excel导出也将仅导出最高成绩数据
					</div>
					<div class="selectbox">
						<div class="selectview nono" style="flex-wrap: wrap;justify-content: flex-start;">
							<select id="xuekeselect2" onchange="getlist()"></select>
							<select id="sjselect2" onchange="getssnewlist()">
							</select>
							<select id="xyselect2" onchange="xychange2(this)">
							</select>
							<select id="zyselect2" onchange="zychange2(this)">
							</select>
							<select style="margin-right: 0;" id="bjselect2" onchange="bjchange2(this)">
							</select>

							<!-- <select id="user1" onchange="user1change(this)">
							</select>
							<select id="user2" onchange="user2change(this)">
							</select> -->

							<div style="width: 100%;margin-top: 0.2604rem;">
								<input id="userxh" oninput="getlist()" placeholder="请输入学号" />
								<input id="userxm" oninput="getlist()" style="margin-left: 0.5208rem" placeholder="请输入姓名" />
							</div>
						</div>

					</div>
					<div class="tabview">
						<div class="tabletopview" id="showpapername">全部试卷</div>
						<div class="tabletoptt">
							<div>序号</div>
							<div>学院</div>
							<div>专业</div>
							<div>班级</div>
							<div>学号</div>
							<div>姓名</div>
							<div>主观题</div>
							<div>客观题</div>
							<div>总成绩</div>
							<div>权重</div>
						</div>
						<div id="paperlist">

						</div>
						<div class="tfyq">
							<div class="fybox" id="fyq">
								<span id="sy">首页</span>
								<span id="syy">上一页</span>
								<div class="num" id="num">
								</div>
								<span id="xyy">下一页</span>
								<span id="wy">尾页</span>
							</div>
						</div>
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			let userinfo = sessionStorage.getItem("userinfo")

			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
				getclass()
				getfooterlink()
				//getnumlist()
				getlist()
				getxueyuan()
				getpaperallyears()
				getyearspaper()
				getallxueke()
				
				// 获取最近一次任务数据
				getLatestTaskData();
			})

			function getallxueke() {
				$.ajax({
					url: baseurl + "/project/section/list/tree",
					type: 'get',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						let html = '<option value="0">请选择学科</option>'
						res.data.forEach((item) => {
							html += `<option value="${item.id}">${item.name}</option>`
						})
						$("#xuekeselect1").html(html)
						$("#xuekeselect2").html(html)
					}
				})
			}

			function daochu() {
				// 如果已有筛选后的学生数据，使用前端筛选好的数据导出
				if (allFilteredStudentData && allFilteredStudentData.length > 0) {
					// 准备导出的数据
					cocoMessage.info(1000, "正在准备导出数据...");
					
					// 从筛选后的数据中提取必要的信息构建CSV数据
					let csvData = [];
					// 添加表头
					csvData.push(['序号', '学院', '专业', '班级', '学号', '姓名', '主观题', '客观题', '总成绩', '权重']);
					
					// 添加数据行
					allFilteredStudentData.forEach((item, index) => {
						csvData.push([
							index + 1,
							item.student != null ? item.student.collegeName : "",
							item.student != null ? item.student.majorName : "",
							item.student != null ? item.student.className1 : "",
							item.student != null ? item.student.userAuth.identifier : "",
							item.student != null ? item.student.name : "",
							parseInt(item.zhuguanScore),
							parseInt(item.keguanTotalScore),
							parseInt(item.score),
							item.cmsTestPaper.theWeight
						]);
					});
					
					// 将数据转换为CSV格式
					let csvContent = csvData.map(e => e.join(',')).join('\n');
					
					// 创建Blob对象
					let blob = new Blob(["\uFEFF" + csvContent], { type: 'text/csv;charset=utf-8;' });
					let url = window.URL.createObjectURL(blob);
					
					// 创建下载链接并触发点击
					let link = document.createElement('a');
					link.href = url;
					link.setAttribute('download', "学生成绩信息.csv");
					link.style.display = 'none';
					document.body.appendChild(link);
					link.click();
					document.body.removeChild(link);
					
					cocoMessage.success(1000, "导出成功！");
				} else {
					// 如果没有筛选数据（可能是页面刚加载完），先获取数据后再导出
					let dcxyid = "";
					if ($("#xyselect2").val() != '0') {
						dcxyid = $("#xyselect2").val();
					}
					let dczyid = "";
					if ($("#zyselect2").val() != "0") {
						dczyid = $("#zyselect2").val();
					}
					let dcbjid = "";
					if ($("#bjselect2").val() != "0") {
						dcbjid = $("#bjselect2").val();
					}
					let dcsjid = "";
					if ($("#sjselect2").val() != "0") {
						dcsjid = $("#sjselect2").val();
					}
					let xkidss = "";
					if($("#xuekeselect2").val() != '0'){
						xkidss = $("#xuekeselect2").val();
					}
					
					// 显示加载提示
					cocoMessage.loading(1000, "正在获取数据...");
					
					// 获取所有成绩数据
					$.ajax({
						url: baseurl + "/paper/answered",
						type: 'GET',
						contentType: "application/json",
						data: {
							subjectId: xkidss, //学科
							name: $("#userxm").val(), //学生姓名
							pageNum: 1,
							pageSize: 2000, // 使用较大值确保获取所有数据
							isMark: 1,
							collegeId: dcxyid,
							majorId: dczyid,
							classId: dcbjid,
							identifier: $("#userxh").val(), //学号
							paperId: dcsjid,
							taskId: latestTaskId // 添加任务ID参数
						},
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						dataType: 'json',
						success: (res) => {
							if (res.code == '200' && res.data && res.data.list) {
								// 处理数据，只保留每个学号的最高分记录
								let studentMap = new Map();
								
								res.data.list.forEach(item => {
									if (item.student && item.student.userAuth && item.student.userAuth.identifier) {
										const studentId = item.student.userAuth.identifier;
										const score = parseFloat(item.score) || 0;
										
										// 如果学号不存在于Map中或者新分数更高，则更新Map
										if (!studentMap.has(studentId) || score > studentMap.get(studentId).score) {
											studentMap.set(studentId, item);
										}
									}
								});
								
								// 将Map转回数组
								const filteredList = Array.from(studentMap.values());
								
								// 准备导出数据
								let csvData = [];
								// 添加表头
								csvData.push(['序号', '学院', '专业', '班级', '学号', '姓名', '主观题', '客观题', '总成绩', '权重']);
								
								// 添加数据行
								filteredList.forEach((item, index) => {
									csvData.push([
										index + 1,
										item.student != null ? item.student.collegeName : "",
										item.student != null ? item.student.majorName : "",
										item.student != null ? item.student.className1 : "",
										item.student != null ? item.student.userAuth.identifier : "",
										item.student != null ? item.student.name : "",
										parseInt(item.zhuguanScore),
										parseInt(item.keguanTotalScore),
										parseInt(item.score),
										item.cmsTestPaper.theWeight
									]);
								});
								
								// 将数据转换为CSV格式
								let csvContent = csvData.map(e => e.join(',')).join('\n');
								
								// 创建Blob对象
								let blob = new Blob(["\uFEFF" + csvContent], { type: 'text/csv;charset=utf-8;' });
								let url = window.URL.createObjectURL(blob);
								
								// 创建下载链接并触发点击
								let link = document.createElement('a');
								link.href = url;
								link.setAttribute('download', "学生成绩信息.csv");
								link.style.display = 'none';
								document.body.appendChild(link);
								link.click();
								document.body.removeChild(link);
								
								cocoMessage.success(1000, "导出成功！");
							} else {
								cocoMessage.error(1000, "导出失败，没有获取到有效数据！");
							}
						},
						error: () => {
							cocoMessage.error(1000, "导出失败，请稍后重试！");
						}
					});
				}
			}
			let collegeId = null //学院
			let majorId = null //专业
			let classId = null //班级

			let collegeId2 = null //学院2
			let majorId2 = null //专业2
			let classId2 = null //班级2
			let userNo = null //学号
			let ppid = null //试卷ID

			let time1 = null //开始时间
			let time2 = null //结束时间

			let xueyuanlist = null //学院专业班级数据
			function user1change(iii) {
				let xh = $(iii).val()
				$("#user2").val(xh)
				userNo = xh

				pageindex = 1
				getlist()
			}

			function user2change(iii) {
				let xh = $(iii).val()
				$("#user1").val(xh)
				userNo = xh

				pageindex = 1
				getlist()
			}

			function getstudentbyclassid(classid) {
				if (classid) {
					$.ajax({
						url: baseurl + "/student/ClaassStudentlist",
						type: 'GET',
						contentType: "application/json",
						data: {
							className: classid
						},
						headers: {
							"Authorization": sessionStorage.getItem("header")
						},
						dataType: 'json',
						success: (res) => {
							if (res.code == '200') {
								// console.log(res.data)
								let html = '<option value="0">请选择学号</option>'
								let html2 = '<option value="0">请选择姓名</option>'
								res.data.forEach((item) => {
									html += '<option value="' + item.identifier + '">' + item.identifier +
										'</option>'
									html2 += '<option value="' + item.identifier + '">' + item.name +
										'</option>'
								})
								$("#user1").html(html)
								$("#user2").html(html2)
							}
						}
					})
				} else {
					$("#user1").html('<option value="0">请选择学号</option>')
					$("#user2").html('<option value="0">请选择姓名</option>')
				}

			}
			let allpapersssss = null

			function getyearspaper() {
				$.ajax({
					url: baseurl + "/paper/listNowYear",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// console.log(res.data)
							allpapersssss = res.data
							let html = '<option value="0">全部试卷</option>'
							res.data.forEach((item) => {
								html += '<option value="' + item.id + '">' + item.name + '</option>'
							})
							$("#sjselect2").html(html)
						}
					}
				})
			}

			function getpaperallyears() { //获取所有年份
				let date = new Date()
				let nowyears = date.getFullYear()
				let smallYears = nowyears - 5
				let Years = nowyears - smallYears
				let array = []
				for (let i = 0; i <= Years; i++) {
					let yy = nowyears--

					array.push(yy + '-下学期')
					array.push(yy + '-上学期')
				}
				let yearstr = '<option value="0">全部年份</option>'
				array.map((item) => {
					yearstr += '<option value="' + item + '">' + item + '</option>'
				})
				$("#years").html(yearstr)
			}

			function yearschange(select) {
				let str = $(select).val()
				let strs = str.split('-')
				if (str == '0') {
					time1 = null
					time2 = null
				} else {
					if (strs[1] == '上学期') {
						//3-1  8-1
						time1 = strs[0] + '-3-2'
						time2 = strs[0] + '-8-1'
					} else {
						// 9- 1  2-29
						time1 = strs[0] + '-9-1'
						time2 = (parseInt(strs[0]) + 1) + '-3-1'
					}
				}

				getnumlist()
			}

			function getxueyuan() { //获取学院专业班级  学科树
				$.ajax({
					url: baseurl + "/college/major",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							xueyuanlist = res.data
							// console.log(xueyuanlist)
							let xyhtml = '<option value="0">请选择学院</option>'
							xueyuanlist.forEach((item) => {
								xyhtml += '<option value="' + item.id + '">' + item.name + '</option>'
							})
							$("#xyselect").html(xyhtml)
							$("#xyselect2").html(xyhtml)
							fleshxueyuan()
							fleshxueyuan2()
						}
					}
				})
			}

			function xychange(xy) { //当学院的list选择时
				if ($(xy).val() == '0') { //当学院选择了空 则 专业班级为空
					collegeId = null
					majorId = null
					classId = null
				} else { //当选择了有值学院则 刷新list  专业和班级为空
						collegeId = $(xy).val()
						majorId = null
						classId = null
				}
				getnumlist()
				fleshxueyuan()
			}

			function xychange2(xy) {
				if ($(xy).val() == '0') { //当学院选择了空 则 专业班级都为空
					collegeId2 = null
					majorId2 = null
					classId2 = null
				} else { //当选择了有值学院则 刷新list  专业和班级为空
					collegeId2 = $(xy).val()
					majorId2 = null
					classId2 = null
				}

				fleshxueyuan2()
			}

			function zychange(zy) { //当专业的list选择时
				if ($(zy).val() == '0') { //当专业选择了空 则 班级为空
					majorId = null
					classId = null
				} else { //当选择了有值专业则 刷新list  班级为空
					majorId = $(zy).val()
					classId = null
				}
				getnumlist()
				fleshxueyuan()
			}

			function zychange2(zy) { //当专业的list选择时
				if ($(zy).val() == '0') { //当专业选择了空 则 班级为空
					majorId2 = null
					classId2 = null
				} else { //选择了有值专业则 刷新list  班级为空
					majorId2 = $(zy).val()
					classId2 = null
				}
				fleshxueyuan2()
			}

			function bjchange(bj) { //当班级选择时
				if ($(bj).val() == '0') {
					classId = null
				} else {
					classId = $(bj).val()
				}
				getnumlist()
			}

			function bjchange2(bj) { //当班级选择时
				if ($(bj).val() == '0') {
					classId2 = null
				} else {
					classId2 = $(bj).val()
				}
				fleshxueyuan2()
				getstudentbyclassid(classId2)
			}

			function fleshxueyuan() { //刷新学院 专业班级数据
				if (collegeId) {
					//有学院id
					if (majorId) {
						//有学院ID  有专业ID 则 循环班级的list
						let bjhtml = '<option value="0">请选择班级</option>'
						xueyuanlist.forEach((item) => { //循环学院
							if (item.id == collegeId) { //判断学院ID
								item.children.forEach((item1) => { //循环专业
									if (item1.id == majorId) { //判断专业ID
										item1.children.forEach((item2) => {
											bjhtml += '<option value="' + item2.id + '">' + item2.name +
												'</option>'
										})
									}
								})
							}
						})
						$("#bjselect").html(bjhtml)
					} else {
						//有学院ID 没有专业ID  则班级全部是空  循环专业list
						classId = null //班级ID 都为空 select为空
						$("#bjselect").html('<option value="0">请选择班级</option>')
						//循环班级的list
						let zyhtml = '<option value="0">请选择专业</option>'
						xueyuanlist.forEach((item) => {
							if (item.id == collegeId) { //判断学院的ID和当前选择的学院ID是否相同
								//如果相同 则循环 当前item下的children
								item.children.forEach((item1) => {
									zyhtml += '<option value="' + item1.id + '">' + item1.name + '</option>'
								})
							}
						})
						$("#zyselect").html(zyhtml)
					}
				} else {
					//没有学院ID
					majorId = null //专业ID 都为空 select为空
					$("#zyselect").html('<option value="0">请选择专业</option>')
					classId = null //班级ID 都为空 select为空
					$("#bjselect").html('<option value="0">请选择班级</option>')
				}
			}


			function fleshxueyuan2() { //刷新学院 专业班级数据
				$("#user1").html('<option value="0">请选择学号</option>')
				$("#user2").html('<option value="0">请选择姓名</option>')
				userNo = null
				if (collegeId2) {
					//有学院id
					if (majorId2) {
						//有学院ID  有专业ID 则 循环班级的list
						if (!classId2) {
							let bjhtml = '<option value="0">请选择班级</option>'
							xueyuanlist.forEach((item) => { //循环学院
								if (item.id == collegeId2) { //判断学院ID
									item.children.forEach((item1) => { //循环专业
										if (item1.id == majorId2) { //判断专业ID
											item1.children.forEach((item2) => {
												bjhtml += '<option value="' + item2.id + '">' + item2
													.name +
													'</option>'
											})
										}
									})
								}
							})
							$("#bjselect2").html(bjhtml)
						}
					} else {
						//有学院ID 没有专业ID  则班级全部是空  循环专业list
						classId2 = null //班级ID 都为空 select为空
						$("#bjselect2").html('<option value="0">请选择班级</option>')
						//循环班级的list
						let zyhtml = '<option value="0">请选择专业</option>'
						xueyuanlist.forEach((item) => {
							if (item.id == collegeId2) { //判断学院的ID和当前选择的学院ID是否相同
								//如果相同 则循环 当前item下的children
								item.children.forEach((item1) => {
									zyhtml += '<option value="' + item1.id + '">' + item1.name + '</option>'
								})
							}
						})
						$("#zyselect2").html(zyhtml)
					}
				} else {
					//没有学院ID
					majorId2 = null //专业ID 都为空 select为空
					$("#zyselect2").html('<option value="0">请选择专业</option>')
					classId2 = null //班级ID 都为空 select为空
					$("#bjselect2").html('<option value="0">请选择班级</option>')
					userNo = null //学生的列表为空  学号也为空
					$("#user1").html('<option value="0">请选择学号</option>')
					$("#user2").html('<option value="0">请选择姓名</option>')
				}

				// console.log(collegeId2)
				pageindex = 1
				getlist()
			}
			//<select id="user1" onchange="user1change(this)">
			//</select>
			//<select id="user2" onchange="user2change(this)">
			//</select>

			function getssnewlist() {
				pageindex = 1
				getlist()
			}
			let pagesize = 10
			let pageindex = 1
			let pages = 1

			// 添加全局变量用于存储所有过滤后的学生数据
			let allFilteredStudentData = [];

			function getlist() {
				let ppids = $("#sjselect2").val()
				if (ppids == '0') {
					ppids = null
					$("#showpapername").html("全部试卷")
				}

				if (allpapersssss) {
					allpapersssss.forEach((item) => {
						if (item.id == ppids) {
							$("#showpapername").html(item.name)
						}
					})
				}
				let ssxuekeid = ""
				let xsname = $("#userxm").val()
				let xuehao = $("#userxh").val()
				
				if($("#xuekeselect2").val() != '0'){
					ssxuekeid = $("#xuekeselect2").val()
				}

				$.ajax({
					url: baseurl + "/paper/answered",
					type: 'GET',
					contentType: "application/json",
					data: {
						subjectId: ssxuekeid, //学科
						name: xsname, //学生姓名 模糊查询
						pageNum: 1, // 始终请求第1页
						pageSize: 1000, // 设置一个较大的值，确保获取所有数据
						isMark: 1,
						collegeId: collegeId2, //学院2
						majorId: majorId2, //专业2
						classId: classId2, //班级2
						identifier: xuehao, //学号
						paperId: ppids, //试卷ID
						taskId: latestTaskId // 添加任务ID参数
					},
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							// 处理数据，只保留每个学号的最高分记录
							let studentMap = new Map();
							
							if (res.data && res.data.list && res.data.list.length > 0) {
								res.data.list.forEach(item => {
									if (item.student && item.student.userAuth && item.student.userAuth.identifier) {
										const studentId = item.student.userAuth.identifier;
										const score = parseFloat(item.score) || 0;
										
										// 如果学号不存在于Map中或者新分数更高，则更新Map
										if (!studentMap.has(studentId) || score > studentMap.get(studentId).score) {
											studentMap.set(studentId, item);
										}
									}
								});
							}
							
							// 将Map转回数组并存储到全局变量中
							allFilteredStudentData = Array.from(studentMap.values());
							
							// 重置为第一页
							pageindex = 1;
							
							// 重新计算总页数
							pages = Math.ceil(allFilteredStudentData.length / pagesize);
							
							// 显示当前页数据
							renderCurrentPage();
						}
					}
				})
			}

			// 添加新函数用于渲染当前页数据
			function renderCurrentPage() {
				// 计算当前页的起始和结束索引
				const startIndex = (pageindex - 1) * pagesize;
				const endIndex = Math.min(startIndex + pagesize, allFilteredStudentData.length);
				const currentPageItems = allFilteredStudentData.slice(startIndex, endIndex);
				
				let htmls = '';
				currentPageItems.forEach((item, index) => {
					htmls += `<div class="tabletoptt nonos">
						<div>${startIndex + index + 1}</div>
						<div>${item.student != null ? item.student.collegeName : ""}</div>
						<div>${item.student != null ? item.student.majorName : ""}</div>
						<div>${item.student != null ? item.student.className1 : ""}</div>
						<div>${item.student != null ? item.student.userAuth.identifier : ""}</div>
						<div>${item.student != null ? item.student.name : ""}</div>
						<div>${parseInt(item.zhuguanScore)}分</div>
						<div>${parseInt(item.keguanTotalScore)}分</div>
						<div>${parseInt(item.score)+'分'}</div>
						<div>${item.cmsTestPaper.theWeight}</div>
					</div>`;
				});
				
				$("#paperlist").html(htmls);
				
				// 更新分页控件
				if (pages > 1) {
					let numhtml = "";
					// 如果页数太多，只显示当前页附近的几页
					const maxVisiblePages = 10;
					let startPage = Math.max(1, pageindex - Math.floor(maxVisiblePages / 2));
					let endPage = Math.min(pages, startPage + maxVisiblePages - 1);
					
					// 确保显示足够多的页码按钮
					if (endPage - startPage + 1 < maxVisiblePages) {
						startPage = Math.max(1, endPage - maxVisiblePages + 1);
					}
					
					// 总是显示第一页
					if (startPage > 1) {
						numhtml += '<label onclick="getnewlist(1)">1</label>';
						if (startPage > 2) {
							numhtml += '<label>...</label>';
						}
					}
					
					for (let a = startPage; a <= endPage; a++) {
						if (pageindex == a) {
							numhtml += '<label class="actinum" onclick="getnewlist(' + a + ')">' + a + '</label>';
						} else {
							numhtml += '<label onclick="getnewlist(' + a + ')">' + a + '</label>';
						}
					}
					
					// 总是显示最后一页
					if (endPage < pages) {
						if (endPage < pages - 1) {
							numhtml += '<label>...</label>';
						}
						numhtml += '<label onclick="getnewlist(' + pages + ')">' + pages + '</label>';
					}
					
					$("#sy").attr("onclick", "getnewlist(1)");
					$("#syy").attr("onclick", "getnewlist(1)");
					if (pageindex > 1) {
						$("#syy").attr("onclick", "getnewlist(" + (pageindex - 1) + ")");
					}
					if (pageindex < pages) {
						$("#xyy").attr("onclick", "getnewlist(" + (pageindex + 1) + ")");
					} else {
						$("#xyy").attr("onclick", "getnewlist(" + pageindex + ")");
					}
					$("#wy").attr("onclick", "getnewlist(" + pages + ")");
					$("#num").html(numhtml);
					$("#fyq").show();
				} else {
					$("#fyq").hide();
				}
			}

			function getnewlist(index) {
				if (index == pageindex) {
					if (index == pages) {
						cocoMessage.warning(1000, "已经是最后一页了！");
					} else {
						cocoMessage.warning(1000, "已经是第" + index + "页了！");
					}
				} else {
					pageindex = index;
					// 不再重新请求后端数据，直接使用已有数据渲染新页面
					renderCurrentPage();
				}
			}

			function xuekechange() {
				if ($("#xuekeselect1").val() != '0') {
					getnumlist()
				} else {
					$("#msg1").show()
					$("#main").hide()
				}
			}

			function getnumlist() {
				$("#msg1").hide()
				$("#main").show()

				let xkid1 = ""
				let stname1 = ""
				if ($("#stinput1").val()) {
					stname1 = $("#stinput1").val()
				}
				if ($("#xuekeselect1").val() != '0') {
					xkid1 = $("#xuekeselect1").val()
				}

				// 构建请求参数对象
				const params = new URLSearchParams();
				
				// 只添加有值的参数
				if (xkid1) params.append('projectId', xkid1);
				if (stname1) params.append('paperName', stname1);
				if (collegeId) params.append('collegeId', collegeId);
				if (majorId) params.append('majorId', majorId);
				if (classId) params.append('classId', classId);
				if (time1) params.append('time1', time1);
				if (time2) params.append('time2', time2);
				if (latestTaskId) params.append('taskId', latestTaskId);
				
				// 添加参数，只获取每个学号的最高分
				params.append('onlyHighestScore', 'true');

				// 并行请求两个接口获取数据
				Promise.all([
					// 获取统计数据
					$.ajax({
						url: baseurl + "/stat/studentsore?" + params.toString(),
						type: 'GET',
						headers: {
							"Authorization": sessionStorage.getItem("header"),
							"Content-Type": "application/json"
						}
					}).catch(error => {
						console.warn("统计数据获取失败:", error);
						return { code: 'error', data: [] };
					}),
					// 获取详细成绩数据
					$.ajax({
						url: baseurl + "/paper/answered",
						type: 'GET',
						data: {
							subjectId: xkid1 || undefined,
							name: "",
							pageNum: 1,
							pageSize: 1000,
							isMark: 1,
							collegeId: collegeId || undefined,
							majorId: majorId || undefined,
							classId: classId || undefined,
							identifier: "",
							paperId: "",
							taskId: latestTaskId || undefined, // 添加任务ID参数
							onlyHighestScore: true  // 添加参数，只获取每个学号的最高分
						},
						headers: {
							"Authorization": sessionStorage.getItem("header"),
							"Content-Type": "application/json"
						}
					}).catch(error => {
						console.warn("成绩数据获取失败:", error);
						return { code: 'error', data: { list: [] } };
					})
				]).then(([statRes, scoreRes]) => {
					if (scoreRes.code == '200') {
						const scoreStats = processScoreStats(scoreRes.data.list);
						showmain(scoreStats);
						
						// 添加说明信息，表示数据已按每个学生最高成绩筛选
						const infoElement = document.createElement('div');
						infoElement.className = 'data-filter-note';
						infoElement.style.textAlign = 'center';
						infoElement.style.color = '#666';
						infoElement.style.fontSize = '14px';
						infoElement.style.margin = '5px 0 15px';
						// infoElement.textContent = '注：统计图表数据已按每个学生的最高成绩进行筛选';
						
						const mainElement = document.getElementById('main');
						if (mainElement && mainElement.parentNode) {
							mainElement.parentNode.insertBefore(infoElement, mainElement);
						}
					} else {
						$("#msg1").show().text("数据获取失败，请稍后重试");
						$("#main").hide();
					}
				}).catch(error => {
					console.error("数据处理失败:", error);
					$("#msg1").show().text("数据处理失败，请稍后重试");
					$("#main").hide();
				});
			}

			// 修改错误提示的样式
			$("#msg1").css({
				"color": "#999999",
				"font-size": "0.8333rem",
				"text-align": "center",
				"padding-top": "8rem"
			});

			// 处理成绩统计数据
			function processScoreStats(scoreList) {
				if (!scoreList || scoreList.length === 0) {
					return {
						stats: [],
						validCounts: {
							subjective: { total: 0 },
							objective: { total: 0 },
							total: { total: 0 }
						},
						scoreList: [] // 保存原始数据用于获取班级信息
					};
				}

				// 首先按学号筛选，保留每个学号最高分的记录
				const studentMap = new Map();
				scoreList.forEach(item => {
					if (item.student && item.student.userAuth && item.student.userAuth.identifier) {
						const studentId = item.student.userAuth.identifier;
						const score = parseFloat(item.score) || 0;
						
						// 如果学号不存在于Map中或者新分数更高，则更新Map
						if (!studentMap.has(studentId) || score > studentMap.get(studentId).score) {
							studentMap.set(studentId, item);
						}
					}
				});
				
				// 将Map转回数组，使用筛选后的学生成绩数据
				const filteredScoreList = Array.from(studentMap.values());

				// 过滤有效分数
				const validScores = {
					subjective: filteredScoreList.filter(item => !isNaN(parseFloat(item.zhuguanScore))),
					objective: filteredScoreList.filter(item => !isNaN(parseFloat(item.keguanTotalScore))),
					total: filteredScoreList.filter(item => !isNaN(parseFloat(item.score)))
				};

				// 计算各项统计数据
				const stats = [
					{
						name: '主观题',
						type: 'bar',
						data: [
							{value: validScores.subjective.length > 0 ? (validScores.subjective.reduce((sum, item) => sum + parseFloat(item.zhuguanScore), 0) / validScores.subjective.length).toFixed(1) : 0, name: '平均分'},
							{value: validScores.subjective.length > 0 ? (validScores.subjective.filter(item => parseFloat(item.zhuguanScore) >= 60).length / validScores.subjective.length * 100).toFixed(1) : 0, name: '及格率'},
							{value: validScores.subjective.length > 0 ? (Math.max(...validScores.subjective.map(item => parseFloat(item.zhuguanScore))) - Math.min(...validScores.subjective.map(item => parseFloat(item.zhuguanScore)))).toFixed(1) : 0, name: '分数跨度'},
							{value: validScores.subjective.length > 0 ? Math.max(...validScores.subjective.map(item => parseFloat(item.zhuguanScore))).toFixed(1) : 0, name: '最高分'},
							{value: validScores.subjective.length > 0 ? Math.min(...validScores.subjective.map(item => parseFloat(item.zhuguanScore))).toFixed(1) : 0, name: '最低分'}
						]
					},
					{
						name: '客观题',
						type: 'bar',
						data: [
							{value: validScores.objective.length > 0 ? (validScores.objective.reduce((sum, item) => sum + parseFloat(item.keguanTotalScore), 0) / validScores.objective.length).toFixed(1) : 0, name: '平均分'},
							{value: validScores.objective.length > 0 ? (validScores.objective.filter(item => parseFloat(item.keguanTotalScore) >= 60).length / validScores.objective.length * 100).toFixed(1) : 0, name: '及格率'},
							{value: validScores.objective.length > 0 ? (Math.max(...validScores.objective.map(item => parseFloat(item.keguanTotalScore))) - Math.min(...validScores.objective.map(item => parseFloat(item.keguanTotalScore)))).toFixed(1) : 0, name: '分数跨度'},
							{value: validScores.objective.length > 0 ? Math.max(...validScores.objective.map(item => parseFloat(item.keguanTotalScore))).toFixed(1) : 0, name: '最高分'},
							{value: validScores.objective.length > 0 ? Math.min(...validScores.objective.map(item => parseFloat(item.keguanTotalScore))).toFixed(1) : 0, name: '最低分'}
						]
					},
					{
						name: '总分',
						type: 'bar',
						data: [
							{value: validScores.total.length > 0 ? (validScores.total.reduce((sum, item) => sum + parseFloat(item.score), 0) / validScores.total.length).toFixed(1) : 0, name: '平均分'},
							{value: validScores.total.length > 0 ? (validScores.total.filter(item => parseFloat(item.score) >= 60).length / validScores.total.length * 100).toFixed(1) : 0, name: '及格率'},
							{value: validScores.total.length > 0 ? (Math.max(...validScores.total.map(item => parseFloat(item.score))) - Math.min(...validScores.total.map(item => parseFloat(item.score)))).toFixed(1) : 0, name: '分数跨度'},
							{value: validScores.total.length > 0 ? Math.max(...validScores.total.map(item => parseFloat(item.score))).toFixed(1) : 0, name: '最高分'},
							{value: validScores.total.length > 0 ? Math.min(...validScores.total.map(item => parseFloat(item.score))).toFixed(1) : 0, name: '最低分'}
						]
					}
				];

				return {
					stats,
					validCounts: {
						subjective: { total: validScores.subjective.length },
						objective: { total: validScores.objective.length },
						total: { total: validScores.total.length }
					},
					scoreList: filteredScoreList // 保存筛选后的数据用于获取班级信息
				};
			}

			function showmain(statsData) {
				// 销毁已存在的实例
				let existingChart = echarts.getInstanceByDom(document.getElementById('main'));
				if (existingChart) {
					existingChart.dispose();
				}
				
				// 创建新实例
				var myChart = echarts.init(document.getElementById('main'));
				
				// 获取当前选中的班级名称
				let className = "全部班级";
				if (classId) {
					xueyuanlist.forEach(college => {
						if (college.id === collegeId) {
							college.children.forEach(major => {
								if (major.id === majorId) {
									major.children.forEach(cls => {
										if (cls.id === classId) {
											className = `${college.name}-${major.name}-${cls.name}`;
										}
									});
								}
							});
						}
					});
				} else if (majorId) {
					xueyuanlist.forEach(college => {
						if (college.id === collegeId) {
							college.children.forEach(major => {
								if (major.id === majorId) {
									className = `${college.name}-${major.name}-全部班级`;
								}
							});
						}
					});
				} else if (collegeId) {
					xueyuanlist.forEach(college => {
						if (college.id === collegeId) {
							className = `${college.name}-全部专业`;
						}
					});
				}
				
				// 定义棕红色系配色方案
				const colors = [
					'#A65D57', // 主观题：深棕红色
					'#D2691E', // 客观题：巧克力色
					'#CD5C5C'  // 总分：印度红色
				];
				
				// 获取班级信息
				function getClassInfo(scoreList) {
					if (!scoreList || scoreList.length === 0) return '';
					
					const classMap = new Map();
					scoreList.forEach(item => {
						if (item.student && item.student.className1) {
							classMap.set(item.student.className1, true);
						}
					});
					
					return Array.from(classMap.keys()).join('、');
				}

				let option = {
					title: {
						text: className + ' - 成绩统计',
						left: 'center',
						top: '10px',
						textStyle: {
							color: '#333',
							fontSize: 16,
							fontWeight: 'normal'
						}
					},
					color: colors,
					tooltip: {
						trigger: 'axis',
						axisPointer: {
							type: 'shadow'
						},
						formatter: function(params) {
							let result = params[0].name + '<br/>';
							params.forEach(param => {
								let value = param.value;
								let suffix = param.name === '及格率' ? '%' : 
										   (param.name !== '分数跨度' ? '分' : '');
								result += param.marker + param.seriesName + ': ' + value + suffix + '<br/>';
							});
							return result;
						}
					},
					legend: {
						data: ['主观题', '客观题', '总分'],
						textStyle: {
							color: '#666'
						},
						top: '40px'
					},
					grid: {
						left: '3%',
						right: '4%',
						bottom: '80px',
						top: '80px',
						containLabel: true
					},
					xAxis: {
						type: 'category',
						data: ['平均分', '及格率', '分数跨度', '最高分', '最低分'],
							axisLabel: {
								interval: 0,
								rotate: 0,
								color: '#666'
							}
					},
					yAxis: {
						type: 'value',
						axisLabel: {
							formatter: function(value) {
								return value;
							},
							color: '#666'
						}
					},
					series: statsData.stats.map((item, index) => ({
						...item,
						itemStyle: {
							color: colors[index % colors.length],
							emphasis: {
								shadowBlur: 10,
								shadowOffsetX: 0,
								shadowColor: 'rgba(0, 0, 0, 0.5)'
							}
						},
						barGap: '10%',
						barCategoryGap: '20%'
					}))
				};
				
				myChart.setOption(option);

				// 添加底部信息显示
				const container = document.getElementById('main');
				let infoDiv = document.getElementById('chart-info');
				if (!infoDiv) {
					infoDiv = document.createElement('div');
					infoDiv.id = 'chart-info';
					container.appendChild(infoDiv);
				}

				// 设置底部信息的样式
				infoDiv.style.cssText = `
					position: absolute;
					bottom: 10px;
					left: 0;
					width: 100%;
					text-align: center;
					display: flex;
					justify-content: center;
					align-items: center;
					gap: 20px;
					font-size: 14px;
					padding: 10px 0;
				`;

				// 更新底部信息内容
				infoDiv.innerHTML = `
					<span style="color: #A65D57;">有效人数: ${statsData.validCounts.total.total}人</span>
					<span style="color: #666;">|</span>
					<span style="color: #D2691E;">参与班级: ${getClassInfo(statsData.scoreList)}</span>
				`;
			}

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 						.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function edit() {
					sessionStorage.clear()
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					cocoMessage.error(1000, "已退出登录！")
			}

			function setDate(value) {
				let date = new Date(value);
				let y = date.getFullYear();
				let MM = date.getMonth() + 1;
				MM = MM < 10 ? ('0' + MM) : MM;
				let d = date.getDate();
				d = d < 10 ? ('0' + d) : d;
				let h = date.getHours();
				h = h < 10 ? ('0' + h) : h;
				let m = date.getMinutes();
				m = m < 10 ? ('0' + m) : m;
				let s = date.getSeconds();
				s = s < 10 ? ('0' + s) : s;
				return y + '年' + MM + '月' + d + '日 ' + h + ':' + m
			}

			// 最近任务ID
			let latestTaskId = null;
			
			// 获取最近的一次任务数据
			function getLatestTaskData() {
				// 获取当前日期
				const currentDate = new Date();
				const currentYear = currentDate.getFullYear();
				const currentMonth = currentDate.getMonth() + 1; // 月份从0开始
				
				// 判断当前学期
				let currentTerm;
				let termStartDate, termEndDate;
				
				if (currentMonth >= 3 && currentMonth <= 8) {
					// 3-8月为春季学期（上学期）
					currentTerm = currentYear + '-上学期';
					termStartDate = currentYear + '-3-1';
					termEndDate = currentYear + '-8-31';
				} else {
					// 9-12月和1-2月为秋季学期（下学期）
					if (currentMonth >= 9) {
						currentTerm = currentYear + '-下学期';
						termStartDate = currentYear + '-9-1';
						termEndDate = (currentYear + 1) + '-2-28';
					} else {
						currentTerm = (currentYear - 1) + '-下学期';
						termStartDate = (currentYear - 1) + '-9-1';
						termEndDate = currentYear + '-2-28';
					}
				}
				
				// 先尝试从API获取最近任务
				$.ajax({
					url: baseurl + "/task/latest",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: function(res) {
						if (res.code == '200' && res.data) {
							latestTaskId = res.data.id;
							
							// 如果有学科ID，则优先使用
							if (res.data.subjectId) {
								// 选中学科下拉列表
								$("#xuekeselect1").val(res.data.subjectId);
								$("#xuekeselect2").val(res.data.subjectId);
							}
							
							// 更新年份选择框（如果任务有相关时间段）
							if (res.data.startTime && res.data.endTime) {
								let startDate = new Date(res.data.startTime);
								let endDate = new Date(res.data.endTime);
								
								// 设置时间范围
								time1 = formatDate(startDate);
								time2 = formatDate(endDate);
								
								// 根据任务的时间判断学期
								let taskYear = startDate.getFullYear();
								let taskMonth = startDate.getMonth() + 1;
								let taskTerm = taskMonth >= 9 || taskMonth <= 2 ? taskYear + '-下学期' : taskYear + '-上学期';
								
								// 更新年份选择框
								$("#years").val(taskTerm).change();
							}
							
							// 如果任务关联了试卷，更新试卷选择
							if (res.data.paperId) {
								$("#sjselect2").val(res.data.paperId).change();
								ppid = res.data.paperId;
							}
							
							// 获取统计数据
							getnumlist();
							getlist();
							
							// 更新提示信息
							$(".data-filter-note").append('<div style="margin-top: 5px; font-style: italic;">当前显示: ' + 
								(res.data.title || '最近任务') + ' 数据</div>');
						} else {
							// 获取失败时使用当前学期
							$("#years").val(currentTerm).change();
							
							// 获取当前学期默认数据
							time1 = termStartDate;
							time2 = termEndDate;
							
							getnumlist();
							getlist();
							
							// 添加当前学期信息
							$(".data-filter-note").append('<div style="margin-top: 5px; font-style: italic;">当前显示: ' + 
								currentTerm + ' 数据</div>');
						}
					},
					error: function() {
						// 错误时使用当前学期
						$("#years").val(currentTerm).change();
						
						// 获取当前学期默认数据
						time1 = termStartDate;
						time2 = termEndDate;
						
						getnumlist();
						getlist();
						
						// 添加当前学期信息
						$(".data-filter-note").append('<div style="margin-top: 5px; font-style: italic;">当前显示: ' + 
							currentTerm + ' 数据</div>');
					}
				});
			}
			
			// 日期格式化辅助函数
			function formatDate(date) {
				let year = date.getFullYear();
				let month = (date.getMonth() + 1).toString().padStart(2, '0');
				let day = date.getDate().toString().padStart(2, '0');
				return year + '-' + month + '-' + day;
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
		<script>
			// 添加CSS样式
			const style = document.createElement('style');
			style.textContent = `
				#main {
					position: relative;
				}
				#chart-info span {
					display: inline-block;
					vertical-align: middle;
					line-height: 1.5;
				}
			`;
			document.head.appendChild(style);
		</script>
		
		<!-- 导航局部刷新功能 -->
		<script>
			// 导航局部刷新功能
			$(document).ready(function() {
				// 为所有导航链接添加点击事件处理
				$(document).on('click', '.leftitem[href]', function(e) {
					e.preventDefault(); // 阻止默认跳转
					
					const href = $(this).attr('href');
					const currentHref = window.location.pathname.split('/').pop();
					
					// 如果点击的是当前页面，则不进行跳转
					if (href === currentHref) {
						cocoMessage.info(800, "您已在当前页面");
						return;
					}
					
					// 显示加载提示
					cocoMessage.loading(500, "页面加载中...");
					
					// 更新激活状态
					$('.leftitem').removeClass('activeleftitem');
					$(this).addClass('activeleftitem');
					
					// 延迟跳转，让用户看到激活状态变化
					setTimeout(function() {
						window.location.href = href;
					}, 300);
				});
				
				// 为无href的激活项添加点击提示
				$(document).on('click', '.leftitem:not([href])', function(e) {
					cocoMessage.info(800, "您已在当前页面");
				});
			});
		</script>
	</body>
</html>
