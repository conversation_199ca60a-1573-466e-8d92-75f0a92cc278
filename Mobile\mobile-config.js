// 移动端配置文件
// 用于管理移动端的各种设置和配置

const MobileConfig = {
    // 版本信息
    version: '1.0.0',
    buildDate: '2024-12-19',
    
    // 应用信息
    app: {
        name: '思政一体化平台',
        shortName: '思政平台',
        description: '传承红色基因，弘扬时代精神',
        keywords: ['思政', '教育', '学习', '红色文化'],
        author: '思政一体化平台开发团队'
    },
    
    // API配置
    api: {
        // 基础URL配置（与PC端保持一致）
        baseUrl: (function() {
            const isProduction = window.location.hostname !== 'localhost' && 
                                !window.location.hostname.includes('127.0.0.1');
            return isProduction ? '/api' : 'http://localhost:5500/api';
        })(),
        
        // 请求超时时间
        timeout: 10000,
        
        // 重试次数
        retryCount: 3,
        
        // 常用接口路径
        endpoints: {
            login: '/student/login',
            casLogin: '/student/caslogin',
            logout: '/student/logout',
            userInfo: '/student/info',
            categories: '/web/category/all',
            posts: '/web/posts',
            redbooks: '/web/redbook/list',
            courses: '/web/course/list',
            banner: '/web/banner/list'
        }
    },
    
    // 移动端特性配置
    mobile: {
        // 触摸交互
        touch: {
            // 滑动阈值（像素）
            swipeThreshold: 50,
            // 触摸反馈延迟（毫秒）
            feedbackDelay: 100,
            // 长按时间（毫秒）
            longPressDelay: 500
        },
        
        // 下拉刷新
        pullRefresh: {
            // 触发阈值（像素）
            threshold: 80,
            // 最大拉动距离（像素）
            maxDistance: 120,
            // 动画持续时间（毫秒）
            animationDuration: 300
        },
        
        // 无限滚动
        infiniteScroll: {
            // 触发距离（像素）
            triggerDistance: 100,
            // 每页数据量
            pageSize: 20
        },
        
        // 返回顶部
        backToTop: {
            // 显示阈值（像素）
            showThreshold: 300,
            // 滚动动画时间（毫秒）
            scrollDuration: 500
        }
    },
    
    // UI配置
    ui: {
        // 主题色彩
        colors: {
            primary: '#c00714',
            primaryDark: '#a00610',
            secondary: '#667eea',
            success: '#28a745',
            warning: '#ffc107',
            error: '#dc3545',
            info: '#17a2b8'
        },
        
        // 动画配置
        animation: {
            // 默认过渡时间
            defaultDuration: 300,
            // 缓动函数
            easing: 'cubic-bezier(0.4, 0.0, 0.2, 1)',
            // 页面切换动画
            pageTransition: 'slide'
        },
        
        // 布局配置
        layout: {
            // 头部高度
            headerHeight: 56,
            // 底部导航高度
            bottomNavHeight: 60,
            // 容器内边距
            containerPadding: 16,
            // 卡片圆角
            cardRadius: 12
        },
        
        // 字体配置
        typography: {
            // 基础字体大小
            baseFontSize: 16,
            // 字体族
            fontFamily: '-apple-system, BlinkMacSystemFont, "Segoe UI", "PingFang SC", "Hiragino Sans GB", "Microsoft YaHei", sans-serif'
        }
    },
    
    // 缓存配置
    cache: {
        // 本地存储键名前缀
        prefix: 'szjx_mobile_',
        
        // 缓存过期时间（毫秒）
        expiration: {
            userInfo: 24 * 60 * 60 * 1000,      // 24小时
            menuData: 60 * 60 * 1000,           // 1小时
            bannerData: 30 * 60 * 1000,         // 30分钟
            postsList: 10 * 60 * 1000           // 10分钟
        },
        
        // 最大缓存大小（条目数）
        maxSize: {
            posts: 100,
            courses: 50,
            redbooks: 50
        }
    },
    
    // 性能配置
    performance: {
        // 图片懒加载
        lazyLoad: {
            enabled: true,
            threshold: 100,
            placeholder: 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMzAwIiBoZWlnaHQ9IjIwMCIgZmlsbD0iI2Y1ZjVmNSIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTQiIGZpbGw9IiM5OTkiIHRleHQtYW5jaG9yPSJtaWRkbGUiIGR5PSIuM2VtIj5sb2FkaW5nLi4uPC90ZXh0Pjwvc3ZnPg=='
        },
        
        // 防抖配置
        debounce: {
            search: 500,
            scroll: 16,
            resize: 250
        },
        
        // 预加载
        preload: {
            enabled: true,
            maxConcurrent: 3
        }
    },
    
    // 错误处理配置
    errorHandling: {
        // 是否显示详细错误信息
        showDetailedErrors: false,
        
        // 错误重试配置
        retry: {
            maxAttempts: 3,
            delay: 1000,
            backoff: 2
        },
        
        // 错误上报
        reporting: {
            enabled: false,
            endpoint: '/api/error/report'
        }
    },
    
    // 开发配置
    development: {
        // 是否为开发模式
        isDevelopment: window.location.hostname === 'localhost' || 
                      window.location.hostname.includes('127.0.0.1'),
        
        // 调试配置
        debug: {
            enabled: false,
            logLevel: 'info', // 'debug', 'info', 'warn', 'error'
            showPerformance: false
        },
        
        // 模拟数据
        mockData: {
            enabled: false,
            delay: 1000
        }
    },
    
    // 安全配置
    security: {
        // CSP配置
        csp: {
            enabled: true,
            reportOnly: false
        },
        
        // XSS防护
        xssProtection: true,
        
        // 内容类型检查
        contentTypeNosniff: true
    },
    
    // 分析统计配置
    analytics: {
        enabled: false,
        trackingId: '',
        
        // 事件追踪
        events: {
            pageView: true,
            userInteraction: true,
            errorTracking: true
        }
    },
    
    // 工具函数
    utils: {
        // 获取配置值
        get: function(path, defaultValue = null) {
            const keys = path.split('.');
            let value = this;
            
            for (const key of keys) {
                if (value && typeof value === 'object' && key in value) {
                    value = value[key];
                } else {
                    return defaultValue;
                }
            }
            
            return value;
        },
        
        // 设置配置值
        set: function(path, value) {
            const keys = path.split('.');
            const lastKey = keys.pop();
            let target = this;
            
            for (const key of keys) {
                if (!(key in target) || typeof target[key] !== 'object') {
                    target[key] = {};
                }
                target = target[key];
            }
            
            target[lastKey] = value;
        },
        
        // 合并配置
        merge: function(config) {
            return Object.assign({}, this, config);
        },
        
        // 验证配置
        validate: function() {
            const required = ['app.name', 'api.baseUrl'];
            const missing = [];
            
            for (const path of required) {
                if (this.get(path) === null) {
                    missing.push(path);
                }
            }
            
            if (missing.length > 0) {
                console.warn('Missing required config:', missing);
                return false;
            }
            
            return true;
        }
    }
};

// 删除工具函数中的this引用问题
Object.assign(MobileConfig.utils, {
    get: function(path, defaultValue = null) {
        const keys = path.split('.');
        let value = MobileConfig;
        
        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            } else {
                return defaultValue;
            }
        }
        
        return value;
    },
    
    set: function(path, value) {
        const keys = path.split('.');
        const lastKey = keys.pop();
        let target = MobileConfig;
        
        for (const key of keys) {
            if (!(key in target) || typeof target[key] !== 'object') {
                target[key] = {};
            }
            target = target[key];
        }
        
        target[lastKey] = value;
    },
    
    merge: function(config) {
        return Object.assign({}, MobileConfig, config);
    }
});

// 初始化配置
MobileConfig.utils.validate();

// 导出配置
if (typeof module !== 'undefined' && module.exports) {
    module.exports = MobileConfig;
} else if (typeof window !== 'undefined') {
    window.MobileConfig = MobileConfig;
}

// 开发模式下的调试信息
if (MobileConfig.development.isDevelopment && MobileConfig.development.debug.enabled) {
    console.log('Mobile Config loaded:', MobileConfig);
}
