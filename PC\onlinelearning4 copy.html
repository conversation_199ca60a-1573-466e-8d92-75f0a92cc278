<!DOCTYPE html>
<html>
		<head>				<meta charset="utf-8" />		<meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no">		<title>思政一体化平台-在线学习-详情</title>		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />		<link rel="stylesheet" type="text/css" href="css/swiper.min.css" />		<link rel="stylesheet" type="text/css" href="css/style.css" />		<link rel="stylesheet" type="text/css" href="css/onlinelearning.css" />
		<!-- Tailwind CSS -->
		<script src="https://cdn.tailwindcss.com"></script>		<script src="./js/swiper.animate.min.js" type="text/javascript" charset="utf-8"></script>		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>		<script src="./js/jquery.media.js" type="text/javascript" charset="utf-8"></script>		<!-- 添加多文件格式支持 -->		<script src="./js/viewers/howler.min.js" type="text/javascript" charset="utf-8"></script>		<script src="https://unpkg.com/jszip@3.10.1/dist/jszip.min.js" type="text/javascript" charset="utf-8"></script>		<script src="./js/viewers/docx-preview.min.js" type="text/javascript" charset="utf-8"></script>		<script src="./js/viewers/file-viewer.js" type="text/javascript" charset="utf-8"></script>
        <!-- 添加账号切换功能 -->
        <script src="./js/login-mock.js" type="text/javascript" charset="utf-8"></script>
		<style>
			/* 现代音频播放器样式 */
			.modern-player-container {
				display: flex;
				justify-content: center;
				align-items: center;
				padding: 30px 0;
				background: #fff;
			}
			
			.modern-audio-player {
				width: 90%;
				max-width: 500px;
				padding: 30px;
				border-radius: 16px;
				background: #fff;
				box-shadow: 0 10px 30px rgba(0,0,0,0.08);
				display: flex;
				flex-direction: column;
				align-items: center;
				position: relative;
				transition: all 0.3s ease;
			}
			
			.album-art {
				width: 200px;
				height: 200px;
				border-radius: 50%;
				background: linear-gradient(145deg, #f0f0f0, #e6e6e6);
				margin-bottom: 25px;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
				overflow: hidden;
				transition: all 0.3s ease;
				/* 添加边框和内部阴影效果 */
				border: 4px solid #fff;
				box-shadow: 0 8px 20px rgba(0,0,0,0.15);
				/* 确保背景图片正确显示 */
				background-position: center !important;
				background-size: cover !important;
				background-repeat: no-repeat !important;
			}
			
			.album-art.rotating {
				animation: rotate 20s linear infinite;
			}
			
			@keyframes rotate {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}
			
			/* 为封面图片添加悬停效果 */
			.album-art:hover {
				transform: scale(1.05);
				box-shadow: 0 10px 25px rgba(0,0,0,0.2), inset 0 0 0 4px rgba(255,255,255,0.7);
			}
			
			.audio-icon {
				width: 70px;
				height: 70px;
				background: #A65D57;
				mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z'%3E%3C/path%3E%3C/svg%3E") no-repeat center;
				-webkit-mask: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24'%3E%3Cpath d='M12 3v10.55c-.59-.34-1.27-.55-2-.55-2.21 0-4 1.79-4 4s1.79 4 4 4 4-1.79 4-4V7h4V3h-6z'%3E%3C/path%3E%3C/svg%3E") no-repeat center;
				mask-size: contain;
				-webkit-mask-size: contain;
				opacity: 0.9;
			}
			
			.audio-info {
				text-align: center;
				margin-bottom: 20px;
				width: 100%;
			}
			
			.audio-title {
				font-size: 1.5rem;
				font-weight: 600;
				color: #333;
				margin-bottom: 8px;
				white-space: nowrap;
				overflow: hidden;
				text-overflow: ellipsis;
			}
			
			.audio-author {
				font-size: 1rem;
				color: #777;
				margin-bottom: 15px;
			}
			
			.audio-progress-container {
				width: 100%;
				display: flex;
				align-items: center;
				margin-bottom: 25px;
			}
			
			.audio-time {
				font-size: 0.85rem;
				color: #888;
				font-variant-numeric: tabular-nums;
			}
			
			.progress-bar-container {
				flex-grow: 1;
				height: 6px;
				background: #f0f0f0;
				border-radius: 3px;
				margin: 0 10px;
				position: relative;
				cursor: pointer;
				overflow: hidden;
			}
			
			.progress-bar {
				position: absolute;
				top: 0;
				left: 0;
				height: 100%;
				background: #A65D57;
				border-radius: 3px;
				width: 0%;
				transition: width 0.1s linear;
			}
			
			.audio-controls {
				display: flex;
				align-items: center;
				justify-content: center;
			}
			
			.audio-controls button {
				background: transparent;
				border: none;
				cursor: pointer;
				outline: none;
				margin: 0 15px;
				padding: 0;
				transition: all 0.2s ease;
			}
			
			.audio-controls button:hover {
				transform: scale(1.1);
			}
			
			.btn-prev svg, .btn-next svg {
				width: 30px;
				height: 30px;
				fill: #A65D57;
			}
			
			.btn-play {
				width: 60px;
				height: 60px;
				border-radius: 50%;
				background: #A65D57;
				display: flex;
				align-items: center;
				justify-content: center;
				position: relative;
				box-shadow: 0 4px 10px rgba(166, 93, 87, 0.3);
				border: 2px solid #fff;
			}
			
			.btn-play svg {
				width: 30px;
				height: 30px;
				fill: white;
				position: absolute;
				top: 50%;
				left: 50%;
				transform: translate(-50%, -50%);
				transition: opacity 0.2s ease;
				/* 增加描边使其在白色背景上更容易辨识 */
				stroke: #333;
				stroke-width: 0.3px;
			}
			
			.btn-play .pause-icon {
				opacity: 0;
			}
			
			.btn-play.playing .play-icon {
				opacity: 0;
			}
			
			.btn-play.playing .pause-icon {
				opacity: 1;
			}
			
			/* 响应式设计 */
			@media (max-width: 768px) {
				.modern-audio-player {
					padding: 20px;
				}
				
				.album-art {
					width: 150px;
					height: 150px;
					margin-bottom: 20px;
				}
				
				.audio-title {
					font-size: 1.2rem;
				}
				
				.audio-author {
					font-size: 0.9rem;
				}
				
				.btn-play {
					width: 50px;
					height: 50px;
				}
				
				.btn-play svg, .btn-prev svg, .btn-next svg {
					width: 24px;
					height: 24px;
				}
			}
			
			@media (max-width: 480px) {
				.modern-audio-player {
					padding: 15px;
				}
				
				.album-art {
					width: 120px;
					height: 120px;
					margin-bottom: 15px;
				}
				
				.audio-title {
					font-size: 1.1rem;
				}
				
				.audio-author {
					font-size: 0.8rem;
				}
				
				.audio-controls button {
					margin: 0 10px;
				}
			}

			.zsdlabel {
				background: #f3f3f3;
				border-radius: 0.3125rem;
				padding: 0.3125rem 0.625rem;
				font-size: 0.833333rem;
				color: #333333;
				display: inline-block;
				margin-bottom: 0.625rem;
				margin-right: 0.625rem;
			}

			.zsdlabel img {
				width: 1rem;
				float: left;
				padding-right: 0.625rem;
			}
			
			/* 按钮交互效果 */
			#backButton:hover {
				background-color: #eaeaea !important;
				box-shadow: 0 2px 5px rgba(0,0,0,0.1);
			}
			
			.kjpfbtn:hover {
				background-color: #8a4d47 !important;
				box-shadow: 0 2px 5px rgba(0,0,0,0.2);
			}
			
			/* 统一文件查看器样式 */
			.doc-toolbar {
				display: flex;
				justify-content: space-between;
				align-items: center;
				padding: 10px 15px;
				background: #f5f5f5;
				border-radius: 8px 8px 0 0;
				box-shadow: 0 1px 3px rgba(0,0,0,0.1);
				font-weight: 500;
				color: #333;
				width: 90%;
				max-width: 1000px;
				margin: 0 auto;
			}
			
			.doc-container {
				width: 90%;
				max-width: 1000px;
				height: 600px;
				border: 1px solid #ddd;
				border-top: none;
				border-radius: 0 0 8px 8px;
				overflow: auto;
				background: white;
				position: relative;
				margin: 0 auto;
			}
			
			.docx-viewer {
				padding: 20px;
				font-family: 'Times New Roman', serif;
				line-height: 1.5;
			}
			
			.docx-viewer .page {
				box-shadow: 0 2px 5px rgba(0,0,0,0.1);
				margin-bottom: 20px;
				background: white;
				padding: 20px;
				border: 1px solid #eee;
				max-width: 90%;
				margin-left: auto;
				margin-right: auto;
				word-break: break-word;
				overflow-wrap: break-word;
			}
			
			.audio-player {
				width: 100%;
				height: 200px;
				display: flex;
				flex-direction: column;
				justify-content: center;
				background: #f9f9f9;
				border: 1px solid #ddd;
				border-top: none;
				border-radius: 0 0 8px 8px;
				padding: 20px;
			}
			
			.audio-player .controls {
				display: flex;
				align-items: center;
				margin-top: 20px;
			}
			
			.audio-player .play-btn {
				background: #A65D57;
				color: white;
				border: none;
				border-radius: 50%;
				width: 50px;
				height: 50px;
				font-size: 20px;
				display: flex;
				align-items: center;
				justify-content: center;
				cursor: pointer;
				transition: background-color 0.3s;
			}
			
			.audio-player .progress-container {
				flex-grow: 1;
				margin: 0 20px;
				background: #ddd;
				border-radius: 5px;
				height: 8px;
				position: relative;
				cursor: pointer;
			}
			
			.audio-player .progress-bar {
				background: #A65D57;
				border-radius: 5px;
				height: 100%;
				width: 0;
			}
			
			.loading-indicator {
				display: flex;
				justify-content: center;
				align-items: center;
				position: absolute;
				top: 0;
				left: 0;
				width: 100%;
				height: 100%;
				background-color: rgba(255, 255, 255, 0.7);
				border-radius: 50%;
				flex-direction: column;
				color: #666;
				font-size: 14px;
				text-align: center;
				z-index: 5;
				backdrop-filter: blur(2px);
			}
			
			.loading-indicator:before {
				content: "";
				width: 40px;
				height: 40px;
				margin-bottom: 10px;
				border: 4px solid #f3f3f3;
				border-top: 4px solid #A65D57;
				border-radius: 50%;
				animation: spin 1s linear infinite;
			}
			
			.loading-indicator.error {
				background-color: rgba(255, 235, 235, 0.8);
				color: #a65d57;
			}
			
			.loading-indicator.error:before {
				border: 4px solid #ffcccc;
				border-top: 4px solid #a65d57;
				animation: none;
				transform: rotate(45deg);
			}
			
			@keyframes spin {
				0% { transform: rotate(0deg); }
				100% { transform: rotate(360deg); }
			}
			
			.error-message {
				padding: 20px;
				color: #721c24;
				background-color: #f8d7da;
				border: 1px solid #f5c6cb;
				border-radius: 8px;
				margin: 10px 0;
				text-align: center;
			}
			
			/* 移动端适配样式 */
			@media (max-width: 768px) {
				/* 顶部导航 */
				.topview_1 {
					padding: 10px;
					flex-direction: column;
					align-items: center;
				}
				
				/* 返回按钮移动端样式 */
				#backButton {
					width: 100%;
					margin-bottom: 10px;
				}
				
				/* 评分按钮移动端样式 */
				.flex.items-center {
					flex-direction: column;
					align-items: flex-start;
				}
				
				.flex.items-center button {
					margin-left: 0;
					margin-top: 0.5rem;
				}
				
				.logo {
					margin-bottom: 10px;
				}
				
				.loginview {
					width: 100%;
					justify-content: space-between;
				}
				
				.ssview {
					width: 60%;
				}
				
				/* 导航菜单 */
				.menu-toggle {
					display: block;
					position: absolute;
					top: 15px;
					right: 15px;
					background: none;
					border: none;
					font-size: 24px;
					color: #A65D57;
					cursor: pointer;
					z-index: 1000;
				}
				
				.topview_2 .itembox {
					flex-direction: column;
					max-height: 0;
					overflow: hidden;
					transition: max-height 0.3s ease;
				}
				
				.topview_2 .itembox.expanded {
					max-height: 300px;
				}
				
				.topview_2 .menuitem {
					width: 100%;
					text-align: left;
					padding: 10px 15px;
					border-bottom: 1px solid rgba(0,0,0,0.05);
				}
				
				/* 内容区域 */
				.content {
					padding: 10px;
				}
				
				.contenttitlebox {
					overflow-x: auto;
					white-space: nowrap;
					padding: 10px 0;
				}
				
				/* 课件页面布局 */
				.kjtop {
					flex-direction: column;
				}
				
				.jttopitem {
					width: 100%;
					margin-bottom: 10px;
				}
				
				.kjname {
					font-size: 16px;
				}
				
				.kjzz {
					flex-wrap: wrap;
				}
				
				.kjzz label {
					margin-bottom: 5px;
				}
				
				/* 课件窗口 */
				.kjbox {
					flex-direction: column;
				}
				
				.kjview {
					width: 100%;
					margin-bottom: 15px;
				}
				
				.kjdg {
					width: 100%;
					max-height: none;
				}
				
				/* 课程详情 */
				.kjjj {
					margin-top: 15px;
				}
				
				.topkjjjs {
					overflow-x: auto;
					white-space: nowrap;
					padding-bottom: 10px;
				}
				
				.topkjjjs div {
					padding: 8px 15px;
					font-size: 14px;
				}
				
				/* 评价区域 */
				.pinjiabox {
					padding: 0 10px;
				}
				
				.pjbbb {
					width: 90%;
					max-width: 90%;
				}
				
				.xxbox {
					padding: 10px;
				}
				
				/* 全屏预览 */
				#qp {
					padding: 10px;
				}
				
				#qp img {
					max-width: 100%;
					max-height: 80vh;
				}
				
				/* 知识点标签 */
				.zsdlabel {
					margin: 3px;
					font-size: 12px;
				}
				
				/* 底部 */
				footer .yqlj .box {
					flex-direction: column;
				}
				
				footer .yqlj .box a {
					margin: 5px 0;
				}
				
				/* 返回顶部按钮 */
				#backtop {
					position: fixed;
					bottom: 20px;
					right: 20px;
					z-index: 999;
					width: 40px;
					height: 40px;
					background: rgba(166, 93, 87, 0.8);
					border-radius: 50%;
					display: flex;
					flex-direction: column;
					justify-content: center;
					align-items: center;
					box-shadow: 0 2px 10px rgba(0,0,0,0.2);
				}
				
				#backtop div {
					font-size: 10px;
					color: white;
				}
				
				#backtop img {
					width: 15px;
					height: 15px;
				}
				
				/* 文档容器的移动端适配 */
				.doc-container {
					height: 500px;
					width: 95% !important;
					margin: 0 auto !important;
					border-radius: 0 0 4px 4px;
				}
				
				/* 文档工具栏移动端适配 */
				.doc-toolbar {
					width: 95% !important;
					padding: 8px 10px;
					font-size: 0.9rem;
					margin: 0 auto;
				}
				
				.audio-player {
					height: 180px;
					padding: 15px;
				}
				
				.audio-player .play-btn {
					width: 40px;
					height: 40px;
					font-size: 16px;
				}
				
				.doc-nav {
					bottom: 70px !important;
					right: 15px !important;
					font-size: 12px !important;
				}
				
				/* 评分按钮移动端样式 */
				.kjpfbtn {
					font-size: 0.8rem !important;
					padding: 0.4rem 0.6rem !important;
				}
				
				/* 移动端按钮容器样式 */
				.jttopitem:last-child {
					justify-content: space-between !important;
					margin-top: 10px;
				}
				
				/* PDF阅读器的移动端适配 */
				.pdfbox {
					width: 100% !important; 
					height: 70vh !important;
					padding: 0 !important;
					margin: 0 auto;
				}
				
				.panel-body.pdfbox object,
				.panel-body.pdfbox iframe,
				.panel-body.pdfbox embed {
					width: 100% !important;
					max-width: 100% !important;
					height: 100% !important;
					margin: 0 auto;
				}
				
				/* DOCX阅读器的移动端适配 */
				.docx-viewer {
					padding: 10px;
				}
				
				.docx-viewer .page {
					padding: 12px;
					margin: 0 auto 15px auto;
					max-width: 98% !important;
					box-sizing: border-box;
					transform-origin: top center;
					overflow-x: auto;
				}
				
				/* 文档内容自适应处理 */
				.docx-viewer table {
					max-width: 100%;
					display: block;
					overflow-x: auto;
					white-space: normal;
					font-size: 0.9em;
				}
				
				/* 确保文档内容自适应移动端屏幕 */
				.docx-viewer p, 
				.docx-viewer span,
				.docx-viewer div {
					max-width: 100%;
					word-break: break-word;
					overflow-wrap: break-word;
				}
				
				.docx-viewer img {
					max-width: 100%;
					height: auto !important;
				}
				
				/* 优化PDF和DOCX渲染 */
				.doc-container {
					transform-origin: top center;
				}
				
				/* 设置PDF文档适应移动屏幕 */
				.panel-body.pdfbox object,
				.panel-body.pdfbox iframe,
				.panel-body.pdfbox embed {
					max-width: 100% !important;
				}
				
				/* 确保预览区域居中 */
				.kjview {
					display: flex;
					flex-wrap: nowrap;
					/* flex-direction: column; */
					flex-direction: row-reverse;
					align-items: center;
				}
			}
			
			/* 超小屏幕优化 */
			@media (max-width: 480px) {
				/* PDF浏览器 */
				.tcbox {
					width: 98% !important;
					max-width: 98% !important;
					margin: 0 auto !important;
				}
				
				.pdfbox {
					width: 100% !important;
					height: 85vh !important;
				}
				
				/* 课件详情 */
				.kjboxtitle {
					font-size: 14px;
				}
				
				.jj p {
					font-size: 13px;
				}
				
				/* 操作按钮 */
				.czbox div label {
					padding: 6px 12px;
					margin: 0 3px;
					font-size: 12px;
				}
				
				/* 课程标签 */
				.zsdlabel {
					padding: 4px 8px;
					font-size: 11px;
					margin: 2px;
				}
				
				.zsdlabel img {
					width: 14px;
					padding-right: 5px;
				}
				
				/* 评分区域 */
				.xx {
					width: 20px;
					height: 20px;
					background-size: contain;
				}
				
				.xxbox div span {
					font-size: 12px;
					width: 80px;
				}
				
				.xxbtnview div {
					padding: 6px 12px;
					font-size: 12px;
				}
				
				/* 文档容器更小屏幕适配 */
				.doc-container {
					height: 450px !important;
					border-radius: 0;
					border-left: none;
					border-right: none;
				}
				
				.docx-viewer .page {
					padding: 8px;
					border: none;
					box-shadow: none;
					border-bottom: 1px solid #eee;
				}
				
				/* 改善在超小屏幕上的文档阅读体验 */
				.docx-viewer p, 
				.docx-viewer span, 
				.docx-viewer div {
					word-wrap: break-word;
					max-width: 100%;
				}
				
				.docx-viewer img {
					max-width: 100%;
					height: auto !important;
				}
				
				/* 缩放控制按钮位置优化 */
				.doc-toolbar button {
					padding: 4px 8px;
					font-size: 12px;
				}
			}
		</style>
	</head>
	<body class="index">
		<div class="tcbox" id="tcbox">

			<div class="topviewsss">
				<a onclick="closetc()">关闭</a>
				<label id="pdfname"></label>
			</div>
			<div class="panel-body pdfbox">
				<a id="pdf" class="media" href=""></a>
			</div>
		</div>
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<button class="menu-toggle" id="menuToggle" aria-label="菜单切换">
					<i class="fas fa-bars"></i>
				</button>
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">

				</div>
			</div>
		</div>
		<div class="contenttitlebox">
			<div>
				<a id="zxxx" href="onlinelearning.html">在线学习</a>
				<img src="img/jtright.png" />
				<a href="onlinelearning3.html" id="hsname"></a>
				<img src="img/jtright.png" />
				<label class="aa" id="titlesss"></label>
			</div>
		</div>
		<div class="content">
			<div class="kjtop">
				<div class="jttopitem">
					<div class="flex items-center">
						<div class="kjname" id="kjname"></div>
						<button onclick="showpfbox()" class="ml-3 inline-flex items-center text-sm text-gray-600 hover:text-gray-800 transition-colors duration-300">
							<svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 text-amber-500" viewBox="0 0 20 20" fill="currentColor">
								<path d="M9.049 2.927c.3-.921 1.603-.921 1.902 0l1.07 3.292a1 1 0 00.95.69h3.462c.969 0 1.371 1.24.588 1.81l-2.8 2.034a1 1 0 00-.364 1.118l1.07 3.292c.3.921-.755 1.688-1.54 1.118l-2.8-2.034a1 1 0 00-1.175 0l-2.8 2.034c-.784.57-1.838-.197-1.539-1.118l1.07-3.292a1 1 0 00-.364-1.118l-2.8-2.034c-.783-.57-.38-1.81.588-1.81h3.461a1 1 0 00.951-.69l1.07-3.292z" />
							</svg>
							<span class="ml-1">评分</span>
						</button>
					</div>
					<div class="kjzz">
						<label><img src="img/kjzz.png" /><span id="zz"></span></label>
						<label><img src="img/kjyj.png" /><span id="yj"></span></label>
						<label><img src="img/kjpf.png" /><span id="pf"></span></label>
					</div>
				</div>
				<div class="jttopitem" style="display: flex; justify-content: flex-end; align-items: center;">
					<button id="backButton" onclick="goBackToResourceList()" class="inline-flex items-center px-3 py-2 border border-gray-300 shadow-sm text-sm leading-4 font-medium rounded-md text-gray-700 bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-indigo-500 transition-all duration-300">
						<svg class="-ml-0.5 mr-2 h-4 w-4" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
							<path fill-rule="evenodd" d="M9.707 16.707a1 1 0 01-1.414 0l-6-6a1 1 0 010-1.414l6-6a1 1 0 011.414 1.414L5.414 9H17a1 1 0 110 2H5.414l4.293 4.293a1 1 0 010 1.414z" clip-rule="evenodd" />
						</svg>
						返回列表
					</button>
				</div>
			</div>

			<div id="bbbb2" class="kjbox">
				<div class="kjview">
					<img id="img" src="" />
					<div class="topbag">
						这是一段课件名称
					</div>
					<div class="czbox" id="ppt">
						<img onclick="qpshow()" src="img/qp.png" />
						<div>
							<label onclick="syy()">上一页</label>
							<label onclick="xyy()">下一页</label>
						</div>
					</div>
					<div class="czbox2" id="video">
						<video autoplay controls id="videos" width="100%" controlsList="nodownload"></video>
					</div>
				</div>
				<div class="kjdg" id="pptbox">
					<div class="kjdgname">课件大纲</div>
					<!-- <div class="kjss">
						<div><img src="img/ss.png" />
							<input placeholder="请输入关键词" type="text" />
						</div>
					</div> -->
					<div class="kjlist" id="list">

					</div>
				</div>
				<div class="kjdg" id="videobox">
					<div class="kjdgname">视频列表</div>
					<div class="kjlist" id="list2">

					</div>
				</div>
			</div>
			<div class="kjjj">
				<div class="topkjjjs" id="selectbox">
					<div onclick="selects(this)" data-id="1" class="aaakj"><img id="xqimg" src="img/kcxq1.png" />课程详情
					</div>
					<div onclick="selects(this)" data-id="2"><img id="zsdimg" src="img/zsd2.png" />知识点</div>
					<div onclick="selects(this)" data-id="3"><img id="pjimg" src="img/kcpf2.png" />课程评价</div>
				</div>
				<div id="pf1" class="kjxqbox">
					<div class="kjboxtitle">课程简介:</div>
					<div class="jj">
						<p id="jj"></p>
					</div>
					<div id="kcgl">
						<div id="kj111">
							<div class="kjboxtitle">课件:</div>
							<div class="abq">
								<label onclick="showsss(this)" id="kj">查看详情</label>
							</div>
						</div>
						<div id="kj222">
							<div class="kjboxtitle">课后测试:</div>
							<div class="abq">
								<label id="stshow" onclick="showpdf(this)">查看详情</label>
							</div>
							<!-- <a href="" download="" id="stxz">立即下载</a> -->
						</div>
					</div>
				</div>
				<div id="pf2" style="display: none;" class="kjpfbox">
					<div class="zsdbox" style="padding: 0.78125rem;" id="zsdbox">

					</div>
				</div>

				<div id="pf3" style="display: none;" class="kjpfbox">
					<div class="pjtitle">课程评价详情</div>
					<div class="xxbox" id="xxbox2">


					</div>
				</div>

			</div>
		</div>
		<div class="pinjiabox">
			<div class="pjbbb">
				<div class="topbags">
					<img src="img/kjpf2.png" />
					课件评分
				</div>
				<div class="xxbox" id="xxbox">

				</div>
				<div class="str" id="pfwz" style="display: none;">您已评无法重复提交评分！</div>
				<div class="xxbtnview" id="wpf">
					<div onclick="pinfensubmit()">确定</div>
					<div onclick="hidepfbox()">取消</div>
				</div>
				<div class="xxbtnview" style="display: none;" id="ypf">
					<div class="gbgbh" onclick="hidepfbox()">关闭</div>
				</div>
			</div>
		</div>
		<div id="qp" onclick="hideqp()">
			<img id="img2" src="" />
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html" style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		
		<!-- 全局样式定义：确保文档查看器在所有类型都保持居中和良好尺寸 -->
		<style>
			/* PDF和文档查看器通用样式 - 应用于FileViewer和PDF预览 */
			.file-viewer-container {
				width: 90%;
				max-width: 1000px;
				margin: 15px auto;
				border-radius: 8px;
				box-shadow: 0 2px 10px rgba(0,0,0,0.1);
				overflow: hidden;
			}
			
			/* 让kjview容器内的所有查看器组件居中 */
			.kjview {
				display: flex;
				flex-direction: column;
				align-items: center;
				width: 100%;
			}
			
			.kjview > * {
				max-width: 100%;
				width: 90%;
				margin: 0 auto;
			}
			
			/* 确保文档播放器占满空间但保持居中 */
			.kjview .doc-container,
			.kjview .doc-toolbar,
			.kjview .audio-player,
			.kjview embed,
			.kjview iframe,
			.kjview object {
				width: 90% !important;
				max-width: 1000px !important;
				margin: 0 auto !important;
			}
			
			/* 增强文档内容自适应 */
			.docx-viewer p, 
			.docx-viewer span, 
			.docx-viewer div, 
			.docx-viewer td, 
			.docx-viewer th, 
			.docx-viewer li {
				word-break: break-word !important;
				overflow-wrap: break-word !important;
				max-width: 100% !important;
			}
			
			.docx-viewer table {
				table-layout: auto !important;
				width: auto !important;
				max-width: 100% !important;
			}
			
			/* 防止表格撑破容器 */
			.docx-viewer .page {
				overflow-x: auto !important;
			}
			
			/* 横屏模式适配 */
			@media screen and (orientation: landscape) and (max-width: 920px) {
				.docx-viewer .page {
					max-width: 99.5% !important;
					padding: 8px !important;
				}
				
				.file-viewer-container,
				.kjview > *,
				.kjview .doc-container,
				.kjview .doc-toolbar,
				.kjview .audio-player,
				.kjview embed,
				.kjview iframe,
				.kjview object,
				.panel-body.pdfbox,
				.panel-body.pdfbox object,
				.panel-body.pdfbox iframe,
				.panel-body.pdfbox embed {
					width: 99.5% !important;
					max-width: 99.5% !important;
				}
				
				/* 横屏模式微调字体 */
				.docx-viewer {
					font-size: 0.95em !important;
				}
				
				.docx-viewer table {
					font-size: 0.9em !important;
				}
				
				/* 横屏状态下减少边距 */
				.docx-viewer p, 
				.docx-viewer span, 
				.docx-viewer div, 
				.docx-viewer td, 
				.docx-viewer th, 
				.docx-viewer li {
					margin: 0.2em 0 !important;
				}
			}
			
			/* 移动端适配 */
			@media (max-width: 768px) {
				.file-viewer-container,
				.kjview > *,
				.kjview .doc-container,
				.kjview .doc-toolbar,
				.kjview .audio-player,
				.kjview embed,
				.kjview iframe,
				.kjview object {
					width: 98% !important;
					max-width: 98% !important;
					margin-left: auto !important;
					margin-right: auto !important;
				}
				
				/* 移动端字体缩小 */
				.docx-viewer {
					font-size: 0.9em;
				}
				
				/* 移动端表格处理 */
				.docx-viewer table {
					font-size: 0.85em;
					width: 98% !important;
					max-width: 98% !important;
				}
				
				/* 确保文档阅读区域更宽 */
				.docx-viewer .page {
					max-width: 98% !important;
					padding: 10px !important;
				}
				
				/* 调整PDF查看器宽度 */
				.panel-body.pdfbox {
					width: 98% !important;
					max-width: 98% !important;
				}
				
				/* 优化文档内部元素 */
				.docx-viewer p, 
				.docx-viewer span, 
				.docx-viewer div, 
				.docx-viewer td, 
				.docx-viewer th, 
				.docx-viewer li {
					max-width: 98% !important;
				}
				
				/* 在特小屏幕上进一步调整 */
				@media (max-width: 480px) {
					.docx-viewer {
						font-size: 0.85em;
					}
					
					.docx-viewer table {
						font-size: 0.8em;
					}
					
					/* 特小屏幕进一步减少留白 */
					.file-viewer-container,
					.kjview > *,
					.kjview .doc-container,
					.kjview .doc-toolbar,
					.kjview .audio-player,
					.kjview embed,
					.kjview iframe,
					.kjview object,
					.docx-viewer .page,
					.panel-body.pdfbox {
						width: 99% !important;
						max-width: 99% !important;
					}
				}
			}
		</style>
		<script>
			function closetc() {
				$("#tcbox").hide()
			}

			function showsss(item) {
				window.location.href = "onlinelearning4.html?id=" + $(item).attr("data-id")
			}

			function hidepfbox() {
				$(".pinjiabox").attr("style", "display: none")
			}

			function showpfbox() {
				$(".pinjiabox").attr("style", "display: flex")
			}

			function showpdf(item) {
				$.ajax({
					url: baseurl + "/course/view/" + $(item).attr("data-cid"),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {}
				})
				$.ajax({
					url: baseurl + "/course/meta/" + $(item).attr("data-id"),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							$("#pdfname").html(res.data.cmsResourcesCourseMetaList[0].attachName)
							$("#tcbox").show()
							$("#pdf").attr("href", baseurl + res.data.cmsResourcesCourseMetaList[0].attachPath)
							$('a.media').media()
						}
					}
				})
			}
			let imgindex = 0
			let imglist = null
			let userinfo = sessionStorage.getItem("userinfo")
			$(function() {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				if (userinfo) {
					//已登录 则显示用户信息
					$("#login").hide()
					$("#user").show()
					$("#user").html(JSON.parse(userinfo).name)
					$("#edit").show()
					getclass('onlinelearning.html')
					
					// 初始化文件查看器
					window.FileViewer.init({
						containerSelector: '#bbbb2',
						previewContainerSelector: '.kjview',
						baseUrl: baseurl
					});
					
					getinfo()
					getfooterlink()
					getclassid()
				} else {
					//未登录 则显示登录按钮
					$("#login").show()
					$("#user").hide()
					$("#edit").hide()
					window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html'
				}
			})

			function xzxx(item) {
				let itemid = $(item).attr("data-id")
				//1 0星  2 半星  3  满星
				//当点击一个星星时  这个星包括前面的星全部满星 同时 data-id=3  后面的星星全部取消 data-id = 1
				if (itemid == 1) {
					$(item).attr('class', 'xx xx3').attr("data-id", "3")
					$(item).prevAll('label').attr("class", "xx xx3").attr("data-id", "3")
				} else if (itemid == 3) {
					//当点击的星星是满星时   这个星星后面的所有星星取消 并且 data-id =1
					$(item).attr('class', 'xx xx1').attr("data-id", "1")
					$(item).nextAll('label').attr("class", "xx xx1").attr("data-id", "1")
				}

				//黑星 0分   红星 1分   半星 0.5分
				let num = 0
				let allxx = $(item).prevAll('label')
				for (let i = 0; i < allxx.length; i++) {
					if ($(allxx[i]).attr("data-id") == '1') {
						num += 0
					} else if ($(allxx[i]).attr("data-id") == '3') {
						num += 1
					}
				}
				if ($(item).attr("data-id") == '1') {
					num += 0
				} else if ($(item).attr("data-id") == '3') {
					num += 1
				}
				// console.log(num)
				$(item).nextAll("div").html(num + '分')
			}

			function selects(item) {
				let allitem = $("#selectbox div")
				for (let i = 0; i < allitem.length; i++) {
					$(allitem[i]).attr("class", "")
				}
				$(item).attr("class", "aaakj")
				let thisid = $(item).attr("data-id")
				if (thisid == 1) {
					$("#xqimg").attr("src", "img/kcxq1.png")
					$("#pjimg").attr("src", "img/kcpf2.png")
					$("#zsdimg").attr("src", "img/zsd2.png")
					$("#pf1").show()
					$("#pf2").hide()
					$("#pf3").hide()
				} else if (thisid == 2) {
					$("#xqimg").attr("src", "img/kcxq2.png")
					$("#pjimg").attr("src", "img/kcpf2.png")
					$("#zsdimg").attr("src", "img/zsd1.png")
					$("#pf1").hide()
					$("#pf2").show()
					$("#pf3").hide()
				} else if (thisid == 3) {
					$("#xqimg").attr("src", "img/kcxq2.png")
					$("#pjimg").attr("src", "img/kcpf1.png")
					$("#zsdimg").attr("src", "img/zsd2.png")
					$("#pf1").hide()
					$("#pf2").hide()
					$("#pf3").show()
				}
			}

			function getclassid() {
				$.ajax({
					url: baseurl + "/category/teaching",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							classid = res.data[0].id
							pflid = res.data[0].parentId
							$("#hsname").html(res.data.name)
						}
					}
				})
			}
			let time1 = Date.now()
			let classid = null
			let pflid = null
			let xkid = null
			
			let videotime = null

			var vid = document.getElementById("videos");
			vid.onloadedmetadata = function() {
				// console.log('metadata loaded!');
				// console.log(vid.duration); //打印时长
				videotime = vid.duration
			};
		
			

			function getfooterlink() {
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item) => {
								html += '<a href="' + item.url + '" target="_blank">' + item.name + '</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}

			function pinfensubmit() {
				let all = $("#xxbox div")
				let json = []
				for (let i = 0; i < all.length; i++) {
					json.push({
						courseId: getUrlParam('id'),
						evaluateId: $(all[i]).attr("data-id"),
						score: $(all[i]).find(".xx3").length
					})
				}
				// console.log(json)
				hidepfbox()
				$.ajax({
					url: baseurl + "/course/evaluate/add",
					type: 'POST',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					data: JSON.stringify(json),
					dataType: 'json',
					success: (res) => {
						getinfo(true)
					}
				})

			}
			let looklist = []//已看过的图片
			let allimglist = []//全部的图片
			let lookcount = 0 //已经看过的数量
			
			function getinfo(isimg) {
				$.ajax({
					url: baseurl + "/course/meta/" + getUrlParam('id'),
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							$("#pf").html(parseFloat(res.data.listCourseEvaluate[3].zh).toFixed(1))
							addview(res.data.courceId)
							let hhhxx = ""
							res.data.listCourseEvaluate.map((item) => {
								if (!item.score) {
									item.score = 0
								}
								if (item.name) {
									hhhxx += '<div><span>' + item.name + ':</span>'
									for (let i = 0; i < 5; i++) {
										if (i < parseInt(item.score)) {
											hhhxx += '<label class="xx xx3"></label>'
										} else {
											if (parseFloat(item.score) > 0) {
												if (parseFloat(item.score) % parseInt(item.score) === 0) {
													hhhxx += '<label class="xx xx1"></label>'
												} else {
													hhhxx += '<label class="xx xx2"></label>'
												}
											} else {
												hhhxx += '<label class="xx xx1"></label>'
											}
										}
									}
									hhhxx += '<div class="fs">' + parseFloat(item.score).toFixed(1) +
										'分</div></div>'
								} else {
									hhhxx += '<div><span>资源综合评分:</span>'
									for (let i = 0; i < 5; i++) {
										if (i < parseInt(item.zh)) {
											hhhxx += '<label class="xx xx3"></label>'
										} else {
											if (i < parseInt(item.zh)) {
												hhhxx += '<label class="xx xx3"></label>'
											} else {
												if (parseFloat(item.zh) > 0) {
													if (parseFloat(item.zh) % parseInt(item.zh) === 0) {
														hhhxx += '<label class="xx xx1"></label>'
													} else {
														hhhxx += '<label class="xx xx2"></label>'
													}
												} else {
													hhhxx += '<label class="xx xx1"></label>'
												}
											}
										}
									}
									hhhxx += '<div class="fs">' + parseFloat(item.zh).toFixed(1) +
										'分</div></div>'
								}
							})
							$("#xxbox2").html(hhhxx)
							if (res.data.isCourseEvaluate == 0) {
								let xxhtml = ''
								res.data.listCurrentCourseEvaluate.map((item) => {
									xxhtml += '<div data-id="' + item.id + '"><span>'
									xxhtml += item.name
									xxhtml += ':</span>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label>' +
										'<label class="xx xx1" onclick="xzxx(this)" data-id="1"></label></div>'
								})
								$("#xxbox").html(xxhtml)
							} else {
								//已评分
								$("#ypf").show()
								$("#wpf").hide()
								$("#pfwz").show()
								let xxhtml = ''
								res.data.listCurrentCourseEvaluate.map((item) => {
									xxhtml += '<div><span>'
									xxhtml += item.name
									xxhtml += ':</span>'
									for (let i = 0; i < 5; i++) {
										if (i < item.score) {
											xxhtml += '<label class="xx xx3"></label>'
										} else {
											xxhtml += '<label class="xx xx1"></label>'
										}
									}
									xxhtml += '</div>'
								})
								$("#xxbox").html(xxhtml)
							}
							if (!isimg) {
								xkid = res.data.projectId
								$("#kjname").html(res.data.title)
								document.title = res.data.title
								$("#titlesss").html(res.data.title)
								$("#zz").html(res.data.author)
								$("#yj").html(res.data.view)
								$("#jj").html(res.data.introduction)
								
								let kjnum = 0
								let kj2num = 0
								
								// 初始化文件查看器
								window.FileViewer.init({
									containerSelector: '#bbbb2',
									previewContainerSelector: '.kjview',
									baseUrl: baseurl
								});
								
								const fileData = res.data.cmsResourcesCourseMetaList[0];
								const fileType = fileData.attachType.toLowerCase();
								const fileName = fileData.attachName;
								const fileUrl = baseurl + fileData.attachPath;
								
								// 处理课件关联
								res.data.cmsResourcesCourseMetaList.map((item) => {
									if (item.attachType == 'ppt' || item.attachType == 'pptx') {
										$("#kj").attr("data-id", item.id);
										kjnum += 1;
									}
								});
								if (kjnum === 0) {
									$("#kj111").hide();
								}

								res.data.cmsResourcesCourseMetaList.map((item) => {
									if (item.attachType == 'pdf') {
										$("#stshow").attr("data-id", item.id);
										$("#stshow").attr("data-cid", item.courceId);
										kj2num += 1;
									}
								});
								if (kj2num === 0) {
									$("#kj222").hide();
								}
								
								// 显示基本布局
								$("#bbbb2").show();
								$("#bbbb2").css("display", "flex");
								
								// 根据文件类型处理不同的显示逻辑
								if (fileType === 'mp4') {
									// 视频文件处理
									$("#video").show();
									$("#videobox").show();
									$("#ppt").hide();
									$("#pptbox").hide();
									
									$("#videos").attr("src", fileUrl);
									
									let html = '<div class="kjitem"><img class="fm" src="' + 
										baseurl + fileData.coverPath + '" /><div class="kjbt">1</div>' + 
										'<img class="zt" src="img/yxx.png" /></div>';
									
									$("#list2").html(html);
								} 
								else if (fileType === 'mp3') {
									// 音频文件处理
									$("#video").hide();
									$("#videobox").hide();
									$("#ppt").hide();
									$("#pptbox").hide();
									$("#kcgl").hide();
									
									// 创建现代简洁的音频播放器UI
									const audioPlayerContainer = $('<div class="modern-audio-player"></div>');
									
									// 使用资源封面图片代替默认图标
									let albumArtHtml = '';
									// 尝试使用封面路径或文件的封面路径
									let coverUrl = '';
									if (res.data.coverPath) {
										coverUrl = baseurl + res.data.coverPath;
									} else if (fileData.coverPath) {
										coverUrl = baseurl + fileData.coverPath;
									}
									
									if (coverUrl) {
										// 如果有封面图片，使用封面图片作为背景
										console.log("使用封面图片:", coverUrl);
										albumArtHtml = '<div class="album-art" style="background-image: url(\'' + coverUrl + '\'); background-size: cover; background-position: center;"></div>';
									} else {
										// 否则使用默认图标
										console.log("未找到封面图片，使用默认图标");
										albumArtHtml = '<div class="album-art"><div class="audio-icon"></div></div>';
									}
									const albumArt = $(albumArtHtml);
									
									// 使用资源标题作为音频名称
									const audioInfo = $('<div class="audio-info"><div class="audio-title">' + res.data.title + '</div><div class="audio-author">' + $("#zz").text() + '</div></div>');
									const progressContainer = $('<div class="audio-progress-container"><div class="audio-time current">00:00</div><div class="progress-bar-container"><div class="progress-bar"></div></div><div class="audio-time duration">00:00</div></div>');
									const controls = $('<div class="audio-controls"><button class="btn-prev"><svg viewBox="0 0 24 24"><path d="M6,18V6h2v12H6z M9.5,12l8.5,6V6L9.5,12z"></path></svg></button><button class="btn-play"><svg class="play-icon" viewBox="0 0 24 24"><path d="M8,5.14v14l11-7L8,5.14z"></path></svg><svg class="pause-icon" viewBox="0 0 24 24"><path d="M6,19h4V5H6V19z M14,5v14h4V5H14z"></path></svg></button><button class="btn-next"><svg viewBox="0 0 24 24"><path d="M16,18h2V6h-2V18z M6,18l8.5-6L6,6V18z"></path></svg></button></div>');
									
									// 组装播放器
									audioPlayerContainer.append(albumArt).append(audioInfo).append(progressContainer).append(controls);
									$(".kjview").empty().addClass("modern-player-container").append(audioPlayerContainer);
									
									// 创建音频元素并设置自动播放
									const audioElement = $('<audio autoplay muted></audio>');
									audioElement.attr('src', fileUrl);
									audioPlayerContainer.append(audioElement);
									
									// 音频播放器逻辑
									const audio = audioElement[0];
									
									// 先静音自动播放（浏览器策略允许），然后尝试取消静音并播放
									setTimeout(function() {
										console.log("尝试自动播放音频...");
										audio.muted = false;
										audio.play()
											.then(() => {
												console.log("自动播放成功！");
												playBtn.addClass('playing');
												albumArt.addClass('rotating');
											})
											.catch(err => {
												console.warn("无法自动播放（可能需要用户交互）:", err);
												// 显示轻提示，不打断用户
												setTimeout(() => {
													cocoMessage.info("点击播放按钮开始播放", 3000);
												}, 1000);
											});
									}, 500);
									const playBtn = controls.find('.btn-play');
									const progressBar = progressContainer.find('.progress-bar');
									const currentTimeEl = progressContainer.find('.current');
									const durationEl = progressContainer.find('.duration');
									
									// 初始设置暂停状态，等待加载完成后自动播放
									playBtn.removeClass('playing');
									
									// 音频控制事件
									playBtn.on('click', function() {
										if (audio.paused) {
											audio.play().then(() => {
												playBtn.addClass('playing');
												albumArt.addClass('rotating');
											}).catch(err => {
												console.error('播放失败:', err);
												cocoMessage.error('音频播放失败，请稍后重试');
											});
										} else {
											audio.pause();
											playBtn.removeClass('playing');
											albumArt.removeClass('rotating');
										}
									});
									
									// 音频加载完成后自动播放
									audio.addEventListener('canplaythrough', function() {
										if (!audio.paused) return; // 避免重复播放
										audio.play().then(() => {
											playBtn.addClass('playing');
											albumArt.addClass('rotating');
										}).catch(err => {
											console.warn('自动播放失败:', err);
											// 许多浏览器会阻止自动播放，显示提示
											cocoMessage.info('点击播放按钮开始播放');
										});
									});
									
									// 进度条更新
									audio.addEventListener('timeupdate', function() {
										const percent = (audio.currentTime / audio.duration) * 100;
										progressBar.css('width', percent + '%');
										currentTimeEl.text(formatTime(audio.currentTime));
									});
									
									// 音频加载后设置总时长
									audio.addEventListener('loadedmetadata', function() {
										durationEl.text(formatTime(audio.duration));
									});
									
									// 进度条点击事件
									progressContainer.find('.progress-bar-container').on('click', function(e) {
										const percent = (e.offsetX / $(this).width());
										audio.currentTime = percent * audio.duration;
									});
									
									// 上一曲/下一曲按钮（此处为装饰，可根据需要实现功能）
									controls.find('.btn-prev, .btn-next').on('click', function() {
										// 这里可以实现切换歌曲功能，现在只是提示
										cocoMessage.info('功能开发中');
									});
									
									// 时间格式化函数
									function formatTime(seconds) {
										const min = Math.floor(seconds / 60);
										const sec = Math.floor(seconds % 60);
										return (min < 10 ? '0' + min : min) + ':' + (sec < 10 ? '0' + sec : sec);
									}
									
									// 播放器初始化完成后的额外操作
									// 为整个播放器添加点击事件，点击封面也可以播放/暂停
									albumArt.on('click', function() {
										playBtn.trigger('click');
									});
									
									// 显示加载中状态
									const loadingIndicator = $('<div class="loading-indicator">准备播放中...</div>');
									albumArt.append(loadingIndicator);
									
									// 音频加载成功时移除加载指示器
									audio.addEventListener('canplay', function() {
										loadingIndicator.fadeOut(300, function() {
											$(this).remove();
										});
									});
									
									// 播放错误时处理
									audio.addEventListener('error', function() {
										loadingIndicator.text('加载失败，请重试').addClass('error');
										cocoMessage.error('音频加载失败');
									});
								}
								else if (fileType === 'pdf' || fileType === 'doc' || fileType === 'docx') {
									// 文档文件处理
									$("#video").hide();
									$("#videobox").hide();
									$("#ppt").hide();
									$("#pptbox").hide();
									$("#kcgl").hide();
									
									// 使用FileViewer显示文档，添加特殊样式确保居中
									$(".kjview").addClass("file-viewer-container");
									window.FileViewer.display(fileUrl, fileType, fileName, {
										containerClass: 'file-viewer-container',
										docContainerHeight: $(window).width() <= 768 ? '500px' : '600px',
										fitToWidth: true,
										enableResponsiveRendering: true
									});
								}
								else if (fileType === 'ppt' || fileType === 'pptx') {
									// 使用FileViewer处理PPT/PPTX文件
									$("#video").hide();
									$("#videobox").hide();
									$("#ppt").hide();
									$("#pptbox").hide();
									$("#kcgl").hide();
									
									// 准备完整的metadata数据给FileViewer
									const metadata = {
										attachListPath: fileData.attachListPath || []
									};
									
									if (fileData.attachListPath && fileData.attachListPath.length > 0) {
										// 如果有图片列表，使用FileViewer的showPPT功能
										window.FileViewer.display(fileUrl, fileType, fileName, metadata);
										
										// 同步更新图片列表到侧边栏（如果需要显示侧边栏）
										if (fileData.attachListPath.length > 1) {
											$("#pptbox").show();
										let html = "";
										imglist = fileData.attachListPath;
										
										fileData.attachListPath.map((item, index) => {
											html += '<div class="kjitem" onclick="showimg(this)" data-url2="'+item+'" data-url="' +
												baseurl + item + '" data-index="' + index +
												'"><img class="fm" src="' + baseurl + item +
													'" /><div class="kjbt">' + (index + 1) + 
												'</div><img class="zt" src="img/yxx.png" /></div>';
										});
										
										$("#list").html(html);
											$("#bbbb2").css("display", "flex");
										}
									} else {
										// 没有PPT图片列表时
										window.FileViewer.display(fileUrl, fileType, fileName);
									}
								} 
								else {
									// 其他未知类型文件的处理
									$("#video").hide();
									$("#videobox").hide();
									$("#ppt").hide();
									$("#pptbox").hide();
									$("#kcgl").hide();
									
									// 使用FileViewer提供下载链接
									$(".kjview").addClass("file-viewer-container");
									window.FileViewer.display(fileUrl, fileType, fileName, {
										containerClass: 'file-viewer-container',
										fitToWidth: true,
										enableResponsiveRendering: true
									});
								}
							}
						
							// 根据文件类型设置不同的学习记录处理方式
							const fileType = res.data.cmsResourcesCourseMetaList[0].attachType.toLowerCase();
							
							// 设置退出页面时的学习记录保存
							window.onbeforeunload = function() {
								if (JSON.parse(userinfo).roleName == '学生') {
									let time2 = Date.now();
									let value = time2 - time1;
									var days = parseInt(value / (1000 * 60 * 60 * 24));
									var hours = parseInt((value % (1000 * 60 * 60 * 24)) / (1000 * 60 * 60));
									var minutes = parseInt((value % (1000 * 60 * 60)) / (1000 * 60));
									var seconds = Math.floor((value % (1000 * 60)) / 1000);
									
									let rwid = null;
									if (getUrlParam('taskid')) {
										rwid = getUrlParam('taskid');
									}
								
									// 根据不同的文件类型设置不同的记录参数
									let totalInfo = 0;
									let progress = "";
									
									if (fileType === 'mp4') {
										// 视频文件学习记录
										totalInfo = videotime || 0;
									}
									else if (fileType === 'mp3') {
										// 音频文件学习记录
										totalInfo = value / 1000; // 使用学习时间作为总时长
									}
									else if (fileType === 'ppt' || fileType === 'pptx') {
										// PPT文件学习记录
										if (res.data.cmsResourcesCourseMetaList[0].attachListPath) {
											allimglist = res.data.cmsResourcesCourseMetaList[0].attachListPath;
											
											// 如果是首次加载，初始化记录
											if (!looklist || looklist.length === 0) {
												looklist = [allimglist[0]];
												lookcount = 1;
											}
											
											totalInfo = allimglist.length;
											progress = lookcount;
										}
									}
									else if (fileType === 'pdf' || fileType === 'doc' || fileType === 'docx') {
										// 文档文件学习记录
										totalInfo = value / 1000; // 使用学习时间作为总信息量
									}
								
									let json = {
										infoId: getUrlParam('id'), //信息id
										categoryId: pflid, //所属分类id
										totalInfo: totalInfo, //总时长或总页数
										positioning: days + "天" + hours + "时" + minutes + "分" + seconds + "秒", //学习了多久
										progress: progress, //进度
										type: '在线学习',
										learningTime: value, //学习时长
										sectionId: xkid, //学科ID
										taskId: rwid
									};
								
									fetch(baseurl + '/study/record/add', {
										method: 'POST',
										headers: {
											"Authorization": sessionStorage.getItem("header"),
											"Content-Type": "application/json"
										},
										body: JSON.stringify(json),
										keepalive: true
									});
									
									// 移除确认提示，使用更弱的提示方式，让用户可以直接离开
									try {
										// 如果有提示组件可用，就显示提示而不是弹窗
										if (window.cocoMessage) {
											cocoMessage.success("学习记录已保存", 1000);
										}
									} catch (e) {
										// 忽略任何错误，不影响用户离开页面
									}
								}
								// 不返回任何内容，避免弹出确认对话框
							};
							
							// 如果是PPT并有图片列表，初始化浏览记录
							if (fileType === 'ppt' || fileType === 'pptx') {
								if (res.data.cmsResourcesCourseMetaList[0].attachListPath) {
									allimglist = res.data.cmsResourcesCourseMetaList[0].attachListPath;
									looklist = [allimglist[0]];
									lookcount = 1;
								}
							}
						
							
						}


						let zsdhtml = ''

						let knowledge = res.data.knowledge
						// 添加空值检查，确保knowledge存在且非空
						if (knowledge && typeof knowledge === 'string') {
							knowledge.split(',').forEach((item) => {
								if (item != null && item != '') {
									zsdhtml += `<div class="zsdlabel"><img src="img/zsd.png"/>${item}</div>`
								}
							})
						} else {
							// console.log('知识点信息不存在或格式不正确')
							// 如果knowledge为空，可以添加默认文本或者留空
							// zsdhtml = '<div class="zsdlabel"><img src="img/zsd.png"/>暂无知识点</div>'
						}
						$("#zsdbox").html(zsdhtml)
						//<div class="zsdlabel"><img src="img/zsd.png"/>知识点1</div>
					}
				})
			}

			function showimg(item) {
				const fileViewer = window.FileViewer;
				const index = $(item).attr("data-index");
				const url = $(item).attr("data-url");
				
				// 更新主视图图片
				$("#img").attr("src", url);
				$("#img2").attr("src", url);
				
				// 更新当前索引
				imgindex = parseInt(index);
				
				// 标记为已浏览
				let ishave = 0;
				looklist.forEach((myurl) => {
					if (myurl == $(item).attr("data-url2")) {
						ishave += 1;
					}
				});
				
				if (ishave == 0) {
					looklist.push($(item).attr("data-url2"));
					lookcount += 1;
				}
				
				// 尝试触发FileViewer的PPT翻页
				try {
					// 查找并触发FileViewer中相应页面的按钮
					const container = document.querySelector('.kjview');
					if (container) {
						const buttons = container.querySelectorAll('button');
						if (buttons && buttons.length > 0) {
							// 找到相应的按钮并触发点击
							for (let i = 0; i < buttons.length; i++) {
								if (buttons[i].id === 'pptx-first' || 
									buttons[i].id === 'pptx-prev' || 
									buttons[i].id === 'pptx-next' || 
									buttons[i].id === 'pptx-last') {
									// 这里不直接触发，让FileViewer内部的渲染函数处理
								}
							}
						}
					}
				} catch (e) {
					console.warn('同步FileViewer状态失败:', e);
				}
			}

			function syy() {
				// console.log(imgindex)
				if (imgindex > 0) { //当前下标记 大于0则表示前面还有
					imgindex -= 1
					$("#img").attr("src", baseurl + imglist[imgindex])
					$("#img2").attr("src", baseurl + imglist[imgindex])
					
					let ishave = 0
					looklist.forEach((item)=>{
						if(item == allimglist[imgindex]){
							ishave += 1
						}
					})
					
					if(ishave == 0){
						//没有看过 则将这个图片添加到已经看过的列表
						looklist.push(allimglist[imgindex])
						lookcount += 1 //更新已看过的数量
					}else{
						//已经看过了 则不做处理
					}
				}
				// console.log('全部图片',allimglist)
				// console.log('已经看过的图片',looklist)
				// console.log('已看过的数量',lookcount)
			}

			function xyy() {
				// console.log(imgindex)
				if (imgindex < (imglist.length - 1)) { //当前下标记 小于 list长度-1  则表示后面还有
					imgindex += 1
					$("#img").attr("src", baseurl + imglist[imgindex])
					$("#img2").attr("src", baseurl + imglist[imgindex])
					
					let ishave = 0
					looklist.forEach((item)=>{
						if(item == allimglist[imgindex]){
							ishave += 1
						}
					})
					
					if(ishave == 0){
						//没有看过 则将这个图片添加到已经看过的列表
						looklist.push(allimglist[imgindex])
						lookcount += 1 //更新已看过的数量
					}else{
						//已经看过了 则不做处理
					}
				}
				// console.log('全部图片',allimglist)
				// console.log('已经看过的图片',looklist)
				// console.log('已看过的数量',lookcount)
			}

			// function getclass() {
			// 	$.ajax({
			// 		url: baseurl + "/web/category/all",
			// 		type: 'GET',
			// 		contentType: "application/json",
			// 		headers: {
			// 			"Authorization": sessionStorage.getItem("header")
			// 		},
			// 		dataType: 'json',
			// 		success: (res) => {
			// 			if (res.code == '200') {
			// 				let html = ""
			// 				for (let i = 0; i < res.data.length; i++) {
			// 					if (res.data[i].id == getUrlParam('id') || res.data[i].redirectUrl ==
			// 						'onlinelearning.html') {
			// 						$("#zxxx").attr('href', "onlinelearning.html?id=" + res.data[i].id)
			// 						$("#zxxx").html(res.data[i].name)
			// 						pflid = res.data[i].id
			// 						html += '<a class="menuitem active" href="' + res.data[i].redirectUrl + '?id=' +
			// 							res
			// 							.data[i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					} else {
			// 						html += '<a class="menuitem" href="' + res.data[i].redirectUrl + '?id=' + res
			// 							.data[
			// 								i].id + '"><label>' + res.data[i].name + '</label></a>'
			// 					}
			// 				}
			// 				$("#menubox").html(html)
			// 			}
			// 		}
			// 	})
			// }

			function getUrlParam(name) {
				var reg = new RegExp("(^|&)" + name + "=([^&]*)(&|$)"); //构造一个含有目标参数的正则表达式对象
				var r = window.location.search.substr(1).match(reg); //匹配目标参数
				if (r != null) return unescape(r[2]);
				return null; //返回参数值
			}

			function hideqp() {
				$("#qp").attr("style", "display: none;")
			}

			function qpshow() {
				$("#qp").attr("style", "display: flex;")
			}

			function addview(id) {
				$.ajax({
					url: baseurl + "/course/view/" + id,
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {}
				})
			}

			// 返回上一页函数
			function goBack() {
				window.history.back();
			}

			// 返回到资源列表页面
			function goBackToResourceList() {
				// 检查是否有保存的筛选状态
				const savedFilterState = sessionStorage.getItem('resourceFilterState');
				
				if (savedFilterState) {
					try {
						// 使用保存的完整筛选状态
						const filterState = JSON.parse(savedFilterState);
						
						// 构建返回URL
						let url = "onlinelearning3.html";
						
						// 添加基本参数
						url += "?categoryId=" + (filterState.categoryId || "");
						url += "&currentPage=" + (filterState.pageIndex || 1);
						
						// 添加课程ID
						if (filterState.courseId) url += "&courseId=" + filterState.courseId;
						
						// 添加章节ID
						if (filterState.chapterId) url += "&chapterId=" + filterState.chapterId;
						
						// 添加节ID
						if (filterState.nodeId) url += "&nodeId=" + filterState.nodeId;
						
						// 添加小节ID
						if (filterState.barId) url += "&barId=" + filterState.barId;
						
						// 添加文件类型
						if (filterState.fileType) url += "&fileType=" + filterState.fileType;
						
						// 添加属性ID
						if (filterState.attributeId) url += "&attributeId=" + filterState.attributeId;
						
						// 添加类型ID
						if (filterState.typeId) url += "&typeId=" + filterState.typeId;
						
						// 添加类别ID (mtype)
						if (filterState.activeFilters && filterState.activeFilters.categoryOption) {
							// 防止"111"(全部)作为参数
							if (filterState.activeFilters.categoryOption !== "111") {
								url += "&mtype=" + filterState.activeFilters.categoryOption;
							}
						}
						
						// 添加排序方向
						url += "&sortDesc=" + filterState.sortDesc;
						
						// 添加搜索关键词
						if (filterState.searchKeyword) url += "&keyword=" + encodeURIComponent(filterState.searchKeyword);
						
						// 添加筛选状态标记 - 告知onlinelearning3.html应用保存的筛选状态
						url += "&applyFilter=true";
						
						// 跳转到资源列表页
						window.location.href = url;
					} catch (error) {
						console.error("解析存储的筛选状态时出错:", error);
						// 出错时回退到使用URL参数方式
						fallbackToUrlParams();
					}
				} else {
					// 如果没有保存的状态，则使用URL参数构建返回URL
					fallbackToUrlParams();
				}
				
				// 没有saved状态时使用URL参数的回退方法
				function fallbackToUrlParams() {
					// 获取分类ID参数
					let categoryId = pflid || getUrlParam('categoryId');
					
					// 获取页码参数
					let currentPage = getUrlParam('currentPage') || 1;
					
					// 构建返回URL，添加所有可能的筛选参数
					let url = "onlinelearning3.html";
					
					// 添加基本参数
					url += "?categoryId=" + (categoryId || "");
					url += "&currentPage=" + currentPage;
					
					// 添加可能的课程ID
					let courseId = getUrlParam('courseId');
					if (courseId) url += "&courseId=" + courseId;
					
					// 添加可能的章节ID
					let chapterId = getUrlParam('chapterId');
					if (chapterId) url += "&chapterId=" + chapterId;
					
					// 添加可能的节ID
					let nodeId = getUrlParam('nodeId');
					if (nodeId) url += "&nodeId=" + nodeId;
					
					// 添加可能的小节ID
					let barId = getUrlParam('barId');
					if (barId) url += "&barId=" + barId;
					
					// 添加可能的文件类型
					let fileType = getUrlParam('fileType');
					if (fileType) url += "&fileType=" + fileType;
					
					// 添加可能的属性ID
					let attributeId = getUrlParam('attributeId');
					if (attributeId) url += "&attributeId=" + attributeId;
					
					// 添加可能的类型ID
					let typeId = getUrlParam('typeId');
					if (typeId) url += "&typeId=" + typeId;
					
					// 添加可能的类别ID (mtype)
					let mtype = getUrlParam('mtype');
					if (mtype) url += "&mtype=" + mtype;
					
					// 添加可能的排序方向
					let sortDesc = getUrlParam('sortDesc');
					if (sortDesc !== null) url += "&sortDesc=" + sortDesc;
					
					// 添加可能的搜索关键词
					let keyword = getUrlParam('keyword');
					if (keyword) url += "&keyword=" + keyword;
					
					// 跳转到资源列表页
					window.location.href = url;
				}
			}
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
		
		<!-- 移动端适配代码 -->
		<script>
			$(document).ready(function() {
				// 移动端菜单切换功能
				$("#menuToggle").on('click', function() {
					$("#menubox").toggleClass('expanded');
				});
				
				// 处理窗口大小变化，确保在切换到桌面视图时菜单正常显示
				$(window).resize(function() {
					if ($(window).width() > 768) {
						$("#menubox").removeClass('expanded');
					}
					
					// 重新适配文档查看器
					adjustDocumentViewer();
				});
				
				// 优化文档查看器显示
				function adjustDocumentViewer() {
					// 处理文档内容宽度自适应
					setTimeout(function() {
						// 计算合适的容器宽度
						let optimalWidth = '90%';
						let screenWidth = $(window).width();
						let screenHeight = $(window).height();
						let isLandscape = screenWidth > screenHeight;
						
						// 根据屏幕宽度和方向动态调整容器尺寸
						if (screenWidth <= 480) {
							optimalWidth = isLandscape ? '99.5%' : '99%'; // 特小屏幕几乎占满
						} else if (screenWidth <= 768) {
							optimalWidth = isLandscape ? '99%' : '98%'; // 普通移动设备
						} else if (screenWidth <= 1024) {
							optimalWidth = '95%'; // 平板设备
						}
						
						// 应用到所有文档容器
						$('.file-viewer-container, .kjview > *, .kjview .doc-container, .kjview .doc-toolbar, .kjview .audio-player').css({
							'width': optimalWidth,
							'max-width': screenWidth <= 768 ? optimalWidth : '1000px',
							'margin-left': 'auto',
							'margin-right': 'auto'
						});
						
						// 处理嵌入式文档
						$('.kjview embed, .kjview iframe, .kjview object, .panel-body.pdfbox object, .panel-body.pdfbox iframe, .panel-body.pdfbox embed').css({
							'width': optimalWidth,
							'max-width': '100%'
						});
						
						// 处理DOCX查看器
						$('.docx-viewer .page').css({
							'max-width': screenWidth <= 768 ? optimalWidth : '90%',
							'padding': screenWidth <= 480 ? '8px' : (screenWidth <= 768 ? '10px' : '20px'),
							'overflow-x': 'auto'
						});
						
						// 处理段落和文字
						$('.docx-viewer p, .docx-viewer span, .docx-viewer div, .docx-viewer li').css({
							'max-width': '100%',
							'word-break': 'break-word',
							'overflow-wrap': 'break-word'
						});
						
						// 特殊处理表格和内容
						if (isLandscape && screenWidth <= 920) {
							// 横屏手机模式
							$('.docx-viewer').css('font-size', '0.95em');
							$('.docx-viewer table').css({
								'max-width': '100%',
								'font-size': '0.9em',
								'width': 'auto'
							});
							$('.docx-viewer .page').css({
								'max-width': '99.5%',
								'padding': '8px'
							});
							$('.panel-body.pdfbox object, .panel-body.pdfbox iframe, .panel-body.pdfbox embed').css({
								'width': '99.5%',
								'max-width': '99.5%'
							});
							// 减少边距
							$('.docx-viewer p, .docx-viewer span, .docx-viewer div, .docx-viewer td, .docx-viewer th, .docx-viewer li').css({
								'margin': '0.2em 0'
							});
						} else {
							// 竖屏或桌面模式
							$('.docx-viewer table').css({
								'max-width': '100%',
								'font-size': screenWidth <= 480 ? '0.8em' : (screenWidth <= 768 ? '0.85em' : '1em'),
								'width': 'auto'
							});
						}
						
						// 处理图片
						$('.docx-viewer img').css({
							'max-width': '100%',
							'height': 'auto'
						});
					}, 300);
				}
				
				// 页面加载后立即调整文档查看器
				adjustDocumentViewer();
				
				// 设置MutationObserver监听DOM变化，确保动态加载的内容也能适配
				const documentObserver = new MutationObserver(function(mutations) {
					// 当DOM发生变化时重新调整文档查看器
					adjustDocumentViewer();
					
					// 查找新加载的docx内容并应用样式
					mutations.forEach(function(mutation) {
						if (mutation.addedNodes && mutation.addedNodes.length > 0) {
							// 检查是否有新的文档内容被添加
							for (let i = 0; i < mutation.addedNodes.length; i++) {
								const node = mutation.addedNodes[i];
								if (node.nodeType === 1) { // 元素节点
									// 如果是新添加的文档查看器或其内部元素
									if ($(node).hasClass('doc-container') || 
										$(node).hasClass('docx-viewer') || 
										$(node).find('.docx-viewer').length > 0 ||
										$(node).find('iframe').length > 0 ||
										$(node).find('object').length > 0) {
										// 稍等片刻让内容完全加载
										setTimeout(adjustDocumentViewer, 1000);
										break;
									}
								}
							}
						}
					});
				});
				
				// 开始观察文档容器的变化
				documentObserver.observe(document.getElementById('bbbb2'), {
					childList: true,
					subtree: true
				});
				
				// 观察弹出式PDF查看器的变化
				if (document.getElementById('tcbox')) {
					documentObserver.observe(document.getElementById('tcbox'), {
						childList: true,
						subtree: true
					});
				}
				
				// 优化移动端PDF和视频播放体验
				if ($(window).width() <= 768) {
					// 视频播放优化
					$("#videos").on('play', function() {
						// 设置视频在移动端全屏播放
						if (this.requestFullscreen) {
							this.requestFullscreen();
						} else if (this.webkitRequestFullscreen) {
							this.webkitRequestFullscreen();
						} else if (this.msRequestFullscreen) {
							this.msRequestFullscreen();
						}
					});
					
					// PDF查看器调整
					window.showpdf = function(item) {
						if(!userinfo) {
							window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html';
							return;
						}
						
						$.ajax({
							url: baseurl + "/course/view/" + $(item).attr("data-cid"),
							type: 'GET',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							dataType: 'json',
							success: (res) => {}
						});
						
						$.ajax({
							url: baseurl + "/course/meta/" + $(item).attr("data-id"),
							type: 'GET',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							dataType: 'json',
							success: (res) => {
								if (res.code == '200') {
									$("#pdfname").html(res.data.cmsResourcesCourseMetaList[0].attachName);
									$("#tcbox").show();
									$("#pdf").attr("href", baseurl + res.data.cmsResourcesCourseMetaList[0].attachPath);
									
									// 在移动端使用更合适的尺寸
									$('a.media').media({
										width: '98%',
										height: '80vh'
									});
									
									// 调整PDF查看器为自适应宽度
									setTimeout(function() {
										$('.panel-body.pdfbox object, .panel-body.pdfbox iframe, .panel-body.pdfbox embed').css({
											'width': '98%',
											'max-width': '98%',
											'height': '70vh',
											'margin': '0 auto'
										});
										
										// 立即调整整体文档查看器
										adjustDocumentViewer();
									}, 1000);
								}
							}
						});
					};
					
					// 优化全屏图片查看
					window.qpshow = function() {
						$("#qp").css({
							"display": "flex",
							"align-items": "center",
							"justify-content": "center"
						});
					};
				} else {
					// 桌面端PDF查看器尺寸优化
					window.showpdf = function(item) {
						if(!userinfo) {
							window.location.href = 'https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html';
							return;
						}
						
						$.ajax({
							url: baseurl + "/course/view/" + $(item).attr("data-cid"),
							type: 'GET',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							dataType: 'json',
							success: (res) => {}
						});
						
						$.ajax({
							url: baseurl + "/course/meta/" + $(item).attr("data-id"),
							type: 'GET',
							contentType: "application/json",
							headers: {
								"Authorization": sessionStorage.getItem("header")
							},
							dataType: 'json',
							success: (res) => {
								if (res.code == '200') {
									$("#pdfname").html(res.data.cmsResourcesCourseMetaList[0].attachName);
									$("#tcbox").show();
									$("#pdf").attr("href", baseurl + res.data.cmsResourcesCourseMetaList[0].attachPath);
									
									// 在桌面端使用更合适的尺寸 - 居中显示和增加宽度
									$('a.media').media({
										width: '90%', 
										height: '80vh',
										success: function(player) {
											// 在PDF加载后应用额外样式
											$(player).css({
												'margin': '0 auto',
												'display': 'block',
												'max-width': '1000px',
												'border': '1px solid #ddd',
												'border-radius': '8px',
												'box-shadow': '0 2px 10px rgba(0,0,0,0.1)'
											});
											
											// 立即调整整体文档查看器
											adjustDocumentViewer();
										}
									});
								}
							}
						});
					};
				}
				
				// 优化返回顶部按钮
				$("#backtop").css({
					"position": "fixed",
					"bottom": "20px",
					"right": "20px",
					"z-index": "999"
				});
				
				// 监听设备方向变化，在横竖屏切换时调整文档布局
				$(window).on('orientationchange', function() {
					// 等待方向实际切换完成
					setTimeout(function() {
						// 强制调整文档
						forceAdjustDocument();
					}, 500);
				});
				
				// 监听窗口大小变化，适用于桌面浏览器和不支持orientation事件的设备
				$(window).on('resize', function() {
					// 延迟执行以避免频繁触发
					clearTimeout(window.resizeTimer);
					window.resizeTimer = setTimeout(function() {
						// 检测是否横屏
						checkOrientationAndAdjust();
					}, 250);
				});
				
				// 检测横屏状态并应用相应样式
				function checkOrientationAndAdjust() {
					let screenWidth = $(window).width();
					let screenHeight = $(window).height();
					let isLandscape = screenWidth > screenHeight;
					
					// 如果是横屏手机模式
					if (isLandscape && screenWidth <= 920) {
						forceAdjustDocument();
					} else {
						// 正常调整
						adjustDocumentViewer();
					}
				}
				
				// 强制调整文档布局 - 用于横屏模式
				function forceAdjustDocument() {
					// 强制应用横屏样式
					let screenWidth = $(window).width();
					let screenHeight = $(window).height();
					
					// 确认是横屏模式
					if (screenWidth > screenHeight && screenWidth <= 920) {
						// 最大化利用空间
						$('.docx-viewer .page').css({
							'max-width': '99.5%',
							'padding': '8px'
						});
						
						// 文档容器最大化
						$('.file-viewer-container, .kjview > *, .kjview .doc-container, .kjview .doc-toolbar').css({
							'width': '99.5%',
							'max-width': '99.5%'
						});
						
						// 减小字体
						$('.docx-viewer').css('font-size', '0.95em');
						$('.docx-viewer table').css('font-size', '0.9em');
						
						// 特殊处理PDF查看器
						$('.panel-body.pdfbox, .panel-body.pdfbox object, .panel-body.pdfbox iframe, .panel-body.pdfbox embed').css({
							'width': '99.5%',
							'max-width': '99.5%'
						});
						
						// 减少内部元素边距
						$('.docx-viewer p, .docx-viewer span, .docx-viewer div, .docx-viewer td, .docx-viewer th, .docx-viewer li').css({
							'margin': '0.2em 0'
						});
						
						// 强制设置表格自动换行
						$('.docx-viewer table, .docx-viewer td, .docx-viewer th').css({
							'word-break': 'break-word',
							'width': 'auto',
							'max-width': '99.5%'
						});
					} else {
						// 正常调整
						adjustDocumentViewer();
					}
				}
				
				// 初始检查设备方向
				checkOrientationAndAdjust();
			});
		</script>
	</body>
</html>
