/* 移动端首页样式 */

/* 轮播图区域 */
.banner-section {
    position: relative;
    margin-bottom: 16px;
}

.swiper-container {
    width: 100%;
    height: 200px;
    border-radius: 0 0 12px 12px;
    overflow: hidden;
}

.swiper-wrapper {
    height: 100%;
}

.swiper-slide {
    position: relative;
    overflow: hidden;
}

.swiper-slide img {
    width: 100%;
    height: 100%;
    object-fit: cover;
}

.slide-content {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    background: linear-gradient(transparent, rgba(0, 0, 0, 0.7));
    color: white;
    padding: 20px 16px 16px;
}

.slide-title {
    font-size: 16px;
    font-weight: 600;
    margin-bottom: 4px;
    line-height: 1.4;
}

.slide-description {
    font-size: 12px;
    opacity: 0.9;
    line-height: 1.3;
}

.swiper-pagination {
    bottom: 12px !important;
}

.swiper-pagination-bullet {
    width: 6px !important;
    height: 6px !important;
    background: rgba(255, 255, 255, 0.5) !important;
    opacity: 1 !important;
    margin: 0 3px !important;
}

.swiper-pagination-bullet-active {
    background: white !important;
    width: 16px !important;
    border-radius: 3px !important;
}

/* 快捷功能区域 */
.quick-actions {
    background: white;
    margin-bottom: 12px;
    padding: 20px 0;
}

.actions-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    gap: 16px;
}

.action-item {
    display: flex;
    flex-direction: column;
    align-items: center;
    text-decoration: none;
    color: #333;
    padding: 12px 8px;
    border-radius: 12px;
    transition: all 0.3s ease;
    position: relative;
}

.action-item:active {
    transform: scale(0.95);
    background: rgba(192, 7, 20, 0.05);
}

.action-icon {
    width: 48px;
    height: 48px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-bottom: 8px;
    position: relative;
    overflow: hidden;
}

.action-icon::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(135deg, rgba(255, 255, 255, 0.2), rgba(255, 255, 255, 0));
    border-radius: inherit;
}

.action-icon svg {
    width: 24px;
    height: 24px;
    color: white;
    z-index: 1;
}

.action-icon.experiment {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.action-icon.museum {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.action-icon.footprint {
    background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
}

.action-icon.vr {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.action-item span {
    font-size: 12px;
    font-weight: 500;
    text-align: center;
    line-height: 1.2;
}

/* 区域标题样式 */
.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 16px;
    padding: 0 4px;
}

.section-header h2 {
    display: flex;
    align-items: center;
    font-size: 18px;
    font-weight: 600;
    color: #333;
    gap: 8px;
}

.section-header h2 svg {
    width: 20px;
    height: 20px;
    color: #c00714;
}

.more-link {
    color: #999;
    font-size: 14px;
    text-decoration: none;
    display: flex;
    align-items: center;
    gap: 4px;
}

.more-link::after {
    content: '>';
    font-size: 12px;
}

/* 心声社区区域 */
.community-section {
    background: white;
    margin-bottom: 12px;
    padding: 20px 0;
}

.community-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.community-item {
    display: flex;
    align-items: flex-start;
    gap: 12px;
    padding: 12px;
    background: #f8f9fa;
    border-radius: 8px;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
    position: relative;
}

.community-item:active {
    transform: translateY(1px);
    background: #f0f1f2;
}

.community-item .new-badge {
    position: absolute;
    top: 8px;
    right: 8px;
    background: #c00714;
    color: white;
    font-size: 10px;
    padding: 2px 6px;
    border-radius: 8px;
    font-weight: 500;
}

.community-content {
    flex: 1;
}

.community-title {
    font-size: 14px;
    font-weight: 500;
    line-height: 1.4;
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.community-time {
    font-size: 12px;
    color: #999;
}

/* 在线学习区域 */
.learning-section {
    background: white;
    margin-bottom: 12px;
    padding: 20px 0;
}

.learning-tabs {
    display: flex;
    margin-bottom: 16px;
    background: #f5f5f5;
    border-radius: 8px;
    padding: 4px;
}

.tab-btn {
    flex: 1;
    background: none;
    border: none;
    padding: 8px 16px;
    border-radius: 6px;
    font-size: 14px;
    color: #666;
    cursor: pointer;
    transition: all 0.3s ease;
}

.tab-btn.active {
    background: white;
    color: #c00714;
    font-weight: 500;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.learning-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
}

.learning-item {
    background: #f8f9fa;
    border-radius: 8px;
    overflow: hidden;
    text-decoration: none;
    color: #333;
    transition: all 0.3s ease;
}

.learning-item:active {
    transform: scale(0.98);
}

.learning-cover {
    width: 100%;
    height: 100px;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 14px;
    font-weight: 500;
    text-align: center;
    padding: 8px;
}

.learning-info {
    padding: 12px;
}

.learning-title {
    font-size: 13px;
    font-weight: 500;
    line-height: 1.3;
    margin-bottom: 4px;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

.learning-meta {
    font-size: 11px;
    color: #999;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

/* 加载状态 */
.loading-item {
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 40px 20px;
    color: #999;
    font-size: 14px;
}

.loading-item .spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #f3f3f3;
    border-top: 2px solid #c00714;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-right: 8px;
}

.empty-state {
    text-align: center;
    padding: 40px 20px;
    color: #999;
    font-size: 14px;
}

/* 响应式调整 */
@media (max-width: 375px) {
    .actions-grid {
        gap: 12px;
    }
    
    .action-icon {
        width: 44px;
        height: 44px;
    }
    
    .action-icon svg {
        width: 22px;
        height: 22px;
    }
    
    .action-item span {
        font-size: 11px;
    }
    
    .learning-grid {
        gap: 8px;
    }
    
    .learning-cover {
        height: 80px;
        font-size: 12px;
    }
    
    .learning-info {
        padding: 8px;
    }
}

/* 暗色模式支持 */
@media (prefers-color-scheme: dark) {
    .quick-actions,
    .community-section,
    .learning-section {
        background: #1a1a1a;
    }
    
    .community-item,
    .learning-item {
        background: #2a2a2a;
        color: #fff;
    }
    
    .community-item:active {
        background: #333;
    }
    
    .section-header h2 {
        color: #fff;
    }
    
    .learning-tabs {
        background: #2a2a2a;
    }
    
    .tab-btn {
        color: #999;
    }
    
    .tab-btn.active {
        background: #333;
        color: #c00714;
    }
}
