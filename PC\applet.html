<!DOCTYPE html>
<html lang="en">
<head>
	
    <meta charset="UTF-8">
    <title>loading...</title>
    <meta name="viewport" content="width=device-width, initial-scale=1.0">

		<script>
		
	
		<script src="./js/common-header.js" type="text/javascript" charset="utf-8"></script>
		<!-- URL路径修复脚本 -->
		<script src="./js/fix-url-paths.js" type="text/javascript" charset="utf-8"></script>
		<!-- 菜单修复脚本 -->
		<script src="./js/fix-menu.js" type="text/javascript" charset="utf-8"></script>
	</head>
<body>
    <div id="data"></div>
</body>

    const baseUrl = "https://szjx.sntcm.edu.cn/api"
    window.onload = ()=>{
        if(!IsPC()){
            const urlParams = new URLSearchParams(window.location.search)
            const ticket = urlParams.get('ticket')
            if(ticket){
                //有ticket则跳转到微信小程序
                //将ticket和小程序路径等传到 scheme 获取接口
                //getScheme(ticket)
                //通过ticket获取学生信息
                getUserInfo(ticket)
            }
        }else{
            alert("请使用手机浏览器打开")
        }
    }
    function getUserInfo(ticket){
        var xhr = new XMLHttpRequest()
        var url = baseUrl + "/student/wechatuserBytiket?ticket=" + ticket // 替换为你的POST请求的URL
        xhr.open("GET", url, true) // 第一个参数是请求类型，第二个是URL，第三个指示是否异步
        xhr.setRequestHeader("Content-Type", "application/json")
        xhr.send()
        xhr.onreadystatechange = function () {
            if (xhr.readyState === 4 && xhr.status === 200) {
                // 请求成功
                let res = JSON.parse(xhr.responseText)
                if(JSON.parse(res.data).errcode == 0){
                    getScheme(JSON.parse(res.data).xh)
                }else{
                    alert("获取用户信息失败！请重试！")
                }
            }
        };
    }
    function IsPC() {
        var userAgentInfo = navigator.userAgent;
        var Agents = ["Android", "iPhone",
            "SymbianOS", "Windows Phone",
            "iPad", "iPod"];
        var flag = true;
        for (var v = 0; v < Agents.length; v++) {
            if (userAgentInfo.indexOf(Agents[v]) > 0) {
                flag = false;
                break;
            }
        }
        return flag;
    }
    function getScheme(xuehao){
        let json = {
            jumpWxa: {
                path: "pages/login/login",
                query: "ticket=" + xuehao,
                env_version: "trial"
            }
        }
        var xhr = new XMLHttpRequest()
        var url = baseUrl + "/student/appletloginHtml" // 替换为你的POST请求的URL
        xhr.open("POST", url, true) // 第一个参数是请求类型，第二个是URL，第三个指示是否异步
        xhr.setRequestHeader("Content-Type", "application/json")
        xhr.send(JSON.stringify(json))
        xhr.onreadystatechange = function () {
            if (xhr.readyState === 4 && xhr.status === 200) {
                // 请求成功
                let res = JSON.parse(xhr.responseText)
                window.location.href = res.data
            }
        };
    }
</script>
</html>