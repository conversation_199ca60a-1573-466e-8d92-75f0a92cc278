<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>账号切换工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 600px;
            margin: 0 auto;
            padding: 20px;
            text-align: center;
        }
        h1 {
            color: #A65D57;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 10px;
            background-color: #A65D57;
            color: white;
            border: none;
            border-radius: 4px;
            font-size: 16px;
            cursor: pointer;
            text-decoration: none;
        }
        .btn:hover {
            background-color: #8A4D47;
        }
        .info {
            margin: 20px;
            padding: 15px;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .current-user {
            margin: 20px;
            padding: 15px;
            background-color: #e9ecef;
            border-radius: 4px;
            font-weight: bold;
        }
        .links {
            margin-top: 30px;
        }
        .links a {
            display: block;
            margin: 10px auto;
            color: #A65D57;
        }
    </style>
</head>
<body>
    <h1>学习平台账号切换工具</h1>
    
    <div class="current-user" id="currentUser">
        当前未登录
    </div>
    
    <div class="info">
        <p>使用此工具可以快速切换账号角色，查看不同角色的功能和内容</p>
    </div>
    
    <button id="studentBtn" class="btn">切换到学生账号</button>
    <button id="teacherBtn" class="btn">切换到教师账号</button>
    
    <div class="links">
        <a href="onlinelearning4.html">进入学习页面</a>
        <a href="index.html">返回首页</a>
    </div>
    
    <script src="./js/jquery-3.2.1.min.js"></script>
    <script src="./js/login-mock.js"></script>
    <script>
        // 显示当前用户信息
        function updateUserInfo() {
            const userInfo = JSON.parse(sessionStorage.getItem('userinfo') || '{}');
            if (userInfo.name) {
                document.getElementById('currentUser').innerHTML = 
                    `当前账号：<strong>${userInfo.name}</strong> (${userInfo.roleName})`;
                
                // 禁用当前角色的按钮
                if (userInfo.roleName === '学生') {
                    document.getElementById('studentBtn').disabled = true;
                    document.getElementById('studentBtn').style.opacity = '0.5';
                    document.getElementById('studentBtn').style.cursor = 'not-allowed';
                } else {
                    document.getElementById('studentBtn').disabled = false;
                    document.getElementById('studentBtn').style.opacity = '1';
                    document.getElementById('studentBtn').style.cursor = 'pointer';
                }
                
                if (userInfo.roleName === '老师') {
                    document.getElementById('teacherBtn').disabled = true;
                    document.getElementById('teacherBtn').style.opacity = '0.5';
                    document.getElementById('teacherBtn').style.cursor = 'not-allowed';
                } else {
                    document.getElementById('teacherBtn').disabled = false;
                    document.getElementById('teacherBtn').style.opacity = '1';
                    document.getElementById('teacherBtn').style.cursor = 'pointer';
                }
            } else {
                document.getElementById('currentUser').textContent = '当前未登录';
            }
        }
        
        // 页面加载时更新用户信息
        window.addEventListener('DOMContentLoaded', updateUserInfo);
        
        // 绑定按钮事件
        document.getElementById('studentBtn').addEventListener('click', function() {
            switchToStudent();
            setTimeout(updateUserInfo, 600);
        });
        
        document.getElementById('teacherBtn').addEventListener('click', function() {
            switchToTeacher();
            setTimeout(updateUserInfo, 600);
        });
    </script>
</body>
</html> 