<!DOCTYPE html>
<html>
	<head>
	
		<meta charset="utf-8" />
		<title>思政一体化平台-精神谱系</title>
		<link rel="stylesheet" type="text/css" href="css/animate.min.css" />
		<link rel="stylesheet" type="text/css" href="css/style.css" />
		<link rel="stylesheet" type="text/css" href="css/nnindex.css" />
		<script src="./js/jquery-3.2.1.min.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/swiper.min.js" type="text/javascript" charset="utf-8"></script>
		<style>
			.jspxgrouptitle{
				display: none;
			}
			#toptitle{
				width: 12rem;
				/* 1.文字强制一行显示 */
				white-space: nowrap;
				/* 2.溢出部分隐藏 */
				overflow: hidden;
				/* 3.溢出部分用省略号代替 */
				text-overflow: ellipsis;
			}
		</style>
	</head>
	<body class="confident">
		<div class="topview">
			<div class="topview_1">
				<img class="logo" src="./img/logo.png" />
				<div class="loginview">
					<div class="ssview">
						<img src="img/ss.png" />
						<input type="text" placeholder="搜索" />
					</div>
					<img src="img/user.png" />
<a id="login" href="https://cas.sntcm.edu.cn/lyuapServer/login?service=http://szjx.sntcm.edu.cn/userinfo.html">用户登录</a>					<a id="user" href="userinfo.html"></a>
					<label id="edit" class="tclogin" onclick="edit()">退出登录</label>
				</div>
			</div>
			<div class="topview_2">
				<div class="itembox" id="menubox">
					
				</div>
			</div>
		</div>
		<div class="nzxview" style="margin-top: 3.125rem;margin-bottom: 3.125rem;">
			<div class="toppx">
				<label>中国共产党人精神谱系</label>
			</div>
			<div class="jspxcontent">
				<div class="jspxleft" id="jsleft">
				</div>
				<div class="jspxright">
					<div class="jspxtitle"><label></label><span id="toptitle"></span></div>
					<div id="jsright">
						
					</div>
				</div>
			</div>
		</div>
		<footer>
			<div class="yqlj">
				<div style="width: 100%;display: flex;align-items: flex-start;">
					<div class="title" style="width: 8.125rem;">友情链接</div>
					<div class="box" style="width: calc(100% - 8.125rem);flex-wrap: wrap;" id="linkbox">
		
					</div>
				</div>
			</div>
			<div
				style="display: flex;align-items: center;justify-content: center;width: 100%;flex-wrap: wrap;flex-direction: column;">
				<a target="_blank" style="font-size: 0.833333rem;" href="https://beian.miit.gov.cn/#/Integrated/index"
					class="ba">陕ICP备05001612号-1<img src="img/ba.png" />陕公网安备 61040202000395号</a>
				<a href="flsminfo.html"
					style="font-size: 0.833333rem;color: #FFFFFF;text-decoration: underline;">网站声明</a>
			</div>
		</footer>
		<div id="backtop">
			<div><img src="img/jttop.png" /></div>
			<div>返回顶部</div>
		</div>
		<script src="./js/coco-message.js" type="text/javascript" charset="utf-8"></script>
		<script src="./js/base.js" type="text/javascript" charset="utf-8"></script>
		<script>
		
		
			$(document).ready(() => {
				$(".ssview").on('click', function() {
					window.location.href = 'searchall.html'
				})
				getclass()
				getfooterlink()
				getclassleft()
			})
			function getfooterlink(){
				$.ajax({
					url: baseurl + "/web/friendlink",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let html = ""
							res.data.map((item)=>{
								html+='<a href="'+item.url+'" target="_blank">'+item.name+'</a>'
							})
							$("#linkbox").html(html)
						}
					}
				})
			}
			let mclassdata = null
			let classdata = null
			function getclassleft() {
				$.ajax({
					url: baseurl + "/web/category/party/building",
					type: 'GET',
					contentType: "application/json",
					headers: {
						"Authorization": sessionStorage.getItem("header")
					},
					dataType: 'json',
					success: (res) => {
						if (res.code == '200') {
							let arr = res.data[0].children
							classdata = arr.find(item => item.name == '精神谱系')
							let html = ""
							mclassdata = classdata.children[0]
							classdata.children.forEach((item)=>{
								html += `<div onclick="selectleft(${item.id})" class="${item.id == mclassdata.id ? 'jspxleftitem jspxacc' : 'jspxleftitem'}"><label><span></span></label><div>${item.name}</div></div>`
							})
							$("#jsleft").html(html)
							$("#toptitle").html(mclassdata.name)
							showhtml()
						}
					}
				})
			}
			
			function selectleft(id){
				mclassdata = classdata.children.find(item => item.id == id)
				let html = ""
				classdata.children.forEach((item)=>{
					html += `<div onclick="selectleft(${item.id})" class="${item.id == mclassdata.id ? 'jspxleftitem jspxacc' : 'jspxleftitem'}"><label><span></span></label><div>${item.name}</div></div>`
				})
				$("#jsleft").html(html)
				$("#toptitle").html(mclassdata.name)
				showhtml()
			}
			
			function showhtml(){
				let html = ""
				// console.log(mclassdata)
				mclassdata.children.forEach((item)=>{
					html+=`<div class="jspxgroup">
										<div class="jspxgrouptitle"><label></label>${item.name}</div>
										<div class="jspxgrouplist">`
											$.ajax({
												url: baseurl + "/web/posts",
												type: 'GET',
												contentType: "application/json",
												headers: {
													"Authorization": sessionStorage.getItem("header")
												},
												async: false,
												data: {
													pageNum: 1,
													pageSize: 999,
													categoryId: item.id
												},
												dataType: 'json',
												success: (res) => {
													if (res.code == '200') {
														// console.log(res.data.list)
														res.data.list.forEach((item2)=>{
															html+=`<a href="spiritinfo.html?id=${item2.id}" class="jspxgroupitem">
																			<img src="${baseurl + item2.thumbPath[0]}"/>
																			<div class="jspxgitembar">
																				<div class="jspxgitemtitle">${item2.title}</div>
																				<div class="jspxgitemstr">${item2.excerpt}</div>
																			</div>
																		</a>`
														})
													}
												}
											})
											
											
									html+=	`</div>
									</div>`
				})
				$("#jsright").html(html)
			}
			
			
		</script>
		<script>
			$("#backtop").hide()
			$(function() {
				$(window).scroll(function() {
					if ($(window).scrollTop() > 600) {
						$("#backtop").show()
					} else {
						$("#backtop").hide()
					}
				})
			})
			$("#backtop").on('click', function() {
				$("body,html").animate({
					scrollTop: 0
				}, 300)
			})
		</script>
	</body>
</html>
